package actionerrors

import (
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"runtime"
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestNewErrServerInternal(t *testing.T) {
	assert := assert.New(t)

	testErr := errors.New("test")
	loggerField := logger.Fields{"type": "test"}
	fakeErrServerInternal := handler.NewLoggerErrorWithSkip(500, 100010500, "服务器内部错误", 2)
	newErr := NewErrServerInternal(testErr, loggerField).(*handler.LoggerError)
	newErr2 := fakeErrServerInternal.New(testErr, loggerField)
	assert.Equal(newErr2.Status, newErr.Status)
	assert.Equal(newErr2.Code, newErr.Code)
	assert.Equal(newErr2.ContextError.Error(), fmt.Sprintf("服务器内部错误: %s", testErr.Error()))
	assert.Equal(newErr2.ContextError.Error(), newErr.ContextError.Error())

	callerFrame := reflect.ValueOf(newErr.ContextError).Elem().Field(2)
	callerFrame = reflect.NewAt(callerFrame.Type(), unsafe.Pointer(callerFrame.UnsafeAddr())).Elem()
	frame := callerFrame.Interface().(*runtime.Frame)

	fakeCallerFrame := reflect.ValueOf(newErr2.ContextError).Elem().Field(2)
	fakeCallerFrame = reflect.NewAt(fakeCallerFrame.Type(), unsafe.Pointer(fakeCallerFrame.UnsafeAddr())).Elem()
	fakeFrame := fakeCallerFrame.Interface().(*runtime.Frame)

	assert.Equal(fakeFrame.File, frame.File)
}

func TestErrParamsMsg(t *testing.T) {
	e := ErrParamsMsg("test")
	a := handler.ActionError{
		Status:  400,
		Code:    501010000,
		Message: "test",
	}
	assert.Equal(t, a, *e.(*handler.ActionError))
}

func TestErrNotFound(t *testing.T) {
	e := ErrNotFound("test")
	a := handler.ActionError{
		Status:  404,
		Code:    501010004,
		Message: "test",
	}
	assert.Equal(t, a, *e.(*handler.ActionError))
}

func TestNewUnknownError(t *testing.T) {
	e := NewUnknownError(200, "test")
	a := handler.ActionError{
		Status:  200,
		Code:    handler.CodeUnknownError,
		Message: "test",
	}
	assert.Equal(t, a, *e.(*handler.ActionError))
}

func TestConfirmRequiredError(t *testing.T) {
	assert := assert.New(t)

	e := ErrConfirmRequired("test error", 1)
	a := handler.ActionError{
		Status:  428,
		Code:    100010020,
		Message: "",
		Info:    handler.M{"confirm": 1, "msg": "test error"},
		Data:    handler.M{"confirm": 1, "msg": "test error"},
	}
	assert.Equal(a, *e)

	a.Info = handler.M{"confirm": 1, "msg": "test error&amp;"}
	a.Data = a.Info
	e = ErrConfirmRequired("test error&", 1)
	assert.Equal(a, *e)

	a.Info = handler.M{"confirm": 1, "msg": "test error&"}
	a.Data = a.Info
	e = ErrConfirmRequired("test error&", 1, true)
	assert.Equal(a, *e)
}

func TestErrBadRequest(t *testing.T) {
	assert := assert.New(t)

	e := ErrBadRequest(handler.CodeOperateTooFrequently, "test error")
	a := handler.ActionError{
		Status:  http.StatusBadRequest,
		Code:    handler.CodeOperateTooFrequently,
		Message: "test error",
	}
	assert.Equal(a, *e.(*handler.ActionError))
}
