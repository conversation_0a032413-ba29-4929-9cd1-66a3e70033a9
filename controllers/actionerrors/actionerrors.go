package actionerrors

import (
	"fmt"
	"html"
	"net/http"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// error code
const (
	CodeBalanceNotEnough = 200360006
	// CodeSSOGetUserNotFound sso get 接口未获取到用户信息
	CodeSSOGetUserNotFound = 300010001
)

// errors
// 文档地址：https://info.missevan.com/pages/viewpage.action?pageId=97898047
var (
	// ------ 系统级错误代码 -----

	newErrServerInternal = handler.NewLoggerErrorWithSkip(500, 100010500, "服务器内部错误", 3)
	// TODO: 下一步迁移服务器内部错的定义的时候要把下面的 ErrServerInternal 完全移除
	ErrServerInternal = handler.NewLoggerError(500, 100010500, "服务器内部错误")
	ErrDatabase       = handler.NewLoggerError(500, 100010002, "数据库错误")

	ErrDatabaseLegacy = handler.NewActionError(500, 100010002, "数据库错误")

	// ----- 通用错误 -----

	ErrParams               = handler.NewActionError(400, 501010000, "参数错误")
	ErrSignNotValid         = handler.NewActionError(401, 501010001, "签名校验失败")
	ErrForbidden            = handler.NewActionError(403, 501010003, "您无权执行该操作")
	ErrConnRole             = handler.NewActionError(403, 501010005, "获取连接身份失败")
	ErrVCode                = handler.NewActionError(403, 501010006, "验证码错误")
	ErrCheckVCodeMoreTimes  = handler.NewActionError(403, 501010007, "验证次数太多")
	ErrMobileSendVCode      = handler.NewActionError(403, 501010009, "获取验证码手机号错误")
	ErrSlideCaptchaRequired = handler.NewActionError(403, 100010014, "需要滑动验证")

	ErrGiftNum = ErrParamsMsg("不支持的赠送数量！")

	// ----- 用户错误 -----

	ErrUnloggedUser                      = handler.NewActionError(401, 500020001, "用户未登录或登录已过期，请重新登录")
	ErrNoAuthority                       = handler.NewActionError(403, 200020003, "用户没有权限") // TODO: 变量改名 ErrForbidden
	ErrUnrealNameAuthenticationUser      = handler.NewActionError(403, 500020003, "用户未通过实名认证")
	ErrCannotFindUser                    = handler.NewActionError(404, 500020004, "无法找到该用户")
	ErrUserNotFound                      = handler.NewActionError(404, 500020004, "该用户不存在")
	ErrUnbindMobileUser                  = handler.NewActionError(403, 500020013, "用户未绑定手机")
	ErrBannedUser                        = handler.NewActionError(403, 500020023, "当前用户被封禁")
	ErrIllegalMsg                        = handler.NewActionError(400, 500150022, "聊天内容含有违规信息")
	ErrHornMessageIllegal                = handler.NewActionError(400, 500020022, "喇叭内容含有违规信息")
	ErrNotEnoughPoint                    = handler.NewActionError(403, 500020024, "小鱼干数量不足")
	ErrGlobalMuteUser                    = handler.NewActionError(403, 500020025, "全站禁言中")
	ErrHornMessageSensitive              = handler.NewActionError(400, 500020026, "喇叭内容含有敏感信息")
	ErrLoginIdentityVerificationRequired = handler.NewActionError(403, 500020027, "用户需要身份验证") // 前端根据错误 code，请求权限验证接口
	ErrBlockUser                         = handler.NewActionError(403, 500020034, "当前用户被拉黑")
	ErrCannotInviteWithoutAuthentication = handler.NewActionError(403, 500020035, "该用户尚未完成实名认证，暂时无法邀请")
	ErrInvalidRealName                   = handler.NewActionError(403, 500020036, "真实姓名错误，请检查后再试")
	ErrCannotInviteWithoutCheckRealName  = handler.NewActionError(403, 500020037, "请先验证主播真实姓名再发起邀请")

	// ----- 聊天室错误------

	ErrWrongRoomType      = handler.NewActionError(400, 500030000, "聊天室类型错误")
	ErrCannotFindRoom     = handler.NewActionError(404, 500030004, "无法找到该聊天室")
	ErrClosedRoom         = handler.NewActionError(400, 500030011, "直播间尚未开启")
	ErrClosedRoomAlt      = handler.NewActionError(400, 500030011, "主播休息时暂时无法使用哦~")
	ErrRoomInfoIllegal    = handler.NewActionError(400, 500030012, "直播间名称或简介含有违规信息")
	ErrBannedRoom         = handler.NewActionError(403, 500030013, "直播间被封禁")
	ErrOutdatedClient     = handler.NewActionError(400, 500030014, "当前客户端版本过低，请更新客户端")
	ErrStartPushTime      = handler.NewActionError(403, 500030015, "当前时间不能开启直播哦")
	ErrRoomIsPushing      = handler.NewActionError(400, 500030021, "直播间正在推流中")
	ErrIllegalMessage     = handler.NewActionError(400, 500030022, "聊天内容含有违规信息")
	ErrMedalAlreadyExist  = handler.NewActionError(403, 500030023, "勋章重名，请更换名称")
	ErrInteractionOngoing = handler.NewActionError(403, 500030024, "您还有一个互动未结束哦~")

	ErrCannotRemoveSuperMedal       = handler.NewActionError(403, 500030025, "无法删除超级粉丝勋章哦~")
	ErrDisableUseHotCardInCloseRoom = handler.NewActionError(403, 500030026, "无法在关播直播间使用热度卡哦~")

	ErrLimitedRoom = NewErrForbidden("该房间是受限房间")

	// ----- 提问错误 -----

	ErrQuestionPriceTooLow  = handler.NewActionError(400, 500150001, "提问价格过低")
	ErrQuestionLimitReached = handler.NewActionError(400, 500150002, "当前提问数量已满")
	ErrQuestionNotFound     = handler.NewActionError(404, 500150004, "无法找到对应提问")
	ErrQuestionSensitive    = handler.NewActionError(400, 500150012, "提问内容含有敏感信息")

	// ----- 连麦错误 -----

	ErrCannotFindConnectUserInQueue = handler.NewActionError(403, 500160004, "无法在队列中找到等待连麦用户")
	ErrRoomChannelConnecting        = handler.NewActionError(403, 500160005, "当前已有用户正在连麦中")

	// ----- 公会错误 -----

	ErrGuildNotExist           = handler.NewActionError(404, 500050001, "公会不存在")
	ErrCannotJoinTwice         = handler.NewActionError(403, 500050002, "已加入另一公会，不能加入多个公会")
	ErrAlreadyRequestJoin      = handler.NewActionError(403, 500050003, "已经申请该公会，请等待公会答复")
	ErrGuildNotInvite          = handler.NewActionError(400, 500050004, "该公会未向您发起邀请")
	ErrContractNotExist        = handler.NewActionError(404, 500050005, "该合约不存在")
	ErrContractStatus          = handler.NewActionError(400, 500050006, "该合约状态已更改")
	ErrContractExpired         = handler.NewActionError(400, 500050007, "该合约状态已过期")
	ErrGuildNotTerminate       = handler.NewActionError(400, 500050008, "该公会未向您发起解约")
	ErrGuildAlreadyInvite      = handler.NewActionError(403, 500050009, "该公会已经向您发起邀请，请查看邀请记录")
	ErrGuildAlreadyInviteSign  = handler.NewActionError(403, 500050009, "该公会已邀请您签约，请先处理签约邀请")
	ErrGuildAlreadyInviteRenew = handler.NewActionError(403, 500050009, "公会已邀请您续约，请先处理续约邀请")
	ErrNotFindExclusiveCreator = handler.NewActionError(404, 500050011, "三方独家主播不存在")
	ErrCannotEditPassed        = handler.NewActionError(403, 501010003, "已过审公会不能再次编辑哦")
	ErrAlreadyJoinGuild        = handler.NewActionError(403, 501010003, "您已经在此公会中，无需重复加入")
	ErrShouldJoinGuild         = handler.NewActionError(403, 501010003, "您还未加入公会，请先申请签约")
	ErrRenewTooEarly           = handler.NewActionError(403, 501010003, "合约到期前 15 天可发起续约")
	ErrApplymentPending        = handler.NewActionError(403, 501010003, "申请已发起，请等待答复")
	ErrApplymentNotExist       = handler.NewActionError(403, 501010003, "该申请不存在")
	ErrApplymentFreezed        = handler.NewActionError(403, 501010003, "不可操作已被处理或已失效的申请")
	ErrApplymentType           = handler.NewActionError(403, 501010003, "错误的合约申请类型")
	ErrInvitationNotExist      = handler.NewActionError(404, 501010004, "该邀请不存在")
	ErrContractBeingProtected  = handler.NewActionError(403, 501010003, "签约未满 180 天，无法解约")
	ErrRenewApplymentPending   = handler.NewActionError(403, 501010003, "请先处理续约邀请")
	ErrGuildNameExists         = handler.NewActionError(403, 501010003, "已有重名公会，请修改后再次提交")

	ErrYouAreNotGuildOwner = handler.NewActionError(403, 501010003, "您需要成为公会长才能操作")

	// /api/v2/guild/agent/operate 公会会长添加或删除经纪人
	ErrAgentAlreadyJoinedOtherGuild = handler.NewActionError(403, 501010003, "该用户是其他公会经纪人，不可添加") // 公会不能添加其他公会经纪人
	ErrYouAlreadyAddThisAgent       = handler.NewActionError(403, 501010003, "此经纪人已在您的公会中")      // 公会不能重复添加经纪人
	ErrGuildAgentNotFound           = handler.NewActionError(404, 501010004, "未找到此经纪人")          // 公会添加或删除经纪人时经纪人不存在

	// /api/v2/guild/agent/assign 公会为主播分配/取消分配经纪人
	ErrCreatorNotInYourGuild       = handler.NewActionError(404, 501010004, "主播未加入您的公会") // 公会为主播分配经纪人时未找到该主播
	ErrAgentNotFound               = handler.NewActionError(404, 501010004, "未找到该经纪人")   // 公会为主播分配经纪人时未找到该经纪人
	ErrCreatorAgentNotFound        = handler.NewActionError(403, 501010003, "主播未分配经纪人")  // 公会对主播取消分配经纪人
	ErrCreatorAgentAlreadyAssigned = handler.NewActionError(403, 501010003, "该主播已分配经纪人") // 公会为主播重复分配经纪人

	// 经纪人操作主播合约申请
	ErrCreatorNotAssignedToYou      = handler.NewActionError(403, 501010003, "该主播未分配至您名下")     // 经纪人操作主播合约申请时需要主播已分配至其名下
	ErrCannotOperateOthersApplyment = handler.NewActionError(403, 501010003, "没有权限操作其它人的合约申请") // 经纪人不可操作其它经纪人的合约申请

	// /api/v2/guild/editguild 创建公会
	ErrUserAlreadyJoinedOtherGuild = handler.NewActionError(403, 501010003, "该用户已加入其他公会，不可添加")      // 用户不能重复创建公会
	ErrAgentCannotCreateGuild      = handler.NewActionError(403, 501010003, "您已成为其他公会经纪人，暂不支持创建公会") // 用户成为其它公会经纪人后，不能创建公会

	ErrLiveTxnOrderNotExist             = handler.NewActionError(404, 500060001, "超粉交易订单不存在")
	ErrSuperFanNotRegisteredCannotRenew = handler.NewActionError(403, 500060002, "未开通超粉，不可续费")
	ErrSuperFanCannotRegisterTwice      = handler.NewActionError(403, 500060003, "已开通超粉，请选择续费")

	// 礼物红包错误
	ErrSendRedPacketBalanceNotEnough = handler.NewActionError(400, CodeBalanceNotEnough, "钻石不够了啊\n小贴士：贵族钻石不能用于发礼物红包哦~")

	// ----- 通用错误 -----

	ErrCannotFindResource = handler.NewActionError(404, 501010004, "无法找到对应资源")
	ErrGoodsNotFound      = handler.NewActionError(404, 501010004, "商品不存在")

	// ----- 礼物红包错误 -----

	ErrRedPacketNotFound         = handler.NewActionError(404, 501010004, "礼物红包不存在")
	ErrRedPacketCanNotGrab       = handler.NewActionError(400, 501010000, "该礼物红包不可抢")
	ErrRedPacketGrabTooQuick     = handler.NewActionError(403, 501010003, "手速太快啦，请稍后重试")
	ErrRedPacketUnbindMobileUser = handler.NewActionError(403, 500020013, "抢红包需要绑定手机号哦~")

	// ----- 支付礼物付费等相关错误 -----

	ErrNotEnoughRedeemPoint = handler.NewActionError(403, 200360105, "兑换积分不足")

	// ----- 直播预告相关错误 -----

	ErrLivePreviewNotFound = handler.NewActionError(404, 501010004, "直播预告不存在")

	// ----- 付费弹幕相关错误 -----

	ErrDanmakuMessageIllegal   = handler.NewActionError(400, 500170001, "弹幕内容含有违规信息")
	ErrDanmakuMessageSensitive = handler.NewActionError(400, 500170002, "弹幕内容含有敏感信息")

	// ----- 福袋相关错误 -----

	ErrLuckyBagRewardBalanceNotEnough = handler.NewActionError(400, CodeBalanceNotEnough, "您的余额不足，请充值后购买")

	// ----- 主播连线相关错误 -----

	ErrMultiConnectGroupNotFound       = handler.NewActionError(404, 500190001, "无法找到对应主播连线组")
	ErrMultiConnectMatchNotFound       = handler.NewActionError(404, 500190002, "无法找到对应连线匹配记录")
	ErrMultiConnectMatchStatusModified = handler.NewActionError(403, 500190003, "连线匹配状态已修改")
	ErrMultiConnectGroupMemberModified = handler.NewActionError(403, 500190004, "连线组成员状态已变更")
	ErrMultiConnectInviteToApply       = handler.NewActionError(400, 500190005, "对方主播在连线中，是否申请加入其连线？")
	ErrMultiConnectIsLimit             = handler.NewActionError(403, 500190006, "连线人数已达上限，无法操作")
)

// NewErrServerInternal 新建一个新的服务器内部错误
func NewErrServerInternal(err error, fields logger.Fields) error {
	return newErrServerInternal.New(err, fields)
}

// ErrParamsMsg ErrParams with extra message
func ErrParamsMsg(msg string) error {
	return handler.NewActionError(ErrParams.Status, ErrParams.Code, msg)
}

// NewErrLiveForbidden forbidden err with extra message
func NewErrLiveForbidden(msg string) error {
	return handler.NewActionError(ErrForbidden.Status, ErrForbidden.Code, msg)
}

// ErrGlobalPopupPromptMsg error msg of global popup error prompt
func ErrGlobalPopupPromptMsg(msg string) error {
	// TODO: 100010018 是全局弹窗错误，可以参考文档 https://github.com/MiaoSiLa/missevan-doc/blob/master/api/error_code.md，需要将该错误迁移到 missevan-go
	return handler.NewActionError(400, 100010018, msg)
}

// ErrGlobalToast 全局 toast 提示错误
func ErrGlobalToast(message string) *handler.ActionError {
	return handler.NewActionError(403, 100010022, message)
}

// ErrBadRequest returns a new bad request error with customized message
func ErrBadRequest(code int, msg string) error {
	return handler.NewActionError(http.StatusBadRequest, code, msg)
}

// ErrNotFound ErrCannotFindResource with extra message
func ErrNotFound(msg string) error {
	return handler.NewActionError(ErrCannotFindResource.Status,
		ErrCannotFindResource.Code, msg)
}

// NewErrForbidden new ErrForbidden with extra message
func NewErrForbidden(msg string) error {
	return handler.NewActionError(ErrNoAuthority.Status, ErrNoAuthority.Code, msg)
}

// NewErrBlockUser 用户被拉黑错误
func NewErrBlockUser(msg string) error {
	return handler.NewActionError(ErrBlockUser.Status, ErrBlockUser.Code, msg)
}

// NewUnknownError 新建一个新的未知错误
func NewUnknownError(httpCode int, info string) error {
	return handler.NewActionError(httpCode, handler.CodeUnknownError, info)
}

// 公会二期错误
// TODO: 同类的错误同一个 var ()
var (
	ErrGuildApplyNotExists = handler.NewActionError(400, 500050005, "该申请不存在")
	ErrGuildApplyStatus    = handler.NewActionError(400, 500050006, "该申请状态异常，请确认后处理")
	ErrUserAlreadySigned   = handler.NewActionError(403, 500050002, "该主播已签约，无法邀请")
)

// ErrGuildApplyConflicted 公会申请冲突错误
func ErrGuildApplyConflicted(message string) *handler.ActionError {
	return handler.NewActionError(403, 500050009, message)
}

// ErrGuildContractConflicted 公会合约冲突错误
func ErrGuildContractConflicted(message string) *handler.ActionError {
	return handler.NewActionError(403, 500050009, message)
}

// ErrConfirmRequired 需要弹窗验证
func ErrConfirmRequired(msg string, confirm interface{}, disableHTMLEscape ...bool) *handler.ActionError {
	if len(disableHTMLEscape) == 0 || !disableHTMLEscape[0] {
		msg = html.EscapeString(msg)
	}
	return handler.NewActionErrorWithInfo(428, 100010020, "", handler.M{"confirm": confirm, "msg": msg})
}

// ErrReviseGuildRate 修改主播最低分成比例错误
func ErrReviseGuildRate(message string) *handler.ActionError {
	return handler.NewActionError(http.StatusForbidden, 501010003, message)
}

// ErrExclusiveCreatorConflicted 三方独家主播冲突错误
func ErrExclusiveCreatorConflicted(message string) *handler.ActionError {
	return handler.NewActionError(http.StatusForbidden, 500050010, message)
}

// ErrInvalidGiftIDs 无效礼物 ID
func ErrInvalidGiftIDs(ids []int64) *handler.ActionError {
	return handler.NewActionError(ErrParams.Status, ErrParams.Code, fmt.Sprintf("无效礼物 ID: %s", goutil.JoinInt64Array(ids, ", ")))
}

// ErrLuckyBagMessageIllegal 福袋口令或备注含有违规信息或敏感词
func ErrLuckyBagMessageIllegal(message string) *handler.ActionError {
	return handler.NewActionError(400, 500180001, message)
}

// NewErrMultiConnectGroupMemberModifiedMsg 连线组成员已变更
func NewErrMultiConnectGroupMemberModifiedMsg(msg string) error {
	return handler.NewActionError(ErrMultiConnectGroupMemberModified.Status, ErrMultiConnectGroupMemberModified.Code, msg)
}
