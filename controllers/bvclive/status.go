package bvclive

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/logdb/livebvclivesdkstatus"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 迁移仓库：https://git.bilibili.co/maoer/bvclive_stats/-/tree/main

// WebrtcClientSdk bvclive sdk 上报参数
// https://git.bilibili.co/maoer/bvclive_stats/-/blob/main/models/WebrtcClientSdk.go
type WebrtcClientSdk struct {
	StreamName string `json:"streamName"`
	Timestamp  int64  `json:"timestamp"`
	Sid        string `json:"sid"`
	Version    string `json:"version"`
	// Type 上报指标类型 1: 直播推流指标 2: 连麦指标 3: 播放指标
	Type int `json:"type"`
	// LiveEvent 事件类型 1: 推流成功 2: 推流开始时失败 3: 已经在推流，过程中发生错误，推流中断
	LiveEvent uint64 `json:"liveEvent"`

	// RtcEvent 事件类型 1: 加入连麦 2: 加入连麦成功 3: 收到首帧数据 4: 退出连麦成功 5: 更新连麦总时长
	RtcEvent uint64 `json:"rtcEvent"`
	// RtcReceiveFirstFrameDuration 加入连麦至收到首桢数据的耗时，收到首帧数据时上报 ms
	RtcReceiveFirstFrameDuration float64 `json:"rtcReceiveFirstFrameDuration"`
	// RtcJoinDuration 加入连麦至连麦成功的耗时，加入连麦成功事件上报 ms
	RtcJoinDuration float64 `json:"rtcJoinDuration"`
	// RtcDuration 本次连麦总时长，退出连麦时上报，连麦过程中也会定时上报 ms
	RtcDuration float64 `json:"rtcDuration"`

	// RtcTotalSamplesDuration WebRTC 接收 sample 总时长 ms
	RtcTotalSamplesDuration float64 `json:"rtcTotalSamplesDuration"`
	// RtcSilentConcealedDuration WebRTC 插入静音的时长 ms
	RtcSilentConcealedDuration float64 `json:"rtcSilentConcealedDuration"`
	// TotalSamplesDuration 播放时长 使用 SDK 内部播放时统计 ms
	TotalSamplesDuration float64 `json:"totalSamplesDuration"`
	// TotalSilentSamplesDuration 播放主动插入静音的时长 使用 SDK 内部播放器统计 ms
	TotalSilentSamplesDuration float64 `json:"totalSilentSamplesDuration"`
}

// ActionBvcliveSdkStatus bvclive status sdk 上报
/**
 * @api {post} /bvclive/sdk-status bvclive sdk 上报
 * @apiVersion 0.1.0
 * @apiGroup bvclive
 *
 * @apiParamExample:
 *   // 参数同 https://bvc.bilivideo.com/nerve-cthun/webrtc_client_sdk_status 接口
 *   [
 *     {
 *       "streamName": "maoer_uat_9075241_9075241",
 *       "timestamp": 1709794723918,
 *       "sid": "8d55ac71-c794-4a5b-8705-5e2240713663",
 *       "version": "1.12.3",
 *       "type": 1,
 *       "liveEvent": 1,
 *       ...
 *     }
 *   ]
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "message": "success",
 *       "data": null
 *     }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} message 相关错误信息
 */
func ActionBvcliveSdkStatus(c *handler.Context) (handler.ActionResponse, string, error) {
	body := make([]WebrtcClientSdk, 0)
	err := c.Bind(&body)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	ip := c.ClientIP()
	records := make([]livebvclivesdkstatus.LiveBvcLiveSdkStatus, 0, len(body))
	for _, s := range body {
		rec := livebvclivesdkstatus.LiveBvcLiveSdkStatus{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			IP:           ip,

			StreamName: s.StreamName,
			Timestamp:  s.Timestamp,
			Sid:        s.Sid,
			Version:    s.Version,
			Type:       s.Type,
			LiveEvent:  s.LiveEvent,

			RtcEvent:                     s.RtcEvent,
			RtcReceiveFirstFrameDuration: s.RtcReceiveFirstFrameDuration,
			RtcJoinDuration:              s.RtcJoinDuration,
			RtcDuration:                  s.RtcDuration,

			RtcTotalSamplesDuration:    s.RtcTotalSamplesDuration,
			RtcSilentConcealedDuration: s.RtcSilentConcealedDuration,
			TotalSamplesDuration:       s.TotalSamplesDuration,
			TotalSilentSamplesDuration: s.TotalSilentSamplesDuration,
		}
		records = append(records, rec)
	}
	err = livebvclivesdkstatus.BatchInsert(records)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	return nil, "success", nil
}
