package bvclive

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/logdb/livebvclivesdkstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionBvcliveSdkStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数不对
	c := handler.NewTestContext(http.MethodPost, "/sdk-status", false, nil)
	_, _, err := ActionBvcliveSdkStatus(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 正常上报
	params := []WebrtcClientSdk{
		{StreamName: "test_1"},
		{StreamName: "test_2"},
	}
	c = handler.NewTestContext(http.MethodPost, "/sdk-status", false, params)
	c.SetClientIP("127.0.0.1")
	_, msg, err := ActionBvcliveSdkStatus(c)
	require.NoError(err)
	assert.Equal("success", msg)

	streamName := make([]string, len(params))
	for i := range params {
		streamName[i] = params[i].StreamName
	}
	var records []livebvclivesdkstatus.LiveBvcLiveSdkStatus
	require.NoError(service.LogDB.Where("stream_name IN (?)", streamName).
		Find(&records).Error)
	require.Equal(len(params), len(records))
	assert.NotZero(records[0].CreateTime)
	assert.NotEmpty(records[0].IP)
}
