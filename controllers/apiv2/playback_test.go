package apiv2

import (
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGetPlaybacks(t *testing.T) {
	assert := assert.New(t)

	c := handler.CreateTestContext(false)
	c.C.Request = httptest.NewRequest(http.MethodGet,
		"/playback/list?p=1&room_id="+strconv.FormatInt(roomID, 10), nil)
	_, err := ActionGetPlaybacks(c)
	assert.NoError(err)
}
