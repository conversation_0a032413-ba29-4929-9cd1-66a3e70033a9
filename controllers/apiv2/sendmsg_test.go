package apiv2

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

var testForeverBlockUserID int64 = 2020101402

func TestActionMessageSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("go://scan/text", func(input any) (output any, err error) {
		return []any{map[string]bool{
			"pass": true,
		}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("im://broadcast", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	simple := liveuser.Simple{
		UID:          testForeverBlockUserID,
		Username:     "testForeverBlockUserID",
		IconURL:      "",
		Contribution: 0,
	}
	_, err := liveuser.Collection().UpdateOne(ctx, bson.M{"room_id": testForeverBlockUserID},
		bson.M{"$set": simple}, options.Update().SetUpsert(true))
	require.NoError(err)

	defer func() {
		_, _ = service.MongoDB.Collection("messages").DeleteMany(ctx, bson.M{"message": "body.message"})
	}()

	body := handler.M{
		"room_id": roomID,
		"message": "body.message",
	}

	c := handler.NewTestContext("POST", "/", true, body)
	c.User().ID = 10
	c.User().Mobile = "18945678901"
	data, err := ActionMessageSend(c)
	require.NoError(err)
	m := data.(handler.M)
	assert.EqualValues(1, m["ok"].(int))

	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testForeverBlockUserID
	c.User().Mobile = "18945678901"
	_, err = ActionMessageSend(c)
	assert.Equal(actionerrors.ErrBannedUser, err)
}
