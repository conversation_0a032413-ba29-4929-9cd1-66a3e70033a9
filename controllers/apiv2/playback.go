package apiv2

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// ActionGetPlaybacks handler
func ActionGetPlaybacks(c *handler.Context) (handler.ActionResponse, error) {
	p, _ := c.GetDefaultParamInt64("p", 1)
	roomID, _ := c.GetParamInt64("room_id")
	if p <= 0 {
		p = 1
	}
	var (
		pb        models.Playback
		playbacks []models.Playback
		pageCount int64
		err       error
	)
	if roomID <= 0 {
		// list all playbacks
		pageCount, err = pb.CountPage()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		if pageCount > 0 && p <= pageCount {
			playbacks, err = pb.FindByPage(p)
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
		}
	} else {
		var room struct {
			OID    primitive.ObjectID `bson:"_id"`
			RoomID int64              `bson:"room_id"`
		}
		err = utils.GetRoom(roomID, &room, "room_id")
		if err != nil {
			if !mongodb.IsNoDocumentsError(err) {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			return nil, actionerrors.ErrCannotFindRoom
		}
		pageCount, err = pb.CountRoomPlaybacksPage(room.OID, room.RoomID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if pageCount > 0 && p <= pageCount {
			playbacks, err = pb.FindRoomPlaybacksByPage(room.OID, room.RoomID, p)
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
		}
	}
	models.AddPlaybackInfo(playbacks)
	return handler.M{
		"playbacks": playbacks,
		// TODO 使用 master 分支的 struct 替换
		"pagination": handler.M{
			"page":       p,
			"page_count": pageCount,
		},
	}, nil
}
