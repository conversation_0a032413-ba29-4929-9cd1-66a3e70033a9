package apiv2

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/activity"
	"github.com/MiaoSiLa/live-service/controllers/admin"
	"github.com/MiaoSiLa/live-service/controllers/channel"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/controllers/dev"
	"github.com/MiaoSiLa/live-service/controllers/guild"
	"github.com/MiaoSiLa/live-service/controllers/noble"
	"github.com/MiaoSiLa/live-service/controllers/preview"
	"github.com/MiaoSiLa/live-service/controllers/recommended"
	"github.com/MiaoSiLa/live-service/controllers/report"
	"github.com/MiaoSiLa/live-service/controllers/shop"
	"github.com/MiaoSiLa/live-service/controllers/webuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/apisign"
	"github.com/MiaoSiLa/missevan-go/middlewares/security"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var csrfAllowURIs = []string{ // TODO: 后续使用 handler NewAction 支持
	"/api/v2/channel/connect/callback",
}

// Handler returns the registered handler
func Handler() *handler.Handler {
	v2Handler := handler.Handler{
		Name: "v2",
		SubHandlers: []handler.Handler{
			guild.Handler(),
			chatroom.Handler(),
			channel.Handler(),
			webuser.Handler(),
			metaHandler(),
			recommended.Handler(),
			noble.Handler(),
			activity.Handler(),
			shop.Handler(),
			preview.Handler(),

			report.Handler(),
			admin.Handler(),
			dev.Handler(),
		},
	}
	h := handler.Handler{
		Name: "api",
		Middlewares: gin.HandlersChain{
			security.CSRFWithAllowURIsMiddleware(config.Conf.HTTP.CSRFAllowTopDomains, csrfAllowURIs),
			appSign(),
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			v2Handler,
		},
		Actions: map[string]*handler.Action{
			"chatroom/gift/send": handler.NewAction(handler.POST, chatroom.ActionGiftSend, true),
		},
	}
	return &h
}

// HandlerV2 returns the registered handlerV2
func HandlerV2() *handler.HandlerV2 {
	h := handler.HandlerV2{
		Name: "api",
		Middlewares: gin.HandlersChain{
			security.CSRFWithIgnoreURIsMiddlewareV2(config.Conf.HTTP.CSRFAllowTopDomains, csrfAllowURIs),
			appSignV2(),
			user.Middleware(),
		},
		SubHandlers: []handler.HandlerV2{
			{
				Name: "v2",
				SubHandlers: []handler.HandlerV2{
					chatroom.HandlerV2(),
					webuser.HandlerV2(),
					admin.HandlerV2(),
				},
			},
		},
	}
	return &h
}

func appSign() gin.HandlerFunc {
	if config.Conf.HTTP.DisableAPISign {
		return func(c *gin.Context) {
			c.Next()
		}
	}
	signFunc := apisign.Middleware([]byte(config.Conf.HTTP.APISignKey))
	return func(c *gin.Context) {
		equip, err := goutil.ParseEquipment(c.Request)
		if err != nil {
			defer c.Next()
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code": v.Code,
					"info": v.Message,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}

func appSignV2() gin.HandlerFunc {
	if config.Conf.HTTP.DisableAPISign {
		return func(c *gin.Context) {
			c.Next()
		}
	}
	signFunc := apisign.MiddlewareV2([]byte(config.Conf.HTTP.APISignKey))
	return func(c *gin.Context) {
		equip, err := goutil.ParseEquipment(c.Request)
		if err != nil {
			defer c.Next()
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code":    v.Code,
					"message": v.Message,
					"data":    nil,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}

func metaHandler() handler.Handler {
	return handler.Handler{
		Name: "meta",
		Actions: map[string]*handler.Action{
			"data":           handler.NewAction(handler.GET, ActionGetMetaData, false),
			"banner":         handler.NewAction(handler.GET, ActionGetBanner, false),
			"diagnosticurls": handler.NewAction(handler.GET, ActionDiagnosticURLs, false),
		},
	}
}
