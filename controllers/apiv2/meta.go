package apiv2

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	confparam "github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	tabutil "github.com/MiaoSiLa/live-service/controllers/utils/tab"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const metaLiveURL = "/live/"

type metaMessages struct {
	RoomClose []string `json:"room_close"`
	RoomTip   string   `json:"room_tip,omitempty"` // 直播间内系统提示
}

func newMetaMessages() *metaMessages {
	return &metaMessages{
		RoomClose: []string{
			"主播正在拯救世界，待会再来看看吧~",
			"主播正在赶来的路上阿噜~",
			"主播在给主子铲屎中~",
		},
		RoomTip: config.Conf.Params.General.RoomTip,
	}
}

type metaDataResp struct {
	Compatible      bool                      `json:"compatible"`
	LiveURL         string                    `json:"live_url,omitempty"`
	CardFrameURL    string                    `json:"card_frame_url,omitempty"`
	Catalogs        []*catalog.LiveCatalog    `json:"catalogs,omitempty"`
	CustomTagGroups []*tag.CustomTagGroups    `json:"custom_tag_groups,omitempty"`
	Tabs            []*tab                    `json:"tabs,omitempty"`
	NobleList       []vip.Meta                `json:"noblelist"`
	Highness        *vip.Meta                 `json:"highness"`
	Messages        *metaMessages             `json:"messages,omitempty"`
	Interaction     config.SectionInteraction `json:"interaction"`
	LivePlayer      string                    `json:"live_player,omitempty"`
	NewEffectPlayer *int                      `json:"new_effect_player,omitempty"`
	NewUserPrize    *newUserPrize             `json:"new_user_prize,omitempty"`
	LiveTask        *liveTask                 `json:"live_task,omitempty"`
}

const (
	typeList    = "list"
	typeCatalog = "catalog"
	typeTag     = "tag"
)

type tab struct {
	Name        string `json:"name,omitempty"`
	Color       string `json:"color,omitempty"`
	IconURL     string `json:"icon_url,omitempty"`
	DarkIconURL string `json:"dark_icon_url,omitempty"`
	WebIconURL  string `json:"web_icon_url,omitempty"`
	SortOrder   int    `json:"-"`
	Active      bool   `json:"active,omitempty"`
	Type        string `json:"type,omitempty"`

	ListType  *int64 `json:"list_type,omitempty"`
	CatalogID int64  `json:"catalog_id,omitempty"`
	TagID     int64  `json:"tag_id,omitempty"`
}

type newUserPrize struct {
	PrizeName string `json:"prize_name,omitempty"`
	IconURL   string `json:"icon_url,omitempty"`
}

type liveTask struct {
	IconURL                   string        `json:"icon_url"`
	IconActiveURL             string        `json:"icon_active_url"`
	DailyListenRemainDuration int64         `json:"daily_listen_remain_duration"` // 单位：毫秒
	Gift                      *liveTaskGift `json:"gift,omitempty"`
}

type liveTaskGift struct {
	GiftID  int64  `json:"gift_id"`
	Name    string `json:"name"`
	IconURL string `json:"icon_url"`
	Price   int64  `json:"price"` // 钻
}

// ActionGetMetaData gets meta data for client, such as compatibility, gifts and room close messages.
/**
 * @api {get} /api/v2/meta/data Get Meta Data
 * @apiDescription Get meta data for client, such as compatibility, gifts and room close messages.
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/meta
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "catalogs": [
 *           {
 *             "catalog_id": 100,
 *             "catalog_name": "PIA 戏",
 *             "color": "#FFCD72",
 *             "icon_url": "http://test.png", // 图标为“日”字结构，上面是默认的情况，下面是选中的情况
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             // 二级分区
 *             "sub_catalogs": [
 *               {
 *                 "catalog_id": 109,
 *                 "catalog_name": "PIA 戏二级分区1",
 *                 "color": "#FFCD72",
 *                 "icon_url": "http://test.png", // 图标为“日”字结构，上面是默认的情况，下面是选中的情况
 *                 "dark_icon_url": "http://test.png",
 *                 "web_icon_url": "http://test.png"
 *               },
 *               {
 *                 "catalog_id": 110,
 *                 "catalog_name": "PIA 戏二级分区2",
 *                 "color": "#FFCD72",
 *                 "icon_url": "http://test.png", // 图标为“日”字结构，上面是默认的情况，下面是选中的情况
 *                 "dark_icon_url": "http://test.png",
 *                 "web_icon_url": "http://test.png"
 *               }
 *             ]
 *           },
 *           {
 *             "catalog_id": 101,
 *             "catalog_name": "唱见",
 *             "color": "#FF9E99",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png"
 *           },
 *           {
 *             "catalog_id": 102,
 *             "catalog_name": "闲聊",
 *             "color": "#CDEBF0",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png"
 *           },
 *           {
 *             "catalog_id": 103,
 *             "catalog_name": "综合",
 *             "color": "#D5FAB3",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png"
 *           }
 *         ],
 *         "custom_tag_groups": [ // https://info.missevan.com/pages/viewpage.action?pageId=109683915
 *           {
 *             "tag_group_id": 1,
 *             "tag_group_name": "人设（女性向）",
 *             "tags": [
 *               {
 *                 "tag_id": 10001,
 *                 "tag_name": "温柔叔音"
 *               },
 *               {
 *                 "tag_id": 10002,
 *                 "tag_name": "腹黑青叔"
 *               }
 *             ]
 *           }
 *         ],
 *         "tabs": [ // 仅对安卓 5.5.1 iOS 4.6.2 及以上版本返回
 *           { // 仅对安卓 6.3.5 iOS 6.3.5 及以上版本返回
 *             "name": "热门",
 *             "color": "#FFCD72", // 仅分区返回颜色
 *             "icon_url": "http://test.png", // 图标为“日”字结构，上面是默认的情况，下面是选中的情况
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "active": true, // 仅对安卓 6.3.5 iOS 6.3.5 及以上版本返回，表示默认选中，未返回时默认选中第一个
 *             "type": "list",
 *             "list_type": 0 // 仅对安卓 6.3.5 iOS 6.3.5 及以上版本返回，选择 Tab 后将此字段传给开播列表接口的 type 参数
 *           },
 *           {
 *             "catalog_id": 100,
 *             "name": "PIA 戏",
 *             "color": "#FFCD72", // 仅分区返回颜色
 *             "icon_url": "http://test.png", // 图标为“日”字结构，上面是默认的情况，下面是选中的情况
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "type": "catalog"
 *           },
 *           {
 *             "catalog_id": 101,
 *             "name": "唱见",
 *             "color": "#FFCD72",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "type": "catalog"
 *           },
 *           {
 *             "catalog_id": 102,
 *             "name": "闲聊",
 *             "color": "#FFCD72",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "type": "catalog"
 *           },
 *           {
 *             "catalog_id": 103,
 *             "name": "综合",
 *             "color": "#FFCD72",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "type": "catalog"
 *           },
 *           {
 *             "tag_id": 1,
 *             "name": "新星",
 *             "icon_url": "http://test.png",
 *             "dark_icon_url": "http://test.png",
 *             "web_icon_url": "http://test.png",
 *             "type": "catalog"
 *           }
 *         ],
 *         "compatible": true,
 *         "live_url": "/live/",
 *         "card_frame_url": "https://static-test.missevan.com/gifts/cardfarmes/000.png",
 *         "noblelist": [
 *           {
 *             "level": 1,
 *             "title": "神话",
 *             "effect_url": "https://static.example.com/effect.png",
 *             "effect_duration": 5000, // 购买贵族的房间内特效持续时间（毫秒）
 *             "icon_mini_url": "https://static.example.com/icon_mini.png",
 *             "avatar_frame_url": "https://static.example.com/avatar_frame.png",
 *             "notify_duration": 50000, // 购买贵族的房间内通知持续时间（毫秒）
 *             "card_frame_url": "https://static-test.missevan.com/gifts/cardframes/001.png"
 *           }
 *           // ...
 *         ],
 *         "highness": {
 *           "level": 1,
 *           "title": "上神",
 *           "icon_mini_url": "https://static.example.com/icon_mini.png"
 *         },
 *         "messages": { // 只有 Web 还下发
 *           "room_close": [
 *             "主播正在拯救世界，待会再来看看吧~",
 *             "主播正在赶来的路上阿噜~",
 *             "主播在给主子铲屎中~"
 *           ],
 *           "room_tip": "<font color='#FFC525'>系统提示：xxxxx</font>"
 *         },
 *         "interaction": {
 *           "vote_help_url": "https://fm.example.com/vote/help",
 *           "vote_agreement_url": "https://fm.example.com/vote/agreement"
 *         },
 *         "live_player": "bvc", // 客户端直播间播放器类型，ksyun: 金山云播放器; bvc: 视频云播放器（网页端没有此字段）
 *         "new_effect_player": 1, // 客户端直播间特效播放器类型，0: 使用旧版，1: 使用新版（网页端没有此字段）
 *         "new_user_prize": { // 新客奖励信息
 *           "prize_name": "新客奖励×10",
 *           "icon_url": "https://static-test.maoercdn.com/live/gifts/icons/30010.png"
 *         },
 *         "live_task": {
 *           "icon_url": "https://static-test.maoercdn.com/icon.png",
 *           "icon_active_url": "https://static-test.maoercdn.com/icon.webp",
 *           "daily_listen_remain_duration": 30000, // 今日收听任务剩余时间，为 0 时无需调用心跳接口，单位：毫秒
 *           "gift": { // 花费钻石使用的礼物信息，没有该字段不显示送礼按钮
 *              "gift_id": 125,
 *              "name": "一期一会",
 *              "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *              "price": 1 // 礼物价格
 *           }
 *         }
 *       }
 *     }
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 */
func ActionGetMetaData(c *handler.Context) (handler.ActionResponse, error) {
	var (
		catalogs []*catalog.LiveCatalog
		err      error
	)
	e := c.Equip()

	// WORKAROUND: 仅对安卓 5.5.7, iOS 4.6.8 以下版本只返回二级分区
	var catalogsForOldWeb bool
	config.GetAB("catalogs_for_old_web", &catalogsForOldWeb)
	if (e.FromApp && e.IsAppOlderThan("4.6.8", "5.5.7")) || (!e.FromApp && catalogsForOldWeb) {
		catalogs, err = catalog.LiveSubCatalogs(false)
		// 将展平后的二级分区复制一份加到其子分区下，防止 web 上线后后端没有及时更新配置导致主播没有分区可选
		for _, liveCatalog := range catalogs {
			c := *liveCatalog
			liveCatalog.SubCatalogs = []*catalog.LiveCatalog{&c}
		}
	} else {
		var tmp []*catalog.LiveCatalog
		tmp, err = catalog.LiveCatalogs(false)
		catalogs = make([]*catalog.LiveCatalog, 0, len(tmp))
		// 不返回没有二级分区的一级分区
		for _, c := range tmp {
			if len(c.SubCatalogs) > 0 {
				catalogs = append(catalogs, c)
			}
		}
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	tabs, err := getTabsWithListType(c)
	if err != nil {
		return nil, err
	}

	nobleMeta, err := vip.NobleMetas()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	highnessMeta, err := vip.HighnessMeta()
	if err != nil {
		return nil, err
	}

	customTagGroups, err := tag.ListAllCustomTagGroups()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	liveTask, err := getLiveTask(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &metaDataResp{
		Compatible:      !e.IsAppOlderThan("4.6.0", "5.4.9"), // 仅对 5 个版本进行兼容
		Catalogs:        catalogs,
		Tabs:            tabs,
		NobleList:       nobleMeta,
		Highness:        highnessMeta,
		Interaction:     config.Conf.Params.Interaction,
		LivePlayer:      livePlayer(c.BUVID(), e),
		NewEffectPlayer: newEffectPlayer(c.BUVID(), e),
		CustomTagGroups: customTagGroups,
		NewUserPrize:    getNewUserPrize(),
		LiveTask:        liveTask,
	}
	// WORKAROUND: 下面的配置已移动到 site/config 接口下发，这里仅对 web、安卓 < 6.0.6、iOS < 6.0.6 的版本返回
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{IOS: "6.0.6", Android: "6.0.6"}) {
		resp.LiveURL = metaLiveURL
		resp.CardFrameURL = service.Storage.Parse(config.Conf.Params.NobleParams.DefaultCardFrame)
		resp.Messages = newMetaMessages()
	}
	return resp, nil
}

func getTabs() ([]*tab, error) {
	result, err := service.LRURedis.Get(keys.KeyLiveMetaTabs0.Format()).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		// PASS
	} else if result != "" {
		var list []*tab
		if err = json.Unmarshal([]byte(result), &list); err == nil {
			return list, nil
		}
		logger.Error(err)
		// PASS
	}
	tags, err := tag.FindTags(false, tag.TypeLiveTag)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 缓存不带有 sort_order 字段，不能用缓存
	catalogs, err := catalog.FindCatalog(false)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	tabs := make([]*tab, 0, len(tags)+len(catalogs))
	for _, catalog := range catalogs {
		t := &tab{
			Color:     catalog.Color,
			SortOrder: catalog.SortOrder,
			Type:      typeCatalog,
			CatalogID: catalog.ID,
			Name:      catalog.CatalogName,
		}
		if catalog.IconURL != nil {
			t.IconURL = *catalog.IconURL
		}
		if catalog.DarkIconURL != nil {
			t.DarkIconURL = *catalog.DarkIconURL
		}
		if catalog.WebIconURL != nil {
			t.WebIconURL = *catalog.WebIconURL
		}
		tabs = append(tabs, t)
	}

	for _, t := range tags {
		if t.ID == tag.TagListenDrama {
			// 若听剧标签下没有直播间，则不下发听剧标签
			if !hasRoomUnderTagID(t.ID) {
				continue
			}
		}
		tabs = append(tabs, &tab{
			IconURL:     t.IconURL,
			DarkIconURL: t.DarkIconURL,
			WebIconURL:  t.WebIconURL,
			SortOrder:   t.SortOrder,
			Type:        typeTag,
			TagID:       t.ID,
			Name:        t.TagName,
		})
	}
	sort.Slice(tabs, func(i, j int) bool {
		return tabs[i].SortOrder > tabs[j].SortOrder
	})
	str, err := json.Marshal(tabs)
	if err != nil {
		logger.Error(err)
		return tabs, nil
	}

	err = service.LRURedis.Set(keys.KeyLiveMetaTabs0.Format(), str, 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return tabs, nil
}

// hasRoomUnderTagID 是否有直播间在该标签下
func hasRoomUnderTagID(tagID int64) bool {
	ok, err := room.Exists2(bson.M{
		"tag_ids":     tagID,
		"status.open": room.StatusOpenTrue,
	})
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return ok
}

func getNewUserPrize() *newUserPrize {
	rewardParam, err := params.FindReward()
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if rewardParam.LiveNewUserRewardID <= 0 {
		return nil
	}

	r, err := reward.FindRewardByRewardIDWithCache(rewardParam.LiveNewUserRewardID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"reward_id": rewardParam.LiveNewUserRewardID,
		}).Error(err)
		// PASS
		return nil
	}
	if r == nil {
		logger.WithFields(logger.Fields{
			"reward_id": rewardParam.LiveNewUserRewardID,
		}).Error("reward not found")
		return nil
	}
	item, err := r.FindBackpackItem()
	if err != nil {
		logger.WithFields(logger.Fields{
			"reward_id":  r.RewardID,
			"type":       r.Type,
			"element_id": r.ElementID,
		}).Error(err)
		// PASS
		return nil
	}
	if item == nil {
		logger.WithFields(logger.Fields{
			"reward_id":  r.RewardID,
			"type":       r.Type,
			"element_id": r.ElementID,
		}).Error("backpack item not found")
		return nil
	}
	return &newUserPrize{
		PrizeName: fmt.Sprintf("%s×%d", item.Name, item.Num),
		IconURL:   item.IconURL,
	}
}

func getLiveTask(userID int64) (*liveTask, error) {
	param, err := params.FindLiveTask()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	task := &liveTask{
		IconURL:       param.IconURL,
		IconActiveURL: param.IconActiveURL,
	}

	g, err := gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g != nil {
		task.Gift = &liveTaskGift{
			GiftID:  g.GiftID,
			Name:    g.Name,
			IconURL: g.Icon,
			Price:   g.Price,
		}
	}

	if userID <= 0 {
		return task, nil
	}

	isNew, err := livelistenlogs.IsNewUser(userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	todayListenDuration, err := livelistenlogs.UserTodayAppListenDuration(userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	listenDuration := int64(0)
	if todayListenDuration != nil {
		listenDuration = *todayListenDuration
	}
	var dailyListenDuration int64
	if isNew {
		dailyListenDuration = param.DailyListenDuration.NewUserFinishDuration - listenDuration
	} else {
		dailyListenDuration = param.DailyListenDuration.OldUserFinishDuration - listenDuration
	}
	task.DailyListenRemainDuration = max(dailyListenDuration, 0)
	return task, nil
}

type bannerCache struct {
	Base []*models.Banner `json:"base"`
	Live []*models.Banner `json:"live"`
}

// ActionGetBanner handler
/**
 * @api {get} /api/v2/meta/banner Get banner
 * @apiDescription Get banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/meta
 *
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "banners": [
 *           {
 *             "url": "https://www.missevan.com",
 *             "app_url": "missevan://live/1",
 *             "image_url": "http://static.missevan.com/dramacoversmini/201604/15/c.png",
 *             "small_image_url": "http://static.missevan.com/dramacoversmini/201604/15/c.png",
 *             "title": "1"
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 */
func ActionGetBanner(c *handler.Context) (handler.ActionResponse, error) {
	key, cache := getCacheBanner()
	var err error
	var banners, liveBanners []*models.Banner
	if cache != nil {
		banners = cache.Base
		liveBanners = cache.Live
	} else {
		banners, err = models.FindAllBanners()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		liveBanners, err = getLiveBanners()
		if err != nil {
			return nil, err
		}
		cache = &bannerCache{
			Base: banners,
			Live: liveBanners,
		}
		v, err := json.Marshal(cache)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			err = service.Redis.Set(key, v, 5*time.Minute).Err()
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}
	rand.Shuffle(len(liveBanners), func(i, j int) {
		liveBanners[i], liveBanners[j] = liveBanners[j], liveBanners[i]
	})
	sep := 0
	for ; sep < len(banners); sep++ {
		// NOTICE: order 如果在数据库中被设置为 null 排序结果会在最前面
		if banners[sep].Order == nil || *banners[sep].Order >= 0 {
			break
		}
	}

	results := make([]*models.Banner, 0, len(banners)+len(liveBanners))
	results = append(results, banners[:sep]...)
	results = append(results, liveBanners...)
	results = append(results, banners[sep:]...)
	equip := c.Equip()
	compatibleWithOld := equip.IsAppOlderThan("4.6.6", "5.5.5")
	for i := range results {
		// WORKAROUND: 老版本即 iOS 4.6.6 和 Android 5.5.5 之前的版本需要返回 SmallImageURL
		if compatibleWithOld {
			if results[i].SmallImageURL == "" {
				results[i].SmallImageURL = results[i].ImageURL
			}
		}
		// 不返回 order 给前端
		results[i].Order = nil
	}

	// 查找并过滤未开播的直播 banner
	results, err = findAndFilterLiveBanners(results)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return handler.M{
		"banners": results,
	}, err
}

// 查找并检查直播 banner 的直播间是否在直播状态
func findAndFilterLiveBanners(allBanners []*models.Banner) ([]*models.Banner, error) {
	if len(allBanners) == 0 {
		return []*models.Banner{}, nil
	}
	roomIDs := make([]int64, 0, len(allBanners))
	for _, banner := range allBanners {
		if banner.RoomID != 0 {
			roomIDs = append(roomIDs, banner.RoomID)
			continue
		}
		if roomID, ok := confparam.ParseRoomIDByRoomURL(banner.URL); ok {
			banner.RoomID = roomID
			roomIDs = append(roomIDs, banner.RoomID)
		}
	}
	if len(roomIDs) == 0 {
		return allBanners, nil
	}
	liveList, err := live.FindOpenLiveByRoomIDs(roomIDs)
	if err != nil {
		return nil, err
	}
	mp := make(map[int64]bool, len(liveList))
	for _, liveItem := range liveList {
		mp[liveItem.RoomID] = true
	}
	resultBanners := make([]*models.Banner, 0, len(allBanners))
	for _, banner := range allBanners {
		if banner.RoomID == 0 {
			resultBanners = append(resultBanners, banner)
		} else {
			if mp[banner.RoomID] {
				resultBanners = append(resultBanners, banner)
			}
		}
	}
	return resultBanners, nil
}

func getLiveBanners() ([]*models.Banner, error) {
	records, err := liverecommendedelements.ListLiveBannersAt(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	liveBanners := make([]*models.Banner, len(records))
	for i, v := range records {
		liveBanners[i] = new(models.Banner)
		urls := strings.Split(v.Cover, ";")
		if len(urls) == 2 {
			liveBanners[i].ImageURL, liveBanners[i].SmallImageURL = storage.ParseSchemeURL(urls[0]), storage.ParseSchemeURL(urls[1])
		} else {
			logger.Error("封面图错误")
			// PASS
		}
		liveBanners[i].URL = fmt.Sprintf(config.Conf.Params.LiveURL.WebRoom, v.ElementID)
		liveBanners[i].AppURL = fmt.Sprintf(config.Conf.Params.LiveURL.AppRoom, v.ElementID)
		liveBanners[i].Title = v.Name
		liveBanners[i].RoomID = v.ElementID
	}
	return liveBanners, nil
}

func getCacheBanner() (string, *bannerCache) {
	key := keys.KeyBannersMetaBanner1.Format(goutil.TimeNow().Minute() / 5)
	v, err := service.Redis.Get(key).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
		}
		return key, nil
	}
	var res bannerCache
	err = json.Unmarshal([]byte(v), &res)
	if err != nil {
		logger.Error(err)
		return key, nil
	}
	return key, &res
}

const (
	livePlayerKsyun = "ksyun"
	livePlayerBVC   = "bvc"
)

const (
	abTestLivePlayer   = "live_player"
	abTestEffectPlayer = "effect_player"
)

// livePlayer 灰度客户端视频云直播间播放器
func livePlayer(buvid string, e *goutil.Equipment) string {
	if e == nil {
		panic("undefined equipment")
	}
	if !e.FromApp {
		// 网页端
		return ""
	}
	if e.OS == goutil.Android && e.AppVersion == "5.5.4" {
		osVersion := strings.Split(e.OSVersion, ".")[0]
		if osVersion == "5" || osVersion == "4" {
			// WORKAROUND: 安卓 5.5.4 客户端版本在 5.x 4.x 的手机上不灰度
			return livePlayerKsyun
		}
	}
	if e.OS == goutil.IOS && e.IsAppOlderThan("4.7.3", "") {
		//  WORKAROUND: iOS < 4.7.3 的版本拉流播放器返回金山云
		return livePlayerKsyun
	}
	if abTest(abTestLivePlayer, e.OS, buvid) {
		return livePlayerBVC
	}
	return livePlayerKsyun
}

// newEffectPlayer 灰度特效播放器
func newEffectPlayer(buvid string, e *goutil.Equipment) *int {
	if e == nil {
		panic("undefined equipment")
	}
	if !e.FromApp {
		// 网页端
		return nil
	}
	if e.OS == goutil.IOS && e.IsAppOlderThan("4.7.4", "") {
		// WORKAROUND: iOS 4.7.4 之前的版本不使用新版特效播放器
		return util.NewInt(0)
	}
	if abTest(abTestEffectPlayer, e.OS, buvid) {
		return util.NewInt(1)
	}
	return util.NewInt(0)
}

// TODO: 支持根据平台设置灰度比例
func abTest(abtestKey string, os goutil.Platform, salt string) bool {
	key := keys.KeyABTest2.Format(abtestKey, os)
	val, err := service.Redis.Get(key).Int64()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		return false
	}
	// 用 key 做后缀防止用户群体聚集，打散用户
	if util.CRC32(salt+key)%100 < val {
		return true
	}
	return false
}

func getTabsWithListType(c *handler.Context) ([]*tab, error) {
	tabs, err := getTabs()
	if err != nil {
		return nil, err
	}

	e := c.Equip()
	// WORKAROUND: 安卓 < 6.3.5 和 iOS < 6.3.5 的版本不支持下发热门分区，因此直接返回常规分区
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{Android: "6.3.5", IOS: "6.3.5"}) {
		return tabs, nil
	}

	hotTab := &tab{
		Name:        "热门",
		Type:        typeList,
		ListType:    goutil.NewInt64(chatroom.OpenListTypeHot), // 热门分区
		IconURL:     storage.ParseSchemeURL(config.Conf.Params.LiveFeed.HotIcon),
		DarkIconURL: storage.ParseSchemeURL(config.Conf.Params.LiveFeed.HotDarkIcon),
		WebIconURL:  storage.ParseSchemeURL(config.Conf.Params.LiveFeed.HotWebIcon),
	}

	if tabutil.IsShowFeedTab(c) {
		// 实验组额外添加推荐分区 tab，并将其默认选中
		recTab := &tab{
			Name:        "推荐",
			Type:        typeList,
			ListType:    goutil.NewInt64(chatroom.OpenListTypeRecommend), // 推荐分区
			IconURL:     storage.ParseSchemeURL(config.Conf.Params.LiveFeed.RecommendIcon),
			DarkIconURL: storage.ParseSchemeURL(config.Conf.Params.LiveFeed.RecommendDarkIcon),
			WebIconURL:  storage.ParseSchemeURL(config.Conf.Params.LiveFeed.RecommendWebIcon),
			Active:      true, // 默认选中推荐
		}
		tabs = append([]*tab{recTab, hotTab}, tabs...)
	} else {
		// 对照组只添加热门分区
		hotTab.Active = true
		tabs = append([]*tab{hotTab}, tabs...)
	}

	return tabs, nil
}
