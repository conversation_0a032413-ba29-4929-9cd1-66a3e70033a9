package apiv2

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const roomID = int64(22489473)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestAppSign(t *testing.T) {
	assert := assert.New(t)
	h := appSign()
	resp := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.117 Safari/537.36")
	h(c)
	assert.Equal(http.StatusOK, resp.Code)

	resp = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "MissEvanApp/4.1.5 (iOS;10.3;iPhone8,1)")
	h(c)
	assert.Equal(http.StatusUnauthorized, resp.Code)

	config.Conf.HTTP.DisableAPISign = true
	h = appSign()
	resp = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "MissEvanApp/4.1.5 (iOS;10.3;iPhone8,1)")
	h(c)
	assert.Equal(http.StatusOK, resp.Code)
}

func TestAppSignV2(t *testing.T) {
	assert := assert.New(t)
	h := appSignV2()
	resp := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.117 Safari/537.36")
	h(c)
	assert.Equal(http.StatusOK, resp.Code)

	resp = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "MissEvanApp/4.1.5 (iOS;10.3;iPhone8,1)")
	h(c)
	assert.Equal(http.StatusUnauthorized, resp.Code)

	config.Conf.HTTP.DisableAPISign = true
	h = appSignV2()
	resp = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(resp)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Add("User-Agent", "MissEvanApp/4.1.5 (iOS;10.3;iPhone8,1)")
	h(c)
	assert.Equal(http.StatusOK, resp.Code)
}

func TestHandler(t *testing.T) {
	t.Run("api", func(t *testing.T) {
		assert := assert.New(t)
		h := Handler()
		assert.Equal("api", h.Name)
		assert.Equal(1, len(h.SubHandlers))
		assert.Equal(3, len(h.Middlewares))
	})
	t.Run("meta", func(t *testing.T) {
		assert := assert.New(t)
		h := metaHandler()
		assert.Equal("meta", h.Name)
		assert.Empty(tutil.KeyExists(tutil.Actions, h, "data", "banner", "diagnosticurls"))
	})
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("api", h.Name)
	assert.Equal(1, len(h.SubHandlers))
	assert.Equal(3, len(h.Middlewares))
}
