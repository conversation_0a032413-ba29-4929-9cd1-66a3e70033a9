package apiv2

import (
	"encoding/json"
	"net/http"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMetaKeyCheck(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(tab{}, "name", "color", "icon_url", "dark_icon_url",
		"web_icon_url", "type", "catalog_id", "tag_id", "active", "list_type",
	)

	kc.Check(metaDataResp{}, "compatible", "live_url", "card_frame_url",
		"catalogs", "custom_tag_groups", "tabs", "noblelist", "highness",
		"messages", "interaction", "live_player",
		"new_effect_player", "new_user_prize", "live_task",
	)
}

func TestActionGetMetaData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := vip.MockVipList()
	defer cancel()
	c := handler.NewTestContext("GET", "/meta/data", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "1.2.3"
	data, err := ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp := data.(*metaDataResp)
	tutil.PrintJSON(data)
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(resp.Messages, "room_close", "room_tip")
	assert.Equal(newMetaMessages(), resp.Messages)
	assert.False(resp.Compatible)
	assert.NotEmpty(resp.Interaction.VoteAgreementURL)
	assert.NotEmpty(resp.Interaction.VoteHelpURL)
	assert.NotEmpty(resp.Catalogs)
	require.NotNil(resp.Highness)
	assert.NotEmpty(resp.Highness.Title)
	assert.Equal(vip.TypeLiveHighness, resp.Highness.Type)
	assert.Equal(2, len(resp.CustomTagGroups))

	c = handler.NewTestContext("GET", "/meta/data", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "6.0.6"
	data, err = ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp = data.(*metaDataResp)
	assert.Empty(resp.LiveURL)
	assert.Empty(resp.CardFrameURL)
	assert.Nil(resp.Messages)
	require.NotNil(resp.NewUserPrize)
	assert.Equal("幸共此时×10", resp.NewUserPrize.PrizeName)
	assert.Equal("https://static-test.missevan.com/live/gifts/icons/40047.png", resp.NewUserPrize.IconURL)

	// 测试 tabs
	c = handler.NewTestContext("GET", "/meta/data", false, nil)
	data, err = ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp = data.(*metaDataResp)
	assert.NotEmpty(resp.Catalogs)

	fn := func(tabs []*tab) bool {
		for _, tab := range tabs {
			if tab.Type == typeTag {
				return true
			}
		}
		return false
	}
	assert.True(fn(resp.Tabs))
	assert.True(sort.SliceIsSorted(resp.Tabs, func(a, b int) bool {
		return resp.Tabs[a].SortOrder > resp.Tabs[b].SortOrder
	}))

	c = handler.NewTestContext("GET", "/meta/data", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.6.8"
	c.Equip().FromApp = true
	data, err = ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp = data.(*metaDataResp)
	for _, liveCatalog := range resp.Catalogs {
		assert.NotNil(liveCatalog.SubCatalogs)
	}

	c = handler.NewTestContext("GET", "/meta/data", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.6.6"
	c.Equip().FromApp = true
	data, err = ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp = data.(*metaDataResp)
	for _, liveCatalog := range resp.Catalogs {
		assert.NotNil(liveCatalog.SubCatalogs)
	}

	// 二级分区 web 老版本展开
	c = handler.NewTestContext("GET", "/meta/data", false, nil)
	c.Equip().FromApp = false
	config.Conf.AB["catalogs_for_old_web"] = true
	data, err = ActionGetMetaData(c)
	require.NoError(err)
	require.NotNil(data)
	resp = data.(*metaDataResp)
	for _, liveCatalog := range resp.Catalogs {
		assert.NotNil(liveCatalog.SubCatalogs)
	}
}

func TestGetTabs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyLiveMetaTabs0.Format()
	require.NoError(service.LRURedis.Del(key).Err())
	res, err := getTabs()
	require.NoError(err)
	catalogs, err := catalog.FindCatalog(false)
	require.NoError(err)
	tags, err := tag.FindTags(false, tag.TypeLiveTag)
	require.NoError(err)
	require.Equal(len(tags)+len(catalogs), len(res))
	assert.True(sort.SliceIsSorted(res, func(a, b int) bool {
		return res[a].SortOrder > res[b].SortOrder
	}))

	require.NotNil(catalogs[0].DarkIconURL)
	assert.Condition(func() bool {
		for i := range res {
			if res[i].Type == typeCatalog && res[i].CatalogID == catalogs[0].ID {
				return *catalogs[0].DarkIconURL == res[i].DarkIconURL
			}
		}
		return false
	})

	res, err = getTabs()
	require.NoError(err)
	assert.True(sort.SliceIsSorted(res, func(a, b int) bool {
		return res[a].SortOrder > res[b].SortOrder
	}))

	require.NoError(service.LRURedis.Set(key, "[]", 10*time.Second).Err())
	res, err = getTabs()
	require.NoError(err)
	assert.Len(res, 0)

	require.NoError(service.LRURedis.Del(key).Err())
}

func TestHasRoomUnderTagID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := hasRoomUnderTagID(131231231123211)
	assert.False(ok)

	err := room.AddTag([]int64{223344}, tag.TagListenDrama)
	require.NoError(err)
	ok = hasRoomUnderTagID(tag.TagListenDrama)
	assert.True(ok)
}

func TestGetNewUserPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(params.KeyReward)
	err := service.LRURedis.Del(cacheKey).Err()
	require.NoError(err)

	// 测试下发的是背包礼物
	prize := getNewUserPrize()
	require.NotNil(prize)
	assert.Equal("幸共此时×10", prize.PrizeName)
	assert.Equal("https://static-test.missevan.com/live/gifts/icons/40047.png", prize.IconURL)

	// 测试下发背包道具
	cacheByte, err := json.Marshal(params.Reward{
		LiveNewUserRewardID: 21,
	})
	require.NoError(err)
	err = service.LRURedis.Set(cacheKey, string(cacheByte), 10*time.Second).Err()
	require.NoError(err)
	prize = getNewUserPrize()
	require.NotNil(prize)
	assert.Equal("大咖体验卡 1 天×1", prize.PrizeName)
	assert.Equal("https://static-test.missevan.com/backpack_item/vip_card/icon.png", prize.IconURL)

	// 测试奖励类型不正确
	cacheByte, err = json.Marshal(params.Reward{
		LiveNewUserRewardID: 1,
	})
	require.NoError(err)
	err = service.LRURedis.Set(cacheKey, string(cacheByte), 10*time.Second).Err()
	require.NoError(err)
	prize = getNewUserPrize()
	require.Nil(prize)
}

func TestGetLiveTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(params.KeyLiveTask)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	testUserID := int64(9074509)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livelistenlogs.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	task, err := getLiveTask(0)
	require.NoError(err)
	require.NotNil(task)
	assert.Nil(task.Gift)
	assert.Zero(task.DailyListenRemainDuration)

	v := params.DefaultLiveTask()
	v.GiftID = 8
	b, err := json.Marshal(v)
	require.NoError(err)
	err = service.LRURedis.Set(cacheKey, string(b), 10*time.Second).Err()
	require.NoError(err)

	// 测试新用户
	beginTime := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserDailyTaskIsNewStatus2.Format(testUserID, beginTime.Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.LRURedis.Set(key, 1, time.Minute).Err())
	task, err = getLiveTask(testUserID)
	require.NoError(err)
	require.NotNil(task)
	require.NotNil(task.Gift)
	assert.Equal(v.DailyListenDuration.NewUserFinishDuration, task.DailyListenRemainDuration)

	// 测试老用户
	require.NoError(service.LRURedis.Set(key, 0, time.Minute).Err())
	task, err = getLiveTask(testUserID)
	require.NoError(err)
	require.NotNil(task)
	require.NotNil(task.Gift)
	assert.Equal(v.DailyListenDuration.OldUserFinishDuration, task.DailyListenRemainDuration)
}

func TestFindAndFilterLiveBanners(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	liveBanners := []*models.Banner{
		&models.Banner{
			URL: "https://fm.uat.missevan.com/live/61618636",
		},
		&models.Banner{
			URL:    "https://fm.uat.missevan.com/live/61618637",
			RoomID: 61618637,
		},
		&models.Banner{
			URL: "https://www.missevan.com/mtopic/item/53",
		},
	}
	var err error
	liveBanners, err = findAndFilterLiveBanners(liveBanners)
	require.NoError(err)
	assert.Len(liveBanners, 2)
	assert.EqualValues(61618636, liveBanners[0].RoomID)

	// 测试无直播 banner 的情况
	liveBanners = []*models.Banner{
		&models.Banner{
			URL: "https://www.missevan.com/mtopic/item/51",
		},
		&models.Banner{
			URL: "https://www.missevan.com/mtopic/item/52",
		},
		&models.Banner{
			URL: "https://www.missevan.com/mtopic/item/53",
		},
	}
	liveBanners, err = findAndFilterLiveBanners(liveBanners)
	require.NoError(err)
	assert.Len(liveBanners, 3)
}

func TestActionGetBanner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	setTop := -1
	banner := models.Banner{
		URL:   "url",
		Title: "super banner",
		Order: &setTop,
	}

	transform := func(before interface{}) []*models.Banner {
		b, err := json.Marshal(before)
		require.NoError(err)
		after := make([]*models.Banner, 0)
		err = json.Unmarshal(b, &after)
		require.NoError(err)
		return after
	}

	collection := service.MongoDB.Collection("banners")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := collection.UpdateOne(ctx, bson.M{"order": -1},
		bson.M{"$set": banner}, options.Update().SetUpsert(true))
	require.NoError(err)

	defer goutil.SetTimeNow(nil)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(86400, 0)
	})
	key := keys.KeyBannersMetaBanner1.Format(goutil.TimeNow().Minute() / 5)
	require.NoError(service.Redis.Del(key).Err())
	c := handler.NewTestContext(http.MethodGet, "/meta/banner", false, nil)
	r, err := ActionGetBanner(c)
	require.NoError(err)
	m := r.(handler.M)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(m, "banners")
	bannersNoCache := transform(m["banners"])
	// 检查缓存
	cacheJSON, err := service.Redis.Get(key).Result()
	require.NoError(err)
	var bc bannerCache
	require.NoError(json.Unmarshal([]byte(cacheJSON), &bc))
	// 过滤掉未开播直播间
	bc.Base, err = findAndFilterLiveBanners(bc.Base)
	require.NoError(err)
	for i := range bc.Base {
		bc.Base[i].Order = nil
		assert.Contains(bannersNoCache, bc.Base[i])
	}
	// 过滤掉未开播直播间
	bc.Live, err = findAndFilterLiveBanners(bc.Live)
	require.NoError(err)
	for i := range bc.Live {
		assert.Contains(bannersNoCache, bc.Live[i])
	}

	r, err = ActionGetBanner(c)
	require.NoError(err)
	m = r.(handler.M)
	bannersCache := transform(m["banners"])
	assert.ElementsMatch(bannersNoCache, bannersCache)
}

func TestLivePlayer(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	keyIOS := keys.KeyABTest2.Format("live_player", goutil.IOS)
	keyAndroid := keys.KeyABTest2.Format("live_player", goutil.Android)
	require.NoError(service.Redis.Del(keyIOS, keyAndroid).Err())
	equipIOS := &goutil.Equipment{OS: goutil.IOS, FromApp: true, AppVersion: "4.7.3"}
	equipAndroid := &goutil.Equipment{OS: goutil.Android, FromApp: true}
	assert.Empty(livePlayer("123", &goutil.Equipment{OS: goutil.Web}), "web")
	assert.Equal("ksyun", livePlayer("123", equipAndroid), "没配置的情况 Android")
	assert.Equal("ksyun", livePlayer("123", equipIOS), "没配置的情况 iOS")
	require.NoError(service.Redis.Set(keyIOS, 0, time.Second).Err())
	require.NoError(service.Redis.Set(keyAndroid, 0, time.Second).Err())
	assert.Equal("ksyun", livePlayer("123", equipAndroid), "不在灰度中 Android")
	assert.Equal("ksyun", livePlayer("123", equipIOS), "不在灰度中 iOS")

	require.NoError(service.Redis.Set(keyIOS, 100, time.Second).Err())
	require.NoError(service.Redis.Set(keyAndroid, 100, time.Second).Err())
	assert.Equal("bvc", livePlayer("123", equipAndroid), "灰度中 Android")
	equipAndroid.AppVersion = "5.5.4"
	equipAndroid.OSVersion = "4.2"
	assert.Equal("ksyun", livePlayer("123", equipAndroid), "特殊版本安卓")
	assert.Equal("bvc", livePlayer("123", equipIOS), "灰度中 iOS")
	equipIOS.AppVersion = "4.7.2"
	assert.Equal("ksyun", livePlayer("123", equipIOS), "特殊版本 iOS")
}

func TestNewEffectPlayer(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e := &goutil.Equipment{
		FromApp:    false,
		OS:         goutil.IOS,
		AppVersion: "4.7.4",
	}
	key := keys.KeyABTest2.Format(abTestEffectPlayer, e.OS)
	require.NoError(service.Redis.Del(key).Err())
	assert.Nil(newEffectPlayer("123", e), "网页")
	e.FromApp = true
	assert.Equal(util.NewInt(0), newEffectPlayer("123", e), "没配置")
	require.NoError(service.Redis.Set(key, 100, time.Second).Err())
	assert.Equal(util.NewInt(1), newEffectPlayer("123", e), "灰度 100")
	e.AppVersion = "4.7.3"
	assert.Equal(util.NewInt(0), newEffectPlayer("123", e), "旧版本 iOS")
}
