package apiv2

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestGetPullURLs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(updateRoomsPushing())
	deletePullURLsInCache()
	defer deletePullURLsInCache()
	defer deletePullURLsLock()
	c := handler.NewTestContext("POST", "", true, nil)
	pullURLs1, err := getPullURLs(c)
	require.NoError(err)
	assert.Equal(6, len(pullURLs1))
	for _, v := range pullURLs1 {
		assert.GreaterOrEqual(len(v.URLs), 2)
	}
	// 读取上一次操作自动设置的缓存
	pullURLs2, err := getPullURLs(c)
	require.NoError(err)
	assert.True(reflect.DeepEqual(pullURLs1, pullURLs2))
	// 手动设置缓存
	setPullURLsInCache(pullURLs1[:1])
	pullURLs3, err := getPullURLs(c)
	require.NoError(err)
	assert.Equal(1, len(pullURLs3))
	assert.Equal(pullURLs1[0], pullURLs3[0])
	// 申请分布式锁
	assert.True(applyPullURLsLock())
	deletePullURLsInCache()
	// 申请分布式锁失败时，从缓存中无法获取数据
	_, err = getPullURLs(c)
	require.Equal("服务器繁忙，请稍后再试", err.Error())
	// 申请分布式锁失败时，从缓存中成功获取数据
	setPullURLsInCache(pullURLs1[:2])
	pullURLs4, err := getPullURLs(c)
	require.NoError(err)
	assert.Equal(2, len(pullURLs4))
	assert.Equal(pullURLs1[0], pullURLs4[0])
}

// updateRoomsPushing 更新一些房间的状态为推流中，用于单元测试
func updateRoomsPushing() error {
	testRoom := room.Helper{
		Type:   "live",
		Status: room.Status{Open: room.StatusOpenTrue, Channel: room.StatusChannel{Type: room.TypeChannel, Event: room.EventStart}},
	}
	ksyunRooms := []int64{22330, 22331}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	for k, v := range ksyunRooms {
		testRoom.RoomID = v
		testRoom.CreatorID = v
		testRoom.Name = fmt.Sprintf("云拨测 %d（金山云）", k)
		testRoom.NameClean = fmt.Sprintf("云拨测 %d（金山云）", k)
		testRoom.Channel.Provider = room.ChannelProviderKsyun
		if err := room.Collection().FindOneAndUpdate(ctx, bson.M{"room_id": v},
			bson.M{"$set": testRoom}, options.FindOneAndUpdate().SetUpsert(true)).Err(); err != nil {
			return err
		}
	}
	aliyunRooms := []int64{22332, 22333}
	for k, v := range aliyunRooms {
		testRoom.RoomID = v
		testRoom.CreatorID = v
		testRoom.Name = fmt.Sprintf("云拨测 %d（阿里云）", k)
		testRoom.NameClean = fmt.Sprintf("云拨测 %d（阿里云）", k)
		testRoom.Channel.Provider = room.ChannelProviderAliyun
		if err := room.Collection().FindOneAndUpdate(ctx, bson.M{"room_id": v},
			bson.M{"$set": testRoom}, options.FindOneAndUpdate().SetUpsert(true)).Err(); err != nil {
			return err
		}
	}
	bvcRooms := []int64{22334, 22335}
	for k, v := range bvcRooms {
		testRoom.RoomID = v
		testRoom.CreatorID = v
		testRoom.Name = fmt.Sprintf("云拨测 %d（视频云）", k)
		testRoom.NameClean = fmt.Sprintf("云拨测 %d（视频云）", k)
		testRoom.Channel.Provider = room.ChannelProviderBvc
		testRoom.Channel.BVC = &bvc.Info{
			FLVPullURL:  "http://testflv/",
			HLSPullURL:  "http://testhls/",
			RTMPPullURL: "rtmp://test/",
			PushURL:     "http://testhls/",
		}
		if err := room.Collection().FindOneAndUpdate(ctx, bson.M{"room_id": v},
			bson.M{"$set": testRoom}, options.FindOneAndUpdate().SetUpsert(true)).Err(); err != nil {
			return err
		}
	}
	return nil

}

// deletePullURLsInCache 清空缓存中存储的在播流信息，用于单元测试
func deletePullURLsInCache() {
	key := keys.KeyMetaPullURLs0.Format()
	err := service.LRURedis.Del(key).Err()
	if err != nil && err != redis.Nil {
		logger.Warnf("redis del error: %v", err)
	}
}
