package apiv2

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type pullURL struct {
	Name string   `json:"name"`
	Type string   `json:"type"`
	URLs []string `json:"urls"`
}

// ActionDiagnosticURLs 拨测拉取在播流
/**
 * @api {get}  /api/v2/meta/diagnosticurls
 * @apiDescription 拨测拉取在播流接口
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/meta
 *
 * @apiSuccess (200) {number} code
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "name": "ksyun-flv",
 *           "type": "flv",
 *           "urls": [
 *             "http://123.flv?yy",
 *             "http://456.flv?yy"
 *           ]
 *         },
 *         {
 *           "name": "ksyun-hls",
 *           "type": "hls",
 *           "urls": [
 *             "http://123.m3u8",
 *             "http://456.m3u8"
 *           ]
 *         },
 *         {
 *           "name": "aliyun-flv",
 *           "type": "flv",
 *           "urls": [
 *             "http://123.flv?yy",
 *             "http://456.flv?yy"
 *           ]
 *         },
 *         {
 *           "name": "aliyun-hls",
 *           "type": "hls",
 *           "urls": [
 *             "http://123.m3u8",
 *             "http://456.m3u8"
 *           ]
 *         },
 *         {
 *           "name": "bvc-flv",
 *           "type": "flv",
 *           "urls": [
 *             "http://123.flv?yy",
 *             "http://456.flv?yy"
 *           ]
 *         },
 *         {
 *           "name": "bvc-hls",
 *           "type": "hls",
 *           "urls": [
 *             "http://123.m3u8",
 *             "http://456.m3u8"
 *           ]
 *         }
 *       ]
 *     }
 */
func ActionDiagnosticURLs(c *handler.Context) (handler.ActionResponse, error) {
	return getPullURLs(c)
}

// getPullURLs 尝试申请分布式锁后获得在播流
// 通常在从缓存中获取结果失败后调用，为防止缓存击穿，先尝试申请分布式锁，申请成功后访问数据库获得正在推流的房间
func getPullURLs(c *handler.Context) ([]*pullURL, error) {
	sleepDuration := 50 * time.Millisecond
	tryTimes := 10
	for i := 0; i < tryTimes; i++ {
		// 尝试从缓存获取
		pullURLs, ok := getPullURLsFromCache()
		if ok {
			return pullURLs, nil
		}
		// 从缓存未读取成功，尝试申请分布式锁
		ok = applyPullURLsLock()
		if ok {
			// 申请成功，访问数据库
			defer deletePullURLsLock()
			urls, err := getPullURLsFromDatabase(c.UserID(), c.ClientIP(), c.Equip())
			if err == nil {
				// 成功从数据库获取则更新缓存
				setPullURLsInCache(urls)
				return urls, nil
			}
			// 访问数据库出错，返回错误
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		// 申请失败，等一段时间继续重试
		time.Sleep(sleepDuration)
	}
	return nil, actionerrors.NewUnknownError(http.StatusInternalServerError, "服务器繁忙，请稍后再试")
}

func applyPullURLsLock() bool {
	key := keys.LockMetaPullURLs.Format()
	// 分布式锁的过期时间为 1 秒
	ok, err := service.Redis.SetNX(key, 1, time.Second).Result()
	if err != nil {
		logger.Errorf("redis lock error: %v", err)
		return false
	}
	return ok
}

func deletePullURLsLock() {
	key := keys.LockMetaPullURLs.Format()
	_, err := service.Redis.Del(key).Result()
	if err != nil {
		logger.Errorf("redis delete error: %v", err)
	}
}

func getPullURLsFromCache() ([]*pullURL, bool) {
	key := keys.KeyMetaPullURLs0.Format()
	val, err := service.LRURedis.Get(key).Result()
	if err != nil {
		return nil, false
	}
	urls := []*pullURL{}
	err = json.Unmarshal([]byte(val), &urls)
	if err != nil {
		return nil, false
	}
	return urls, true
}

func setPullURLsInCache(urls []*pullURL) {
	key := keys.KeyMetaPullURLs0.Format()
	value, err := json.Marshal(urls)
	if err != nil {
		logger.Errorf("marshal error: %v", err)
		return
	}
	if err := service.LRURedis.Set(key, string(value), time.Minute).Err(); err != nil {
		logger.Errorf("redis set error: %v", err)
	}
}

// getPullURLsFromDatabase 从数据库获取正在推流的房间，构建 pullURL
func getPullURLsFromDatabase(userID int64, ip string, equip *goutil.Equipment) ([]*pullURL, error) {
	cdnProviders := []string{room.ChannelProviderAliyun, room.ChannelProviderKsyun, room.ChannelProviderBvc}
	pullURLs := make([]*pullURL, 0, 2*len(cdnProviders))
	for _, v := range cdnProviders {
		urls, err := getPullURLByCDNProvider(v, 2, userID, ip, equip)
		if err != nil {
			return nil, err
		}
		pullURLs = append(pullURLs, urls...)
	}
	return pullURLs, nil
}

func getPullURLByCDNProvider(provider string, count int64, userID int64, ip string, equip *goutil.Equipment) ([]*pullURL, error) {
	simples, err := room.FindPushingRoom(provider, count)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var flvURLs, hlsURLs []string
	for _, v := range simples {
		// REVIEW: 开关下发的流之后对其的影响有待测试
		v.BuildAuthedChannelURL(userID, ip, equip, false, false)
		// 流会有不存在的情况，需要检查为空
		if v.Channel.FLVPullURL != "" {
			flvURLs = append(flvURLs, v.Channel.FLVPullURL)
		}
		if v.Channel.HLSPullURL != "" {
			hlsURLs = append(hlsURLs, v.Channel.HLSPullURL)
		}
	}
	pullURLs := make([]*pullURL, 0, 2)
	if len(flvURLs) != 0 {
		pullURLs = append(pullURLs, &pullURL{
			Name: provider + "-flv",
			Type: "flv",
			URLs: flvURLs,
		})
	}
	if len(hlsURLs) != 0 {
		pullURLs = append(pullURLs, &pullURL{
			Name: provider + "-hls",
			Type: "hls",
			URLs: hlsURLs,
		})
	}
	return pullURLs, nil
}
