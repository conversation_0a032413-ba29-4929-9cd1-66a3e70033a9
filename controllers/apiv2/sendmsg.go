package apiv2

import (
	"strings"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type msgSendReqBody struct {
	RoomID  int64  `json:"room_id" form:"room_id"`
	Message string `json:"message" form:"message"`
	MsgID   string `json:"msg_id" form:"msg_id"`
}

// ActionMessageSend 发送消息
/*
 * @api {post} /api/v2/chatroom/message/send 发送消息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} message 消息
 * @apiParam {String} msg_id 消息 ID（UUIDv4）：Android 设备应以 "1" 开头，iOS 设备应以 "2" 开头，Web 以 "3" 开头，例：3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
 *         "user": {
 *           "user_id": 123,
 *           "username": "name",
 *           "iconurl": "",
 *           "titles": [
 *             {
 *               "type": "staff",
 *               "name": "超管",
 *               "color": "#F45B41"
 *             },
 *             {
 *               "type": "medal",
 *               "name": "1",
 *               "level": 1
 *             }
 *           ]
 *         },
 *         "ok": 1, // 1 成功，0 失败
 *       }
 *     }
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 *
 * @apiError (403) {Number} code 500020023
 * @apiError (403) {String} info 当前用户被封禁
 */
func ActionMessageSend(c *handler.Context) (handler.ActionResponse, error) {
	user := c.User()
	// 判断用户是否绑定手机
	if user.Mobile == "" {
		return nil, actionerrors.ErrUnbindMobileUser
	}

	var body msgSendReqBody
	err := c.Bind(&body)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	body.Message = strings.TrimSpace(body.Message)
	if body.RoomID <= 0 || body.Message == "" {
		return nil, actionerrors.ErrParams
	}
	// 是否在黑名单
	exists, err := blocklist.Exists(user.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrBannedUser
	}
	// 是否被封禁
	if banned, err := userstatus.IsBanned(user.ID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	} else if banned {
		return nil, actionerrors.ErrBannedUser
	}
	// 是否被禁言
	if mute, err := livemembers.IsMute(user.ID, body.RoomID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	} else if mute.GlobalMute {
		return nil, actionerrors.ErrGlobalMuteUser
	} else if mute.RoomMute {
		return nil, actionerrors.NewErrForbidden("当前用户被禁言")
	}

	prefix := "3" // Web
	equip := c.Equip()
	if equip != nil && equip.OS == goutil.Android {
		prefix = "1" // Android
	} else if equip != nil && equip.OS == goutil.IOS {
		prefix = "2" // iOS
	}

	if body.MsgID == "" {
		uuidv4, err := uuid.NewRandom()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		body.MsgID = prefix + uuidv4.String()[1:]
	} else {
		_, err := uuid.Parse(body.MsgID)
		if err != nil {
			return nil, actionerrors.ErrParamsMsg("unknown uuid")
		}
		if body.MsgID[0] != prefix[0] {
			return nil, actionerrors.ErrParamsMsg("prefix not match")
		}
	}

	r, err := room.Find(body.RoomID,
		&room.FindOptions{
			Projection: bson.M{"creator_id": 1},
			DisableAll: true,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	u, err := liveuser.FindOneSimple(bson.M{"user_id": c.UserID()},
		&liveuser.FindOptions{FindTitles: true, RoomID: body.RoomID},
		options.FindOne().SetProjection(liveuser.ProjectionTitles))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, actionerrors.ErrUnloggedUser
	}
	u.UID = user.ID
	u.Username = user.Username
	u.IconURL = user.IconURL

	if !u.IsRole(liveuser.RoleStaff) {
		// 不是超管需要检查消息是否有违禁词
		adFree := u.UserID() == r.CreatorID
		if !adFree {
			// 房主和房管都不检查广告违禁词
			adFree, err = livemembers.IsRoomAdmin(r.OID, user.ID)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		opts := make([]scan.CheckTextOption, 0, 1)
		if adFree {
			opts = append(opts, scan.AdFreeOption)
		}
		r, err := goclient.CheckText(c, body.Message, scan.SceneIM, opts...)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !r.Pass {
			return nil, actionerrors.ErrIllegalMessage
		}
	}

	// TODO: 和 models.Message 迁移到单独的包
	attach := map[string]interface{}{
		"type":    liveim.TypeMessage,
		"event":   liveim.EventNew,
		"room_id": body.RoomID,
		"user":    u,
		"msg_id":  body.MsgID,
		"message": body.Message,
	}
	err = userapi.Broadcast(body.RoomID, attach)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	room.IncrMessageCount(body.RoomID)
	m := models.Message{
		MsgID:   body.MsgID,
		RoomOID: r.OID,
		RoomID:  body.RoomID,
		UserID:  user.ID,
		Message: body.Message,
	}
	ok := 1
	_, err = m.Insert()
	if err != nil {
		ok = 0
		logger.Error(err)
		// PASS
	}
	return handler.M{
		"msg_id": body.MsgID,
		"user":   u,
		"ok":     ok,
	}, nil
}
