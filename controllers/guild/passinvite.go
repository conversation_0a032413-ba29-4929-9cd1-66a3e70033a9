package guild

// ActionPassInvite 主播同意公会入会邀请
/*
 * @api {post} /api/v2/guild/passinvite 主播同意公会入会邀请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} contract_id 合同 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 500050002
 * @apiError (403) {String} info 已加入另一公会，不能加入多个公会
 *
 * @apiError (400) {Number} code 500050004
 * @apiError (400) {String} info 该公会未向您发起邀请
 */
/*
func ActionPassInvite(c *handler.Context) (handler.ActionResponse, error) {
	// 获取合约 ID
	contractID, err := getContractID(c)
	if err != nil {
		return false, err
	}

	// 检查该合约是否存在
	contract, err := lct.GetByPK(contractID)
	if err != nil {
		return false, actionerrors.ErrServerInternal.New(err, nil)
	}
	if contract == nil {
		return false, actionerrors.ErrContractNotExist
	}

	// 查看该用户是否有统一签约权限
	if err := contract.HaveRight(lct.StatusUntreated, lct.FromGuild, c.User().ID); err != nil {
		return false, err
	}

	// 该主播是否已经和其他公会签约
	count, err := lct.LiveCount(contract.LiveID, lct.StatusContracting)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if count >= 1 {
		lct.UpdateOtherContractsFinished(contract.LiveID, 0)
		return false, actionerrors.ErrCannotJoinTwice
	}
	ok := contract.UpdateContract(lct.StatusContracting)

	var guildOwnerID int64
	err = service.DB.Table(guild.TableName()).Select("user_id").
		Where("id = ?", contract.GuildID).Row().Scan(&guildOwnerID)
	if err == nil {
		err = messageassign.SystemMessageAssign(guildOwnerID,
			"您的入会邀请已被通过",
			fmt.Sprintf("主播 %s（MID：%d）已通过您的直播公会入会邀请。您于%s与该主播成功签约。", c.User().Username, c.User().ID, time.Now().Format(" 2006 年 01 月 02 日")))
		if err != nil {
			logger.Error(err)
			// PASS
		}
	} else {
		logger.Error(err)
		// PASS
	}

	return ok, nil
}
*/
