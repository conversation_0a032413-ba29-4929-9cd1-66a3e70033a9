package guild

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGetGuildInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	data, err := ActionGetGuildInfo(c)
	require.NoError(err)
	assert.NotEmpty(data)

	c = handler.CreateTestContext(true)
	c.User().ID = -9999
	c.C.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	data, err = ActionGetGuildInfo(c)
	require.NoError(err)
	assert.Empty(data)
}
