package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionSearchLive(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/searchlive?user_id=12&real_name=minikube", true, nil)
	r, err := ActionSearchLive(ctx)
	require.NoError(err)
	assert.IsType(r, &mowangskuser.Simple{})

	ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/searchlive?user_id=12&real_name=kube", true, nil)
	_, err = ActionSearchLive(ctx)
	assert.EqualError(err, "真实姓名错误，请检查后再试")

	ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/searchlive?user_id=13&real_name=minikube", true, nil)
	_, err = ActionSearchLive(ctx)
	assert.EqualError(err, "该用户尚未完成实名认证，暂时无法邀请")
}

func TestValidateRealNameAuthentication(t *testing.T) {
	assert := assert.New(t)

	err := validateRealNameAuthentication(12, "minikube")
	assert.NoError(err)

	err = validateRealNameAuthentication(12, "kube")
	assert.EqualError(err, "真实姓名错误，请检查后再试")

	err = validateRealNameAuthentication(13, "minikube")
	assert.EqualError(err, "该用户尚未完成实名认证，暂时无法邀请")
}
