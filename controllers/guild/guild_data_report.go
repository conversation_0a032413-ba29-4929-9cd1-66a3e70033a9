package guild

import (
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/report"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/reportlivedailyreport"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// dataReportParam 报表请求参数
type dataReportParam struct {
	page     int64
	pageSize int64
	sort     string

	startDate time.Time
	endDate   time.Time

	guildID int64

	searchAgent   handler.SearchWord
	searchCreator handler.SearchWord

	role guildrole.GuildRole
	user *user.User

	agentCreators agentCreators

	userIDs []int64
}

// dataReportResp 响应
type dataReportResp struct {
	Menu       *DataReportMenu   `json:"menu,omitempty"`
	Data       []DataReportItem  `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

// DataReportMenu 收益菜单栏
type DataReportMenu struct {
	OpenLiveNum         int64         `gorm:"-" json:"open_live_num"`                                    // 开播主播数
	NewLiveNum          int64         `gorm:"column:new_live_num" json:"new_live_num"`                   // 新主播数
	ListenerDurationAvg util.Float2DP `gorm:"column:listener_duration_avg" json:"listener_duration_avg"` // 人均收听时长（分钟）

	PureNewLiveNum   int64         `gorm:"column:pure_new_live_num" json:"pure_new_live_num"`     // 纯新主播数（首次开播在所选时段内的主播）
	TotalIncome      util.Float2DP `gorm:"-" json:"total_income"`                                 // 总收益，元（总收益包含贵族开通、礼物打赏、玩法、超粉）
	PureNewIncome    util.Float2DP `gorm:"-" json:"pure_new_income"`                              // 纯新主播收益，元（首次开播在所选时段内的主播在所选时段内在该公会的总收益）
	ValidOpenLiveNum int64         `gorm:"column:valid_open_live_num" json:"valid_open_live_num"` // 所选时段内直播达 15 个有效天、总时长 30 小时的主播数（当天直播 2 小时以上计一个有效天）
}

// DataReportItem for response data list
type DataReportItem struct {
	LiveID   int64  `gorm:"column:live_id" json:"live_id"`
	RoomID   int64  `gorm:"column:room_id" json:"room_id"`
	IconURL  string `gorm:"-" json:"iconurl"`
	Username string `gorm:"-" json:"username"`

	AgentID       int64  `gorm:"-" json:"agent_id"`
	AgentUsername string `gorm:"-" json:"agent_username"`

	// 收听时长（单位：分钟）
	LiveDuration  util.Float2DP `gorm:"column:live_duration" json:"live_duration"`
	EffectiveDays int           `gorm:"-" json:"effective_days"`
	NewFansNum    int64         `gorm:"column:new_fans_num" json:"new_fans_num"`
	// 人均收听时长（单位：分钟）
	ListenerDurationAvg util.Float2DP `gorm:"column:listener_duration_avg" json:"listener_duration_avg"`
	// 超 5 分钟收听人数占比（单位：百分比）
	ListenerDurationMoreThanFiveMinutesRatio util.Float2DP `gorm:"column:listener_duration_more_than_five_minutes_ratio" json:"listener_duration_more_than_five_minutes_ratio"`
}

// DataReportSortMapSupported 排序数组
var DataReportSortMapSupported = map[string]bool{
	"new_fans_num":                                        true,
	"new_fans_num.asc":                                    true,
	"new_fans_num.desc":                                   true,
	"listener_duration_avg":                               true,
	"listener_duration_avg.asc":                           true,
	"listener_duration_avg.desc":                          true,
	"listener_duration_more_than_five_minutes_ratio":      true,
	"listener_duration_more_than_five_minutes_ratio.asc":  true,
	"listener_duration_more_than_five_minutes_ratio.desc": true,
}

// ActionGuildDataReport 公会获取主播报表
/**
 * @api {get} /api/v2/guild/data-report 公会获取主播报表
 * @apiDescription 当公会会长和主播的经纪人访问时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date] 筛选的起始时间，默认为 30 天前，格式 "2006-01-02"
 * @apiParam {String} [end_date] 筛选的结束时间，默认为今天，格式 "2006-01-31"
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {String} [sort=new_fans_num.desc] 排序方式：new_fans_num.asc, listener_duration_avg.asc,
 * listener_duration_more_than_five_minutes_ratio.asc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "menu": { // p = 1 返回
 *           "total_income": 123.01, // 所选时段内公会主播的总收益，元（总收益包含贵族开通、礼物打赏、玩法、超粉）
 *           "pure_new_income": 123.02, // 首次开播在所选时段内的主播在所选时段内在该公会的总收益，元（总收益包含贵族开通、礼物打赏、玩法、超粉）
 *           "open_live_num": 123, // 开播主播数
 *           "valid_open_live_num": 123, // 有效开播主播数
 *           "new_live_num": 233, // 新主播数
 *           "pure_new_live_num": 233, // 纯新主播数（首次开播在所选时段内的主播）
 *           "listener_duration_avg": 10.01 // 人均收听时长（分钟）
 *         },
 *         "data": [{
 *           "live_id": 123,
 *           "room_id": 123,
 *           "username": "用户名",
 *           "iconurl": "用户头像",
 *           "agent_username": "经纪人昵称",
 *           "live_duration": 12.01, // 开播时长（分钟）
 *           "effective_days": 33, // 有效开播天数
 *           "new_fans_num": 200, // 粉丝数
 *           "listener_duration_avg": 200.01, // 人均收听时长（分钟）
 *           "listener_duration_more_than_five_minutes_ratio": 12.22 // 超 5 分钟收听占比（单位：百分比）
 *         }],
 *         "pagination": {
 *           "count": 10,
 *           "p": 10,
 *           "pagesize": 1,
 *           "maxpage": 10
 *         }
 *       }
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionGuildDataReport(ctx *handler.Context) (handler.ActionResponse, error) {
	param, err := newDataReportParam(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := newDataReportResp(param, true)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newDataReportParam(ctx *handler.Context) (*dataReportParam, error) {
	param := new(dataReportParam)

	var err error
	param.page, param.pageSize, err = ctx.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	param.startDate, param.endDate, err = ctx.GetParamDateRange(
		now.AddDate(0, 0, -30).Format(goutil.TimeFormatYMD),
		now.Format(goutil.TimeFormatYMD),
	)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 不获取当天数据
	beginningOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	if goutil.TimeGte(param.endDate, beginningOfToday) {
		param.endDate = beginningOfToday.AddDate(0, 0, -1)
	}

	orderByField, order, ok := utils.ParseSortStr(
		ctx.GetDefaultParamString("sort", "new_fans_num.desc"),
		DataReportSortMapSupported,
	)
	if !ok {
		return nil, actionerrors.ErrParams
	}
	param.sort = orderByField + " " + order

	param.searchCreator, err = ctx.GetParamSearchWord("s")
	if err != nil && err != handler.ErrEmptyValue {
		return nil, actionerrors.ErrParams
	}
	param.searchAgent, err = ctx.GetParamSearchWord("search_agent")
	if err != nil && err != handler.ErrEmptyValue {
		return nil, actionerrors.ErrParams
	}

	role, guildInfo, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}
	param.user = ctx.User()
	param.role = role
	param.guildID = guildInfo.ID

	param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return param, nil
}

func newDataReportResp(param *dataReportParam, enableMenu bool) (*dataReportResp, error) {
	resp := new(dataReportResp)

	err := resp.buildList(param)
	if err != nil {
		return nil, err
	}

	if enableMenu {
		err = resp.buildMenu(param)
		if err != nil {
			return nil, err
		}
	}
	return resp, nil
}

func (resp *dataReportResp) setEmptyList(param *dataReportParam) {
	resp.Data = make([]DataReportItem, 0)
	resp.Pagination = goutil.MakePagination(0, param.page, param.pageSize)
}

func (resp *dataReportResp) buildList(param *dataReportParam) error {
	db := service.DB.Table(livecontract.TableName()+" AS lc").
		Where("lc.guild_id = ?", param.guildID).
		Where("lc.status = ?", livecontract.StatusContracting)
	db, err := param.applySearch(db)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if db == nil {
		resp.setEmptyList(param)
		return nil
	}

	// 访问用户不是公会会长则是经纪人，从公会主播列表中取出该经纪人的主播
	if !param.role.IsGuildOwner() {
		creatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(creatorIDs) == 0 {
			resp.setEmptyList(param)
			return nil
		}
		db = db.Where("lc.live_id IN (?)", creatorIDs)
	}

	fields := []string{
		"lc.live_id",
		"COALESCE(t2.room_id, 0) AS room_id",
		"COALESCE(SUM(t1.follow), 0) AS new_fans_num",
		"COALESCE(ROUND(SUM(t1.listener_duration_total) / SUM(t1.listener_user_num) * 100, 2) / 100, 0) AS listener_duration_avg",
		"COALESCE(ROUND(SUM(t1.listener_duration_more_than_five_minutes_num) / SUM(t1.listener_user_num) * 100, 2), 0) AS listener_duration_more_than_five_minutes_ratio",
	}

	// 获取公会主播在该时间段内的历史数据子查询
	reportSubQuery, err := reportlivedailyreport.GuildDataReportSubQuery(param.guildID, 0, param.startDate, param.endDate, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	db = db.Select(fields).
		Joins("LEFT JOIN ? AS t1 ON lc.live_id = t1.creator_id",
			reportSubQuery.SubQuery()).
		Joins(fmt.Sprintf("LEFT JOIN %s AS t2 ON lc.live_id = t2.user_id", live.TableName()))

	var totalCount int64
	err = db.Select("COUNT(DISTINCT lc.live_id) AS live_count").Row().Scan(&totalCount)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if totalCount == 0 {
		resp.setEmptyList(param)
		return nil
	}

	resp.Pagination = goutil.MakePagination(totalCount, param.page, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]DataReportItem, 0)
		return nil
	}
	if err = resp.Pagination.ApplyTo(db.Group("lc.live_id, room_id").
		Order(param.sort)).Scan(&resp.Data).Error; err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(resp.Data) == 0 {
		return nil
	}

	userIDs := make([]int64, len(resp.Data))
	for i, v := range resp.Data {
		userIDs[i] = v.LiveID
	}
	if err = resp.fillUserInfo(param, userIDs); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if err = resp.buildNewFans(param); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 开播时长（包含截止日期当天的数据）
	endTime := goutil.BeginningOfDay(param.endDate).AddDate(0, 0, 1)
	durationMap, err := livelog.GetUserLiveTotalDuration(param.guildID, userIDs, param.startDate, endTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(durationMap) == 0 {
		return nil
	}
	for i, v := range resp.Data {
		info, ok := durationMap[v.LiveID]
		if ok {
			// 毫秒转分钟
			resp.Data[i].LiveDuration = util.Float2DP(info.TotalDuration) / 1000 / 60
			resp.Data[i].EffectiveDays = info.Days
		}
	}
	return nil
}

func (resp *dataReportResp) buildNewFans(param *dataReportParam) error {
	fields := "follower_count"
	prevDate := param.startDate.AddDate(0, 0, -1)
	reportsMap, err := reportlivedailyreport.ListLiveDailyReportMap(fields, param.userIDs, prevDate, param.endDate)
	if err != nil {
		return err
	}
	for i, data := range resp.Data {
		if reportsMap[data.LiveID] == nil {
			continue
		}
		resp.Data[i].NewFansNum = report.CountNewFans(reportsMap[data.LiveID], prevDate)
	}
	return nil
}

func (param *dataReportParam) applySearch(db *gorm.DB) (*gorm.DB, error) {
	if param.searchCreator.Word != "" {
		if param.searchCreator.IsInteger {
			db = db.Where("lc.live_id = ?", param.searchCreator.WordInteger)
		} else {
			var allCreatorIDs []int64
			// 该公会下所有的主播 ID
			err := db.Pluck("live_id", &allCreatorIDs).Error
			if err != nil {
				return nil, err
			}
			if len(allCreatorIDs) == 0 {
				return nil, nil
			}
			// 从该公会下所有的主播 ID 中匹配用户
			liveIDs, err := mowangskuser.SearchUserByUsername(param.searchCreator.Word, allCreatorIDs)
			if err != nil {
				return nil, err
			}
			if len(liveIDs) == 0 {
				return nil, nil
			}
			db = db.Where("lc.live_id IN (?)", liveIDs)
		}
	}

	if param.searchAgent.Word != "" {
		var liveIDs []int64
		if param.searchAgent.IsInteger {
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, param.searchAgent.WordInteger)
		} else {
			allAgentIDs := param.agentCreators.allAgentIDs()
			if len(allAgentIDs) == 0 {
				return nil, nil
			}
			agentIDs, err := mowangskuser.SearchUserByUsername(param.searchAgent.Word, allAgentIDs)
			if err != nil {
				return nil, err
			}
			if len(agentIDs) == 0 {
				return nil, nil
			}
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDs...)
		}
		if len(liveIDs) == 0 {
			return nil, nil
		}
		db = db.Where("lc.live_id IN (?)", liveIDs)
	}
	return db, nil
}

func (resp *dataReportResp) fillUserInfo(param *dataReportParam, creatorIDs []int64) error {
	creatorAgentMap := make(map[int64]int64, len(param.agentCreators))
	param.userIDs = make([]int64, 0, len(creatorIDs)+len(param.agentCreators))
	param.userIDs = append(param.userIDs, creatorIDs...)
	if param.role.IsGuildOwner() {
		agentIDs := make([]int64, len(param.agentCreators))
		for i, item := range param.agentCreators {
			agentIDs[i] = item.AgentID
			creatorAgentMap[item.CreatorID] = item.AgentID
		}
		param.userIDs = append(param.userIDs, agentIDs...)
	}
	param.userIDs = util.Uniq(param.userIDs)

	userInfoMap, err := mowangskuser.FindSimpleMap(param.userIDs)
	if err != nil {
		return err
	}
	for i, item := range resp.Data {
		if creatorInfo, ok := userInfoMap[item.LiveID]; ok {
			resp.Data[i].IconURL = creatorInfo.IconURL
			resp.Data[i].Username = creatorInfo.Username
		}

		// 如果访问用户不是会长，则当前访问用户为经纪人
		if !param.role.IsGuildOwner() {
			resp.Data[i].AgentID = param.user.ID
			resp.Data[i].AgentUsername = param.user.Username
			continue
		}

		if agentID, ok := creatorAgentMap[item.LiveID]; ok {
			agentInfo, ok := userInfoMap[agentID]
			if ok {
				resp.Data[i].AgentID = agentInfo.ID
				resp.Data[i].AgentUsername = agentInfo.Username
			}
		}
	}
	return nil
}

func (resp *dataReportResp) buildMenu(param *dataReportParam) error {
	if param.page != 1 {
		return nil
	}

	// 截止时间（包含截止日期当天的数据）
	endTime := goutil.BeginningOfDay(param.endDate).AddDate(0, 0, 1)
	// 如果用户入会时间在开始时间前 30 天至结束时间内加入公会，认为是新主播
	lcDB := service.DB.Table(livecontract.TableName()).
		Where("guild_id = ?", param.guildID).
		Where("status = ?", livecontract.StatusContracting).
		Where("contract_start >= ? AND contract_start < ?", param.startDate.Unix()-30*util.SecondOneDay, endTime.Unix())

	reportSubQuery, err := reportlivedailyreport.GuildDataReportSubQuery(param.guildID, 0, param.startDate, param.endDate, true)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 访问用户不是公会会长则是经纪人，从公会主播列表中取出该经纪人的主播
	var agentCreatorIDs []int64
	if !param.role.IsGuildOwner() {
		agentCreatorIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(agentCreatorIDs) == 0 {
			return nil
		}
		reportSubQuery = reportSubQuery.Where("t.live_id IN (?)", agentCreatorIDs)
		lcDB = lcDB.Where("live_id IN (?)", agentCreatorIDs)
	}

	resp.Menu = &DataReportMenu{}
	// 获取总收益
	resp.Menu.TotalIncome, err = transactionlog.GetGuildTotalIncome(param.guildID, agentCreatorIDs, param.startDate, param.endDate.AddDate(0, 0, 1))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 获取在指定日期首播的主播总收益
	resp.Menu.PureNewIncome, err = getPureNewIncome(param, agentCreatorIDs, reportSubQuery)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取平均观看时长、在指定日期首播的主播数
	err = service.DB.
		Raw("SELECT"+
			" COALESCE(ROUND(SUM(rli.listener_duration_total) / SUM(rli.listener_user_num) * 100, 2) / 100, 0) AS listener_duration_avg"+
			fmt.Sprintf(" , COALESCE(COUNT(DISTINCT IF(first_live_time >= %d AND first_live_time < %d, creator_id, NULL)), 0) AS pure_new_live_num", param.startDate.Unix(), endTime.Unix())+
			" FROM ? AS rli",
			reportSubQuery.SubQuery()).
		Scan(resp.Menu).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 所选日期范围内，至少有 15 天每天开播 2 小时及以上，并且总开播时长 30 小时及以上的主播数量
	err = service.DB.
		Raw("SELECT COUNT(DISTINCT creator_id) valid_open_live_num FROM "+
			"("+
			"  SELECT creator_id, COUNT(bizdate) days, SUM(live_duration) total_duration "+
			"  FROM ("+
			"      SELECT DISTINCT rli.creator_id, rli.bizdate, rli.live_duration"+
			"      FROM (?) AS rli"+
			"      WHERE live_duration >= 7200 * 1000"+
			"    ) AS cbl"+
			"  GROUP BY creator_id"+
			"  HAVING days >= 15"+
			"  AND total_duration >= 30 * 3600 * 1000"+
			") AS voln",
			reportSubQuery.SubQuery()).
		Scan(&resp.Menu).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	err = lcDB.Count(&resp.Menu.NewLiveNum).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取开播主播数（包含截止日期当天的数据）
	resp.Menu.OpenLiveNum, err = livelog.GetOpenLiveNum(param.guildID, param.startDate, endTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// 获取在指定日期首播的主播总收益
func getPureNewIncome(param *dataReportParam, agentCreatorIDs []int64, reportSubQuery *gorm.DB) (util.Float2DP, error) {
	pureNewLiveDB := reportSubQuery.Select("creator_id").
		Where("first_live_time >= ? AND first_live_time < ?", param.startDate.Unix(), param.endDate.AddDate(0, 0, 1).Unix())
	if len(agentCreatorIDs) > 0 {
		pureNewLiveDB = pureNewLiveDB.Where("creator_id IN (?)", agentCreatorIDs)
	}

	var pureNewLiveCreatorIDs []int64
	err := pureNewLiveDB.
		Group("creator_id").
		Pluck("creator_id", &pureNewLiveCreatorIDs).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	if len(pureNewLiveCreatorIDs) == 0 {
		return 0, nil
	}
	return transactionlog.GetGuildTotalIncome(param.guildID, pureNewLiveCreatorIDs, param.startDate, param.endDate.AddDate(0, 0, 1))
}

// ActionGuildDataReportExport 公会创建者导出公会报表 csv
/**
 * @api {get} /api/v2/guild/data-report-export 公会创建者导出公会报表 csv
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date] 筛选的起始时间，默认为 30 天前，格式 "2006-01-02"
 * @apiParam {String} [end_date] 筛选的结束时间，默认为今天，格式 "2006-01-31"
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildDataReportExport(ctx *handler.Context) (handler.ActionResponse, error) {
	param, err := newDataReportParam(ctx)
	if err != nil {
		return nil, err
	}
	param.page = 1
	param.pageSize = 10000 // 设置一个大值，避免遗漏数据

	resp, err := newDataReportResp(param, false)
	if err != nil {
		return nil, err
	}

	var records [][]string
	titleLine := []string{"主播 ID", "主播昵称", "经纪人 ID", "经纪人",
		"开播时长（分钟）", "有效开播天数", "新增粉丝数", "人均收听时长（分钟）", "超 5 分钟收听占比"}
	records = append(records, titleLine)
	for _, v := range resp.Data {
		var agentIDStr string
		if v.AgentID != 0 {
			agentIDStr = strconv.FormatInt(v.AgentID, 10)
		}
		records = append(records, []string{
			strconv.FormatInt(v.LiveID, 10),
			v.Username,
			agentIDStr,
			v.AgentUsername,
			v.LiveDuration.String(),
			strconv.Itoa(v.EffectiveDays),
			strconv.FormatInt(v.NewFansNum, 10),
			v.ListenerDurationAvg.String(),
			v.ListenerDurationMoreThanFiveMinutesRatio.String() + "%",
		})
	}

	filename := fmt.Sprintf("公会数据报表_%s_%s.csv", param.startDate.Format(goutil.TimeFormatYMD), param.endDate.Format(goutil.TimeFormatYMD))
	return writeDataToCsv(ctx, records, filename)
}
