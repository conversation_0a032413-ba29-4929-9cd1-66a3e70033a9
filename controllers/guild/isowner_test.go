package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestIsOwner(t *testing.T) {
	assert := assert.New(t)
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/guild/isowner", nil)
	r, err := ActionIsowner(c)
	assert.NoError(err)
	assert.True(r.(bool))
	c = handler.CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "/guild/isowner", nil)
	r, err = ActionIsowner(c)
	assert.NoError(err)
	assert.False(r.(bool))
}

func TestGuildRoleTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(guildRole{}, "guild_id", "role")
}

func TestActionRole(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "/guild/role", true, nil)
	c.User().ID = 302010

	g := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    c.User().ID,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	err := service.DB.Create(g).Error
	require.NoError(err)
	defer func() {
		require.NoError(service.DB.Table(g.TableName()).Delete("", "id = ?", g.ID).Error)
	}()

	lc := &livecontract.LiveContract{
		LiveID:      c.User().ID,
		GuildID:     g.ID,
		Status:      livecontract.StatusContracting,
		ContractEnd: util.TimeNow().Add(5 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(lc).Error)
	defer func() {
		require.NoError(service.DB.Table(lc.TableName()).Delete("", "id = ?", lc.ID).Error)
	}()

	ag := &guildagent.GuildAgent{
		GuildID: g.ID,
		AgentID: c.User().ID,
	}
	err = ag.DB().Create(ag).Error
	require.NoError(err)
	defer func() {
		require.NoError(ag.DB().Delete("", "id = ?", ag.ID).Error)
	}()

	r, err := ActionRole(c)
	require.NoError(err)
	result, ok := r.(guildRole)
	require.True(ok)
	assert.Equal(g.ID, result.GuildID)
	expectedRole := int(1<<(uint(guildrole.RoleGuildLiveCreator)-1) | 1<<(uint(guildrole.RoleGuildAgent)-1) | 1<<(uint(guildrole.RoleGuildOwner)-1))
	assert.Equal(expectedRole, int(result.Role))
}
