package guild

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
)

// ActionGetUserMobile 获取用户脱敏手机号
/**
 * @api {get} api/v2/guild/getusermobile 获取用户脱敏手机号
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "132****1127"
 *   }
 *
 * @apiError (403) {Number} code 500020013
 * @apiError (403) {String} info 用户未绑定手机
 *
 */
func ActionGetUserMobile(c *handler.Context) (handler.ActionResponse, error) {
	// 检查是否绑定手机号
	bind, err := c.User().IsBindMobile()
	if err != nil {
		return "", actionerrors.ErrServerInternal.New(err, nil)
	}
	if !bind {
		return "", actionerrors.ErrUnbindMobileUser
	}
	return c.User().Mobile, nil
}

type checkCodeParam struct {
	IdentifyCode  string `form:"identify_code" json:"identify_code"`
	ObjectiveType int    `form:"objective_type" json:"objective_type"`
}

// ActionSetUserPermission 验证并设置用户操作（签约、续约、解约）临时权限
/**
 * @api {post} api/v2/guild/setuserpermission 验证并设置用户操作（签约、续约、解约）临时权限
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} identify_code 验证码
 * @apiParam {Number} objective_type 验证类型，公会关键操作权限：18
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "验证成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500, 100010002
 * @apiError (500) {String} info 服务器内部错误, 数据库错误
 *
 * @apiError (403) {Number} code 501010006
 * @apiError (403) {String} info 验证码错误
 *
 */
func ActionSetUserPermission(c *handler.Context) (handler.ActionResponse, error) {
	param := checkCodeParam{}
	if err := c.Bind(&param); err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(param.IdentifyCode) <= 0 || param.ObjectiveType != vcode.ObjectiveTypeOperateGuild {
		return nil, actionerrors.ErrParams
	}

	// 校验验证码
	acc, err := service.SSO.UserInfo(c.UserID())
	if err != nil {
		if v, ok := err.(*sso.ClientError); ok && v.Code == actionerrors.CodeSSOGetUserNotFound {
			return nil, actionerrors.ErrUserNotFound
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	regionMobile := fmt.Sprintf("%d%s", acc.Region, acc.Mobile)
	ok, err := vcode.CheckIdentifyCode(regionMobile, param.IdentifyCode, param.ObjectiveType)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrVCode
	}

	// 添加临时操作权限
	err = guild.SetOperatePermission(c.UserID(), c.Token())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 操作成功删除验证码
	vcode.DelIdentifyCode(regionMobile)
	return "验证成功", nil
}

// 发起申请前用户是否通过了身份认证
func checkPermission(userID int64, token string) error {
	if config.Conf.Params.GuildOperate.GuildPermissionSwitch {
		pass, err := guild.CheckOperatePermission(userID, token)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !pass {
			return actionerrors.ErrLoginIdentityVerificationRequired
		}
	}
	return nil
}
