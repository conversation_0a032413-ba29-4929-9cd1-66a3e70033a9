package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 测试数据
var (
	userID    = int64(6666)
	GuildID   = int64(3)
	userName  = "三体星人"
	guildName = "测试 liveContract 服务协议"
	endTime   = goutil.TimeNow().Add(time.Hour).Unix()
)

func insertTestLiveContract() error {
	lc := livecontract.LiveContract{
		GuildID:     GuildID,
		LiveID:      userID,
		GuildName:   guildName,
		Status:      livecontract.StatusContracting,
		ContractEnd: endTime,
	}
	return service.DB.Assign(lc).FirstOrCreate(&lc).Error
}

func TestAgreement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/guild/agreement", true, nil)
	c.User().ID = userID
	c.User().Username = userName
	// 测试正常获取服务协议
	require.NoError(service.DB.Where("user_id = ?", userID).
		Delete(&liveaddendum.LiveAddendum{}).Error)
	r, err := ActionAgreement(c)
	require.NoError(err)
	resp := r.(agreementResp)
	assert.Equal(userName, resp.UserName)
	assert.Equal(guildName, resp.GuildName)
	assert.Equal(false, resp.IsAgreed)

	// 测试已经通过服务协议
	var la liveaddendum.LiveAddendum
	la.SetUserID(userID)
	la.IsAgreed.Set(liveaddendum.AgreeGuildAgreement)
	require.NoError(service.DB.Save(&la).Error)
	r, err = ActionAgreement(c)
	require.NoError(err)
	assert.Equal(true, r.(agreementResp).IsAgreed)
	assert.Equal("", r.(agreementResp).Content)

	// 测试没有公会的主播获取协议
	c = handler.NewTestContext(http.MethodGet, "/guild/agreement", true, nil)
	c.User().ID = int64(99999)
	_, err = ActionAgreement(c)
	assert.Equal(actionerrors.ErrGuildNotExist, err)
}

func TestConfirmAgreement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试正常同意协议
	c := handler.NewTestContext(http.MethodPost, "/guild/confirmagreement", true,
		map[string]interface{}{"agree": 1})
	c.User().ID = userID
	_, err := ActionConfirmAgreement(c)
	require.NoError(err)
	// 验证修改结果
	var la liveaddendum.LiveAddendum
	require.NoError(service.DB.Where("user_id = ?", userID).First(&la).Error)
	assert.True(la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement))

	// 测试 postForm格式
	require.NoError(service.DB.Where("user_id = ?", userID).
		Delete(&liveaddendum.LiveAddendum{}).Error)
	c = handler.NewTestContext(http.MethodPost, "/guild/confirmagreement", true,
		"agree=1")
	c.User().ID = userID
	_, err = ActionConfirmAgreement(c)
	require.NoError(err)
	// 验证修改结果
	require.NoError(service.DB.Where("user_id = ?", userID).First(&la).Error)
	assert.True(la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement))

	// 测试错误参数
	c = handler.NewTestContext(http.MethodPost, "/guild/confirmagreement", true,
		"agree=0")
	c.User().ID = userID
	_, err = ActionConfirmAgreement(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试数据库新增记录然后更新
	require.NoError(service.DB.Where("user_id = ?", userID).
		Delete(&liveaddendum.LiveAddendum{}).Error)
	c.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/confirmagreement", tutil.ToRequestBody(map[string]interface{}{"agree": 1}))
	c.C.Request.Header.Set("Content-Type", "application/json")
	_, err = ActionConfirmAgreement(c)
	require.NoError(err)
	// 验证修改结果
	require.NoError(service.DB.Where("user_id = ?", userID).First(&la).Error)
	assert.True(la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement))

	// 测试无公会主播同意协议
	c = handler.NewTestContext(http.MethodPost, "/guild/confirmagreement", true,
		"agree=1")
	c.User().ID = int64(99999)
	_, err = ActionConfirmAgreement(c)
	assert.Equal(actionerrors.ErrGuildNotExist, err)
}

func TestGuildNameByLiveID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lc, err := guildNameByLiveID(userID)
	require.NoError(err)
	assert.NotNil(lc)
	assert.Equal(guildName, lc.GuildName)

	lc, err = guildNameByLiveID(int64(99999))
	assert.Equal(actionerrors.ErrGuildNotExist, err)
	assert.Nil(lc)
}
