package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestGuildrate(t *testing.T) {
	assert := assert.New(t)
	m := map[string]interface{}{
		"guild_id": 3,
		"rate":     0.5,
	}
	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "", paramToRequestBody(m))
	_, err := ActionGuildrate(ctx)
	// require.NoError(t, err)
	// resp := r.(handler.M)
	// assert.True(t, resp["rate"].(bool))
	e := err.(*handler.ActionError)
	assert.Equal(403, e.Status)
	assert.Equal(handler.CodeUnknownError, e.Code)
}

func TestActionContractRates(t *testing.T) {
	assert := assert.New(t)
	ctx := handler.NewTestContext("GET", "api/v2/guild/contract/rates", false, nil)
	res, _ := ActionContractRates(ctx)
	assert.Equal(guildrate.ContractRates, res.(respContractRates).Rates)
}

func TestTagKey(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).
		Check(respRateInfo{}, "rate", "edit_rate", "edit_type",
			"status", "expire_time", "create_time")
}

func TestActionApplicationRateInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	contract := livecontract.LiveContract{
		LiveID:       777888,
		Status:       livecontract.StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	require.NoError(contract.DB().Create(&contract).Error)
	defer func() {
		require.NoError(contract.DB().Delete(contract).Error)
	}()
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       contract.LiveID,
		Username: "TestActionApplyRenewCreate2",
	}).Error)

	application := contractapplyment.ContractApplyment{
		LiveID:     777888,
		GuildID:    3,
		Rate:       44,
		ContractID: contract.ID,
		ExpireTime: now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		Status:     contractapplyment.StatusPending,
		Type:       contractapplyment.TypeRateDown,
		CreateTime: now.Unix(),
	}
	require.NoError(application.DB().Create(&application).Error)
	defer func() {
		require.NoError(application.DB().Delete(application).Error)
	}()

	// 测试申请还在未处理状态中
	res, err := ActionApplicationRateInfo(newPostC("edit/rate", "?creator_id=777888", nil))
	require.NoError(err)
	assert.NotNil(res)

	// 测试公会关系发生变化后又重新建立关系
	require.NoError(application.DB().Update("contract_id", 2333233).Error)
	res, err = ActionApplicationRateInfo(newPostC("edit/rate", "?creator_id=777888", nil))
	require.NoError(err)
	assert.Nil(res)
}
