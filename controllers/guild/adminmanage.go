package guild

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type adminManageParam struct {
	adminmanageResp

	guildID   int64
	guildName string
	ownerID   int64
	ownerName string
	sort      string

	// filterType 筛选模式
	// 0: 无筛选
	// 1： 公会 ID
	// 2： 公会名
	// 3: 会长 ID
	// 4: 会长昵称
	filterType int
	db         *gorm.DB
}

type guildInfo struct {
	GuildID   int64   `gorm:"column:id" json:"guild_id"`
	GuildName string  `gorm:"column:name" json:"guild_name"`
	OwnerID   int64   `gorm:"column:user_id" json:"owner_id"`
	OwnerName string  `gorm:"column:username" json:"owner_name"`
	LiveNum   int64   `gorm:"column:live_num" json:"live_num"`
	Rate      float64 `gorm:"column:rate" json:"rate"`
}

func (guildInfo) TableName() string {
	return guild.TableName()
}

type adminmanageResp struct {
	GuildInfos []guildInfo       `json:"guild_infos"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionAdminmanage 公会管理查询
/**
 * @api {get} /api/v2/admin/guild/adminmanage 公会管理查询
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [p] 页码
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名
 * @apiParam {Number} [owner_id] 公会会长 ID
 * @apiParam {String} [owner_name] 会长昵称
 * @apiParam {String} [sort=id] 排序方式 id, id.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "guild_infos": [{
 *         "guild_id": 3,
 *         "guild_name": "公会3",
 *         "owner_id": 12,
 *         "owner_name": "零月",
 *         "live_num": 2,
 *         "rate": 0.6
 *       },
 *       {
 *         "guild_id": 6,
 *         "guild_name": "公i会6",
 *         "owner_id": 12,
 *         "owner_name": "零月",
 *         "live_num": 0,
 *         "rate": 0
 *       },
 *       {
 *         "guild_id": 118,
 *         "guild_name": "test",
 *         "owner_id": 12,
 *         "owner_name": "零月",
 *         "live_num": 0,
 *         "rate": 0.5
 *       }],
 *       "pagination": {
 *         "count": 3,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 */
func ActionAdminmanage(c *handler.Context) (handler.ActionResponse, error) {
	access, _, err := liveuser.IsStaff(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}
	var param adminManageParam
	err = param.Load(c)
	if err != nil {
		return nil, err
	}
	param.ApplyParam()
	err = param.BuildResp()
	if err != nil {
		return nil, err
	}
	return &param.adminmanageResp, nil
}

func (param *adminManageParam) Load(c *handler.Context) error {
	param.Pagination.P, _ = c.GetParamInt64("p")
	if param.Pagination.P <= 0 {
		param.Pagination.P = 1
	}
	var err error
	param.guildID, err = c.GetDefaultParamInt64("guild_id", 0)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.ownerID, err = c.GetDefaultParamInt64("owner_id", 0)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.guildName, _ = c.GetParam("guild_name")
	param.ownerName, _ = c.GetParam("owner_name")
	filters := []bool{
		param.guildID != 0,
		param.guildName != "",
		param.ownerID != 0,
		param.ownerName != "",
	}
	for i := 0; i < len(filters); i++ {
		if filters[i] {
			param.filterType = i + 1
			break
		}
	}

	param.sort = parseSortStr(c.GetDefaultParam("sort", "guild_id"))
	return nil
}

func (param *adminManageParam) ApplyParam() {
	db := service.DB.Table(guild.TableName() + " AS g")

	if param.sort == "guild_id DESC" {
		db = db.Order("g.id DESC")
	} else {
		db = db.Order("g.id")
	}
	db = db.Select("g.id, g.name, g.user_id, u.username, g.live_num")
	db = db.Joins(fmt.Sprintf("LEFT JOIN %s AS u ON u.id = g.user_id", mowangskuser.TableName()))
	db = db.Where("g.checked = ?", guild.CheckedPass)
	switch param.filterType {
	case 1:
		db = db.Where("g.id = ?", param.guildID)
	case 2:
		db = db.Where("g.name LIKE ?", servicedb.ToLikeStr(param.guildName))
	case 3:
		db = db.Where("g.user_id = ?", param.ownerID)
	case 4:
		db = db.Where("u.username LIKE ?", servicedb.ToLikeStr(param.ownerName))
	}
	param.db = db
}

func (param *adminManageParam) BuildResp() error {
	err := param.db.Count(&param.Pagination.Count).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p := &param.Pagination
	param.Pagination = goutil.MakePagination(p.Count, p.P, goutil.DefaultPageSize)
	param.GuildInfos = make([]guildInfo, 0)
	err = param.Pagination.ApplyTo(param.db).Find(&param.GuildInfos).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 补充 rate 信息
	err = param.fillGuildRate()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// fillGuildRate 补充 rate 信息
func (param *adminManageParam) fillGuildRate() error {
	if len(param.GuildInfos) <= 0 {
		return nil
	}

	guildIDs := make([]int64, 0, len(param.GuildInfos))
	for _, gInfo := range param.GuildInfos {
		guildIDs = append(guildIDs, gInfo.GuildID)
	}

	guildBalanceMap, err := guildbalance.FindMap(guildIDs)
	if err != nil {
		return err
	}
	for k, gInfo := range param.GuildInfos {
		if guildBalance, ok := guildBalanceMap[gInfo.GuildID]; ok {
			param.GuildInfos[k].Rate = guildBalance.Rate
		}
	}

	return nil
}
