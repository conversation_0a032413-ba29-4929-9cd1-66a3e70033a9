package guild

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testSignLiveID          = 1234
	testRenewLiveID         = 2345
	testTerminateLiveID     = 3456
	testApplyOperateGuildID = 3
)

func TestNewGuildOperateLiveApplyParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := newPostC("/sign/pass", "", nil)
	c.User().ID = 999999
	_, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveSign)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	// json
	param, err := newGuildOperateLiveApplyParam(newPostC("/sign/refuse", "",
		map[string]interface{}{"id": 10, "live_id": 12}), contractapplyment.TypeLiveSign)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(int64(1), param.applymentType)
	assert.Equal(int64(10), param.ID)
	assert.Equal(int64(12), param.LiveID)
	assert.NotNil(param.guild)
	// postform
	param, err = newGuildOperateLiveApplyParam(newPostC("/renew/revoke",
		"?live_id=15&id=999", nil), contractapplyment.TypeGuildRenew)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(int64(4), param.applymentType)
	assert.Equal(int64(999), param.ID)
	assert.Equal(int64(15), param.LiveID)
	// 没读取到
	_, err = newGuildOperateLiveApplyParam(newPostC("/renew/revoke",
		"?live_id=a&id=999", nil), contractapplyment.TypeGuildRenew)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestActionSignOperates(t *testing.T) {
	require := require.New(t)

	ca := contractapplyment.ContractApplyment{
		ID:               1,
		LiveID:           testSignLiveID,
		GuildID:          testApplyOperateGuildID,
		GuildName:        "测试公会处理签约申请用",
		ExpireTime:       goutil.TimeNow().Unix() + 100,
		Type:             contractapplyment.TypeGuildSign,
		Rate:             22,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		Status:           contractapplyment.StatusPending,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca.ID).Error)
	require.NoError(service.DB.FirstOrCreate(&ca).Error)
	require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ? AND live_id = ?", ca.GuildID, testSignLiveID).Error)
	updateDB := service.DB.Model(&ca)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 2}).Error)
	t.Run("revoke", subTestActionApplySignRevoke)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 1}).Error)
	t.Run("refuse", subTestActionApplySignRefuse)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 1, "rate": 22}).Error)
	t.Run("pass", subTestActionApplySignPass)
}

func subTestActionApplySignPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	beforePass, err := guild.Find(testApplyOperateGuildID)
	require.NoError(err)
	now := goutil.TimeNow().Unix()
	_, err = ActionApplySignPass(newPostC("/sign/pass", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplySignPass(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplySignPass(newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	require.NoError(err)
	assert.Equal("签约成功", r)
	_, err = ActionApplySignPass(newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)

	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的签约申请已通过").Error)
	assert.True(strings.HasPrefix(msg.Content,
		fmt.Sprintf("直播公会 %s 已通过您的签约申请。", testGuild.Name)), msg.Content)

	afterPass, err := guild.Find(testApplyOperateGuildID)
	require.NoError(err)
	assert.Equal(int64(1), afterPass.LiveNum-beforePass.LiveNum)

	contract := new(livecontract.LiveContract)
	require.NoError(service.DB.Table(livecontract.TableName()).
		Where("guild_id = ? AND live_id = ? AND status = ?", testApplyOperateGuildID, testSignLiveID, 1).First(&contract).Error)
	assert.Equal(22, contract.Rate)
	assert.GreaterOrEqual(contract.ContractStart, now)
}

func subTestActionApplySignRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplySignRefuse(newPostC("/sign/refuse", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplySignRefuse(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplySignRefuse(newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	require.NoError(err)
	assert.Equal("拒绝成功", r)
	_, err = ActionApplySignRefuse(newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)

	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的签约申请未通过").Error)

	assert.Equal(fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的签约申请。", testGuild.Name), msg.Content)
}

func subTestActionApplySignRevoke(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplySignRevoke(newPostC("/sign/revoke", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/sign/revoke", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplySignRevoke(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplySignRevoke(newPostC("/sign/revoke", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	require.NoError(err)
	assert.Equal("已撤回签约邀请", r)
	_, err = ActionApplySignRevoke(newPostC("/sign/revoke", fmt.Sprintf("?live_id=%d&id=%d", testSignLiveID, 1), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)
}

func TestActionRenewOperates(t *testing.T) {
	require := require.New(t)

	ca := contractapplyment.ContractApplyment{
		ID:         3,
		LiveID:     testRenewLiveID,
		GuildID:    testApplyOperateGuildID,
		GuildName:  "测试公会处理续约申请用",
		ExpireTime: 3876543210,
		Type:       4,
	}
	require.NoError(service.DB.Save(&ca).Error)
	contract := livecontract.LiveContract{
		ID:               11,
		GuildID:          testApplyOperateGuildID,
		GuildName:        "测试公会处理续约申请用",
		LiveID:           testRenewLiveID,
		Status:           livecontract.StatusContracting,
		ContractDuration: 1,
		ContractEnd:      goutil.TimeNow().AddDate(0, 6, 0).Unix(),
		Attr:             1,
	}
	require.NoError(service.DB.Save(&contract).Error)
	updateDB := service.DB.Model(&ca)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 4}).Error)
	t.Run("revoke", subTestActionRenewRevoke)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 3}).Error)
	t.Run("refuse", subTestActionRenewRefuse)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 3, "rate": 80,
			"contract_expire_time": renewContractExpireTime,
			"contract_duration":    1, "contract_id": 11}).Error)
	t.Run("pass", subTestActionRenewPass)
}

var renewContractExpireTime = int64(1234567890)

func subTestActionRenewRevoke(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplyRenewRevoke(newPostC("/renew/revoke", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/renew/revoke", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyRenewRevoke(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplyRenewRevoke(newPostC("/renew/revoke", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	require.NoError(err)
	assert.Equal("已撤回续约邀请", r)
	_, err = ActionApplyRenewRevoke(newPostC("/renew/revoke", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)
}

func subTestActionRenewRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplyRenewRefuse(newPostC("/sign/refuse", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyRenewRefuse(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplyRenewRefuse(newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	require.NoError(err)
	assert.Equal("拒绝成功", r)
	_, err = ActionApplyRenewRefuse(newPostC("/sign/refuse", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)

	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的续约申请未通过").Error)

	assert.Equal(fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的续约申请。", testGuild.Name), msg.Content)
}

func subTestActionRenewPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	_, err := ActionApplyRenewPass(newPostC("/sign/pass", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyRenewPass(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 正常续约
	r, err := ActionApplyRenewPass(newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	require.NoError(err)
	assert.Equal("续约成功", r)
	contract := new(livecontract.LiveContract)
	require.NoError(service.DB.Table(livecontract.TableName()).
		Where("guild_id = ? AND live_id = ?", testApplyOperateGuildID, testRenewLiveID).First(&contract).Error)
	assert.Equal(80, contract.Rate)
	assert.Equal(renewContractExpireTime, contract.ContractEnd)
	assert.False(contract.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated))

	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的续约申请已通过").Error)
	assert.True(strings.HasPrefix(msg.Content,
		fmt.Sprintf("直播公会 %s 已通过您的续约申请。", testGuild.Name)), msg.Content)
	// 合同错误
	_, err = ActionApplyRenewPass(newPostC("/sign/pass", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)
	// 有合同但是没合约
	require.NoError(service.DB.Table(contractapplyment.TableName()).
		Where("id = ?", 3).Update("status", 0).Error)
	require.NoError(service.DB.Table(livecontract.TableName()).
		Where("id = ?", 11).Update("status", livecontract.StatusUntreated).Error)
	_, err = ActionApplyRenewPass(newPostC("/terminate/pass", fmt.Sprintf("?live_id=%d&id=%d", testRenewLiveID, 3), nil))
	assert.Equal(actionerrors.ErrContractNotExist, err)
}

func TestActionTerminateOperates(t *testing.T) {
	require := require.New(t)

	ca := contractapplyment.ContractApplyment{
		ID:         4,
		LiveID:     testTerminateLiveID,
		GuildID:    testApplyOperateGuildID,
		GuildName:  "测试公会处理解约申请用",
		ExpireTime: 3876543210,
		ContractID: 12,
		Type:       6,
	}
	require.NoError(service.DB.Save(&ca).Error)
	contract := livecontract.LiveContract{
		ID:               12,
		GuildID:          testApplyOperateGuildID,
		GuildName:        "测试公会处理解约申请用",
		LiveID:           testTerminateLiveID,
		ContractDuration: 1,
		Status:           livecontract.StatusContracting,
		ContractEnd:      goutil.TimeNow().AddDate(0, 6, 0).Unix(),
	}
	require.NoError(service.DB.Save(&contract).Error)
	updateDB := service.DB.Model(&ca)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 6}).Error)
	t.Run("revoke", subTestActionTerminateRevoke)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 5}).Error)
	t.Run("refuse", subTestActionTerminateRefuse)

	require.NoError(
		updateDB.Updates(map[string]interface{}{"status": 0, "type": 5}).Error)
	t.Run("pass", subTestActionTerminatePass)
}

func subTestActionTerminateRevoke(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplyTerminateRevoke(newPostC("/terminate/revoke", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/terminate/revoke", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyTerminateRevoke(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplyTerminateRevoke(newPostC("/terminate/revoke", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	require.NoError(err)
	assert.Equal("已撤回清退申请", r)
	_, err = ActionApplyTerminateRevoke(newPostC("/terminate/revoke", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)
}

func subTestActionTerminateRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionApplyTerminateRefuse(newPostC("/terminate/refuse", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/terminate/refuse", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyTerminateRefuse(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	r, err := ActionApplyTerminateRefuse(newPostC("/terminate/refuse", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	require.NoError(err)
	assert.Equal("拒绝成功", r)
	_, err = ActionApplyTerminateRefuse(newPostC("/terminate/refuse", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)

	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的协商解约申请未通过").Error)

	assert.Equal(fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的协商解约申请。", testGuild.Name), msg.Content)
}

func subTestActionTerminatePass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	beforePass, err := guild.Find(testApplyOperateGuildID)
	require.NoError(err)
	// 参数错误
	_, err = ActionApplyTerminatePass(newPostC("/terminate/pass", "", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/terminate/pass", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyTerminatePass(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 成功执行
	r, err := ActionApplyTerminatePass(newPostC("/terminate/pass", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	require.NoError(err)
	assert.Equal("解约成功", r)
	contract := new(livecontract.LiveContract)
	require.NoError(service.DB.Table(livecontract.TableName()).
		Where("guild_id = ? AND live_id = ?", testApplyOperateGuildID, testTerminateLiveID).First(&contract).Error)
	assert.Equal(livecontract.StatusUseless, contract.Status)
	var msg messageassign.MessageAssign
	require.NoError(service.DB.Order("time DESC").First(&msg, "title = ?", "您的协商解约申请已通过").Error)
	assert.True(strings.HasPrefix(msg.Content,
		fmt.Sprintf("直播公会 %s 已通过您的协商解约申请。", testGuild.Name)), msg.Content)
	afterPass, err := guild.Find(testApplyOperateGuildID)
	require.NoError(err)
	assert.Equal(int64(1), beforePass.LiveNum-afterPass.LiveNum)
	// 合同错误
	_, err = ActionApplyTerminatePass(newPostC("/terminate/pass", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	assert.Equal(actionerrors.ErrGuildApplyStatus, err)
	// 合同对但是没有合约
	require.NoError(service.DB.Table(contractapplyment.TableName()).
		Where("id = ?", 4).Update("status", 0).Error)
	_, err = ActionApplyTerminatePass(newPostC("/terminate/pass", fmt.Sprintf("?live_id=%d&id=%d", testTerminateLiveID, 4), nil))
	assert.Equal(actionerrors.ErrContractNotExist, err)
}

func TestGuildOperateLiveApplyParamCheckApplyment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLiveID := int64(33445566)
	ca := contractapplyment.ContractApplyment{
		LiveID:     testLiveID,
		GuildID:    testApplyOperateGuildID,
		GuildName:  "test-check-applyment",
		ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		Type:       contractapplyment.TypeGuildSign,
	}
	require.NoError(service.DB.Save(&ca).Error)
	defer func() {
		require.NoError(service.DB.Table(contractapplyment.TableName()).Delete(nil, "id = ?", ca.ID).Error)
	}()

	var role guildrole.GuildRole
	role.Set(guildrole.RoleGuildAgent)
	param := guildOperateLiveApplyParam{
		ID: ca.ID,
		guild: &guild.IDName{
			ID: testApplyOperateGuildID,
		},
		LiveID: testLiveID,
		role:   role,
		user: &user.User{
			IUser: user.IUser{
				ID: 333111333,
			},
		},
	}
	_, err := param.checkApplyment(contractapplyment.TypeGuildRenew)
	require.EqualError(err, actionerrors.ErrGuildApplyStatus.Message)

	_, err = param.checkApplyment(ca.Type)
	require.EqualError(err, actionerrors.ErrCreatorNotAssignedToYou.Message)

	agentCreator := guildagent.AgentCreator{
		CreatorID: ca.LiveID,
		GuildID:   ca.GuildID,
		AgentID:   param.user.ID,
	}
	require.NoError(guildagent.AgentCreator{}.DB().Create(&agentCreator).Error)
	defer func() {
		require.NoError(guildagent.AgentCreator{}.DB().Delete(nil, "id = ?", agentCreator.ID).Error)
	}()
	applyment, err := param.checkApplyment(ca.Type)
	require.NoError(err)
	assert.NotNil(applyment)

	require.NoError(service.DB.Table(contractapplyment.TableName()).Where("id = ?", ca.ID).
		UpdateColumn("agent_id", param.user.ID).Error)
	applyment, err = param.checkApplyment(ca.Type)
	require.NoError(err)
	assert.NotNil(applyment)

	role.Set(guildrole.RoleGuildOwner)
	applyment, err = param.checkApplyment(ca.Type)
	require.NoError(err)
	assert.NotNil(applyment)
}
