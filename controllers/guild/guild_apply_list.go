package guild

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildliveorder"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type applymentPendingInfo struct {
	Type         int64 `gorm:"column:type"`
	PendingCount int   `gorm:"column:pending_count"`
}

type applymentPendingResp struct {
	SignCount      int `json:"sign_count"`
	RenewCount     int `json:"renew_count"`
	TerminateCount int `json:"terminate_count"`
	TotalCount     int `json:"total_count"`
}

// 公会查看列表的分类
const (
	sign      = "sign"
	renew     = "renew"
	terminate = "terminate"
	editRate  = "edit_rate"
)

const settlement = "对公结算"

type guildApplyListResp struct {
	Data       []*guildApplyElem `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

func (resp *guildApplyListResp) fillData(param *guildApplyListParam, cas []*contractapplyment.ContractApplyment) error {
	// 只保留页码内的
	if int64(len(cas)) > resp.Pagination.PageSize {
		cas = cas[:resp.Pagination.PageSize]
	}
	liveIDs := make([]int64, len(cas))
	for i := 0; i < len(cas); i++ {
		liveIDs[i] = cas[i].LiveID
	}
	userIDs := liveIDs
	isGuildOwner := param.role.IsGuildOwner()
	var creatorIDAgentIDMap map[int64]int64
	if isGuildOwner {
		creatorIDAgentIDMap = param.agentCreators.creatorAgentMap(liveIDs)
		for _, item := range cas {
			if item.AgentID != 0 {
				creatorIDAgentIDMap[item.LiveID] = item.AgentID
			}
		}
		for _, agentID := range creatorIDAgentIDMap {
			userIDs = append(userIDs, agentID)
		}
	}

	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	resp.Data = make([]*guildApplyElem, len(cas))
	for i := 0; i < len(cas); i++ {
		elem := &guildApplyElem{
			ID:                 cas[i].ID,
			LiveID:             cas[i].LiveID,
			Period:             contractapplyment.FormatContractDuration(cas[i]),
			Settlement:         settlement,
			Rate:               guildrate.ApplymentRate(cas[i].Rate),
			Type:               cas[i].Type,
			ExpireTime:         cas[i].ExpireTime,
			CreateTime:         cas[i].CreateTime,
			ContractExpireTime: cas[i].ContractExpireTime,
			AgentID:            cas[i].AgentID,
		}
		elem.setStatus(cas[i].Status)
		if u := users[cas[i].LiveID]; u != nil {
			elem.LiveUsername = u.Username
			elem.LiveIconURL = u.IconURL
		}
		if isGuildOwner {
			if elem.AgentID != 0 {
				if u := users[elem.AgentID]; u != nil {
					elem.AgentID = u.ID
					elem.AgentUsername = u.Username
				}
			} else if agentID, ok := creatorIDAgentIDMap[cas[i].LiveID]; ok {
				if u := users[agentID]; u != nil {
					elem.AgentID = u.ID
					elem.AgentUsername = u.Username
				}
			}
		} else {
			elem.AgentID = param.user.ID
			elem.AgentUsername = param.user.Username
		}
		resp.Data[i] = elem
	}
	return nil
}

type guildApplyElem struct {
	ID int64 `json:"id"`

	LiveID       int64  `json:"live_id"`
	LiveUsername string `json:"live_username"`
	LiveIconURL  string `json:"live_iconurl"`

	Period             string `json:"period"`
	Settlement         string `json:"settlement"`
	Rate               int    `json:"rate"`
	Type               int64  `json:"type"`
	Status             int64  `json:"status"`
	ExpireTime         int64  `json:"expire_time"`
	CreateTime         int64  `json:"create_time"`
	ContractExpireTime int64  `json:"contract_expire_time,omitempty"`

	AgentID       int64  `json:"agent_id"`
	AgentUsername string `json:"agent_username"`
}

func (elem *guildApplyElem) setStatus(applyStatus int64) {
	now := goutil.TimeNow()
	switch {
	case applyStatus == contractapplyment.StatusPending && elem.ExpireTime <= now.Unix():
		applyStatus = contractapplyment.StatusOutdated
	case applyStatus == contractapplyment.StatusRevoked &&
		(elem.Type == contractapplyment.TypeLiveSign ||
			elem.Type == contractapplyment.TypeLiveRenew ||
			elem.Type == contractapplyment.TypeLiveTerminate):
		applyStatus = contractapplyment.StatusInvalid
	}
	elem.Status = applyStatus
}

type guildApplyListParam struct {
	guildID  int64
	listType string
	role     guildrole.GuildRole
	user     *user.User

	p        int64
	pageSize int64

	liveID       int64
	liveUsername string
	searchAgent  struct {
		word        string
		isInteger   bool
		wordInteger int64
	}
	status        *int64 // 用值无法区分没传值和传的 status 是 0
	applymentType int64

	agentCreators agentCreators
}

func (param *guildApplyListParam) check() error {
	if param.status != nil && (*param.status < contractapplyment.StatusInvalid || *param.status > contractapplyment.StatusAgreed) {
		return actionerrors.ErrParams
	}
	if param.applymentType != 0 {
		switch param.listType {
		case sign:
			if param.applymentType != contractapplyment.TypeLiveSign &&
				param.applymentType != contractapplyment.TypeGuildSign {
				return actionerrors.ErrParams
			}
		case renew:
			if param.applymentType != contractapplyment.TypeLiveRenew &&
				param.applymentType != contractapplyment.TypeGuildRenew {
				return actionerrors.ErrParams
			}
		case terminate:
			if param.applymentType != contractapplyment.TypeLiveTerminate &&
				param.applymentType != contractapplyment.TypeGuildExpel &&
				param.applymentType != contractapplyment.TypeLiveTerminateForcely {
				return actionerrors.ErrParams
			}
		}
	}
	return nil
}

// constructSQL 根据筛选条件构建查询语句
// NOTICE: 未加入排序
// TODO: 改用 union 而不是 where 写关于 OR 的执行语句
func (param *guildApplyListParam) constructSQL() (*gorm.DB, error) {
	db := service.DB.Table(contractapplyment.TableName()+" AS c").Select("c.*").
		Where("c.guild_id = ?", param.guildID)
	switch param.applymentType {
	case 0: // 无 type 筛选
		switch param.listType {
		case sign:
			db = db.Where("c.type IN (?)",
				[]int64{contractapplyment.TypeLiveSign, contractapplyment.TypeGuildSign})
		case renew:
			db = db.Where("c.type IN (?)",
				[]int64{contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildRenew})
		case terminate:
			db = db.Where("c.type IN (?) OR (c.type = ? AND c.status = ?)",
				[]int64{contractapplyment.TypeLiveTerminate, contractapplyment.TypeGuildExpel},
				contractapplyment.TypeLiveTerminateForcely, contractapplyment.StatusAgreed)
		}
	case contractapplyment.TypeLiveTerminateForcely: // 强制解约筛选
		db = db.Where("c.type = ? AND c.status = ?",
			contractapplyment.TypeLiveTerminateForcely, contractapplyment.StatusAgreed)
	default: // 一般的筛选
		db = db.Where("c.type = ?", param.applymentType)
	}
	if param.liveID != 0 {
		db = db.Where("c.live_id = ?", param.liveID)
	}
	now := goutil.TimeNow()
	if param.status != nil {
		switch *param.status {
		case contractapplyment.StatusInvalid:
			db = db.Where(
				"c.status IN (?) OR (c.status = ? AND c.initiator = ?) OR (c.status = ? AND c.expire_time <= ?)",
				[]int64{contractapplyment.StatusOutdated, contractapplyment.StatusInvalid},
				contractapplyment.StatusRevoked, contractapplyment.InitiatorLive,
				contractapplyment.StatusPending, now.Unix())
		case contractapplyment.StatusRevoked:
			db = db.Where("c.status = ? AND c.initiator = ?",
				contractapplyment.StatusRevoked, contractapplyment.InitiatorGuild)
		case contractapplyment.StatusPending:
			db = db.Where("c.status = ? AND c.expire_time > ?", contractapplyment.StatusPending, now.Unix())
		case contractapplyment.StatusDeclined, contractapplyment.StatusAgreed:
			fallthrough
		default:
			db = db.Where("c.status = ?", *param.status)
		}
	}
	if param.liveUsername != "" {
		db = db.Joins(fmt.Sprintf("LEFT JOIN %s AS u on c.live_id = u.id", mowangskuser.TableName())).Where("u.username LIKE ?", servicedb.ToLikeStr(param.liveUsername))
	}

	if !param.role.IsGuildOwner() {
		agentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(agentCreatorIDs) == 0 {
			db = db.Where("c.agent_id = ?", param.user.ID)
		} else {
			db = db.Where("c.live_id IN (?) OR c.agent_id = ?", agentCreatorIDs, param.user.ID)
		}
	}

	// 通过经纪人 ID 或经纪人昵称搜索主播
	if param.searchAgent.word != "" {
		if !param.role.IsGuildOwner() {
			// 经纪人只能搜索自己名下的主播
			if param.searchAgent.isInteger && param.searchAgent.wordInteger == param.user.ID || strings.Contains(param.user.Username, param.searchAgent.word) {
				return db, nil
			}

			return nil, nil
		}

		var agentCreatorIDs []int64
		if param.searchAgent.isInteger {
			agentCreatorIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, param.searchAgent.wordInteger)
			if len(agentCreatorIDs) == 0 {
				db = db.Where("c.agent_id = ?", param.searchAgent.wordInteger)
			} else {
				db = db.Where("c.live_id IN (?) OR c.agent_id = ?", agentCreatorIDs, param.searchAgent.wordInteger)
			}
		} else {
			agentIDs := param.agentCreators.allAgentIDs()
			agentIDsFounded, err := mowangskuser.SearchUserByUsername(param.searchAgent.word, agentIDs)
			if err != nil {
				return nil, err
			}
			if len(agentIDsFounded) != 0 {
				agentCreatorIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDsFounded...)
			}

			var agentIDsApplyment []int64
			err = service.DB.Table(contractapplyment.TableName()).Select("agent_id").
				Where("guild_id = ? AND agent_id <> 0", param.guildID).
				Pluck("agent_id", &agentIDsApplyment).Error
			if err != nil {
				return nil, err
			}
			var conditions []string
			var args []interface{}
			if len(agentIDsApplyment) != 0 {
				conditions = append(conditions, "c.agent_id IN (?)")
				args = append(args, agentIDsApplyment)
			}
			if len(agentCreatorIDs) != 0 {
				conditions = append(conditions, "c.live_id IN (?)")
				args = append(args, agentCreatorIDs)
			}
			if len(conditions) == 0 {
				return nil, nil
			}
			db = db.Where(strings.Join(conditions, " OR "), args...)
		}
	}

	return db, nil
}

func (param *guildApplyListParam) emptyResp() *guildApplyListResp {
	return &guildApplyListResp{
		Data:       make([]*guildApplyElem, 0),
		Pagination: goutil.MakePagination(0, param.p, param.pageSize),
	}
}

func (param *guildApplyListParam) buildResp() (*guildApplyListResp, error) {
	db, err := param.constructSQL()
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if db == nil {
		return param.emptyResp(), nil
	}

	var count int64
	if err = db.Count(&count).Error; err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if count == 0 {
		return param.emptyResp(), nil
	}

	resp := new(guildApplyListResp)
	resp.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]*guildApplyElem, 0)
		return resp, nil
	}
	caList := make([]*contractapplyment.ContractApplyment, 0, param.pageSize)
	err = resp.Pagination.ApplyTo(db).Order("c.create_time DESC").Find(&caList).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	err = resp.fillData(param, caList)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newGuildApplyListParam(c *handler.Context, listType string) (*guildApplyListParam, error) {
	if listType != sign && listType != renew && listType != terminate {
		panic("unsupported listType: " + listType)
	}

	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() || (listType == terminate && !role.IsGuildOwner()) {
		return nil, actionerrors.ErrNoAuthority
	}

	param := &guildApplyListParam{guildID: guildInfo.ID, listType: listType, role: role}
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	status, err := c.GetParamInt64("status")
	if err == nil {
		param.status = &status
	}
	param.applymentType, _ = c.GetParamInt64("type")
	param.liveID, _ = c.GetParamInt64("live_id")
	param.liveUsername, _ = c.GetParam("live_username")
	if param.searchAgent.word, _ = c.GetParam("search_agent"); param.searchAgent.word != "" {
		param.searchAgent.wordInteger, err = strconv.ParseInt(param.searchAgent.word, 10, 64)
		if err == nil {
			param.searchAgent.isInteger = true
		}
	}
	param.user = c.User()
	param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	return param, nil
}

// ActionApplySignList 公会查看签约申请列表
/**
 * @api {get} /api/v2/guild/applyment/sign/list 公会查看签约申请列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {Number} [live_id] 主播 ID 筛选
 * @apiParam {String} [live_username] 主播昵称筛选
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {number=-3,-2,-1,0,1} [status] 状态筛选. -3: 已失效（包含过期失效和失效）, -2: 已撤回 -1: 已拒绝, 0: 待处理, 1: 已通过
 * @apiParam {number=1,2} [type] 申请类型筛选, 1: 主播签约公会申请，2：公会签约主播申请
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "Datas": [{
 *           "id": 1, // 签约申请 id
 *           "live_id": 32, // 主播 ID
 *           "live_username": "XXX", // 主播昵称
 *           "live_iconurl": "https://xxx.png", // 主播头像
 *           "period": "6 个月", // 签约期限
 *           "settlement": "对公结算", // 结算方式
 *           "rate": 45, // 主播分成比例，不得低于 45%
 *           "type": 1, // 申请类型 1: 主播签约公会申请，2：公会签约主播申请
 *           "status": 1, // 状态 1 已通过, 0 待处理, -1 已拒绝, -2 已撤回, -3 为已失效, -4 为过期失效
 *           "expire_time": 133456789, // 过期时间或已处理的时间
 *           "create_time": 123456789 // 申请时间
 *           "contract_expire_time": 1534567890, // 合约到期时间，可能没有
 *           "agent_username": "invinciblezz", // 经纪人昵称
 *           "agent_id": 346286  // 经纪人 ID
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionApplySignList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildApplyListParam(c, sign)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	return param.buildResp()
}

// ActionApplyRenewList 公会查看签约申请列表
/**
 * @api {get} /api/v2/guild/applyment/renew/list 公会查看续约申请列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {Number} [live_id] 主播 ID 筛选
 * @apiParam {String} [live_username] 主播昵称筛选
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {number=-3,-2,-1,0,1} [status] 状态筛选. -3: 已失效（包含过期失效和失效）, -2: 已撤回 -1: 已拒绝, 0: 待处理, 1: 已通过
 * @apiParam {number=3,4} [type] 申请类型筛选, 3: 主播发起的续约申请，4: 公会发起的续约申请
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "Datas": [{
 *           "id": 1,
 *           "live_id": 32,
 *           "live_username": "XXX",
 *           "live_iconurl": "https://xxx.png",
 *           "period": "6 个月",
 *           "settlement": "对公结算",
 *           "rate": 45,
 *           "type": 3,
 *           "status": 1, // -4 已过期（超时未处理），-3 已失效（公会/用户状态发生变更），-2 已撤回，-1 已拒绝，0 未处理，1 已同意
 *           "expire_time": 133456789,
 *           "create_time": 123456789,
 *           "contract_expire_time": 1534567890, // 续约通过之后的合同到期时间
 *           "agent_username": "invinciblezz", // 经纪人昵称
 *           "agent_id": 346286  // 经纪人 ID
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionApplyRenewList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildApplyListParam(c, renew)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	return param.buildResp()
}

// ActionApplyTerminateList 公会查看解约申请列表
/**
 * @api {get} /api/v2/guild/applyment/terminate/list 公会查看解约申请列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {Number} [live_id] 主播 ID 筛选
 * @apiParam {String} [live_username] 主播昵称筛选
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {number=-3,-2,-1,0,1} [status] 状态筛选. -3: 已失效, -2: 已撤回 -1: 已拒绝, 0: 待处理, 1: 已通过
 * @apiParam {number=5,6,7} [type] 申请类型筛选, 5: 主播协商解约申请，6: 公会清退申请，7: 主播强制解约申请
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "Datas": [{
 *           "id": 1,
 *           "live_id": 32,
 *           "live_username": "XXX",
 *           "live_iconurl": "https://xxx.png",
 *           "period": "6 个月",
 *           "settlement": "对公结算",
 *           "rate": 45,
 *           "type": 5,
 *           "status": 1,
 *           "expire_time": 133456789,
 *           "create_time": 123456789,
 *           "agent_username": "invinciblezz", // 经纪人昵称
 *           "agent_id": 346286  // 经纪人 ID
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionApplyTerminateList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildApplyListParam(c, terminate)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	return param.buildResp()
}

// ActionApplyTerminatePrice 公会获取强制解约的申请的金额
/**
 * @api {get} /api/v2/guild/applyment/terminate/price 公会获取强制解约的申请的金额
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "price": "100.42"
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyTerminatePrice(c *handler.Context) (handler.ActionResponse, error) {
	liveID, _ := c.GetParamInt64("live_id")
	id, _ := c.GetParamInt64("id")
	if liveID <= 0 || id <= 0 {
		return nil, actionerrors.ErrParams
	}
	g, err := guildrole.IsGuildOwner(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrNoAuthority
	}
	var glo guildliveorder.GuildLiveOrder
	// TODO: guildliveorder 的 status 还没定义常量
	err = glo.DB().First(&glo,
		"guild_id = ? AND live_id = ? AND status = 1 AND applyment_id = ?",
		g.ID, liveID, id).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, actionerrors.ErrParams
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	return handler.M{"price": fmt.Sprintf("%.2f", float64(glo.Price)/100)}, nil
}

// ActionApplymentPendingCount 公会未处理的签约/续约/解约数量
/**
 * @api {get} /api/v2/guild/applyment/pending-count 公会未处理的签约/续约/解约数量
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": {
 *     "sign_count": 2, // 未处理的签约数量
 *     "renew_count": 3, // 未处理的续约数量
 *     "terminate_count": 3, // 未处理的解约数量
 *     "total_count": 8 // 总的未处理数量
 *   }
 * }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionApplymentPendingCount(c *handler.Context) (handler.ActionResponse, error) {
	userID := c.UserID()
	role, guildInfo, err := guildrole.UserGuildRole(userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}

	db := contractapplyment.ContractApplyment{}.DB().
		Select("type, COUNT(*) AS pending_count").
		Where("guild_id = ? AND status = ? AND expire_time > ?",
			guildInfo.ID, contractapplyment.StatusPending, goutil.TimeNow().Unix())

	if !role.IsGuildOwner() {
		// 公会经纪人只能获取自己名下的主播
		var agentCreators agentCreators
		agentCreators, err = guildagent.FindAgentCreatorListByGuild(guildInfo.ID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		agentCreatorIDs := agentCreators.agentCreatorIDs(userID, role)
		if len(agentCreatorIDs) == 0 {
			db = db.Where("agent_id = ?", userID)
		} else {
			db = db.Where("live_id IN (?) OR agent_id = ?", agentCreatorIDs, userID)
		}

		// 公会经纪人能查看的未处理的合约申请类型
		guildAgentApplymentTypes := []int64{contractapplyment.TypeLiveSign, contractapplyment.TypeLiveRenew}
		db = db.Where("type IN (?)", guildAgentApplymentTypes)
	} else {
		// 公会长能查看的未处理的合约申请类型
		applymentTypes := []int64{contractapplyment.TypeLiveSign,
			contractapplyment.TypeLiveRenew,
			contractapplyment.TypeLiveTerminate}
		db = db.Where("type IN (?)", applymentTypes)
	}

	var applymentPendingInfo []applymentPendingInfo
	err = db.Group("type").Scan(&applymentPendingInfo).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var applymentPendingResp applymentPendingResp
	if len(applymentPendingInfo) > 0 {
		for _, v := range applymentPendingInfo {
			switch v.Type {
			case contractapplyment.TypeLiveSign, contractapplyment.TypeGuildSign:
				// 未处理的签约申请数量
				applymentPendingResp.SignCount += v.PendingCount
			case contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildRenew:
				// 未处理的续约申请数量
				applymentPendingResp.RenewCount += v.PendingCount
			case contractapplyment.TypeLiveTerminate, contractapplyment.TypeGuildExpel:
				// 未处理的解约申请数量
				applymentPendingResp.TerminateCount += v.PendingCount
			default:
				logger.Errorf("unsupported type: %d", v.Type)
			}
		}
		// 总的未处理的申请数量
		applymentPendingResp.TotalCount = applymentPendingResp.SignCount + applymentPendingResp.RenewCount + applymentPendingResp.TerminateCount
	}
	return applymentPendingResp, nil
}
