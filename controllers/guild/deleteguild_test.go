package guild

import (
	"fmt"
	"html"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionDeleteGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := map[string]interface{}{
		"id": "",
	}

	// 测试参数错误
	c := handler.NewTestContext(http.MethodPost, "api/v2/admin/deleteguild", true, body)
	_, err := ActionDeleteGuild(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试公会不存在
	body["id"] = ***********
	require.NoError(service.DB.Table(guild.TableName()).Delete("", "id = ?", body["id"]).Error)
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/deleteguild", true, body)
	_, err = ActionDeleteGuild(c)
	assert.Equal(actionerrors.ErrGuildNotExist, err)

	// 创建公会数据
	nowStamp := goutil.TimeNow().Unix()
	g := guild.Guild{
		ID:            ***********,
		Name:          "sssss",
		Intro:         "测试公会",
		OwnerName:     "钢铁侠",
		OwnerIDNumber: "*********012345678",
		Mobile:        "***********",
		Bank:          "测试银行",
		CreateTime:    nowStamp,
		ModifiedTime:  nowStamp,
		Type:          guild.TypeUnencrypted,
		Checked:       guild.CheckedPass,
		UserID:        *********,
		LiveNum:       2,
	}
	g.Encrypt()
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", body["id"]).FirstOrCreate(&g).Error)

	// 生成生效合约
	contract := livecontract.LiveContract{
		GuildID:          g.ID,
		LiveID:           101,
		GuildOwner:       g.UserID,
		Status:           livecontract.StatusContracting,
		ContractStart:    nowStamp,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ContractEnd:      goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:        g.Name,
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract).Error)

	// 创建测试直播间
	rr := &room.Room{
		Helper: room.Helper{
			RoomID:    *************,
			CreatorID: contract.LiveID,
			GuildID:   g.ID,
			Name:      "测试直播间",
			NameClean: fmt.Sprintf("测试直播间 %d", *************),
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = room.Collection().DeleteOne(ctx, bson.M{"creator_id": contract.LiveID})
	require.NoError(err)
	_, err = room.Collection().InsertOne(ctx, rr)
	require.NoError(err)

	// 创建公会收益数据
	balance := guildbalance.GuildBalance{
		ID:           g.ID,
		LiveProfit:   100,
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
	}
	require.NoError(guildbalance.GuildBalance{}.DB().Where("id = ?",
		body["id"]).FirstOrCreate(&balance).Error)

	// 创建本月流水数据
	t1 := transactionlog.TransactionLog{
		Title:        "测试公会本月流水数据",
		Income:       110,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: g.ID,
		ToID:         3,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterSuperFan,
		CTime:        nowStamp,
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		ConfirmTime:  nowStamp,
	}
	require.NoError(transactionlog.ADB().Create(&t1).Error)

	// 创建贵族流水数据
	t2 := transactionlog.TransactionLog{
		Title:        "测试公会贵族流水数据",
		Income:       120,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: g.ID,
		ToID:         3,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterNoble,
		CTime:        nowStamp,
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		ConfirmTime:  nowStamp,
	}
	require.NoError(transactionlog.ADB().Create(&t2).Error)

	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/deleteguild", true, body)
	_, err = ActionDeleteGuild(c)
	message := fmt.Sprintf("<p>注销公会名称：%s</p><p>本月礼物流水：%.2f 元</p><p>本月贵族开通流水：%.2f 元</p><p>签约主播：%d 名</p><p>注销后将全部自动解约，是否确认注销？</p><p>确认注销后将邮件通知业务负责人。</p>",
		html.EscapeString(g.Name), t1.Income-t1.Tax, t2.Income-t2.Tax, g.LiveNum)
	assert.Equal(actionerrors.ErrConfirmRequired(message, 1, true), err)

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/email",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	// 测试注销成功
	body["confirm"] = 1
	config.Conf.Params.GuildNoticeEmails.GuildDeleteNoticeEmails = "<EMAIL>,<EMAIL>"
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/deleteguild", true, body)
	r, _ := ActionDeleteGuild(c)
	assert.Equal("公会注销成功", r)

	// 测试 room.guild_id 已经被置为 0
	r1, err := room.FindOne(bson.M{"creator_id": contract.LiveID})
	require.NoError(err)
	assert.Zero(r1.GuildID)
}
