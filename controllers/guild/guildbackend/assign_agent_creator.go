package guildbackend

import (
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	"github.com/MiaoSiLa/missevan-go/util"
)

type operateAgentCreatorParam struct {
	AgentID    int64   `json:"agent_id"`
	CreatorID  int64   `json:"creator_id"`
	CreatorIDs []int64 `json:"creator_ids"`

	c         *handler.Context
	guildInfo *guild.IDName
}

// ActionGuildAssignAgentCreator 公会为主播分配/取消分配经纪人
/**
 * @api {post} /api/v2/guild/agent/assign 公会为主播分配/取消分配经纪人
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} agent_id 待分配的经纪人 ID，0 为取消分配
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {Number[]} [creator_ids] 主播 ID 列表
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "添加成功"
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 操作频繁，请稍后再试
 *
 * @apiError (404) {Number} code 500050019
 * @apiError (404) {String} info 您需要成为公会长才能操作
 *
 * @apiError (404) {Number} code 500050021
 * @apiError (404) {String} info 主播未加入您的公会
 *
 * @apiError (403) {Number} code 500050022
 * @apiError (403) {String} info 主播未分配经纪人
 *
 * @apiError (403) {Number} code 500050022
 * @apiError (403) {String} info 该主播已分配经纪人
 *
 * @apiError (404) {Number} code 500050021
 * @apiError (404) {String} info 未找到该经纪人
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionGuildAssignAgentCreator(ctx *handler.Context) (handler.ActionResponse, error) {
	param := operateAgentCreatorParam{c: ctx}
	err := ctx.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.CreatorID <= 0 && len(param.CreatorIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	param.guildInfo, err = guildrole.IsGuildOwner(param.c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.guildInfo == nil {
		return nil, actionerrors.ErrYouAreNotGuildOwner
	}
	lock := redismutex.New(
		service.Redis,
		keys.LockGuildAgentAssign1.Format(param.guildInfo.ID),
		time.Minute,
		redismutex.WithTryLockCount(1),
	)
	if !lock.TryLock() {
		return nil, actionerrors.NewErrForbidden("操作过于频繁，请稍后再试")
	}
	defer lock.Unlock()

	switch {
	case param.AgentID > 0:
		return param.assignCreatorToAgent()
	case param.AgentID == 0:
		return param.unassignCreatorToAgent()
	}
	return nil, actionerrors.ErrParams
}

func (param *operateAgentCreatorParam) assignCreatorToAgent() (handler.ActionResponse, error) {
	if len(param.CreatorIDs) > 0 && param.CreatorID > 0 {
		return nil, actionerrors.ErrParamsMsg("不能同时指定主播 ID 和主播 ID 列表")
	}
	if param.CreatorID > 0 {
		// TODO: 兼容旧接口, web 支持后, 删除对 CreatorID 的兼容
		param.CreatorIDs = append(param.CreatorIDs, param.CreatorID)
	}
	uniqCreatorIDs := util.Uniq(param.CreatorIDs)
	if len(uniqCreatorIDs) != len(param.CreatorIDs) {
		return nil, actionerrors.ErrParamsMsg("主播 ID 列表中存在重复的主播")
	}
	// 禁止给受限房间分配经纪人
	exists, err := room.Exists2(bson.M{
		"creator_id": bson.M{"$in": param.CreatorIDs},
		"limit":      bson.M{"$exists": true, "$ne": nil},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrNoAuthority
	}
	creators, err := mowangskuser.FindSimpleMap(param.CreatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(creators) != len(param.CreatorIDs) {
		return nil, actionerrors.ErrCannotFindUser
	}
	// 检查主播是否都在公会中
	contracts, err := livecontract.FindUsersGuildContractingContracts(param.guildInfo.ID, param.CreatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(contracts) != len(param.CreatorIDs) {
		return nil, actionerrors.ErrCreatorNotInYourGuild
	}

	agent, err := mowangskuser.FindByUserID(param.AgentID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if agent == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	agentGuildID, err := guildagent.GuildID(param.AgentID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	needAddAgent := agentGuildID != param.guildInfo.ID && agentGuildID == 0 && param.AgentID == param.guildInfo.UserID
	if agentGuildID != param.guildInfo.ID && !needAddAgent {
		return nil, actionerrors.ErrAgentNotFound
	}
	err = param.assign(needAddAgent)
	if err != nil {
		return nil, err
	}
	return "经纪人分配成功", nil
}

func (param *operateAgentCreatorParam) assign(needAddAgent bool) error {
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		if needAddAgent {
			// 如果公会会长没有经纪人记录，则预先分配会长经纪人身份
			err := tx.Create(&guildagent.GuildAgent{GuildID: param.guildInfo.ID, AgentID: param.AgentID}).Error
			if err != nil {
				return err
			}
		}
		// 查询对应主播已分配的经纪人
		agentCreators, err := guildagent.FindAgentsByCreatorIDs(tx, param.CreatorIDs, param.guildInfo.ID)
		if err != nil {
			return err
		}
		if len(agentCreators) > 0 {
			// 移除对应主播已分配的经纪人
			_, err = guildagent.BatchUnassign(tx, util.SliceMap(agentCreators, func(v *guildagent.AgentCreator) int64 {
				return v.ID
			}))
			if err != nil {
				return err
			}
		}
		// 分配经纪人
		return guildagent.BatchAssign(tx, param.CreatorIDs, param.guildInfo.ID, param.AgentID)
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *operateAgentCreatorParam) unassignCreatorToAgent() (handler.ActionResponse, error) {
	// 取消分配经纪人仅支持操作单个主播
	if param.CreatorID <= 0 {
		return nil, actionerrors.ErrParams
	}
	creator, err := mowangskuser.FindByUserID(param.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if creator == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	guildID, err := livecontract.IsInGuild(creator.ID, param.guildInfo.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildID != param.guildInfo.ID {
		return nil, actionerrors.ErrCreatorNotInYourGuild
	}
	gac := new(guildagent.AgentCreator)
	err = guildagent.AgentCreator{}.DB().
		First(gac, "guild_id = ? AND creator_id = ? AND delete_time = 0",
			param.guildInfo.ID, creator.ID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrCreatorAgentNotFound
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	ok, err := guildagent.BatchUnassign(gac.DB(), []int64{gac.ID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrCreatorAgentNotFound
	}
	return "取消分配成功", nil
}
