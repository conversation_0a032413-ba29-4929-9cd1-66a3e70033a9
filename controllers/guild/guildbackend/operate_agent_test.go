package guildbackend

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildOperateAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 不会是公会会长
	c := handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 3000,
	})
	c.User().ID = 9876
	_, err := ActionGuildOperateAgent(c)
	require.EqualError(err, actionerrors.ErrYouAreNotGuildOwner.Message)

	g := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    c.User().ID,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	require.NoError(g.DB().Create(g).Error)
	defer func() {
		require.NoError(g.DB().Delete(nil, "id = ?", g.ID).Error)
	}()

	// 没有找到用户
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 3000,
	})
	c.User().ID = 9876
	_, err = ActionGuildOperateAgent(c)
	require.EqualError(err, actionerrors.ErrUserNotFound.Message)

	// 操作过于频繁
	lock := keys.LockGuildAgentOperate1.Format(g.ID)
	_, err = service.Redis.SetNX(lock, "1", time.Minute).Result()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 1,
	})
	c.User().ID = 9876
	_, err = ActionGuildOperateAgent(c)
	require.EqualError(err, "操作频繁，请稍后再试")
	require.NoError(service.Redis.Del(lock).Err())

	// 正常情况
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 1,
	})
	c.User().ID = 9876
	resp, err := ActionGuildOperateAgent(c)
	require.NoError(err)
	defer func() {
		require.NoError(guildagent.GuildAgent{}.DB().Delete(nil, "agent_id = ?", 1).Error)
	}()
	msg := resp.(string)
	assert.Equal("添加成功", msg)
}

func TestLoadOperateAgentParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var param operateAgentParam
	// 正常情况
	c := handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 3000,
	})
	err := param.load(c)
	require.NoError(err)
	assert.Equal(int64(3000), param.AgentID)
	assert.Equal(operateAgentTypeAdd, param.Type)
	assert.NotNil(param.operateFunc)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeRemove,
		"agent_id": 2000,
	})
	err = param.load(c)
	require.NoError(err)
	assert.Equal(int64(2000), param.AgentID)
	assert.Equal(operateAgentTypeRemove, param.Type)
	assert.NotNil(param.operateFunc)

	// 参数错误
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     3,
		"agent_id": 2000,
	})
	err = param.load(c)
	assert.EqualError(err, actionerrors.ErrParams.Message)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeRemove,
		"agent_id": -2000,
	})
	err = param.load(c)
	assert.EqualError(err, actionerrors.ErrParams.Message)
}

func TestAddAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 添加失败
	ctx := handler.NewTestContext(http.MethodPost, "/api/v2/guild/operate", true, map[string]interface{}{
		"type":     operateAgentTypeAdd,
		"agent_id": 4000,
	})
	_, err := addAgent(4000, 100, &guildagent.GuildAgent{
		GuildID: 100,
		AgentID: 4000,
	})
	require.EqualError(err, actionerrors.ErrYouAlreadyAddThisAgent.Message)

	_, err = addAgent(4000, 200, &guildagent.GuildAgent{
		GuildID: 100,
		AgentID: 4000,
	})
	require.EqualError(err, actionerrors.ErrAgentAlreadyJoinedOtherGuild.Message)

	// 正常情况
	ctx.User().ID = 88
	msg, err := addAgent(4000, 200, nil)
	defer func() {
		require.NoError(
			guildagent.GuildAgent{}.DB().
				Delete(nil, "guild_id = ? AND agent_id = ? AND delete_time = 0",
					200, 4000).Error,
		)
	}()
	require.NoError(err)
	assert.Equal("添加成功", msg)

	ga := new(guildagent.GuildAgent)
	err = ga.DB().
		Where("guild_id = ? AND agent_id = ? AND delete_time = 0", 200, 4000).
		First(ga).Error
	require.NoError(err)
	assert.NotNil(ga)
}

func TestRemoveAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 移除失败
	_, err := removeAgent(5000, 300, &guildagent.GuildAgent{
		GuildID: 200,
		AgentID: 5000,
	})
	require.EqualError(err, actionerrors.ErrGuildAgentNotFound.Message)

	// 正常情况
	ga := &guildagent.GuildAgent{
		GuildID: 300,
		AgentID: 5000,
	}
	require.NoError(guildagent.GuildAgent{}.DB().Create(ga).Error)
	gac := &guildagent.AgentCreator{
		GuildID:   ga.GuildID,
		AgentID:   ga.AgentID,
		CreatorID: 99,
	}
	require.NoError(gac.DB().Create(gac).Error)
	defer func() {
		require.NoError(ga.DB().Delete(nil, "guild_id = ? AND agent_id = ?", ga.GuildID, ga.AgentID).Error)
		require.NoError(gac.DB().Delete(nil, "guild_id = ? AND agent_id = ?", gac.GuildID, gac.AgentID).Error)
	}()

	msg, err := removeAgent(5000, 300, ga)
	require.NoError(err)
	assert.Equal("已成功移除", msg)

	ga2 := new(guildagent.GuildAgent)
	require.NoError(ga2.DB().First(ga2, "id = ?", ga.ID).Error)
	assert.NotEqual(0, ga2.DeleteTime)
	gac2 := new(guildagent.AgentCreator)
	require.NoError(gac2.DB().First(gac2, "id = ?", gac.ID).Error)
	assert.NotEqual(0, gac2.DeleteTime)
}

func TestIsInOtherGuild(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testGuildOwner := int64(987000)
	g := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	require.NoError(g.DB().Create(g).Error)
	defer func() {
		require.NoError(g.DB().Delete(nil, "id = ?", g.ID).Error)
	}()

	testCreatorID := int64(987001)
	contract := &livecontract.LiveContract{
		LiveID:           testCreatorID,
		GuildID:          g.ID,
		GuildName:        g.Name,
		GuildOwner:       g.UserID,
		ContractStart:    util.TimeNow().Add(-time.Minute).Unix(),
		ContractEnd:      util.TimeNow().Add(time.Minute).Unix(),
		ContractDuration: 120,
		Status:           livecontract.StatusContracting,
		Type:             livecontract.FromGuild,
	}
	require.NoError(contract.DB().Create(contract).Error)
	defer func() {
		contract.DB().Delete(nil, "id = ?", contract.ID)
	}()

	isIn, err := isInOtherGuild(g.UserID, g.ID+1)
	require.NoError(err)
	assert.True(isIn)

	isIn, err = isInOtherGuild(contract.LiveID, contract.GuildID+1)
	require.NoError(err)
	assert.False(isIn)

	isIn, err = isInOtherGuild(30000, g.ID)
	require.NoError(err)
	assert.False(isIn)
}
