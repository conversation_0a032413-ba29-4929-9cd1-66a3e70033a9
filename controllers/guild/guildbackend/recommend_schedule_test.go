package guildbackend

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/mysql/liveschedulerecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testCreatorID              = int64(12)
	testRoomID                 = int64(18113499)
	testExistApplyID           = int64(1)
	testApplyCountLimitGuildID = int64(3)
)

func TestBackpackTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(recommendScheduleApplyParam{}, "creator_id", "cover_url", "start_time", "end_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(recommendScheduleApplyParam{}, "creator_id", "cover_url", "start_time", "end_time")
}

func TestRecommendScheduleApplyParamCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := new(recommendScheduleApplyParam)
	err := p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": -1,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": -1,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   0,
	}))
	assert.EqualError(err, "结束时间不得早于开始时间")

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   3000,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   86401,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": 9999,
		"start_time": 0,
		"end_time":   86400,
	}))
	assert.EqualError(err, "您没有权限操作该主播")

	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   86400,
		"cover_url":  "",
	}))
	assert.NoError(err)

	require.NoError(guildrecommendblocklist.Add(guildrecommendblocklist.TypeElementRecommendLive, testRoomID))
	p = new(recommendScheduleApplyParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   86400,
		"cover_url":  "https://fm.example.com/testdata/test.jpg",
	}))
	assert.EqualError(err, "该主播目前不可申请")

	require.NoError(guildrecommendblocklist.Remove(guildrecommendblocklist.TypeElementRecommendLive, testRoomID))
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   86400,
		"cover_url":  "https://fm.example.com/testdata/test.jpg",
	}))
	assert.Nil(err)

	cleanTestDataFunc, err := createGuildApplyCountLimitTestData()
	require.NoError(err)
	defer cleanTestDataFunc()
	p = new(recommendScheduleApplyParam)
	ctx := handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"creator_id": testCreatorID,
		"start_time": 0,
		"end_time":   86400,
		"cover_url":  "https://fm.example.com/testdata/test.jpg",
	})
	err = p.check(ctx)
	assert.EqualError(err, "已达首页推荐位推荐池上限")
}

func createGuildApplyCountLimitTestData() (func(), error) {
	applys := make([]*guildscheduleapply.LiveGuildScheduleApply, guildscheduleapply.GuildApplyCountLimit)
	applysIDs := make([]int64, guildscheduleapply.GuildApplyCountLimit)
	for i := 0; i < len(applys); i++ {
		applysIDs[i] = int64(1000 + i)
		applys[i] = &guildscheduleapply.LiveGuildScheduleApply{
			ID:         applysIDs[i],
			Status:     1,
			DeleteTime: 0,
			GuildID:    testApplyCountLimitGuildID,
		}
	}
	err := servicedb.SplitBatchInsert(service.LiveDB, guildscheduleapply.TableName(), applys, guildscheduleapply.GuildApplyCountLimit/5, false)
	return func() {
		guildscheduleapply.LiveGuildScheduleApply{}.DB().Table(guildscheduleapply.TableName()).Delete("", "id IN (?)", applysIDs)
	}, err
}

func TestRecommendScheduleApplyParamAddApply(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.FindOne(bson.M{"creator_id": testCreatorID}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	param := recommendScheduleApplyParam{
		CreatorID: testCreatorID,
		StartTime: 0,
		EndTime:   1800,
		r:         r,
		CoverURL:  "https://fm.example.com/testdata/test.jpg",
		contract: &livecontract.LiveContract{
			GuildID:    3,
			GuildOwner: 12,
			GuildName:  "test",
		},
	}
	result, err := param.addApply()
	require.NoError(err)
	require.NotNil(result)
	assert.Equal(guildscheduleapply.StatusReviewing, result.Status)

	param.CoverURL = ""
	result, err = param.addApply()
	require.NoError(err)
	require.NotNil(result)
	assert.Equal(guildscheduleapply.StatusPassed, result.Status)
}

func TestActionRecommendScheduleApplyAdd(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err := ActionRecommendScheduleApplyAdd(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestScheduleApplyEditParamCheck(t *testing.T) {
	assert := assert.New(t)

	p := new(scheduleApplyEditParam)
	err := p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id": -1,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(scheduleApplyEditParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id":   testExistApplyID,
		"start_time": -1,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(scheduleApplyEditParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id":   testExistApplyID,
		"start_time": 0,
		"end_time":   -1,
	}))
	assert.EqualError(err, "结束时间不得早于开始时间")

	p = new(scheduleApplyEditParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id":   testExistApplyID,
		"start_time": 0,
		"end_time":   86401,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(scheduleApplyEditParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id":   99999,
		"start_time": 0,
		"end_time":   86400,
	}))
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	// 测试不传推荐图不报错
	p = new(scheduleApplyEditParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id":   3,
		"start_time": 0,
		"end_time":   86400,
	}))
	assert.NoError(err)
}

func TestScheduleApplyEditParamEdit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	apply, err := guildscheduleapply.FindApplyByID(testExistApplyID)
	require.NoError(err)
	param := scheduleApplyEditParam{
		ApplyID:   testExistApplyID,
		StartTime: 0,
		EndTime:   3600,
		apply:     apply,
	}
	require.NoError(param.editApply())
	newApply, err := guildscheduleapply.FindApplyByID(testExistApplyID)
	require.NoError(err)
	require.NoError(apply.AfterFind())
	assert.Equal(apply, newApply)
	assert.False(newApply.CoverReviewing)

	// 修改图片
	cover := fmt.Sprintf("oss://testdata/test_%d.jpg", goutil.TimeNow().Unix())
	param = scheduleApplyEditParam{
		ApplyID:        testExistApplyID,
		StartTime:      0,
		EndTime:        4800,
		uploadCoverURL: cover,
		apply:          apply,
	}
	require.NoError(param.editApply())
	newApply, err = guildscheduleapply.FindApplyByID(testExistApplyID)
	require.NoError(err)
	assert.Equal(apply.Status, newApply.Status)
	assert.True(apply.CoverReviewing)
	assert.NotEqual(cover, newApply.Cover)
}

func TestActionRecommendScheduleApplyEdit(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err := ActionRecommendScheduleApplyEdit(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestScheduleApplyResetParamCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := new(scheduleApplyResetParam)
	err := p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id": -1,
	}))
	assert.Equal(actionerrors.ErrParams, err)

	p = new(scheduleApplyResetParam)
	err = p.check(handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id": testExistApplyID,
		"type":     TypeSetPause,
	}))
	assert.EqualError(err, "您没有权限操作该主播")

	// 校验重新推荐公会数量是否大于推荐上限
	apply, _ := guildscheduleapply.FindApplyByID(testExistApplyID)
	cid := apply.CreatorID
	testContractStart := int64(23333333)
	require.NoError(livecontract.LiveContract{}.DB().Create(livecontract.LiveContract{
		ID:            222333,
		GuildID:       222333,
		LiveID:        1,
		GuildOwner:    20,
		Status:        livecontract.StatusContracting,
		ContractStart: testContractStart,
		ContractEnd:   goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:     "测试 GetGuildID 方法",
	}).Error)
	defer livecontract.LiveContract{}.DB().Delete("", "id = ?", 222333)
	// update => creator_id: 1
	// because FindAssociatedContractWithAgent(1, 20) == true
	err = guildscheduleapply.LiveGuildScheduleApply{}.DB().Where("id=?", testExistApplyID).Update("creator_id", 1).Error
	defer func() {
		err := guildscheduleapply.LiveGuildScheduleApply{}.DB().Where("id=?", testExistApplyID).Update("creator_id", cid).Error
		assert.NoError(err)
	}()
	assert.NoError(err)
	// create LiveGuildScheduleApply{} 500 records
	cleanTestDataFunc, err := createGuildApplyCountLimitTestData()
	require.NoError(err)
	defer cleanTestDataFunc()

	p = new(scheduleApplyResetParam)
	ctx := handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"apply_id": testExistApplyID,
		"type":     TypeSetRestart,
	})
	// update => current user id: 20
	// because FindAssociatedContractWithAgent(1, 20) == true
	ctx.User().ID = 20
	err = p.check(ctx)
	assert.EqualError(err, "已达首页推荐位推荐池上限")
}

func TestScheduleApplyResetParamSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testPassApplyID := int64(3)
	param := scheduleApplyResetParam{
		ApplyID: testPassApplyID,
		Type:    -1,
	}
	_, err := param.set()
	assert.Equal(actionerrors.ErrParams, err)

	param = scheduleApplyResetParam{
		ApplyID: testPassApplyID,
		Type:    TypeSetRestart,
	}
	_, err = param.set()
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	param = scheduleApplyResetParam{
		ApplyID: testPassApplyID,
		Type:    TypeSetPause,
	}
	result, err := param.set()
	require.NoError(err)
	assert.Equal("停止推荐成功", result)

	param = scheduleApplyResetParam{
		ApplyID: testPassApplyID,
		Type:    TypeSetRestart,
	}
	result, err = param.set()
	require.NoError(err)
	assert.Equal("重新推荐成功", result)
}

func TestActionRecommendScheduleApplyReset(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err := ActionRecommendScheduleApplySet(c)
	assert.Equal(actionerrors.ErrParams, err)
}

var (
	testContractGuildID   = int64(3)
	testContractCreatorID = int64(12345)
)

func addTestGuildContract() error {
	now := goutil.TimeNow()
	contract := &livecontract.LiveContract{
		GuildID:          testContractGuildID,
		GuildOwner:       12,
		LiveID:           testContractCreatorID,
		ContractDuration: 120,
		ContractStart:    now.Add(-time.Minute).Unix(),
		ContractEnd:      now.Add(+time.Minute).Unix(),
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
	}
	return livecontract.LiveContract{}.DB().Create(contract).Error
}

func clearTestGuildContract() error {
	return livecontract.LiveContract{}.DB().Delete(nil, "guild_id = ? AND live_id = ?",
		testContractGuildID, testContractCreatorID).Error
}

func TestActionRecommendScheduleApply(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?apply_id=-1", true, nil)
	_, err := ActionRecommendScheduleApply(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/?apply_id=999999", true, nil)
	_, err = ActionRecommendScheduleApply(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/?apply_id=%d", testExistApplyID), true, nil)
	_, err = ActionRecommendScheduleApply(c)
	assert.EqualError(err, "您没有权限查看该申请")

	require.NoError(addTestGuildContract())
	defer func() {
		assert.NoError(clearTestGuildContract())
	}()
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/?apply_id=%d", testExistApplyID), true, nil)
	result, err := ActionRecommendScheduleApply(c)
	require.NoError(err)
	apply := result.(*guildscheduleapply.LiveGuildScheduleApply)
	assert.Equal(testExistApplyID, apply.ID)
	assert.NotZero(apply.EndTime)
}

func TestActionRecommendScheduleApplyList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?creator_id=-1", true, nil)
	_, err := ActionRecommendScheduleApplyList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/?type=-1", true, nil)
	_, err = ActionRecommendScheduleApplyList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	c.User().ID = 10
	_, err = ActionRecommendScheduleApplyList(c)
	assert.Equal(actionerrors.ErrForbidden, err)

	require.NoError(addTestGuildContract())
	defer func() {
		assert.NoError(clearTestGuildContract())
	}()
	c = handler.NewTestContext(http.MethodGet, "/?type=1", true, nil)
	result, err := ActionRecommendScheduleApplyList(c)
	require.NoError(err)
	resp := result.(*scheduleApplyLisResp)
	assert.NotEmpty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/?creator_id=999999", true, nil)
	result, err = ActionRecommendScheduleApplyList(c)
	require.NoError(err)
	resp = result.(*scheduleApplyLisResp)
	assert.Empty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/?creator_username=dddd", true, nil)
	result, err = ActionRecommendScheduleApplyList(c)
	require.NoError(err)
	resp = result.(*scheduleApplyLisResp)
	assert.Empty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/?type=4", true, nil)
	result, err = ActionRecommendScheduleApplyList(c)
	require.NoError(err)
	resp = result.(*scheduleApplyLisResp)
	assert.Empty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/?type=1&creator_username=dddd&creator_id=999999", true, nil)
	result, err = ActionRecommendScheduleApplyList(c)
	require.NoError(err)
	resp = result.(*scheduleApplyLisResp)
	assert.Empty(resp.Data)
}

func TestActionRecommendScheduleList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildOwner := int64(998877)
	now := time.Date(2021, 6, 9, 20, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	testGuild := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    guildOwner,
		ApplyTime:                 now.Unix(),
	}
	require.NoError(guild.Guild{}.DB().Create(testGuild).Error)
	defer func() {
		assert.NoError(guild.Guild{}.DB().Delete(nil, "id = ?", testGuild.ID).Error)
	}()

	testCreatorID := int64(********)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().DeleteOne(ctx, bson.M{"creator_id": testCreatorID})
	assert.NoError(err)
	r := room.Room{
		Helper: room.Helper{
			RoomID:          ********,
			NameClean:       "********",
			CreatorID:       testCreatorID,
			CreatorUsername: "test_1",
		},
	}
	_, err = room.Collection().InsertOne(ctx, r)
	require.NoError(err)

	agentOneID := int64(**********)
	agentTwoID := int64(**********)
	require.NoError(servicedb.BatchInsert(service.LiveDB, guildagent.GuildAgent{}.TableName(), []*guildagent.GuildAgent{
		{
			GuildID: testGuild.ID,
			AgentID: agentOneID,
		},
		{
			GuildID: testGuild.ID,
			AgentID: agentTwoID,
		},
	}))
	defer func() {
		assert.NoError(guildagent.GuildAgent{}.DB().Delete(nil, "guild_id = ?", testGuild.ID).Error)
	}()

	agentCreator := guildagent.AgentCreator{
		GuildID:   testGuild.ID,
		AgentID:   agentOneID,
		CreatorID: testCreatorID,
	}
	require.NoError(guildagent.AgentCreator{}.DB().Create(&agentCreator).Error)
	defer func() {
		assert.NoError(guildagent.AgentCreator{}.DB().Delete(nil, "id = ?", agentCreator.ID).Error)
	}()

	contract := &livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testCreatorID,
		ContractDuration: 120,
		ContractStart:    goutil.TimeNow().Add(-time.Minute).Unix(),
		ContractEnd:      goutil.TimeNow().Add(+time.Minute).Unix(),
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
	}
	require.NoError(livecontract.LiveContract{}.DB().Create(contract).Error)
	defer func() {
		assert.NoError(livecontract.LiveContract{}.DB().Delete(nil, "id = ?", contract.ID).Error)
	}()

	beginNow := goutil.BeginningOfDay(now)
	recommend := &liveschedulerecord.ScheduleRecord{
		GuildID:    testGuild.ID,
		RoomID:     r.RoomID,
		CreatorID:  r.CreatorID,
		Day:        beginNow.Unix(),
		StartTime:  beginNow.Unix(),
		ExpireTime: beginNow.Add(30 * time.Minute).Unix(),
	}
	require.NoError(liveschedulerecord.ScheduleRecord{}.DB().Create(recommend).Error)
	defer func() {
		assert.NoError(liveschedulerecord.ScheduleRecord{}.DB().Delete(nil, "room_id = ?", r.RoomID).Error)
	}()

	c := handler.NewTestContext(http.MethodGet, "/?date="+now.AddDate(0, 0, -7).Format(util.TimeFormatYMD), true, nil)
	c.User().ID = guildOwner
	res, err := ActionRecommendScheduleList(c)
	require.NoError(err)
	assert.Nil(res)

	c = handler.NewTestContext(http.MethodGet, "/?date="+now.AddDate(0, 0, 2).Format(util.TimeFormatYMD), true, nil)
	c.User().ID = guildOwner
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	assert.Nil(res)

	// 公会会长
	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	c.User().ID = guildOwner
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	result := res.([]*liveScheduleResult)
	require.Len(result, 1)
	assert.Len(result[0].Data, 1)

	// 分配到测试主播主播的公会经纪人
	c = handler.NewTestContext(http.MethodGet, "/?date="+now.Format(util.TimeFormatYMD), true, nil)
	c.User().ID = agentOneID
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	require.Len(res, 1)
	assert.Len(result[0].Data, 1)

	// 当天无数据
	c = handler.NewTestContext(http.MethodGet, "/?date="+now.AddDate(0, 0, -1).Format(util.TimeFormatYMD), true, nil)
	c.User().ID = agentOneID
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	assert.Empty(res)

	// 未分配到测试主播的公会经纪人
	c = handler.NewTestContext(http.MethodGet, "/?date="+now.Format(util.TimeFormatYMD), true, nil)
	c.User().ID = agentTwoID
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	assert.Empty(res)

	// 未到公布时间
	goutil.SetTimeNow(func() time.Time {
		return now.AddDate(0, 0, -1).Add(-time.Minute)
	})
	c = handler.NewTestContext(http.MethodGet, "/?date="+now.Format(util.TimeFormatYMD), true, nil)
	c.User().ID = agentOneID
	res, err = ActionRecommendScheduleList(c)
	require.NoError(err)
	assert.Nil(res)
}
