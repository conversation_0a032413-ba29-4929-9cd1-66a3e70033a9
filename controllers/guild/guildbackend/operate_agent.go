package guildbackend

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 公会经纪人操作类型
const (
	operateAgentTypeAdd int = iota
	operateAgentTypeRemove
)

type operateAgentParam struct {
	Type    int   `json:"type"`
	AgentID int64 `json:"agent_id"`

	operateFunc func(agentID, guildID int64, ga *guildagent.GuildAgent) (msg string, err error)
}

// ActionGuildOperateAgent 公会添加或移除经纪人
/**
 * @api {post} /api/v2/guild/agent/operate 公会添加或移除经纪人
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1} [type=0] 操作类型：0 添加，1 移除
 * @apiParam {Number} agent_id 经纪人 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "添加成功"
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 操作频繁，请稍后再试
 *
 * @apiError (404) {Number} code 500050019
 * @apiError (404) {String} info 您需要成为公会长才能操作
 *
 * @apiError (404) {Number} code 500050020
 * @apiError (404) {String} info 此经纪人已加入其他公会
 *
 * @apiError (404) {Number} code 500050020
 * @apiError (404) {String} info 此经纪人已在您的公会中
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionGuildOperateAgent(ctx *handler.Context) (handler.ActionResponse, error) {
	var param operateAgentParam
	if err := param.load(ctx); err != nil {
		return nil, actionerrors.ErrParams
	}
	guildInfo, err := guildrole.IsGuildOwner(ctx.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil {
		return nil, actionerrors.ErrYouAreNotGuildOwner
	}

	agent, err := mowangskuser.FindByUserID(param.AgentID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if agent == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	lock := keys.LockGuildAgentOperate1.Format(guildInfo.ID)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)

	ga := new(guildagent.GuildAgent)
	err = guildagent.GuildAgent{}.DB().
		First(ga, "agent_id = ? AND delete_time = 0", agent.ID).Error
	if err != nil {
		if !servicedb.IsErrNoRows(err) {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		ga = nil
	}

	return param.operateFunc(param.AgentID, guildInfo.ID, ga)
}

func (param *operateAgentParam) load(c *handler.Context) error {
	if err := c.BindJSON(&param); err != nil {
		return actionerrors.ErrParams
	}
	if param.AgentID <= 0 {
		return actionerrors.ErrParams
	}
	switch param.Type {
	case operateAgentTypeAdd:
		param.operateFunc = addAgent
	case operateAgentTypeRemove:
		param.operateFunc = removeAgent
	default:
		return actionerrors.ErrParams
	}

	return nil
}

// addAgent 添加经纪人
func addAgent(agentID, guildID int64, ga *guildagent.GuildAgent) (msg string, err error) {
	if ga != nil {
		if ga.GuildID == guildID {
			return "", actionerrors.ErrYouAlreadyAddThisAgent
		}
		return "", actionerrors.ErrAgentAlreadyJoinedOtherGuild
	}

	isIn, err := isInOtherGuild(agentID, guildID)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if isIn {
		return "", actionerrors.ErrUserAlreadyJoinedOtherGuild
	}

	err = guildagent.GuildAgent{}.DB().Create(&guildagent.GuildAgent{
		GuildID: guildID,
		AgentID: agentID,
	}).Error
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	return "添加成功", nil
}

// removeAgent 移除经纪人
func removeAgent(agentID, guildID int64, ga *guildagent.GuildAgent) (msg string, err error) {
	if ga == nil || ga.GuildID != guildID {
		return "", actionerrors.ErrGuildAgentNotFound
	}
	err = servicedb.Tx(ga.DB(), func(tx *gorm.DB) error {
		db := tx.Table(ga.TableName()).
			Where("id = ? AND delete_time = 0", ga.ID).
			Updates(map[string]interface{}{
				"delete_time":   util.TimeNow().Unix(),
				"modified_time": util.TimeNow().Unix(),
			})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return actionerrors.ErrAgentNotFound
		}
		err = tx.Table(guildagent.AgentCreator{}.TableName()).
			Where("agent_id = ? AND guild_id = ? AND delete_time = 0", ga.AgentID, ga.GuildID).
			Updates(map[string]interface{}{
				"delete_time":   util.TimeNow().Unix(),
				"modified_time": util.TimeNow().Unix(),
			}).Error

		return err
	})
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	return "已成功移除", nil
}

func isInOtherGuild(userID, guildID int64) (bool, error) {
	// 是否是其它公会的公会会长
	var guildInfo guild.Guild
	err := guild.Guild{}.DB().Find(&guildInfo, "user_id = ? AND checked = ? AND id <> ?",
		userID, guild.CheckedPass, guildID).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return false, err
	}
	if guildInfo.ID != 0 {
		return true, nil
	}

	return false, nil
}
