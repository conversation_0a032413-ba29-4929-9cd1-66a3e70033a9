package guildbackend

import (
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildbanner"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type bannerListParam struct {
	status     int
	guildID    int64
	creatorIDs []int64

	p        int64
	pageSize int64
	resp     bannerListResp
}

type bannerListResp struct {
	Data       []*guildbanner.GuildRecommendBanner `json:"data"`
	Pagination goutil.Pagination                   `json:"pagination"`
}

// ActionBannerList 列出申请的 banner 的记录
/**
 * @api {get} /api/v2/guild/recommend/banner/list 列出申请的 banner 的记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播名称
 * @apiParam {number=0,1,2,3} [status] 申请状态，0：待审核，1：已过审，2：未过审，3：已请假
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 10232,
 *           "room_id": "162883684",
 *           "title": "原创歌曲推荐",
 *           "image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "creator_id": 10,
 *           "creator_username": "bless",
 *           "creator_iconurl": "http://static.missevan.com/icon/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "start_time": 1584806400,
 *           "end_time": 1584808200,
 *           "create_time": 1584808200,
 *           "status": 0, // 0：待审核，1：已过审，2：未过审，3：已请假
 *           "reason": "违规词",
 *           "show_absence": 0 // 按位判断，第一位为请假按钮状态 1：显示；0：不显示
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 */
func ActionBannerList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newBannerListParam(c)
	if err != nil {
		return nil, err
	}
	if err = param.bannerList(); err != nil {
		return nil, err
	}
	return param.resp, nil
}

func newBannerListParam(c *handler.Context) (*bannerListParam, error) {
	var err error
	param := new(bannerListParam)
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.status, err = c.GetDefaultParamInt("status", guildbanner.StatusFindAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	creatorUsername, _ := c.GetParam("creator_username")
	creatorID, _ := c.GetParamInt64("creator_id")
	if creatorID < 0 {
		return nil, actionerrors.ErrParams
	}

	managerRole, err := guildrole.FindManagerRole(c.UserID(), true, guildrole.Options{
		GuildOwnerWithContracted: true,
	})
	if err != nil {
		return nil, err
	}
	if managerRole == nil {
		return nil, actionerrors.ErrNoAuthority
	}
	param.creatorIDs, err = mowangskuser.SearchUserIDs(creatorID, creatorUsername, managerRole.CreatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.guildID = managerRole.GuildInfo.ID
	return param, nil
}

func (param *bannerListParam) bannerList() error {
	var err error
	param.resp.Data, param.resp.Pagination, err = guildbanner.FindApplyList(
		param.guildID, param.creatorIDs, param.status, param.p, param.pageSize)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type bannerAddParam struct {
	CreatorID int64                 `form:"creator_id" json:"creator_id"`
	Title     string                `form:"title" json:"title"`
	ImageURL  upload.SourceURL      `form:"image_url" json:"image_url"`
	Periods   []bannerRequestPeriod `form:"periods" json:"periods"`

	uploadImageURL string
	userID         int64
	roomID         int64
	guildID        int64
}

type bannerRequestPeriod struct {
	StartTime int64 `form:"start_time" json:"start_time"`
	endTime   int64
}

// ActionBannerAdd 添加 banner 申请
/**
 * @api {post} /api/v2/guild/recommend/banner/add 添加 banner 申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {String} title banner 标题
 * @apiParam {String} image_url Web banner 图链接地址
 * @apiParam {Object[]} periods 排期的时间区间
 * @apiParam {Number} periods.start_time 排期的开始时间
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionBannerAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newBannerAddParam(c)
	if err != nil {
		return nil, err
	}
	if err = param.bannerAdd(); err != nil {
		return nil, err
	}
	return "success", nil
}

func newBannerAddParam(c *handler.Context) (*bannerAddParam, error) {
	now := goutil.TimeNow()
	// 每周一 16 点 - 每周五 24 点才能申请次周 banner 位
	beginningOfWeek := util.BeginningOfWeek(now)
	startTime := beginningOfWeek.Add(16 * time.Hour).Unix()
	endTime := beginningOfWeek.AddDate(0, 0, 5).Unix()
	if now.Unix() < startTime || endTime <= now.Unix() {
		return nil, actionerrors.NewErrForbidden("不在申请时间范围内")
	}

	param := new(bannerAddParam)
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.Title = strings.TrimSpace(param.Title)
	if param.CreatorID == 0 || param.Title == "" || param.ImageURL == "" || len(param.Periods) == 0 {
		return nil, actionerrors.ErrParams
	}
	role, g, err := guildrole.FindAssociatedContractWithAgent(param.CreatorID, c.UserID())
	if err != nil {
		return nil, err
	}
	if g == nil {
		return nil, actionerrors.ErrCreatorNotInYourGuild
	}
	if !role.IsGuildManager() {
		return nil, actionerrors.NewErrForbidden("您没有权限查看该申请")
	}

	endOfWeek := util.EndOfWeek(now)
	for i, v := range param.Periods {
		if v.StartTime < endOfWeek.Unix() {
			return nil, actionerrors.ErrParamsMsg("存在过期时间段")
		}
		if v.StartTime%util.SecondOneHour != 0 {
			return nil, actionerrors.ErrParams
		}
		param.Periods[i].endTime = v.StartTime + util.SecondOneHour
	}
	exists, err := guildbanner.CheckCreatorCreateTime(param.CreatorID, beginningOfWeek, endOfWeek)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.NewErrForbidden("本周该主播已无申请次数")
	}
	checkRes, err := goclient.CheckText(c, param.Title, scan.SceneIntro)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !checkRes.Pass {
		return nil, actionerrors.ErrParamsMsg("您申请的标题名称含有违禁词，请重新输入")
	}
	if ok := storage.CheckStorage(param.ImageURL); !ok {
		return nil, actionerrors.ErrParamsMsg("请求上传的资源不被支持，请重新选择")
	}
	param.roomID, err = room.FindRoomID(param.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.roomID == 0 {
		return nil, actionerrors.ErrParamsMsg("找不到房间")
	}
	exists, err = guildrecommendblocklist.ExistsAny(param.roomID, g.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		// 主播在资源位黑名单中
		return nil, actionerrors.NewErrLiveForbidden("该主播目前不可申请")
	}
	param.guildID = g.GuildID
	param.userID = c.UserID()
	return param, nil
}

func (param bannerAddParam) bannerAdd() error {
	var err error
	param.uploadImageURL, err = storage.UploadToOSS(param.ImageURL, storage.PathPrefixBanner)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	countMap, err := guildbanner.PeriodPendingCount(nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	g := guildbanner.GuildRecommendBanner{
		RoomID:     param.roomID,
		GuildID:    param.guildID,
		CreatorID:  param.CreatorID,
		OperatorID: param.userID,
		Title:      param.Title,
		ImageURL:   param.uploadImageURL,
		Status:     guildbanner.StatusPending,
	}
	err = servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		for _, period := range param.Periods {
			count := countMap[period.StartTime].Count
			bannerElementsCount, err := liverecommendedelements.BannerPeriodCount(period.StartTime, period.endTime)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, logger.Fields{
					"start_time": period.StartTime,
					"end_time":   period.endTime,
				})
			}
			// 待审核的数量加上超管创建和审核通过的 banner 数量是否超过最大数量
			if count+bannerElementsCount >= guildbanner.BannerMaxCount {
				return actionerrors.NewErrForbidden("当前时段已满，请重新选择时间段")
			}
			g.StartTime = period.StartTime
			g.EndTime = period.endTime
			err = g.Create(tx)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, logger.Fields{
					"creator_id": g.CreatorID,
					"start_time": g.StartTime,
					"end_time":   g.EndTime,
				})
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

type bannerVacancyResp struct {
	Date    string          `json:"date"`
	Periods []vacancyPeriod `json:"periods"`
}

type vacancyPeriod struct {
	Period    string `json:"period"`
	StartTime int64  `json:"start_time"`
	Vacancy   int64  `json:"vacancy"`
}

// ActionBannerVacancy 获取可申请的 banner 的余量
/**
 * @api {get} /api/v2/guild/recommend/banner/vacancy 获取可申请的 banner 的余量
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "date": "2021-05-13",
 *         "periods": [
 *           {
 *             "period": "00:00 - 01:00",
 *             "start_time": 1625414400,
 *             "vacancy": 0 // 当前时间段剩余 banner 数量
 *           },
 *           {
 *             "period": "01:00 - 02:00",
 *             "start_time": 1625418000,
 *             "vacancy": 0 // 当前时间段剩余 banner 数量
 *           }
 *         ]
 *       },
 *       {
 *         "date": "2021-05-14",
 *         "periods": [
 *           {
 *             "period": "00:00 - 01:00",
 *             "start_time": 1625414400,
 *             "vacancy": 0 // 当前时间段剩余 banner 数量
 *           },
 *           {
 *             "period": "01:00 - 02:00",
 *             "start_time": 1625418000,
 *             "vacancy": 0 // 当前时间段剩余 banner 数量
 *           }
 *         ]
 *       }
 *     ]
 *   }
 */
func ActionBannerVacancy(*handler.Context) (handler.ActionResponse, error) {
	startTime := util.EndOfWeek(goutil.TimeNow())
	endTime := startTime.AddDate(0, 0, 7)
	countMap, err := guildbanner.PeriodPendingCount(nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	adminBannerCountArray, err := bannerElementCountArray(startTime, endTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var resp []bannerVacancyResp
	// 遍历下周的日期
	for d, adminCountIndex := startTime, 0; d.Before(endTime); d = d.AddDate(0, 0, 1) {
		var periods []vacancyPeriod
		// 遍历每天的 banner 时段，间隔一小时
		for h := d; h.Before(d.AddDate(0, 0, 1)); h = h.Add(time.Hour) {
			recommendCount := countMap[h.Unix()].Count
			count := guildbanner.BannerMaxCount - recommendCount - adminBannerCountArray[adminCountIndex]
			if count < 0 {
				count = 0
			}
			adminCountIndex++
			startHour := h.Hour()
			periods = append(periods, vacancyPeriod{
				Period:    fmt.Sprintf("%02d:00 - %02d:00", startHour, startHour+1),
				StartTime: h.Unix(),
				Vacancy:   count,
			})
		}
		dayFormat := d.Format(goutil.TimeFormatYMD)
		resp = append(resp, bannerVacancyResp{
			Date:    dayFormat,
			Periods: periods,
		})
	}
	return resp, nil
}

// 根据下标表示审核通过的数量
func bannerElementCountArray(startTime, endTime time.Time) ([]int64, error) {
	bannerElements, err := liverecommendedelements.PeriodBanner(startTime, endTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	weekCount := make([]int64, 7*24)
	for _, element := range bannerElements {
		startIndex := (*element.StartTime - startTime.Unix()) / util.SecondOneHour
		if startIndex < 0 {
			startIndex = 0
		}
		// ExpireTime 是开区间，这里转为闭区间计算，需要 -1
		endIndex := ((element.ExpireTime - 1) - startTime.Unix()) / util.SecondOneHour
		if endIndex >= int64(len(weekCount)) {
			endIndex = int64(len(weekCount) - 1)
		}
		for i := startIndex; i <= endIndex; i++ {
			weekCount[i]++
		}
	}
	return weekCount, nil
}

// ActionBannerAbsenceRequest banner 请假申请
/**
 * @api {post} /api/v2/guild/recommend/banner/absence/request banner 请假申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} banner_id banner 列表返回的 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionBannerAbsenceRequest(*handler.Context) (handler.ActionResponse, error) {
	return nil, nil
}
