package guildbackend

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

type agentListParam struct {
	searchAgentParam

	sort string
}

type agentListItem struct {
	mowangskuser.Simple

	// 覆盖 mowangskuser.Simple 中的 ID
	ID         int64 `gorm:"column:user_id" json:"user_id"`
	CreatorNum int   `gorm:"column:creator_num" json:"creator_num"`
}

type agentListResp struct {
	Data       []*agentListItem `json:"data"`
	Pagination util.Pagination  `json:"pagination"`
}

var agentListSortMapSupported = map[string]bool{
	"user_id":          true,
	"user_id.asc":      true,
	"user_id.desc":     true,
	"creator_num":      true,
	"creator_num.asc":  true,
	"creator_num.desc": true,
}

// ActionGuildAgentList 公会获取经纪人列表
/**
 * @api {get} /api/v2/guild/agent/list 公会获取经纪人列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {String} [search_word] 经纪人 ID 或昵称
 * @apiParam {String} [sort=user_id.asc] 排序方式（支持 user_id 与 creator_num 的排序）
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [{
 *           "user_id": 43389,  // 经纪人用户 ID
 *           "username": "AAA",  // 经纪人昵称
 *           "iconurl": "http://foo.com/bar.png",  // 经纪人头像
 *           "creator_num": 4  // 经纪人管理的主播数
 *         }]
 *         "pagination": {
 *           "count": 3,
 *           "p": 1,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *       }
 *     }
 */
func ActionGuildAgentList(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(agentListParam)
	if err := param.load(ctx); err != nil {
		return nil, err
	}
	return guildAgentList(ctx, param)
}

func (param *agentListParam) load(c *handler.Context) error {
	var err error
	if err = param.searchAgentParam.load(c); err != nil {
		return err
	}
	sortStr := c.GetDefaultParamString("sort", "user_id.asc")
	orderBy, order, ok := utils.ParseSortStr(sortStr, agentListSortMapSupported)
	if !ok {
		return actionerrors.ErrParams
	}
	param.sort = orderBy + " " + order

	return nil
}

func guildAgentList(ctx *handler.Context, param *agentListParam, moreCondition ...func(ctx *handler.Context, db *gorm.DB) *gorm.DB) (*agentListResp, error) {
	g, err := guildrole.IsGuildOwner(ctx.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrYouAreNotGuildOwner
	}

	var count int64
	resp := new(agentListResp)
	db := guildagent.GuildAgent{}.DB().
		Table(guildagent.GuildAgent{}.TableName()+" AS ga").
		Select("ga.agent_id AS user_id, COUNT(gac.creator_id) AS creator_num").
		Joins(fmt.Sprintf("LEFT JOIN %s AS gac ON gac.agent_id = ga.agent_id AND gac.delete_time = 0",
			guildagent.AgentCreator{}.TableName())).
		Where("ga.guild_id = ? AND ga.delete_time = 0", g.ID).
		Group("ga.agent_id").
		Order(param.sort)
	for _, cond := range moreCondition {
		db = cond(ctx, db)
	}

	if db, err = param.applySearch(db, g.ID); err != nil || db == nil {
		if db == nil {
			resp.Pagination = util.MakePagination(0, param.Page, param.PageSize)
		}

		return resp, err
	}

	if err = db.Count(&count).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Pagination = util.MakePagination(count, param.Page, param.PageSize)
	if !resp.Pagination.Valid() {
		return resp, nil
	}
	if err = resp.Pagination.ApplyTo(db).Find(&resp.Data).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err = fillAgentUserInfo(resp.Data); err != nil {
		return nil, err
	}

	return resp, nil
}

func (param *agentListParam) applySearch(db *gorm.DB, guildID int64) (*gorm.DB, error) {
	if param.SearchWord == "" {
		return db, nil
	}

	if param.isSearchWordInteger {
		db = db.Where("ga.agent_id = ?", param.searchWordInteger)
		return db, nil
	}

	var allAgentIDs []int64
	err := guildagent.GuildAgent{}.DB().
		Select("agent_id").
		Where("guild_id = ? AND delete_time = 0", guildID).
		Pluck("agent_id", &allAgentIDs).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	agentIDs, err := mowangskuser.SearchUserByUsername(param.SearchWord, allAgentIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(agentIDs) == 0 {
		return nil, nil
	}
	db = db.Where("ga.agent_id IN (?)", agentIDs)

	return db, nil
}

func fillAgentUserInfo(dataList []*agentListItem) error {
	if len(dataList) == 0 {
		return nil
	}
	userIDs := make([]int64, len(dataList))
	for i, item := range dataList {
		userIDs[i] = item.ID
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range dataList {
		user := users[dataList[i].ID]
		if user == nil {
			logger.WithField("user_id", dataList[i].ID).Error("guild agent is not found")
			continue
		}
		dataList[i].Username = user.Username
		dataList[i].IconURL = user.IconURL
	}

	return nil
}
