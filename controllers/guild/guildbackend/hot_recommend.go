package guildbackend

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/guild/guildutil"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// EffectiveDuration 设置推荐成功后的默认生效时长
	EffectiveDuration = 20 * time.Minute
)

type squareHotListParam struct {
	P        int64
	PageSize int64

	Position        int
	CreatorID       int64
	CreatorUsername string
	GuildID         int64
	UserID          int64
	guildRole       *guildrole.GuildManagerRole
}

type recommendItem struct {
	CreatorID       int64  `gorm:"column:creator_id" json:"creator_id"`
	CreatorUsername string `gorm:"-" json:"creator_username"`
	Position        int    `gorm:"column:position" json:"position"`
	StartTime       int64  `gorm:"column:start_time" json:"start_time"`
	EndTime         int64  `gorm:"column:end_time" json:"end_time"`
	CreateTime      int64  `gorm:"column:create_time" json:"create_time"`
}

type squareHotListResp struct {
	Data       []*recommendItem  `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionSquareHotList 获取所有的申请记录
/**
 * @api {get} /api/v2/guild/recommend/squarehot/list 获取所有的申请记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播昵称，模糊搜索
 * @apiParam {number=0,4,6} [position] 申请位置，0: 所有位置，包括热门 4 和热门 6, 4: 热门 4, 6: 热门 6
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [
 *           {
 *             "creator_id": 43389,
 *             "creator_username": "AAA",
 *             "position": 4,
 *             "start_time": 1625216400,
 *             "end_time": 1625217600,
 *             "create_time": 1625130000
 *           }
 *         ],
 *         "pagination": {
 *           "count": 3,
 *           "p": 1,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *       }
 *     }
 */
func ActionSquareHotList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSquareHotList(c)
	if err != nil {
		return nil, err
	}
	return param.list()
}

func newSquareHotList(c *handler.Context) (*squareHotListParam, error) {
	param := &squareHotListParam{}
	param.UserID = c.UserID()
	var err error
	param.guildRole, param.GuildID, err = checkUserAuthority(c.UserID(), true)
	if err != nil {
		return nil, err
	}
	if param.P, param.PageSize, err = c.GetParamPage(); err != nil {
		return nil, actionerrors.ErrParams
	}
	param.Position, _ = c.GetParamInt("position")
	if param.Position != 0 && !validPosition(param.Position) {
		return nil, actionerrors.ErrParams
	}
	param.CreatorID, _ = c.GetParamInt64("creator_id")
	param.CreatorUsername, _ = c.GetParamString("creator_username")
	return param, nil
}

func (p *squareHotListParam) list() (*squareHotListResp, error) {
	resp := new(squareHotListResp)
	resp.Pagination = goutil.MakePagination(0, p.P, p.PageSize)
	resp.Data = make([]*recommendItem, 0)
	if !p.guildRole.ExistsContractCreator() {
		return resp, nil
	}
	// 经纪人只能查看当前在自己旗下的主播的推荐记录
	allCreatorIDs := p.guildRole.CreatorIDs
	filteredCreatorIDs, err := mowangskuser.SearchUserIDs(p.CreatorID, p.CreatorUsername, allCreatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(filteredCreatorIDs) == 0 {
		return resp, nil
	}
	var count int64
	db := service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).
		Where("guild_id = ?", p.GuildID).
		Where("creator_id IN (?)", filteredCreatorIDs)
	if p.Position != 0 {
		db = db.Where("position = ?", p.Position)
	}
	err = db.Count(&count).Error
	if err != nil {
		return nil, err
	}
	resp.Pagination = goutil.MakePagination(count, p.P, p.PageSize)
	if !resp.Pagination.Valid() {
		return resp, nil
	}
	if err = resp.Pagination.ApplyTo(db).Order("create_time DESC").Find(&resp.Data).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	foundCreatorIDs := make([]int64, len(resp.Data))
	for i, v := range resp.Data {
		foundCreatorIDs[i] = v.CreatorID
	}
	userMap, err := mowangskuser.FindSimpleMap(foundCreatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range resp.Data {
		if userInfo := userMap[resp.Data[i].CreatorID]; userInfo != nil {
			resp.Data[i].CreatorUsername = userInfo.Username
		}
	}
	return resp, nil
}

// ActionSquareHotVacancy 获取剩余推荐次数
/**
 * @api {get} /api/v2/guild/recommend/squarehot/vacancy 获取剩余推荐次数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=4,6} position 申请位置，4: 热门 4, 6: 热门 6
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "vacancy": 1
 *       }
 *     }
 */
func ActionSquareHotVacancy(c *handler.Context) (handler.ActionResponse, error) {
	position, err := c.GetParamInt("position")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if !validPosition(position) {
		return nil, actionerrors.ErrParams
	}
	_, guildID, err := checkUserAuthority(c.UserID(), false)
	if err != nil {
		return nil, err
	}
	vacancy, err := guildHotRecommendVacancy(guildID, position)
	if err != nil {
		return nil, err
	}
	return handler.M{
		"vacancy": vacancy,
	}, nil
}

type squareHotTimeParam struct {
	Date     time.Time // 查看该天的可申请时间
	Position int
	availableTime
}

type availableTime struct {
	AvailableDate   []string `json:"available_date"`
	Date            string   `json:"date"`
	AvailablePeriod []period `json:"available_period"`
}

type period struct {
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
}

// ActionSquareHotTime 获取可申请时间
/**
 * @api {get} /api/v2/guild/recommend/squarehot/time 获取可申请时间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=4,6} position 申请位置，4: 热门 4, 6: 热门 6
 * @apiParam {String} [date] 如果要查询某天可申请的时间段，需要传该天的日期，比如 2006-01-02，不传的话 available_period 返回当天可申请的时间段或者当前周期第一天
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "available_date": [
 *           "2021-07-01",
 *           "2021-07-02",
 *           "2021-07-03"
 *         ],
 *         "date": "2021-07-01",
 *         "available_period": [
 *           {
 *             "start_time": 1625068800,
 *             "end_time": 1625070000
 *           },
 *           {
 *             "start_time": 1625070000,
 *             "end_time": 1625071200
 *           }
 *         ]
 *       }
 *     }
 */
func ActionSquareHotTime(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newSquareHotTimeParam(c)
	if err != nil {
		return nil, err
	}
	return p.getTime()
}

// 获取可申请日期的第一天
func defaultBeginTime() time.Time {
	now := guildutil.TimeNowForHotRecommend()
	// 每月 1 号 20:00 后才可申请下个周期，返回下个周期的第一天
	if inNextPeriod(now) {
		return day2OfMonth(now)
	}
	return util.BeginningOfDay(now)
}

func newSquareHotTimeParam(c *handler.Context) (*squareHotTimeParam, error) {
	_, _, err := checkUserAuthority(c.UserID(), false)
	if err != nil {
		return nil, err
	}
	param := &squareHotTimeParam{}
	date, _ := c.GetParamString("date")
	var beginningOfDay time.Time
	defaultBeginTime := defaultBeginTime()
	if date != "" {
		var err error
		beginningOfDay, err = time.ParseInLocation(util.TimeFormatYMD, date, time.Local)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
	} else {
		beginningOfDay = defaultBeginTime
	}
	beginningOfDay8 := beginningOfDay.AddDate(0, 0, 7)
	if beginningOfDay.Before(defaultBeginTime) || goutil.TimeGte(beginningOfDay, beginningOfDay8) {
		return nil, actionerrors.ErrParams
	}
	periodStart, periodEnd := currentPeriod()
	if beginningOfDay.Before(periodStart) || goutil.TimeGte(beginningOfDay, periodEnd) {
		return nil, actionerrors.ErrParams
	}
	param.Date = beginningOfDay
	param.Position, _ = c.GetParamInt("position")
	if !validPosition(param.Position) {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (p *squareHotTimeParam) getTime() (*availableTime, error) {
	now := guildutil.TimeNowForHotRecommend()
	beginOfToday := util.BeginningOfDay(now)
	_, periodEnd := currentPeriod()
	startTime := util.BeginningOfDay(now)
	endTime := beginOfToday.AddDate(0, 0, 7)
	// 每月 1 号 20:00 后才可申请下个周期，返回的可申请日期开始时间为下个周期的第一天
	if inNextPeriod(now) {
		startTime = day2OfMonth(now)
		endTime = startTime.AddDate(0, 0, 7)
	}
	if endTime.After(periodEnd) {
		endTime = periodEnd
	}
	periods, err := p.availablePeriods()
	if err != nil {
		return nil, err
	}
	return &availableTime{
		AvailableDate:   p.availableDays(startTime, endTime),
		Date:            p.Date.Format(util.TimeFormatYMD),
		AvailablePeriod: periods,
	}, nil
}

func (p *squareHotTimeParam) availableDays(startTime, endTime time.Time) []string {
	availableDays := make([]string, 0, 7)
	for availableDay := startTime; availableDay.Before(endTime); availableDay = availableDay.AddDate(0, 0, 1) {
		availableDays = append(availableDays, availableDay.Format(util.TimeFormatYMD))
	}
	return availableDays
}

func (p *squareHotTimeParam) availablePeriods() ([]period, error) {
	name := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	// 查询占用了当天内任意时间段的所有记录
	occupiedModels, err := liverecommendedelements.FindSquareElement(
		p.Position, name, p.Date.Unix(), p.Date.AddDate(0, 0, 1).Unix())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	allPeriodsMark := make([]int64, int64(24*time.Hour)/int64(EffectiveDuration))
	startTime := p.Date
	endTime := p.Date.AddDate(0, 0, 1)
	for _, v := range occupiedModels {
		if v.StartTime == nil {
			continue
		}
		if *v.StartTime >= v.ExpireTime {
			continue
		}
		// 对于每条记录，通过每条记录的开始时间和结束时间计算该条记录占用的所有时间段，每个时间段的长度是 EffectiveDuration
		var startIndex int64
		if *v.StartTime > startTime.Unix() {
			startIndex = (*v.StartTime - startTime.Unix()) / int64(EffectiveDuration.Seconds())
		}
		endIndex := int64(len(allPeriodsMark) - 1)
		if v.ExpireTime < endTime.Unix() {
			// ExpireTime 是开区间，这里转为闭区间计算，需要 -1
			endIndex = ((v.ExpireTime - 1) - startTime.Unix()) / int64(EffectiveDuration.Seconds())
		}
		for i := startIndex; i <= endIndex; i++ {
			// 标记被占用的时间段
			allPeriodsMark[i] = 1
		}
	}
	availablePeriods := make([]period, 0, len(allPeriodsMark))
	for _, v := range allPeriodsMark {
		if v == 0 {
			beginTime := startTime.Unix()
			endTime := startTime.Add(EffectiveDuration).Unix()
			availablePeriods = append(availablePeriods, period{StartTime: beginTime, EndTime: endTime})
		}
		startTime = startTime.Add(EffectiveDuration)
	}
	return availablePeriods, nil
}

type applyRecommendParams struct {
	Position  int   `form:"position" json:"position"`
	CreatorID int64 `form:"creator_id" json:"creator_id"`
	ApplyTime int64 `form:"apply_time" json:"apply_time"`

	userID          int64
	guildID         int64
	managerRole     *guildrole.GuildManagerRole
	roomID          int64
	applyEndTime    int64
	elementsName    string
	creatorUsername string
}

// ActionSquareHotAdd 新增申请
/**
 * @api {post} /api/v2/guild/recommend/squarehot/add 新增申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=4,6} position 申请位置，4: 热门 4, 6: 热门 6
 * @apiParam {Number} apply_time 申请时间
 * @apiParam {Number} creator_id 主播 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "creator_id": 43389, // 主播 ID
 *         "creator_username": "AAA", // 主播昵称
 *         "position": 4, // 申请位置
 *         "start_time": 1625216400, // 推荐开始时间，为秒级时间戳
 *         "end_time": 1625217600, // 推荐结束时间，为秒级时间戳
 *         "create_time": 1625130000 // 创建时间，为秒级时间戳
 *       }
 *     }
 */
func ActionSquareHotAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param applyRecommendParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	var guildID int64
	param.managerRole, guildID, err = checkUserAuthority(c.UserID(), false)
	if err != nil {
		return nil, err
	}
	if err := param.checkParams(); err != nil {
		return nil, err
	}
	param.userID = c.UserID()
	param.guildID = guildID
	if err := param.checkBeforeAdd(); err != nil {
		return nil, err
	}
	return param.add()
}

func (p *applyRecommendParams) checkParams() error {
	if p.ApplyTime <= 0 {
		return actionerrors.NewErrForbidden("未选择时间")
	}
	if p.Position <= 0 {
		return actionerrors.NewErrForbidden("未选择申请位置")
	}
	if !validPosition(p.Position) {
		return actionerrors.ErrParams
	}
	if p.CreatorID <= 0 {
		return actionerrors.NewErrForbidden("未选择主播")
	}
	now := guildutil.TimeNowForHotRecommend()
	applyStartTime := time.Unix(p.ApplyTime, 0)
	maxEndTime := util.BeginningOfDay(now).AddDate(0, 0, 7)
	if inNextPeriod(now) {
		maxEndTime = maxEndTime.AddDate(0, 0, 1)
	}
	// 下面是对申请时间的判断：不可申请七天外的时间且不可申请当前周期外的时间
	if now.After(applyStartTime) {
		return actionerrors.NewErrForbidden("推荐开始时间不能早于当前时间")
	}
	// 申请结束时间 <= maxEndTime
	if !goutil.TimeLte(applyStartTime.Add(EffectiveDuration), maxEndTime) {
		return actionerrors.NewErrForbidden("推荐时间不可在当前周期外")
	}
	periodStartTime, periodEndTime := currentPeriod()
	// 正确情况：申请开始时间 >= periodStartTime && 申请开始时间 < periodEndTime
	if !(goutil.TimeGte(applyStartTime, periodStartTime) && applyStartTime.Before(periodEndTime)) {
		return actionerrors.NewErrForbidden("推荐时间不可在当前周期外")
	}
	if p.ApplyTime%(int64(EffectiveDuration.Seconds())) != 0 {
		return actionerrors.ErrParams
	}
	return nil
}

func (p *applyRecommendParams) checkBeforeAdd() error {
	// 每个主播同一天（即申请的推荐时间日期）分别只能申请一次热四、热六（即各个位置各一次）
	todayApplyVacancy, err := creatorApplyCount(p.ApplyTime, p.CreatorID, p.Position)
	if err != nil {
		return err
	}
	if todayApplyVacancy >= 1 {
		return actionerrors.NewErrForbidden("该日申请次数已用完")
	}
	vacancy, err := guildHotRecommendVacancy(p.guildID, p.Position)
	if err != nil {
		return err
	}
	if vacancy < 1 {
		return actionerrors.NewErrForbidden("当前本期推荐剩余次数为 0")
	}
	u, err := mowangskuser.FindByUserID(p.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return actionerrors.ErrCannotFindUser
	}
	p.creatorUsername = u.Username
	filter := bson.M{"creator_id": p.CreatorID}
	r, err := room.FindOne(filter, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.NewErrForbidden("直播间不存在")
	}
	allCreatorIDs := p.managerRole.CreatorIDs
	if !goutil.HasElem(allCreatorIDs, p.CreatorID) {
		if !p.managerRole.Role.IsGuildOwner() {
			// 判断主播是否是被当前人管理的
			return actionerrors.ErrCreatorNotAssignedToYou
		}
		return actionerrors.ErrCreatorNotInYourGuild
	}
	isExists, err := guildrecommendblocklist.ExistsAny(r.RoomID, p.guildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isExists {
		return actionerrors.NewErrForbidden("该主播当前不可申请")
	}
	elementsName := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	endTime := time.Unix(p.ApplyTime, 0).Add(EffectiveDuration)
	lock := lockKeyGuildRecommendHot(p.Position, elementsName, p.ApplyTime)
	ok, err := service.Redis.SetNX(lock, p.guildID, 5*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("该时间段已有申请，请重新选择")
	}
	defer service.Redis.Del(lock)
	conflictElements, err := liverecommendedelements.FindSquareElement(p.Position, elementsName, p.ApplyTime, endTime.Unix())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(conflictElements) > 0 {
		return actionerrors.NewErrForbidden("该时间段已有申请，请重新选择")
	}
	p.roomID = r.RoomID
	p.elementsName = elementsName
	p.applyEndTime = endTime.Unix()
	return nil
}

func (p *applyRecommendParams) add() (*recommendItem, error) {
	now := guildutil.TimeNowForHotRecommend()
	startTime, _ := currentPeriod()
	guildRecommend := &recommend.GuildRecommendSquareHot{
		Position:     p.Position,
		CreatorID:    p.CreatorID,
		GuildID:      p.guildID,
		OperatorID:   p.userID,
		StartTime:    p.ApplyTime,
		EndTime:      p.applyEndTime,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		if err := recommend.DecreaseVacancy(tx, p.guildID, startTime, p.Position); err != nil {
			return err
		}
		if err := tx.Create(guildRecommend).Error; err != nil {
			return err
		}
		extendedFields := liverecommendedelements.SquareExtendedFields{
			From: liverecommendedelements.SquareFromGuild,
		}
		extendedFieldsBytes, err := json.Marshal(extendedFields)
		if err != nil {
			return err
		}
		m := liverecommendedelements.Model{
			ElementType: liverecommendedelements.ElementSquare,
			Sort:        int(p.Position),
			ElementID:   p.roomID,
			Attribute: liverecommendedelements.Attribute{
				Name:      p.elementsName,
				StartTime: &p.ApplyTime,
			},
			ExpireTime:     p.applyEndTime,
			ExtendedFields: string(extendedFieldsBytes),
		}
		if err := service.DB.Create(&m).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return &recommendItem{
		CreatorID:       guildRecommend.CreatorID,
		CreatorUsername: p.creatorUsername,
		Position:        guildRecommend.Position,
		StartTime:       guildRecommend.StartTime,
		EndTime:         guildRecommend.EndTime,
		CreateTime:      guildRecommend.CreateTime,
	}, nil
}

// guildHotRecommendVacancy 获得公会本期推荐次数
func guildHotRecommendVacancy(guildID int64, position int) (int64, error) {
	startTime, _ := currentPeriod()
	vacancy, err := recommend.VacancyLeft(service.LiveDB, guildID, startTime, position)
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	return vacancy, nil
}

// creatorApplyCount 获取主播当天申请了某个位置的次数
func creatorApplyCount(applyTime int64, creatorID int64, position int) (int, error) {
	beginOfDay := goutil.BeginningOfDay(time.Unix(applyTime, 0))
	endOfDay := beginOfDay.AddDate(0, 0, 1)
	var count int
	if err := service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).
		Where("creator_id = ? AND position = ? AND start_time < ? AND end_time > ?", creatorID, position, endOfDay.Unix(), beginOfDay.Unix()).
		Count(&count).Error; err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	return count, nil
}

func checkUserAuthority(userID int64, guildOwnerWithContracted bool) (*guildrole.GuildManagerRole, int64, error) {
	role, guildInfo, err := guildrole.UserGuildRole(userID)
	if err != nil {
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, 0, actionerrors.ErrNoAuthority
	}
	manageRole, err := guildrole.FindManagerRole(userID, true, guildrole.Options{GuildOwnerWithContracted: guildOwnerWithContracted})
	if err != nil {
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}
	if manageRole == nil {
		return nil, 0, actionerrors.ErrForbidden
	}
	return manageRole, guildInfo.ID, nil
}

// currentPeriod 获得当前周期的开始时间和结束时间，每个周期是从当月 2 号 0 点到下个月 2 号 0 点
func currentPeriod() (time.Time, time.Time) {
	now := guildutil.TimeNowForHotRecommend()
	// 每月 1 号 20:00 后才可申请下个周期
	if now.Day() == 1 && now.Hour() < 20 {
		now = now.AddDate(0, 0, -1)
	}
	startTime := day2OfMonth(now)
	endTime := getDay2OfNextMonth(now)
	return startTime, endTime
}

// 判断时间是否处在下个周期，只有 1 号的 20 点到 24 点才处在下个周期
func inNextPeriod(t time.Time) bool {
	return t.Day() == 1 && t.Hour() >= 20
}

func day2OfMonth(now time.Time) time.Time {
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()
	day2 := time.Date(currentYear, currentMonth, 2, 0, 0, 0, 0, currentLocation)
	return day2
}

func getDay2OfNextMonth(now time.Time) time.Time {
	return day2OfMonth(now).AddDate(0, 1, 0)
}

// validPosition 判断位置是否合法
func validPosition(position int) bool {
	if position != 4 && position != 6 {
		return false
	}
	return true
}

func lockKeyGuildRecommendHot(position int, name string, startTime int64) string {
	return keys.LockGuildRecommendHot3.Format(position, name, startTime)
}
