package guildbackend

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildAssignAgentCreator(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	guildOwner := int64(998877)
	testGuild := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    guildOwner,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	require.NoError(guild.Guild{}.DB().Create(testGuild).Error)
	defer func() {
		require.NoError(guild.Guild{}.DB().Delete(nil, "id = ?", testGuild.ID).Error)
	}()

	agent := user.MowangskUser{
		Simple: user.Simple{
			UserName: "测试经纪人",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
		},
		CIP:   "127.0.0.1",
		UIP:   "127.0.0.1",
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&agent).Error)
	defer func() {
		require.NoError(service.DB.Table(agent.TableName()).Delete(nil, "id = ?", agent.ID).Error)
	}()
	creator := user.MowangskUser{
		Simple: user.Simple{
			UserName: "测试主播",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
		},
		CIP:   "127.0.0.1",
		UIP:   "127.0.0.1",
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&creator).Error)
	defer func() {
		require.NoError(service.DB.Table(creator.TableName()).Delete(nil, "id = ?", creator.ID).Error)
	}()
	guildAgent := guildagent.GuildAgent{
		GuildID: testGuild.ID,
		AgentID: agent.ID,
	}
	require.NoError(guildagent.GuildAgent{}.DB().Create(&guildAgent).Error)
	defer func() {
		require.NoError(guildagent.GuildAgent{}.DB().Delete(nil, "id = ?", guildAgent.ID).Error)
	}()

	contract := &livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           creator.ID,
		ContractDuration: 120,
		ContractStart:    util.TimeNow().Add(-time.Minute).Unix(),
		ContractEnd:      util.TimeNow().Add(+time.Minute).Unix(),
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
	}
	require.NoError(livecontract.LiveContract{}.DB().Create(contract).Error)
	defer func() {
		require.NoError(livecontract.LiveContract{}.DB().Delete(nil, "id = ?", contract.ID).Error)
	}()

	c := handler.NewTestContext(http.MethodPost, "/api/v2/guild/agent/assign", true, map[string]interface{}{
		"agent_id":    agent.ID,
		"creator_ids": []int64{creator.ID},
	})
	c.User().ID = 98888779
	_, err := ActionGuildAssignAgentCreator(c)
	require.EqualError(err, actionerrors.ErrYouAreNotGuildOwner.Message)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/agent/assign", true, map[string]interface{}{
		"agent_id":    agent.ID,
		"creator_ids": []int64{9876564321},
	})
	c.User().ID = guildOwner
	_, err = ActionGuildAssignAgentCreator(c)
	require.EqualError(err, actionerrors.ErrCannotFindUser.Message)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/agent/assign", true, map[string]interface{}{
		"agent_id":    agent.ID,
		"creator_ids": []int64{creator.ID},
	})
	c.User().ID = guildOwner
	msg, err := ActionGuildAssignAgentCreator(c)
	require.NoError(err)
	assert.Equal("经纪人分配成功", msg)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/agent/assign", true, map[string]interface{}{
		"agent_id":   0,
		"creator_id": creator.ID,
	})
	c.User().ID = guildOwner
	msg, err = ActionGuildAssignAgentCreator(c)
	require.NoError(err)
	assert.Equal("取消分配成功", msg)
}

func TestOperateAgentCreatorParam_AssignCreatorToAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	agent := user.MowangskUser{
		Simple: user.Simple{
			UserName: "测试经纪人",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
		},
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&agent).Error)
	defer func() {
		require.NoError(service.DB.Table(agent.TableName()).Delete(nil, "id = ?", agent.ID).Error)
	}()
	creator := user.MowangskUser{
		Simple: user.Simple{
			UserName: "测试主播",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
		},
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&creator).Error)
	defer func() {
		require.NoError(service.DB.Table(creator.TableName()).Delete(nil, "id = ?", creator.ID).Error)
	}()
	guildOwner := user.MowangskUser{
		Simple: user.Simple{
			UserName: "测试公会会长",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
		},
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&guildOwner).Error)
	defer func() {
		require.NoError(service.DB.Table(guildOwner.TableName()).Delete(nil, "id = ?", guildOwner.ID).Error)
	}()
	testGuild := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    guildOwner.ID,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	require.NoError(testGuild.DB().Create(testGuild).Error)
	defer func() {
		require.NoError(testGuild.DB().Delete(nil, "id = ?", testGuild.ID).Error)
	}()
	guildAgent := &guildagent.GuildAgent{
		AgentID: agent.ID,
		GuildID: testGuild.ID,
	}
	require.NoError(guildAgent.DB().Create(guildAgent).Error)
	defer func() {
		require.NoError(guildAgent.DB().Delete(nil, "id = ?", guildAgent.ID).Error)
	}()
	contract := &livecontract.LiveContract{
		GuildID:     testGuild.ID,
		LiveID:      creator.ID,
		ContractEnd: util.TimeNow().Add(+time.Minute).Unix(),
		Status:      livecontract.StatusContracting,
	}
	require.NoError(livecontract.LiveContract{}.DB().Create(contract).Error)
	defer func() {
		require.NoError(livecontract.LiveContract{}.DB().Delete(nil, "id = ?", contract.ID).Error)
	}()

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	c.User().ID = testGuild.UserID

	param := &operateAgentCreatorParam{
		CreatorID: 987654321,
		AgentID:   agent.ID,
		c:         c,
		guildInfo: &guild.IDName{
			ID:     testGuild.ID,
			Name:   testGuild.Name,
			UserID: testGuild.UserID,
		},
	}
	_, err := param.assignCreatorToAgent()
	require.EqualError(err, actionerrors.ErrCannotFindUser.Message)

	param = &operateAgentCreatorParam{
		CreatorID: creator.ID,
		AgentID:   creator.ID,
		c:         c,
		guildInfo: &guild.IDName{
			ID:     testGuild.ID,
			Name:   testGuild.Name,
			UserID: testGuild.UserID,
		},
	}
	_, err = param.assignCreatorToAgent()
	require.EqualError(err, actionerrors.ErrAgentNotFound.Message)

	// 会长为自己分配主播
	require.NoError(guildagent.AgentCreator{}.DB().Delete(nil, "guild_id = ?", testGuild.ID).Error)
	param = &operateAgentCreatorParam{
		CreatorID: creator.ID,
		AgentID:   testGuild.UserID,
		c:         c,
		guildInfo: &guild.IDName{
			ID:     testGuild.ID,
			Name:   testGuild.Name,
			UserID: testGuild.UserID,
		},
	}
	msg, err := param.assignCreatorToAgent()
	require.NoError(err)
	assert.Equal("经纪人分配成功", msg)

	// 会长为名下的经纪人分配主播
	require.NoError(guildagent.AgentCreator{}.DB().Delete(nil, "guild_id = ?", testGuild.ID).Error)
	param = &operateAgentCreatorParam{
		CreatorIDs: []int64{creator.ID},
		AgentID:    agent.ID,
		c:          c,
		guildInfo: &guild.IDName{
			ID:     testGuild.ID,
			Name:   testGuild.Name,
			UserID: testGuild.UserID,
		},
	}
	msg, err = param.assignCreatorToAgent()
	require.NoError(err)
	assert.Equal("经纪人分配成功", msg)
	agents, err := guildagent.FindAgentsByCreatorIDs(guildagent.AgentCreator{}.DB(), []int64{creator.ID}, testGuild.ID)
	require.NoError(err)
	assert.NotEmpty(agents)
}

func TestOperateAgentCreatorParam_unassignCreatorToAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	agentCreator := &guildagent.AgentCreator{
		CreatorID: 12,
		AgentID:   444333,
		GuildID:   222111,
	}
	require.NoError(agentCreator.DB().Delete(nil, "id = ?", agentCreator.ID).Error)
	contract := &livecontract.LiveContract{
		GuildID:     agentCreator.GuildID,
		LiveID:      agentCreator.CreatorID,
		ContractEnd: util.TimeNow().Add(+time.Minute).Unix(),
		Status:      livecontract.StatusContracting,
	}
	require.NoError(livecontract.LiveContract{}.DB().Create(contract).Error)
	defer func() {
		require.NoError(livecontract.LiveContract{}.DB().Delete(nil, "id = ?", contract.ID).Error)
	}()

	param := &operateAgentCreatorParam{
		CreatorID: agentCreator.CreatorID,
		AgentID:   -99999,
		guildInfo: &guild.IDName{
			ID: agentCreator.GuildID,
		},
	}
	_, err := param.unassignCreatorToAgent()
	require.EqualError(err, actionerrors.ErrCreatorAgentNotFound.Message)

	require.NoError(agentCreator.DB().Create(agentCreator).Error)
	param = &operateAgentCreatorParam{
		CreatorID: agentCreator.CreatorID,
		AgentID:   agentCreator.AgentID,
		guildInfo: &guild.IDName{
			ID: agentCreator.GuildID,
		},
	}
	msg, err := param.unassignCreatorToAgent()
	require.NoError(err)
	assert.Equal("取消分配成功", msg)

	err = agentCreator.DB().First(agentCreator, "id = ?", agentCreator.ID).Error
	require.NoError(err)
	assert.Greater(agentCreator.ModifiedTime, int64(0))

	param = &operateAgentCreatorParam{
		CreatorID: agentCreator.CreatorID,
		AgentID:   agentCreator.AgentID,
		guildInfo: &guild.IDName{
			ID: agentCreator.GuildID,
		},
	}
	_, err = param.unassignCreatorToAgent()
	require.EqualError(err, actionerrors.ErrCreatorAgentNotFound.Message)
}
