package guildbackend

import (
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/mysql/liveschedulerecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	maxScheduleApplyStartTime = 84600 // 对应 23:30
	maxScheduleApplyEndTime   = 86400 // 对应 24:00
	halfHourSecond            = 1800  // 单位秒
)

// reset type
const (
	TypeSetPause   = iota // 暂停推荐
	TypeSetRestart        // 重新推荐
)

type recommendScheduleApplyParam struct {
	CreatorID int64            `form:"creator_id" json:"creator_id"`
	CoverURL  upload.SourceURL `form:"cover_url" json:"cover_url"`
	StartTime int              `form:"start_time" json:"start_time"`
	EndTime   int              `form:"end_time" json:"end_time"`

	operatorID int64
	r          *room.Room
	role       guildrole.GuildRole
	contract   *livecontract.LiveContract
}

// ActionRecommendScheduleApplyAdd 公会添加首页推荐申请
/**
 * @api {post} /api/v2/guild/recommend/schedule/apply-add 公会添加首页推荐申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {String} [cover_url] 封面图 720*368
 * @apiParam {Number} start_time 开始时间, 如: 0, 1800, ..., 84600
 * @apiParam {Number} end_time 结束时间 ID, 如: 1800, ..., 86400
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "apply_id": 1,  // 记录 ID
 *         "creator_id": 43389,  // 主播 ID
 *         "status": 0,  // 申请状态
 *         "cover_url": "http://foo.com/bar.png",  // 推荐图
 *         "cover_reviewing": true,  // 图片审核中
 *         "start_time": 0,  // 申请开始时间, 如: 0, 1800, ..., 84600
 *         "end_time": 1800, // 申请结束时间
 *         "create_time": 1594695035  // 创建时间
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionRecommendScheduleApplyAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendScheduleApplyParam
	err := param.check(c)
	if err != nil {
		return nil, err
	}
	return param.addApply()
}

func checkApplyTime(startTime, endTime int) error {
	// 校验时间
	if startTime >= endTime {
		return actionerrors.ErrParamsMsg("结束时间不得早于开始时间")
	}
	if startTime > maxScheduleApplyStartTime || startTime%halfHourSecond != 0 ||
		endTime > maxScheduleApplyEndTime || endTime%halfHourSecond != 0 {
		return actionerrors.ErrParams
	}
	return nil
}

func (p *recommendScheduleApplyParam) check(c *handler.Context) error {
	err := c.Bind(&p)
	if err != nil || p.CreatorID <= 0 || p.StartTime < 0 {
		return actionerrors.ErrParams
	}
	p.operatorID = c.UserID()
	if err = checkApplyTime(p.StartTime, p.EndTime); err != nil {
		return err
	}
	p.role, p.contract, err = guildrole.FindAssociatedContractWithAgent(p.CreatorID, c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !p.role.IsGuildManager() {
		return actionerrors.NewErrForbidden("您没有权限操作该主播")
	}
	p.r, err = room.FindOne(bson.M{"creator_id": p.CreatorID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.NewErrLiveForbidden("该直播间不存在")
	}
	if p.CoverURL != "" {
		if ok := storage.CheckStorage(p.CoverURL); !ok {
			return actionerrors.NewErrLiveForbidden("请求上传的资源不被支持，请重新选择")
		}
	}

	exists, err := guildrecommendblocklist.ExistsAny(p.r.RoomID, p.contract.GuildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		// 主播在资源位黑名单中
		return actionerrors.NewErrLiveForbidden("该主播目前不可申请")
	}

	apply, err := guildscheduleapply.FindApply(p.CreatorID, p.contract.GuildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if apply != nil {
		return actionerrors.NewErrLiveForbidden("已有该主播申请")
	}
	count, err := guildscheduleapply.ApplyCount(p.contract.GuildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if count >= guildscheduleapply.GuildApplyCountLimit {
		return actionerrors.NewErrLiveForbidden("已达首页推荐位推荐池上限")
	}
	return nil
}

func (p *recommendScheduleApplyParam) addApply() (*guildscheduleapply.LiveGuildScheduleApply, error) {
	var uploadCoverURL string
	var err error
	// 不传图片不用审核，状态直接是通过状态
	// WORKAROUND: 前端修改完要删除兼容代码
	// https://www.tapd.bilibili.co/35612194/prong/stories/view/1135612194002637839
	status := guildscheduleapply.StatusPassed
	if p.CoverURL != "" {
		uploadCoverURL, err = storage.UploadToOSS(p.CoverURL, storage.PathPrefixSchedule)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		status = guildscheduleapply.StatusReviewing
	}
	apply := &guildscheduleapply.LiveGuildScheduleApply{
		OperatorID: p.operatorID,
		GuildID:    p.contract.GuildID,
		RoomID:     p.r.RoomID,
		CreatorID:  p.r.CreatorID,
		Status:     status,
		Cover:      uploadCoverURL,
		StartTime:  p.StartTime,
		Duration:   p.EndTime - p.StartTime,
		EndTime:    p.EndTime,
	}
	err = apply.Save(apply.Cover)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return apply, nil
}

type scheduleApplyEditParam struct {
	ApplyID   int64            `form:"apply_id" json:"apply_id"`
	CoverURL  upload.SourceURL `form:"cover_url" json:"cover_url"`
	StartTime int              `form:"start_time" json:"start_time"`
	EndTime   int              `form:"end_time" json:"end_time"`

	operatorID     int64
	uploadCoverURL string
	role           guildrole.GuildRole
	apply          *guildscheduleapply.LiveGuildScheduleApply
}

// ActionRecommendScheduleApplyEdit 公会修改首页推荐申请
/**
 * @api {post} /api/v2/guild/recommend/schedule/apply-edit 公会修改首页推荐申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} apply_id 申请推荐 ID
 * @apiParam {Number} [start_time] 开始时间, 如: 0, 1800, ..., 84600
 * @apiParam {Number} [end_time] 结束时间 ID, 如: 1800, ..., 86400
 * @apiParam {String} [cover_url] 封面图 720*368
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "apply_id": 1,  // 记录 ID
 *         "creator_id": 43389,  // 主播 ID
 *         "status": 1,  // 申请状态 -1: 停止推荐; 0: 待审核; 1: 已过审
 *         "cover_url": "http://foo.com/bar.png",  // 推荐图
 *         "cover_reviewing": true,  // 图片审核中，未在审核时，不返回该字段
 *         "start_time": 0,  // 申请开始时间, 如: 0, 1800, ..., 84600
 *         "end_time": 1800, // 申请结束时间
 *         "create_time": 1594695035  // 创建时间
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionRecommendScheduleApplyEdit(c *handler.Context) (handler.ActionResponse, error) {
	var param scheduleApplyEditParam
	err := param.check(c)
	if err != nil {
		return nil, err
	}
	if err = param.editApply(); err != nil {
		return nil, err
	}
	return param.apply, nil
}

func (p *scheduleApplyEditParam) check(c *handler.Context) error {
	err := c.Bind(&p)
	if err != nil || p.ApplyID <= 0 || p.StartTime < 0 {
		return actionerrors.ErrParams
	}
	if p.EndTime != 0 {
		if err = checkApplyTime(p.StartTime, p.EndTime); err != nil {
			return err
		}
	}
	p.operatorID = c.UserID()
	p.apply, err = guildscheduleapply.FindApplyByID(p.ApplyID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.apply == nil {
		return actionerrors.ErrCannotFindResource
	}
	p.role, _, err = guildrole.FindAssociatedContractWithAgent(p.apply.CreatorID, c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !p.role.IsGuildManager() {
		return actionerrors.NewErrForbidden("您没有权限操作该主播")
	}
	if p.CoverURL != "" {
		// 修改推荐图
		if ok := storage.CheckStorage(p.CoverURL); !ok {
			return actionerrors.NewErrLiveForbidden("请求上传的资源不被支持，请重新选择")
		}
		p.uploadCoverURL, err = storage.UploadToOSS(p.CoverURL, storage.PathPrefixSchedule)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

func (p *scheduleApplyEditParam) editApply() error {
	p.apply.OperatorID = p.operatorID
	if p.EndTime != 0 {
		p.apply.StartTime = p.StartTime
		p.apply.EndTime = p.EndTime
		p.apply.Duration = p.apply.EndTime - p.apply.StartTime
	}
	switch p.apply.Status {
	case guildscheduleapply.StatusRefused:
		p.apply.Status = guildscheduleapply.StatusReviewing
	}
	if err := p.apply.Save(p.uploadCoverURL); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type scheduleApplyResetParam struct {
	ApplyID int64 `form:"apply_id" json:"apply_id"`
	Type    int   `form:"type" json:"type"`
}

// ActionRecommendScheduleApplySet 公会管理首页排期推荐
/**
 * @api {post} /api/v2/guild/recommend/schedule/apply-set 公会管理首页排期推荐
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1} type 操作类型：0 停止推荐，1 重新推荐
 * @apiParam {Number} apply_id 申请推荐 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "重新推荐成功/停止推荐成功"
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionRecommendScheduleApplySet(c *handler.Context) (handler.ActionResponse, error) {
	var param scheduleApplyResetParam
	err := param.check(c)
	if err != nil {
		return nil, err
	}
	return param.set()
}

func (p *scheduleApplyResetParam) check(c *handler.Context) error {
	err := c.Bind(&p)
	if err != nil || p.ApplyID <= 0 {
		return actionerrors.ErrParams
	}
	apply, err := guildscheduleapply.FindApplyByID(p.ApplyID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if apply == nil {
		return actionerrors.ErrCannotFindResource
	}
	role, _, err := guildrole.FindAssociatedContractWithAgent(apply.CreatorID, c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !role.IsGuildManager() {
		return actionerrors.NewErrForbidden("您没有权限操作该主播")
	}
	// 重新推荐需要校验：首页推荐位推荐池上限
	if p.Type == TypeSetRestart {
		count, err := guildscheduleapply.ApplyCount(apply.GuildID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if count >= guildscheduleapply.GuildApplyCountLimit {
			return actionerrors.NewErrLiveForbidden("已达首页推荐位推荐池上限")
		}
	}
	return nil
}

func (p *scheduleApplyResetParam) set() (string, error) {
	switch p.Type {
	case TypeSetPause:
		ok, err := guildscheduleapply.PauseApply(p.ApplyID)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return "", actionerrors.ErrCannotFindResource
		}
		return "停止推荐成功", nil
	case TypeSetRestart:
		ok, err := guildscheduleapply.RestartApply(p.ApplyID)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return "", actionerrors.ErrCannotFindResource
		}
		return "重新推荐成功", nil
	default:
		return "", actionerrors.ErrParams
	}
}

// ActionRecommendScheduleApply 首页推荐位申请详情
/**
 * @api {get} /api/v2/guild/recommend/schedule/apply 首页推荐位申请详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} apply_id 申请推荐 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "apply_id": 1,  // 记录 ID
 *         "creator_id": 43389,  // 主播 ID
 *         "creator_username": "AAA",  // 主播昵称
 *         "status": 2,  // 申请状态 -1: 停止推荐; 0: 待审核; 1: 已过审; 2: 未过审
 *         "reason": "图片不符合要求", // 未过审原因, 只在未过审时返回该字段
 *         "cover_url": "http://foo.com/bar.png",  // 推荐图
 *         "cover_reviewing": true,  // 图片审核中，未在审核时，不返回该字段
 *         "start_time": 0,  // 申请开始时间, 如: 0, 1800, ..., 84600
 *         "end_time": 1800, // 申请结束时间
 *         "create_time": 1594695035 // 创建时间, 单位秒
 *       }
 *     }
 */
func ActionRecommendScheduleApply(c *handler.Context) (handler.ActionResponse, error) {
	applyID, _ := c.GetParamInt64("apply_id")
	if applyID <= 0 {
		return nil, actionerrors.ErrParams
	}
	apply, err := guildscheduleapply.FindApplyByID(applyID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if apply == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	role, _, err := guildrole.FindAssociatedContractWithAgent(apply.CreatorID, c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !role.IsGuildManager() {
		return nil, actionerrors.NewErrForbidden("您没有权限查看该申请")
	}
	creator, err := mowangskuser.FindByUserID(apply.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if creator == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	apply.CreatorUsername = creator.Username
	// 查询审核中的推荐图
	lr, err := livereview.FindGuildRoomReviewing(apply.GuildID, apply.RoomID, livereview.TypeScheduleRecommend)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if lr != nil {
		apply.CoverURL = storage.ParseSchemeURL(lr.ImageURL)
		apply.CoverReviewing = true
	}
	return apply, nil
}

type scheduleApplyLisResp struct {
	Data       []*guildscheduleapply.LiveGuildScheduleApply `json:"data"`
	Pagination goutil.Pagination                            `json:"pagination"`
}

// ActionRecommendScheduleApplyList 首页推荐位申请列表
/**
 * @api {get} /api/v2/guild/recommend/schedule/apply-list 首页推荐位申请列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {String} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播名称
 * @apiParam {number=0,1,2,3,4,5,6} [type] 申请状态 0: 全部; 1: 待审核; 2: 已过审; 3: 未过审; 4: 停止推荐; 5: 已请假; 6: 已换人
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [{
 *           "apply_id": 1,  // 记录 ID
 *           "creator_id": 43389,  // 主播 ID
 *           "creator_username": "AAA",  // 主播昵称
 *           "creator_iconurl": "http://static.missevan.com/icon/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "status": 2, // 申请状态 -1: 停止推荐; 0: 待审核; 1: 已过审; 2: 未过审; 3: 已请假; 4: 已换人
 *           "cover_url": "http://foo.com/bar.png",  // 推荐图
 *           "cover_reviewing": true,  // 图片审核中，未在审核时，不返回该字段
 *           "start_time": 0, // 申请开始时间, 如: 0, 1800, ..., 84600
 *           "end_time": 1800, // 申请结束时间
 *           "create_time": 1594695035,  // 创建时间
 *           "show_absence": 0 // 按位判断，第一位为请假按钮状态，第二位为换人按钮状态 1：显示；0：不显示
 *         }]
 *         "pagination": {
 *           "count": 3,
 *           "p": 1,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *       }
 *     }
 */
func ActionRecommendScheduleApplyList(c *handler.Context) (handler.ActionResponse, error) {
	creatorID, _ := c.GetParamInt64("creator_id")
	creatorUsername, _ := c.GetParam("creator_username")
	searchType, _ := c.GetParamInt("type")
	if creatorID < 0 || searchType < guildscheduleapply.SearchTypeAll ||
		searchType > guildscheduleapply.SearchTypePaused {
		return nil, actionerrors.ErrParams
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	managerRole, err := guildrole.FindManagerRole(c.UserID(), true)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if managerRole == nil {
		return nil, actionerrors.ErrForbidden
	}
	searchUserIDs := searchCreatorIDs(managerRole.CreatorIDs, creatorID)
	if len(searchUserIDs) == 0 {
		return &scheduleApplyLisResp{
			Data:       make([]*guildscheduleapply.LiveGuildScheduleApply, 0),
			Pagination: goutil.Pagination{},
		}, nil
	}
	applyList, pa, err := guildscheduleapply.FindApplyList(p, pageSize,
		guildscheduleapply.SearchParam{
			CreatorIDs:      searchUserIDs,
			CreatorUsername: creatorUsername,
			Type:            searchType,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(applyList) == 0 {
		return &scheduleApplyLisResp{Data: applyList, Pagination: pa}, nil
	}
	creatorIDs := make([]int64, 0, len(applyList))
	reviewRoomIDs := make([]int64, 0, len(applyList))
	for i := range applyList {
		creatorIDs = append(creatorIDs, applyList[i].CreatorID)
		reviewRoomIDs = append(reviewRoomIDs, applyList[i].RoomID)
	}
	reviews, err := livereview.ListGuildRoomReviewing(managerRole.GuildInfo.ID, reviewRoomIDs, livereview.TypeScheduleRecommend)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	userMaps, err := mowangskuser.FindSimpleMap(creatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range applyList {
		if simple := userMaps[applyList[i].CreatorID]; simple != nil {
			applyList[i].CreatorUsername = simple.Username
		}
		// 替换审核中的图片
		for j := range reviews {
			if applyList[i].RoomID == reviews[j].RoomID {
				applyList[i].CoverURL = storage.ParseSchemeURL(reviews[j].ImageURL)
				applyList[i].CoverReviewing = true
				break
			}
		}
	}
	return &scheduleApplyLisResp{
		Data:       applyList,
		Pagination: pa,
	}, nil
}

func searchCreatorIDs(roleCreatorIDs []int64, searchCreatorID int64) (searchCreatorIDs []int64) {
	if len(roleCreatorIDs) == 0 || (searchCreatorID != 0 && !goutil.HasElem(roleCreatorIDs, searchCreatorID)) {
		return
	}
	if searchCreatorID != 0 {
		return []int64{searchCreatorID}
	}
	return roleCreatorIDs
}

type liveScheduleResult struct {
	Time int64           `json:"time"`
	Data []*liveSchedule `json:"data"`
}

type liveSchedule struct {
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	Tag             int    `json:"tag"`
	StartTime       int64  `json:"start_time"`
	ExpireTime      int64  `json:"expire_time"`
}

// ActionRecommendScheduleList 获取首页推荐排期
/**
 * @api {get} /api/v2/guild/recommend/schedule/list 获取首页推荐排期
 * @apiVersion 0.1.0
 * @apiDescription 获取某一天 [now-5d, now+1d] 的全部排期, 只返回当前公会管理员所管理的主播, 公会会长可查看所有主播, 不在允许的时间范围内时返回空
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [date] 例如 "2006-01-02"，不传默认是今天
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [ // 排期未生成时或不在允许查询时间范围内时返回 nil, 旗下主播没有排上时返回空数组
 *       {
 *         "time": 1584806400,
 *         "data": [
 *           {
 *             "room_id": 22489473,
 *             "creator_id": 10,
 *             "creator_username": "bless",
 *             "tag": 0, // 直播推荐标签，0: 为公会申请推荐、1: 艺人主播、2: 优质主播、3: 神话推荐、4: 官签主播
 *             "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *             "start_time": 1584806400,
 *             "expire_time": 1584808200
 *           },
 *           ...
 *         ]
 *       },
 *       {
 *         "time": 1584808200,
 *         "data": []
 *       },
 *       ...
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRecommendScheduleList(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	day := c.GetDefaultParam("date", now.Format(util.TimeFormatYMD))
	paramTime, err := time.ParseInLocation(util.TimeFormatYMD, day, time.Local)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	beginningNow := goutil.BeginningOfDay(now)
	nextDayBeginning := beginningNow.AddDate(0, 0, 1)
	if paramTime.Before(beginningNow.AddDate(0, 0, -6)) ||
		paramTime.After(nextDayBeginning) ||
		(paramTime.Equal(nextDayBeginning) && now.Before(beginningNow.Add(20*time.Hour))) {
		// 不在允许的查询时间范围内或未到公布的时间
		return nil, nil
	}
	// 获取用户公会管理员角色
	role, err := guildrole.FindManagerRole(c.UserID(), true)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if role == nil {
		return nil, actionerrors.ErrForbidden
	}
	if !role.ExistsContractCreator() {
		return make([]*liveScheduleResult, 0), nil
	}
	var lrs []*liveschedulerecord.ScheduleRecord
	db := liveschedulerecord.ScheduleRecord{}.DB().Where("day = ?", paramTime.Unix()).
		Where("guild_id = ?", role.GuildInfo.ID)
	if role.Role.IsGuildAgent() {
		// 公会经纪人只能看到当前旗下主播的记录
		db = db.Where("creator_id IN (?)", role.CreatorIDs)
	}
	err = db.Find(&lrs).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(lrs) == 0 {
		return make([]*liveScheduleResult, 0), nil
	}
	userIDs := make([]int64, len(lrs))
	for i := range lrs {
		userIDs[i] = lrs[i].CreatorID
	}
	userIDs = util.Uniq(userIDs)
	roomMap, err := room.FindSimpleMapByCreatorID(userIDs, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	liveScheduleMap := make(map[int64][]*liveSchedule)
	for i := range lrs {
		s := &liveSchedule{
			RoomID:     lrs[i].RoomID,
			CreatorID:  lrs[i].CreatorID,
			Tag:        liverecommendedelements.AttrToTag(lrs[i].Attr),
			StartTime:  lrs[i].StartTime,
			ExpireTime: lrs[i].ExpireTime,
		}
		if r := roomMap[lrs[i].CreatorID]; r != nil {
			s.CreatorUsername = r.CreatorUsername
		}
		liveScheduleMap[lrs[i].StartTime] = append(liveScheduleMap[lrs[i].StartTime], s)
	}
	res := make([]*liveScheduleResult, 0, len(liveScheduleMap))
	for key, val := range liveScheduleMap {
		res = append(res, &liveScheduleResult{
			Time: key,
			Data: val,
		})
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].Time < res[j].Time
	})
	return res, nil
}

// ActionRecommendScheduleAbsenceRequest 首页推荐位请假申请
/**
 * @api {post} /api/v2/guild/recommend/schedule/absence/request 首页推荐位请假申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} apply_id 记录 ID
 * @apiParam {number=1,2} type 1：请假；2：换人
 * @apiParam {Number} [creator_id] 主播 ID 换人时需要传递
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionRecommendScheduleAbsenceRequest(*handler.Context) (handler.ActionResponse, error) {
	return nil, nil
}
