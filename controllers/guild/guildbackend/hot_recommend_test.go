package guildbackend

import (
	"fmt"
	"sort"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSquareHotList(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext("POST", fmt.Sprintf("list?creator_id=%d&creator_username=%s&position=%d", 123, "xxx", -1), true, nil)
	_, err := ActionSquareHotList(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("list?creator_id=%d&creator_username=%s&position=%d", 123, "xxx", 0), true, nil)
	_, err = ActionSquareHotList(c)
	require.NoError(err)
}

func TestNewSquareHotList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "list?position=-1", true, nil)
	_, err := newSquareHotList(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("list?creator_id=%d&creator_username=%s&position=%d", 123, "xxx", 4), true, nil)
	resp, err := newSquareHotList(c)
	require.NoError(err)
	require.NotNil(resp.guildRole)
	assert.Equal([]int64{12, 5, 10, 892}, resp.guildRole.CreatorIDs)
	resp.guildRole = nil
	assert.Equal(squareHotListParam{
		P:               1,
		PageSize:        20,
		Position:        4,
		CreatorID:       123,
		CreatorUsername: "xxx",
		GuildID:         3,
		UserID:          12,
	}, *resp)
	c = handler.NewTestContext("POST", fmt.Sprintf("list?creator_id=%d&creator_username=%s&position=%d", 123, "xxx", 4), false, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 4000005,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	_, err = newSquareHotList(c)
	require.Equal(actionerrors.ErrNoAuthority, err)
}

func TestSquareHotListParamList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := squareHotListParam{
		Position:  -1,
		GuildID:   1000005,
		guildRole: &guildrole.GuildManagerRole{},
	}
	resp, err := p.list()
	require.NoError(err)
	assert.Len(resp.Data, 0, "查不到记录")
	now := goutil.TimeNow()
	position := 12
	creatorID1 := int64(892)
	creatorID2 := int64(10)
	creatorID3 := int64(15)
	guildID := int64(3)
	referrerID := int64(12) // 公会 owner ID
	createTime := util.BeginningOfDay(now).Add(EffectiveDuration).Unix()
	p = squareHotListParam{
		Position:  position,
		GuildID:   guildID,
		CreatorID: 100,
		guildRole: &guildrole.GuildManagerRole{},
	}
	p.CreatorID = 100
	resp, err = p.list()
	require.NoError(err)
	assert.Len(resp.Data, 0, "查不到记录")

	p.CreatorID = creatorID1
	resp, err = p.list()
	require.NoError(err)
	assert.Len(resp.Data, 0, "分页非法")
	p.P = 1
	p.PageSize = 20
	baseRecord := recommend.GuildRecommendSquareHot{
		Position:     position,
		CreatorID:    creatorID1,
		GuildID:      p.GuildID,
		OperatorID:   referrerID,
		StartTime:    util.BeginningOfDay(now).Unix(),
		EndTime:      createTime,
		CreateTime:   createTime,
		ModifiedTime: createTime,
	}
	records := make([]recommend.GuildRecommendSquareHot, 5)
	for i := 0; i < len(records); i++ {
		records[i] = baseRecord
		records[i].CreateTime = createTime + int64(i)
	}
	records[1].CreatorID = creatorID2
	records[2].CreatorID = creatorID2
	records[3].CreatorID = creatorID3
	records[4].Position = 4
	require.NoError(service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).Delete("", "guild_id = ?", p.GuildID).Error)
	for i := 0; i < len(records); i++ {
		require.NoError(service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).Create(&records[i]).Error)
	}
	p.guildRole = &guildrole.GuildManagerRole{
		CreatorIDs: []int64{creatorID1, creatorID2, creatorID3},
	}
	p.CreatorID = creatorID1
	tutil.PrintJSON(p)
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 1)
	assert.Equal(records[0].CreateTime, resp.Data[0].CreateTime)
	p.CreatorUsername = "有直播间的用户"
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 1, "输入 ID 和正确输入了 ID 对应的用户名的情况下可以查到记录")
	assert.Equal(records[0].CreateTime, resp.Data[0].CreateTime)
	p.CreatorUsername = "错误，有直播间的用户"
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 0, "输入 ID 和错误输入了 ID 对应的用户名的情况下查不到记录")
	p.CreatorID = 0
	p.CreatorUsername = "bles"
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 2)
	assert.Equal(records[2].CreateTime, resp.Data[0].CreateTime)
	p.CreatorUsername = ""
	p.Position = 0
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 5)
	sort.Slice(resp.Data, func(i, j int) bool {
		return resp.Data[i].CreateTime < resp.Data[j].CreateTime
	})
	assert.Equal(records[0].CreateTime, resp.Data[0].CreateTime)
	assert.Equal(records[1].CreateTime, resp.Data[1].CreateTime)
	assert.Equal(records[2].CreateTime, resp.Data[2].CreateTime)
	assert.Equal(records[3].CreateTime, resp.Data[3].CreateTime)
	assert.Equal(records[4].CreateTime, resp.Data[4].CreateTime)

	// 测试经纪人查看记录
	agentID := int64(45)
	require.NoError(guildagent.AgentCreator{}.DB().Create(guildagent.AgentCreator{
		ID:        2021721,
		GuildID:   guildID,
		AgentID:   agentID,
		CreatorID: creatorID1,
	}).Error)
	defer guildagent.AgentCreator{}.DB().Delete("", "id = ?", 2021721)
	p = squareHotListParam{
		Position:  position,
		GuildID:   guildID,
		guildRole: &guildrole.GuildManagerRole{CreatorIDs: []int64{creatorID1}},
		UserID:    agentID,
		P:         1,
		PageSize:  20,
	}
	var role guildrole.GuildRole
	role.Set(guildrole.RoleGuildAgent)
	p.guildRole = &guildrole.GuildManagerRole{
		CreatorIDs: []int64{creatorID1},
		Role:       role,
	}
	resp, err = p.list()
	require.NoError(err)
	assert.Len(resp.Data, 1)
	assert.Equal(creatorID1, resp.Data[0].CreatorID)
}

func TestActionSquareHotVacancy(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext("GET", fmt.Sprintf("vacancy?position=%d", 0), true, nil)
	_, err := ActionSquareHotVacancy(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", fmt.Sprintf("vacancy?position=%d", 4), true, nil)
	_, err = ActionSquareHotVacancy(c)
	require.NoError(err)
	c = handler.NewTestContext("GET", fmt.Sprintf("vacancy?position=%d", 4), false, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 4000005,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	_, err = ActionSquareHotVacancy(c)
	require.Equal(actionerrors.ErrNoAuthority, err)
}

func TestActionSquareHotTime(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext("GET", fmt.Sprintf("time?position=%d", 0), true, nil)
	_, err := ActionSquareHotTime(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", fmt.Sprintf("time?position=%d", 4), true, nil)
	_, err = ActionSquareHotTime(c)
	require.NoError(err)
}

func TestDefaultBeginTime(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	assert.Equal(time.Date(2021, 7, 1, 0, 0, 0, 0, time.Local), defaultBeginTime())
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 20, 0, 0, 0, time.Local)
	})
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), defaultBeginTime())
}

func TestNewSquareHotTimeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	beginOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	c := handler.NewTestContext("POST", fmt.Sprintf("add?date=%s", "xxx"), true, nil)
	_, err := newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s", "xxx"), true, nil)
	_, err = newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s", beginOfToday.AddDate(0, 0, -1).Format(util.TimeFormatYMD)), true, nil)
	_, err = newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s", beginOfToday.Format(util.TimeFormatYMD)), true, nil)
	_, err = newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s&position=%d", beginOfToday.Format(util.TimeFormatYMD), 4), true, nil)
	resp, err := newSquareHotTimeParam(c)
	require.NoError(err)
	assert.Equal(beginOfToday, resp.Date)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s&position=%d", beginOfToday.Format(util.TimeFormatYMD), 4), false, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 4000005,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	_, err = newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrNoAuthority, err)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 30, 21, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	c = handler.NewTestContext("POST", fmt.Sprintf("add?date=%s&position=%d", "20210803", 4), true, nil)
	_, err = newSquareHotTimeParam(c)
	require.Equal(actionerrors.ErrParams, err)
}

func TestSquareHotTimeParamGetTime(t *testing.T) {
	require := require.New(t)

	position := 21
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 19, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	beginOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	p := squareHotTimeParam{
		Date:     beginOfToday,
		Position: 21,
	}
	startTime := beginOfToday.Add(EffectiveDuration).Unix()
	name := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	baseRecord1 := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &startTime,
		},
		ExpireTime: beginOfToday.Add(2 * EffectiveDuration).Unix(),
	}
	require.NoError(service.DB.Where("sort = ?", position).Delete(liverecommendedelements.Model{}).Error)
	require.NoError(service.DB.Create(&baseRecord1).Error)
	availableTime, err := p.getTime()
	require.NoError(err)
	require.NotNil(availableTime)
	require.Len(availableTime.AvailableDate, 7)
}

func TestAvailableDays(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 19, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	beginOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	_, periodEnd := currentPeriod()
	endTime := beginOfToday.AddDate(0, 0, 7)
	if endTime.After(periodEnd) {
		endTime = periodEnd
	}
	p := squareHotTimeParam{}
	date := p.availableDays(beginOfToday, endTime)
	require.NotNil(date)
	require.Len(date, 7)
	assert.Equal(beginOfToday.Format(util.TimeFormatYMD), date[0])
	assert.Equal(beginOfToday.AddDate(0, 0, 6).Format(util.TimeFormatYMD), date[6])
}

func TestAvailablePeriods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	position := 21
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 19, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	beginOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	p := squareHotTimeParam{
		Date:     beginOfToday,
		Position: position,
	}
	startTime := beginOfToday.Add(EffectiveDuration).Unix()
	name := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	baseRecord1 := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &startTime,
		},
		ExpireTime: beginOfToday.Add(2 * EffectiveDuration).Unix(),
	}
	require.NoError(service.DB.Where("sort = ?", position).Delete(liverecommendedelements.Model{}).Error)
	require.NoError(service.DB.Create(&baseRecord1).Error)
	periods, err := p.availablePeriods()
	require.NoError(err)
	require.Len(periods, 71)
	assert.Equal(beginOfToday.Unix(), periods[0].StartTime)
	assert.Equal(beginOfToday.Add(EffectiveDuration).Unix(), periods[0].EndTime)
	assert.Equal(beginOfToday.Add(2*EffectiveDuration).Unix(), periods[1].StartTime)
	assert.Equal(beginOfToday.Add(3*EffectiveDuration).Unix(), periods[1].EndTime)

	startTime2 := beginOfToday.Add(50 * time.Minute).Unix()
	baseRecord2 := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &startTime2,
		},
		ExpireTime: beginOfToday.Add(70 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(&baseRecord2).Error)
	startTime3 := beginOfToday.Add(80 * time.Minute).Unix()
	baseRecord3 := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &startTime3,
		},
		ExpireTime: beginOfToday.Add(150 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(&baseRecord3).Error)
	startTime4 := beginOfToday.Add(210 * time.Minute).Unix()
	baseRecord4 := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &startTime4,
		},
		ExpireTime: beginOfToday.Add(240 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(&baseRecord4).Error)
	periods, err = p.availablePeriods()
	require.NoError(err)
	require.Len(periods, 72-9)
	assert.Equal(beginOfToday.Unix(), periods[0].StartTime)
	assert.Equal(beginOfToday.Add(EffectiveDuration).Unix(), periods[0].EndTime)
	assert.Equal(beginOfToday.Add(8*EffectiveDuration).Unix(), periods[1].StartTime)
	assert.Equal(beginOfToday.Add(9*EffectiveDuration).Unix(), periods[1].EndTime)
	assert.Equal(beginOfToday.Add(9*EffectiveDuration).Unix(), periods[2].StartTime)
	assert.Equal(beginOfToday.Add(10*EffectiveDuration).Unix(), periods[2].EndTime)
	assert.Equal(beginOfToday.Add(12*EffectiveDuration).Unix(), periods[3].StartTime)
	assert.Equal(beginOfToday.Add(13*EffectiveDuration).Unix(), periods[3].EndTime)
	require.NoError(service.DB.Where("sort = ?", position).Delete(liverecommendedelements.Model{}).Error)
}

func TestActionSquareHotAdd(t *testing.T) {
	require := require.New(t)

	p := applyRecommendParams{}
	c := handler.NewTestContext("POST", "add", true, p)
	_, err := ActionSquareHotAdd(c)
	require.Equal(actionerrors.NewErrForbidden("未选择时间"), err)
	p.ApplyTime = util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1).Unix()
	c = handler.NewTestContext("POST", "add", true, p)
	_, err = ActionSquareHotAdd(c)
	require.Error(err, "无法找到 CreatorID")
	p.Position = 4
	p.CreatorID = 4000005
	p.ApplyTime = util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1).Unix()
	c = handler.NewTestContext("POST", "add", true, p)
	_, err = ActionSquareHotAdd(c)
	require.Error(err, "无法找到 CreatorID")
}

func TestCheckParams(t *testing.T) {
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 8, 5, 23, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	p := applyRecommendParams{}
	err := p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("未选择时间"), err)
	p.ApplyTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("未选择申请位置"), err)
	p.Position = 5
	err = p.checkParams()
	require.Equal(actionerrors.ErrParams, err)
	p.Position = 4
	err = p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("未选择主播"), err)
	p.CreatorID = 4000005
	p.ApplyTime = goutil.TimeNow().AddDate(0, 0, 8).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("推荐时间不可在当前周期外"), err)
	p.ApplyTime = util.BeginningOfDay(goutil.TimeNow()).Add(-time.Hour).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("推荐开始时间不能早于当前时间"), err)
	p.ApplyTime = util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 8).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.NewErrForbidden("推荐时间不可在当前周期外"), err)
	p.ApplyTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.ErrParams, err)
	p.ApplyTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.ErrParams, err)
	p.ApplyTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = p.checkParams()
	require.Equal(actionerrors.ErrParams, err)
	p.ApplyTime = util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1).Unix()
	err = p.checkParams()
	require.NoError(err)
}

func TestCheckBeforeAdd(t *testing.T) {
	require := require.New(t)

	role := guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	now := goutil.TimeNow()
	beginOfDay := goutil.BeginningOfDay(now)
	p := applyRecommendParams{
		CreatorID:   -1,
		managerRole: &guildrole.GuildManagerRole{Role: role},
		ApplyTime:   beginOfDay.AddDate(0, 0, 1).Unix(),
	}
	gr := recommend.GuildRecommendSquareHot{
		GuildID:    p.guildID,
		CreatorID:  p.CreatorID,
		Position:   p.Position,
		CreateTime: goutil.TimeNow().Unix(),
		StartTime:  beginOfDay.AddDate(0, 0, 1).Unix(),
		EndTime:    beginOfDay.AddDate(0, 0, 1).Add(20 * time.Minute).Unix(),
	}
	require.NoError(service.LiveDB.Create(&gr).Error)
	err := p.checkBeforeAdd()
	require.Equal(actionerrors.NewErrForbidden("该日申请次数已用完"), err)
	require.NoError(service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).Where("id = ?", gr.ID).Delete(recommend.GuildRecommendSquareHot{}).Error)
	startTime, endTime := currentPeriod()
	gv := recommend.GuildRecommendVacancy{
		Position:  p.Position,
		GuildID:   p.guildID,
		Vacancy:   12,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&gv).Error)
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.ErrCannotFindUser, err)
	p.CreatorID = 2
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.NewErrForbidden("直播间不存在"), err)
	p.CreatorID = 1
	p.Position = 6
	p.guildID = 3
	err = p.checkBeforeAdd()
	require.NoError(service.LiveDB.Table(recommend.GuildRecommendSquareHot{}.TableName()).Where("guild_id = ?", p.guildID).Delete("").Error)
	require.Equal(actionerrors.NewErrForbidden("当前本期推荐剩余次数为 0"), err)
	p.Position = 4
	p.guildID = 23333471
	gv = recommend.GuildRecommendVacancy{
		Position:  p.Position,
		GuildID:   p.guildID,
		Vacancy:   12,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&gv).Error)
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.ErrCreatorNotInYourGuild, err)

	guildID := int64(3)
	guildOwnerID := int64(12)
	creatorID := int64(892)
	p.guildID = guildID
	p.userID = creatorID
	p.CreatorID = creatorID
	gv = recommend.GuildRecommendVacancy{
		Position:  p.Position,
		GuildID:   p.guildID,
		Vacancy:   12,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&gv).Error)
	role = guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildAgent)
	p.managerRole = &guildrole.GuildManagerRole{Role: role}
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.ErrCreatorNotAssignedToYou, err)
	p.CreatorID = creatorID
	p.userID = guildOwnerID
	filter := bson.M{"creator_id": p.CreatorID}
	r, err := room.FindOne(filter, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NoError(guildrecommendblocklist.Add(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID))
	role = guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	p.managerRole = &guildrole.GuildManagerRole{Role: role, CreatorIDs: []int64{p.CreatorID}}
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.NewErrForbidden("该主播当前不可申请"), err, "直播间在推荐位黑名单")
	require.NoError(guildrecommendblocklist.GuildRecommendBlocklist{}.DB().Delete("", "element_id = ?", r.RoomID).Error)

	// 触发 lock
	elementsName := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	lock := lockKeyGuildRecommendHot(p.Position, elementsName, p.ApplyTime)
	err = service.Redis.Set(lock, p.guildID, 5*time.Second).Err()
	require.NoError(err)
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.NewErrForbidden("该时间段已有申请，请重新选择"), err)

	// 数据库数据占用
	require.NoError(service.Redis.Del(lock).Err())
	st := p.ApplyTime
	name := strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10)
	record := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        p.Position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      name,
			StartTime: &st,
		},
		ExpireTime: time.Unix(p.ApplyTime, 0).Add(EffectiveDuration).Unix(),
	}
	require.NoError(service.DB.Create(&record).Error)
	err = p.checkBeforeAdd()
	require.Equal(actionerrors.NewErrForbidden("该时间段已有申请，请重新选择"), err)
	require.NoError(service.DB.Where("name = ? AND sort = ?", name, p.Position).Delete(liverecommendedelements.Model{}).Error)
	err = p.checkBeforeAdd()
	require.NoError(err)
}

func TestApplyRecommendParamsAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	role := guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	p := applyRecommendParams{
		CreatorID:       -1,
		managerRole:     &guildrole.GuildManagerRole{Role: role},
		roomID:          123,
		creatorUsername: "xxx",
		guildID:         123,
		Position:        23,
	}
	startTime, endTime := currentPeriod()
	r := recommend.GuildRecommendVacancy{
		Position:  p.Position,
		GuildID:   p.guildID,
		Vacancy:   1,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&r).Error)
	resp, err := p.add()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(p.CreatorID, resp.CreatorID)
	assert.Equal(p.ApplyTime, resp.StartTime)
}

func TestGuildHotRecommendVacancy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 8, 1, 23, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	guildID := int64(21)
	position := 4
	gv := recommend.GuildRecommendVacancy{
		Position:  position,
		GuildID:   guildID,
		Vacancy:   12,
		StartTime: time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local).Unix(),
		EndTime:   time.Date(2021, 9, 2, 0, 0, 0, 0, time.Local).Unix(),
	}
	require.NoError(service.LiveDB.Create(&gv).Error)
	vacancy, err := guildHotRecommendVacancy(guildID, position)
	require.NoError(err)
	assert.Equal(int64(12), vacancy)
}

func TestCreatorApplyCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorID := int64(231)
	applyTime := goutil.BeginningOfDay(goutil.TimeNow()).Unix()
	position := 123
	count, err := creatorApplyCount(applyTime, creatorID, position)
	require.NoError(err)
	assert.Zero(count)
	v1 := recommend.GuildRecommendSquareHot{
		CreatorID: creatorID,
		Position:  position,
		StartTime: applyTime,
		EndTime:   applyTime + int64(30*time.Minute),
	}
	require.NoError(v1.DB().Create(&v1).Error)
	require.NoError(err)
	count, err = creatorApplyCount(applyTime, creatorID, position)
	require.NoError(err)
	assert.Equal(1, count)
	require.NoError(v1.DB().Where("id = ?", v1.ID).Delete("").Error)
}

func TestCheckUserAuthority(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, _, err := checkUserAuthority(4000005, false)
	require.Equal(actionerrors.ErrNoAuthority, err)
	r, id, err := checkUserAuthority(23333471, false)
	require.NoError(err)
	assert.True(r.Role.IsGuildLiveCreator())
	assert.Equal(int64(23333471), id)
}

func TestCurrentPeriod(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	startTime, endTime := currentPeriod()
	assert.Equal(time.Date(2021, 6, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 21, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 6, 15, 1, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 6, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 2, 1, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 12, 2, 1, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 12, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2022, 1, 2, 0, 0, 0, 0, time.Local), endTime)
}

func TestInNextPeriod(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	assert.False(inNextPeriod(goutil.TimeNow()))
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 20, 0, 0, 0, time.Local)
	})
	assert.True(inNextPeriod(goutil.TimeNow()))
}

func TestGetDay2OfMonth(t *testing.T) {
	assert := assert.New(t)

	now := time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), day2OfMonth(now))
}

func TestGetDay2OfNextMonth(t *testing.T) {
	assert := assert.New(t)

	now := time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local), getDay2OfNextMonth(now))
}

func TestValidPosition(t *testing.T) {
	assert := assert.New(t)
	assert.True(validPosition(4))
	assert.True(validPosition(6))
	assert.False(validPosition(0))
	assert.False(validPosition(8))
}
