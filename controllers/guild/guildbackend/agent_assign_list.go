package guildbackend

import (
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

// ActionGuildAgentAssignList 主播管理页 - 公会获取待分配的经纪人列表
/**
 * @api {get} /api/v2/guild/agent/assign-list 主播管理页 - 公会获取待分配的经纪人列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {String} [search_word] 经纪人 ID 或昵称
 * @apiParam {String} [sort=user_id.asc] 排序方式（支持 user_id 与 creator_num 的排序）
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [{
 *           "user_id": 43389,  // 经纪人用户 ID
 *           "username": "AAA",  // 经纪人昵称
 *           "iconurl": "http://foo.com/bar.png",  // 经纪人头像
 *           "creator_num": 4
 *         }]
 *         "pagination": {
 *           "count": 3,
 *           "p": 1,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *       }
 *     }
 */
func ActionGuildAgentAssignList(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(agentListParam)
	if err := param.load(ctx); err != nil {
		return nil, err
	}
	resp, err := guildAgentList(ctx, param, func(ctx *handler.Context, db *gorm.DB) *gorm.DB {
		return db.Where("ga.agent_id <> ?", ctx.User().ID)
	})
	if err != nil {
		return nil, err
	}
	// 将公会会长添加至最后一项
	if param.SearchWord == "" || param.isSearchGuildOwner(ctx) {
		resp.Pagination = util.MakePagination(resp.Pagination.Count+1, param.Page, param.PageSize)
		if resp.Pagination.P == resp.Pagination.MaxPage || param.isSearchGuildOwner(ctx) {
			var creatorNum int
			err = guildagent.AgentCreator{}.DB().
				Where("agent_id = ? AND delete_time = 0", ctx.User().ID).Count(&creatorNum).Error
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			resp.Data = append(resp.Data, &agentListItem{
				Simple: mowangskuser.Simple{
					Username: ctx.User().Username,
					IconURL:  ctx.User().IconURL,
				},
				ID:         ctx.User().ID,
				CreatorNum: creatorNum,
			})
		}
	}

	return resp, err
}

func (param *agentListParam) isSearchGuildOwner(ctx *handler.Context) bool {
	return param.SearchWord != "" && ((param.isSearchWordInteger && param.searchWordInteger == ctx.User().ID) || strings.Contains(ctx.User().Username, param.SearchWord))
}
