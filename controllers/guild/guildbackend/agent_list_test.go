package guildbackend

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestLoadAgentListParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var param agentListParam
	// 正常情况
	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/agent/list?search_word=阿&sort=creator_num.desc&p=2&pagesize=10", true, nil)
	err := param.load(ctx)
	require.NoError(err)
	assert.Equal("阿", param.SearchWord)
	assert.Equal(int64(2), param.Page)
	assert.Equal(int64(10), param.PageSize)
	assert.Equal("creator_num DESC", param.sort)

	// 参数错误
	ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/agent/list?search_word=阿&sort=guild_id.desc&p=2&pagesize=10", true, nil)
	err = param.load(ctx)
	require.EqualError(err, actionerrors.ErrParams.Message)
}

func TestApplySearch(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testGuildID := int64(100)
	require.NoError(guildagent.GuildAgent{}.DB().Create(&guildagent.GuildAgent{
		GuildID: testGuildID,
		AgentID: 200,
	}).Error)
	require.NoError(guildagent.GuildAgent{}.DB().Create(&guildagent.GuildAgent{
		GuildID: testGuildID,
		AgentID: 300,
	}).Error)
	defer func() {
		require.NoError(guildagent.GuildAgent{}.DB().Delete(nil, "guild_id = ?", testGuildID).Error)
	}()
	require.NoError(service.DB.Create(&user.MowangskUser{
		Simple: user.Simple{
			ID:       300,
			UserName: "测试用户 300",
			Confirm:  0,
		},
		CIP:   "127.0.0.1",
		UIP:   "127.0.0.1",
		CTime: util.TimeNow().Unix(),
		UTime: util.TimeNow().Unix(),
	}).Error)
	defer func() {
		require.NoError(service.DB.Table(user.MowangskUser{}.TableName()).Delete(nil, "id = ?", 300).Error)
	}()

	db := guildagent.GuildAgent{}.DB().Table(guildagent.GuildAgent{}.TableName()+" AS ga").
		Where("guild_id = ? AND delete_time = 0", testGuildID)
	var count int
	require.NoError(db.Count(&count).Error)
	assert.Equal(2, count)

	var param agentListParam

	// 搜索经纪人 ID
	param.SearchWord = "200"
	param.isSearchWordInteger = true
	param.searchWordInteger = 200
	db, err := param.applySearch(db, testGuildID)
	require.NoError(err)
	require.NoError(db.Count(&count).Error)
	assert.Equal(1, count)

	// 搜索经纪人昵称（空结果）
	db2 := guildagent.GuildAgent{}.DB().Table(guildagent.GuildAgent{}.TableName()+" AS ga").
		Where("guild_id = ? AND delete_time = 0", testGuildID)
	param.SearchWord = "测试用户8888"
	param.isSearchWordInteger = false
	param.searchWordInteger = 0
	db2, err = param.applySearch(db2, testGuildID)
	require.NoError(err)
	assert.Nil(db2)

	// 搜索经纪人昵称
	db2 = guildagent.GuildAgent{}.DB().Table(guildagent.GuildAgent{}.TableName()+" AS ga").
		Where("guild_id = ? AND delete_time = 0", testGuildID)
	param.SearchWord = "测试用户"
	db2, err = param.applySearch(db2, testGuildID)
	require.NoError(err)
	require.NoError(db2.Count(&count).Error)
	assert.Equal(1, count)
}

func TestFillAgentUserInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var userIDs []int64
	err := service.DB.Table(user.MowangskUser{}.TableName()).
		Select("id").Limit(2).
		Pluck("id", &userIDs).Error
	require.NoError(err)
	assert.GreaterOrEqual(len(userIDs), 1)
	agentList := make([]*agentListItem, len(userIDs))
	for i, userID := range userIDs {
		agentList[i] = &agentListItem{
			ID: userID,
		}
	}
	err = fillAgentUserInfo(agentList)
	require.NoError(err)
	for _, item := range agentList {
		assert.NotEmpty(item.Username)
		assert.NotEmpty(item.IconURL)
	}
}

func TestGuildAgentList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 需要为公会会长才能操作
	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/agent/list", true, nil)
	ctx.User().ID = 99998888
	ctx.User().Username = "test_username"
	ctx.User().IconURL = "http://foo.com/bar.png"
	_, err := guildAgentList(ctx, &agentListParam{})
	require.EqualError(err, actionerrors.ErrYouAreNotGuildOwner.Message)

	// 正常情况
	testGuild := &guild.Guild{
		Name:                      "火锅公会",
		Intro:                     "火锅公会的简介",
		OwnerName:                 "张火锅",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://foo.png",
		OwnerBackcover:            "oss://bar.png",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "10001",
		CorporationName:           "火锅公司",
		CorporationAddress:        "火锅店",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "*********",
		BusinessLicenseFrontcover: "oss://abc.png",
		TaxAccount:                "*********",
		BankAccount:               "*********",
		BankAccountName:           "火锅公司",
		Bank:                      "测试银行",
		BankAddress:               "火锅店",
		BankBranch:                "火锅支行",
		InvoiceRate:               guild.InvoiceRateNoIssue,
		Type:                      guild.TypeEncrypted,
		Checked:                   guild.CheckedPass,
		UserID:                    ctx.User().ID,
		ApplyTime:                 util.TimeNow().Unix(),
	}
	err = testGuild.DB().Create(testGuild).Error
	require.NoError(err)
	require.NoError(guildagent.GuildAgent{}.DB().Create(&guildagent.GuildAgent{
		GuildID: testGuild.ID,
		AgentID: 12,
	}).Error)
	require.NoError(guildagent.GuildAgent{}.DB().Create(&guildagent.GuildAgent{
		GuildID: testGuild.ID,
		AgentID: 15,
	}).Error)
	require.NoError(guildagent.AgentCreator{}.DB().Create(&guildagent.AgentCreator{
		GuildID:   testGuild.ID,
		AgentID:   12,
		CreatorID: 3000,
	}).Error)
	defer func() {
		require.NoError(testGuild.DB().Delete(nil, "id = ?", testGuild.ID).Error)
		require.NoError(guildagent.GuildAgent{}.DB().Delete(nil, "guild_id = ?", testGuild.ID).Error)
		require.NoError(guildagent.AgentCreator{}.DB().Delete(nil, "guild_id = ?", testGuild.ID).Error)
	}()
	resp, err := guildAgentList(ctx, &agentListParam{
		searchAgentParam: searchAgentParam{
			Page:     1,
			PageSize: 20,
		},
		sort: "user_id DESC",
	})
	require.NoError(err)
	require.Len(resp.Data, 2)
	assert.Equal(int64(15), resp.Data[0].ID)
	assert.Equal(0, resp.Data[0].CreatorNum)
	assert.Equal(int64(12), resp.Data[1].ID)
	assert.Equal(1, resp.Data[1].CreatorNum)

	resp, err = guildAgentList(ctx, &agentListParam{
		searchAgentParam: searchAgentParam{
			Page:     1,
			PageSize: 20,
		},
		sort: "creator_num ASC",
	})
	require.NoError(err)
	require.Len(resp.Data, 2)
	assert.Equal(int64(15), resp.Data[0].ID)
	assert.Equal(0, resp.Data[0].CreatorNum)
	assert.Equal(int64(12), resp.Data[1].ID)
	assert.Equal(1, resp.Data[1].CreatorNum)

	ret, err := ActionGuildAgentList(ctx)
	require.NoError(err)
	result, ok := ret.(*agentListResp)
	require.True(ok)
	assert.Len(result.Data, 2)

	ret, err = ActionGuildAgentAssignList(ctx)
	require.NoError(err)
	result, ok = ret.(*agentListResp)
	require.True(ok)
	assert.Len(result.Data, 3)
	assert.Equal(ctx.User().ID, result.Data[2].ID)
}

func TestIsSearchGuildOwner(t *testing.T) {
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/agent/list", true, nil)
	param := &agentListParam{}
	assert.False(param.isSearchGuildOwner(ctx))

	param.SearchWord = ctx.User().Username
	assert.True(param.isSearchGuildOwner(ctx))

	param.SearchWord = fmt.Sprintf("%d", ctx.UserID())
	param.isSearchWordInteger = true
	param.searchWordInteger = ctx.UserID()
	assert.True(param.isSearchGuildOwner(ctx))
}
