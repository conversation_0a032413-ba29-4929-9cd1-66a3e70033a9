package guildbackend

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildbanner"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionBannerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/list", true, nil)
	resp, err := ActionBannerList(c)
	require.NoError(err)
	r := resp.(bannerListResp)
	assert.Len(r.Data, 2)
}

func TestNewBannerListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/list", true, nil)
	c.User().ID = 123444
	_, err := newBannerListParam(c)
	require.EqualError(err, actionerrors.ErrNoAuthority.Message)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/list", true, nil)
	_, err = newBannerListParam(c)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/list?creator_id=12", true, nil)
	param, err := newBannerListParam(c)
	require.NoError(err)
	assert.Equal(int64(3), param.guildID)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/list?creator_id=12&creator_username=零", true, nil)
	param, err = newBannerListParam(c)
	require.NoError(err)
	assert.Len(param.creatorIDs, 1)
	assert.Equal(int64(12), param.creatorIDs[0])
}

func TestBannerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := bannerListParam{
		guildID:    222,
		p:          1,
		pageSize:   10,
		creatorIDs: []int64{12},
	}
	err := param.bannerList()
	require.NoError(err)
	require.NotNil(param.resp)
	assert.Len(param.resp.Data, 1)
	assert.Equal("零月", param.resp.Data[0].CreatorUsername)
	assert.Equal(int64(1), param.resp.Pagination.Count)
	assert.Equal(int64(1), param.resp.Pagination.MaxPage)
}

func TestActionBannerAdd(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return goutil.BeginningOfWeek(now).Add(16 * time.Hour)
	})
	defer goutil.SetTimeNow(nil)
	body := bannerAddParam{
		CreatorID: 122,
		Title:     "testAdd",
		ImageURL:  "https://fm.example.com/testdata/test.png",
		Periods: []bannerRequestPeriod{
			{
				StartTime: util.EndOfWeek(now).Unix(),
				endTime:   util.EndOfWeek(now).Add(time.Hour).Unix(),
			},
		},
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	_, err := ActionBannerAdd(c)
	assert.EqualError(err, "主播未加入您的公会")
}

func TestNewBannerAddParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return goutil.BeginningOfWeek(now)
	})
	defer goutil.SetTimeNow(nil)
	cannel := mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []scan.CheckResult{
			{Pass: true},
		}, nil
	})
	defer cannel()
	body := bannerAddParam{
		CreatorID: 12123,
		Title:     "testAdd",
		ImageURL:  "https://fm.example.com/testdata/test.png",
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	_, err := newBannerAddParam(c)
	assert.EqualError(err, "不在申请时间范围内")

	goutil.SetTimeNow(func() time.Time {
		return goutil.BeginningOfWeek(now).Add(16 * time.Hour)
	})
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	_, err = newBannerAddParam(c)
	assert.EqualError(err, actionerrors.ErrParams.Message)

	body.Periods = []bannerRequestPeriod{
		{
			StartTime: util.EndOfWeek(now).Unix(),
			endTime:   util.EndOfWeek(now).Add(time.Hour).Unix(),
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	_, err = newBannerAddParam(c)
	assert.EqualError(err, "主播未加入您的公会")

	body.CreatorID = 12
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	c.User().ID = 122
	_, err = newBannerAddParam(c)
	assert.EqualError(err, "您没有权限查看该申请")
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	param, err := newBannerAddParam(c)
	require.NoError(err)
	require.NotEmpty(param)
	assert.Equal(int64(12), param.CreatorID)
	assert.Len(param.Periods, 1)

	body.Periods = []bannerRequestPeriod{
		{
			StartTime: now.Unix(),
			endTime:   now.Add(time.Hour).Unix(),
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/recommend/banner/add", true, body)
	_, err = newBannerAddParam(c)
	assert.EqualError(err, "存在过期时间段")
}

func TestBannerAdd(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := bannerAddParam{
		CreatorID: 12121,
		Title:     "testAdd",
		ImageURL:  "https://fm.example.com/testdata/test.png",
		Periods: []bannerRequestPeriod{
			{
				StartTime: 1626422400,
				endTime:   1626426000,
			},
			{
				StartTime: 1626426000,
				endTime:   1626429600,
			},
		},
	}
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 9, 15, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	err := param.bannerAdd()
	require.NoError(err)

	param = bannerAddParam{
		CreatorID: 12121,
		Title:     "testAdd",
		ImageURL:  "https://fm.example.com/testdata/test.png",
		Periods: []bannerRequestPeriod{
			{
				StartTime: 1626418800,
				endTime:   1626422400,
			},
		},
	}
	err = param.bannerAdd()
	assert.EqualError(err, "当前时段已满，请重新选择时间段")
}

func TestActionBannerVacancy(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	day := util.BeginningOfWeek(goutil.TimeNow().AddDate(0, 0, 7))
	banner := guildbanner.GuildRecommendBanner{
		RoomID:    1234,
		GuildID:   111,
		CreatorID: 123455,
		Title:     "test",
		StartTime: day.Unix(),
		EndTime:   day.Add(time.Hour).Unix(),
	}
	require.NoError(service.LiveDB.Create(&banner).Error)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/recommend/banner/vacancy", true, nil)
	resp, err := ActionBannerVacancy(ctx)
	require.NoError(err)
	r := resp.([]bannerVacancyResp)
	assert.Len(r, 7)
}

func TestBannerElementCountArray(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 9, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	endDay := util.EndOfWeek(now).AddDate(0, 0, 7)
	element, err := bannerElementCountArray(util.EndOfWeek(now), endDay)
	require.NoError(err)
	assert.Equal(int64(3), element[0])
	assert.Equal(int64(3), element[1])
	assert.Equal(int64(2), element[2])
	assert.Equal(int64(3), element[23])
	assert.Equal(int64(3), element[len(element)-1])
}
