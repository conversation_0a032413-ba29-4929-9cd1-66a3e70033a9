package guildbackend

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGuildSearchAgent(t *testing.T) {
	require := require.New(t)

	// 不是公会会长
	c := handler.NewTestContext(
		http.MethodGet, "/api/v2/guild/agent/search?search_word=1&pagesize=5", true, nil)
	c.User().ID = 99988877776
	_, err := ActionGuildSearchAgent(c)
	require.EqualError(err, actionerrors.ErrYouAreNotGuildOwner.Message)

	guildInfo := new(guild.Guild)
	require.NoError(
		guildInfo.DB().
			Where("checked = ?", guild.CheckedPass).
			First(guildInfo).Error,
	)
	c.User().ID = guildInfo.UserID
	_, err = ActionGuildSearchAgent(c)
	require.NoError(err)
}

func TestSearchAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := searchAgentParam{
		SearchWord:          "1",
		searchWordInteger:   1,
		isSearchWordInteger: true,
		Page:                1,
		PageSize:            20,
	}
	resp, err := param.searchAgent()
	require.NoError(err)
	assert.Len(resp.Data, 1)
	assert.Equal(int64(1), resp.Pagination.Count)
	assert.Equal(int64(1), resp.Data[0].ID)
	assert.NotEmpty(resp.Data[0].Username)
	assert.NotEmpty(resp.Data[0].IconURL)

	param = searchAgentParam{
		SearchWord: "猫耳",
		Page:       1,
		PageSize:   20,
	}
	resp, err = param.searchAgent()
	require.NoError(err)
	assert.GreaterOrEqual(1, len(resp.Data))
	assert.True(strings.HasPrefix(resp.Data[0].Username, param.SearchWord))
}

func TestLoadSearchAgentParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var param searchAgentParam

	// 参数错误
	c := handler.NewTestContext(
		http.MethodGet, "/api/v2/guild/agent/search?search_word=111&p=-200&pagesize=5", true, nil)
	err := param.load(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	// 正常情况
	c = handler.NewTestContext(
		http.MethodGet, "/api/v2/guild/agent/search?p=2&pagesize=5", true, nil)
	err = param.load(c)
	require.NoError(err)
	assert.Equal("", param.SearchWord)
	assert.Equal(int64(2), param.Page)
	assert.Equal(int64(5), param.PageSize)

	c = handler.NewTestContext(
		http.MethodGet, "/api/v2/guild/agent/search?search_word=我&p=2&pagesize=5", true, nil)
	err = param.load(c)
	require.NoError(err)
	assert.Equal(int64(2), param.Page)
	assert.Equal(int64(5), param.PageSize)
	assert.Equal("我", param.SearchWord)
	assert.False(param.isSearchWordInteger)
	assert.Equal(int64(0), param.searchWordInteger)

	c = handler.NewTestContext(
		http.MethodGet, "/api/v2/guild/agent/search?search_word=24500&pagesize=5", true, nil)
	err = param.load(c)
	require.NoError(err)
	assert.Equal(int64(1), param.Page)
	assert.Equal(int64(5), param.PageSize)
	assert.Equal("24500", param.SearchWord)
	assert.True(param.isSearchWordInteger)
	assert.Equal(int64(24500), param.searchWordInteger)
}
