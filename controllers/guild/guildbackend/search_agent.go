package guildbackend

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

type searchAgentParam struct {
	Page       int64
	PageSize   int64
	SearchWord string

	isSearchWordInteger bool
	searchWordInteger   int64
}

type searchAgentResp struct {
	Data       []*mowangskuser.Simple `json:"data"`
	Pagination util.Pagination        `json:"pagination"`
}

// ActionGuildSearchAgent 搜索经纪人
/**
 * @api {get} /api/v2/guild/agent/search 搜索经纪人
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {String} search_word 经纪人 ID 或昵称
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [{
 *           "user_id": 43389,  // 经纪人用户 ID
 *           "username": "AAA",  // 经纪人昵称
 *           "iconurl": "http://foo.com/bar.png"  // 经纪人头像
 *         }]
 *         "pagination": {
 *           "count": 3,
 *           "p": 1,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *       }
 *     }
 */
func ActionGuildSearchAgent(c *handler.Context) (handler.ActionResponse, error) {
	var param searchAgentParam
	if err := param.load(c); err != nil || param.SearchWord == "" {
		if err != nil {
			return nil, err
		}
		return nil, actionerrors.ErrParams
	}
	g, err := guildrole.IsGuildOwner(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrYouAreNotGuildOwner
	}

	return param.searchAgent()
}

func (param *searchAgentParam) load(c *handler.Context, moreFunc ...func(*searchAgentParam) error) error {
	var err error
	param.Page, param.PageSize, err = c.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	param.SearchWord = c.GetDefaultParamString("search_word", "")
	if param.SearchWord != "" {
		param.searchWordInteger, err = strconv.ParseInt(param.SearchWord, 10, 64)
		if err == nil {
			param.isSearchWordInteger = true
		}
	}

	return nil
}

func (param *searchAgentParam) searchAgent() (resp searchAgentResp, err error) {
	db := service.DB.Table(mowangskuser.TableName()).
		Select("id, username, userintro, avatar, icontype, boardiconurl").
		Order("id ASC")
	if param.isSearchWordInteger {
		db = db.Where("id = ?", param.searchWordInteger)
	} else {
		// TODO: 实际业务中没有使用用户名搜索，之后可以删除
		db = db.Where("username LIKE ?", servicedb.ToRightLikeStr(param.SearchWord))
	}

	var count int64
	if err = db.Count(&count).Error; err != nil {
		return
	}
	resp.Pagination = util.MakePagination(count, param.Page, param.PageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]*mowangskuser.Simple, 0)
		return
	}
	if err = resp.Pagination.ApplyTo(db).Find(&resp.Data).Error; err != nil {
		return
	}
	if err = mowangskuser.BuildUserIconURL(resp.Data); err != nil {
		return
	}

	return
}
