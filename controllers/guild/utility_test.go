package guild

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
)

func TestGetUserPassedGuildID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.DB.Table(guild.TableName()).Delete("", "user_id = 0").Error)
	_, err := GetUserPassedGuildID(0)
	assert.Equal(actionerrors.ErrGuildNotExist, err)

	var guildInfo guild.Guild
	require.NoError(service.DB.Table(guild.TableName()).Where("checked = ?", guild.CheckedPass).First(&guildInfo).Error)
	id, err := GetUserPassedGuildID(guildInfo.UserID)
	require.NoError(err)
	assert.Equal(guildInfo.ID, id)
}

func TestParseSortStr(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("aaa", parseSortStr("aaa"))
	assert.Equal("aaa DESC", parseSortStr("aaa.desc"))
	assert.Equal("desc", parseSortStr("desc"))
	assert.Equal(".desc DESC", parseSortStr(".desc.desc"))
}
