package guild

import (
	"regexp"
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type createGuildResp struct {
	Success bool              `json:"success"`
	Message string            `json:"message,omitempty"`
	Errors  map[string]string `json:"errors,omitempty"`
}

// 身份证号
var regIDNumber = regexp.MustCompile(`^\d{17}[0-9Xx]$`)

// 邮箱
var regEmail = regexp.MustCompile(`^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,})$`)

type guildParams struct {
	Name                         string           `form:"name" json:"name" binding:"required"`
	Intro                        string           `form:"intro" json:"intro" binding:"required"`
	Mobile                       string           `form:"mobile" json:"mobile" binding:"required"`
	Email                        string           `form:"email" json:"email" binding:"required"`
	QQ                           string           `form:"qq" json:"qq" binding:"required"`
	OwnerName                    string           `form:"owner_name" json:"owner_name" binding:"required"`
	OwnerIDNumber                string           `form:"owner_id_number" json:"owner_id_number" binding:"required"`
	OwnerIDPeopleURL             upload.SourceURL `form:"owner_id_people_url" json:"owner_id_people_url" binding:"required"`
	OwnerBackcoverURL            upload.SourceURL `form:"owner_backcover_url" json:"owner_backcover_url" binding:"required"`
	CorporationName              string           `form:"corporation_name" json:"corporation_name" binding:"required"`
	CorporationAddress           string           `form:"corporation_address" json:"corporation_address" binding:"required"`
	CorporationPhone             string           `form:"corporation_phone" json:"corporation_phone" binding:"required"`
	BusinessLicenseNumber        string           `form:"business_license_number" json:"business_license_number" binding:"required"`
	BusinessLicenseFrontcoverURL upload.SourceURL `form:"business_license_frontcover_url" json:"business_license_frontcover_url" binding:"required"`
	TaxAccount                   string           `form:"tax_account" json:"tax_account" binding:"required"`
	BankAccount                  string           `form:"bank_account" json:"bank_account" binding:"required"`
	BankAccountName              string           `form:"bank_account_name" json:"bank_account_name" binding:"required"`
	Bank                         string           `form:"bank" json:"bank" binding:"required"`
	BankAddress                  string           `form:"bank_address" json:"bank_address" binding:"required"`
	BankBranch                   string           `form:"bank_branch" json:"bank_branch" binding:"required"`
	InvoiceRate                  int              `form:"invoice_rate" json:"invoice_rate" binding:"required"`
	IdentifyCode                 string           `form:"identify_code" json:"identify_code"`

	regionMobile string
}

// ActionEditGuild 创建公会
/**
 * @api {post} api/v2/guild/editguild 创建公会
 * @apiName editguild
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} name 公会名称
 * @apiParam {String} intro 公会简介
 * @apiParam {String} mobile 法人代表手机号
 * @apiParam {String} email 邮箱
 * @apiParam {String} qq QQ 号
 * @apiParam {String} owner_name 法人代表姓名
 * @apiParam {String} owner_id_number 法人代表身份证号
 * @apiParam {String} owner_id_people_url 法人代表手持身份证正面照
 * @apiParam {String} owner_backcover_url 法人代表身份证背面
 * @apiParam {String} corporation_name 公司名称
 * @apiParam {String} corporation_address 公司地址
 * @apiParam {String} corporation_phone 公司电话
 * @apiParam {String} business_license_number 营业执照号
 * @apiParam {String} business_license_frontcover_url 营业执照扫描件
 * @apiParam {String} tax_account 纳税人识别号
 * @apiParam {String} bank_account 银行卡号
 * @apiParam {String} bank_account_name 银行开户名
 * @apiParam {String} bank 开户行
 * @apiParam {String} bank_address 开户行所在地
 * @apiParam {String} bank_branch 开户支行
 * @apiParam {number=-2,-1} invoice_rate 发票税率（-2 未知的发票税率，-1 不开具发票）
 * @apiParam {String} identify_code 手机验证码
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": true,
 *       "message": "申请成功",
 *     }
 *   }
 *
 * @apiSuccessExample {json} 参数格式错误
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": false,
 *       "errors": {
 *         "email": "邮箱格式错误",
 *         "qq": "QQ 号填写有误",
 *         "owner_id_number": "请输入正确身份证号",
 *         "invoice_rate": "请选择是否开具发票"
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code *********, *********
 * @apiError (500) {String} info 服务器内部错误, 数据库错误
 *
 * @apiError (403) {Number} code 501010006
 * @apiError (403) {String} info 验证码错误
 *
 * @apiError (403) {Number} code 500050010
 * @apiError (403) {String} info 已过审公会不能再次编辑哦
 *
 */
func ActionEditGuild(c *handler.Context) (handler.ActionResponse, error) {
	var params guildParams
	// 获取的参数同时根据 tag binding 进行参数空值的校验
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	errs := params.check()
	if len(errs) > 0 {
		return &createGuildResp{Errors: errs}, nil
	}

	isNew := false
	var dbRecord guild.Guild
	err = service.DB.Table(guild.TableName()).Where("user_id = ?", c.User().ID).
		Where("checked IN (?)", []int64{guild.CheckedReject, guild.CheckedChecking, guild.CheckedPass}).
		Find(&dbRecord).Error
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
		isNew = true
	}
	if isNew {
		role, _, err := guildrole.UserGuildRole(c.UserID())
		if err != nil {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
		if role.IsGuildManager() {
			if role.IsGuildAgent() {
				return nil, actionerrors.ErrAgentCannotCreateGuild
			}
			return nil, actionerrors.ErrUserAlreadyJoinedOtherGuild
		}
	}
	if !isNew {
		err = dbRecord.Decrypt()
		if err != nil {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
	}
	// 判断是否已过审
	if !isNew && dbRecord.Checked == guild.CheckedPass {
		return nil, actionerrors.ErrCannotEditPassed
	}

	// 若有修改手机号或编辑已拒审公会信息时，需要验证短信验证码，创建公会目前仅针对国内手机号
	needDeleteVCode := false
	if isNew || dbRecord.Checked == guild.CheckedReject || dbRecord.Mobile != params.Mobile {
		ok, err := vcode.CheckIdentifyCode(params.regionMobile, params.IdentifyCode, vcode.ObjectiveTypeCreateGuild)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			errs["mobile"] = "验证码错误"
			return &createGuildResp{Errors: errs}, nil
		}
		needDeleteVCode = true
	}
	// 判断公会名称是否重名
	if params.Name != dbRecord.Name {
		exists, err := guild.ExistsByName(params.Name)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if exists {
			return nil, actionerrors.ErrGuildNameExists
		}
	}

	// 名称及简介
	dbRecord.Intro = params.Intro
	dbRecord.Name = params.Name
	// 联系方式
	dbRecord.Mobile = params.Mobile
	dbRecord.Email = params.Email
	dbRecord.QQ = params.QQ
	// 法人信息
	dbRecord.OwnerName = params.OwnerName
	dbRecord.OwnerIDNumber = params.OwnerIDNumber
	// 公司主体信息
	dbRecord.CorporationName = params.CorporationName
	dbRecord.CorporationAddress = params.CorporationAddress
	dbRecord.CorporationPhone = params.CorporationPhone
	// 营业执照信息
	dbRecord.BusinessLicenseNumber = params.BusinessLicenseNumber
	dbRecord.TaxAccount = params.TaxAccount
	// 银行卡信息
	dbRecord.BankAccount = params.BankAccount
	dbRecord.BankAccountName = params.BankAccountName
	dbRecord.Bank = params.Bank
	dbRecord.BankAddress = params.BankAddress
	dbRecord.BankBranch = params.BankBranch
	dbRecord.InvoiceRate = params.InvoiceRate

	dbRecord.Checked = guild.CheckedChecking
	dbRecord.UserID = c.UserID()
	dbRecord.ApplyTime = goutil.TimeNow().Unix()

	// 上传手持身份证图片到 OSS
	dbRecord.OwnerIDPeople, err = checkThenUpload(params.OwnerIDPeopleURL)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
	}
	// 上传身份证背面（国徽面）图片到 OSS
	dbRecord.OwnerBackcover, err = checkThenUpload(params.OwnerBackcoverURL)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
	}
	// 上传营业执照图片到 OSS
	dbRecord.BusinessLicenseFrontcover, err = checkThenUpload(params.BusinessLicenseFrontcoverURL)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
	}
	err = service.DB.Save(&dbRecord).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 操作成功删除验证码
	if needDeleteVCode {
		vcode.DelIdentifyCode(params.regionMobile)
	}
	return &createGuildResp{Success: true, Message: "申请成功"}, nil
}

func (p *guildParams) check() map[string]string {
	errs := make(map[string]string)
	if !regEmail.MatchString(p.Email) {
		errs["email"] = "邮箱格式有误"
	}
	mobileNum, err := vcode.RegionMobile(p.Mobile, vcode.DefaultRegion)
	if err != nil {
		errs["mobile"] = "请输入正确的中国大陆手机号码"
	}
	// 用于校验和删除验证码信息
	p.regionMobile = mobileNum.RegionMobile
	if !p.checkQQ() {
		errs["qq"] = "QQ 号填写有误"
	}
	if !regIDNumber.MatchString(p.OwnerIDNumber) {
		errs["owner_id_number"] = "请输入正确的身份证号"
	}
	if !p.checkInvoiceRate() {
		errs["invoice_rate"] = "是否开具发票选择有误"
	}
	return errs
}

func (p *guildParams) checkQQ() bool {
	if p.QQ != "" {
		qqNum, err := strconv.ParseInt(p.QQ, 10, 64)
		if err == nil && qqNum > 0 {
			return true
		}
	}

	return false
}

func (p *guildParams) checkInvoiceRate() bool {
	return p.InvoiceRate == guild.InvoiceRateNoIssue || p.InvoiceRate == guild.InvoiceRateUnknown
}

func checkThenUpload(sourceURL upload.SourceURL) (string, error) {
	if ok := storage.CheckStorage(sourceURL); ok {
		imageURL, err := storage.UploadToOSS(sourceURL, storage.PathPrefixGuildImage)
		if err != nil {
			return "", err
		}
		return imageURL, nil
	}
	imageURL, ok := service.Storage.Format(sourceURL.String())
	if !ok {
		return "", actionerrors.ErrParamsMsg("图片地址错误")
	}
	return imageURL, nil
}
