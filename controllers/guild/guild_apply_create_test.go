package guild

import (
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/certification"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func newGetC(uri string) *handler.Context {
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest(http.MethodGet, uri, nil)
	return c
}

// query 会被转成 postForm 传进 request
// TODO: 换成 missevan-go 的生成方式
func newPostC(uri string, query string, body interface{}) *handler.Context {
	c := handler.CreateTestContext(true)
	if body != nil {
		c.C.Request, _ = http.NewRequest(http.MethodPost, uri, tutil.ToRequestBody(body))
		c.C.Request.Header.Add("Content-Type", "application/json")
	} else {
		u, err := url.Parse(query)
		if err != nil {
			panic(err)
		}
		c.C.Request, _ = http.NewRequest(http.MethodPost, uri, tutil.ToRequestBody(body))
		c.C.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
		c.C.Request.PostForm = u.Query()
	}
	return c
}

func TestGuildApplyTagKey(t *testing.T) {
	assert := assert.New(t)

	// TODO: 还有其他的没写
	assert.Empty(tutil.KeyExists(tutil.JSON, guildApplyListResp{}, "Datas", "pagination"))
	assert.Empty(tutil.KeyExists(tutil.JSON, guildApplyElem{}, "id",
		"live_id", "live_username", "live_iconurl",
		"period", "settlement", "rate", "type", "status",
		"expire_time", "contract_expire_time", "create_time", "agent_id", "agent_username"))
}

func TestNewApplyCreateParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := newApplyCreateParam(newPostC("/sign/create", "?live_id=-10", nil), sign)
	assert.Equal(actionerrors.ErrParams, err)
	_, err = newApplyCreateParam(newPostC("/sign/create", "?live_id=10&duration=1", nil), sign)
	assert.Equal(actionerrors.ErrParams, err)
	c := newPostC("", "", map[string]interface{}{"live_id": 12, "duration": 4})
	c.User().ID = 999999
	_, err = newApplyCreateParam(c, renew)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	_, err = newApplyCreateParam(newPostC("/terminate/create", "?live_id=999999", nil), terminate)
	assert.Equal(actionerrors.ErrCannotFindUser, err)
	param, err := newApplyCreateParam(newPostC("sign/create", "?live_id=12&duration=4&real_name=minikube", nil), sign)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.guildInfo)
	assert.NotNil(param.liveUser)
	assert.Equal(int64(12), param.LiveID)
	assert.Equal(int64(4), param.Duration)

	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       123,
		Username: "TestNewApplyCreateParam",
	}).Error)
	param, err = newApplyCreateParam(newPostC("renew/create", "", map[string]interface{}{"live_id": 123, "duration": 4}), renew)
	require.NoError(err)
	assert.Equal(int64(123), param.LiveID)
	assert.Equal(int64(4), param.Duration)
}

func TestActionApplySignCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	applyDB := service.DB.Table(contractapplyment.TableName())
	contractDB := service.DB.Table(livecontract.TableName())
	require.NoError(applyDB.Delete("", "live_id = 123 AND guild_id = 3").Error)
	require.NoError(contractDB.Delete("", "live_id = 123 AND guild_id = 3").Error)
	defer func() {
		applyDB.Delete("", "live_id = 123 AND guild_id = 3")
		contractDB.Delete("", "live_id = 123 AND guild_id = 3")
	}()

	// 获取参数失败
	actionURL := "/sign/create"
	ctx := handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{"live_id": -5})
	_, err := ActionApplySignCreate(ctx)
	assert.EqualError(err, "参数错误")

	// 获取参数错误的情况
	params := handler.M{"duration": 10, "live_id": 123}
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, params)
	_, err = ActionApplySignCreate(ctx)
	assert.EqualError(err, "参数错误")

	// 测试邀请已入会的用户
	contract := new(livecontract.LiveContract)
	require.NoError(service.DB.Where("status >= ? AND guild_id = 3",
		livecontract.StatusContracting).First(contract).Error)
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       contract.LiveID,
		Username: "TestActionApplySignCreate1",
	}).Error)

	// 测试主播身份验证失败
	testRealName := "testRealName"
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   contract.LiveID,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
	})
	_, err = ActionApplySignCreate(ctx)
	assert.Equal(actionerrors.ErrCannotInviteWithoutCheckRealName, err)

	nowTimestamp := goutil.TimeNow().Unix()
	mosaicRealName := goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
		strconv.FormatInt(nowTimestamp, 10), testRealName)
	require.NoError(service.DB.Save(&certification.Certification{
		UserID:     contract.LiveID,
		RealName:   mosaicRealName,
		IDType:     11,
		CreateTime: nowTimestamp,
		UpdateTime: nowTimestamp,
		Checked:    certification.CheckedPassed,
	}).Error)
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   contract.LiveID,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
	})
	_, err = ActionApplySignCreate(ctx)
	assert.Equal(actionerrors.ErrUserAlreadySigned, err)

	// 测试用户需要验证后才可操作
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       123,
		Username: "TestActionApplySignCreate2",
	}).Error)
	require.NoError(service.DB.Save(&certification.Certification{
		UserID:     123,
		RealName:   mosaicRealName,
		IDType:     11,
		CreateTime: nowTimestamp,
		UpdateTime: nowTimestamp,
		Checked:    certification.CheckedPassed,
	}).Error)
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   123,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
		"rate":      55,
	})
	ctx.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplySignCreate(ctx)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 正常触发
	onWeekLater := goutil.TimeNow().AddDate(0, 0, 7).Unix()
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   123,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
		"rate":      55,
	})
	r, err := ActionApplySignCreate(ctx)
	require.NoError(err)
	assert.Equal("已向主播发送签约邀请", r)
	ca := new(contractapplyment.ContractApplyment)
	require.NoError(applyDB.First(ca, "guild_id = 3 AND live_id = 123").Error)
	assert.Equal(int64(4), ca.ContractDuration)
	assert.Equal(guildrate.RatePercent55, ca.Rate)
	assert.GreaterOrEqual(ca.ExpireTime, onWeekLater)

	// 重复邀请
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   123,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
	})
	_, err = ActionApplySignCreate(ctx)
	assert.EqualError(err, "已邀请过该主播，请勿重复邀请")

	// 有合约，但是是主播申请的
	require.NoError(applyDB.Where("id = ?", ca.ID).Update(
		map[string]interface{}{"status": 0, "type": 1}).Error)
	ctx = handler.NewTestContext(http.MethodPost, actionURL, true, handler.M{
		"live_id":   123,
		"real_name": testRealName,
		"duration":  contractapplyment.ContractDurationThreeYear,
	})
	_, err = ActionApplySignCreate(ctx)
	assert.EqualError(err, "该主播已申请签约，请先处理签约申请")
}

func TestApplyCreateParam_sendSignNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var content string
	cancelPushService := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (interface{}, error) {
			sms, ok := input.(map[string]interface{})
			require.True(ok)
			require.NotEmpty(sms)
			content = sms["systemmsgs"].([]pushservice.SystemMsg)[0].Content
			return handler.M{
				"count": 1,
			}, nil
		})
	defer cancelPushService()

	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return &sso.Account{
			Region: 86,
			Mobile: "***********",
		}, nil
	})
	defer cancel()
	cancelShortURLMock := mrpc.SetMock(userapi.URLGoShortURL, func(input interface{}) (output interface{}, err error) {
		return handler.M{"short_url": "https://123.cc"}, nil
	})
	defer cancelShortURLMock()
	p := &applyCreateParam{
		LiveID:    10,
		c:         handler.NewTestContext(http.MethodPost, "", true, nil),
		applyType: sign,
		guildInfo: &guild.IDName{ID: 3, Name: "testGuild"},
	}
	config.Conf.AB["enable_send_new_guild_sign_system_msg"] = true
	defer func() {
		delete(config.Conf.AB, "enable_send_new_guild_sign_system_msg")
	}()
	require.NoError(p.sendSignNotice(&contractapplyment.ContractApplyment{
		ID:         1,
		ExpireTime: **********,
	}))
	assert.Equal("主播您好，公会【testGuild】邀请您加入，请在 2023-08-22 00:00 前处理。"+
		"<a href=\"https://fm.example.com/guild/apply/1\">前往处理</a>", content)

	p.applyType = renew
	require.NoError(p.sendSignNotice(&contractapplyment.ContractApplyment{
		ID:         1,
		ExpireTime: **********,
	}))
	assert.Equal("主播您好，公会【testGuild】邀请您续约，请在 2023-08-22 00:00 前处理。"+
		"<a href=\"https://fm.example.com/guild/apply/1\">前往处理</a>", content)

	config.Conf.AB["enable_send_new_guild_sign_system_msg"] = false
	require.NoError(p.sendSignNotice(&contractapplyment.ContractApplyment{
		ID:               1,
		ExpireTime:       **********,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
	}))
	assert.Equal("直播公会 testGuild 向您发出时限为 3 年的续约邀请，合约到期前未处理，将自动解约，"+
		"可在主播工作台 > 申请 / 邀请记录 > 邀请记录 查看", content)
}

func TestActionApplyRenewCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	applyDB := service.DB.Table(contractapplyment.TableName())
	contractDB := service.DB.Table(livecontract.TableName())
	require.NoError(applyDB.Delete("", "live_id = 234 AND guild_id = 3").Error)
	require.NoError(contractDB.Delete("", "live_id = 234 AND guild_id = 3").Error)
	defer func() {
		contractDB.Delete("", "live_id = 234 AND guild_id = 3")
		applyDB.Delete("", "live_id = 234 AND guild_id = 3")
	}()
	var systemMsgs []pushservice.SystemMsg
	// mock pushservice
	cancelMock := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(i interface{}) (interface{}, error) {
		body, ok := i.(map[string]interface{})
		require.True(ok)
		msgs, ok := body["systemmsgs"]
		require.True(ok)
		pushserviceMsgs, ok := msgs.([]pushservice.SystemMsg)
		require.True(ok)
		systemMsgs = append(systemMsgs, pushserviceMsgs...)
		return "success", nil
	})
	defer cancelMock()

	// 参数错误
	_, err := ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=-5", nil))
	assert.Equal(actionerrors.ErrParams, err)

	// 不是签约主播
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       234,
		Username: "TestActionApplyRenewCreate1",
	}).Error)
	_, err = ActionApplyRenewCreate(newPostC("/renew/create",
		"?live_id=234&duration=4", nil))
	assert.EqualError(err, "操作失败，该主播未与您签约")
	// 没到续约时间
	contract := &livecontract.LiveContract{
		LiveID:      rand.Int63n(10000),
		Status:      livecontract.StatusContracting,
		GuildID:     3,
		ContractEnd: goutil.TimeNow().Unix() + fifteenDaySec + 60,
	}
	require.NoError(contractDB.Create(contract).Error)
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       contract.LiveID,
		Username: "TestActionApplyRenewCreate2",
	}).Error)
	_, err = ActionApplyRenewCreate(newPostC("/renew/create", "",
		map[string]interface{}{"live_id": contract.LiveID, "duration": contractapplyment.ContractDurationThreeYear}))
	assert.EqualError(err, "操作失败，合约到期前 15 天才能邀请续约")

	contract.ID = 0
	contract.LiveID = 234
	contract.ContractEnd = goutil.TimeNow().AddDate(0, 0, 1).Unix()
	contract.GuildName = "测试公会创建续约申请用"
	require.NoError(contractDB.Create(contract).Error)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/renew/create", "?live_id=234&duration=4&rate=55", nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyRenewCreate(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 正常触发
	r, err := ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=234&duration=4&rate=55", nil))
	require.NoError(err)
	assert.Equal("已向主播发送续约申请", r)
	ca := new(contractapplyment.ContractApplyment)
	require.NoError(applyDB.First(ca, "guild_id = 3 AND live_id = 234").Error)

	assert.Equal(contractapplyment.ContractDurationThreeYear, ca.ContractDuration)
	assert.Equal(contractapplyment.TypeGuildRenew, ca.Type)
	assert.GreaterOrEqual(ca.ExpireTime, contract.ContractEnd)
	assert.Equal(guildrate.RatePercent55, ca.Rate)

	require.Len(systemMsgs, 1)
	assert.True(strings.HasPrefix(systemMsgs[0].Content,
		fmt.Sprintf("直播公会 %s 向您发出时限为 3 年的续约邀请", testGuild.Name)))

	// 有重复的申请 type 4
	_, err = ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=234&duration=4", nil))
	assert.EqualError(err, "您已邀请该主播续约，请勿重复邀请")
	// 有重复的申请 type 3
	db := applyDB.Where("id = ?", ca.ID)
	require.NoError(db.Update(
		map[string]interface{}{"type": 3}).Error)
	_, err = ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=234&duration=4", nil))
	assert.EqualError(err, "该主播已申请续约，请先处理续约申请")
	// 有重复的申请 type 5
	require.NoError(db.Update(
		map[string]interface{}{"type": 5}).Error)
	_, err = ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=234&duration=4", nil))
	assert.EqualError(err, "请先处理该主播的协商解约申请")
	// 有重复的申请 type 6
	require.NoError(db.Update(
		map[string]interface{}{"type": 6}).Error)
	_, err = ActionApplyRenewCreate(newPostC("/renew/create", "?live_id=234&duration=4", nil))
	assert.EqualError(err, "请先撤回对该主播的清退申请")
}

func TestActionApplyTerminateCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	applyDB := service.DB.Table(contractapplyment.TableName())
	contractDB := service.DB.Table(livecontract.TableName())
	require.NoError(applyDB.Delete("", "live_id = 345 AND guild_id = 3").Error)
	require.NoError(contractDB.Delete("", "live_id = 345 AND guild_id = 3").Error)
	defer func() {
		contractDB.Delete("", "live_id = 345 AND guild_id = 3")
		applyDB.Delete("", "live_id = 345 AND guild_id = 3")
	}()
	now := goutil.TimeNow()

	// 参数错误
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       345,
		Username: "TestActionApplyTerminateCreate",
	}).Error)
	_, err := ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=-5", nil))
	assert.Equal(actionerrors.ErrParams, err)
	// 不是签约主播
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create",
		"?live_id=345", nil))
	assert.EqualError(err, "操作失败，该主播未与您签约")
	// 在保护期内
	contract := &livecontract.LiveContract{
		GuildID:          3,
		GuildName:        "测试公会创建清退申请用",
		LiveID:           345,
		Status:           livecontract.StatusContracting,
		ContractDuration: 1,
		ContractStart:    now.AddDate(0, 0, -10).Unix(),
		ContractEnd:      now.AddDate(0, 4, 0).Unix(),
	}
	require.NoError(contractDB.Create(contract).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.EqualError(err, "该主播签约未满 30 天，无法清退")
	require.NoError(contractDB.Model(contract).Updates(
		map[string]interface{}{
			"contract_start": 1000000000,
			"live_id":        room.TestLimitedRoomCreatorID,
		}).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create",
		fmt.Sprintf("?live_id=%d", room.TestLimitedRoomCreatorID), nil))
	assert.Equal(actionerrors.ErrNoAuthority, err, "受限房间")
	require.NoError(contractDB.Model(contract).Update("live_id", 345).Error)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/terminate/create", "?live_id=345", nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplyTerminateCreate(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 测试主播是三方独家主播
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              19,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       345,
		GuildContractID: 23333475,
		Status:          exclusivecreator.StatusValid,
		ContractEnd:     goutil.TimeNow().AddDate(3, 0, 0).Unix(),
	}
	require.NoError(tec.DB().Save(&tec).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.Equal(actionerrors.ErrExclusiveCreatorConflicted("三方独家主播不可被清退"), err)
	require.NoError(tec.DB().Delete(nil, "id = ?", tec.ID).Error)

	// 正常触发
	r, err := ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	require.NoError(err)
	assert.Equal("清退申请已提交，将于 2 天后生效，生效前可随时撤回清退申请", r)
	ca := new(contractapplyment.ContractApplyment)
	require.NoError(applyDB.First(ca, "guild_id = 3 AND live_id = 345 AND type = 6").Error)
	assert.Equal(int64(1), ca.ContractDuration)
	assert.Equal(contract.ContractEnd, ca.ContractExpireTime)
	// 有重复的申请 type 6
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.EqualError(err, "您已清退该主播，请勿重复清退")
	// 有重复的申请 type 3
	db := applyDB.Where("id = ?", ca.ID)
	require.NoError(db.Update(
		map[string]interface{}{"type": 3}).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.EqualError(err, "请先处理该主播的续约申请")
	// 有重复的申请 type 4
	require.NoError(db.Update(
		map[string]interface{}{"type": 4}).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.EqualError(err, "请先撤回对该主播的续约申请")
	// 有重复的申请 type 5
	require.NoError(db.Update(
		map[string]interface{}{"type": 5}).Error)
	_, err = ActionApplyTerminateCreate(newPostC("/terminate/create", "?live_id=345", nil))
	assert.EqualError(err, "请先处理该主播的协商解约申请")
}

func TestHasManagerCreateApplymentPermission(t *testing.T) {
	assert := assert.New(t)

	var role guildrole.GuildRole
	assert.False(role.IsGuildManager())
	assert.False(hasManagerCreateApplymentPermission(role, sign))
	assert.False(hasManagerCreateApplymentPermission(role, renew))
	assert.False(hasManagerCreateApplymentPermission(role, terminate))

	role.Set(guildrole.RoleGuildLiveCreator)
	assert.False(role.IsGuildManager())
	assert.False(hasManagerCreateApplymentPermission(role, sign))
	assert.False(hasManagerCreateApplymentPermission(role, renew))
	assert.False(hasManagerCreateApplymentPermission(role, terminate))

	role.Set(guildrole.RoleGuildAgent)
	assert.True(role.IsGuildManager())
	assert.False(role.IsGuildOwner())
	assert.True(hasManagerCreateApplymentPermission(role, sign))
	assert.True(hasManagerCreateApplymentPermission(role, renew))
	assert.False(hasManagerCreateApplymentPermission(role, terminate))

	role.Set(guildrole.RoleGuildOwner)
	assert.True(role.IsGuildManager())
	assert.True(role.IsGuildOwner())
	assert.True(hasManagerCreateApplymentPermission(role, sign))
	assert.True(hasManagerCreateApplymentPermission(role, renew))
	assert.True(hasManagerCreateApplymentPermission(role, terminate))
}

func TestHasManagerOperateApplymentPermission(t *testing.T) {
	assert := assert.New(t)

	var role guildrole.GuildRole
	assert.False(role.IsGuildManager())
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveSign))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveRenew))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildSign))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildRenew))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveTerminate))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildExpel))

	role.Set(guildrole.RoleGuildLiveCreator)
	assert.False(role.IsGuildManager())
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveSign))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveRenew))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildSign))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildRenew))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveTerminate))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildExpel))

	role.Set(guildrole.RoleGuildAgent)
	assert.True(role.IsGuildManager())
	assert.False(role.IsGuildOwner())
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveSign))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveRenew))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildSign))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildRenew))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveTerminate))
	assert.False(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildExpel))

	role.Set(guildrole.RoleGuildOwner)
	assert.True(role.IsGuildManager())
	assert.True(role.IsGuildOwner())
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveSign))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveRenew))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildSign))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildRenew))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeLiveTerminate))
	assert.True(hasManagerOperateApplymentPermission(role, contractapplyment.TypeGuildExpel))
}

func TestNewApplyTerminateBatchCreateParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testPath := "../../testdata/test_file.csv"
	content := "live_ids\n1919"
	require.NoError(os.WriteFile(testPath, []byte(content), 0644))

	now := goutil.TimeNow()
	body := applyTerminateBatchCreateParam{
		MetricStartTime: now.Unix(),
		MetricEndTime:   now.Unix(),
		CSVURL:          "https://fm.example.com/testdata/test_file.csv",
	}
	c := handler.NewTestContext("POST", "/api/v2/guild/applyment/terminate/batch-create", true, body)
	p, err := newApplyTerminateBatchCreateParam(c)
	require.NoError(err)
	require.NotNil(p)
	assert.Equal(body.MetricStartTime, p.MetricStartTime)
	assert.NotEmpty(p.liveIDBatches)
}

func TestApplyTerminateBatchCreateParam_parseCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanFile := func(path string) {
		err := os.Remove(path)
		if err != nil {
			t.Log(err)
		}
	}
	writeFile := func(content []string, path string) error {
		fileContent := strings.Join(content, "\n")
		err := os.WriteFile(path, []byte(fileContent), 0644)
		if err != nil {
			return err
		}
		return nil
	}
	testPath := "../../testdata/test_file.csv"
	content := make([]string, 0, 1010)
	content = append(content, "live_ids")
	content = append(content, "1919")
	require.NoError(writeFile(content, testPath))
	defer cleanFile(testPath)

	p := applyTerminateBatchCreateParam{CSVURL: upload.SourceURL("https://fm.example.com/testdata/test_file.csv")}
	require.NoError(p.parseCSV())
	require.NotNil(p.liveIDBatches)
	require.Len(p.liveIDBatches, 1)
	assert.Len(p.liveIDBatches[0], 1)
	assert.EqualValues(1, p.applyCounts)

	for i := int64(0); i < 1000; i++ {
		content = append(content, strconv.FormatInt(i, 10))
	}
	require.NoError(writeFile(content, testPath))
	p.liveIDBatches = [][]int64{}
	require.NoError(p.parseCSV())
	require.NotNil(p.liveIDBatches)
	require.Len(p.liveIDBatches, 2)
	assert.EqualValues(1001, p.applyCounts)
}

func TestApplyTerminateBatchCreateParam_checkExclusive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eCreator := exclusivecreator.TripartiteExclusiveCreator{
		CreatorID:   114514,
		Status:      exclusivecreator.StatusValid,
		ContractEnd: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	require.NoError(eCreator.DB().Delete("", "creator_id = ?", eCreator.CreatorID).Error)

	p := applyTerminateBatchCreateParam{}
	assert.NoError(p.checkExclusive([]int64{eCreator.CreatorID}))

	require.NoError(eCreator.DB().Save(&eCreator).Error)
	assert.EqualError(p.checkExclusive([]int64{eCreator.CreatorID}), "有三方独家主播不可被清退，live_ids: [114514]")
}

func TestApplyTerminateBatchCreateParam_checkContract(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	contract := livecontract.LiveContract{
		LiveID:        1919,
		Status:        livecontract.StatusContracting,
		ContractStart: now.Unix(),
		ContractEnd:   now.Add(10 * time.Minute).Unix(),
	}
	require.NoError(contract.DB().Delete("", "live_id = ?", contract.LiveID).Error)
	p := applyTerminateBatchCreateParam{
		MetricStartTime: now.Add(-time.Minute).Unix(),
		contractMap:     map[int64]*livecontract.LiveContract{},
	}
	assert.EqualError(p.checkContract([]int64{contract.LiveID}), "操作失败，有主播未与公会签约，live_id: 1919")

	require.NoError(contract.DB().Save(&contract).Error)
	assert.EqualError(p.checkContract([]int64{contract.LiveID}), "无法清退，有主播签约时间晚于操作开始时间，live_id: 1919")

	p.MetricStartTime = now.Unix()
	require.NoError(p.checkContract([]int64{contract.LiveID}))
}

func TestApplyTerminateBatchCreateParam_checkLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testCreatorID := int64(1145141919)
	testGuildID := int64(18189914)
	_, err := livelog.Collection().DeleteMany(ctx, bson.M{"creator_id": testCreatorID})
	require.NoError(err)

	now := goutil.TimeNow()
	p := applyTerminateBatchCreateParam{
		MetricStartTime: now.AddDate(0, 0, -10).Unix(),
		MetricEndTime:   now.Unix(),
	}
	assert.EqualError(p.checkLive([]int64{testCreatorID}), "操作失败，有主播未与公会签约，live_id: 1145141919")

	p.contractMap = map[int64]*livecontract.LiveContract{
		testCreatorID: {
			GuildID: testGuildID,
		},
	}
	assert.NoError(p.checkLive([]int64{testCreatorID}))

	recordTime := now.Add(-time.Minute)
	_, err = livelog.Collection().InsertOne(ctx, livelog.Record{
		CreatorID: testCreatorID,
		GuildID:   testGuildID,
		Revenue:   20000,
		StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
		EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
	})
	require.NoError(err)
	assert.EqualError(p.checkLive([]int64{testCreatorID}), "操作失败，有主播在当前公会流水大于 10000 钻，live_id: 1145141919")

	documents := make([]interface{}, 0, 6)
	for i := -5; i <= 0; i++ {
		record := livelog.Record{
			CreatorID: testCreatorID,
			GuildID:   testGuildID,
			Duration:  2 * time.Hour.Milliseconds(),
			StartTime: goutil.TimeUnixMilli(recordTime.AddDate(0, 0, i).UnixMilli()),
			EndTime:   goutil.TimeUnixMilli(recordTime.AddDate(0, 0, i).UnixMilli()),
		}
		documents = append(documents, record)
	}
	_, err = livelog.Collection().InsertMany(ctx, documents)
	require.NoError(err)
	assert.EqualError(p.checkLive([]int64{testCreatorID}), "操作失败，有主播在当前公会开播有效天数不小于 5 天，live_id: 1145141919")

	documents = make([]interface{}, 0, 4)
	for i := 0; i < 4; i++ {
		record := livelog.Record{
			CreatorID: testCreatorID,
			GuildID:   testGuildID,
			StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
			EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
		}
		documents = append(documents, record)
	}
	_, err = livelog.Collection().InsertMany(ctx, documents)
	require.NoError(err)
	assert.EqualError(p.checkLive([]int64{testCreatorID}), "操作失败，有主播在当前公会开播次数不小于 10 次，live_id: 1145141919")
}

func TestApplyTerminateBatchCreateParam_insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLiveID := int64(1919)
	p := applyTerminateBatchCreateParam{
		liveIDBatches: [][]int64{{testLiveID}},
	}
	p.contractMap = map[int64]*livecontract.LiveContract{
		1919: {},
	}
	require.NoError(p.insert())

	var applyment contractapplyment.ContractApplyment
	defer func() {
		require.NoError(applyment.DB().Delete("", "live_id = ?", testLiveID).Error)
	}()
	require.NoError(applyment.DB().Find(&applyment, "live_id = ?", testLiveID).Error)
	assert.NotNil(applyment)
}

func TestActionApplicationRateEdit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建用户
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       234,
		Username: "TestActionApplyReviseCreate1",
	}).Error)

	// 参数错误
	_, err := ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=234&rate=-1", nil))
	assert.EqualError(err, "参数错误")

	// 测试主播不是公会成员
	_, err = ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=234&rate=45&revise_rate=46", nil))
	assert.EqualError(err, "操作失败，该主播未与您签约")

	now := goutil.TimeNow()
	contract := &livecontract.LiveContract{
		LiveID:       662233,
		Status:       livecontract.StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	require.NoError(contract.DB().Create(contract).Error)
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       contract.LiveID,
		Username: "TestActionApplyRenewCreate2",
	}).Error)

	// 测试当前分成比例与降薪申请比例相同
	_, err = ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=662233&rate=45", nil))
	assert.EqualError(err, "与当前最低分成比例相同，修改失败！")

	// 测试正常申请降薪
	res, err := ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=662233&rate=42", nil))
	require.NoError(err)
	assert.Equal("修改申请已发送", res)

	// 测试有未处理的申请
	_, err = ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=662233&rate=41", nil))
	assert.Contains(err.Error(), "逾期将自动拒绝调整")

	updates := map[string]interface{}{
		"modified_time": goutil.TimeNow().Unix(),
		"status":        contractapplyment.StatusAgreed,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).
		Where("guild_id = ? AND live_id = ?", contract.GuildID, contract.LiveID).
		Updates(updates).Error)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	c := newPostC("/rate/edit", "?live_id=662233&rate=46", nil)
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionApplicationRateEdit(c)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.GuildPermissionSwitch = false

	// 测试正常升薪
	res, err = ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=662233&rate=46", nil))
	require.NoError(err)
	assert.Equal("修改成功", res)

	// 查看数据库是否正常修改
	application := &contractapplyment.ContractApplyment{}
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ?", contract.GuildID, contract.LiveID).Last(application).Error)
	assert.Equal(contractapplyment.StatusAgreed, application.Status)
	newContract := &livecontract.LiveContract{}
	require.NoError(newContract.DB().Where("guild_id = ? AND live_id = ?", contract.GuildID, contract.LiveID).Last(newContract).Error)
	assert.Equal(46, newContract.Rate)

	// 测试距上次降薪不能小于 7 个自然日
	_, err = ActionApplicationRateEdit(newPostC("/rate/edit", "?live_id=662233&rate=41", nil))
	assert.Contains(err.Error(), "短期内降薪频繁！")
}
