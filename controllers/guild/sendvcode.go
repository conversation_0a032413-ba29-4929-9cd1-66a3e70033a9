package guild

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
)

type sendVCodeParams struct {
	Region    string `form:"region" json:"region"`
	Mobile    string `form:"mobile" json:"mobile"`
	PostType  int    `form:"post_type" json:"post_type"`   // 验证码类型
	SessionID string `form:"session_id" json:"session_id"` // 滑动验证会话 ID
	Token     string `form:"token" json:"token"`           // 请求唯一标识
	Sig       string `form:"sig" json:"sig"`               // 签名串
}

// ActionSendVCode 发送短信验证码
/**
 * @api {post} api/v2/guild/sendvcode 发送验证码
 * @apiVersion 0.1.0
 * @apiName SendCode
 * @apiGroup /api/v2/guild/sendvcode
 *
 * @apiParam {String} [region="CN"] 手机国际区号
 * @apiParam {String} [mobile] 手机号码（创建直播公会需要此字段）
 * @apiParam {Number} post_type 验证码类型（创建公会：14，公会关键操作权限：18）
 * @apiParam {String} [session_id] 滑动验证会话 ID
 * @apiParam {String} [token] 请求唯一标识
 * @apiParam {String} [sig] 签名串
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 501010008
 * @apiError (403) {String} info 获取验证码次数太多
 *
 * @apiError (403) {Number} code 501010009
 * @apiError (403) {String} info 获取验证码手机号错误
 *
 * @apiError (500) {Number} code 100010500, 100010002
 * @apiError (500) {String} info 服务器内部错误, 数据库错误
 */
func ActionSendVCode(c *handler.Context) (handler.ActionResponse, error) {
	var params sendVCodeParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 人机校验
	if service.Captcha.IsEnabled() {
		if params.SessionID == "" || params.Token == "" || params.Sig == "" {
			return nil, actionerrors.ErrSlideCaptchaRequired
		}
		// TODO: 合并 session_id, token, sig 参数
		if ok := service.Captcha.Valid(params.SessionID, params.Token, params.Sig,
			captcha.SceneOriginalMessage, c.ClientIP()); !ok {
			return nil, actionerrors.ErrParamsMsg("滑动验证失败")
		}
	}
	var mobileNum vcode.MobileNumber
	switch params.PostType {
	case vcode.ObjectiveTypeOperateGuild:
		if c.User() == nil {
			return nil, actionerrors.ErrUnloggedUser
		}
		acc, err := service.SSO.UserInfo(c.UserID())
		if err != nil {
			if v, ok := err.(*sso.ClientError); ok && v.Code == actionerrors.CodeSSOGetUserNotFound {
				return nil, actionerrors.ErrUserNotFound
			}
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		mobileNum = vcode.MobileNumber{
			RegionMobile: fmt.Sprintf("%d%s", acc.Region, acc.Mobile),
			RegionCode:   acc.Region,
		}
	case vcode.ObjectiveTypeCreateGuild:
		if params.Region == "" {
			params.Region = vcode.DefaultRegion
		}
		if params.Region != vcode.DefaultRegion {
			return nil, actionerrors.ErrParams
		}
		mobileNum, err = vcode.RegionMobile(params.Mobile, params.Region)
		if err != nil {
			return nil, actionerrors.ErrMobileSendVCode
		}
	default:
		return nil, actionerrors.ErrParams
	}
	scene, err := vcode.SendSmsScene(params.PostType)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	ip := c.ClientIP()
	userID := c.UserID()
	sendParam := vcode.SendVCodeParam{
		RegionMobile: mobileNum.RegionMobile,
		RegionCode:   mobileNum.RegionCode,
		IP:           ip,
		UserID:       userID,
		PostType:     params.PostType,
		Scene:        scene,
	}
	if err = sendParam.Validate(); err != nil {
		if vcode.IsValidateLimitErr(err) {
			return nil, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, err.Error())
		}
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"user_id": userID, "ip": ip})
	}
	// 发送短信
	err = sendParam.SendSms()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
