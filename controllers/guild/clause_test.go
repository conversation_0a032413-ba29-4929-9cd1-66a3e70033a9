package guild

/*
func TestClause(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试正常获取条款
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/api/v2/guild/clause", nil)
	r, err := ActionClause(c)
	require.NoError(err)
	b, err := json.Marshal(r)
	require.NoError(err)
	resp := make(map[string]interface{})
	require.NoError(json.Unmarshal(b, &resp))
	assert.Equal(float64(9), resp["id"])
	assert.Equal("good clause", resp["content"])
}
*/
