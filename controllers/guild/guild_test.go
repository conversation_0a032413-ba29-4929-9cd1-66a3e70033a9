package guild

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// 会长 ID: 12; 公会 ID: 3
var testGuild *guild.Guild

func TestMain(m *testing.M) {
	config.InitTest()

	startMockSendSmsRPCServer()
	config.Conf.Service.PushService = pushservice.Config{
		URL: mockServer.URL + "/",
		Key: "testkey",
	}
	defer closeMockSendSmsRPCServer()

	handler.SetMode(handler.TestMode)
	logger.InitTestLog()
	service.InitTest()
	service.SetDBUseSQLite()

	cleanup := mockGoRPCServer()
	defer cleanup()

	// 构建 agreement 测试数据
	err := insertTestLiveContract()
	if err != nil {
		logger.Fatal(err)
	}
	defer clearTestData()

	// 清除发送验证码用户与 IP 次数限制
	err = clearSendVCodeLimits()
	if err != nil {
		logger.Fatal(err)
	}

	testGuild, err = guild.Find(3)
	if err != nil {
		logger.Fatal(err)
	}
	if testGuild == nil {
		logger.Fatal("test guild is nil")
	}
	m.Run()
}

func clearTestData() {
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "您的公会入驻申请已通过审核")
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "您的公会入驻申请未通过审核")
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "直播公会分成修改")
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "您的入会邀请已被通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的入会邀请未被通过")
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "您的入会申请已被通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的入会申请未被通过")

	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的签约申请已通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的签约申请未通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的续约申请已通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的续约申请未通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的协商解约申请已通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"您的协商解约申请未通过")
	service.DB.Table(messageassign.TableName()).Delete("", "title = ?",
		"直播公会续约邀请")

	var count int64
	service.DB.Table(livecontract.TableName()).Where("guild_id = 3 AND status = ?", livecontract.StatusContracting).Count(&count)
	service.DB.Table(guild.TableName()).Where("id = 3").Update("live_num", count)
}

func paramToRequestBody(param interface{}) *bytes.Buffer {
	data, _ := json.Marshal(param)
	return bytes.NewBuffer(data)
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	t.Run("guild", func(t *testing.T) {
		h := Handler()
		assert.Equal("guild", h.Name)
		assert.Len(h.SubHandlers, 6)
		assert.Empty(tutil.KeyExists(tutil.Actions, h,
			"role", "contract/rates",
			// 公会后台
			"searchlive", "getguildinfo",
			"guildliveincome",
			"guildincome-v2", "guildincomeexport-v2",
			"guildliveincome-v3", "guildincome-v3", "guildincomeexport-v3",
			"data-report", "data-report-export",
			"member/list",
			// 主播后台
			"my", "mycontract", "search",
			"liveincomelist", "liveincomelist-v3", "livewithdrawrecord",
			"agreement", "confirmagreement",
			// 创建公会
			"editguild",
			// 公会权限
			"sendvcode", "getusermobile", "setuserpermission",
		))
	})

	t.Run("livebackend", func(t *testing.T) {
		h := livebackendHandler()
		assert.Equal("livebackend", h.Name)
		checker := tutil.NewKeyChecker(t, tutil.Actions)
		checker.Check(h.Actions, "sign", "renew", "terminate", "revoke",
			"terminateforcely", "passinvite", "declineinvite",
			"applymentdetail", "applymentlist", "invitelist",
			"application/detail", "application/list", "application/pending-count",
			"application/terminate-check", "application/terminate", "application/terminateforcibly",
			"rate/agree", "rate/disagree", "rate/info")
	})

	t.Run("applyment", func(t *testing.T) {
		h := applymentHandler()
		assert.Equal("applyment", h.Name)
		checker := tutil.NewKeyChecker(t, tutil.Actions)
		checker.Check(h.Actions, "sign/list", "sign/create", "sign/pass", "sign/refuse", "sign/revoke",
			"renew/list", "renew/create", "renew/pass", "renew/refuse", "renew/revoke",
			"terminate/list", "terminate/create", "terminate/pass", "terminate/refuse", "terminate/revoke",
			"terminate/price", "pending-count")
	})

	t.Run("agent", func(t *testing.T) {
		h := agentHandler()
		assert.Equal("agent", h.Name)
		assert.Empty(tutil.KeyExists(tutil.Actions, h,
			"search", "operate", "list", "assign-list", "assign"))
	})

	t.Run("square/hot/recommend", func(t *testing.T) {
		h := squareHotRecommendHandler()
		assert.Equal("recommend/squarehot", h.Name)
		assert.Empty(tutil.KeyExists(tutil.Actions, h,
			"list", "vacancy", "time", "add"))
	})

	t.Run("recommend", func(t *testing.T) {
		h := recommendHandler()
		assert.Equal("recommend", h.Name)
		assert.Empty(tutil.KeyExists(tutil.Actions, h,
			"schedule/apply-add", "schedule/list",
			"schedule/apply-add", "schedule/apply-edit", "schedule/apply-set",
			"schedule/apply", "schedule/apply-list", "schedule/list",
			"banner/list", "banner/add", "banner/vacancy",
		))
	})

	t.Run("application", func(t *testing.T) {
		h := applicationHandler()
		assert.Equal("application", h.Name)
		assert.Empty(tutil.KeyExists(tutil.Actions, h,
			"rate/edit", "rate/info",
		))
	})
}

func clearSendVCodeLimits() error {
	err := service.Redis.Del(keys.KeyCounterVCodeIP1.Format("127.0.0.1")).Err()
	if err != nil {
		return err
	}
	err = service.Redis.Del(keys.KeyCounterVCodeUID1.Format(12)).Err()
	return err
}

var (
	// a multiplexer that can be used to register handlers.
	mux *http.ServeMux
	// an in-memory HTTP server for testing.
	mockServer *httptest.Server
)

// releases HTTP-related resources.
func closeMockSendSmsRPCServer() {
	mockServer.Close()
}

func startMockSendSmsRPCServer() {
	mux = http.NewServeMux()
	mockServer = httptest.NewServer(mux)
	mux.HandleFunc("/api/sms", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Add("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"code":0, "info":"success"}`)
	})
}

func mockGoRPCServer() func() {
	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"/util/addadminlog": handler.NewAction(handler.POST, func(c *handler.Context) (response handler.ActionResponse, e error) {
				return handler.M{
					"code": 0,
					"info": "success",
				}, nil
			}, false),
		},
	}
	addr := tutil.RunMockServer(r, 13032, &h)

	goRPCEntryOriginal := service.MRPC.Config["go"]
	service.MRPC.Config["go"] = mrpc.ConfigEntry{
		URL: "http://" + addr + "/",
		Key: "testkey",
	}
	return func() {
		service.MRPC.Config["go"] = goRPCEntryOriginal
	}
}
