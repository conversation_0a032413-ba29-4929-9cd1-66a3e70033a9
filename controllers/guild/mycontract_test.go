package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testLiveID1 int64 = 400793
	testLiveID2 int64 = 400794
	testLiveID3 int64 = 400795
	testLiveID4 int64 = 400796
	testGuildID int64 = 3
)

func TestActionMyContract(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 未签约且未有签约申请
	newC := func(userID int64) *handler.Context {
		c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/mycontract", true, nil)
		c.User().ID = userID
		return c
	}
	resp, err := ActionMyContract(newC(testLiveID1))
	require.NoError(err)
	assert.Nil(resp)

	now := goutil.TimeNow()
	// 未签约且正在申请签约
	applyment := contractapplyment.ContractApplyment{
		GuildID:    testGuildID,
		LiveID:     testLiveID2,
		Type:       contractapplyment.TypeLiveSign,
		Status:     contractapplyment.StatusPending,
		ExpireTime: now.AddDate(0, 0, 7).Unix(),
		Initiator:  contractapplyment.InitiatorLive,
		GuildName:  "测试：未签约且正在申请签约",
		Rate:       guildrate.RatePercent50,
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment.ID)
	}()
	resp, err = ActionMyContract(newC(testLiveID2))
	require.NoError(err)
	guildResp := resp.(myGuildResp)
	assert.Equal(0, int(guildResp.ContractID))
	assert.Equal(applyment.ID, guildResp.ApplymentID)
	assert.Equal(contractapplyment.TypeLiveSign, guildResp.Type)
	assert.Equal("50%", guildResp.RateStr)

	// 已签约
	contract := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      testLiveID3,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  10000000,
		GuildName:   "测试：已签约",
		Rate:        guildrate.RatePercent50,
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract.ID)
	}()
	resp, err = ActionMyContract(newC(testLiveID3))
	require.NoError(err)
	guildResp = resp.(myGuildResp)
	assert.Equal(0, int(guildResp.ApplymentID))
	assert.Equal(contract.ID, guildResp.ContractID)
	assert.Equal("50%", guildResp.RateStr)

	// 已签约且正在申请续约
	contract = livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      testLiveID4,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  10000000,
		GuildName:   "测试：已签约且正在申请续约",
		Rate:        guildrate.RatePercent50,
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract.ID)
	}()
	applyment = contractapplyment.ContractApplyment{
		GuildID:    testGuildID,
		LiveID:     testLiveID4,
		Type:       contractapplyment.TypeLiveRenew,
		Status:     contractapplyment.StatusPending,
		ExpireTime: now.AddDate(0, 0, 7).Unix(),
		Initiator:  contractapplyment.InitiatorLive,
		GuildName:  "测试：已签约且正在申请续约",
		Rate:       guildrate.RatePercent50,
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment.ID)
	}()
	resp, err = ActionMyContract(newC(testLiveID4))
	require.NoError(err)
	require.NotNil(resp)
	guildResp = resp.(myGuildResp)
	assert.Equal(applyment.ID, guildResp.ApplymentID)
	assert.Equal(applyment.Type, guildResp.Type)
	assert.Equal(contract.ID, guildResp.ContractID)
	assert.Equal("50%", guildResp.RateStr)
}
