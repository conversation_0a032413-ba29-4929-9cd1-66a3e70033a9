package guild

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"regexp"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func subTestActionGuildsProfit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	target, err := url.Parse("/api/v2/guild/guildsprofit")
	require.NoError(err)

	q := url.Values{}
	q.Add("start_date", "2018-03-27")
	q.Add("end_date", "2018-03-27")
	target.RawQuery = q.Encode()
	ctx := handler.NewTestContext(http.MethodGet, target.String(), true, nil)

	r, err := ActionGuildsProfit(ctx)
	require.NoError(err)
	data := r.(*ProfitResult)
	// 这里只检查 guild id = 3 的公会，排除有收益但没有对应直播时长的公会数据
	assert.NotZero(len(data.Data))

	index := -1
	for i := range data.Data {
		if data.Data[i].ID == 3 {
			index = i
		}
	}
	require.NotEqual(index, -1, "不存在 guild id = 3 的公会")

	assert.NotZero(data.Data[index].GrossProfit)
	assert.NotZero(data.Data[index].Duration)
}

func subTestActionGuildProfitView(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	target, err := url.Parse("/api/v2/guild/guildprofitview")
	require.NoError(err)

	q := url.Values{}
	q.Add("start_date", "2018-03-27")
	q.Add("end_date", "2018-03-27")
	q.Add("guild_id", "3")
	target.RawQuery = q.Encode()
	ctx := handler.NewTestContext(http.MethodGet, target.String(), true, nil)

	r, err := ActionGuildProfitView(ctx)
	assert.NoError(err)
	data := r.(*ProfitViewResult)
	assert.NotZero(data.Guild)
	require.NotZero(len(data.Data))
	assert.NotZero(data.Data[0].Date)
	assert.NotZero(data.Data[0].GrossProfit)
	assert.NotZero(data.Data[0].CreatorNum)
	assert.NotZero(data.Data[0].Duration)
}

func TestGuildProfit(t *testing.T) {
	require := require.New(t)
	sql := "INSERT INTO `transaction_log` (" +
		"`from_id`,`to_id`,`c_time`,`create_time`,`modified_time`,`confirm_time`," +
		"`gift_id`,`title`,`ios_coin`,`android_coin`,`paypal_coin`,`income`,`tax`,`rate`," +
		"`num`,`status`,`type`,`suborders_num`" +
		") " +
		"VALUES (" +
		"2877572,249092,1522128358,1522128358,1522128358,1522128358," +
		"2,'寿司',0,100,0,10,0.01,0.5," +
		"1,1,9,3" +
		")"
	err := transactionlog.ADB().Exec(sql).Error
	require.NoError(err)

	subTestActionGuildsProfit(t)
	subTestActionGuildProfitView(t)
}

func TestActionGuildIncome(t *testing.T) {
	liveContracts := []livecontract.LiveContract{
		{
			GuildID:       3,
			LiveID:        11,
			ContractStart: 1565002308,
			ContractEnd:   1999999999,
			Status:        livecontract.StatusContracting,
		},
		{
			GuildID:       3,
			LiveID:        12,
			ContractStart: 1565002308,
			ContractEnd:   1999999999,
			Status:        livecontract.StatusContracting,
		},
	}
	for i, v := range liveContracts {
		err := service.DB.FirstOrCreate(&liveContracts[i], v).Error
		require.NoError(t, err)
		err = service.NewADB.FirstOrCreate(&liveContracts[i], v).Error
		require.NoError(t, err)
	}

	transactionLogs := []transactionlog.TransactionLog{
		{
			ID:           9999999,
			FromID:       2877572,
			ToID:         12,
			CTime:        1522128358,
			CreateTime:   1522128358,
			ModifiedTime: 1522128358,
			ConfirmTime:  1522128358,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       1,
			Tax:          0.01,
			Rate:         0.5,
			Num:          1,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
		{
			ID:           10000000,
			FromID:       0,
			ToID:         11,
			CTime:        1522128358,
			CreateTime:   1522128358,
			ModifiedTime: 1522128358,
			ConfirmTime:  1522128358,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       2,
			Tax:          0.01,
			Rate:         0.5,
			Num:          2,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
	}

	for i, v := range transactionLogs {
		err := transactionlog.ADB().FirstOrCreate(&transactionLogs[i], v).Error
		require.NoError(t, err)
	}

	target, err := url.Parse("/api/v2/guild/guildincome")
	require.NoError(t, err)

	q := url.Values{}
	q.Add("start_date", "2017-03-27")
	q.Add("type", "0")
	q.Add("end_date", "2019-08-27")

	t.Run("order by guild_total desc", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		q := q
		q.Add("sort", "guild_total.desc")
		target.RawQuery = q.Encode()
		ctx := handler.NewTestContext(http.MethodGet, target.String(), true, nil)

		r, err := ActionGuildIncome(ctx)
		require.NoError(err)

		resp := r.(*guildIncomeResp)
		require.GreaterOrEqual(len(resp.Data), 2)
		assert.Greater(float64(resp.Data[0].GuildTotal), float64(resp.Data[1].GuildTotal))
	})

	t.Run("search by id, order by contract_start", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		q := q
		q.Add("s", "12")
		q.Add("sort", "contract_start.asc")
		target.RawQuery = q.Encode()

		c := handler.CreateTestContext(true)
		c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
		r, err := ActionGuildIncome(c)
		require.NoError(err)

		b, err := json.Marshal(r)
		require.NoError(err)
		resp := make(map[string]interface{})
		require.NoError(json.Unmarshal(b, &resp))

		assert.Greater(resp["total"].(float64), 0.00)
		assert.GreaterOrEqual(len(resp["Datas"].([]interface{})), 1)
	})

	t.Run("search by id, order by guild_total", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		q := q
		q.Add("s", "12")
		q.Add("sort", "guild_total")
		target.RawQuery = q.Encode()

		c := handler.CreateTestContext(true)
		c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
		r, err := ActionGuildIncome(c)
		require.NoError(err)

		b, err := json.Marshal(r)
		require.NoError(err)
		resp := make(map[string]interface{})
		require.NoError(json.Unmarshal(b, &resp))

		assert.Greater(resp["total"].(float64), 0.00)
		assert.GreaterOrEqual(len(resp["Datas"].([]interface{})), 1)
	})

	t.Run("search by name, order by contract_start", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		q := q
		q.Add("s", "零月")
		q.Add("sort", "contract_start.asc")
		target.RawQuery = q.Encode()

		c := handler.CreateTestContext(true)
		c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
		r, err := ActionGuildIncome(c)
		require.NoError(err)

		b, err := json.Marshal(r)
		require.NoError(err)
		resp := make(map[string]interface{})
		require.NoError(json.Unmarshal(b, &resp))

		assert.Greater(resp["total"].(float64), 0.00)
		assert.GreaterOrEqual(len(resp["Datas"].([]interface{})), 1)
	})

	t.Run("search by name, order by guild_total", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		q := q
		q.Add("s", "零月")
		q.Add("sort", "guild_total")
		target.RawQuery = q.Encode()

		c := handler.CreateTestContext(true)
		c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
		r, err := ActionGuildIncome(c)
		require.NoError(err)

		b, err := json.Marshal(r)
		require.NoError(err)
		resp := make(map[string]interface{})
		require.NoError(json.Unmarshal(b, &resp))

		assert.Greater(resp["total"].(float64), 0.00)
		assert.GreaterOrEqual(len(resp["Datas"].([]interface{})), 1)
	})
}

func TestActionGuildIncomeExport(t *testing.T) {
	assert := assert.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	f := func(c *gin.Context) (*user.User, error) {
		return handler.CreateTestUser(), nil
	}
	c.Set("user", user.GetUserFunc(f))
	ctx := &handler.Context{C: c}
	ctx.C.Request = httptest.NewRequest(http.MethodGet, "/?type=0", nil)

	_, err := ActionGuildIncomeExport(ctx)
	assert.Equal(handler.ErrRawResponse, err)

	assert.Equal(http.StatusOK, w.Code)
	assert.NotEmpty(w.Body.String())
	assert.Equal("application/octet-stream", w.Header().Get("Content-Type"))
	length, _ := strconv.Atoi(w.Header().Get("Content-Length"))
	assert.NotZero(length)
	assert.Regexp(regexp.MustCompile(`^attachment; filename=".+"$`), w.Header().Get("Content-Disposition"))
}

func TestGetIncomeTypes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?type=0", true, nil)
	incomeTypes, err := getIncomeTypes(c)
	require.NoError(err)
	assert.Equal(transactionlog.GiftAttrs(), incomeTypes)

	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	incomeTypes, err = getIncomeTypes(c)
	require.NoError(err)
	assert.Equal(transactionlog.GiftAttrs(), incomeTypes)

	c = handler.NewTestContext(http.MethodGet, "/?type=1", true, nil)
	incomeTypes, err = getIncomeTypes(c)
	require.NoError(err)
	assert.Len(incomeTypes, 1)
	assert.Equal(transactionlog.AttrLiveRegisterNoble, incomeTypes[0])

	c = handler.NewTestContext(http.MethodGet, "/?type=2", true, nil)
	incomeTypes, err = getIncomeTypes(c)
	require.NoError(err)
	assert.Len(incomeTypes, 2)
	assert.Equal([]int64{transactionlog.AttrLiveRegisterSuperFan, transactionlog.AttrLiveRenewSuperFan}, incomeTypes)

	c = handler.NewTestContext(http.MethodGet, "/?type=999", true, nil)
	_, err = getIncomeTypes(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestWriteDataToCsv(t *testing.T) {
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/agent/list", true, nil)

	records := [][]string{
		{"title1", "title2"},
		{"value1", "value2"},
		{"value3", "value4"},
	}
	_, err := writeDataToCsv(ctx, records, "test.csv")
	assert.EqualError(err, handler.ErrRawResponse.Error())
	assert.Equal("application/octet-stream", ctx.C.Writer.Header().Get("Content-Type"))
	assert.Equal(`attachment; filename="test.csv"; filename*=UTF-8''test.csv`, ctx.C.Writer.Header().Get("Content-Disposition"))
	assert.Equal(http.StatusOK, ctx.C.Writer.Status())
	conentLengthStr := ctx.C.Writer.Header().Get("Content-Length")
	contentLength, err := strconv.Atoi(conentLengthStr)
	assert.NoError(err)

	expectedContentLength := 3 // UTF-8 BOM length
	for _, line := range records {
		expectedContentLength++
		for i, str := range line {
			expectedContentLength += len([]rune(str))
			if i != len(line)-1 {
				expectedContentLength++
			}
		}
	}

	assert.Equal(expectedContentLength, contentLength)
}

func TestAddIncomeTimeFilter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tm := time.Date(2021, 10, 10, 0, 0, 0, 0, time.Local)
	sql := "INSERT INTO `m_attention_user` (`user_active`,`user_passtive`,`time`)" +
		"VALUES " +
		fmt.Sprintf("(333, 444, %d),", tm.Unix()-1) +
		fmt.Sprintf("(444, 555, %d),", tm.Unix()) +
		fmt.Sprintf("(555, 666, %d),", tm.Unix()+1) +
		fmt.Sprintf("(666, 777, %d),", tm.AddDate(0, 0, 1).Unix()-1) +
		fmt.Sprintf("(777, 888, %d),", tm.AddDate(0, 0, 1).Unix()) +
		fmt.Sprintf("(888, 999, %d);", tm.AddDate(0, 0, 1).Unix()+1)
	err := service.DB.Exec(sql).Error
	require.NoError(err)
	db := service.DB.Table("m_attention_user")
	db = addIncomeTimeFilter(db, "time", tm, tm)

	var num int
	require.NoError(db.Count(&num).Error)
	assert.Equal(3, num)
}

func TestFillUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := user.User{IUser: user.IUser{ID: 12, Username: "零月"}}
	ac1 := guildagent.AgentCreator{AgentID: 10, CreatorID: 12}
	ac2 := guildagent.AgentCreator{AgentID: 10, CreatorID: 13}
	agentCreators := agentCreators{&ac1, &ac2}
	param := &guildIncomeParam{
		agentCreators: agentCreators,
		guildID:       3,
		page:          1,
		pageSize:      20,
		role:          guildrole.GuildRole{BitMask: goutil.BitMask(3)},
		user:          &u,
	}
	resp := &guildIncomeResp{
		Data: []itemType{{LiveID: 12, Status: 1}, {LiveID: 13, Status: 1}},
	}
	creatorIDs := []int64{12, 13}

	// 测试用户是公会会长时
	err := param.fillUserInfo(resp, creatorIDs)
	require.NoError(err)
	assert.Equal(ac1.AgentID, resp.Data[0].AgentID)
	assert.Equal(ac2.AgentID, resp.Data[1].AgentID)

	// 测试用户不是公会会长时
	param.role = guildrole.GuildRole{BitMask: goutil.BitMask(4)}
	err = param.fillUserInfo(resp, creatorIDs)
	require.NoError(err)
	assert.Equal(param.user.ID, resp.Data[0].AgentID)
	assert.Equal(param.user.ID, resp.Data[1].AgentID)
}

func TestFillLiveDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &guildIncomeParam{
		guildID:   3,
		startDate: util.UnixMilliToTime(1522138345941),
		endDate:   util.UnixMilliToTime(1522138345947),
	}
	creatorIDs := []int64{12, 13}
	resp := &guildIncomeResp{
		Data: []itemType{{LiveID: 12}, {LiveID: 13}},
	}

	context, cancel := service.MongoDB.Context()
	defer cancel()

	// 清空测试数据
	_, err := livelog.Collection().DeleteMany(context, bson.M{
		"creator_id": bson.M{"$in": []int64{12, 13}},
		"guild_id":   3,
		"start_time": 1522138345941,
		"end_time":   1522138345947,
	})
	require.NoError(err)

	rec := []interface{}{
		livelog.Record{
			GuildID:   3,
			RoomID:    100,
			CreatorID: 12,
			CatalogID: 115,
			StartTime: 1522138345941,
			EndTime:   1522138345947,
			Duration:  2000},
		livelog.Record{
			GuildID:   3,
			RoomID:    100,
			CreatorID: 13,
			CatalogID: 115,
			StartTime: 1522138345941,
			EndTime:   1522138345947,
			Duration:  1000},
	}
	_, err = livelog.Collection().InsertMany(context, rec)
	require.NoError(err)

	err = param.fillLiveDuration(resp, creatorIDs)
	require.NoError(err)
	assert.NotZero(resp.Data[0].Duration)
	assert.NotZero(resp.Data[1].Duration)
}
