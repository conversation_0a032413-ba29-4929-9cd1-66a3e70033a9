package guild

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildliveorder"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGuildApplyListRespFillData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	resp := guildApplyListResp{Pagination: goutil.Pagination{PageSize: 1}}
	caList := []*contractapplyment.ContractApplyment{{
		ID:               123,
		LiveID:           12,
		ContractDuration: 1,
		Rate:             45,
	}, nil, nil}
	// 可能因为 calist 有 nil 而 panic
	assert.NotPanics(func() {
		assert.NoError(resp.fillData(&guildApplyListParam{
			user: handler.CreateTestUser(),
		}, caList))
	})
	require.Len(resp.Data, int(resp.Pagination.PageSize))
	u, err := mowangskuser.FindByUserID(12)
	require.NoError(err)
	assert.Equal(u.Username, resp.Data[0].LiveUsername)
	assert.Equal(u.IconURL, resp.Data[0].LiveIconURL)
	assert.Equal("6 个月", resp.Data[0].Period)
	assert.Equal(int64(123), resp.Data[0].ID)
	assert.Equal("对公结算", resp.Data[0].Settlement)
}

func TestNewGuildApplyListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.PanicsWithValue("unsupported listType: 1234", func() {
		_, _ = newGuildApplyListParam(handler.CreateTestContext(true), "1234")
	})

	_, err := newGuildApplyListParam(handler.CreateTestContext(false), sign)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 默认参数
	param, err := newGuildApplyListParam(newGetC("applyment/sign/list"), sign)
	assert.NoError(err)
	require.NotNil(param)
	assert.Equal("sign", param.listType)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
	assert.Nil(param.status)
	assert.Zero(param.applymentType)
	assert.Zero(param.liveID)
	assert.Empty(param.liveUsername)

	// 正确参数
	param, err = newGuildApplyListParam(newGetC(
		"applyment/sign/list?p=3&page_size=10&status=-3&type=1&live_id=12&live_username=abc"), renew)
	assert.NoError(err)
	require.NotNil(param)
	assert.Equal("renew", param.listType)
	assert.Equal(int64(3), param.p)
	assert.Equal(int64(10), param.pageSize)
	assert.True(param.status != nil && *param.status == -3)
	assert.Equal(int64(1), param.applymentType)
	assert.Equal(int64(12), param.liveID)
	assert.Equal("abc", param.liveUsername)

	// 错误参数
	param, err = newGuildApplyListParam(newGetC(
		"applyment/sign/list?status=cc&type=dd&live_id=ee"), terminate)
	assert.NoError(err)
	require.NotNil(param)
	assert.Equal("terminate", param.listType)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
	assert.Nil(param.status)
	assert.Zero(param.applymentType)
	assert.Zero(param.liveID)
}

func TestGuildApplyElemSetStatus(t *testing.T) {
	assert := assert.New(t)

	// 超时未处理
	now := goutil.TimeNow()
	elem := &guildApplyElem{ExpireTime: now.Unix() - 100}
	elem.setStatus(0)
	assert.Equal(int64(-4), elem.Status)
	elem.ExpireTime = now.Unix() + 10000
	// 被主播撤回
	elem.Type = 1 // 签约撤回
	elem.setStatus(-2)
	assert.Equal(int64(-3), elem.Status)
	elem.Type = 3 // 续约撤回
	elem.setStatus(-2)
	assert.Equal(int64(-3), elem.Status)
	elem.Type = 5 // 解约撤回
	elem.setStatus(-2)
	assert.Equal(int64(-3), elem.Status)
}

func TestGuildApplyListCheck(t *testing.T) {
	param := &guildApplyListParam{status: new(int64), listType: sign}
	t.Run("status", func(t *testing.T) {
		assert := assert.New(t)
		statuses := [3]int64{-4, 5, 100}
		for i := 0; i < len(statuses); i++ {
			*param.status = statuses[i]
			assert.Equal(actionerrors.ErrParams, param.check())
		}
	})
	param.status = nil
	types := [7]int64{1, 2, 3, 4, 5, 6, 7}
	results := make(map[int64]error, 7)
	for i := 0; i < len(types); i++ {
		results[types[i]] = actionerrors.ErrParams
	}
	t.Run("sign", func(t *testing.T) {
		assert := assert.New(t)
		param.listType = sign
		results[1], results[2] = nil, nil
		for i := 0; i < len(types); i++ {
			param.applymentType = types[i]
			assert.Equal(results[types[i]], param.check(), "type: %d", types[i])
		}
		results[1], results[2] = actionerrors.ErrParams, actionerrors.ErrParams
	})
	t.Run("renew", func(t *testing.T) {
		assert := assert.New(t)
		param.listType = renew
		results[3], results[4] = nil, nil
		for i := 0; i < len(types); i++ {
			param.applymentType = types[i]
			assert.Equal(results[types[i]], param.check(), "type: %d", types[i])
		}
		results[3], results[4] = actionerrors.ErrParams, actionerrors.ErrParams
	})
	t.Run("terminate", func(t *testing.T) {
		assert := assert.New(t)
		param.listType = terminate
		results[5], results[6], results[7] = nil, nil, nil
		for i := 0; i < len(types); i++ {
			param.applymentType = types[i]
			assert.Equal(results[types[i]], param.check(), "type: %d", types[i])
		}
		results[5], results[6], results[7] =
			actionerrors.ErrParams, actionerrors.ErrParams, actionerrors.ErrParams
	})
}

func TestActionApplySignList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := newGetC("/sign/list")
	c.User().ID = 999999
	_, err := ActionApplySignList(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	_, err = ActionApplySignList(newGetC("/sign/list?status=-5"))
	assert.Equal(actionerrors.ErrParams, err)
	r, err := ActionApplySignList(newGetC("/sign/list"))
	require.NoError(err)
	resp := r.(*guildApplyListResp)
	assert.LessOrEqual(int64(len(resp.Data)), resp.Pagination.PageSize)
}

func TestActionApplyRenewList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := newGetC("/renew/list")
	c.User().ID = 999999
	_, err := ActionApplyRenewList(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	_, err = ActionApplyRenewList(newGetC("/renew/list?status=-5"))
	assert.Equal(actionerrors.ErrParams, err)
	r, err := ActionApplyRenewList(newGetC("/renew/list"))
	require.NoError(err)
	resp := r.(*guildApplyListResp)
	assert.LessOrEqual(int64(len(resp.Data)), resp.Pagination.PageSize)
}

func TestActionApplyTerminateList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := newGetC("/terminate/list")
	c.User().ID = 999999
	_, err := ActionApplyTerminateList(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	_, err = ActionApplyTerminateList(newGetC("/terminate/list?status=-5"))
	assert.Equal(actionerrors.ErrParams, err)
	r, err := ActionApplyTerminateList(newGetC("/terminate/list"))
	require.NoError(err)
	resp := r.(*guildApplyListResp)
	assert.LessOrEqual(int64(len(resp.Data)), resp.Pagination.PageSize)
}

func TestGuildApplyListBuildResp(t *testing.T) {
	// 申请后面的注释是用到该合约的子测试的标题
	caList := []contractapplyment.ContractApplyment{{
		// listSign&StatusDefault liveIDLiveUsername
		Type:       1,
		Initiator:  1,
		Status:     -1,
		CreateTime: 100,
		ExpireTime: 2987654321,
	}, {
		// listSign&StatusDefault liveIDLiveUsername
		Type:       2,
		Initiator:  2,
		Status:     -1,
		CreateTime: 200,
		ExpireTime: 6000, // 拒绝了但是时间过了过期时间
	}, {
		// liveID&LiveUsername
		Type:       2,
		Initiator:  2,
		Status:     0, // 这里作为了 listSignStatusDefault 的干扰项
		CreateTime: 200,
		ExpireTime: 2987654321,
	}, {
		// listRenew&Status0
		Type:       3,
		Initiator:  1,
		Status:     0,
		CreateTime: 300,
		ExpireTime: 2987654321,
	}, {
		// listRenew&Status0
		Type:       4,
		Initiator:  2,
		Status:     0,
		CreateTime: 400,
		ExpireTime: 2987654321,
	}, {
		// listTerminate&Status-2
		Type:       5,
		Initiator:  2, // NOTICE: 正常来说 type 为 5 initiatior 肯定为 1
		Status:     -2,
		CreateTime: 600,
		ExpireTime: 600,
	}, {
		// listTerminate&Status-2
		Type:       6,
		Initiator:  2,
		Status:     -2,
		CreateTime: 600,
		ExpireTime: 600,
	}, {
		// type5&Status-3
		Type:       5,
		Initiator:  1,
		Status:     -2, // 列表返回会处理成 -3
		CreateTime: 500,
		ExpireTime: 2987654321,
	}, {
		// type5&Status-3
		Type:       5,
		Initiator:  1,
		Status:     -3, // 列表返回会处理成 -4
		CreateTime: 500,
		ExpireTime: 2987654321,
	}, { // type5&Status-3
		Type:       5,
		Initiator:  1,
		Status:     0, // 列表返回会处理成 -4
		CreateTime: 500,
		ExpireTime: 600, // 超时失效
	}, { // type7
		Type:       7,
		Initiator:  1,
		Status:     0, // 这个用于测试查不到数据
		CreateTime: 500,
		ExpireTime: 2987654321,
	}, {
		// type7
		Type:       7,
		Initiator:  1,
		Status:     1,
		CreateTime: 500,
		ExpireTime: 600,
	}}
	// service.DB = service.DB.Debug()
	for i := 0; i < len(caList); i++ {
		caList[i].ID = 30 + int64(i)
		caList[i].ContractDuration = 1
		caList[i].GuildID = 2000000
		caList[i].LiveID = 9876
		caList[i].GuildName = "测试公会查询申请列表用"
		ca := contractapplyment.ContractApplyment{ID: caList[i].ID}
		require.NoError(t, service.DB.Model(&caList[i]).FirstOrCreate(&ca).UpdateColumns(&caList[i]).Error) // 不需要 callback
	}
	role := guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	param := guildApplyListParam{
		guildID:       caList[0].GuildID,
		p:             1,
		pageSize:      20,
		role:          role,
		agentCreators: []*guildagent.AgentCreator{},
	}
	t.Run("listSign&StatusDefault", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		param.listType = sign
		param.status = new(int64)
		*param.status = -1
		r, err := param.buildResp()
		require.NoError(err)
		require.Len(r.Data, 2)
		assert.Equal(int64(2), r.Pagination.Count)
		assert.Greater(r.Data[0].CreateTime, r.Data[1].CreateTime)
		for i := 0; i < len(r.Data); i++ {
			assert.Contains([]int64{1, 2}, r.Data[i].Type)
			assert.Equal(int64(-1), r.Data[i].Status)
		}
	})
	t.Run("listRenew&Status0", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		param.listType = renew
		param.status = new(int64)
		*param.status = 0
		r, err := param.buildResp()
		require.NoError(err)
		require.Len(r.Data, 2)
		now := goutil.TimeNow()
		for i := 0; i < len(r.Data); i++ {
			assert.Contains([]int64{3, 4}, r.Data[i].Type)
			assert.Zero(r.Data[i].Status)
			assert.Greater(r.Data[i].ExpireTime, now.Unix())
		}
	})
	t.Run("listTerminate&Status-2", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		param.listType = terminate
		param.status = new(int64)
		*param.status = -2
		r, err := param.buildResp()
		require.NoError(err)
		require.Len(r.Data, 2)
		for i := 0; i < len(r.Data); i++ {
			assert.Contains([]int64{5, 6, 7}, r.Data[i].Type)
			assert.Contains([]int64{-3, -2}, r.Data[i].Status) // NOTICE： - 是因为假数据导致的
			assert.Equal(int64(600), r.Data[i].ExpireTime)
		}
	})
	t.Run("type5&Status-3", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		param.applymentType = 5
		param.status = new(int64)
		*param.status = -3
		r, err := param.buildResp()
		require.NoError(err)
		require.Len(r.Data, 3)
		for i := 0; i < len(r.Data); i++ {
			assert.Equal(int64(5), r.Data[i].Type)
			assert.Contains([]int64{-3, -4}, r.Data[i].Status)
		}
	})
	t.Run("liveID&LiveUsername", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
			ID:       9876,
			Username: "liveIDLiveUsername1",
		}).Error)
		u, err := mowangskuser.FindByUserID(9876)
		require.NoError(err)
		require.NotNil(u)

		param.applymentType = 0
		param.status = nil
		param.listType = sign
		param.liveID = 9876
		param.liveUsername = u.Username
		r, err := param.buildResp()
		require.NoError(err)
		assert.Len(r.Data, 3)

		require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
			ID:       123,
			Username: "liveIDLiveUsername2",
		}).Error)
		param.liveUsername = ""
		param.liveID = 123
		r, err = param.buildResp()
		require.NoError(err)
		assert.Empty(r.Data, r.Data)

		param.liveUsername = "testtestaaabdshfgd"
		param.liveID = 0
		r, err = param.buildResp()
		require.NoError(err)
		assert.Empty(r.Data)
	})
	t.Run("type7", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		param.liveID = 0
		param.liveUsername = ""
		param.applymentType = 7
		param.status = nil
		r, err := param.buildResp()
		require.NoError(err)
		assert.Len(r.Data, 1)
		param.applymentType = 0
		param.listType = terminate
		param.status = new(int64)
		*param.status = 0
		r, err = param.buildResp()
		require.NoError(err)
		assert.Empty(r.Data)
	})
}

func TestGuildApplyListEmptyResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param guildApplyListParam
	resp := param.emptyResp()
	require.NotNil(resp)
	assert.Len(resp.Data, 0)
	assert.Equal(int64(0), resp.Pagination.Count)
}

func TestActionApplyTerminatePrice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	glo := guildliveorder.GuildLiveOrder{
		LiveID:       12,
		GuildID:      3,
		Status:       1,
		CreateTime:   1234567890,
		ModifiedTime: 1234567890,
		Price:        1,
		ApplymentID:  1,
	}
	require.NoError(glo.DB().Where("applyment_id = ?").Save(&glo).Error)
	_, err := ActionApplyTerminatePrice(newGetC("/applyment/terminate/price"))
	assert.Equal(actionerrors.ErrParams, err)
	c := newGetC("/applyment/terminate/price?live_id=11&id=1")
	c.User().ID = 99999
	_, err = ActionApplyTerminatePrice(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	c.User().ID = 12
	_, err = ActionApplyTerminatePrice(c)
	assert.Equal(actionerrors.ErrParams, err)
	r, err := ActionApplyTerminatePrice(newGetC("/applyment/terminate/price?id=1&live_id=12"))
	require.NoError(err)
	assert.Equal(handler.M{"price": "0.01"}, r)
}

func TestGuildApplyListParamConstructSQL(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	sql := "INSERT INTO " + contractapplyment.TableName() +
		" (`live_id`, `guild_id`, `agent_id`, `guild_name`, `contract_id`, `contract_expire_time`, `type`, `contract_duration`, `rate`, `expire_time`, `status`, `initiator`, `more`, `create_time`, `modified_time`, `process_time`) " +
		"VALUES " +
		"(88880001, 7777, 66660001, '测试公会', 0, 0, 1, 1, 45, 1619020800, 1, 2, '', 1618416000, 1618416000, 1618416000), " +
		"(88880001, 7777, 66660001, '测试公会', 1, 1649952000, 4, 1, 45, 1619020800, 1, 1, '', 1618416000, 1618416000, 1618416000), " +
		"(88880001, 7777, 66660001, '测试公会', 1, 1618416000, 5, 1, 45, 1619020800, 1, 1, '', 1618416000, 1618416000, 1618416000), " +
		"(88880001, 7777, 66660001, '测试公会', 0, 0, 1, 1, 45, 1619020800, 0, -2, '', 1618416000, 1618416000, 0), " +
		"(88880002, 7777, 66660001, '测试公会', 0, 0, 2, 1, 45, 1619020800, 0, 1, '', 1618416000, 1618416000, 0), " +
		"(88880003, 7777, 66660001, '测试公会', 0, 0, 6, 1, 45, 1619020800, 0, 1, '', 1618416000, 1618416000, 0), " +
		"(88880004, 7777, 66660001, '测试公会', 2, 0, 7, 1, 45, 1619020800, 0, 1, '', 1618416000, 1618416000, 0), " +
		"(88880005, 7777, 66660001, '测试公会', 0, 0, 1, 1, 45, 1619020800, -2, 2, '', 1618416000, 1618416000, 1618416000); "
	require.NoError(service.DB.Exec(sql).Error)
	defer func() {
		require.NoError(service.DB.Table(contractapplyment.TableName()).Delete(nil, "guild_id = 66660001").Error)
	}()

	var role guildrole.GuildRole
	role.Set(guildrole.RoleGuildAgent)
	param := guildApplyListParam{
		guildID:       7777,
		applymentType: contractapplyment.TypeGuildSign,
		role:          role,
		user: &user.User{
			IUser: user.IUser{
				ID:       66660001,
				Username: "test-agent",
			},
		},
		agentCreators: []*guildagent.AgentCreator{
			{
				AgentID:   7777,
				CreatorID: 88880001,
			},
		},
	}
	param.searchAgent.word = "test"
	var contractApplyment []contractapplyment.ContractApplyment

	db, err := param.constructSQL()
	require.NoError(err)
	require.NotNil(db)
	err = db.Scan(&contractApplyment).Error
	require.NoError(err)
	require.Len(contractApplyment, 1)
	assert.Equal(int64(88880002), contractApplyment[0].LiveID)

	param.applymentType = contractapplyment.TypeLiveSign
	status := contractapplyment.StatusRevoked
	param.status = &status
	param.liveID = 88880005
	db, err = param.constructSQL()
	require.NoError(err)
	require.NotNil(db)
	err = db.Scan(&contractApplyment).Error
	require.NoError(err)
	require.Len(contractApplyment, 1)
	assert.Equal(int64(88880005), contractApplyment[0].LiveID)

	param.applymentType = contractapplyment.TypeGuildExpel
	param.status = nil
	param.liveID = 0
	db, err = param.constructSQL()
	require.NoError(err)
	require.NotNil(db)
	err = db.Scan(&contractApplyment).Error
	require.NoError(err)
	require.Len(contractApplyment, 1)
	assert.Equal(int64(88880003), contractApplyment[0].LiveID)

	param.applymentType = contractapplyment.TypeLiveSign
	param.searchAgent.isInteger = true
	param.searchAgent.wordInteger = 7777
	db, err = param.constructSQL()
	require.NoError(err)
	require.NotNil(db)
	err = db.Scan(&contractApplyment).Error
	require.NoError(err)
	require.Len(contractApplyment, 3)
}

func TestActionApplymentPendingCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowStamp := goutil.TimeNow().Unix()

	// 创建公会
	testGuildID := int64(11)
	g := guild.Guild{
		ID:            testGuildID,
		Name:          "测试转会 1",
		Intro:         "测试公会 1",
		OwnerName:     "钢铁侠",
		OwnerIDNumber: "123456789012345678",
		Mobile:        "***********",
		Bank:          "测试银行",
		CreateTime:    nowStamp,
		ModifiedTime:  nowStamp,
		Type:          guild.TypeUnencrypted,
		Checked:       guild.CheckedPass,
		UserID:        111,
	}
	g.Encrypt()
	require.NoError(guild.Guild{}.DB().Create(&g).Error)

	defer func() {
		assert.NoError(guild.Guild{}.DB().Delete("", "id = ?", g.ID).Error)
	}()

	// 生成未处理的公会签约申请
	contractApplyment1 := contractapplyment.ContractApplyment{
		GuildID:          testGuildID,
		LiveID:           10,
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		GuildName:        g.Name,
		Type:             contractapplyment.TypeGuildSign,
		ExpireTime:       goutil.TimeNow().Unix() + 100,
	}
	require.NoError(contractapplyment.ContractApplyment{}.DB().Create(&contractApplyment1).Error)

	// 生成未处理的主播协商解约申请
	contractApplyment2 := contractapplyment.ContractApplyment{
		GuildID:          testGuildID,
		LiveID:           contractApplyment1.LiveID,
		Status:           contractapplyment.StatusPending,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		GuildName:        g.Name,
		Initiator:        contractapplyment.InitiatorLive,
		Type:             contractapplyment.TypeLiveTerminate,
		ExpireTime:       goutil.TimeNow().Unix() + 100,
	}
	require.NoError(contractapplyment.ContractApplyment{}.DB().Create(&contractApplyment2).Error)

	// 生成未处理的主播签约申请
	contractApplyment3 := contractapplyment.ContractApplyment{
		GuildID:          testGuildID,
		LiveID:           10,
		Status:           contractapplyment.StatusPending,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		GuildName:        g.Name,
		Initiator:        contractapplyment.InitiatorLive,
		Type:             contractapplyment.TypeLiveSign,
		ExpireTime:       goutil.TimeNow().Unix() + 100,
	}
	require.NoError(contractapplyment.ContractApplyment{}.DB().Create(&contractApplyment3).Error)

	// 生成未处理的主播续约申请
	contractApplyment4 := contractapplyment.ContractApplyment{
		GuildID:          testGuildID,
		LiveID:           13,
		Status:           contractapplyment.StatusPending,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		GuildName:        g.Name,
		Initiator:        contractapplyment.InitiatorLive,
		Type:             contractapplyment.TypeLiveRenew,
		ExpireTime:       goutil.TimeNow().Unix() + 100,
	}
	require.NoError(contractapplyment.ContractApplyment{}.DB().Create(&contractApplyment4).Error)

	defer func() {
		assert.NoError(contractapplyment.ContractApplyment{}.DB().
			Delete("", "id IN (?)",
				[]int64{contractApplyment1.ID,
					contractApplyment2.ID,
					contractApplyment3.ID,
					contractApplyment4.ID}).Error)
	}()

	// 生成公会经纪人
	ga := guildagent.GuildAgent{
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		DeleteTime:   0,
		GuildID:      testGuildID,
		AgentID:      14,
	}
	require.NoError(guildagent.GuildAgent{}.DB().Create(&ga).Error)

	// 生成公会经纪人与主播关系
	gac := guildagent.AgentCreator{
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		DeleteTime:   0,
		GuildID:      testGuildID,
		AgentID:      14,
		CreatorID:    contractApplyment1.LiveID,
	}
	require.NoError(guildagent.AgentCreator{}.DB().Create(&gac).Error)

	defer func() {
		assert.NoError(guildagent.GuildAgent{}.DB().Delete("", "id = ?", ga.ID).Error)
		assert.NoError(guildagent.AgentCreator{}.DB().Delete("", "id = ?", gac.ID).Error)
	}()

	// 测试没有权限
	c := newGetC("/pending-count")
	c.User().ID = 10000001

	var role guildrole.GuildRole
	role.Unset(guildrole.RoleGuildLiveCreator)
	role.Unset(guildrole.RoleGuildOwner)
	role.Unset(guildrole.RoleGuildAgent)

	_, err := ActionApplymentPendingCount(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 测试公会会长
	c = newGetC("/pending-count")
	c.User().ID = g.UserID
	r, _ := ActionApplymentPendingCount(c)
	resp := r.(applymentPendingResp)
	assert.Equal(1, resp.SignCount)
	assert.Equal(1, resp.RenewCount)
	assert.Equal(1, resp.TerminateCount)
	assert.Equal(3, resp.TotalCount)

	// 测试公会经纪人
	c = newGetC("/pending-count")
	c.User().ID = ga.AgentID
	r, _ = ActionApplymentPendingCount(c)
	resp = r.(applymentPendingResp)
	assert.Equal(1, resp.SignCount)
	assert.Equal(0, resp.RenewCount)
	assert.Equal(0, resp.TerminateCount)
	assert.Equal(1, resp.TotalCount)
}
