package guild

import (
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildIncomeV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	transactionLogs := []transactionlog.TransactionLog{
		{
			ID:           2018032601,
			FromID:       2877572,
			ToID:         12,
			CTime:        1522128358,
			CreateTime:   1522128358,
			ModifiedTime: 1522128358,
			ConfirmTime:  1522128358,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       10,
			Tax:          1,
			Rate:         0.5,
			Num:          1,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
		{
			ID:           2018032602,
			FromID:       0,
			ToID:         11,
			CTime:        1522128358,
			CreateTime:   1522128358,
			ModifiedTime: 1522128358,
			ConfirmTime:  1522128358,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       20,
			Tax:          2,
			Rate:         0.5,
			Num:          2,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
	}

	for i, v := range transactionLogs {
		err := transactionlog.ADB().FirstOrCreate(&transactionLogs[i], v).Error
		require.NoError(err)
	}

	target, err := url.Parse("/api/v2/guild/guildincomev-2")
	require.NoError(err)

	q := url.Values{}
	q.Add("start_date", "2018-03-26")
	q.Add("end_date", "2018-03-28")

	t.Run("param error", func(t *testing.T) {
		ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincomev-2?start_date=2018-03-26&end_date=2018-03-28&sort=reward_income.desc&p=-1", true, nil)

		_, err := ActionGuildIncomeV2(ctx)
		assert.Error(err, actionerrors.ErrParams)

		ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincomev-2?start_date=2018-03-26&end_date=2018-03-25&sort=reward_income.desc", true, nil)
		_, err = ActionGuildIncomeV2(ctx)
		assert.Error(err, actionerrors.ErrParams)

		ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincomev-2?start_date=2018-03-26&end_date=2018-03-27&sort=reward_income.descss", true, nil)
		_, err = ActionGuildIncomeV2(ctx)
		assert.Error(err, actionerrors.ErrParams)
	})

	t.Run("order by reward_income desc", func(t *testing.T) {
		q := q
		q.Add("sort", "reward_income.desc")
		target.RawQuery = q.Encode()
		ctx := handler.NewTestContext(http.MethodGet, target.String(), true, nil)

		r, err := ActionGuildIncomeV2(ctx)
		require.NoError(err)
		require.NotNil(r)
		resp := r.(*guildIncomeV2Resp)
		require.NotNil(resp)
		require.GreaterOrEqual(len(resp.Data), 2)
	})

	t.Run("search by id, order by reward_income", func(t *testing.T) {
		q := q
		q.Add("s", "12")
		q.Add("sort", "reward_income.asc")
		target.RawQuery = q.Encode()

		c := handler.NewTestContext(http.MethodGet, target.String(), true, nil)
		r, err := ActionGuildIncomeV2(c)
		require.NoError(err)
		require.NotNil(r)
		resp := r.(*guildIncomeV2Resp)
		require.NotNil(resp)
		require.GreaterOrEqual(len(resp.Data), 1)
	})

	t.Run("search by name, order by reward_income", func(t *testing.T) {
		q := q
		q.Add("s", "零月")
		q.Add("sort", "reward_income.asc")
		target.RawQuery = q.Encode()

		c := handler.NewTestContext(http.MethodGet, target.String(), true, nil)
		r, err := ActionGuildIncomeV2(c)
		require.NoError(err)

		require.NotNil(r)
		resp := r.(*guildIncomeV2Resp)
		require.NotNil(resp)
		require.GreaterOrEqual(len(resp.Data), 1)
	})
}

func TestSetEmptyGuildIncomeV2Resp(t *testing.T) {
	assert := assert.New(t)

	resp := new(guildIncomeV2Resp)
	pa := guildIncomeV2Param{
		page:     1,
		pageSize: 10,
	}
	resp.setEmptyGuildIncomeV2Resp(&pa)
	assert.Len(resp.Data, 0)
	assert.Equal(goutil.MakePagination(0, 1, 10), resp.Pagination)
}

func TestNewGuildIncomeV2Resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	transactionLogs := []transactionlog.TransactionLog{
		{
			ID:           2017032601,
			FromID:       2877572,
			ToID:         12,
			CTime:        1490544000,
			CreateTime:   1490544000,
			ModifiedTime: 1490544000,
			ConfirmTime:  1490544000,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       10,
			Tax:          1,
			Rate:         0.5,
			Num:          1,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
		{
			ID:           2017032602,
			FromID:       0,
			ToID:         11,
			CTime:        1490544000,
			CreateTime:   1490544000,
			ModifiedTime: 1490544000,
			ConfirmTime:  1490544000,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       20,
			Tax:          2,
			Rate:         0.5,
			Num:          2,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
	}

	for i, v := range transactionLogs {
		err := transactionlog.ADB().FirstOrCreate(&transactionLogs[i], v).Error
		require.NoError(err)
	}

	ctx := handler.NewTestContext(http.MethodGet,
		"/api/v2/guild/guildincome-v2?sort=reward_income.asc&start_date=2017-03-26&end_date=2017-03-28",
		true, nil)

	param, err := newGuildIncomeV2Param(ctx)
	require.NoError(err)
	require.NotNil(param)

	resp, err := newGuildIncomeV2Resp(param, true)
	require.NoError(err)
	require.Len(resp.Data, 2)
	assert.Equal(util.Float2DP(9), resp.Data[0].RewardIncome)
	assert.Equal(util.Float2DP(18), resp.Data[1].RewardIncome)

	resp, err = newGuildIncomeV2Resp(param, false)
	require.NoError(err)
	require.Len(resp.Data, 2)
	assert.Nil(resp.Menu)
}

func TestBuildList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	transactionLogs := []transactionlog.TransactionLog{
		{
			ID:           2017092701,
			FromID:       2877572,
			ToID:         12,
			CTime:        1506528000,
			CreateTime:   1506528000,
			ModifiedTime: 1506528000,
			ConfirmTime:  1506528000,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       10,
			Tax:          1,
			Rate:         0.5,
			Num:          1,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
		{
			ID:           2017092702,
			FromID:       0,
			ToID:         11,
			CTime:        1506528000,
			CreateTime:   1506528000,
			ModifiedTime: 1506528000,
			ConfirmTime:  1506528000,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       20,
			Tax:          2,
			Rate:         0.5,
			Num:          2,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
	}

	for i, v := range transactionLogs {
		err := transactionlog.ADB().FirstOrCreate(&transactionLogs[i], v).Error
		require.NoError(err)
	}

	ctx := handler.NewTestContext(http.MethodGet,
		"/api/v2/guild/guildincome-v2?sort=reward_income.asc&start_date=2017-09-27&end_date=2017-09-29",
		true, nil)

	param, err := newGuildIncomeV2Param(ctx)
	require.NoError(err)
	require.NotNil(param)

	resp := new(guildIncomeV2Resp)
	err = resp.BuildList(param)
	require.NoError(err)
	require.Len(resp.Data, 2)

	assert.Equal(util.Float2DP(9), resp.Data[0].RewardIncome)
	assert.Equal(util.Float2DP(18), resp.Data[1].RewardIncome)
}

func TestBuildMenu(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	transactionLogs := []transactionlog.TransactionLog{
		{
			ID:           2017042701,
			FromID:       2877572,
			ToID:         12,
			CTime:        1493222400,
			CreateTime:   1493222400,
			ModifiedTime: 1493222400,
			ConfirmTime:  1493222400,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       10,
			Tax:          1,
			Rate:         0.5,
			Num:          1,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
		{
			ID:           2017042702,
			FromID:       0,
			ToID:         11,
			CTime:        1493222400,
			CreateTime:   1493222400,
			ModifiedTime: 1493222400,
			ConfirmTime:  1493222400,
			GiftID:       2,
			Title:        "寿司",
			AndroidCoin:  10,
			Income:       20,
			Tax:          2,
			Rate:         0.5,
			Num:          2,
			Status:       transactionlog.StatusSuccess,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 3,
		},
	}

	for i, v := range transactionLogs {
		err := transactionlog.ADB().FirstOrCreate(&transactionLogs[i], v).Error
		require.NoError(err)
	}

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincome-v2?start_date=2017-04-27&end_date=2017-04-29", true, nil)
	param, err := newGuildIncomeV2Param(ctx)
	require.NoError(err)
	require.NotNil(param)

	resp := new(guildIncomeV2Resp)
	// page != 1 时
	param.page = 2
	err = resp.BuildMenu(param)
	require.NoError(err)
	assert.Nil(resp.Menu)

	// page = 1 时
	param.page = 1
	err = resp.BuildMenu(param)
	require.NoError(err)
	require.NotNil(resp.Menu)

	// 表中没数据时
	param.guildID = 999999999233
	err = resp.BuildMenu(param)
	require.NoError(err)
	require.NotNil(resp.Menu)
}

func TestApplySearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincome-v2", true, nil)

	t.Run("搜索主播", func(t *testing.T) {
		param, err := newGuildIncomeV2Param(ctx)
		require.NoError(err)
		require.NotNil(param)

		db := livecontract.ADB("lc").
			Where("lc.guild_id = ?", 3).
			// 获取状态为：合约解约、合约失效、合约生效中 的合约
			Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})

		emptyDB, err := param.applySearch(db)
		require.NoError(err)
		assert.Equal(db, emptyDB)

		// param.searchCreator.Word != "" & param.searchCreator.IsInteger = true
		param.searchCreator.Word = "233"
		param.searchCreator.IsInteger = true
		param.searchCreator.WordInteger = 233
		wordIntegerDB, err := param.applySearch(db)
		require.NoError(err)
		newDB := db.Where("lc.live_id = ?", param.searchCreator.WordInteger)
		assert.Equal(newDB, wordIntegerDB)

		// param.searchCreator.Word != "" & param.searchCreator.IsInteger = false & Word = 233
		param.searchCreator.Word = "233"
		param.searchCreator.IsInteger = false
		param.searchCreator.WordInteger = 233
		wordIntegerDB, err = param.applySearch(db)
		require.NoError(err)
		var nilDB *gorm.DB
		assert.Equal(nilDB, wordIntegerDB)

		require.NoError(mowangskuser.Simple{}.DB().Save(mowangskuser.Simple{ID: 11, Username: "233456"}).Error)
		wordDB, err := param.applySearch(db)
		require.NoError(err)
		inDB := db.Where("lc.live_id IN (?)", []int64{11})
		assert.Equal(inDB, wordDB)
	})

	t.Run("搜索经纪人", func(t *testing.T) {
		param, err := newGuildIncomeV2Param(ctx)
		require.NoError(err)
		require.NotNil(param)

		db := livecontract.ADB("lc").
			Where("lc.guild_id = ?", 3).
			// 获取状态为：合约解约、合约失效、合约生效中 的合约
			Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})

		emptyDB, err := param.applySearch(db)
		require.NoError(err)
		assert.Equal(db, emptyDB)

		// param.searchAgent.Word != "" & param.searchAgent.IsInteger = true
		param.searchAgent.Word = "233"
		param.searchAgent.IsInteger = true
		param.searchAgent.WordInteger = 233
		wordIntegerAgentDB, err := param.applySearch(db)
		require.NoError(err)
		var nilDB *gorm.DB
		assert.Equal(nilDB, wordIntegerAgentDB)

		gac := &guildagent.AgentCreator{
			GuildID:   3,
			AgentID:   35,
			CreatorID: 555,
		}
		err = guildagent.AgentCreator{}.DB().Create(gac).Error
		require.NoError(err)

		param, err = newGuildIncomeV2Param(ctx)
		require.NoError(err)
		require.NotNil(param)
		param.searchAgent.Word = "35"
		param.searchAgent.IsInteger = true
		param.searchAgent.WordInteger = 35
		wordIntegerDB, err := param.applySearch(db)
		require.NoError(err)
		newDB := db.Where("lc.live_id IN (?)", []int64{555})
		assert.Equal(newDB, wordIntegerDB)

		// param.searchAgent.Word != "" & param.searchAgent.IsInteger = false & Word = 233
		param.searchAgent.Word = "233"
		param.searchAgent.IsInteger = false
		wordIntegerAgentDB, err = param.applySearch(db)
		require.NoError(err)
		assert.Equal(nilDB, wordIntegerAgentDB)

		require.NoError(mowangskuser.Simple{}.DB().Save(mowangskuser.Simple{ID: 35, Username: "233456"}).Error)
		wordDB, err := param.applySearch(db)
		require.NoError(err)
		inDB := db.Where("lc.live_id IN (?)", []int64{555})
		assert.Equal(inDB, wordDB)
	})
}

func TestFillUserInfoV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := user.User{IUser: user.IUser{ID: 12, Username: "零月"}}
	ac1 := guildagent.AgentCreator{AgentID: 10, CreatorID: 12}
	ac2 := guildagent.AgentCreator{AgentID: 10, CreatorID: 13}
	agentCreators := agentCreators{&ac1, &ac2}
	param := &guildIncomeV2Param{
		agentCreators: agentCreators,
		guildID:       3,
		page:          1,
		pageSize:      20,
		role:          guildrole.GuildRole{BitMask: goutil.BitMask(3)},
		user:          &u,
	}
	resp := &guildIncomeV2Resp{
		Data: []itemTypeV2{{LiveID: 12}, {LiveID: 13}},
	}
	creatorIDs := []int64{12, 13}

	// 测试用户是公会会长时
	err := resp.fillUserInfo(param, creatorIDs)
	require.NoError(err)
	assert.Equal(ac1.AgentID, resp.Data[0].AgentID)
	assert.Equal(ac2.AgentID, resp.Data[1].AgentID)

	// 测试用户不是公会会长时
	param.role = guildrole.GuildRole{BitMask: goutil.BitMask(4)}
	err = resp.fillUserInfo(param, creatorIDs)
	require.NoError(err)
	assert.Equal(param.user.ID, resp.Data[0].AgentID)
	assert.Equal(param.user.ID, resp.Data[1].AgentID)
}

func TestIncomeV2FieldSQL(t *testing.T) {
	assert := assert.New(t)

	_, err := incomeV2FieldSQL([]int64{}, "2123", true)
	assert.EqualError(err, "attrRateMap.GenerateRateAttrsMap: attrs is empty")

	// 测试 attr 不在 transactionlog.attrMap 的报错
	_, err = incomeV2FieldSQL([]int64{233}, "贵族收益", true)
	assert.EqualError(err, "attrRateMap.GenerateRateAttrsMap: attr 233 not exists")

	// 贵族收益
	sql, _ := incomeV2FieldSQL([]int64{transactionlog.AttrLiveRegisterNoble}, "贵族收益", true)
	assert.Equal(
		"COALESCE(SUM(FLOOR(ROUND((IF(`attr` = 2, `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS 贵族收益",
		sql)

	// 普通礼物收益
	sql, _ = incomeV2FieldSQL([]int64{transactionlog.AttrCommon}, "普通礼物收益", true)
	assert.Equal(
		"COALESCE(SUM(FLOOR(ROUND((IF(`attr` = 0, `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS 普通礼物收益",
		sql)

	// 普通礼物、贵族收益混合
	sql, _ = incomeV2FieldSQL([]int64{transactionlog.AttrCommon, transactionlog.AttrLiveRegisterNoble}, "普通礼物贵族收益混合", true)
	assert.Equal(
		"COALESCE(SUM(FLOOR(ROUND((IF(`attr` IN (0, 2), `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS 普通礼物贵族收益混合",
		sql)
}

func TestIncomeV2SQL(t *testing.T) {
	assert := assert.New(t)

	incomeSQL := incomeV2SQL()
	assert.Equal("COALESCE(SUM(FLOOR(ROUND((IF(`attr` IN (0, 3, 4, 9), `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS reward_income"+
		", COALESCE(SUM(FLOOR(ROUND((IF(`attr` = 2, `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS noble_income"+
		", COALESCE(SUM(FLOOR(ROUND((IF(`attr` IN (5, 6), `income` - `tax`, 0)) * 1000) / 10) / 100), 0) AS superfan_income", incomeSQL)
}

func TestActionGuildIncomeV2ExportV2(t *testing.T) {
	assert := assert.New(t)

	t.Run("param error", func(t *testing.T) {
		ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincomeexport-v2?start_date=2018-03-26&end_date=2018-03-28&sort=reward_income.desc&p=-1", true, nil)
		_, err := ActionGuildIncomeExportV2(ctx)
		assert.Error(err, actionerrors.ErrParams)
	})

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guildincomeexport-v2", true, nil)
	_, err := ActionGuildIncomeExportV2(ctx)
	assert.Equal(handler.ErrRawResponse, err)

	assert.Equal(http.StatusOK, ctx.C.Writer.Status())
	assert.Equal("application/octet-stream", ctx.C.Writer.Header().Get("Content-Type"))
	length, _ := strconv.Atoi(ctx.C.Writer.Header().Get("Content-Length"))
	assert.NotZero(length)
	assert.Regexp(regexp.MustCompile(`^attachment; filename=".+"$`), ctx.C.Writer.Header().Get("Content-Disposition"))
}
