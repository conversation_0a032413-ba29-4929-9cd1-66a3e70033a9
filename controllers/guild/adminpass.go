package guild

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/guild/guildutil"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type adminPassResp struct {
	Pass   bool `json:"pass"`
	Notify bool `json:"notfy"`
}

// ActionAdminPass 超管通过公会创建
/**
 * @api {post} /api/v2/admin/guild/adminpass 超管通过公会创建
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} guild_id 公会 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "pass": true,
 *       "notify": true
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionAdminPass(c *handler.Context) (handler.ActionResponse, error) {
	access, _, err := liveuser.IsStaff(c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}

	var param struct {
		GuildID int64 `json:"guild_id"`
	}
	err = c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	ok, ownerID := false, int64(0)
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		ok, ownerID, err = guild.SwitchChecked(tx, param.GuildID, guild.CheckedPass)
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				return actionerrors.ErrGuildNotExist
			}
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return handler.ErrBadRequest
		}
		agentGuildID, err := guildagent.GuildID(ownerID)
		if err != nil {
			return actionerrors.ErrServerInternal.New(err, nil)
		}
		if agentGuildID != 0 && agentGuildID != param.GuildID {
			return actionerrors.ErrUserAlreadyJoinedOtherGuild
		}

		gb := &guildbalance.GuildBalance{}
		err = guildbalance.GuildBalance{}.DB().Where(guildbalance.GuildBalance{ID: param.GuildID}).
			Attrs(guildbalance.GuildBalance{Rate: 0.5}).FirstOrCreate(gb).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	})
	if err != nil {
		if _, ok = err.(*handler.ActionError); ok {
			return nil, err
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := adminPassResp{Pass: true, Notify: true}
	sysMsg := []pushservice.SystemMsg{{
		Title:   "您的公会入驻申请已通过审核",
		UserID:  ownerID,
		Content: "您提交的公会入驻申请已通过审核。您可前往网页端猫耳FM官网直播间，点击右上角 开启直播 > 数据中心 > 公会管理中心 对公会进行维护管理。",
	}}
	err = service.PushService.SendSystemMsgWithOptions(sysMsg, nil)
	if err != nil {
		logger.Error(err)
		// PASS
		resp.Notify = false
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("通过公会创建，公会 ID: %d", param.GuildID)
	box.AddAdminLog(intro, userapi.CatalogGuildPass)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if err := addMonthRecommendSquarehotVacancy(param.GuildID); err != nil {
		logger.WithField("guild_id", param.GuildID).Errorf("通过公会申请后，设置公会热门位推荐次数失败: %v", err)
		// PASS
	}
	return resp, nil
}

const (
	// Position4 热门推荐位 4
	Position4 = 4
	// Position6 热门推荐位 6
	Position6 = 6
)

// addMonthRecommendSquarehotVacancy 设置公会当前月的热门位推荐次数
func addMonthRecommendSquarehotVacancy(guildID int64) error {
	// 如果当前时间不超过本月 15 号，则设置公会的本月热门位推荐次数
	now := goutil.TimeNow()
	if now.Day() > 15 {
		return nil
	}
	startTime, endTime := currentPeriod()
	g4 := recommend.GuildRecommendVacancy{
		Position:       Position4,
		GuildID:        guildID,
		InitialVacancy: 1,
		EditedVacancy:  1,
		Vacancy:        1,
		StartTime:      startTime.Unix(),
		EndTime:        endTime.Unix(),
	}
	g6 := recommend.GuildRecommendVacancy{
		Position:       Position6,
		GuildID:        guildID,
		InitialVacancy: 8,
		EditedVacancy:  8,
		Vacancy:        8,
		StartTime:      startTime.Unix(),
		EndTime:        endTime.Unix(),
	}
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		err := tx.Save(&g4).Error
		if err != nil {
			return err
		}
		err = tx.Save(&g6).Error
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// currentPeriod 获得当前周期的开始时间和结束时间，每个周期是从当月 2 号 0 点到下个月 1 号 24 点
func currentPeriod() (time.Time, time.Time) {
	now := guildutil.TimeNowForHotRecommend()
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()
	beginningOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation)
	startTime := beginningOfMonth.AddDate(0, 0, 1)
	endTime := beginningOfMonth.AddDate(0, 1, 1)
	return startTime, endTime
}
