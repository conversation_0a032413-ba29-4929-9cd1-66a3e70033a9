package guild

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLiveIncomeList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup, err := createTestData()
	require.NoError(err)
	defer cleanup()

	target, err := url.Parse("/api/v2/guild/liveincomelist")
	require.NoError(err)

	q := url.Values{}
	// 打赏收益
	q.Set("type", "0")
	q.Set("start_date", "2020-05-10")
	q.Set("end_date", "2020-05-22")
	target.RawQuery = q.Encode()
	c := handler.NewTestContext(http.MethodGet, target.String(), true, nil)
	c.User().ID = 55
	r, err := ActionLiveIncomeList(c)
	require.NoError(err)
	b, err := json.Marshal(r)
	require.NoError(err)
	var resp liveIncomeResp
	require.NoError(json.Unmarshal(b, &resp))
	assert.Equal(util.Float2DP(70), resp.Total)
	assert.Len(resp.Data, 7)
	assert.Equal("付费问答--测试付费问答", resp.Data[0].Title)
	assert.Equal("礼物--测试随机礼物 × 1", resp.Data[1].Title)
	assert.Equal("礼物--测试白给礼物 × 10", resp.Data[2].Title)
	assert.Equal("礼物--测试普通礼物 × 10", resp.Data[3].Title)

	// 贵族开通收益
	q.Set("type", "1")
	target.RawQuery = q.Encode()
	c = handler.NewTestContext(http.MethodGet, target.String(), true, nil)
	c.User().ID = 55
	r, err = ActionLiveIncomeList(c)
	require.NoError(err)
	b, err = json.Marshal(r)
	require.NoError(err)
	require.NoError(json.Unmarshal(b, &resp))
	assert.Equal(util.Float2DP(20), resp.Total)
	assert.Len(resp.Data, 2)
	assert.Equal("开通直播贵族--测试主播公会收益", resp.Data[0].Title)

	// 未加入公会
	c.User().ID = 987654321
	r, err = ActionLiveIncomeList(c)
	require.EqualError(err, "您还未加入公会，请先申请签约")
	assert.Nil(r)
}

func TestActionLiveWithdrawRecord(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup, err := createTestData()
	require.NoError(err)
	defer cleanup()

	target, err := url.Parse("/api/v2/guild/livewithdrawrecord")
	require.NoError(err)

	q := url.Values{}
	// 测试获取旧提现记录
	q.Set("old", "0")
	target.RawQuery = q.Encode()
	c := handler.CreateTestContext(true)
	c.User().ID = 55
	c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
	r, err := ActionLiveWithdrawRecord(c)
	require.NoError(err)
	b, err := json.Marshal(r)
	require.NoError(err)
	resp := withdrawalRecordResp{}
	require.NoError(json.Unmarshal(b, &resp))
	assert.Len(resp.Data, 1)

	// 测试获取新提现记录
	q.Set("old", "1")
	target.RawQuery = q.Encode()
	c = handler.CreateTestContext(true)
	c.User().ID = 55
	c.C.Request = httptest.NewRequest(http.MethodGet, target.String(), nil)
	r, err = ActionLiveWithdrawRecord(c)
	require.NoError(err)
	b, err = json.Marshal(r)
	require.NoError(err)
	resp = withdrawalRecordResp{}
	require.NoError(json.Unmarshal(b, &resp))
	assert.Len(resp.Data, 1)
}

func cleanTestData() {
	err := transactionlog.DB().Delete("", "to_id = ? AND suborders_num = ?", 55, 111111).Error
	if err != nil {
		logger.Error(err)
		return
	}
	err = transactionlog.ADB().Delete("", "to_id = ? AND suborders_num = ?", 55, 111111).Error
	if err != nil {
		logger.Error(err)
		return
	}
	err = service.DB.Table(livecontract.TableName()).Where("live_id = ? AND guild_id = ?", 55, 111111).Delete("").Error
	if err != nil {
		logger.Error(err)
		return
	}

	// 删除创建的提现记录测试数据
	err = accountinfo.AccountInfo{}.DB().Where("id = ?", 1).Delete("").Error
	if err != nil {
		logger.Error(err)
		return
	}
	err = withdrawalrecord.WithdrawalRecord{}.DB().Where("account_id = ? AND user_id = ?", 1, 55).Delete("").Error
	if err != nil {
		logger.Error(err)
		return
	}
}

func createTestData() (func(), error) {
	sql := "INSERT INTO " + livecontract.TableName() +
		"(`guild_id`, `live_id`, `contract_duration`, `contract_start`, `contract_end`, `rate`, `kpi`, `status`, `create_time`, `modified_time`, `type`, `guild_owner`, `guild_name`) " +
		"VALUES" +
		"(111111, 55, 0, **********, **********, 0, '0', 1, **********, **********, 1, 66, '测试主播公会收益');"
	if err := service.DB.Exec(sql).Error; err != nil {
		return nil, err
	}
	if err := service.NewADB.Exec(sql).Error; err != nil {
		return nil, err
	}
	now := goutil.TimeNow()
	timestamp1 := time.Date(2020, 5, 15, 12, 0, 0, 0, now.Location()).Unix()
	timestamp2 := time.Date(2020, 5, 19, 12, 0, 0, 0, now.Location()).Unix()
	timestamp3 := time.Date(2020, 5, 20, 12, 0, 0, 0, now.Location()).Unix()
	timestamp4 := time.Date(2020, 5, 20, 15, 0, 0, 0, now.Location()).Unix()

	sql = "INSERT INTO " + transactionlog.TransactionLog{}.TableName() +
		"(`from_id`, `to_id`, `c_time`, `create_time`, `modified_time`, `confirm_time`, `gift_id`, `title`, `ios_coin`, `android_coin`, `paypal_coin`, `tmallios_coin`, `income`, `tax`, `rate`, `num`, `status`, `type`, `suborders_num`, `attr`, `googlepay_coin`, `all_coin`, `revenue`)" +
		"VALUES" +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 0, 0, 0, 0),", timestamp1) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 0, 0, 0, 0),", timestamp2) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 0, 0, 0, 0),", timestamp2) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 1, 0, 0, 0),", timestamp2) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 2, 0, 0, 0),", timestamp3) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试主播公会收益', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 1, 1, 9, 111111, 2, 0, 0, 0),", timestamp4) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试普通礼物', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 10, 1, 9, 111111, 0, 0, 0, 0),", timestamp1) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试白给礼物', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 10, 1, 9, 111111, 3, 0, 0, 0),", timestamp2) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 1, '测试随机礼物', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 10, 1, 9, 111111, 4, 0, 0, 0),", timestamp2) +
		fmt.Sprintf("(1, 55, %[1]d, %[1]d, %[1]d, %[1]d, 0, '测试付费问答', 0, 110, 0, 0, 11.0, 1.0000, 0.1, 10, 1, 9, 111111, 0, 0, 0, 0);", timestamp2)

	if err := transactionlog.DB().Exec(sql).Error; err != nil {
		return nil, err
	}
	if err := transactionlog.ADB().Exec(sql).Error; err != nil {
		return nil, err
	}

	// 创建提现记录测试数据
	// 创建提现账号
	testIDNumber := goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "**********12345678")
	testBankAccount := goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "6228480402564890018")
	testMobile := goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "**********")
	sql = "INSERT INTO " + accountinfo.AccountInfo{}.TableName() +
		"(id, `user_id`, `real_name`, `mobile`, `id_number`, `bank_account`, `bank`, `bank_branch`, `create_time`, `type`, `status`) " +
		"VALUES" +
		fmt.Sprintf("(1, 55, 'test_real_name', '%s', '%s', '%s', '邮政储蓄', '山东分行', **********, 1, 1);", testMobile, testIDNumber, testBankAccount)
	if err := (accountinfo.AccountInfo{}).DB().Exec(sql).Error; err != nil {
		return nil, err
	}
	// 创建提现记录
	sql = "INSERT INTO " + withdrawalrecord.TableName() +
		"(`user_id`, `account_id`, `profit`, `create_time`, `status`, `type`) " +
		"VALUES" +
		"(55, 1, '2000', **********, 2, 2)," +
		"(55, 1, '2000', **********, 2, 5);"
	if err := (withdrawalrecord.WithdrawalRecord{}).DB().Exec(sql).Error; err != nil {
		return nil, err
	}

	return cleanTestData, nil
}

func TestGetIncomeTitle(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("其它礼物", getIncomeTitle("其它礼物", 1000, 55, 1, false))
	title := getIncomeTitle("景向", transactionlog.AttrLiveRenewNoble, 3, 1, false)
	assert.Equal("景向", title)

	title = getIncomeTitle("河豚", transactionlog.AttrCommon, 55, 30, false)
	assert.Equal("礼物--河豚 × 30", title)

	title = getIncomeTitle("星之钥（星运卡）", transactionlog.AttrLiveLuckyGift, 55, 100, false)
	assert.Equal("礼物--星之钥（星运卡） × 1", title)

	title = getIncomeTitle("童话之书", transactionlog.AttrLiveRebateGift, 55, 2, false)
	assert.Equal("礼物--童话之书 × 2", title)

	title = getIncomeTitle("景向", transactionlog.AttrCommon, 0, 1, false)
	assert.Equal("玩法--景向", title)

	title = getIncomeTitle("景向", transactionlog.AttrLiveRegisterNoble, 3, 1, false)
	assert.Equal("开通直播贵族--景向", title)

	title = getIncomeTitle("景向", transactionlog.AttrLiveRenewSuperFan, 3, 1, false)
	assert.Equal("续费直播超粉--1 个月（主播：景向）", title)

	title = getIncomeTitle("景向", transactionlog.AttrLiveRenewSuperFan, 3, 1, true)
	assert.Equal("续费直播超粉--1 个月", title)

	title = getIncomeTitle("玫瑰（超能魔方）", transactionlog.AttrLiveGashaponGift, 55, 10, false)
	assert.Equal("礼物--玫瑰（超能魔方）× 10", title)
}
