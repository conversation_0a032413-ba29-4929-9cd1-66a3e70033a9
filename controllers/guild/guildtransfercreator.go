package guild

import (
	"fmt"
	"html"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/mysql/guildtransferhistory"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/models/useroa"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type transferGuildParams struct {
	FromGuildID             int64  `form:"from_guild_id" json:"from_guild_id" binding:"required"`
	ToGuildID               int64  `form:"to_guild_id" json:"to_guild_id" binding:"required"`
	CreatorIDs              string `form:"creator_ids" json:"creator_ids" binding:"required"`
	RevenueLiveDurationType int    `form:"revenue_live_duration_type" json:"revenue_live_duration_type" binding:"required"`
	ContractType            int    `form:"contract_type" json:"contract_type" binding:"required"`
	Confirm                 int    `form:"confirm" json:"confirm"`

	c                        *handler.Context
	liveIDs                  []int64
	roomMap                  map[int64]*room.Room
	contractingLiveIDs       []int64
	terminatingLiveIDs       []int64
	exclusiveCreatorIDs      []int64
	toGuild                  *guild.Guild
	fromGuild                *guild.Guild
	userOA                   *useroa.UserOA
	confirmInfos             []*confirmInfo
	guildTransferHistoryList []*guildtransferhistory.GuildTransferHistory
	sysMsgs                  []pushservice.SystemMsg
}

type monthRevenue struct {
	LiveID       int64         `gorm:"column:live_id"`
	MonthRevenue util.Float2DP `gorm:"column:month_revenue"`
}

type confirmInfo struct {
	liveID            int64
	liveName          string
	roomID            int64
	roomName          string
	monthRevenue      util.Float2DP
	monthLiveDuration string
}

// ActionGuildTransferCreator 转移公会主播
/**
 * @api {post} /api/v2/admin/guild/guild-transfer-creator
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} from_guild_id 转出公会 ID
 * @apiParam {Number} to_guild_id 转入公会 ID
 * @apiParam {String} creator_ids 需要转出的主播 IDs（多个用逗号分隔）
 * @apiParam {number=1,2} revenue_live_duration_type 当月收益和时长转入（1 原公会，2 新公会）
 * @apiParam {number=1,2} contract_type 签约类型（1 续签，2 重签）
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample 转移公会主播成功:
 *   {
 *     "code": 0,
 *     "info": "转移公会主播成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": ""
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 */
func ActionGuildTransferCreator(c *handler.Context) (handler.ActionResponse, error) {
	var params transferGuildParams

	err := params.loadTransferGuildParams(c)
	if err != nil {
		return nil, err
	}

	// 转移公会
	err = params.transferGuild()
	if err != nil {
		return nil, err
	}

	err = guildscheduleapply.AfterQuitGuild(params.FromGuildID, params.liveIDs)
	if err != nil {
		logger.WithFields(logger.Fields{
			"guild_id":    params.FromGuildID,
			"creator_ids": params.liveIDs,
		}).Error(err)
		// PASS
	}

	params.addAdminLogAndSendEmail(c)
	return "转移公会主播成功", nil
}

func (p *transferGuildParams) loadTransferGuildParams(c *handler.Context) error {
	err := c.Bind(p)
	if err != nil {
		return actionerrors.ErrParams
	}

	p.c = c

	p.userOA, err = useroa.FindByUserID(c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.userOA == nil {
		return actionerrors.ErrParamsMsg("未绑定 OA 账号")
	}
	p.fromGuild, err = guild.Find(p.FromGuildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.fromGuild == nil || p.fromGuild.Checked != guild.CheckedPass {
		return actionerrors.ErrGuildNotExist
	}
	p.toGuild, err = guild.Find(p.ToGuildID)
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	if p.toGuild == nil || p.toGuild.Checked != guild.CheckedPass {
		return actionerrors.ErrGuildNotExist
	}
	p.liveIDs, err = goutil.SplitToInt64Array(p.CreatorIDs, ",")
	if err != nil {
		return actionerrors.ErrParams
	}
	p.liveIDs = util.Uniq(p.liveIDs)
	var contractingLiveIDs []int64
	// 生效中的合约（主播申请入会通过或公会邀约通过）的主播 ID
	err = livecontract.LiveContract{}.DB().
		Select("live_id").
		Where("guild_id = ? AND live_id IN (?) AND status = ?",
			p.FromGuildID, p.liveIDs, livecontract.StatusContracting).
		Pluck("live_id", &contractingLiveIDs).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(contractingLiveIDs) == 0 {
		return actionerrors.ErrParamsMsg("请输入正确的主播 ID")
	}

	now := goutil.TimeNow()
	// 主播申请退会、公会清退、公会申请续约、主播申请续约未处理的主播 ID
	err = contractapplyment.ContractApplyment{}.DB().
		Select("live_id").
		Where("guild_id = ? AND live_id IN (?) AND status = ? AND type IN (?) AND expire_time > ?",
			p.FromGuildID, contractingLiveIDs, contractapplyment.StatusPending,
			[]int64{contractapplyment.TypeLiveTerminate, contractapplyment.TypeGuildExpel,
				contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew}, now.Unix()).
		Pluck("live_id", &p.terminatingLiveIDs).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 三方独家主播 ID
	p.exclusiveCreatorIDs, err = exclusivecreator.FindExclusiveCreatorIDs(contractingLiveIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 若主播全为三方独家主播
	if len(util.DiffInt64(contractingLiveIDs, p.exclusiveCreatorIDs)) == 0 {
		return actionerrors.ErrParamsMsg("输入的主播 ID 均为生效中的三方独家主播，请输入正确的主播 ID")
	}

	// 总的不符合转会要求的主播 ID
	tes := util.Uniq(append(p.terminatingLiveIDs, p.exclusiveCreatorIDs...))
	// 符合要求可以转会的主播 ID
	p.contractingLiveIDs = util.DiffInt64(contractingLiveIDs, tes)
	if len(p.contractingLiveIDs) == 0 {
		return actionerrors.ErrParamsMsg("请输入正确的主播 ID")
	}
	p.terminatingLiveIDs = util.Uniq(p.terminatingLiveIDs)

	// 根据生效的主播 ID 查询直播间信息
	rooms, err := room.FindAll(bson.M{"creator_id": bson.M{"$in": p.contractingLiveIDs}})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.roomMap = goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Room)

	// 获取主播本月直播时长
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(now)
	userDurationMap, err := livelog.GetUserLiveTotalDuration(p.FromGuildID, p.contractingLiveIDs,
		nowStampFirstDayOfThisMonth, now)
	if err != nil {
		return err
	}
	// 单位为元（统计本月的收益）
	var revenueThisMonth []*monthRevenue
	err = transactionlog.ADB().
		Select("to_id AS live_id, IFNULL(SUM(FLOOR(ROUND(IFNULL((income - tax), 0) * 1000) / 10) / 100), 0) AS month_revenue").
		Where("suborders_num = ? AND type = ? AND status = ?",
			p.FromGuildID, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("attr IN (?) AND to_id IN (?)", transactionlog.AllRevenueAttrs(), p.contractingLiveIDs).
		Where("confirm_time >= ? AND confirm_time < ?", nowStampFirstDayOfThisMonth.Unix(), now.Unix()).
		Group("live_id").
		Find(&revenueThisMonth).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	lrm := map[int64]*monthRevenue{}
	if len(revenueThisMonth) != 0 {
		lrm = goutil.ToMap(revenueThisMonth, "LiveID").(map[int64]*monthRevenue)
	}
	userList, err := mowangskuser.FindSimpleList(p.contractingLiveIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	confirmInfos := make([]*confirmInfo, 0, len(userList))
	for _, v := range userList {
		confirmInfo := &confirmInfo{
			liveID:   v.ID,
			liveName: v.Username,
		}
		r := p.roomMap[v.ID]
		if r == nil {
			confirmInfo.monthLiveDuration = "0"
			confirmInfo.roomName = "-"
		} else {
			duration := userDurationMap[r.CreatorID]
			if duration == nil {
				confirmInfo.monthLiveDuration = "0"
			} else {
				confirmInfo.monthLiveDuration = time.Duration(duration.TotalDuration * int64(time.Millisecond)).String()
			}
			confirmInfo.roomID = r.RoomID
			confirmInfo.roomName = r.Name
			if s := lrm[r.CreatorID]; s != nil {
				confirmInfo.monthRevenue = s.MonthRevenue
			}
		}
		confirmInfos = append(confirmInfos, confirmInfo)
	}
	p.confirmInfos = confirmInfos
	if p.Confirm == 0 {
		info := "<p><b>此次转会主播列表</b></p><table border=\"1\"><tr><th>主播 ID </th> <th>主播昵称</th> <th>房间号</th> <th>房间名称</th> <th>本月流水（元）</th> <th>本月有效直播时长</th></tr>"
		for _, c := range confirmInfos {
			info += fmt.Sprintf("<tr><td>%d</td><td>%s</td><td>%d</td><td>%s</td><td>%.2f</td><td>%s</td></tr>",
				c.liveID, html.EscapeString(c.liveName), c.roomID, html.EscapeString(c.roomName), c.monthRevenue, c.monthLiveDuration)
		}
		info += fmt.Sprintf("</table><p>共有 %d 位主播将从公会【%s】转出，转入公会【%s】</p><p>当月流水、直播时长归属：%s</p><p>签约时长：%s</p>",
			len(confirmInfos),
			html.EscapeString(p.fromGuild.Name),
			html.EscapeString(p.toGuild.Name),
			guildtransferhistory.SwitchRevenueLiveDurationType(p.RevenueLiveDurationType),
			guildtransferhistory.SwitchContractType(p.ContractType))
		if len(p.terminatingLiveIDs) > 0 {
			s := "存在解约未处理的主播，转会失败："
			i := goutil.JoinInt64Array(p.terminatingLiveIDs, ",")
			info += "<p>" + s + i + "</p>"
		}
		if len(p.exclusiveCreatorIDs) > 0 {
			s := "存在三方独家主播，转会失败："
			i := goutil.JoinInt64Array(p.exclusiveCreatorIDs, ",")
			info += "<p>" + s + i + "</p>"
		}
		// 其他失效原因的主播 ID
		otherLiveIDs := util.DiffInt64(p.liveIDs, contractingLiveIDs)
		if len(otherLiveIDs) > 0 {
			s := "其他原因失效主播："
			i := goutil.JoinInt64Array(otherLiveIDs, ",")
			info += "<p>" + s + i + "</p>"
		}
		return actionerrors.ErrConfirmRequired(info, 1, true)
	}
	return nil
}

func (p *transferGuildParams) transferGuild() error {
	// 需要失效的合约
	var liveContracts []*livecontract.LiveContract
	err := livecontract.LiveContract{}.DB().Select("*").
		Where("guild_id = ? AND live_id IN (?) AND status = ?",
			p.FromGuildID, p.contractingLiveIDs, livecontract.StatusContracting).
		Scan(&liveContracts).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		contractLength := len(liveContracts)
		newApplymentList := make([]*contractapplyment.ContractApplyment, 0, contractLength*2)
		guildTransferHistoryList := make([]*guildtransferhistory.GuildTransferHistory, 0, contractLength)
		sysMsgs := make([]pushservice.SystemMsg, 0, contractLength)
		for _, contract := range liveContracts {
			// 获取新合同的开始时间和结束时间
			contractStart, contractEnd := p.getContractStartAndEndTime(contract)
			// 退出原来公会（使合约失效）
			err = tx.Table(livecontract.TableName()).
				Where("id = ? ", contract.ID).
				Update(map[string]interface{}{
					"status":        livecontract.StatusFinished,
					"contract_end":  contractStart,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}

			// 未处理的降薪申请失效
			// TODO: 其他所有未处理申请失效
			if err = contractapplyment.ApplicationEditRateInvalid(contract.GuildID, contract.LiveID, tx); err != nil {
				return err
			}
			// 退会申请
			newApplymentList = append(newApplymentList, &contractapplyment.ContractApplyment{
				LiveID:             contract.LiveID,
				GuildID:            contract.GuildID,
				GuildName:          contract.GuildName,
				ContractID:         contract.ID,
				ExpireTime:         now.AddDate(0, 0, 7).Unix(),
				Type:               contractapplyment.TypeLiveTerminate,
				ContractDuration:   contract.ContractDuration,
				Rate:               contract.Rate,
				ContractExpireTime: contractStart,
				Status:             contractapplyment.StatusAgreed,
				Initiator:          contractapplyment.InitiatorGuild,
				CreateTime:         nowStamp,
				ModifiedTime:       nowStamp,
			})
			// 主播加入新的公会
			lc := &livecontract.LiveContract{
				LiveID:           contract.LiveID,
				GuildID:          p.toGuild.ID,
				GuildOwner:       p.toGuild.UserID,
				GuildName:        p.toGuild.Name,
				Type:             contract.Type,
				ContractDuration: contract.ContractDuration,
				Rate:             contract.Rate,
				KPI:              contract.KPI,
				Status:           livecontract.StatusContracting,
				CreateTime:       nowStamp,
				ModifiedTime:     nowStamp,
			}
			lc.ContractStart = contractStart
			lc.ContractEnd = contractEnd
			err = tx.Save(lc).Error
			if err != nil {
				return err
			}
			// 补充入会申请
			newApplymentList = append(newApplymentList, &contractapplyment.ContractApplyment{
				LiveID:             contract.LiveID,
				GuildID:            p.ToGuildID,
				GuildName:          p.toGuild.Name,
				ContractID:         lc.ID,
				ExpireTime:         now.AddDate(0, 0, 7).Unix(),
				Type:               lc.Type,
				ContractDuration:   lc.ContractDuration,
				Rate:               lc.Rate,
				ContractExpireTime: lc.ContractEnd,
				Status:             contractapplyment.StatusAgreed,
				Initiator:          int(lc.Type),
				CreateTime:         nowStamp,
				ModifiedTime:       nowStamp,
			})
			// 转会记录
			roomID := int64(0)
			if rm := p.roomMap[contract.LiveID]; rm != nil {
				roomID = rm.RoomID
			}
			guildTransferHistoryList = append(guildTransferHistoryList, &guildtransferhistory.GuildTransferHistory{
				OperatorID:              p.userOA.UserID,
				CreatorID:               contract.LiveID,
				FromGuildID:             p.FromGuildID,
				ToGuildID:               p.ToGuildID,
				RoomID:                  roomID,
				RevenueLiveDurationType: p.RevenueLiveDurationType,
				ContractType:            p.ContractType,
				CreateTime:              nowStamp,
				ModifiedTime:            nowStamp,
			})
			sysMsgs = append(sysMsgs, pushservice.SystemMsg{
				UserID: contract.LiveID,
				Title:  "主播转会通知",
				Content: fmt.Sprintf("主播您好，您已被公会【%s】转入至新公会【%s】。您本月在原公会的流水、直播时长%s。签约时长至 %s。如有任何疑问，请联系原公会负责人。",
					html.EscapeString(p.fromGuild.Name), html.EscapeString(p.toGuild.Name),
					p.switchRevenueLiveDurationInfo(p.RevenueLiveDurationType),
					time.Unix(contractEnd, 0).Format(util.TimeFormatYMDHMS)),
			})
		}
		p.sysMsgs = sysMsgs
		err = helper.BatchInsert(tx, contractapplyment.TableName(), newApplymentList)
		if err != nil {
			return err
		}
		p.guildTransferHistoryList = guildTransferHistoryList

		// 更新旧公会主播数冗余字段
		err = guild.IncreaseLiveNum(p.fromGuild.ID, -int64(contractLength), tx)
		if err != nil {
			return err
		}

		// 更新新公会主播数冗余字段
		err = guild.IncreaseLiveNum(p.toGuild.ID, int64(contractLength), tx)
		if err != nil {
			return err
		}

		// 当月流水和时长转入新公会
		if p.RevenueLiveDurationType == guildtransferhistory.TypeRevenueNew {
			err = p.transferMonthRevenueLiveDuration()
			if err != nil {
				return err
			}
		}

		err = servicedb.Tx(service.LiveDB, func(tx2 *gorm.DB) error {
			// 添加转会历史记录
			err = helper.BatchInsert(tx2, guildtransferhistory.GuildTransferHistory{}.TableName(), p.guildTransferHistoryList)
			if err != nil {
				return err
			}

			// 处理公会经纪人
			err = tx2.Table(guildagent.GuildAgent{}.TableName()).
				Where("guild_id = ? AND agent_id IN (?) AND delete_time = 0", p.fromGuild.ID, p.contractingLiveIDs).
				Update(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}

			// 处理公会经纪人与主播关系
			err = tx2.Table(guildagent.AgentCreator{}.TableName()).
				Where("guild_id = ? AND (agent_id IN (?) OR creator_id IN (?)) AND delete_time = 0",
					p.fromGuild.ID, p.contractingLiveIDs, p.contractingLiveIDs).
				Update(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 将所有转移公会的主播的 rooms.guild_id 更新为转入的公会的 id
	err = room.SetGuildID(p.contractingLiveIDs, p.ToGuildID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"guild_id":    p.FromGuildID,
			"creator_ids": p.contractingLiveIDs,
			"to_guild_id": p.ToGuildID,
		}).Error(err)
		// PASS
	}
	return nil
}

func (p *transferGuildParams) transferMonthRevenueLiveDuration() error {
	// 转移当月流水
	now := goutil.TimeNow()
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(now)

	err := userapi.GuildTransferRevenue(p.c.UserContext(), p.FromGuildID, p.ToGuildID, p.contractingLiveIDs,
		nowStampFirstDayOfThisMonth, goutil.EndOfMonth(now))
	if err != nil {
		return err
	}

	// 转移当月时长
	st := goutil.NewTimeUnixMilli(nowStampFirstDayOfThisMonth)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livelog.Collection().UpdateMany(ctx, bson.M{"guild_id": p.FromGuildID, "creator_id": bson.M{"$in": p.contractingLiveIDs},
		"start_time": bson.M{"$gte": st}},
		bson.M{"$set": bson.M{"guild_id": p.ToGuildID}})
	if err != nil {
		return err
	}
	return nil
}

func (p *transferGuildParams) addAdminLogAndSendEmail(c *handler.Context) {
	// 发送系统通知
	if len(p.sysMsgs) > 0 {
		err := service.PushService.SendSystemMsg(p.sysMsgs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	// 管理员操作日志
	info := ""
	for _, liveInfo := range p.confirmInfos {
		info = info + fmt.Sprintf("%d %s", liveInfo.liveID, liveInfo.liveName)
	}
	nowStamp := goutil.TimeNow().Unix()
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("转出公会 ID：%d，转出公会名称：%s，转入公会 ID：%d，转入公会名称：%s，转会主播明细：%s",
		p.fromGuild.ID,
		p.fromGuild.Name,
		p.toGuild.ID,
		p.toGuild.Name,
		info)
	box.AddWithChannelID(userapi.CatalogTransferGuild, p.toGuild.ID, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 发邮件
	e := config.Conf.Params.GuildNoticeEmails.GuildTransferNoticeEmails
	email := pushservice.Email{
		To:      e,
		Subject: fmt.Sprintf("【%s】主播转入【%s】", p.fromGuild.Name, p.toGuild.Name),
	}
	email.Body = fmt.Sprintf("<p>操作时间：%s</p><p>操作人：%s</p><p>转出公会 ID：%d</p><p>转出公会名称：%s</p> "+
		"<p>转入公会 ID：%d</p><p>转入公会名称：%s</p><p>当月流水、直播时长归属：%s</p><p>签约时长：%s</p><p>转会主播明细：</p>%s",
		time.Unix(nowStamp, 0).Format(util.TimeFormatYMDHMS),
		html.EscapeString(p.userOA.OAName),
		p.fromGuild.ID,
		html.EscapeString(p.fromGuild.Name),
		p.toGuild.ID,
		html.EscapeString(p.toGuild.Name),
		guildtransferhistory.SwitchRevenueLiveDurationType(p.RevenueLiveDurationType),
		guildtransferhistory.SwitchContractType(p.ContractType),
		info)
	err = service.PushService.SendEmail(email)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// getContractStartAndEndTime
func (p *transferGuildParams) getContractStartAndEndTime(contract *livecontract.LiveContract) (int64, int64) {
	now := goutil.TimeNow()
	switch p.RevenueLiveDurationType {
	// 本月时长和流水留在原公会
	case guildtransferhistory.TypeRevenueOriginal:
		// 续签
		if p.ContractType == guildtransferhistory.TypeContractOriginal {
			contractStart := now.Unix()
			contractEnd := contract.ContractEnd
			return contractStart, contractEnd
		}
		// 重签
		if p.ContractType == guildtransferhistory.TypeContractAgain {
			contractStart := now.Unix()
			contractEnd := contractapplyment.GetContractEnd(now, contract.ContractDuration).Unix()
			return contractStart, contractEnd
		}
		panic(fmt.Sprintf("未知合约类型: %d", p.ContractType))
	// 本月流水和时长转入新公会
	case guildtransferhistory.TypeRevenueNew:
		nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(now)
		// 本月刚加入的主播，新合同的时间用原来的时间
		if contract.ContractStart >= nowStampFirstDayOfThisMonth.Unix() {
			contractStart := contract.ContractStart
			contractEnd := contract.ContractEnd
			return contractStart, contractEnd
		}
		// 续签
		if p.ContractType == guildtransferhistory.TypeContractOriginal {
			contractStart := nowStampFirstDayOfThisMonth.Unix()
			contractEnd := contract.ContractEnd
			return contractStart, contractEnd
		}
		// 重签
		if p.ContractType == guildtransferhistory.TypeContractAgain {
			contractStart := nowStampFirstDayOfThisMonth.Unix()
			contractEnd := contractapplyment.GetContractEnd(nowStampFirstDayOfThisMonth, contract.ContractDuration).Unix()
			return contractStart, contractEnd
		}
		panic(fmt.Sprintf("未知合约类型: %d", p.ContractType))
	default:
		panic(fmt.Sprintf("未知当月收益和时长类型: %d", p.RevenueLiveDurationType))
	}
}

// switchRevenueLiveDurationInfo 获取当月流水和时长转入信息
func (p *transferGuildParams) switchRevenueLiveDurationInfo(revenueLiveDurationType int) string {
	switch revenueLiveDurationType {
	case guildtransferhistory.TypeRevenueOriginal:
		return "保留在原公会"
	case guildtransferhistory.TypeRevenueNew:
		return "已转入新公会"
	default:
		panic(fmt.Sprintf("未知类型: %d", revenueLiveDurationType))
	}
}
