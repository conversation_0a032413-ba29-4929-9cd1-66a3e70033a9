package guild

// ActionPassTerminateContract 同意公会解约申请
/*
* @api {post} /api/v2/guild/passterminatecontract 同意公会解约申请
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild
*
* @apiParam {Number} guild_id 公会 ID
*
* @apiSuccess {Number} code
* @apiSuccess {String} info
*
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": true
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (400) {Number} code 500050005
* @apiError (400) {String} info 该公会未向您发起解约
 */
/*
func ActionPassTerminateContract(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID int64 `json:"guild_id"`
	}
	err := c.BindJ<PERSON>(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildID := params.GuildID
	ok, err := livecontract.LiveContract{}.CopeWithTerminatingContract(guildID, c.User().ID, livecontract.StatusUseless)

	return ok, err
}
*/

// ActionDeclineTerminateContract 拒绝公会解约申请
/*
* @api {post} /api/v2/guild/declineterminatecontract 拒绝公会解约申请
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild
*
* @apiParam {Number} guild_id 公会 ID
*
* @apiSuccess {Number} code
* @apiSuccess {String} info
*
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": true
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (400) {Number} code 500050005
* @apiError (400) {String} info 该公会未向您发起解约
 */
/*
func ActionDeclineTerminateContract(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID int64 `json:"guild_id"`
	}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildID := params.GuildID
	ok, err := livecontract.LiveContract{}.CopeWithTerminatingContract(guildID, c.User().ID, livecontract.StatusContracting)

	return ok, err
}
*/
