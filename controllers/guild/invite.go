package guild

/*
type inviteResp struct {
	Success  bool                       `json:"success"`
	Contract *livecontract.LiveContract `json:"contract,omitempty"`
	Reason   string                     `json:"reason,omitempty"`
}
*/

// ActionInvite 邀请主播加入公会
/*
 * @api {post} api/v2/guild/invite 邀请主播加入公会
 * @apiName invite
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} user_id 主播 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (403) {Number} code 500050002
 * @apiError (403) {String} info 已加入另一公会，不能加入多个公会
 */
/*
func ActionInvite(c *handler.Context) (handler.ActionResponse, error) {
	_, err := GetUserPassedGuildID(c.User().ID)
	if err != nil {
		return nil, err
	}
	// 获取被邀请人信息
	var d struct {
		UserID int64 `json:"user_id"`
	}
	err = c.BindJSON(&d)
	if err != nil || d.UserID == 0 {
		return nil, actionerrors.ErrParams
	}
	liveID := d.UserID
	// TODO: using exists
	var liver mowangskuser.Simple
	err = service.DB.Find(&liver, "id = ?", liveID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, actionerrors.ErrCannotFindUser
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	// 获取公会信息
	g, err := findGuild(c.User())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if g == nil || g.Checked != guild.CheckedPass {
		return nil, actionerrors.ErrNoAuthority
	}

	contract := new(livecontract.LiveContract)
	// 判断用户是否已经加入其他公会
	err = service.DB.Where("status >= ?", livecontract.StatusContracting).Not("guild_id = ?", g.ID).First(contract, "live_id = ?", liveID).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrCannotJoinTwice
	}
	// 判断是否需要邀请
	err = service.DB.First(contract, "guild_id = ? AND live_id = ? AND status >= ?",
		g.ID, liveID, livecontract.StatusUntreated).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	if !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.NewUnknownError(403, "该用户已在公会中或有未处理的邀请/申请记录")
	}

	var resp inviteResp
	resp.Success = true
	contract = new(livecontract.LiveContract)
	contract.GuildID = g.ID
	contract.GuildOwner = g.UserID
	contract.GuildName = g.Name
	contract.LiveID = liveID
	contract.Type = livecontract.FromGuild
	contract.Status = livecontract.StatusUntreated
	contract.ContractEnd = math.MaxInt32
	err = service.DB.Save(contract).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	return true, nil
}
*/
