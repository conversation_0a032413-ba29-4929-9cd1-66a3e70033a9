package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionLiveIncomeListV3 主播获取公会收益
/**
 * @api {get} /api/v2/guild/liveincomelist-v3 主播获取公会收益
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1,2,3} [type=0] 收益类型，0：礼物收益，1：贵族收益，2：超粉收益，3：玩法收益
 * @apiParam {String} start_date 开始时间（例 2019-05-01）
 * @apiParam {String} end_date 结束时间（例 2019-05-15）
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "total": 1.35,  // 收益总额（单位：元），仅第一页时返回
 *         "data": [{
 *           "user_id": 70,
 *           "username": "Mic",
 *           "title": "礼物--直播收益",
 *           "revenue": 0.45,  // 金额（单位：元）
 *           "status": 1,  // 状态：1 交易成功
 *           "confirm_time": 1556640000  // 秒级时间戳
 *         },
 *         {
 *           "user_id": 70
 *           "username": "Mic",
 *           "title": "礼物--直播收益",
 *           "revenue": 0.45,
 *           "status": 1,
 *           "confirm_time": 1556640000
 *         }],
 *         "pagination": {
 *           "count": 2,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionLiveIncomeListV3(ctx *handler.Context) (handler.ActionResponse, error) {
	var param utils.CreatorIncomeListParam
	if err := param.LoadCommonParams(ctx); err != nil {
		return nil, err
	}
	creatorID := ctx.UserID()
	guildID, err := livecontract.GetGuildID(creatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildID == 0 {
		return nil, actionerrors.ErrShouldJoinGuild
	}

	return param.CreatorIncomeList(creatorID, guildID, true)
}
