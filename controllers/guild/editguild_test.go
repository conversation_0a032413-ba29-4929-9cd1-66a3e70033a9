package guild

import (
	"strconv"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestEditGuildTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).Check(guildParams{}, "name", "intro", "mobile", "email", "qq", "owner_name",
		"owner_id_number", "owner_id_people_url", "owner_backcover_url", "corporation_name", "corporation_address", "corporation_phone",
		"business_license_number", "business_license_frontcover_url", "tax_account", "bank_account", "bank_account_name",
		"bank", "bank_address", "bank_branch", "invoice_rate", "identify_code")

	tutil.NewKeyChecker(t, tutil.FORM).Check(guildParams{}, "name", "intro", "mobile", "email", "qq", "owner_name",
		"owner_id_number", "owner_id_people_url", "owner_backcover_url", "corporation_name", "corporation_address", "corporation_phone",
		"business_license_number", "business_license_frontcover_url", "tax_account", "bank_account", "bank_account_name",
		"bank", "bank_address", "bank_branch", "invoice_rate", "identify_code")
}

func TestEditGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试提交创建新公会
	body := map[string]interface{}{
		"name":                            "",
		"intro":                           "测试新公会",
		"mobile":                          "***********",
		"email":                           "<EMAIL>",
		"qq":                              "10001",
		"owner_name":                      "法人",
		"owner_id_number":                 "123456789012345678",
		"owner_id_people_url":             "https://static-test.missevan.com/live/guild/201912/02/9f0e13ea474e453d8d4d2d4d8a571757110640.jpg",
		"owner_backcover_url":             "https://static-test.missevan.com/live/guild/201912/02/9f0e13ea474e453d8d4d2d4d8a571757110640.jpg",
		"corporation_name":                "三体有限责任公司",
		"corporation_address":             "三体星三体路",
		"corporation_phone":               "5555123",
		"business_license_number":         "123456789012345678",
		"business_license_frontcover_url": "https://static-test.missevan.com/live/guild/201912/02/9f0e13ea474e453d8d4d2d4d8a571757110640.jpg",
		"tax_account":                     "123456789012345678",
		"bank_account":                    "6227002551360968617",
		"bank_branch":                     "上海分行",
		"bank":                            "建设银行",
		"bank_address":                    "上海",
		"bank_account_name":               "三体",
		"invoice_rate":                    guild.InvoiceRateUnknown,
		"identify_code":                   "555513",
	}
	// 参数 name 为空
	c := handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	_, err := ActionEditGuild(c)
	require.Equal(actionerrors.ErrParams, err)

	nowStamp := goutil.TimeNow().Unix()
	ng := guild.Guild{
		Name:                      "公会名称已存在",
		Intro:                     "测试公会介绍",
		OwnerName:                 "钢铁侠",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://userid/201906/17/6b48915bd3c236e711ce4916272b10d6183144.jpg",
		OwnerBackcover:            "oss://userid/201906/14/6b3d208c1f4e7ebfa93cd3a472a3756e145622.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "********",
		CorporationName:           "测试公司名称",
		CorporationAddress:        "***********",
		CorporationPhone:          "324234599452345689",
		BusinessLicenseNumber:     "324234599452345689",
		BusinessLicenseFrontcover: "oss://userid/202007/22/fef8b261100e8bd20a6c2311dd22c9d3150909.jpg",
		TaxAccount:                "23333",
		BankAccount:               "91360403MA399FU36E",
		BankAccountName:           "测试银行开户名",
		Bank:                      "测试银行",
		BankAddress:               "开户行所在地",
		BankBranch:                "开户支行",
		InvoiceRate:               guild.InvoiceRateUnknown,
		Type:                      guild.TypeUnencrypted,
		Checked:                   guild.CheckedPass,
		CreateTime:                nowStamp,
		ModifiedTime:              nowStamp,
		ApplyTime:                 nowStamp,
		UserID:                    11,
	}
	ng.Encrypt()

	// 创建公会数据
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", ng.ID).FirstOrCreate(&ng).Error)

	// 测试新建公会名称重名
	body["name"] = ng.Name
	userID := int64(6666)
	require.NoError(service.DB.Table(guild.TableName()).Delete("", "user_id = ?", userID).Error)
	redisParam := make(map[string]interface{})
	redisParam[vcode.FieldVCode] = "555513"
	redisParam[vcode.FieldVCodeCounter] = 0
	redisParam[vcode.FieldVCodeObjective] = vcode.ObjectiveTypeCreateGuild
	require.NoError(service.Redis.HMSet("mobile_86***********", redisParam).Err())
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	c.User().ID = userID
	_, err = ActionEditGuild(c)
	assert.Equal(actionerrors.ErrGuildNameExists, err)

	// 正常创建公会
	body["name"] = "创建新公会"
	userID = int64(6666)
	require.NoError(service.DB.Table(guild.TableName()).Delete("", "user_id = ?", userID).Error)
	redisParam = make(map[string]interface{})
	redisParam[vcode.FieldVCode] = "555513"
	redisParam[vcode.FieldVCodeCounter] = 0
	redisParam[vcode.FieldVCodeObjective] = vcode.ObjectiveTypeCreateGuild
	require.NoError(service.Redis.HMSet("mobile_86***********", redisParam).Err())
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	c.User().ID = userID
	r, err := ActionEditGuild(c)
	require.NoError(err)
	resp := r.(*createGuildResp)
	assert.True(resp.Success)
	assert.Equal("申请成功", resp.Message)
	// 确认创建成功
	guildNew := new(guild.Guild)
	assert.NoError(service.DB.Table(guild.TableName()).Where("user_id = ?", 6666).First(guildNew).Error)
	assert.Equal(goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
		strconv.FormatInt(guildNew.CreateTime, 10), "123456789012345678"), guildNew.OwnerIDNumber)
	assert.Equal(body["name"], guildNew.Name)
	assert.True(strings.HasPrefix(guildNew.OwnerIDPeople, "oss://"))
	assert.Equal(0, guildNew.Checked)
	// 解密
	require.NoError(guildNew.Decrypt())
	assert.Equal("123456789012345678", guildNew.OwnerIDNumber)

	// 测试修改公会信息公会名称重名
	body["name"] = ng.Name
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	c.User().ID = userID
	_, err = ActionEditGuild(c)
	assert.Equal(actionerrors.ErrGuildNameExists, err)

	// 修改公会信息提交
	body["name"] = "修改创建的公会"
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	c.User().ID = userID
	_, err = ActionEditGuild(c)
	require.NoError(err)
	guildEdit := new(guild.Guild)
	assert.NoError(service.DB.Table(guild.TableName()).Where("user_id = ?", 6666).First(guildEdit).Error)
	// 确认修改成功
	assert.Equal(body["name"], guildEdit.Name)
	assert.True(strings.HasPrefix(guildEdit.OwnerIDPeople, "oss://"))
	assert.Equal(0, guildEdit.Checked)

	// 修改被拒绝状态的公会信息提交
	body["name"] = "修改被拒绝的创建的公会"
	body["identify_code"] = "777888"
	body["email"] = "<EMAIL>"
	redisParam[vcode.FieldVCode] = "777888"
	require.NoError(service.Redis.HMSet("mobile_86***********", redisParam).Err())
	guildEdit.Checked = guild.CheckedReject
	assert.NoError(service.DB.Table(guild.TableName()).Save(&guildEdit).Error)
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	c.User().ID = userID
	_, err = ActionEditGuild(c)
	require.NoError(err)

	// 确认修改成功
	g := new(guild.Guild)
	require.NoError(service.DB.Table(guild.TableName()).Where("user_id = ?", 6666).First(g).Error)

	assert.Equal(goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
		strconv.FormatInt(g.CreateTime, 10), "123456789012345678"), g.OwnerIDNumber)
	require.Equal(goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
		strconv.FormatInt(g.CreateTime, 10), "<EMAIL>"), g.Email)
	require.NoError(g.Decrypt())
	assert.Equal(body["name"], g.Name)
	assert.Equal("<EMAIL>", g.Email)
	assert.Equal(0, g.Checked)

	// 参数错误
	body["owner_id_number"] = "111"
	body["email"] = "111"
	c = handler.NewTestContext("POST", "api/v2/guild/editguild", true, body)
	r, err = ActionEditGuild(c)
	require.NoError(err)
	resp = r.(*createGuildResp)
	assert.False(resp.Success)
	assert.Len(resp.Errors, 2)
}

func TestCreateGuildCheck(t *testing.T) {
	assert := assert.New(t)

	p := guildParams{Email: "<EMAIL>", Mobile: "***********", QQ: "10001", OwnerIDNumber: "41112341234123412X", InvoiceRate: guild.InvoiceRateUnknown}
	assert.Empty(p.check())
	assert.Equal("86***********", p.regionMobile)

	p.Email = "123.com.ckc"
	assert.Len(p.check(), 1)

	p.QQ = "马化腾"
	assert.Len(p.check(), 2)

	p.OwnerIDNumber = "xxxXXxxxxxxxxxxxx"
	assert.Len(p.check(), 3)

	p.InvoiceRate = 9********
	assert.Len(p.check(), 4)

	p.Mobile = "1311234"
	assert.Len(p.check(), 5)
}

func TestCheckQQ(t *testing.T) {
	assert := assert.New(t)

	p := guildParams{QQ: "①②③④⑤⑥"}
	assert.False(p.checkQQ())

	p.QQ = "0"
	assert.False(p.checkQQ())

	p.QQ = "-123456"
	assert.False(p.checkQQ())

	p.QQ = "123456"
	assert.True(p.checkQQ())
}

func TestCheckInvoice(t *testing.T) {
	assert := assert.New(t)

	p := guildParams{InvoiceRate: 9********}
	assert.False(p.checkInvoiceRate())

	p.InvoiceRate = 0
	assert.False(p.checkInvoiceRate())
	p.InvoiceRate = guild.InvoiceRateUnknown
	assert.True(p.checkInvoiceRate())
	p.InvoiceRate = guild.InvoiceRateNoIssue
	assert.True(p.checkInvoiceRate())
}
