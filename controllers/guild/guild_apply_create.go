package guild

import (
	"fmt"
	"html"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
)

type applyCreateParam struct {
	LiveID   int64  `json:"live_id"`
	RealName string `json:"real_name"`
	Rate     int    `json:"rate"`
	Duration int64  `json:"duration"`

	c         *handler.Context
	applyType string

	liveUser *mowangskuser.Simple

	guildInfo *guild.IDName
	role      guildrole.GuildRole
	user      *user.User
}

// 是否有发起特定申请的权限
func hasManagerCreateApplymentPermission(role guildrole.GuildRole, applyType string) bool {
	if !role.IsGuildManager() {
		return false
	}

	// 会长有公会方的所有权限
	if role.IsGuildOwner() {
		return true
	}

	// 若不是会长，公会管理员有权限发起签约或续约申请
	// TODO: 调整 sign, renew 相关的常量名
	return applyType == sign || applyType == renew
}

// 是否有操作特定申请的权限
func hasManagerOperateApplymentPermission(role guildrole.GuildRole, applymentType int64) bool {
	if !role.IsGuildManager() {
		return false
	}

	if role.IsGuildOwner() {
		return true
	}

	switch applymentType {
	case contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildSign, contractapplyment.TypeGuildRenew:
		return true
	}

	return false
}

func newApplyCreateParam(c *handler.Context, applyType string) (*applyCreateParam, error) {
	param := applyCreateParam{c: c, applyType: applyType}
	_ = c.Bind(&param)
	if param.LiveID == 0 {
		param.LiveID, _ = c.GetParamInt64("live_id")
		param.RealName, _ = c.GetParamString("real_name")
		param.Rate, _ = c.GetParamInt("rate")
		param.Duration, _ = c.GetParamInt64("duration")
	}
	// 对于解约和修改主播最低分成比例申请不需要考虑 duration
	if param.LiveID <= 0 || (applyType == sign && param.RealName == "") ||
		(applyType != editRate && applyType != terminate &&
			!contractapplyment.IsLegalContractDuration(param.Duration)) {
		return nil, actionerrors.ErrParams
	}

	// 公会发起签约主播申请需要再次校验被邀请主播的真实姓名，防止用户跳过前置校验直接请求
	if applyType == sign {
		err := validateRealNameAuthentication(param.LiveID, param.RealName)
		if err != nil {
			return nil, actionerrors.ErrCannotInviteWithoutCheckRealName
		}
	}

	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !hasManagerCreateApplymentPermission(role, applyType) {
		return nil, actionerrors.ErrNoAuthority
	}

	param.role, param.guildInfo, param.user = role, guildInfo, c.User()
	param.liveUser, err = mowangskuser.FindByUserID(param.LiveID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if param.liveUser == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	return &param, nil
}

// ActionApplySignCreate 公会发起签约主播申请
/**
 * @api {post} /api/v2/guild/applyment/sign/create 公会发起签约主播申请
 * @apiDescription 只有会长有权限在发起申请前选择最低分成比例
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} live_id 主播 id
 * @apiParam {String} real_name 用户真实姓名
 * @apiParam {number=45,50,55,60} [rate=45] 公会对主播的最低分成比例
 * @apiParam {number=2,3,4,5} duration 合约时长（2: 十二个月（已弃用），3: 二十四个月（已弃用），4: 3 年，5: 5 年）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "已向主播发送签约邀请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (403) {Number} code 500020037
 * @apiError (403) {String} info 请先验证主播真实姓名再发起邀请
 *
 */
func ActionApplySignCreate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newApplyCreateParam(c, sign)
	if err != nil {
		return nil, err
	}
	param.Rate = guildrate.ApplymentRate(param.Rate)
	if !guildrate.IsLegalContractRate(param.Rate) {
		return "", actionerrors.ErrParams
	}

	// 若申请发起人不是公会长且分成比例不为 45%
	if param.Rate != guildrate.RatePercent45 && !param.role.IsGuildOwner() {
		return nil, actionerrors.ErrReviseGuildRate("无权修改最低分成比例")
	}

	// 判断用户是否已经是公会的签约主播
	contract := new(livecontract.LiveContract)
	err = service.DB.Select("id").First(contract,
		"live_id = ? AND status = ?", param.LiveID, livecontract.StatusContracting).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if err == nil {
		return nil, actionerrors.ErrUserAlreadySigned
	}
	now := goutil.TimeNow()
	// 查找公会和主播之间未处理的签约申请
	ca := new(contractapplyment.ContractApplyment)
	err = service.DB.First(ca,
		"guild_id = ? AND live_id = ? AND status = ? AND expire_time > ?",
		param.guildInfo.ID, param.LiveID, contractapplyment.StatusPending, now.Unix()).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if err == nil {
		switch ca.Type {
		case contractapplyment.TypeLiveSign:
			return nil, actionerrors.ErrGuildApplyConflicted("该主播已申请签约，请先处理签约申请")
		case contractapplyment.TypeGuildSign:
			return nil, actionerrors.ErrGuildApplyConflicted("已邀请过该主播，请勿重复邀请")
		default:
			logger.WithField("apply_id", ca.ID).Error("unexpected case")
			return nil, handler.ErrBadRequest
		}
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 可以新建签约邀请
	ca = &contractapplyment.ContractApplyment{
		LiveID:           param.LiveID,
		GuildID:          param.guildInfo.ID,
		GuildName:        param.guildInfo.Name,
		ContractDuration: param.Duration,
		Type:             contractapplyment.TypeGuildSign,
		Status:           contractapplyment.StatusPending,
		ExpireTime:       now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		Initiator:        contractapplyment.InitiatorGuild,
		Rate:             param.Rate,
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
	}
	if !param.role.IsGuildOwner() {
		ca.AgentID = param.user.ID
	}
	err = service.DB.Save(ca).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	// 向主播发送邀请通知
	err = param.sendSignNotice(ca)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "已向主播发送签约邀请", nil
}

func (param *applyCreateParam) sendSignNotice(ca *contractapplyment.ContractApplyment) error {
	var (
		link          = fmt.Sprintf(config.Conf.Params.LiveURL.GuildApplyContract, ca.ID)
		expireTimeStr = time.Unix(ca.ExpireTime, 0).Format(util.TimeFormatYMDHHMM)

		title    string
		content  string
		smsScene string

		sendNewMsg bool
	)
	config.GetAB("enable_send_new_guild_sign_system_msg", &sendNewMsg)
	switch param.applyType {
	case sign:
		if sendNewMsg {
			title = "公会签约邀请"
			content = fmt.Sprintf("主播您好，公会【%s】邀请您加入，请在 %s 前处理。<a href=\"%s\">前往处理</a>",
				html.EscapeString(param.guildInfo.Name), expireTimeStr, link)
			smsScene = "guild_live_contract_join"
		}
	case renew:
		title = "公会续约邀请"
		if sendNewMsg {
			content = fmt.Sprintf("主播您好，公会【%s】邀请您续约，请在 %s 前处理。<a href=\"%s\">前往处理</a>",
				html.EscapeString(param.guildInfo.Name), expireTimeStr, link)
			smsScene = "guild_live_contract_renew"
		} else {
			content = fmt.Sprintf("直播公会 %s 向您发出时限为 %s的续约邀请，合约到期前未处理，将自动解约，"+
				"可在主播工作台 > 申请 / 邀请记录 > 邀请记录 查看",
				html.EscapeString(param.guildInfo.Name), contractapplyment.FormatContractDuration(ca))
		}
	default:
		panic("unsupported apply type")
	}
	if title != "" && content != "" {
		err := service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{
			{
				UserID:  param.LiveID,
				Title:   title,
				Content: content,
			},
		}, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	if smsScene == "" {
		return nil
	}
	shortURL, err := userapi.ShortURL(param.c.UserContext(), link)
	if err != nil {
		return err
	}
	acc, err := service.SSO.UserInfo(param.LiveID)
	if err != nil {
		return err
	}
	if acc.Mobile == "" {
		// 主播未绑定手机号，不发送短信
		return nil
	}
	sms := pushservice.SMS{
		To:         fmt.Sprintf("+%d%s", acc.Region, acc.Mobile),
		RegionCode: acc.Region,
		Scene:      smsScene,
		Payload: map[string]interface{}{
			"guild_name": param.guildInfo.Name,
			"time":       expireTimeStr,
			"link":       shortURL,
		},
	}
	err = service.PushService.SendSMS(sms)
	if err != nil {
		return err
	}
	return nil
}

// ActionApplyRenewCreate 公会发起续约主播申请
/**
 * @api {post} /api/v2/guild/applyment/renew/create 公会发起续约主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} live_id 主播 id
 * @apiParam {number=45,50,55,60} [rate=旧合约分成比例] 公会对主播的最低分成比例
 * @apiParam {number=2,3,4,5} duration 合约时长（2: 十二个月（已弃用），3: 二十四个月（已弃用），4: 3 年，5: 5 年）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "已向主播发送续约申请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 */
func ActionApplyRenewCreate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newApplyCreateParam(c, renew)
	if err != nil {
		return nil, err
	}
	// 判断用户是否已经是本公会签约主播
	contract := new(livecontract.LiveContract)
	err = service.DB.First(contract, "guild_id = ? AND live_id = ? AND status = ?",
		param.guildInfo.ID, param.LiveID, livecontract.StatusContracting).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrGuildContractConflicted("操作失败，该主播未与您签约")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.Rate == 0 {
		// rate 默认为旧合约分成比例
		param.Rate = guildrate.ApplymentRate(contract.Rate)
	}
	if param.Rate != contract.Rate && !guildrate.IsLegalContractRate(param.Rate) {
		return "", actionerrors.ErrParams
	}
	// 判断当前时刻是否允许发送续约申请
	now := goutil.TimeNow()
	if contract.ContractEnd > now.Unix()+fifteenDaySec {
		return nil, actionerrors.ErrGuildContractConflicted(
			"操作失败，合约到期前 15 天才能邀请续约")
	}
	// 判断是否已有未处理申请存在
	ca := new(contractapplyment.ContractApplyment)
	err = service.DB.First(ca,
		"guild_id = ? AND live_id = ? AND status = 0 AND expire_time > ?",
		param.guildInfo.ID, param.LiveID, now.Unix()).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err == nil {
		// 有未处理的申请
		switch ca.Type {
		case contractapplyment.TypeLiveRenew:
			return nil, actionerrors.ErrGuildApplyConflicted("该主播已申请续约，请先处理续约申请")
		case contractapplyment.TypeGuildRenew:
			return nil, actionerrors.ErrGuildApplyConflicted("您已邀请该主播续约，请勿重复邀请")
		case contractapplyment.TypeLiveTerminate:
			return nil, actionerrors.ErrGuildApplyConflicted("请先处理该主播的协商解约申请")
		case contractapplyment.TypeGuildExpel:
			return nil, actionerrors.ErrGuildApplyConflicted("请先撤回对该主播的清退申请")
		}
	}
	if !param.role.IsGuildOwner() {
		isAssigned, err := guildagent.IsAssigned(contract.LiveID, param.user.ID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !isAssigned {
			return nil, actionerrors.ErrCreatorNotAssignedToYou
		}
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 可以新建续约邀请
	ca = &contractapplyment.ContractApplyment{
		LiveID:             param.LiveID,
		GuildID:            param.guildInfo.ID,
		GuildName:          param.guildInfo.Name,
		ContractID:         contract.ID,
		ContractDuration:   param.Duration,
		ContractExpireTime: contractapplyment.GetContractEnd(time.Unix(contract.ContractEnd, 0), param.Duration).Unix(),
		Type:               contractapplyment.TypeGuildRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               param.Rate,
		CreateTime:         now.Unix(),
		ModifiedTime:       now.Unix(),
	}
	if !param.role.IsGuildOwner() {
		ca.AgentID = param.user.ID
	}
	err = service.DB.Save(ca).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 向主播发送续约通知
	err = param.sendSignNotice(ca)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "已向主播发送续约申请", nil
}

// ActionApplyTerminateCreate 公会发起清退主播申请
/**
 * @api {post} /api/v2/guild/applyment/terminate/create 公会发起清退主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "清退申请已提交，将于 2 天后生效，生效前可随时撤回清退申请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 */
func ActionApplyTerminateCreate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newApplyCreateParam(c, terminate)
	if err != nil {
		return nil, err
	}
	// 判断用户是否已经是本公会签约主播
	contract, err := livecontract.FindInContractingByLiveID(param.LiveID, param.guildInfo.ID, "id, rate, contract_duration, contract_end, contract_start")
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if contract == nil {
		return nil, actionerrors.ErrGuildContractConflicted("操作失败，该主播未与您签约")
	}

	// 三方独家主播不可被清退
	exclusive, err := exclusivecreator.IsExclusiveCreator(param.LiveID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exclusive {
		return nil, actionerrors.ErrExclusiveCreatorConflicted("三方独家主播不可被清退")
	}

	// 受限房间禁止退会
	r, err := room.FindOne(bson.M{"creator_id": param.LiveID},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r != nil && r.Limit != nil {
		return nil, actionerrors.ErrNoAuthority
	}
	// 判断当前时刻是否允许发送清退申请
	inProtecting := livecontract.IsInProtectingGuildExpel(contract.ContractStart)
	if inProtecting {
		return nil, actionerrors.ErrGuildContractConflicted(fmt.Sprintf("该主播签约未满 %d 天，无法清退", livecontract.ProtectedDaysOfGuildExpel))
	}
	// 判断是否已有未处理申请存在
	now := goutil.TimeNow()
	ca := new(contractapplyment.ContractApplyment)
	err = service.DB.First(ca,
		"guild_id = ? AND live_id = ? AND status = ? AND expire_time > ?",
		param.guildInfo.ID, param.LiveID, contractapplyment.StatusPending, now.Unix()).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if err == nil {
		// 有未处理的申请
		switch ca.Type {
		case contractapplyment.TypeLiveRenew:
			return nil, actionerrors.ErrGuildApplyConflicted("请先处理该主播的续约申请")
		case contractapplyment.TypeGuildRenew:
			return nil, actionerrors.ErrGuildApplyConflicted("请先撤回对该主播的续约申请")
		case contractapplyment.TypeLiveTerminate:
			return nil, actionerrors.ErrGuildApplyConflicted("请先处理该主播的协商解约申请")
		case contractapplyment.TypeGuildExpel:
			return nil, actionerrors.ErrGuildApplyConflicted("您已清退该主播，请勿重复清退")
		}
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 发起清退主播申请
	ca = &contractapplyment.ContractApplyment{
		LiveID:             param.LiveID,
		GuildID:            param.guildInfo.ID,
		GuildName:          param.guildInfo.Name,
		ContractID:         contract.ID,
		ContractDuration:   int64(contract.ContractDuration),
		ContractExpireTime: contract.ContractEnd,
		Type:               contractapplyment.TypeGuildExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         contractapplyment.ExpireTime(now, contractapplyment.TypeGuildExpel),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               guildrate.ApplymentRate(contract.Rate),
		CreateTime:         now.Unix(),
		ModifiedTime:       now.Unix(),
	}
	err = service.DB.Save(ca).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	return "清退申请已提交，将于 2 天后生效，生效前可随时撤回清退申请", nil
}

type applyTerminateBatchCreateParam struct {
	MetricStartTime int64            `json:"metric_start_time"`
	MetricEndTime   int64            `json:"metric_end_time"`
	CSVURL          upload.SourceURL `json:"csv_url"`

	liveIDBatches [][]int64
	applyCounts   int64
	contractMap   map[int64]*livecontract.LiveContract
}

// ActionApplyTerminateBatchCreate 批量发起清退主播申请
/**
 * @api {post} /api/v2/admin/guild/applyment/terminate/batch-create 批量发起清退主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} metric_start_time 数据统计的开始时间（秒级时间戳）
 * @apiParam {Number} metric_end_time 数据统计的结束时间（秒级时间戳）
 * @apiParam {String} csv_url 含有主播 ID 列表（带表头）的 csv 地址
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "清退申请已全部提交"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionApplyTerminateBatchCreate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newApplyTerminateBatchCreateParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	// 发起清退主播申请
	err = param.insert()
	if err != nil {
		return nil, err
	}
	// 添加管理员操作日志
	param.addAdminLog(c)
	return "清退申请已全部提交", nil
}

func newApplyTerminateBatchCreateParam(c *handler.Context) (*applyTerminateBatchCreateParam, error) {
	var param applyTerminateBatchCreateParam
	err := c.Bind(&param)
	if err != nil || param.CSVURL == "" || param.MetricStartTime == 0 || param.MetricEndTime == 0 {
		return nil, actionerrors.ErrParams
	}
	err = param.parseCSV()
	if err != nil {
		return nil, err
	}
	param.contractMap = make(map[int64]*livecontract.LiveContract, param.applyCounts)
	return &param, nil
}

func (p *applyTerminateBatchCreateParam) parseCSV() error {
	res, err := service.Upload.ToResource(p.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	p.applyCounts = 0
	liveIDSet := make(map[int64]struct{})
	callback := func(rows [][]string, _ int64) error {
		liveIDs := make([]int64, 0, len(rows))
		for i := range rows {
			if len(rows[i]) != 1 {
				return actionerrors.ErrParamsMsg("CSV 列数不正确，请检查后再试")
			}
			liveID, err := strconv.ParseInt(rows[i][0], 10, 64)
			if err != nil {
				return actionerrors.ErrParamsMsg("CSV 的主播 ID 字段不可用，请检查后再试")
			}
			if _, ok := liveIDSet[liveID]; ok {
				// 跳过重复的
				continue
			}
			liveIDSet[liveID] = struct{}{}
			liveIDs = append(liveIDs, liveID)
		}
		p.liveIDBatches = append(p.liveIDBatches, liveIDs)
		p.applyCounts += int64(len(liveIDs))
		return nil
	}
	return csv.Read(file, callback)
}

func (p *applyTerminateBatchCreateParam) check() error {
	for _, liveIDs := range p.liveIDBatches {
		err := p.checkExclusive(liveIDs)
		if err != nil {
			return err
		}
		err = p.checkContract(liveIDs)
		if err != nil {
			return err
		}
		err = p.checkLive(liveIDs)
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *applyTerminateBatchCreateParam) checkExclusive(liveIDs []int64) error {
	exclusiveCreatorIDs, err := exclusivecreator.FindExclusiveCreatorIDs(liveIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(exclusiveCreatorIDs) != 0 {
		logger.WithField("live_ids", exclusiveCreatorIDs).Error("三方独家主播不可被清退")
		return actionerrors.ErrExclusiveCreatorConflicted(fmt.Sprintf("有三方独家主播不可被清退，live_ids: %v", exclusiveCreatorIDs))
	}
	return nil
}

func (p *applyTerminateBatchCreateParam) checkContract(liveIDs []int64) error {
	contracts := make([]*livecontract.LiveContract, 0, len(liveIDs))
	err := livecontract.LiveContract{}.DB().Where("live_id IN (?) AND status = ? AND contract_end > ?",
		liveIDs, livecontract.StatusContracting, goutil.TimeNow().Unix()).Find(&contracts).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, c := range contracts {
		p.contractMap[c.LiveID] = c
	}
	for _, id := range liveIDs {
		contract, ok := p.contractMap[id]
		if !ok {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("操作失败，有主播未与公会签约，live_id: %d", id))
		}
		if contract.ContractStart > p.MetricStartTime {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("无法清退，有主播签约时间晚于操作开始时间，live_id: %d", id))
		}
	}
	return nil
}

const (
	// 清退主播不能超过该开播次数（单位：次）
	terminateMaxLiveTimes = 10
	// 清退主播不能超过该直播有效天数（单位：天）
	terminateMaxLiveDays = 5
	// 清退主播的最大可获取过的直播流水（单位：钻）
	terminateMaxLiveRevenue = 10000
)

func (p *applyTerminateBatchCreateParam) checkLive(liveIDs []int64) error {
	startTime := time.Unix(p.MetricStartTime, 0)
	endTime := time.Unix(p.MetricEndTime, 0)
	metricsMap, err := livelog.GetUserGuildTerminationMetrics(liveIDs, startTime, endTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, liveID := range liveIDs {
		contract := p.contractMap[liveID]
		if contract == nil {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("操作失败，有主播未与公会签约，live_id: %d", liveID))
		}
		gID := contract.GuildID
		metrics, ok := metricsMap[liveID][gID]
		if !ok {
			continue
		}
		if metrics.Count >= terminateMaxLiveTimes {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("操作失败，有主播在当前公会开播次数不小于 %d 次，live_id: %d", terminateMaxLiveTimes, liveID))
		}
		if metrics.Days >= terminateMaxLiveDays {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("操作失败，有主播在当前公会开播有效天数不小于 %d 天，live_id: %d", terminateMaxLiveDays, liveID))
		}
		if metrics.Revenue > terminateMaxLiveRevenue {
			return actionerrors.ErrGuildContractConflicted(fmt.Sprintf("操作失败，有主播在当前公会流水大于 %d 钻，live_id: %d", terminateMaxLiveRevenue, liveID))
		}
	}
	return nil
}

func (p *applyTerminateBatchCreateParam) insert() error {
	for _, liveIDs := range p.liveIDBatches {
		cas := make([]*contractapplyment.ContractApplyment, 0, len(liveIDs))
		for _, liveID := range liveIDs {
			contract := p.contractMap[liveID]
			if contract == nil {
				logger.Errorf("无法获取公会签约合同，live_id: %d", liveID)
				continue
			}
			// 发起清退主播申请
			now := goutil.TimeNow()
			ca := &contractapplyment.ContractApplyment{
				LiveID:             liveID,
				GuildID:            contract.GuildID,
				GuildName:          contract.GuildName,
				ContractID:         contract.ID,
				ContractDuration:   contract.ContractDuration,
				ContractExpireTime: contract.ContractEnd,
				Type:               contractapplyment.TypePlatformExpel,
				Status:             contractapplyment.StatusPending,
				ExpireTime:         now.Add(10 * time.Minute).Unix(), // 强制预留 10 分钟检查数据
				Initiator:          contractapplyment.InitiatorPlatform,
				Rate:               guildrate.ApplymentRate(contract.Rate),
				CreateTime:         now.Unix(),
				ModifiedTime:       now.Unix(),
			}
			cas = append(cas, ca)
		}
		if len(cas) == 0 {
			// 批次为空时跳过插入步骤
			continue
		}
		err := servicedb.BatchInsert(service.DB, contractapplyment.TableName(), cas)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

// 添加管理员操作日志
func (p *applyTerminateBatchCreateParam) addAdminLog(c *handler.Context) {
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("平台清退主播建单，影响总数: %d", p.applyCounts)
	box.Add(userapi.CatalogManageGuildMember, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionApplicationRateEdit 公会申请修改主播最低分成比例
/**
 * @api {post} /api/v2/guild/application/rate/edit 公会申请修改主播最低分成比例
 * @apiDescription 只有会长有权限申请修改主播最低分成比例
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild/application
 * @apiName rate/edit
 *
 * @apiParam {Number} live_id 主播 id
 * @apiParam {number{1-99}} rate 修改分成比例数，调整范围 0 < rate < 100
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": "修改成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionApplicationRateEdit(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newApplyCreateParam(c, editRate)
	if err != nil {
		return nil, err
	}
	// rate 调整范围是 0 < rate < 100
	if param.LiveID <= 0 || param.Rate <= 0 || param.Rate >= 100 {
		return nil, actionerrors.ErrParams
	}

	// 判断用户是否已经是本公会签约主播
	contract := new(livecontract.LiveContract)
	err = service.DB.First(contract, "guild_id = ? AND live_id = ? AND status = ?",
		param.guildInfo.ID, param.LiveID, livecontract.StatusContracting).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrGuildContractConflicted("操作失败，该主播未与您签约")
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	// 原比例与调整比例相同返回参数错误
	if contract.Rate == param.Rate {
		return nil, actionerrors.ErrReviseGuildRate("与当前最低分成比例相同，修改失败！")
	}

	// 查找最新未过期的降薪申请
	now := goutil.TimeNow()
	application := new(contractapplyment.ContractApplyment)
	err = application.DB().Where("guild_id = ? AND contract_id = ? AND live_id = ? AND type = ? AND expire_time > ?",
		param.guildInfo.ID, contract.ID, param.LiveID, contractapplyment.TypeRateDown, now.Unix()).
		Order("create_time DESC").First(application).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	// 若有未过期的降薪申请，需判断公会是否有权利调整主播薪水
	if application.ID > 0 {
		// 是否降薪申请还未处理
		endTime := time.Unix(application.ExpireTime, 0)
		if application.Status == contractapplyment.StatusPending {
			// 获取申请发起前主播与公会最低分成比例
			more, err := application.UnmarshalMore()
			if err != nil {
				return nil, actionerrors.ErrServerInternal.New(err, nil)
			}
			if more == nil || more.Rate == nil {
				return nil, actionerrors.ErrReviseGuildRate("数据异常，请联系客服处理")
			}
			errMessage := fmt.Sprintf("无法修改当前主播的最低分成比例！最低分成比例调整确认中：%d → %d 主播需在 %s 前确认，逾期将自动拒绝调整",
				*more.Rate, application.Rate, endTime.Format(util.TimeFormatYMDHHMM))
			return nil, actionerrors.ErrReviseGuildRate(errMessage)
		}

		// 同一合约下，最近一次降薪申请还未过期，则不可连续降薪
		if param.Rate < contract.Rate {
			errMessage := fmt.Sprintf("短期内降薪频繁！请在 %s 后申请调整", endTime.Format(util.TimeFormatYMDHHMM))
			return nil, actionerrors.ErrReviseGuildRate(errMessage)
		}
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 新建修改最低分成比例申请
	newApplication, err := contract.NewGuildRateApplication(param.Rate)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if newApplication == nil {
		return nil, actionerrors.ErrReviseGuildRate("创建修改主播最低分成比例记录失败")
	}

	// 保存合约记录
	if err = livecontract.SaveGuildRate(newApplication); err != nil {
		if err == servicedb.ErrNoRowsAffected {
			return nil, actionerrors.ErrContractNotExist
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 向主播发送私信
	var resp string
	var title string
	var message string
	if newApplication.Type == contractapplyment.TypeRateUp {
		resp = "修改成功"
		title = "公会主播最低分成比例变更通知"
		message = fmt.Sprintf("亲爱的主播，您的公会%s已将您的最低分成比例从 %d%% 变更为 %d%%，如有疑问，请联系公会管理人员。",
			html.EscapeString(param.guildInfo.Name), contract.Rate, newApplication.Rate)
	} else {
		resp = "修改申请已发送"
		expireTime := time.Unix(newApplication.ExpireTime, 0)
		title = "公会主播最低分成比例变更确认"
		url := fmt.Sprintf("%s?id=%d&webview=1", config.Conf.Params.GuildRate.GuildRateDetailURL, newApplication.ID)
		message = fmt.Sprintf("亲爱的主播，您的公会%s正在申请将您的最低分成比例从 %d%% 变更为 %d%%，请在 %s 前处理。<a href=\"%s\">前往处理</a>",
			html.EscapeString(param.guildInfo.Name), contract.Rate, newApplication.Rate, expireTime.Format(util.TimeFormatYMDHHMM), url)
	}

	// 发送系统通知
	sysMsg := []pushservice.SystemMsg{{
		Title:   title,
		UserID:  param.LiveID,
		Content: message,
	}}
	err = service.PushService.SendSystemMsgWithOptions(sysMsg,
		&pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return resp, nil
}
