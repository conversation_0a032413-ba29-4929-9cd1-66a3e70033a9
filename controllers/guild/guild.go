// Package guild 直播间公会后端接口
// 需求文档 PR: https://github.com/MiaoSiLa/requirements-doc/pull/155
// TODO：公会创建的几个页面
// TODO: 需要把一些代码移动到 models 中
// NOTICE: 公会页面的接口都需要验证是否是公会会长
// TODO: 超管接口需要迁移
package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/guild/guildbackend"
	"github.com/MiaoSiLa/live-service/controllers/guild/livebackend"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler returns the registered handler
// 未包含超管部分的 action
// TODO: 禁用旧接口
func Handler() handler.Handler {
	return handler.Handler{
		Name: "guild",
		SubHandlers: []handler.Handler{
			livebackendHandler(),        // 公会二期主播后台
			applymentHandler(),          // 公会二期公会后台申请相关
			agentHandler(),              // 公会经纪人操作相关
			squareHotRecommendHandler(), // 热门列表推荐位相关
			recommendHandler(),          // 公会资源位申请相关
			applicationHandler(),        // 公会后台申请相关
		},
		Actions: map[string]*handler.Action{
			"role":           handler.NewAction(handler.GET, ActionRole, false),          // 用户在公会中的角色
			"contract/rates": handler.NewAction(handler.GET, ActionContractRates, false), // 获取公会与主播最低分成比例

			/* --- 公会后台 --- */
			// TODO: 删除 ActionIsowner
			// "isowner":    handler.NewAction(handler.GET, ActionIsowner, false),    // 判断是否是公会会长（弃用，由接口 /api/v2/guild/role 代替）
			"searchlive": handler.NewAction(handler.GET, ActionSearchLive, false), // 查询主播

			// TODO: 删除 ActionGuildIncome, ActionGuildIncomeExport
			// "guildincome":       handler.NewAction(handler.GET, ActionGuildIncome, true),       // 公会收益
			// "guildincomeexport": handler.NewAction(handler.GET, ActionGuildIncomeExport, true), // 导出公会收益

			"getguildinfo": handler.NewAction(handler.GET, ActionGetGuildInfo, true), // 获取公会创建信息

			// DEPRECATED
			"guildliveincome":      handler.NewAction(handler.GET, ActionGuildLiveIncome, true),     // 获取公会主播收益流水
			"guildincome-v2":       handler.NewAction(handler.GET, ActionGuildIncomeV2, true),       // 公会收益
			"guildincomeexport-v2": handler.NewAction(handler.GET, ActionGuildIncomeExportV2, true), // 导出公会收益

			// TODO: guildliveincome-v3, guildincome-v3, guildincomeexport-v3 前后端上线使用后，删除 guildliveincome, guildincome-v2, guildincomeexport-v2
			"guildliveincome-v3":   handler.NewAction(handler.GET, ActionGuildLiveIncomeV3, true),   // 获取公会主播收益流水
			"guildincome-v3":       handler.NewAction(handler.GET, ActionGuildIncomeV3, true),       // 公会收益
			"guildincomeexport-v3": handler.NewAction(handler.GET, ActionGuildIncomeExportV3, true), // 导出公会收益

			"data-report":        handler.NewAction(handler.GET, ActionGuildDataReport, true),       // 公会数据报表
			"data-report-export": handler.NewAction(handler.GET, ActionGuildDataReportExport, true), // 导出公会数据报表

			"member/list": handler.NewAction(handler.GET, ActionMemberList, true), // 公会查看签约主播列表

			/* --- 主播后台 --- */
			// TODO: 等前端将 my 换成 mycontract 后删除 my 接口
			"my":         handler.NewAction(handler.GET, ActionMyContract, true), // 主播后台主播自己所属公会合约
			"mycontract": handler.NewAction(handler.GET, ActionMyContract, true), // 主播后台主播自己所属公会合约
			"search":     handler.NewAction(handler.GET, ActionSearch, false),    // 查询公会

			// DEPRECATED
			"liveincomelist": handler.NewAction(handler.GET, ActionLiveIncomeList, true), // 主播获取公会收益
			// TODO: liveincomelist-v3 前后端上线后，删除 liveincomelist
			"liveincomelist-v3": handler.NewAction(handler.GET, ActionLiveIncomeListV3, true), // 主播获取公会收益

			"livewithdrawrecord": handler.NewAction(handler.GET, ActionLiveWithdrawRecord, true), // 主播后台获取主播提现记录
			"agreement":          handler.NewAction(handler.GET, ActionAgreement, true),          // 获取服务协议内容
			"confirmagreement":   handler.NewAction(handler.POST, ActionConfirmAgreement, true),  // 主播确认接受服务协议

			/* --- 公会入驻 --- */
			"editguild": handler.NewAction(handler.POST, ActionEditGuild, true), // 创建和修改公会入驻申请

			/* --- 公会权限 --- */
			"sendvcode":         handler.NewAction(handler.POST, ActionSendVCode, false),        // 发送短信验证码
			"getusermobile":     handler.NewAction(handler.GET, ActionGetUserMobile, true),      // 获取用户脱敏手机号
			"setuserpermission": handler.NewAction(handler.POST, ActionSetUserPermission, true), // 验证并设置用户操作（签约、续约、解约）临时权限
		},
	}
}

func livebackendHandler() handler.Handler {
	return handler.Handler{
		Name: "livebackend",
		Actions: map[string]*handler.Action{
			"sign":   handler.NewAction(handler.POST, livebackend.ActionSign, true),   // 主播申请签约
			"renew":  handler.NewAction(handler.POST, livebackend.ActionRenew, true),  // 主播申请续约
			"revoke": handler.NewAction(handler.POST, livebackend.ActionRevoke, true), // 主播申请撤回
			// TODO: 新版解约流程上线后此接口可删除
			"terminate":        handler.NewAction(handler.POST, livebackend.ActionTerminate, true),        // 主播申请协商解约
			"terminateforcely": handler.NewAction(handler.POST, livebackend.ActionTerminateForcely, true), // 主播强制解约

			"passinvite":    handler.NewAction(handler.POST, livebackend.ActionPassInvite, true),    // 主播同意公会邀请
			"declineinvite": handler.NewAction(handler.POST, livebackend.ActionDeclineInvite, true), // 主播拒绝公会邀请
			"invitelist":    handler.NewAction(handler.GET, livebackend.ActionInviteList, true),     // 公会邀请记录
			// TODO: 前端改了之后可以删除
			"applymentlist":   handler.NewAction(handler.GET, livebackend.ActionApplymentList, true),   // 主播申请记录
			"applymentdetail": handler.NewAction(handler.GET, livebackend.ActionApplymentDetail, true), // 合约申请内容弹窗

			"application/list":              handler.NewAction(handler.GET, livebackend.ActionApplymentList, true),                 // 主播申请记录
			"application/detail":            handler.NewAction(handler.GET, livebackend.ActionApplymentDetail, true),               // 合约申请内容弹窗
			"application/pending-count":     handler.NewAction(handler.GET, livebackend.ActionApplymentPendingCount, true),         // 主播未处理的邀请数量
			"application/terminate-check":   handler.NewAction(handler.POST, livebackend.ActionApplicationTerminateCheck, true),    // 主播协商解约检查
			"application/terminate":         handler.NewAction(handler.POST, livebackend.ActionApplicationTerminate, true),         // 主播申请协商解约
			"application/terminateforcibly": handler.NewAction(handler.POST, livebackend.ActionApplicationTerminateForcibly, true), // 主播强制解约

			"rate/agree":    handler.NewAction(handler.POST, livebackend.ActionRateAgree, true),    // 主播同意公会降低最低分成比例
			"rate/disagree": handler.NewAction(handler.POST, livebackend.ActionRateDisagree, true), // 主播拒绝公会降低最低分成比例
			"rate/info":     handler.NewAction(handler.GET, livebackend.ActionRateInfo, true),      // 主播获取公会修改主播最低分成比例详情
		},
	}
}

func applymentHandler() handler.Handler {
	return handler.Handler{
		Name: "applyment",
		Actions: map[string]*handler.Action{
			"sign/list":        handler.NewAction(handler.GET, ActionApplySignList, true),         // 公会查看签约申请列表
			"sign/create":      handler.NewAction(handler.POST, ActionApplySignCreate, true),      // 公会发起邀请主播申请
			"sign/pass":        handler.NewAction(handler.POST, ActionApplySignPass, true),        // 公会通过邀请主播申请
			"sign/refuse":      handler.NewAction(handler.POST, ActionApplySignRefuse, true),      // 公会拒绝邀请主播申请
			"sign/revoke":      handler.NewAction(handler.POST, ActionApplySignRevoke, true),      // 公会撤回邀请主播申请
			"renew/list":       handler.NewAction(handler.GET, ActionApplyRenewList, true),        // 公会查看续约申请列表
			"renew/create":     handler.NewAction(handler.POST, ActionApplyRenewCreate, true),     // 公会发起续约主播申请
			"renew/pass":       handler.NewAction(handler.POST, ActionApplyRenewPass, true),       // 公会通过续约主播申请
			"renew/refuse":     handler.NewAction(handler.POST, ActionApplyRenewRefuse, true),     // 公会拒绝邀请主播申请
			"renew/revoke":     handler.NewAction(handler.POST, ActionApplyRenewRevoke, true),     // 公会撤回续约主播申请
			"terminate/list":   handler.NewAction(handler.GET, ActionApplyTerminateList, true),    // 公会查看解约申请列表
			"terminate/create": handler.NewAction(handler.POST, ActionApplyTerminateCreate, true), // 公会发起解约主播申请
			"terminate/pass":   handler.NewAction(handler.POST, ActionApplyTerminatePass, true),   // 公会通过解约主播申请
			"terminate/refuse": handler.NewAction(handler.POST, ActionApplyTerminateRefuse, true), // 公会拒绝解约主播申请
			"terminate/revoke": handler.NewAction(handler.POST, ActionApplyTerminateRevoke, true), // 公会撤回解约主播申请
			"terminate/price":  handler.NewAction(handler.GET, ActionApplyTerminatePrice, true),   // 公会获取强制解约申请违约金
			"pending-count":    handler.NewAction(handler.GET, ActionApplymentPendingCount, true), // 公会未处理的签约/续约/解约数量
		},
	}
}

func agentHandler() handler.Handler {
	return handler.Handler{
		Name: "agent",
		Actions: map[string]*handler.Action{
			"search":      handler.NewAction(handler.GET, guildbackend.ActionGuildSearchAgent, true),         // 公会搜索经纪人
			"operate":     handler.NewAction(handler.POST, guildbackend.ActionGuildOperateAgent, true),       // 公会添加或移除经纪人
			"list":        handler.NewAction(handler.GET, guildbackend.ActionGuildAgentList, true),           // 公会获取经纪人列表
			"assign-list": handler.NewAction(handler.GET, guildbackend.ActionGuildAgentAssignList, true),     // 公会获取待分配的经纪人列表
			"assign":      handler.NewAction(handler.POST, guildbackend.ActionGuildAssignAgentCreator, true), // 公会为主播分配/取消分配经纪人
		},
	}
}

func squareHotRecommendHandler() handler.Handler {
	return handler.Handler{
		Name: "recommend/squarehot", // 直播广场热门位相关
		Actions: map[string]*handler.Action{
			"list":    handler.NewAction(handler.GET, guildbackend.ActionSquareHotList, true),    // 获取所有的申请记录
			"vacancy": handler.NewAction(handler.GET, guildbackend.ActionSquareHotVacancy, true), // 获取剩余推荐次数
			"time":    handler.NewAction(handler.GET, guildbackend.ActionSquareHotTime, true),    // 获取可申请时间
			"add":     handler.NewAction(handler.POST, guildbackend.ActionSquareHotAdd, true),    // 新增申请
		},
	}
}

func recommendHandler() handler.Handler {
	return handler.Handler{
		Name: "recommend",
		Actions: map[string]*handler.Action{
			"schedule/apply-add":  handler.NewAction(handler.POST, guildbackend.ActionRecommendScheduleApplyAdd, true),  // 公会添加首页推荐申请
			"schedule/apply-edit": handler.NewAction(handler.POST, guildbackend.ActionRecommendScheduleApplyEdit, true), // 公会修改首页推荐申请
			"schedule/apply-set":  handler.NewAction(handler.POST, guildbackend.ActionRecommendScheduleApplySet, true),  // 公会修改首页推荐申请状态
			"schedule/apply":      handler.NewAction(handler.GET, guildbackend.ActionRecommendScheduleApply, true),      // 公会获取首页推荐申请详情
			"schedule/apply-list": handler.NewAction(handler.GET, guildbackend.ActionRecommendScheduleApplyList, true),  // 公会获取首页推荐申请列表
			"schedule/list":       handler.NewAction(handler.GET, guildbackend.ActionRecommendScheduleList, true),       // 公会获取首页推荐排期结果

			"banner/list":    handler.NewAction(handler.GET, guildbackend.ActionBannerList, true),
			"banner/add":     handler.NewAction(handler.POST, guildbackend.ActionBannerAdd, true),
			"banner/vacancy": handler.NewAction(handler.GET, guildbackend.ActionBannerVacancy, true),
		},
	}
}

func applicationHandler() handler.Handler {
	return handler.Handler{
		Name: "application",
		Actions: map[string]*handler.Action{
			"rate/edit": handler.NewAction(handler.POST, ActionApplicationRateEdit, true), // 公会申请修改主播最低分成比例
			"rate/info": handler.NewAction(handler.GET, ActionApplicationRateInfo, true),  // 公会获取降薪申请详情
		},
	}
}
