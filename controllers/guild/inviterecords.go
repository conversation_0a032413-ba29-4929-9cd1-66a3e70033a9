package guild

// ActionInviteRecords 公会邀请主播入会记录
/*
 * @api {get} /api/v2/guild/inviterecords 公会邀请主播入会记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [page=1] 第几页
 * @apiParam {Number} [page_size=20] 每页个数
 * @apiParam {String} [s=""] 过滤词
 * @apiParam {number=0,1} [type=0] 过滤类型（0 会长 ID，1 公会名称）
 * @apiParam {number=-3,-2,1,0,1} [status] 合约状态（-3 失效，-2 解约，-1 拒绝，0 未处理，1 已同意即生效中）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
/*
func ActionInviteRecords(c *handler.Context) (handler.ActionResponse, error) {
	page, err := c.GetDefaultParamInt64("page", 1)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	pageSize, err := c.GetDefaultParamInt64("page_size", models.DefaultPageSize)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	searchType, err := strconv.Atoi(c.C.DefaultQuery("type", "0"))
	if err != nil {
		return nil, err
	}
	keyword := c.C.Query("s")

	// status 默认取 10（不在 status 取值范围内，代表获取所有的状态）
	status, err := c.GetDefaultParamInt("status", 10)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	var inviteRecords livecontract.ReturnData
	inviteRecords, err = livecontract.LiveContract{}.InviteOrJoinRecords(livecontract.FromGuild, c.User().ID, page, pageSize, keyword, searchType, status)
	if err != nil {
		return nil, err
	}

	return inviteRecords, nil
}
*/
