package guild

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSquareHotVacancyUpdate(t *testing.T) {
	require := require.New(t)

	p := squareHotVacancyUpdateParam{Position: 1, Vacancy: 12}
	c := handler.NewTestContext("POST", "update", true, p)
	_, err := ActionSquareHotVacancyUpdate(c)
	require.Equal(actionerrors.ErrParams, err)
	p.Position = 4
	p.Data = []guildID{{
		GuildID: 1222,
	}}
	c = handler.NewTestContext("POST", "update", true, p)
	_, err = ActionSquareHotVacancyUpdate(c)
	require.Equal(actionerrors.ErrGuildNotExist, err)
	p.Data = []guildID{{
		GuildID: 3,
	}}
	startTime, endTime := currentPeriod()
	g := recommend.GuildRecommendVacancy{
		Position:  Position4,
		GuildID:   3,
		Vacancy:   1,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&g).Error)
	c = handler.NewTestContext("POST", "update", true, p)
	_, err = ActionSquareHotVacancyUpdate(c)
	require.NoError(err)
}

func TestSquareHotVacancyUpdate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	guildID := goutil.TimeNow().Unix()
	position := 9
	err := squareHotVacancyUpdate(guildID, 6, position)
	require.Error(err, "no rows affected")
	startTime, endTime := currentPeriod()
	v := recommend.GuildRecommendVacancy{
		Position:       position,
		GuildID:        guildID,
		InitialVacancy: 10,
		EditedVacancy:  10,
		Vacancy:        1,
		StartTime:      startTime.Unix(),
		EndTime:        endTime.Unix(),
	}
	require.NoError(service.LiveDB.Create(&v).Error)

	err = squareHotVacancyUpdate(guildID, 6, position)
	msg := fmt.Sprintf("编辑公会 %d 的推荐次数 %d 错误，不可小于公会已用推荐次数 %d", guildID, 6, 9)
	require.Equal(actionerrors.NewErrForbidden(msg), err)
	err = squareHotVacancyUpdate(guildID, 16, position)
	require.NoError(err)
	var g recommend.GuildRecommendVacancy
	require.NoError(service.LiveDB.Table(recommend.GuildRecommendVacancy{}.TableName()).
		Select("vacancy, edited_vacancy").Where("guild_id = ?", guildID).Scan(&g).Error)
	assert.Equal(int64(7), g.Vacancy)
	assert.Equal(int64(16), g.EditedVacancy)
}

func TestActionSquareHotVacancyList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext("GET", "list?position=44", true, nil)
	resp, err := ActionSquareHotVacancyList(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(resp)

	c = handler.NewTestContext("GET", "list?position=4", true, nil)
	resp, err = ActionSquareHotVacancyList(c)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	guildID1 := int64(3)
	guildID2 := int64(23333471)
	p := squareHotVacancyListParam{Position: 4, P: 1}
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 8, 1, 22, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	gv1 := recommend.GuildRecommendVacancy{
		Position:       p.Position,
		GuildID:        guildID1,
		InitialVacancy: 11,
		EditedVacancy:  20,
		Vacancy:        19,
		StartTime:      time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local).Unix(),
		EndTime:        time.Date(2021, 9, 2, 0, 0, 0, 0, time.Local).Unix(),
	}

	require.NoError(service.LiveDB.Create(&gv1).Error)
	gv2 := recommend.GuildRecommendVacancy{
		Position:       p.Position,
		GuildID:        guildID2,
		InitialVacancy: 12,
		EditedVacancy:  30,
		Vacancy:        30,
		StartTime:      time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local).Unix(),
		EndTime:        time.Date(2021, 9, 2, 0, 0, 0, 0, time.Local).Unix(),
	}
	require.NoError(service.LiveDB.Create(&gv2).Error)
	resp, err := p.list()
	require.NoError(err)
	require.GreaterOrEqual(len(resp.Data), 2)
	assert.Equal(int64(0), resp.Data[0].TimesUsed)
	assert.Equal(int64(12), resp.Data[0].InitialVacancy)
	assert.Equal(int64(30), resp.Data[0].EditedVacancy)
	assert.Equal(int64(guildID2), resp.Data[0].GuildID)
	assert.Equal(int64(1), resp.Data[1].TimesUsed)
	assert.Equal(int64(11), resp.Data[1].InitialVacancy)
	assert.Equal(int64(20), resp.Data[1].EditedVacancy)
	assert.Equal(guildID1, resp.Data[1].GuildID)
	p.GuildID = guildID1
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 1)
	assert.Equal(guildID1, resp.Data[0].GuildID)
	assert.Equal(int64(1), resp.Data[0].TimesUsed)
	assert.Equal(int64(11), resp.Data[0].InitialVacancy)
	assert.Equal(int64(20), resp.Data[0].EditedVacancy)
	p.GuildID = -1
	resp, err = p.list()
	require.NoError(err)
	require.Len(resp.Data, 0)
}
