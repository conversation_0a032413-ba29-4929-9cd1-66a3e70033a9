package guild

/*
func TestActionPassinvite(t *testing.T) {
	setup()
	assert := assert.New(t)
	require := require.New(t)

	c := livecontract.LiveContract{
		ID:          138,
		GuildID:     3,
		ContractEnd: 1999999999,
		// Status 只有在创建表时会被使用，更新时因为该属性等于默认值所以会被忽略
		Status:    livecontract.StatusUntreated,
		Type:      livecontract.FromGuild,
		LiveID:    1562,
		GuildName: "测试主播同意公会的入会邀请",
	}
	err := service.DB.Assign(handler.M{
		// 强制更新 status 属性
		"status": livecontract.StatusUntreated,
	}).Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	params := map[string]int64{"contract_id": 138}

	ctx := handler.CreateTestContext(true)
	ctx.User().ID = 1562
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/passinvite", paramToRequestBody(params))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	r, err := ActionPassInvite(ctx)
	assert.NoError(err)
	assert.True(r.(bool))
}
*/
