package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

func TestAdminreject(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildInfo := &guild.Guild{
		Name:    "测试拒绝公会创建申请",
		UserID:  222222,
		Checked: guild.CheckedChecking,
	}
	require.NoError(service.DB.Create(guildInfo).Error)
	defer func() {
		require.NoError(service.DB.Table(guild.TableName()).Delete("", "id = ?", guildInfo.ID).Error)
	}()
	cleanup := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(interface{}) (interface{}, error) {
			return handler.M{"count": 1}, nil
		})
	defer cleanup()

	m := map[string]interface{}{
		"guild_id": guildInfo.ID,
		"reason":   "test",
	}
	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "/admin/guild/adminreject", paramToRequestBody(m))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	r, err := ActionAdminreject(ctx)
	require.NoError(err)
	resp := r.(handler.M)
	assert.True(resp["pass"].(bool))
	assert.True(resp["notify"].(bool))
	newG, err := guild.Find(guildInfo.ID)
	require.NoError(err)
	assert.Equal(guild.CheckedReject, newG.Checked)

	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", 3).Update("checked", guild.CheckedPass).Error)
	m["guild_id"] = 3
	ctx = handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "/admin/guild/adminreject", paramToRequestBody(m))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	_, err = ActionAdminreject(ctx)
	assert.EqualError(err, "错误的请求")
}
