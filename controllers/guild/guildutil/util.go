package guildutil

import (
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TimeNowForHotRecommend 根据热门列表推荐位的 key 的偏移获取当前时间
func TimeNowForHotRecommend() time.Time {
	now := goutil.TimeNow()
	// release 模式下不读时间偏移
	if util.IsProdEnv() {
		return now
	}
	key := keys.KeyHotRecommendTimeOffset0.Format()
	offset, err := service.Redis.Get(key).Int64()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		// PASS
	}
	return now.Add(time.Duration(offset) * time.Second)
}
