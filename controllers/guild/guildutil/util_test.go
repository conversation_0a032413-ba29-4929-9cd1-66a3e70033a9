package guildutil

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTimeNowForHotRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	key := keys.KeyHotRecommendTimeOffset0.Format()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	handler.SetMode(handler.DebugMode)
	require.NoError(service.Redis.Del(key).Err())
	assert.Equal(now, TimeNowForHotRecommend())
	require.NoError(service.Redis.Set(key, "2000", time.Minute).Err())
	assert.Equal(now.Unix()+2000, TimeNowForHotRecommend().Unix())
	require.NoError(service.Redis.Set(key, "-2000", time.Minute).Err())
	assert.Equal(now.Unix()-2000, TimeNowForHotRecommend().Unix())
	require.NoError(service.Redis.Set(key, "invalid", time.Minute).Err())
	assert.Equal(now.Unix(), TimeNowForHotRecommend().Unix())
	handler.SetMode(handler.ReleaseMode)
	assert.Equal(now, TimeNowForHotRecommend())
}
