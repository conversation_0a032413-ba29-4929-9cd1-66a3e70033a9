package guild

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestAdminmanage(t *testing.T) {
	assert := assert.New(t)
	// require := require.New(t)
	// g, _ := guild.Find(124)
	// require.NotNil(g)
	// defer service.DB.Model(g).Update("checked", g.Checked)
	// service.DB.Model(g).Update("checked", guild.CheckedPass)
	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("GET", "/admin/guild/adminmange?sort=guild_id.desc", nil)
	r, err := ActionAdminmanage(ctx)
	assert.NoError(err)
	b, _ := json.Marshal(r)
	logger.Debug(string(b))
	// TODO: 覆盖所有情况
}

func TestAdminManageParam_fillGuildRate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := adminManageParam{}
	require.Nil(param.fillGuildRate())

	param = adminManageParam{
		adminmanageResp: adminmanageResp{
			GuildInfos: []guildInfo{
				{
					GuildID:   8,
					GuildName: "test_guild_name",
					OwnerID:   12,
					OwnerName: "test_owner_name",
					LiveNum:   10,
				},
			},
		},
	}
	require.NoError(param.fillGuildRate())
	require.Len(param.GuildInfos, 1)
	assert.Equal(0.6, param.GuildInfos[0].Rate)
}
