package guild

import (
	"errors"
	"fmt"
	"html"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/models/useroa"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type deleteGuildParams struct {
	ID      int64 `form:"id" json:"id" binding:"required"`
	Confirm int   `form:"confirm" json:"confirm"`

	guild               *guild.Guild
	userOA              *useroa.UserOA
	sysMsgs             []pushservice.SystemMsg
	exclusiveCreatorIDs []int64
}

var errNoRowsAffected = errors.New("no rows affected")

// ActionDeleteGuild 注销公会
/**
 * @api {post} /api/v2/admin/guild/deleteguild 注销公会
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} id 公会 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample 公会注销成功:
 *   {
 *     "code": 0,
 *     "info": "公会注销成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": ""
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 */
func ActionDeleteGuild(c *handler.Context) (handler.ActionResponse, error) {
	var params deleteGuildParams
	err := params.loadDeleteGuildParams(c)
	if err != nil {
		return nil, err
	}
	// 注销公会
	err = params.deleteGuild()
	if err != nil {
		return nil, err
	}

	err = guildscheduleapply.AfterQuitGuild(params.ID, nil)
	if err != nil {
		logger.WithFields(logger.Fields{
			"guild_id": params.ID,
		}).Error(err)
		// PASS
	}
	params.addAdminLogSendEmail(c)
	return "公会注销成功", nil
}

func (p *deleteGuildParams) loadDeleteGuildParams(c *handler.Context) error {
	err := c.Bind(p)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.userOA, err = useroa.FindByUserID(c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.userOA == nil {
		return actionerrors.ErrParamsMsg("未绑定 OA 账号")
	}
	p.guild, err = guild.Find(p.ID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.guild == nil || p.guild.Checked != guild.CheckedPass {
		return actionerrors.ErrGuildNotExist
	}

	// 公会是否存在生效中的三方独家主播
	now := goutil.TimeNow()
	err = exclusivecreator.TripartiteExclusiveCreator{}.DB().
		Select("creator_id").
		Where("guild_id = ? AND status = ? AND contract_end > ?", p.guild.ID, exclusivecreator.StatusValid, now.Unix()).
		Pluck("creator_id", &p.exclusiveCreatorIDs).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.exclusiveCreatorIDs) > 0 {
		s := fmt.Sprintf("公会内主播 %s 为三方独家主播，注销公会失败", goutil.JoinInt64Array(p.exclusiveCreatorIDs, "、"))
		return actionerrors.NewErrForbidden(s)
	}

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(now)
	// 本月礼物流水（打赏 + 超粉 - 渠道费）
	var giftRevenue util.Float2DP
	attrs := append(transactionlog.GiftAttrs(), transactionlog.SuperFanAttrs()...)
	err = transactionlog.ADB().
		Select("IFNULL(SUM(FLOOR(ROUND(IFNULL((income - tax), 0) * 1000) / 10) / 100), 0) AS gift_revenue").
		Where("suborders_num = ? AND type = ? AND status = ?", p.ID, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("attr IN (?)", attrs).
		Where("confirm_time BETWEEN ? AND ?", nowStampFirstDayOfThisMonth.Unix(), now.Unix()).
		Row().Scan(&giftRevenue)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 本月贵族开通流水（贵族开通 - 渠道费）
	var nobleRevenue util.Float2DP
	err = transactionlog.ADB().
		Select("IFNULL(SUM(FLOOR(ROUND(IFNULL((income - tax), 0) * 1000) / 10) / 100), 0) AS noble_revenue").
		Where("suborders_num = ? AND type = ? AND status = ?", p.ID, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("attr IN (?)", transactionlog.NobleAttrs()).
		Where("confirm_time BETWEEN ? AND ?", nowStampFirstDayOfThisMonth.Unix(), now.Unix()).
		Row().Scan(&nobleRevenue)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.Confirm == 0 {
		message := fmt.Sprintf("<p>注销公会名称：%s</p><p>本月礼物流水：%.2f 元</p><p>本月贵族开通流水：%.2f 元</p><p>签约主播：%d 名</p><p>注销后将全部自动解约，是否确认注销？</p><p>确认注销后将邮件通知业务负责人。</p>",
			html.EscapeString(p.guild.Name), giftRevenue, nobleRevenue, p.guild.LiveNum)
		return actionerrors.ErrConfirmRequired(message, 1, true)
	}
	return nil
}

func (p *deleteGuildParams) deleteGuild() error {
	nowStamp := goutil.TimeNow().Unix()
	var creatorIDs []int64
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 公会状态置为已解散
		gdb := tx.Table(guild.TableName()).
			Where("id = ? AND checked = ?", p.ID, guild.CheckedPass).
			Updates(map[string]interface{}{
				"checked":       guild.CheckedDissolved,
				"modified_time": nowStamp,
			})
		if err := gdb.Error; err != nil {
			return err
		}
		if gdb.RowsAffected == 0 {
			return errNoRowsAffected
		}
		// 需要失效的合约
		var contractList []livecontract.LiveContract
		err := tx.Table(livecontract.TableName()).
			Select("*").
			Where("guild_id = ? AND status = ?", p.ID, livecontract.StatusContracting).
			Scan(&contractList).Error
		if err != nil {
			return err
		}
		contractLength := len(contractList)
		if contractLength > 0 {
			err = p.addGuildExpelApplyment(contractList, tx)
			if err != nil {
				return err
			}
			creatorIDs = goutil.SliceMap(contractList, func(contract livecontract.LiveContract) int64 {
				return contract.LiveID
			})
		}
		// 拒绝未处理的合约
		err = tx.Table(livecontract.TableName()).
			Where("guild_id = ? AND status = ?", p.ID, livecontract.StatusUntreated).
			Update(map[string]interface{}{
				"status":        livecontract.StatusRefused,
				"contract_end":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return err
		}
		// 拒绝未处理的合约申请
		err = tx.Table(contractapplyment.TableName()).
			Where("guild_id = ? AND status = ? AND expire_time > ?",
				p.ID, contractapplyment.StatusPending, nowStamp).
			Update(map[string]interface{}{
				"status":        contractapplyment.StatusDeclined,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return err
		}
		err = servicedb.Tx(service.LiveDB, func(tx2 *gorm.DB) error {
			// 处理公会经纪人
			err := tx2.Table(guildagent.GuildAgent{}.TableName()).
				Where("guild_id = ? AND delete_time = 0", p.ID).
				Update(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
			// 处理公会经纪人与主播关系
			err = tx2.Table(guildagent.AgentCreator{}.TableName()).
				Where("guild_id = ? AND delete_time = 0", p.ID).
				Update(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 将注销的公会下的主播的 guild_id 设置为 0
	if len(creatorIDs) > 0 {
		err = room.SetGuildID(creatorIDs, 0)
		if err != nil {
			logger.WithFields(logger.Fields{
				"guild_id": p.guild.ID,
			}).Error(err)
			// PASS
		}
	}
	return nil
}

// addGuildExpelApplyment 使现有合约失效以及补充退会申请记录（公会清退主播）
func (p *deleteGuildParams) addGuildExpelApplyment(contractList []livecontract.LiveContract, tx *gorm.DB) error {
	contractLength := len(contractList)
	newApplymentList := make([]contractapplyment.ContractApplyment, 0, contractLength)
	sysMsgs := make([]pushservice.SystemMsg, 0, contractLength)
	now := goutil.TimeNow()
	nowStamp := now.Unix()

	// 更新公会主播数为 0
	guildDb := tx.Table(guild.TableName()).
		Where("id = ?", p.ID).
		Update(map[string]interface{}{
			"live_num":      0,
			"modified_time": nowStamp,
		})
	if err := guildDb.Error; err != nil {
		return err
	}
	if guildDb.RowsAffected == 0 {
		return errNoRowsAffected
	}

	// 使现有合约失效
	lcdb := tx.Table(livecontract.TableName()).
		Where("guild_id = ? AND status = ?", p.ID, livecontract.StatusContracting).
		Update(map[string]interface{}{
			"status":        livecontract.StatusFinished,
			"contract_end":  nowStamp,
			"modified_time": nowStamp,
		})
	if err := lcdb.Error; err != nil {
		return err
	}
	if lcdb.RowsAffected == 0 {
		return errNoRowsAffected
	}
	for _, contract := range contractList {
		// 补充退会申请记录（公会清退主播）
		newApplymentList = append(newApplymentList, contractapplyment.ContractApplyment{
			LiveID:             contract.LiveID,
			GuildID:            contract.GuildID,
			GuildName:          contract.GuildName,
			ContractID:         contract.ID,
			ExpireTime:         now.AddDate(0, 0, 2).Unix(),
			Type:               contractapplyment.TypeGuildExpel,
			ContractDuration:   contract.ContractDuration,
			Rate:               contract.Rate,
			ContractExpireTime: nowStamp,
			Status:             contractapplyment.StatusAgreed,
			Initiator:          contractapplyment.InitiatorGuild,
			CreateTime:         nowStamp,
			ModifiedTime:       nowStamp,
		})
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: contract.LiveID,
			Title:  "公会注销通知",
			Content: fmt.Sprintf("主播您好，您之前所在的公会【%s】提前终止与猫耳FM的合作并完成注销，现您已成为素人主播。如有任何疑问，请联系原公会负责人。",
				html.EscapeString(p.guild.Name)),
		})
	}
	p.sysMsgs = sysMsgs
	if err := helper.BatchInsert(tx, contractapplyment.TableName(), newApplymentList); err != nil {
		return err
	}
	return nil
}

func (p *deleteGuildParams) addAdminLogSendEmail(c *handler.Context) {
	if len(p.sysMsgs) > 0 {
		// 发送系统通知
		err := service.PushService.SendSystemMsg(p.sysMsgs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	// 管理员操作日志
	nowStamp := goutil.TimeNow().Unix()
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("注销公会，公会 ID: %d，公会名称 %s", p.guild.ID, p.guild.Name)
	box.Add(userapi.CatalogDeleteGuild, intro, goclient.AdminLogOptions{
		ChannelID: &p.guild.ID,
	})
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 发邮件
	toEmails := config.Conf.Params.GuildNoticeEmails.GuildDeleteNoticeEmails
	email := pushservice.Email{
		To:      toEmails,
		Subject: fmt.Sprintf("【%s】直播公会注销", p.guild.Name),
	}
	email.Body = fmt.Sprintf("<p>注销时间：%s</p><p>操作人：%s</p><p>注销公会 ID：%d</p><p>注销公会：%s</p>",
		time.Unix(nowStamp, 0).Format(util.TimeFormatYMDHMS), html.EscapeString(p.userOA.OAName), p.guild.ID, html.EscapeString(p.guild.Name))
	err = service.PushService.SendEmail(email)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
