package guild

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// itemTypeV2 for response data list
type itemTypeV2 struct {
	LiveID   int64  `gorm:"column:live_id" json:"live_id"`
	IconURL  string `gorm:"-" json:"iconurl"`
	Username string `gorm:"-" json:"username"`

	AgentID       int64  `gorm:"-" json:"agent_id"`
	AgentUsername string `gorm:"-" json:"agent_username"`

	RewardIncome   util.Float2DP `gorm:"column:reward_income" json:"reward_income"`
	NobleIncome    util.Float2DP `gorm:"column:noble_income" json:"noble_income"`
	SuperFanIncome util.Float2DP `gorm:"column:superfan_income" json:"superfan_income"`
}

// incomeMenu 收益菜单栏
type incomeMenu struct {
	RewardIncome   util.Float2DP `gorm:"column:reward_income" json:"reward_income"`
	NobleIncome    util.Float2DP `gorm:"column:noble_income" json:"noble_income"`
	SuperFanIncome util.Float2DP `gorm:"column:superfan_income" json:"superfan_income"`
}

type guildIncomeV2Resp struct {
	Menu       *incomeMenu       `json:"menu,omitempty"`
	Data       []itemTypeV2      `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

type guildIncomeV2Param struct {
	page     int64
	pageSize int64
	sort     string

	startDate time.Time
	endDate   time.Time

	guildID int64

	searchAgent   handler.SearchWord
	searchCreator handler.SearchWord

	role guildrole.GuildRole
	user *user.User

	agentCreators agentCreators
}

var guildIncomeSortMapV2Supported = map[string]bool{
	"reward_income":        true,
	"reward_income.asc":    true,
	"reward_income.desc":   true,
	"noble_income":         true,
	"noble_income.asc":     true,
	"noble_income.desc":    true,
	"superfan_income":      true,
	"superfan_income.asc":  true,
	"superfan_income.desc": true,
}

// ActionGuildIncomeV2 公会创建者获取公会收益
/**
 * @api {get} /api/v2/guild/guildincome-v2 公会创建者获取公会收益
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2022-05-21）
 * @apiParam {String} [end_date=今天] 截止日期（例 2022-06-21）
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {String} [sort=reward_income.desc] 排序方式：reward_income.asc、
 * reward_income.desc、noble_income.asc、noble_income.desc、superfan_income.asc、superfan_income.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "menu": { // p = 1 时返回
 *           "reward_income": 1.02, // 打赏收益（元）
 *           "noble_income": 12, // 贵族收益（元）
 *           "superfan_income": 12 // 超粉收益（元）
 *         },
 *         "data": [{
 *           "live_id": 346285,
 *           "iconurl": "http://static.missevan.com/avatars/201609/18/8bab6898a560f32f9893d6df816c493a121724.jpg",
 *           "username": "T2ye_ZJ",
 *           "agent_id": 1, // 经纪人 ID
 *           "agent_username": "invincible", // 经纪人昵称
 *           "reward_income": 1.02, // 打赏收益（元）
 *           "noble_income": 12, // 贵族收益（元）
 *           "superfan_income": 12 // 超粉收益（元）
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code *********
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionGuildIncomeV2(ctx *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildIncomeV2Param(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := newGuildIncomeV2Resp(param, true)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func newGuildIncomeV2Param(ctx *handler.Context) (*guildIncomeV2Param, error) {
	param := new(guildIncomeV2Param)

	var err error
	param.page, param.pageSize, err = ctx.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	param.startDate, param.endDate, err = ctx.GetParamDateRange(
		now.AddDate(0, -1, 0).Format(goutil.TimeFormatYMD),
		now.Format(goutil.TimeFormatYMD),
	)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	var ok bool
	orderByField, order, ok := utils.ParseSortStr(
		ctx.GetDefaultParamString("sort", "reward_income.desc"),
		guildIncomeSortMapV2Supported,
	)
	if !ok {
		return nil, actionerrors.ErrParams
	}
	param.sort = orderByField + " " + order

	param.searchCreator, err = ctx.GetParamSearchWord("s")
	if err != nil && err != handler.ErrEmptyValue {
		return nil, actionerrors.ErrParams
	}
	param.searchAgent, err = ctx.GetParamSearchWord("search_agent")
	if err != nil && err != handler.ErrEmptyValue {
		return nil, actionerrors.ErrParams
	}

	role, guildInfo, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}
	param.user = ctx.User()
	param.role = role
	param.guildID = guildInfo.ID

	param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return param, nil
}

func newGuildIncomeV2Resp(param *guildIncomeV2Param, enableMenu bool) (*guildIncomeV2Resp, error) {
	resp := new(guildIncomeV2Resp)

	err := resp.BuildList(param)
	if err != nil {
		return nil, err
	}

	if enableMenu {
		err = resp.BuildMenu(param)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	return resp, nil
}

func (resp *guildIncomeV2Resp) setEmptyGuildIncomeV2Resp(param *guildIncomeV2Param) {
	resp.Data = make([]itemTypeV2, 0)
	resp.Pagination = goutil.MakePagination(0, param.page, param.pageSize)
}

func (resp *guildIncomeV2Resp) BuildList(param *guildIncomeV2Param) error {
	var err error
	db := livecontract.ADB("lc").
		Where("lc.guild_id = ?", param.guildID).
		// 获取状态为：合约解约、合约失效、合约生效中 的合约
		Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished,
			livecontract.StatusContracting})

	if db, err = param.applySearch(db); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if db == nil {
		resp.setEmptyGuildIncomeV2Resp(param)
		return nil
	}
	if !param.role.IsGuildOwner() {
		creatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(creatorIDs) == 0 {
			resp.setEmptyGuildIncomeV2Resp(param)
			return nil
		}
		db = db.Where("lc.live_id IN (?)", creatorIDs)
	}

	var totalCount int64
	err = db.Select("COUNT(DISTINCT live_id) AS live_count").Row().Scan(&totalCount)
	if err != nil && !servicedb.IsErrNoRows(err) {
		resp.setEmptyGuildIncomeV2Resp(param)
		return nil
	}
	if totalCount == 0 {
		resp.setEmptyGuildIncomeV2Resp(param)
		return nil
	}

	resp.Pagination = goutil.MakePagination(totalCount, param.page, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]itemTypeV2, 0)
		return nil
	}
	db = db.Select("lc.live_id"+
		", COALESCE(t.reward_income, 0) AS reward_income"+
		", COALESCE(t.noble_income, 0) AS noble_income"+
		", COALESCE(t.superfan_income, 0) AS superfan_income").
		Joins("LEFT JOIN ? AS t ON t.to_id = lc.live_id", param.revenueSubquery()).
		Group("lc.live_id").
		Order(param.sort)
	err = resp.Pagination.ApplyTo(db).Scan(&resp.Data).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(resp.Data) == 0 {
		resp.setEmptyGuildIncomeV2Resp(param)
		return nil
	}

	userIDs := make([]int64, len(resp.Data))
	for i, item := range resp.Data {
		userIDs[i] = item.LiveID
	}
	if err = resp.fillUserInfo(param, userIDs); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

var totalIncomeAttrs = []int64{
	transactionlog.AttrCommon,
	transactionlog.AttrLiveRegisterNoble,
	transactionlog.AttrLiveRebateGift,
	transactionlog.AttrLiveLuckyGift,
	transactionlog.AttrLiveRegisterSuperFan,
	transactionlog.AttrLiveRenewSuperFan,
	transactionlog.AttrLiveGashaponGift,
}

var rewardIncomeAttrs = []int64{
	transactionlog.AttrCommon,
	transactionlog.AttrLiveRebateGift,
	transactionlog.AttrLiveLuckyGift,
	transactionlog.AttrLiveGashaponGift,
}

var superFanIncomeAttrs = []int64{
	transactionlog.AttrLiveRegisterSuperFan,
	transactionlog.AttrLiveRenewSuperFan,
}

var nobleIncomeAttrs = []int64{
	transactionlog.AttrLiveRegisterNoble,
}

func (param *guildIncomeV2Param) revenueSubquery() interface{} {
	incomeV2SQL := incomeV2SQL()

	query := transactionlog.ADB().
		Select("to_id"+
			", "+incomeV2SQL,
		).
		Where("suborders_num = ? AND status = ? AND type = ?",
			param.guildID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", totalIncomeAttrs).
		Group("to_id")

	return addIncomeTimeFilter(query, "confirm_time", param.startDate, param.endDate).SubQuery()
}

func (param *guildIncomeV2Param) applySearch(db *gorm.DB) (*gorm.DB, error) {
	if param.searchCreator.Word != "" {
		if param.searchCreator.IsInteger {
			db = db.Where("lc.live_id = ?", param.searchCreator.WordInteger)
		} else {
			var allCreatorIDs []int64
			// 该公会下所有的主播 ID
			err := db.Pluck("live_id", &allCreatorIDs).Error
			if err != nil {
				return nil, err
			}
			if len(allCreatorIDs) == 0 {
				return nil, nil
			}
			// 从该公会下所有的主播 ID 中匹配用户
			liveIDs, err := mowangskuser.SearchUserByUsername(param.searchCreator.Word, allCreatorIDs)
			if err != nil {
				return nil, err
			}
			if len(liveIDs) == 0 {
				return nil, nil
			}
			db = db.Where("lc.live_id IN (?)", liveIDs)
		}
	}
	if param.searchAgent.Word != "" {
		var liveIDs []int64
		if param.searchAgent.IsInteger {
			liveIDs = param.agentCreators.agentCreatorIDs(
				param.user.ID,
				param.role,
				param.searchAgent.WordInteger,
			)
		} else {
			allAgentIDs := param.agentCreators.allAgentIDs()
			if len(allAgentIDs) == 0 {
				return nil, nil
			}
			agentIDs, err := mowangskuser.SearchUserByUsername(param.searchAgent.Word, allAgentIDs)
			if err != nil {
				return nil, err
			}
			if len(agentIDs) == 0 {
				return nil, nil
			}
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDs...)
		}
		if len(liveIDs) == 0 {
			return nil, nil
		}
		db = db.Where("lc.live_id IN (?)", liveIDs)
	}

	return db, nil
}

func (resp *guildIncomeV2Resp) fillUserInfo(param *guildIncomeV2Param, creatorIDs []int64) error {
	creatorAgentMap := make(map[int64]int64, len(param.agentCreators))
	userIDs := make([]int64, 0, len(creatorIDs)+len(param.agentCreators))
	userIDs = append(userIDs, creatorIDs...)
	if param.role.IsGuildOwner() {
		agentIDs := make([]int64, len(param.agentCreators))
		for i, item := range param.agentCreators {
			agentIDs[i] = item.AgentID
			creatorAgentMap[item.CreatorID] = item.AgentID
		}
		userIDs = append(userIDs, agentIDs...)
	}

	userInfoMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return err
	}
	for i, item := range resp.Data {
		if creatorInfo, ok := userInfoMap[item.LiveID]; ok {
			resp.Data[i].IconURL = creatorInfo.IconURL
			resp.Data[i].Username = creatorInfo.Username
		}
		// 如果访问用户不是会长，则当前访问用户为经纪人
		if !param.role.IsGuildOwner() {
			resp.Data[i].AgentID = param.user.ID
			resp.Data[i].AgentUsername = param.user.Username
			continue
		}

		if agentID, ok := creatorAgentMap[item.LiveID]; ok {
			agentInfo, ok := userInfoMap[agentID]
			if ok {
				resp.Data[i].AgentID = agentInfo.ID
				resp.Data[i].AgentUsername = agentInfo.Username
			}
		}
	}

	return nil
}

func incomeV2SQL() string {
	rewardIncomeSQL, _ := incomeV2FieldSQL(rewardIncomeAttrs,
		"reward_income", true)

	nobleIncomeSQL, _ := incomeV2FieldSQL(nobleIncomeAttrs,
		"noble_income", true)

	superFanIncomeSQL, _ := incomeV2FieldSQL(superFanIncomeAttrs,
		"superfan_income", true)

	return rewardIncomeSQL +
		", " + nobleIncomeSQL +
		", " + superFanIncomeSQL
}

func (resp *guildIncomeV2Resp) BuildMenu(param *guildIncomeV2Param) error {
	if param.page != 1 {
		return nil
	}
	incomeSQL := incomeV2SQL()
	db := transactionlog.ADB().
		Select(incomeSQL).
		Where("suborders_num = ? AND status = ? AND type = ?",
			param.guildID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", totalIncomeAttrs)
	db = addIncomeTimeFilter(db, "confirm_time", param.startDate, param.endDate)

	resp.Menu = &incomeMenu{}
	if !param.role.IsGuildOwner() {
		agentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(agentCreatorIDs) == 0 {
			return nil
		}
		db = db.Where("to_id IN (?)", agentCreatorIDs)
	}
	err := db.Take(resp.Menu).Error
	return err
}

// incomeV2FieldSQL 返回收益 SQL
func incomeV2FieldSQL(attr []int64, alias string, enableSubTax bool) (string, error) {
	a, err := transactionlog.GuildIncomeV2AttrRateMap.GenerateRateAttrsMap(attr)
	if err != nil {
		return "", err
	}

	sqlArr := make([]string, 0, len(a))
	for rate, attrs := range a {
		s, err := transactionlog.GenerateAttrsIncomeFieldSQL(attrs, rate, enableSubTax)
		if err != nil {
			return "", err
		}
		sqlArr = append(sqlArr, s)
	}

	return transactionlog.GenerateSafe2dpSQL(strings.Join(sqlArr, " + "), alias), nil
}

// ActionGuildIncomeExportV2 公会创建者导出公会收益 csv
/**
 * @api {get} /api/v2/guild/guildincomeexport-v2 公会创建者导出公会收益 csv
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2019-05-01）
 * @apiParam {String} [end_date=今天] 截止日期（例 2019-05-15）
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code *********
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildIncomeExportV2(ctx *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildIncomeV2Param(ctx)
	if err != nil {
		return nil, err
	}
	param.page = 1
	param.pageSize = 10000 // 设置一个大值，避免遗漏数据

	resp, err := newGuildIncomeV2Resp(param, false)
	if err != nil {
		return nil, err
	}

	var records [][]string
	titleLine := []string{
		"主播 ID", "主播昵称", "经纪人 ID", "经纪人",
		"打赏总收益（元）", "贵族开通总收益（元）", "超粉总收益（元）",
	}
	records = append(records, titleLine)
	for _, v := range resp.Data {
		var agentIDStr string
		if v.AgentID != 0 {
			agentIDStr = strconv.FormatInt(v.AgentID, 10)
		}
		records = append(records, []string{
			strconv.FormatInt(v.LiveID, 10),
			v.Username,
			agentIDStr,
			v.AgentUsername,
			v.RewardIncome.String(),
			v.NobleIncome.String(),
			v.SuperFanIncome.String(),
		})
	}

	filename := fmt.Sprintf(
		"公会收益_%s_%s.csv",
		param.startDate.Format(goutil.TimeFormatYMD),
		param.endDate.Format(goutil.TimeFormatYMD),
	)
	return writeDataToCsv(ctx, records, filename)
}
