package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionIsowner 判断用户是否是会长
/**
 * @apiDeprecated 由 /api/v2/guild/role 替换
 * @api {post} /api/v2/guild/isowner 判断用户是否是会长
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 */
func ActionIsowner(c *handler.Context) (handler.ActionResponse, error) {
	g, err := guildrole.IsGuildOwner(c.UserID())
	if err != nil {
		return false, nil
	}
	return g != nil, nil
}

type guildRole struct {
	GuildID int64          `json:"guild_id,omitempty"`
	Role    goutil.BitMask `json:"role"`
}

// ActionRole 判断在公会中的角色（公会、经纪人）
/**
 * @api {post} /api/v2/guild/role 判断在公会中的角色（公会、经纪人）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "guild_id": 123,  // 公会 ID，如若没有拥有或加入任何公会，不返回该字段
 *       "role": 0  // 按位运算：第 1 位公会主播，第 2 位公会会长，第 3 位公会经纪人
 *      }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRole(ctx *handler.Context) (handler.ActionResponse, error) {
	userRole, g, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	var res guildRole
	if g != nil {
		res.GuildID = g.ID
	}
	res.Role = userRole.BitMask
	return res, nil
}
