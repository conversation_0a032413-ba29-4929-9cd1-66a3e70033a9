package guild

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type agreementResp struct {
	Title     string `json:"title"`
	Content   string `json:"content"`
	GuildName string `json:"guild_name"`
	UserName  string `json:"username"`
	IsAgreed  bool   `json:"is_agreed"`
}

// TODO: ActionAgreement, ActionConfirmAgreement 目前支持旧版本使用，后面可以考虑移除

// ActionAgreement 获取服务协议内容
/**
 * @api {get} /api/v2/guild/agreement 获取服务协议内容
 * @apiDescription 需要先请求 /api/v2/live/{room_id} 判断接口中 room.guild_id 存在且不为零后，后再请求该接口。\
 * 新建直播间的时候（没有 room_id 的时候）也不需要调用。\
 * 另外客户端需要忽略该接口返回的 40X 类型错误。
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "title": "猫耳FM公会主播入驻服务协议",
 *       "content": <p><b>猫耳FM公会主播入驻服务协议。</b></p>,
 *       "is_agreed": false,
 *       "guild_name": "三体会",
 *       "username": "小喵喵"
 *     }
 *   }
 *
 * @apiError (404) {Number} code 500050001
 * @apiError (404) {String} info 公会不存在
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionAgreement(c *handler.Context) (handler.ActionResponse, error) {
	// 获取主播所属公会信息
	lc, err := guildNameByLiveID(c.UserID())
	if err != nil {
		return nil, err
	}

	la, err := liveaddendum.Find(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if la == nil {
		// 未找到认为未同意协议
		la = new(liveaddendum.LiveAddendum)
	}
	var agreement agreementResp
	if la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement) {
		agreement.IsAgreed = true
		return agreement, nil
	}
	agreement.Title = config.Conf.Params.GuildAgreement.Title
	agreement.UserName = c.User().Username
	agreement.GuildName = lc.GuildName
	agreement.Content = config.Conf.Params.GuildAgreement.ContentHTML
	return agreement, nil
}

// ActionConfirmAgreement 主播确认服务协议
/**
 * @api {post} /api/v2/guild/confirmagreement 主播确认服务协议
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=1} agree 同意协议, 1：同意
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (404) {Number} code 500050001
 * @apiError (404) {String} info 公会不存在
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionConfirmAgreement(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		Agree int `json:"agree" form:"agree"`
	}
	err := c.Bind(&params)
	if err != nil || params.Agree != liveaddendum.Agreed {
		return nil, actionerrors.ErrParams
	}
	userID := c.UserID()
	// 获取主播是否为公会成员
	_, err = guildNameByLiveID(userID)
	if err != nil {
		return nil, err
	}

	err = liveaddendum.Agree(userID, liveaddendum.AgreeGuildAgreement)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	return true, nil
}

func guildNameByLiveID(live int64) (*livecontract.LiveContract, error) {
	lc, err := livecontract.FindInContractingByLiveID(live, 0, "guild_name")
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	// 主播未加入公会
	if lc == nil {
		return nil, actionerrors.ErrGuildNotExist
	}
	return lc, nil
}
