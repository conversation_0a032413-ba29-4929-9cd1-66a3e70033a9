package guild

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mysql/guildtransferhistory"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTransferGuildParamsTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).Check(transferGuildParams{},
		"from_guild_id", "to_guild_id", "creator_ids", "revenue_live_duration_type", "contract_type", "confirm")

	tutil.NewKeyChecker(t, tutil.FORM).Check(transferGuildParams{},
		"from_guild_id", "to_guild_id", "creator_ids", "revenue_live_duration_type", "contract_type", "confirm")
}

func TestActionGuildTransferCreator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := map[string]interface{}{
		"from_guild_id":              60,
		"to_guild_id":                70,
		"creator_ids":                "",
		"revenue_live_duration_type": guildtransferhistory.TypeRevenueNew,
		"contract_type":              guildtransferhistory.TypeContractAgain,
	}
	now := goutil.TimeNow()
	nowStamp := now.Unix()

	// 创建公会 1
	g1 := guild.Guild{
		ID:            60,
		Name:          "测试转会 1",
		Intro:         "测试公会 1",
		OwnerName:     "钢铁侠",
		OwnerIDNumber: "123456789012345678",
		Mobile:        "***********",
		Bank:          "测试银行",
		CreateTime:    nowStamp,
		ModifiedTime:  nowStamp,
		Type:          guild.TypeUnencrypted,
		Checked:       guild.CheckedPass,
		UserID:        111,
	}
	g1.Encrypt()
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", body["from_guild_id"]).FirstOrCreate(&g1).Error)

	// 创建公会 2
	g2 := guild.Guild{
		ID:            70,
		Name:          "测试转会 2",
		Intro:         "测试公会 2",
		OwnerName:     "钢铁侠 2",
		OwnerIDNumber: "123456789012345678",
		Mobile:        "***********",
		Bank:          "测试银行2",
		CreateTime:    nowStamp,
		ModifiedTime:  nowStamp,
		Type:          guild.TypeUnencrypted,
		Checked:       guild.CheckedPass,
		UserID:        222,
	}
	g2.Encrypt()
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", body["to_guild_id"]).FirstOrCreate(&g2).Error)

	// 生成合约 1
	contract1 := livecontract.LiveContract{
		ID:               1,
		GuildID:          g1.ID,
		LiveID:           99,
		GuildOwner:       g1.UserID,
		Status:           livecontract.StatusContracting,
		ContractStart:    nowStamp,
		ContractDuration: 2,
		ContractEnd:      goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:        g1.Name,
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract1).Error)

	// 生成合约 2
	contract2 := livecontract.LiveContract{
		ID:               2,
		GuildID:          g1.ID,
		LiveID:           100,
		GuildOwner:       g1.UserID,
		Status:           livecontract.StatusContracting,
		ContractStart:    nowStamp,
		ContractDuration: 2,
		ContractEnd:      now.Add(time.Minute).Unix(),
		GuildName:        g1.Name,
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract2).Error)

	// 生成房间数据
	r1 := room.Room{
		Helper: room.Helper{
			RoomID:          8888,
			Name:            "测试房间 1",
			NameClean:       "测试房间 1",
			CreatorID:       contract1.LiveID,
			CreatorUsername: "测试主播转会 1",
			GuildID:         g1.ID,
		},
	}
	r2 := room.Room{
		Helper: room.Helper{
			RoomID:          9999,
			Name:            "测试房间 2",
			NameClean:       "测试房间 2",
			CreatorID:       contract2.LiveID,
			CreatorUsername: "测试主播转会 2",
			GuildID:         g2.ID,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{r1.Helper.RoomID, r2.Helper.RoomID}}})
	require.NoError(err)
	_, err = room.Collection().InsertMany(ctx, []interface{}{r1, r2})
	require.NoError(err)

	// 生成未处理的退会申请
	contractApplyment := contractapplyment.ContractApplyment{
		GuildID:          g1.ID,
		LiveID:           contract1.LiveID,
		Status:           contractapplyment.StatusPending,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.Add(time.Minute).Unix(),
		GuildName:        g1.Name,
		Type:             contractapplyment.TypeLiveTerminate,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&contractApplyment).Error)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(now)
	st := goutil.NewTimeUnixMilli(nowStampFirstDayOfThisMonth)
	et := goutil.NewTimeUnixMilli(now)

	// 创建本月流水数据
	t1 := transactionlog.TransactionLog{
		Title:        "测试公会本月流水数据",
		Income:       110,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: g1.ID,
		ToID:         contract1.LiveID,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterSuperFan,
		CTime:        nowStamp - 100,
		CreateTime:   nowStamp - 100,
		ModifiedTime: nowStamp - 100,
		ConfirmTime:  nowStamp - 100,
	}
	require.NoError(transactionlog.ADB().Create(&t1).Error)
	require.NoError(transactionlog.DB().Create(&t1).Error)

	t2 := transactionlog.TransactionLog{
		Title:        "测试公会本月流水数据",
		Income:       110,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: g1.ID,
		ToID:         contract2.LiveID,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterSuperFan,
		CTime:        nowStamp - 100,
		CreateTime:   nowStamp - 100,
		ModifiedTime: nowStamp - 100,
		ConfirmTime:  nowStamp - 100,
	}
	require.NoError(transactionlog.ADB().Create(&t2).Error)
	require.NoError(transactionlog.DB().Create(&t2).Error)

	// 删除数据
	_, err = livelog.Collection().DeleteMany(ctx, bson.M{"creator_id": bson.M{"$in": []int64{r1.Helper.CreatorID, r2.Helper.CreatorID}}})
	assert.NoError(err)
	_, err = livelog.Collection().InsertMany(ctx, []interface{}{
		livelog.Record{GuildID: g1.ID, RoomID: r1.Helper.RoomID, CreatorID: r1.Helper.CreatorID,
			CatalogID: 115, StartTime: st, EndTime: et, Duration: 43200000},
		livelog.Record{GuildID: g1.ID, RoomID: r1.Helper.RoomID, CreatorID: r1.Helper.CreatorID,
			CatalogID: 105, StartTime: st, EndTime: et, Duration: 42563},
		livelog.Record{GuildID: g1.ID, RoomID: r2.Helper.RoomID, CreatorID: r2.Helper.CreatorID,
			CatalogID: 116, StartTime: st, EndTime: et, Duration: 198937},
		livelog.Record{GuildID: g1.ID, RoomID: r2.Helper.RoomID, CreatorID: r2.Helper.CreatorID,
			CatalogID: 105, StartTime: st, EndTime: et, Duration: 2934752},
	})
	require.NoError(err)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	_, err = ActionGuildTransferCreator(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试主播 ID 都不存在
	body["creator_ids"] = "10000,10001"
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	_, err = ActionGuildTransferCreator(c)
	assert.Equal(actionerrors.ErrParamsMsg("请输入正确的主播 ID"), err)

	// 测试 confirm = 0
	body["creator_ids"] = fmt.Sprintf("%d,%d,%d", r1.Helper.CreatorID, r2.Helper.CreatorID, 100000)
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	_, err = ActionGuildTransferCreator(c)
	r := err.(*handler.ActionError)
	info := "<p><b>此次转会主播列表</b></p>" +
		"<table border=\"1\"><tr><th>主播 ID </th> <th>主播昵称</th> <th>房间号</th> <th>房间名称</th> <th>本月流水（元）</th> <th>本月有效直播时长</th></tr>" +
		"<tr><td>100</td><td>测试主播转会 2</td><td>9999</td><td>测试房间 2</td><td>100.00</td><td>52m13.689s</td></tr></table>" +
		"<p>共有 1 位主播将从公会【测试转会 1】转出，转入公会【测试转会 2】</p>" +
		"<p>当月流水、直播时长归属：新公会</p>" +
		"<p>签约时长：重签</p>" +
		"<p>存在解约未处理的主播，转会失败：99</p>" +
		"<p>其他原因失效主播：100000</p>"
	assert.Equal(actionerrors.ErrConfirmRequired(info, 1, true), err)
	assert.Equal(1, r.Info["confirm"])

	// 生成合约 3 (没有直播间的主播)
	contract3 := livecontract.LiveContract{
		ID:               3,
		GuildID:          g1.ID,
		LiveID:           105,
		GuildOwner:       g1.UserID,
		Status:           livecontract.StatusContracting,
		ContractStart:    nowStamp,
		ContractDuration: 2,
		ContractEnd:      now.Add(time.Minute).Unix(),
		GuildName:        g1.Name,
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract3).Error)

	// 测试 confirm = 0，转没有直播间的主播
	body["creator_ids"] = fmt.Sprintf("%d,%d,%d", r1.Helper.CreatorID, contract3.LiveID, 100000)
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	_, err = ActionGuildTransferCreator(c)
	r = err.(*handler.ActionError)
	info = "<p><b>此次转会主播列表</b></p>" +
		"<table border=\"1\"><tr><th>主播 ID </th> <th>主播昵称</th> <th>房间号</th> <th>房间名称</th> <th>本月流水（元）</th> <th>本月有效直播时长</th></tr>" +
		"<tr><td>105</td><td>测试主播转会 3</td><td>0</td><td>-</td><td>0.00</td><td>0</td></tr></table>" +
		"<p>共有 1 位主播将从公会【测试转会 1】转出，转入公会【测试转会 2】</p>" +
		"<p>当月流水、直播时长归属：新公会</p>" +
		"<p>签约时长：重签</p>" +
		"<p>存在解约未处理的主播，转会失败：99</p>" +
		"<p>其他原因失效主播：100000</p>"
	assert.Equal(actionerrors.ErrConfirmRequired(info, 1, true), err)
	assert.Equal(1, r.Info["confirm"])

	// 测试转会成功
	body["confirm"] = 1
	body["creator_ids"] = fmt.Sprintf("%d,%d,%d,%d", r1.Helper.CreatorID, r2.Helper.CreatorID, contract3.LiveID, 100000)

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/email",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock(userapi.APIGuildTransferRevenue,
		func(input interface{}) (any, error) {
			params := input.(map[string]interface{})
			err = transactionlog.DB().
				Where("suborders_num = ? AND to_id IN (?) AND type = ? AND status = ?",
					params["from_guild_id"].(int64),
					params["creator_ids"].([]int64),
					transactionlog.TypeGuildLive, transactionlog.StatusSuccess,
				).
				Where("confirm_time >= ? AND confirm_time < ?",
					params["start_time"].(int64),
					params["end_time"].(int64),
				).
				Update(map[string]interface{}{
					"suborders_num": params["to_guild_id"].(int64),
					"modified_time": now.Unix(),
				}).Error
			require.NoError(err)

			return handler.M{
				"status": true,
			}, nil
		})
	defer cancel()

	// 生成未处理降薪申请
	applicationEditRate := contractapplyment.ContractApplyment{
		LiveID:     105,
		GuildID:    g1.ID,
		GuildName:  g1.Name,
		Type:       contractapplyment.TypeRateDown,
		Status:     contractapplyment.StatusPending,
		ExpireTime: now.Add(time.Minute).Unix(),
		Rate:       40,
	}
	require.NoError(service.DB.Save(&applicationEditRate).Error)

	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	a, _ := ActionGuildTransferCreator(c)
	assert.Equal("转移公会主播成功", a)
	var count int
	require.NoError(guildtransferhistory.GuildTransferHistory{}.DB().
		Where("creator_id IN (?)", []int64{r2.Helper.CreatorID, r2.Helper.CreatorID, contract3.LiveID}).Count(&count).Error)
	assert.Equal(2, count)
	application := new(contractapplyment.ContractApplyment)
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ? AND status = ?",
		g1.ID, 105, contractapplyment.StatusInvalid).First(application).Error)
	assert.Equal(contractapplyment.StatusInvalid, application.Status)

	// 测试主播本月直播时长是否转入成功
	oldGuildUserDurationMap, err := livelog.GetUserLiveTotalDuration(g1.ID, []int64{r1.Helper.CreatorID, r2.Helper.CreatorID, contract3.LiveID}, nowStampFirstDayOfThisMonth, now)
	require.NoError(err)
	assert.Equal(1, len(oldGuildUserDurationMap))
	newGuildUserDurationMap, err := livelog.GetUserLiveTotalDuration(g2.ID, []int64{r1.Helper.CreatorID, r2.Helper.CreatorID, contract3.LiveID}, nowStampFirstDayOfThisMonth, now)
	require.NoError(err)
	assert.Nil(newGuildUserDurationMap[r1.Helper.CreatorID])

	// 确认当月流水是否转成功
	var tl []*transactionlog.TransactionLog
	assert.NoError(transactionlog.DB().
		Where("suborders_num = ? AND to_id IN (?) AND type = ? AND status = ?",
			g2.ID, []int64{r2.Helper.CreatorID, contract3.LiveID}, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("confirm_time BETWEEN ? AND ?", nowStampFirstDayOfThisMonth.Unix(), now.Unix()).
		Find(&tl).Error)
	assert.Equal(1, len(tl))

	// 确认转入新公会后的 rooms.guild_id 是否相应改变
	r3, err := room.Find(r2.RoomID)
	require.NoError(err)
	require.NotNil(r3)
	assert.Equal(g2.ID, r3.GuildID)

	// 测试本月直播时长不转入新公会
	body["from_guild_id"] = g2.ID
	body["to_guild_id"] = g1.ID
	body["revenue_live_duration_type"] = guildtransferhistory.TypeRevenueOriginal
	body["contract_type"] = guildtransferhistory.TypeContractOriginal

	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/guild-transfer-creator", true, body)
	a, _ = ActionGuildTransferCreator(c)
	assert.Equal("转移公会主播成功", a)

	// 删除时长数据
	_, err = livelog.Collection().DeleteMany(ctx, bson.M{"creator_id": bson.M{"$in": []int64{r1.Helper.CreatorID, r2.Helper.CreatorID}}})
	assert.NoError(err)
}
