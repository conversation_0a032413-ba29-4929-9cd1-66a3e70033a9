package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAdminPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (interface{}, error) {
			sms, ok := input.(map[string]interface{})
			require.True(ok)
			require.NotEmpty(sms)
			return handler.M{
				"count": 1,
			}, nil
		})
	defer cancel()

	guildInfo := &guild.Guild{
		Name:    "测试通过公会创建申请",
		UserID:  111111,
		Checked: guild.CheckedChecking,
	}
	require.NoError(service.DB.Create(guildInfo).Error)
	defer func() {
		require.NoError(service.DB.Table(guild.TableName()).Delete("", "id = ?", guildInfo.ID).Error)
	}()
	params := map[string]int64{"guild_id": guildInfo.ID}

	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/adminpass", paramToRequestBody(params))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	r, err := ActionAdminPass(ctx)
	require.NoError(err)
	resp := r.(adminPassResp)
	assert.True(resp.Pass)
	assert.True(resp.Notify)
}

func TestAddMonthRecommendSquarehotVacancy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID := int64(32)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 8, 31, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	require.NoError(recommend.GuildRecommendVacancy{}.DB().Delete("", "guild_id = ?", guildID).Error)
	defer require.NoError(recommend.GuildRecommendVacancy{}.DB().Delete("", "guild_id = ?", guildID).Error)
	require.NoError(addMonthRecommendSquarehotVacancy(guildID))
	var count int64
	require.NoError(recommend.GuildRecommendVacancy{}.DB().Where("guild_id = ?", guildID).Count(&count).Error)
	assert.Zero(count)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 8, 15, 0, 0, 0, 0, time.Local)
	})
	require.NoError(addMonthRecommendSquarehotVacancy(guildID))
	var g recommend.GuildRecommendVacancy
	require.NoError(recommend.GuildRecommendVacancy{}.DB().Where("guild_id = ? AND position = ?", guildID, Position4).Scan(&g).Error)
	assert.Equal(int64(1), g.Vacancy)
	assert.Equal(int64(1), g.EditedVacancy)
	assert.Equal(int64(1), g.InitialVacancy)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local).Unix(), g.StartTime)
	require.NoError(recommend.GuildRecommendVacancy{}.DB().Where("guild_id = ? AND position = ?", guildID, Position6).Scan(&g).Error)
	assert.Equal(int64(8), g.Vacancy)
	assert.Equal(int64(8), g.EditedVacancy)
	assert.Equal(int64(8), g.InitialVacancy)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local).Unix(), g.StartTime)
}

func TestCurrentPeriod(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 1, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	startTime, endTime := currentPeriod()
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 6, 15, 1, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 6, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), endTime)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 2, 1, 0, 0, 0, time.Local)
	})
	startTime, endTime = currentPeriod()
	assert.Equal(time.Date(2021, 7, 2, 0, 0, 0, 0, time.Local), startTime)
	assert.Equal(time.Date(2021, 8, 2, 0, 0, 0, 0, time.Local), endTime)
}
