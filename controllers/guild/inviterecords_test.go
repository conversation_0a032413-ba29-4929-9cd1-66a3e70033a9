package guild

/*
func TestActionInviteRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/api/v2/guild/inviterecords", nil)
	r, err := ActionInviteRecords(c)
	require.NoError(err)

	b, err := json.Marshal(r)
	require.NoError(err)
	resp := make(map[string]interface{})
	require.NoError(json.Unmarshal(b, &resp))

	assert.Contains(resp, "Datas")
	assert.Contains(resp, "pagination")
}
*/
