package guild

import (
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type guildIncomeV3Param struct {
	page     int64
	pageSize int64

	sort string

	startDate time.Time
	endDate   time.Time

	searchCreator handler.SearchWord
	searchAgent   handler.SearchWord

	guildID int64
	role    guildrole.GuildRole
	user    *user.User

	agentCreators agentCreators

	isExportCsv bool
}

var guildIncomeSortV3Supported = []string{
	"gift_income",
	"noble_income",
	"superfan_income",
	"play_income",
}

type incomeMenuV3 struct {
	GiftIncome     util.Float2DP `gorm:"column:gift_income" json:"gift_income"`
	NobleIncome    util.Float2DP `gorm:"column:noble_income" json:"noble_income"`
	SuperFanIncome util.Float2DP `gorm:"column:superfan_income" json:"superfan_income"`
	PlayIncome     util.Float2DP `gorm:"column:play_income" json:"play_income"`
}

type creatorIncomeItem struct {
	CreatorID       int64  `gorm:"column:creator_id" json:"creator_id"`
	CreatorUsername string `gorm:"-" json:"creator_username"`
	CreatorIconURL  string `gorm:"-" json:"creator_iconurl"`

	AgentID       int64  `gorm:"-" json:"agent_id"`
	AgentUsername string `gorm:"-" json:"agent_username"`

	GiftIncome     util.Float2DP `gorm:"column:gift_income" json:"gift_income"`
	NobleIncome    util.Float2DP `gorm:"column:noble_income" json:"noble_income"`
	SuperFanIncome util.Float2DP `gorm:"column:superfan_income" json:"superfan_income"`
	PlayIncome     util.Float2DP `gorm:"column:play_income" json:"play_income"`
}

type guildIncomeV3Resp struct {
	Menu       *incomeMenuV3       `json:"menu,omitempty"`
	Data       []creatorIncomeItem `json:"data"`
	Pagination goutil.Pagination   `json:"pagination"`
}

// ActionGuildIncomeV3 公会创建者获取公会收益
/**
 * @api {get} /api/v2/guild/guildincome-v3 公会创建者获取公会收益
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2022-05-21）
 * @apiParam {String} [end_date=今天] 截止日期（例 2022-06-21）
 * @apiParam {String} [search_creator] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {String} [sort=gift_income.desc] 排序方式：\
 * gift_income.asc, gift_income.desc, \
 * noble_income.asc, noble_income.desc, \
 * superfan_income.asc, superfan_income.desc \
 * play_income.asc, play_income.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "menu": { // p = 1 时返回
 *           "gift_income": 1.02, // 礼物收益（元）
 *           "noble_income": 12, // 贵族收益（元）
 *           "superfan_income": 12, // 超粉收益（元）
 *           "play_income": 12 // 玩法收益（元）
 *         },
 *         "data": [{
 *           "creator_id": 346285,
 *           "creator_iconurl": "http://static.maoercdn.com/avatars/201609/18/8bab6898a560f32f9893d6df816c493a121724.jpg",
 *           "creator_username": "T2ye_ZJ",
 *           "agent_id": 1, // 经纪人 ID
 *           "agent_username": "invincible", // 经纪人昵称
 *           "gift_income": 1.02, // 礼物收益（元）
 *           "noble_income": 12, // 贵族收益（元）
 *           "superfan_income": 12, // 超粉收益（元）
 *           "play_income": 12 // 玩法收益（元）
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionGuildIncomeV3(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(guildIncomeV3Param)
	if err := param.load(ctx); err != nil {
		return nil, err
	}

	resp := new(guildIncomeV3Resp)

	if param.page == 1 {
		err := resp.buildMenu(param)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	if err := resp.buildList(param); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return resp, nil
}

func setIncomeV3Fields(db *gorm.DB, moreFields ...string) *gorm.DB {
	fields := []string{
		transactionlog.SumRevenueColumn(transactionlog.RevenueTypeGuildCreator, transactionlog.GiftCondSQL(), "gift_income"),
		transactionlog.SumRevenueColumn(transactionlog.RevenueTypeGuildCreator, transactionlog.NobleCondSQL(), "noble_income"),
		transactionlog.SumRevenueColumn(transactionlog.RevenueTypeGuildCreator, transactionlog.SuperFanCondSQL(), "superfan_income"),
		transactionlog.SumRevenueColumn(transactionlog.RevenueTypeGuildCreator, transactionlog.PlayCondSQL(), "play_income"),
	}
	if len(moreFields) > 0 {
		fields = append(fields, moreFields...)
	}

	return db.Select(fields)
}

func (param *guildIncomeV3Param) load(ctx *handler.Context) error {
	var (
		err error
		ok  bool
	)
	param.page, param.pageSize, err = ctx.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	param.startDate, param.endDate, err = ctx.GetParamDateRange(
		now.AddDate(0, -1, 0).Format(goutil.TimeFormatYMD),
		now.Format(goutil.TimeFormatYMD),
	)
	if err != nil {
		return actionerrors.ErrParams
	}

	orderByField, order, ok := utils.ParseSortStr(
		ctx.GetDefaultParamString("sort", "gift_income.desc"),
		utils.GenerateSortMap(guildIncomeSortV3Supported),
	)
	if !ok {
		return actionerrors.ErrParams
	}
	param.sort = orderByField + " " + order

	param.searchCreator, err = ctx.GetParamSearchWord("search_creator")
	if err != nil && err != handler.ErrEmptyValue {
		return actionerrors.ErrParams
	}

	param.searchAgent, err = ctx.GetParamSearchWord("search_agent")
	if err != nil && err != handler.ErrEmptyValue {
		return actionerrors.ErrParams
	}

	role, guildInfo, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return actionerrors.ErrNoAuthority
	}
	param.user = ctx.User()
	param.role = role
	param.guildID = guildInfo.ID

	param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *guildIncomeV3Param) revenueSubquery() interface{} {
	db := transactionlog.ADB().
		Where(transactionlog.AllLiveRevenueCondSQL(transactionlog.TypeGuildLive, param.guildID)).
		Group("to_id")
	db = setIncomeV3Fields(db, "to_id")

	return addIncomeTimeFilter(db, "confirm_time", param.startDate, param.endDate).SubQuery()
}

func (param *guildIncomeV3Param) applySearch(db *gorm.DB) (*gorm.DB, error) {
	if param.searchCreator.Word != "" {
		if param.searchCreator.IsInteger {
			db = db.Where("lc.live_id = ?", param.searchCreator.WordInteger)
		} else {
			var allCreatorIDs []int64
			// 该公会下所有的主播 ID
			err := db.Pluck("live_id", &allCreatorIDs).Error
			if err != nil {
				return nil, err
			}
			if len(allCreatorIDs) == 0 {
				return nil, nil
			}
			// 从该公会下所有的主播 ID 中匹配用户
			// FIXME: 当存在大量主播时，此处会存在慢 SQL 问题
			liveIDs, err := mowangskuser.SearchUserByUsername(param.searchCreator.Word, allCreatorIDs)
			if err != nil {
				return nil, err
			}
			if len(liveIDs) == 0 {
				return nil, nil
			}
			db = db.Where("lc.live_id IN (?)", liveIDs)
		}
	}
	if param.searchAgent.Word != "" {
		var liveIDs []int64
		if param.searchAgent.IsInteger {
			liveIDs = param.agentCreators.agentCreatorIDs(
				param.user.ID,
				param.role,
				param.searchAgent.WordInteger,
			)
		} else {
			allAgentIDs := param.agentCreators.allAgentIDs()
			if len(allAgentIDs) == 0 {
				return nil, nil
			}
			agentIDs, err := mowangskuser.SearchUserByUsername(param.searchAgent.Word, allAgentIDs)
			if err != nil {
				return nil, err
			}
			if len(agentIDs) == 0 {
				return nil, nil
			}
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDs...)
		}
		if len(liveIDs) == 0 {
			return nil, nil
		}
		db = db.Where("lc.live_id IN (?)", liveIDs)
	}

	return db, nil
}

func (resp *guildIncomeV3Resp) buildMenu(param *guildIncomeV3Param) error {
	db := transactionlog.ADB().
		Where(transactionlog.AllLiveRevenueCondSQL(transactionlog.TypeGuildLive, param.guildID))
	db = setIncomeV3Fields(db)
	db = addIncomeTimeFilter(db, "confirm_time", param.startDate, param.endDate)

	resp.Menu = &incomeMenuV3{}
	if !param.role.IsGuildOwner() {
		agentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(agentCreatorIDs) == 0 {
			return nil
		}
		db = db.Where("to_id IN (?)", agentCreatorIDs)
	}
	err := db.Take(resp.Menu).Error

	return err
}

func (resp *guildIncomeV3Resp) fillUserInfo(param *guildIncomeV3Param, creatorIDs []int64) error {
	creatorAgentMap := make(map[int64]int64, len(param.agentCreators))
	userIDs := make([]int64, 0, len(creatorIDs)+len(param.agentCreators))
	userIDs = append(userIDs, creatorIDs...)

	if param.role.IsGuildOwner() {
		for _, item := range param.agentCreators {
			creatorAgentMap[item.CreatorID] = item.AgentID
			userIDs = append(userIDs, item.AgentID)
		}
	}
	userInfoMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return err
	}
	for i, item := range resp.Data {
		if creatorInfo, ok := userInfoMap[item.CreatorID]; ok {
			resp.Data[i].CreatorIconURL = creatorInfo.IconURL
			resp.Data[i].CreatorUsername = creatorInfo.Username
		}
		// 如果访问用户不是会长，则当前访问用户为经纪人
		if !param.role.IsGuildOwner() {
			resp.Data[i].AgentID = param.user.ID
			resp.Data[i].AgentUsername = param.user.Username
			continue
		}

		if agentID, ok := creatorAgentMap[item.CreatorID]; ok {
			agentInfo, ok := userInfoMap[agentID]
			if ok {
				resp.Data[i].AgentID = agentInfo.ID
				resp.Data[i].AgentUsername = agentInfo.Username
			}
		}
	}

	return nil
}

func (resp *guildIncomeV3Resp) buildEmptyList(param *guildIncomeV3Param) {
	resp.Data = make([]creatorIncomeItem, 0)
	resp.Pagination = goutil.MakePagination(0, param.page, param.pageSize)
}

func (resp *guildIncomeV3Resp) buildList(param *guildIncomeV3Param) error {
	var err error
	db := livecontract.ADB("lc").
		Where("lc.guild_id = ?", param.guildID).
		// 获取状态为：合约解约、合约失效、合约生效中的合约
		Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished, livecontract.StatusContracting})

	if db, err = param.applySearch(db); err != nil {
		return err
	}

	if db == nil {
		resp.buildEmptyList(param)
		return nil
	}

	if !param.role.IsGuildOwner() {
		creatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(creatorIDs) == 0 {
			resp.buildEmptyList(param)
			return nil
		}
		db = db.Where("lc.live_id IN (?)", creatorIDs)
	}

	var totalCount int64
	err = db.Select("COUNT(DISTINCT live_id) AS creator_count").Row().Scan(&totalCount)
	if err != nil && !servicedb.IsErrNoRows(err) {
		resp.buildEmptyList(param)
		return nil
	}
	if totalCount == 0 {
		resp.buildEmptyList(param)
		return nil
	}
	if param.isExportCsv {
		param.pageSize = totalCount
	}
	resp.Pagination = goutil.MakePagination(totalCount, param.page, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]creatorIncomeItem, 0)
		return nil
	}
	db = db.Select([]string{
		"lc.live_id AS creator_id",
		"COALESCE(t.gift_income, 0) AS gift_income",
		"COALESCE(t.noble_income, 0) AS noble_income",
		"COALESCE(t.superfan_income, 0) AS superfan_income",
		"COALESCE(t.play_income, 0) AS play_income",
	}).Joins("LEFT JOIN ? AS t ON t.to_id = lc.live_id", param.revenueSubquery()).
		Group("lc.live_id").
		Order(param.sort)
	err = resp.Pagination.ApplyTo(db).Scan(&resp.Data).Error
	if err != nil {
		return err
	}
	if len(resp.Data) == 0 {
		resp.buildEmptyList(param)
		return nil
	}

	userIDs := make([]int64, len(resp.Data))
	for i, item := range resp.Data {
		userIDs[i] = item.CreatorID
	}
	if err = resp.fillUserInfo(param, userIDs); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

// ActionGuildIncomeExportV3 公会创建者导出公会收益 csv
/**
 * @api {get} /api/v2/guild/guildincomeexport-v3 公会创建者导出公会收益 csv
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2019-05-01）
 * @apiParam {String} [end_date=今天] 截止日期（例 2019-05-15）
 * @apiParam {String} [search_creator] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildIncomeExportV3(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(guildIncomeV3Param)
	if err := param.load(ctx); err != nil {
		return nil, err
	}

	lockKey := keys.LockGuildIncomeExport1.Format(ctx.UserID())
	ok, err := service.Redis.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("操作太快啦，请稍后再试~")
	}
	param.page = 1
	param.isExportCsv = true

	resp := new(guildIncomeV3Resp)
	if err := resp.buildList(param); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	records := make([][]string, 0, param.pageSize+1)
	titleLine := []string{
		"主播 ID", "主播昵称", "经纪人 ID", "经纪人",
		"礼物总收益（元）", "贵族开通总收益（元）", "超粉总收益（元）", "玩法总收益（元）",
	}
	records = append(records, titleLine)
	for _, v := range resp.Data {
		var agentIDStr string
		if v.AgentID != 0 {
			agentIDStr = strconv.FormatInt(v.AgentID, 10)
		}
		records = append(records, []string{
			strconv.FormatInt(v.CreatorID, 10),
			v.CreatorUsername,
			agentIDStr,
			v.AgentUsername,
			v.GiftIncome.String(),
			v.NobleIncome.String(),
			v.SuperFanIncome.String(),
			v.PlayIncome.String(),
		})
	}

	filename := fmt.Sprintf(
		"公会收益_%s_%s.csv",
		param.startDate.Format(goutil.TimeFormatYMD),
		param.endDate.Format(goutil.TimeFormatYMD),
	)
	return writeDataToCsv(ctx, records, filename)
}
