package guild

// ActionJoin 主播申请加入公会
/*
 * @api {post} /api/v2/guild/join 主播申请加入公会
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} guild_id 公会 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (404) {Number} code 500050001
 * @apiError (404) {String} info 公会不存在
 *
 * @apiError (403) {Number} code 500050002
 * @apiError (403) {String} info 已加入另一公会，不能加入多个公会
 *
 * @apiError (403) {Number} code 500050003
 * @apiError (403) {String} info 已经申请该公会，请等待公会答复
 *
 * @apiError (403) {Number} code 500050009
 * @apiError (403) {String} info 该公会已经向您发起邀请，请查看邀请记录
 */
/*
func ActionJoin(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID int64 `json:"guild_id"`
	}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildID := params.GuildID

	ok, err := SendJoinRequest(c.User().ID, guildID)

	return ok, err
}
*/

/*
// SendJoinRequest for live anchor to send join request for joining guild
func SendJoinRequest(userID int64, guildID int64) (bool, error) {
	// TODO: Add lock for avoiding concurrency request?
	if alreadyHas, err := HasOngoingContract(userID, guildID); alreadyHas {
		return false, err
	}

	var guildData guild.Guild
	err := service.DB.Table(guild.TableName()).Select("user_id, name").
		Where("id = ? AND checked = ?", guildID, guild.CheckedPass).Scan(&guildData).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return false, actionerrors.ErrGuildNotExist
		}
		return false, err
	}

	contract := livecontract.LiveContract{
		GuildID:       guildID,
		LiveID:        userID,
		ContractStart: time.Now().Unix(),
		ContractEnd:   1999999999, // TODO: 当前版本合约暂无合约过期的规定
		Rate:          0.00,       // TODO: 当前版本合约暂无主播与公会分成比例的规定
		KPI:           "",         // TODO: 当前版本合约暂无直播指标的规定
		Status:        livecontract.StatusUntreated,
		Type:          livecontract.FromLiver,
		GuildOwner:    guildData.UserID,
		GuildName:     guildData.Name,
	}
	err = service.DB.Create(&contract).Error
	if err != nil {
		return false, err
	}

	return true, nil
}
*/

/*
// HasOngoingContract checks whether live anchor already has ongoing guild live contract
func HasOngoingContract(userID int64, guildID int64) (bool, error) {
	var r livecontract.LiveContract
	err := service.DB.Table(livecontract.TableName()).
		Where("live_id = ? AND contract_end > ?", userID, time.Now().Unix()).
		Where("(status >= ?) OR (status = ? AND guild_id = ?)", livecontract.StatusContracting, livecontract.StatusUntreated, guildID).
		Scan(&r).Error
	if err == gorm.ErrRecordNotFound {
		return false, err
	}
	if r.GuildID == guildID {
		if r.Type == livecontract.FromLiver {
			return true, actionerrors.ErrAlreadyRequestJoin
		}
		return true, actionerrors.ErrGuildAlreadyInvite
	}

	return true, actionerrors.ErrCannotJoinTwice
}
*/
