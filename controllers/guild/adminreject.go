package guild

import (
	"fmt"
	"html"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

// ActionAdminreject 超管拒绝公会创建
/**
 * @api {post} /api/v2/admin/guild/adminreject 超管拒绝公会创建
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {String} reason 拒绝原因
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "pass": true,
 *       "notify": true
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (404) {Number} code 500050001
 * @apiError (404) {String} info 公会不存在
 */
func ActionAdminreject(c *handler.Context) (handler.ActionResponse, error) {
	access, _, err := liveuser.IsStaff(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}
	var param struct {
		GuildID int64  `json:"guild_id"`
		Reason  string `json:"reason"`
	}
	err = c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	ok, ownerID, err := guild.SwitchChecked(nil, param.GuildID, guild.CheckedReject)
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, actionerrors.ErrGuildNotExist
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !ok {
		return nil, handler.ErrBadRequest
	}

	var (
		notify  = true
		url     = config.Conf.Params.URL.Main + "guild/edit"
		content = fmt.Sprintf("很遗憾 _(:3 」∠)_ 您提交的公会入驻申请未能通过审核。拒绝原因：%s。您可完善申请信息后重新提交，公会入驻申请网址为 <a href=\"%s\">%s</a>",
			html.EscapeString(param.Reason), url, url)
	)
	msg := []pushservice.SystemMsg{
		{
			UserID:  ownerID,
			Title:   "您的公会入驻申请未通过审核",
			Content: content,
		},
	}
	err = service.PushService.SendSystemMsgWithOptions(msg, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
		notify = false
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("拒绝公会创建，公会 ID：%d，拒绝原因：%s", param.GuildID, param.Reason)
	box.AddAdminLog(intro, userapi.CatalogGuildReject)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return handler.M{
		"pass":   true,
		"notify": notify,
	}, nil
}
