package guild

// ActionPassjoin 公会通过主播入会申请
/*
 * @api {post} /api/v2/guild/passjoin 公会通过主播入会申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} contract_id 合同 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {bool} info true 表示签约成功，false 表示签约失败
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * 需要处理错误
 * @apiError (404) {Number} code 500050005
 * @apiError (404) {String} info 该合约不存在
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该合约状态已更改
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @todo 该接口现在并发不安全，之后进行调整
 *
 */
/*
func ActionPassjoin(c *handler.Context) (handler.ActionResponse, error) {
	_, err := GetUserPassedGuildID(c.User().ID)
	if err != nil {
		return nil, err
	}
	// 获取合约 ID
	contractID, err := getContractID(c)
	if err != nil {
		return false, err
	}

	// 检查该合约是否存在
	contract, err := lct.GetByPK(contractID)
	if err != nil {
		return false, actionerrors.ErrServerInternal.New(err, nil)
	}
	if contract == nil {
		return false, actionerrors.ErrContractNotExist
	}

	// 查看该用户是否有统一签约权限
	if err := contract.HaveRight(lct.StatusUntreated, lct.FromLiver, c.User().ID); err != nil {
		return false, err
	}

	// 该主播是否已经和其他公会签约
	count, err := lct.LiveCount(contract.LiveID, lct.StatusContracting)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if count >= 1 {
		lct.UpdateOtherContractsFinished(contract.LiveID, 0)
		return false, actionerrors.ErrCannotJoinTwice
	}

	ok := contract.UpdateContract(lct.StatusContracting)

	// 合约通过发送成功
	if ok {
		err = messageassign.SystemMessageAssign(
			contract.LiveID,
			"您的入会申请已被通过",
			fmt.Sprintf(
				"直播公会 %s 已通过您的入会申请。您于%s与该直播公会成功签约。",
				contract.GuildName, time.Now().Format(" 2006 年 01 月 02 日"),
			),
		)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return ok, nil
}
*/
