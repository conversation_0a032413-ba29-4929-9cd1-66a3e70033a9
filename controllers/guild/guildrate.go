package guild

import (
	"fmt"
	"net/http"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 后面改成不是写死的
var accessIDs = []int64{2, 6, 10}

type guildrateParam struct {
	GuildID int64   `json:"guild_id"`
	Rate    float64 `json:"rate"`
}

// ActionGuildrate 超管编辑公会分成
/**
 * @api {post} /api/v2/admin/guild/guildrate 超管编辑公会分成
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {Double} rate 公会分成
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "rate": true
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionGuildrate(c *handler.Context) (handler.ActionResponse, error) {
	// REVIEW: 如何确定是否有编辑公会分成的权限
	// TODO: 编辑权限未限制
	u := c.User()
	access, _, err := liveuser.IsStaff(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}
	access = false
	for i := 0; i < len(accessIDs); i++ {
		if accessIDs[i] == u.ID {
			access = true
			break
		}
	}
	if !access {
		return nil, actionerrors.NewUnknownError(http.StatusForbidden, "该用户没有权限编辑公会分成")
	}

	var param guildrateParam
	err = c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Rate < 0.0 || param.Rate > 1.0 {
		return nil, actionerrors.ErrParams
	}
	g, err := guild.Find(param.GuildID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrGuildNotExist
	}
	err = guildbalance.UpdateRate(param.GuildID, param.Rate)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	// TODO: 通知魔王和七夜，文案未给
	notifyIds := []int64{1, 2}
	content := fmt.Sprintf("%s（用户 ID: %d）将直播公会 %s 的公会分成修改为 %f", u.Username, u.ID, g.Name, param.Rate)
	notify := true
	for i := 0; i < len(notifyIds); i++ {
		err := messageassign.SystemMessageAssign(notifyIds[i], "直播公会分成修改", content)
		if err != nil {
			logger.Error(err)
			notify = false
		}
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("编辑公会分成，公会 ID：%d，公会分成：%f", param.GuildID, param.Rate)
	box.AddAdminLog(intro, userapi.CatalogGuildEditRate)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return handler.M{
		"rate":   true,
		"notify": notify,
	}, nil
}

type respContractRates struct {
	Rates []int `json:"rates"`
}

// ActionContractRates 获取公会与主播最低分成比例
/**
 * @api {get} /api/v2/guild/contract/rates 获取公会与主播最低分成比例
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "rates": [45, 50, 55, 60]
 *     }
 *   }
 *
 */
func ActionContractRates(c *handler.Context) (handler.ActionResponse, error) {
	resp := respContractRates{
		Rates: guildrate.ContractRates,
	}
	return resp, nil
}

type respRateInfo struct {
	Rate       int   `json:"rate"`
	EditRate   int   `json:"edit_rate"`
	EditType   int64 `json:"edit_type"`
	Status     int64 `json:"status"`
	ExpireTime int64 `json:"expire_time"`
	CreateTime int64 `json:"create_time"`
}

// ActionApplicationRateInfo 公会获取降薪申请详情
/**
 * @api {get} /api/v2/guild/application/rate/info 公会获取降薪申请详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild/application
 *
 * @apiParam {Number} creator_id 主播 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "rate": 45, // 当期分成比例
 *       "edit_rate": 46, // 最新未过期申请调整最低分成比例
 *       "edit_type": 2, // 修改分成类型，2：公会对主播降薪
 *       "status": 1, // 合约状态，-1：拒绝；0：待处理；1：同意
 *       "expire_time": 1637635487, // 合约过期时间
 *       "create_time": 1637635487 // 合约创建时间
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (403) {Number} code 100010500
 * @apiError (403) {String} info 服务器内部错误
 */
func ActionApplicationRateInfo(c *handler.Context) (handler.ActionResponse, error) {
	creatorID, err := c.GetParamInt64("creator_id")
	if err != nil || creatorID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 获取公会
	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !role.IsGuildOwner() {
		return nil, actionerrors.ErrNoAuthority
	}

	// 查看主播与公会关系
	// TODO: 后续支持查看历史降薪申请
	contract := new(livecontract.LiveContract)
	err = contract.DB().First(contract, "guild_id = ? AND live_id = ? AND status = ?",
		guildInfo.ID, creatorID, livecontract.StatusContracting).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrCreatorNotInYourGuild
		}
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	// 查找最新未过期的降薪申请
	now := util.TimeNow()
	application := new(contractapplyment.ContractApplyment)
	err = application.DB().Where("guild_id = ? AND contract_id = ? AND live_id = ? AND type = ? AND expire_time > ?",
		guildInfo.ID, contract.ID, creatorID, contractapplyment.TypeRateDown, now.Unix()).
		Order("create_time DESC").First(application).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp := respRateInfo{
		Rate:       contract.Rate,
		EditRate:   application.Rate,
		EditType:   application.Type,
		Status:     application.Status,
		ExpireTime: application.ExpireTime,
		CreateTime: application.CreateTime,
	}
	return resp, nil
}
