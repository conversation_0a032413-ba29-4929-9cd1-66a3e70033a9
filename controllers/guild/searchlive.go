package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/certification"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionSearchLive 主播查询
/**
 * @api {get} /api/v2/guild/searchlive 查询主播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} real_name 用户真实姓名
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_id": 123456,
 *       "username": "2345",
 *       "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 500020035
 * @apiError (403) {String} info 该用户尚未完成实名认证，暂时无法邀请
 *
 * @apiError (403) {Number} code 500020036
 * @apiError (403) {String} info 真实姓名错误，请检查后再试
 */
func ActionSearchLive(c *handler.Context) (handler.ActionResponse, error) {
	userID, err := c.GetParamInt64("user_id")
	realName, _ := c.GetParamString("real_name")
	if err != nil || realName == "" {
		return nil, actionerrors.ErrParams
	}
	user, err := mowangskuser.FindByUserID(userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	err = validateRealNameAuthentication(userID, realName)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func validateRealNameAuthentication(userID int64, comparedRealName string) error {
	cert, err := certification.Info(userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if cert == nil {
		return actionerrors.ErrCannotInviteWithoutAuthentication
	}
	realName, err := cert.DecryptedRealName()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if realName != comparedRealName {
		return actionerrors.ErrInvalidRealName
	}
	return nil
}
