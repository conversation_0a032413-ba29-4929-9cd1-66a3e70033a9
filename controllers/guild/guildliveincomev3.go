package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// ActionGuildLiveIncomeV3 公会创建者获取公会主播收益流水
/**
 * @api {get} /api/v2/guild/guildliveincome-v3 公会创建者获取公会主播收益流水
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1,2,3} [type=0] 收益类型，0：礼物收益，1：贵族收益，2：超粉收益，3：玩法收益
 * @apiParam {String} start_date 开始时间（例 2019-05-01）
 * @apiParam {String} end_date 结束时间（例 2019-05-15）
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {Number} creator_id 主播 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "total": 1.35,  // 收益总额（单位：元），仅第一页时返回
 *         "data": [{
 *           "confirm_time": 1556640000,  // 秒级时间戳
 *           "username": "违规昵称_Q61dOKx3",
 *           "title": "直播收益",
 *           "revenue": 0.45,  // 金额（单位：元）
 *           "status": 1  // 状态：1 交易成功
 *         },
 *         {
 *           "confirm_time": 1556640000,
 *           "username": "违规昵称_Q61dOKx3",
 *           "title": "直播收益",
 *           "revenue": 0.45,
 *           "status": 1
 *         }],
 *         "pagination": {
 *           "count": 2,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildLiveIncomeV3(ctx *handler.Context) (handler.ActionResponse, error) {
	var param utils.CreatorIncomeListParam
	if err := param.LoadCommonParams(ctx); err != nil {
		return nil, err
	}
	creatorID, err := ctx.GetParamInt64("creator_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	role, guildInfo, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}

	queryContract := livecontract.LiveContract{}.DB().
		Where("guild_id = ?", guildInfo.ID).
		Where("live_id = ?", creatorID).
		Where("status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished, livecontract.StatusContracting})
	exists, err := servicedb.Exists(queryContract)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrNoAuthority
	}

	if !role.IsGuildOwner() {
		isAssigned, err := guildagent.IsAssigned(creatorID, ctx.UserID())
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !isAssigned {
			return nil, actionerrors.ErrCreatorNotAssignedToYou
		}
	}

	return param.CreatorIncomeList(creatorID, guildInfo.ID, false)
}
