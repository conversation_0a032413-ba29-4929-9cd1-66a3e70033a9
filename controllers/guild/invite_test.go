package guild

/*
func TestInvite(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(guild.TableName()).Where("id = 3").Update("checked", guild.CheckedPass).Error
	require.NoError(err)
	c := livecontract.LiveContract{
		ID:          10013,
		GuildID:     3,
		LiveID:      12345,
		ContractEnd: 2147483647,
		GuildName:   "测试公会邀请已邀请的主播",
	}
	err = service.DB.Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	// 删除对 105 用户的邀请记录，以便再次邀请
	err = service.DB.Table(livecontract.TableName()).Where("live_id = ?", 105).Delete("").Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	// 测试邀请已邀请过的主播
	body := map[string]interface{}{"user_id": 12345}
	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "api/v2/guild/invite", paramToRequestBody(body))
	r, err := ActionInvite(ctx)
	assert.Nil(r)
	e := err.(*handler.ActionError)
	assert.Equal(handler.CodeUnknownError, e.Code)

	// 测试正常邀请
	body["user_id"] = 105
	ctx.C.Request, _ = http.NewRequest("POST", "api/v2/guild/invite", paramToRequestBody(body))
	r, err = ActionInvite(ctx)
	assert.True(r.(bool))
	assert.Nil(err)

	// 测试邀请参数错误
	r, err = ActionInvite(ctx)
	assert.Nil(r)
	assert.Equal("参数错误", err.Error())
	time.Sleep(200 * time.Millisecond)

	// 确认主播已被邀请
	contract := new(livecontract.LiveContract)
	assert.NoError(service.DB.Table(livecontract.TableName()).Where("live_id = ?", 105).First(contract).Error)
	assert.Equal(livecontract.StatusUntreated, contract.Status)
	assert.Equal(int64(3), contract.GuildID)
	assert.Equal(livecontract.FromGuild, int(contract.Type))
}
*/
