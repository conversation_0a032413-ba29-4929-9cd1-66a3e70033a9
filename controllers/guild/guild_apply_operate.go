package guild

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type guildOperateLiveApplyParam struct {
	ID     int64 `json:"id"`
	LiveID int64 `json:"live_id"`

	applymentType int64
	guild         *guild.IDName
	role          guildrole.GuildRole
	user          *user.User
}

// TODO: 由于共用代码了，单元测试可以进行优化，减少对测试数据的操作
func (param *guildOperateLiveApplyParam) updateContract(db *gorm.DB, update map[string]interface{}) error {
	db = db.Table(contractapplyment.TableName()).
		Where("id = ? AND guild_id = ? AND live_id = ? AND type = ? AND status = ? AND expire_time > ?",
			param.ID, param.guild.ID, param.LiveID, param.applymentType,
			contractapplyment.StatusPending, goutil.TimeNow().Unix()).Updates(update)
	if db.Error != nil {
		return actionerrors.NewErrServerInternal(db.Error, nil)
	}
	if db.RowsAffected == 0 {
		return actionerrors.ErrGuildApplyStatus
	}
	return nil
}

func newGuildOperateLiveApplyParam(c *handler.Context, applymentType int64) (*guildOperateLiveApplyParam, error) {
	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if guildInfo == nil || !hasManagerOperateApplymentPermission(role, applymentType) {
		return nil, actionerrors.ErrNoAuthority
	}

	param := &guildOperateLiveApplyParam{
		applymentType: applymentType,
		guild:         guildInfo,
		role:          role,
		user:          c.User(),
	}
	_ = c.BindJSON(param)
	if param.ID == 0 || param.LiveID == 0 {
		param.ID, _ = c.GetParamInt64("id")
		param.LiveID, _ = c.GetParamInt64("live_id")
	}
	if param.ID <= 0 || param.LiveID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *guildOperateLiveApplyParam) checkApplyment(applymentType int64) (*contractapplyment.ContractApplyment, error) {
	ca, err := contractapplyment.FindByID(param.ID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if ca == nil || !ca.IsPending() ||
		ca.GuildID != param.guild.ID || ca.LiveID != param.LiveID ||
		ca.Type != applymentType {
		return nil, actionerrors.ErrGuildApplyStatus
	}

	if !param.role.IsGuildOwner() {
		if ca.AgentID == 0 {
			isAssigned, err := guildagent.IsAssigned(ca.LiveID, param.user.ID)
			if err != nil {
				return nil, actionerrors.ErrServerInternal.New(err, nil)
			}
			if !isAssigned {
				return nil, actionerrors.ErrCreatorNotAssignedToYou
			}
		} else if ca.AgentID != param.user.ID {
			return nil, actionerrors.ErrCannotOperateOthersApplyment
		}
	}

	return ca, nil
}

// ActionApplySignPass 公会通过主播签约申请
/**
 * @api {post} /api/v2/guild/applyment/sign/pass 公会通过主播签约申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "签约成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050005
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplySignPass(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveSign)
	if err != nil {
		return nil, err
	}
	ca, err := contractapplyment.FindByID(param.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// NOTICE: 目前限制了同一个主播只能有一个处理中的申请，所以不对主播的申请做处理
	if ca == nil || !ca.IsPending() ||
		ca.GuildID != param.guild.ID || ca.LiveID != param.LiveID ||
		ca.Type != contractapplyment.TypeLiveSign {
		return nil, actionerrors.ErrGuildApplyStatus
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	now := goutil.TimeNow()
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 新建合同
		contract := &livecontract.LiveContract{
			GuildID:          param.guild.ID,
			GuildOwner:       c.UserID(),
			GuildName:        param.guild.Name,
			LiveID:           param.LiveID,
			ContractStart:    now.Unix(),
			ContractEnd:      contractapplyment.GetContractEnd(now, ca.ContractDuration).Unix(),
			ContractDuration: ca.ContractDuration,
			Rate:             guildrate.ApplymentRate(ca.Rate),
			Type:             livecontract.FromLiver,
			Status:           livecontract.StatusContracting,
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
		}
		err := tx.Save(contract).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 更新申请表
		update := map[string]interface{}{
			"status":               contractapplyment.StatusAgreed,
			"process_time":         now.Unix(),
			"modified_time":        now.Unix(),
			"contract_id":          contract.ID,
			"contract_expire_time": contract.ContractEnd,
		}
		err = param.updateContract(tx, update)
		if err != nil {
			return err
		}
		// 让其他公会的签约申请失效
		delete(update, "contract_expire_time")
		delete(update, "contract_id")
		update["status"] = contractapplyment.StatusInvalid
		err = tx.Table(contractapplyment.TableName()).
			Where("live_id = ? AND status = ?", param.LiveID, contractapplyment.StatusPending).
			Not("id = ?", param.ID).Updates(update).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		// 更新主播数冗余字段
		err = guild.IncreaseLiveNum(param.guild.ID, 1, tx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 更新直播间公会信息
		err = room.UpdateGuildID(param.LiveID, param.guild.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的签约申请已通过",
		fmt.Sprintf(
			"直播公会 %s 已通过您的签约申请。您于%s与该直播公会成功签约。",
			param.guild.Name, now.Format(" 2006 年 01 月 02 日")),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "签约成功", nil
}

// ActionApplySignRefuse 公会拒绝主播签约申请
/**
 * @api {post} /api/v2/guild/applyment/sign/refuse 公会拒绝主播签约申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "拒绝成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050005
 * @apiError (400) {String} info 该申请不存在
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplySignRefuse(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveSign)
	if err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusDeclined,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的签约申请未通过",
		fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的签约申请。", param.guild.Name),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "拒绝成功", nil
}

// ActionApplySignRevoke 公会撤回邀请主播申请
/**
 * @api {post} /api/v2/guild/applyment/sign/revoke 公会撤回签约主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "已撤回签约邀请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplySignRevoke(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeGuildSign)
	if err != nil {
		return nil, err
	}

	if _, err = param.checkApplyment(contractapplyment.TypeGuildSign); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusRevoked,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}

	return "已撤回签约邀请", nil
}

// ActionApplyRenewPass 公会通过主播续约申请
/**
 * @api {post} /api/v2/guild/applyment/renew/pass 公会通过主播续约申请
 * @apiDescription 公会同意续约后未处理的公会降薪申请失效
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "签约成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050005
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyRenewPass(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveRenew)
	if err != nil {
		return nil, err
	}
	now := goutil.TimeNow()
	// 这里查找公会发起的清退主播申请
	ca := new(contractapplyment.ContractApplyment)
	err = service.DB.First(ca, "guild_id = ? AND live_id = ? AND type = ? AND status = ? AND expire_time > ?", param.guild.ID, param.LiveID, contractapplyment.TypeGuildExpel,
		contractapplyment.StatusPending, now.Unix()).Error
	if err == nil {
		return nil, actionerrors.ErrGuildApplyConflicted("请先撤回对该主播的清退申请")
	}
	if !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if ca, err = param.checkApplyment(contractapplyment.TypeLiveRenew); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		update := map[string]interface{}{
			"status":        contractapplyment.StatusAgreed,
			"process_time":  now.Unix(),
			"modified_time": now.Unix(),
		}
		err := param.updateContract(tx, update)
		if err != nil {
			return err
		}

		// 未处理的降薪申请失效
		err = contractapplyment.ApplicationEditRateInvalid(ca.GuildID, ca.LiveID, tx)
		if err != nil {
			return err
		}

		// 更新合同
		db := tx.Table(livecontract.TableName()).
			Where("id = ? AND guild_id = ? AND live_id = ? AND status = ?",
				ca.ContractID, ca.GuildID, ca.LiveID, livecontract.StatusContracting).
			Updates(map[string]interface{}{
				"contract_end":      ca.ContractExpireTime,
				"contract_duration": ca.ContractDuration,
				"rate":              guildrate.ApplymentRate(ca.Rate),
				// 续约后重置主播在合约期内是否申请过协商解约的状态
				"attr":          livecontract.UnsetAttrBitMaskExpr(livecontract.AttrBitMaskLiveTerminated),
				"modified_time": now.Unix(),
			})
		if db.Error != nil {
			return actionerrors.ErrServerInternal.New(db.Error, nil)
		}
		if db.RowsAffected == 0 {
			return actionerrors.ErrContractNotExist
		}
		// 更新直播间公会信息
		err = room.UpdateGuildID(ca.LiveID, ca.GuildID)
		if err != nil {
			return actionerrors.ErrServerInternal.New(err, nil)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的续约申请已通过",
		fmt.Sprintf(
			"直播公会 %s 已通过您的续约申请。您于%s与该直播公会成功续约。",
			param.guild.Name, now.Format(" 2006 年 01 月 02 日")),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "续约成功", nil
}

// ActionApplyRenewRefuse 公会拒绝主播续约申请
/**
 * @api {post} /api/v2/guild/applyment/renew/refuse 公会拒绝主播续约申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "拒绝成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyRenewRefuse(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveRenew)
	if err != nil {
		return nil, err
	}

	if _, err = param.checkApplyment(contractapplyment.TypeLiveRenew); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusDeclined,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的续约申请未通过",
		fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的续约申请。", param.guild.Name),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "拒绝成功", nil
}

// ActionApplyRenewRevoke 公会撤回续约主播申请
/**
 * @api {post} /api/v2/guild/applyment/renew/revoke 公会撤回续约主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "已撤回续约邀请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyRenewRevoke(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeGuildRenew)
	if err != nil {
		return nil, err
	}

	if _, err = param.checkApplyment(contractapplyment.TypeGuildRenew); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusRevoked,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}
	return "已撤回续约邀请", nil
}

// ActionApplyTerminatePass 公会通过主播协商解约申请
/**
 * @api {post} /api/v2/guild/applyment/terminate/pass 公会通过主播协商解约申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "解约成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyTerminatePass(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveTerminate)
	if err != nil {
		return nil, err
	}

	ca, err := param.checkApplyment(contractapplyment.TypeLiveTerminate)
	if err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	now := goutil.TimeNow()
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		update := map[string]interface{}{
			"status":               contractapplyment.StatusAgreed,
			"contract_expire_time": now.Unix(),
			"process_time":         now.Unix(),
			"modified_time":        now.Unix(),
		}
		err := param.updateContract(tx, update)
		if err != nil {
			return err
		}
		// 将其他待处理的合同（比如系统自动发送的续约合同，公会清退申请）变为失效
		update["status"] = contractapplyment.StatusInvalid
		err = tx.Table(contractapplyment.TableName()).
			Where("live_id = ? AND status = ?", param.LiveID, contractapplyment.StatusPending).
			Not("id = ?", param.ID).Updates(update).Error
		if err != nil {
			return actionerrors.ErrServerInternal.New(err, nil)
		}
		// 更新合同
		update = map[string]interface{}{
			"status":        livecontract.StatusUseless,
			"contract_end":  now.Unix(),
			"modified_time": now.Unix(),
		}
		db := tx.Table(livecontract.TableName()).
			Where("id = ? AND guild_id = ? AND live_id = ? AND status = ?",
				ca.ContractID, ca.GuildID, ca.LiveID,
				livecontract.StatusContracting).Updates(update)
		if db.Error != nil {
			return actionerrors.ErrServerInternal.New(db.Error, nil)
		}
		if db.RowsAffected == 0 {
			return actionerrors.ErrContractNotExist
		}
		// 更新主播数冗余字段
		err = guild.IncreaseLiveNum(param.guild.ID, -1, tx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		// TODO: 迁移到同一个库后使用 tx
		if err = guildagent.Unassign(ca.LiveID, ca.GuildID); err != nil {
			return actionerrors.ErrServerInternal.New(err, nil)
		}
		err = guildscheduleapply.AfterQuitGuild(ca.GuildID, []int64{ca.LiveID})
		if err != nil {
			logger.WithFields(logger.Fields{
				"guild_id":   ca.GuildID,
				"creator_id": ca.LiveID,
			}).Error(err)
			// PASS
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的协商解约申请已通过",
		fmt.Sprintf(
			"直播公会 %s 已通过您的协商解约申请。您于%s与该直播公会成功解约。",
			param.guild.Name, now.Format(" 2006 年 01 月 02 日")),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "解约成功", nil
}

// ActionApplyTerminateRefuse 公会拒绝主播协商解约申请
/**
 * @api {post} /api/v2/guild/applyment/terminate/refuse 公会拒绝主播续约申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "拒绝成功"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyTerminateRefuse(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeLiveTerminate)
	if err != nil {
		return nil, err
	}

	if _, err := param.checkApplyment(contractapplyment.TypeLiveTerminate); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusDeclined,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}
	err = messageassign.SystemMessageAssign(param.LiveID, "您的协商解约申请未通过",
		fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 拒绝了您的协商解约申请。", param.guild.Name),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "拒绝成功", nil
}

// ActionApplyTerminateRevoke 公会撤回清退主播申请
/**
 * @api {post} /api/v2/guild/applyment/terminate/revoke 公会撤回续约主播申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} id 申请 id
 * @apiParam {Number} live_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "已撤回清退申请"
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (400) {Number} code 500050006
 * @apiError (400) {String} info 该申请状态异常，请确认后处理
 */
func ActionApplyTerminateRevoke(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGuildOperateLiveApplyParam(c, contractapplyment.TypeGuildExpel)
	if err != nil {
		return nil, err
	}

	if _, err := param.checkApplyment(contractapplyment.TypeGuildExpel); err != nil {
		return nil, err
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	nowUnix := goutil.TimeNow().Unix()
	update := map[string]interface{}{
		"status":        contractapplyment.StatusRevoked,
		"process_time":  nowUnix,
		"modified_time": nowUnix,
	}
	err = param.updateContract(service.DB, update)
	if err != nil {
		return nil, err
	}
	return "已撤回清退申请", nil
}
