package guild

/*
func TestActionPassjoin(t *testing.T) {
	t.<PERSON><PERSON>() // 公会二期上线后该接口会弃用
	setup()
	assert := assert.New(t)
	require := require.New(t)

	c := livecontract.LiveContract{
		ID:          157,
		GuildID:     3,
		ContractEnd: 1999999999,
		// Status 只有在创建表时会被使用，更新时因为该属性等于默认值所以会被忽略
		Status:     livecontract.StatusUntreated,
		Type:       livecontract.FromLiver,
		GuildOwner: 12,
		GuildName:  "测试公会拒绝主播的加入申请",
	}
	err := service.DB.Assign(handler.M{
		// 强制更新 status 属性
		"status": livecontract.StatusUntreated,
	}).Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	params := map[string]int64{"contract_id": 157}

	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/passjoin", paramToRequestBody(params))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	r, err := ActionPassjoin(ctx)
	assert.NoError(err)
	assert.True(r.(bool))
}
*/
