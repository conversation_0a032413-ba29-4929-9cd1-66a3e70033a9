package guild

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type myGuildResp struct {
	ApplymentID int64 `json:"applyment_id"`

	GuildID   int64  `gorm:"column:guild_id" json:"guild_id"`
	GuildName string `gorm:"column:guild_name" json:"guild_name"`
	UserID    int64  `gorm:"column:user_id" json:"user_id"`

	ContractID       int64  `gorm:"column:contract_id" json:"-"`
	ContractDuration int64  `gorm:"column:contract_duration" json:"-"`
	Duration         string `json:"duration"`
	ContractStart    int64  `gorm:"column:contract_start" json:"contract_start"`
	ContractEnd      int64  `gorm:"column:contract_end" json:"contract_end"`
	ContractLeftTime string `json:"contract_left_time"`

	Type       int64 `json:"type"`
	ExpireTime int64 `gorm:"column:expire_time" json:"expire_time"`

	Rate        int    `gorm:"column:rate" json:"-"`
	RateStr     string `json:"rate_string"`
	Settlement  string `json:"settlement"`
	ApplyTime   int64  `gorm:"column:apply_time" json:"apply_time"`
	IsRevokable bool   `json:"is_revokable"`
	IsRenewable bool   `json:"is_renewable"`
}

// ActionMyContract 获取主播所属公会合约
/**
 * @api {get} /api/v2/guild/mycontract 获取主播所属公会合约
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response: 签约申请中 type=1
 *   {
 *     "code": 0,
 *     "info": {
 *       "contract_id": 61,  // 合约 ID
 *       "applyment_id": 6,  // 合约申请 ID
 *       "guild_id": 4,  // 公会 ID
 *       "guild_name": "测试公会",  // 公会名称
 *       "user_id": 425603,  // 会长 ID
 *       "type": 1,  // 合约类型（0 签约中，1 签约申请中，3 续约申请中，5 解约申请中）
 *       "duration": "12 个月",  // 签约时限
 *       "contract_start": 0,  // 合约开始时间
 *       "contract_end": 0,  // 合约结束时间
 *       "contract_left_time": "",  // 合约剩余时间
 *       "apply_time": 1560141304,  // 申请时间
 *       "expire_time": 1576116752,  // 申请过期时间
 *       "rate_string": "45%",
 *       "settlement": "对公结算",
 *       "is_revokable": true,  // 可否撤回
 *       "is_renewable": false  // 可否续约
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response: 签约中 type=0
 *   {
 *     "code": 0,
 *     "info": {
 *       "contract_id": 61,
 *       "applyment_id": 0,
 *       "guild_id": 4,
 *       "guild_name": "测试公会",
 *       "user_id": 425603,
 *       "duration": "12 个月",
 *       "type": 0,
 *       "contract_start": 1560141304,
 *       "contract_end": 1660541304,
 *       "contract_left_time": "15 天",
 *       "apply_time": 0,
 *       "expire_time": 0,
 *       "rate_string": "45%",
 *       "settlement": "对公结算",
 *       "is_revokable": false,
 *       "is_renewable": true
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response: 续约申请中 type=3
 *   {
 *     "code": 0,
 *     "info": {
 *       "contract_id": 61,
 *       "applyment_id": 6,
 *       "guild_id": 4,
 *       "guild_name": "测试公会",
 *       "user_id": 425603,
 *       "duration": "12 个月",
 *       "type": 3,
 *       "contract_start": 1560141304,
 *       "contract_end": 1660541304,
 *       "contract_left_time": "15 天",
 *       "apply_time": 1560141304,
 *       "expire_time": 1576116752,
 *       "rate_string": "45%",
 *       "settlement": "对公结算",
 *       "is_revokable": false,
 *       "is_renewable": false
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response: 解约申请中 type=5
 *   {
 *     "code": 0,
 *     "info": {
 *       "contract_id": 61,
 *       "applyment_id": 6,
 *       "guild_id": 4,
 *       "guild_name": "音熊联萌",
 *       "user_id": 425603,
 *       "duration": "12 个月",
 *       "type": 1,
 *       "contract_start": 1560141304,
 *       "contract_end": 1660541304,
 *       "contract_left_time": "15 天",
 *       "apply_time": 1560141304,
 *       "expire_time": 1576116752,
 *       "rate_string": "45%",
 *       "settlement": "对公结算",
 *       "is_revokable": false,
 *       "is_renewable": false
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": null
 *   }
 */
func ActionMyContract(c *handler.Context) (handler.ActionResponse, error) {
	nowStamp := goutil.TimeNow().Unix()
	// 查询现有合约
	var contract myGuildResp
	err := service.DB.Table(livecontract.TableName()+" AS l").
		Select("l.id AS contract_id, g.user_id, g.id AS guild_id, g.name AS guild_name, l.contract_start, l.contract_end, l.contract_duration, l.rate").
		Where("l.status = ? AND l.live_id = ?", livecontract.StatusContracting, c.UserID()).
		Where("l.contract_end > ?", nowStamp).
		Joins(fmt.Sprintf("LEFT JOIN %s AS g ON g.id = l.guild_id", guild.TableName())).
		Scan(&contract).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	// 查询现有合约申请
	var applyment myGuildResp
	err = service.DB.Table(contractapplyment.TableName()+" AS c").
		Select("c.id AS applyment_id, c.type, g.user_id, g.name AS guild_name, c.create_time AS apply_time, c.contract_duration, c.expire_time, c.rate").
		Where("c.type IN (?)", []int64{contractapplyment.TypeLiveSign, contractapplyment.TypeLiveRenew, contractapplyment.TypeLiveTerminate}).
		Where("c.status = ? AND c.live_id = ? AND c.initiator = ? AND c.expire_time > ?",
			contractapplyment.StatusPending, c.UserID(), contractapplyment.InitiatorLive, nowStamp).
		Joins(fmt.Sprintf("LEFT JOIN %s AS g ON g.id = c.guild_id", guild.TableName())).
		Scan(&applyment).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}

	if contract.ContractID > 0 {
		contract.Settlement = contractapplyment.SettlementDefault
		contract.RateStr = guildrate.FormatRateToString(contract.Rate)
		if applyment.ApplymentID > 0 {
			contract.ApplyTime = applyment.ApplyTime
			contract.ApplymentID = applyment.ApplymentID
			contract.ExpireTime = applyment.ExpireTime
			contract.Type = applyment.Type
			contract.ContractDuration = applyment.ContractDuration
			contract.Rate = applyment.Rate
			contract.IsRevokable = contractapplyment.IsTimeToRevoke(applyment.Type, applyment.ApplyTime, applyment.ExpireTime)
		}

		leftDuration := time.Until(time.Unix(contract.ContractEnd, 0))
		if leftHours := leftDuration.Hours(); leftHours < 24*15 && leftHours > 0 {
			contract.ContractLeftTime = contractapplyment.GetLeftTimeString(leftDuration)
		}
		if contract.ContractLeftTime != "" && applyment.ApplymentID == 0 {
			// 是否续约申请被拒绝过
			contract.IsRenewable = true
			var declinedApplyment contractapplyment.ContractApplyment
			err = service.DB.Table(contractapplyment.TableName()).
				Select("id").
				Where("live_id = ? AND guild_id = ? AND status = ? AND type = ?",
					c.UserID(), contract.GuildID, contractapplyment.StatusDeclined, contractapplyment.TypeLiveRenew).
				Where("create_time > ?", contract.ContractStart).
				Scan(&declinedApplyment).Error
			if err != nil && !gorm.IsRecordNotFoundError(err) {
				return nil, actionerrors.ErrServerInternal.New(err, nil)
			}
			if declinedApplyment.ID > 0 {
				contract.IsRenewable = false
			}
		}
		contract.Duration = contractapplyment.DurationLabelMap[contract.ContractDuration]
		return contract, nil
	} else if applyment.ApplymentID > 0 {
		applyment.Settlement = contractapplyment.SettlementDefault
		applyment.RateStr = guildrate.FormatRateToString(applyment.Rate)
		applyment.IsRevokable = contractapplyment.IsTimeToRevoke(applyment.Type, applyment.ApplyTime, applyment.ExpireTime)
		applyment.Duration = contractapplyment.DurationLabelMap[applyment.ContractDuration]

		return applyment, nil
	}

	return nil, nil
}
