package guild

import (
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildIncomeV3(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	g := guild.Guild{
		ID:           0,
		Name:         "测试公会-TestGuildIncomeV3Param-1",
		Checked:      guild.CheckedPass,
		UserID:       88889999,
		ApplyTime:    goutil.TimeNow().Unix(),
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
	}
	require.NoError(g.DB().Create(&g).Error)
	defer func() {
		g.DB().Delete("", "id = ?", g.ID)
	}()

	ac := guildagent.AgentCreator{
		ID:           0,
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
		DeleteTime:   0,
		GuildID:      g.ID,
		AgentID:      6,
		CreatorID:    5,
	}
	require.NoError(ac.DB().Create(&ac).Error)
	defer func() {
		ac.DB().Delete("", "id = ?", ac.ID)
	}()

	contractList := []*livecontract.LiveContract{
		{
			LiveID: 1,
		},
		{
			LiveID: 2,
		},
		{
			LiveID: 3,
		},
		{
			LiveID: 4,
		},
		{
			LiveID: 5,
		},
	}
	contractIDs := make([]int64, len(contractList))
	for _, contract := range contractList {
		contract.GuildID = g.ID
		contract.GuildOwner = g.UserID
		contract.GuildName = g.Name
		contract.ContractDuration = 30 * 24 * 60 * 60
		contract.ContractStart = time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local).Unix()
		contract.ContractEnd = goutil.TimeNow().Add(time.Hour).Unix()
		contract.Rate = 45
		contract.KPI = ""
		contract.Type = 0
		contract.Attr = 0
		contract.Status = livecontract.StatusContracting
		contract.CreateTime = goutil.TimeNow().Unix()
		contract.ModifiedTime = goutil.TimeNow().Unix()
		require.NoError(livecontract.ADB().Create(&contract).Error)
		contractIDs = append(contractIDs, contract.ID)
	}
	defer func() {
		livecontract.ADB().Delete("", "id IN (?)", contractIDs)
	}()

	tradelogList := []*transactionlog.TransactionLog{
		{
			ToID:    1,
			GiftID:  1,
			Title:   "公会主播普通礼物",
			IOSCoin: 100,
			AllCoin: 100,
			Income:  10,
			Tax:     5,
			Attr:    transactionlog.AttrCommon,
		},
		{
			ToID:    2,
			GiftID:  0,
			Title:   "公会主播付费问答",
			IOSCoin: 500,
			AllCoin: 500,
			Income:  50,
			Tax:     30,
			Attr:    transactionlog.AttrCommon,
		},
		{
			ToID:    3,
			GiftID:  1,
			Title:   "公会主播付费弹幕",
			IOSCoin: 800,
			AllCoin: 800,
			Income:  80,
			Tax:     40,
			Attr:    transactionlog.AttrLiveDanmaku,
		},
		{
			ToID:    4,
			GiftID:  666,
			Title:   "公会主播贵族开通",
			IOSCoin: 1000,
			AllCoin: 1000,
			Income:  100,
			Tax:     50,
			Attr:    transactionlog.AttrLiveRegisterNoble,
		},
		{
			ToID:    5,
			GiftID:  999,
			Title:   "公会主播超粉开通",
			IOSCoin: 2000,
			AllCoin: 2000,
			Income:  200,
			Tax:     100,
			Attr:    transactionlog.AttrLiveRegisterSuperFan,
		},
	}
	tradeIDs := make([]int64, len(tradelogList))
	for _, tradelog := range tradelogList {
		tradelog.SubordersNum = g.ID
		tradelog.Type = transactionlog.TypeGuildLive
		tradelog.FromID = 88888888
		tradelog.Status = transactionlog.StatusSuccess
		tradelog.Num = 1
		tradelog.Rate = 0.5
		tradelog.CTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.CreateTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.ModifiedTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.ConfirmTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()

		require.NoError(transactionlog.ADB().Create(&tradelog).Error)
		tradeIDs = append(tradeIDs, tradelog.ID)
	}
	defer func() {
		transactionlog.ADB().Delete("", "id IN (?)", tradeIDs)
	}()

	values := url.Values{}
	values.Add("start_date", "2024-02-15")
	values.Add("end_date", "2024-02-20")
	values.Add("p", "1")
	values.Add("pagesize", "15")
	values.Add("sort", "play_income.desc")

	ctx := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/guild/guildincome-v3?"+values.Encode()),
		true,
		nil)
	ctx.User().ID = g.UserID

	resp, err := ActionGuildIncomeV3(ctx)
	require.NoError(err)
	result, ok := resp.(*guildIncomeV3Resp)
	require.True(ok)

	assert.Equal(util.Float2DP(5), result.Menu.GiftIncome)
	assert.Equal(util.Float2DP(60), result.Menu.PlayIncome)
	assert.Equal(util.Float2DP(50), result.Menu.NobleIncome)
	assert.Equal(util.Float2DP(100), result.Menu.SuperFanIncome)

	require.Len(result.Data, 5)
	for i, item := range result.Data {
		if i == 0 {
			assert.Equal(util.Float2DP(40), item.PlayIncome)
		} else if i == 1 {
			assert.Equal(util.Float2DP(20), item.PlayIncome)
		} else {
			assert.Equal(util.Float2DP(0), item.PlayIncome)
		}
		switch item.CreatorID {
		case 1:
			assert.Equal(util.Float2DP(5), item.GiftIncome)
			assert.Equal(util.Float2DP(0), item.PlayIncome)
			assert.Equal(util.Float2DP(0), item.NobleIncome)
			assert.Equal(util.Float2DP(0), item.SuperFanIncome)
		case 2:
			assert.Equal(util.Float2DP(0), item.GiftIncome)
			assert.Equal(util.Float2DP(20), item.PlayIncome)
			assert.Equal(util.Float2DP(0), item.NobleIncome)
			assert.Equal(util.Float2DP(0), item.SuperFanIncome)
		case 3:
			assert.Equal(util.Float2DP(0), item.GiftIncome)
			assert.Equal(util.Float2DP(40), item.PlayIncome)
			assert.Equal(util.Float2DP(0), item.NobleIncome)
			assert.Equal(util.Float2DP(0), item.SuperFanIncome)
		case 4:
			assert.Equal(util.Float2DP(0), item.GiftIncome)
			assert.Equal(util.Float2DP(0), item.PlayIncome)
			assert.Equal(util.Float2DP(50), item.NobleIncome)
			assert.Equal(util.Float2DP(0), item.SuperFanIncome)
		case 5:
			assert.Equal(util.Float2DP(0), item.GiftIncome)
			assert.Equal(util.Float2DP(0), item.PlayIncome)
			assert.Equal(util.Float2DP(0), item.NobleIncome)
			assert.Equal(util.Float2DP(100), item.SuperFanIncome)
		}
	}
}

func TestSetIncomeV3Fields(t *testing.T) {
	assert := assert.New(t)

	db := setIncomeV3Fields(transactionlog.DB(), "to_id")
	subQuery := db.SubQuery()

	v := reflect.ValueOf(subQuery).Elem()
	fieldsExpr := v.FieldByName("expr").String()

	assert.Contains(fieldsExpr, "gift_income")
	assert.Contains(fieldsExpr, "noble_income")
	assert.Contains(fieldsExpr, "superfan_income")
	assert.Contains(fieldsExpr, "play_income")
	assert.Contains(fieldsExpr, "to_id")
}

func TestGuildIncomeV3Param_load(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	g := guild.Guild{
		Name:         "测试公会-TestGuildIncomeV3Param-2",
		Checked:      guild.CheckedPass,
		UserID:       888999,
		ApplyTime:    goutil.TimeNow().Unix(),
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
	}
	require.NoError(g.DB().Create(&g).Error)
	defer func() {
		g.DB().Delete("", "id = ?", g.ID)
	}()

	ac := guildagent.AgentCreator{
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
		GuildID:      g.ID,
		AgentID:      777999,
		CreatorID:    666888,
	}
	require.NoError(ac.DB().Create(&ac).Error)
	defer func() {
		ac.DB().Delete("", "id = ?", ac.ID)
	}()

	values := url.Values{}
	values.Add("start_date", "2024-02-15")
	values.Add("end_date", "2024-02-20")
	values.Add("p", "2")
	values.Add("pagesize", "15")
	values.Add("sort", "play_income.desc")
	values.Add("search_creator", "mmm")
	values.Add("search_agent", "vvv")

	ctx := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/guild/liveincomelist-v3?"+values.Encode()),
		true,
		nil)
	ctx.User().ID = g.UserID

	param := &guildIncomeV3Param{}
	err := param.load(ctx)
	require.NoError(err)
	assert.Equal(int64(2), param.page)
	assert.Equal(int64(15), param.pageSize)
	assert.Equal("play_income DESC", param.sort)
	assert.Equal("mmm", param.searchCreator.Word)
	assert.Equal("vvv", param.searchAgent.Word)
	assert.Equal("2024-02-15 00:00:00", param.startDate.Format(goutil.TimeFormatHMS))
	assert.Equal("2024-02-20 00:00:00", param.endDate.Format(goutil.TimeFormatHMS))
	assert.Equal(g.ID, param.guildID)
	assert.True(param.role.IsGuildOwner())
	require.Len(param.agentCreators, 1)
	assert.Equal(ac.AgentID, param.agentCreators[0].AgentID)
	assert.Equal(ac.CreatorID, param.agentCreators[0].CreatorID)
}

func TestGuildIncomeV3Param_revenueSubquery(t *testing.T) {
	assert := assert.New(t)

	param := &guildIncomeV3Param{
		guildID:   999,
		startDate: goutil.TimeNow(),
		endDate:   goutil.TimeNow().Add(time.Hour),
	}

	expr := reflect.ValueOf(param.revenueSubquery()).Elem().FieldByName("expr").String()
	assert.Regexp(`WHERE \(\(status = 1 AND type = 9 AND attr IN \(.*\) AND suborders_num = 999\)\) AND \(confirm_time BETWEEN \? AND \?\) GROUP BY to_id\)`, expr)
}

func TestGuildIncomeV2Resp_buildEmptyList(t *testing.T) {
	assert := assert.New(t)

	param := &guildIncomeV3Param{
		page:     1,
		pageSize: 15,
	}

	resp := guildIncomeV3Resp{}
	resp.buildEmptyList(param)

	assert.Len(resp.Data, 0)
	assert.Equal(int64(1), resp.Pagination.P)
	assert.Equal(int64(15), resp.Pagination.PageSize)
	assert.Equal(int64(0), resp.Pagination.Count)
}

func TestGuildIncomeV2Resp_fillUserInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	role := guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	param := &guildIncomeV3Param{
		role: role,
		agentCreators: []*guildagent.AgentCreator{
			{
				CreatorID: 3,
				AgentID:   4,
			},
			{
				CreatorID: 2,
				AgentID:   5,
			},
		},
	}

	resp := guildIncomeV3Resp{
		Data: []creatorIncomeItem{
			{
				CreatorID: 1,
				AgentID:   0,
			},
			{
				CreatorID: 2,
				AgentID:   5,
			},
			{
				CreatorID: 3,
				AgentID:   4,
			},
		},
	}
	require.NoError(resp.fillUserInfo(param, []int64{1, 2, 3}))

	for _, item := range resp.Data {
		assert.NotEmpty(item.CreatorUsername)
		if item.AgentID > 0 {
			assert.NotEmpty(item.AgentUsername)
		}
	}
}
