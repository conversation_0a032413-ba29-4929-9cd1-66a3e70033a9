package guild

import (
	"fmt"
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/livedb/reportinactiveanchor"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const fifteenDaySec = 15 * 24 * 3600

type memberElem struct {
	LiveID           int64  `json:"live_id"`
	LiveUsername     string `json:"live_username"`
	LiveIconURL      string `json:"live_iconurl"`
	Period           string `json:"period"`
	ContractStart    int64  `json:"contract_start"`
	ContractEnd      int64  `json:"contract_end"`
	Settlement       string `json:"settlement"`
	Rate             int    `json:"rate"`
	EditRate         int    `json:"edit_rate,omitempty"`
	RatePending      bool   `json:"rate_pending"`
	RenewApplymentID int64  `json:"renew_applyment_id,omitempty"`

	AgentID       int64  `json:"agent_id"`
	AgentUsername string `json:"agent_username"`
	AgentIconURL  string `json:"agent_iconurl"`

	Vitality int   `json:"vitality"`
	RoomID   int64 `json:"room_id"`
	FansNum  int64 `json:"fans_num"`

	TotalRevenue util.Float2DP `json:"total_revenue"`
	LiveDays     int64         `json:"live_days"`
	LastLiveDate string        `json:"last_live_date"`

	ExclusiveCreator     bool  `json:"exclusive_creator,omitempty"`      // 是否是三方独家主播
	ExclusiveContractEnd int64 `json:"exclusive_contract_end,omitempty"` // 三方独家合约到期时间（单位：秒）
}

type memberListResp struct {
	Data       []*memberElem     `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
	Total      *int64            `json:"total,omitempty"`

	NoAgentNum          *int `json:"no_agent_num,omitempty"`
	ExclusiveCreatorNum *int `json:"exclusive_creator_num,omitempty"`
}

var memberListSortMapSupported = map[string]bool{
	"live_id":             true,
	"live_id.asc":         true,
	"live_id.desc":        true,
	"contract_start":      true,
	"contract_start.asc":  true,
	"contract_start.desc": true,
	"contract_end":        true,
	"contract_end.asc":    true,
	"contract_end.desc":   true,
}

// ActionMemberList 获取公会主播列表
/**
 * @api {get} /api/v2/guild/member/list 公会获取主播列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 * @apiParam {Number} [live_id] 主播 ID
 * @apiParam {String} [live_username] 主播昵称
 * @apiParam {String} [sort=contract_end.asc] 排序
 * @apiParam {String} [search_word] 搜索 ID 或昵称
 * @apiParam {number=0,1,2,3} [search_type=0] 搜索类型（0 按主播搜索，1 按主播所属经纪人搜索，2 仅看无经纪人的主播，3 仅看三方独家主播）
 * @apiParam {number=0,1} [live_type=0] 是否断播（0 获取全部主播，1 获取断播主播 60 天内直播过且最近 10 天未直播的主播）
 * @apiParam {number=0,1} [recommend_type=0] 推荐类型（0 获取全部主播，1 隐藏不符合条件的主播），可能会一页返回条数不足，前端需要自动获取下一页
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "Datas": [{
 *           "live_id": 123,
 *           "live_username": "用户名",
 *           "live_iconurl": "用户头像",
 *           "period": "12 个月",
 *           "contract_start": 1234567890, // 合约开始时间（单位：秒）
 *           "contract_end": 1534567890, // 合约到期时间（单位：秒）
 *           "settlement": "对公结算",
 *           "rate": 45,
 *           "edit_rate": 40, // 公会申请修改主播的分成比例，后续移除
 *           "rate_pending": true, // 主播是否存在还未处理的降薪申请
 *           "renew_applyment_id": 888, // 续约申请 id，在有待处理的续约申请时有此字段
 *           "agent_id": 245,
 *           "agent_username": "经纪人昵称",
 *           "agent_iconurl": "http://foo.com/bar.png",
 *           "vitality": 10, // 元气值
 *           "room_id": 10, // 房间号
 *           "fansnum": 200, // 粉丝数
 *           "total_revenue": 10.02, // 收益（单位：元）
 *           "live_days": 10, // 直播天数
 *           "last_live_date": "2021-02-01", // 最后直播日期
 *           "exclusive_creator": true, // 是否是三方独家主播
 *           "exclusive_contract_end": 1534567890 // 三方独家合约到期时间（单位：秒）
 *         }],
 *         "total": 100, // 在没有筛选条件且 p == 1 时返回
 *         "no_agent_num": 7, // 没有经纪人的主播数（只有公会长会返回此字段）
 *         "exclusive_creator_num": 2, // 三方独家主播数（只有公会长会返回此字段）
 *         "pagination": {
 *           "count": 10,
 *           "p": 10,
 *           "pagesize": 1,
 *           "maxpage": 10
 *         }
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMemberList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newMemberListParam(c)
	if err != nil {
		return nil, err
	}

	db := livecontract.LiveContract{}.DB().
		Select("l.guild_owner, l.live_id, l.contract_duration, l.contract_start, l.contract_end, l.rate, l.attr").
		Table(livecontract.TableName()+" AS l").
		Where("l.guild_id = ?", param.guildID).
		Where("l.status = ? AND l.contract_end > ?", livecontract.StatusContracting, goutil.TimeNow().Unix())

	if param.liveType == anchorInactive {
		db, err = param.addInactiveAnchorCondition(db)
		if err != nil {
			return nil, err
		}
		if db == nil {
			return &param.memberListResp, nil
		}
	}

	if err = param.findNoAgentNum(db); err != nil {
		return nil, err
	}
	if err = param.findExclusiveCreator(); err != nil {
		return nil, err
	}
	if err = param.findContracts(db); err != nil {
		return nil, err
	}
	if err = param.findApplicationRates(); err != nil {
		return nil, err
	}
	param.findRenews()
	return param.buildResp()
}

// 主播类型：0 全部，1 断播主播（获取断播主播 60 天内直播过且最近 10 天未直播的主播）
const (
	anchorInactive = iota + 1
)

func (param *memberListParam) addInactiveAnchorCondition(db *gorm.DB) (*gorm.DB, error) {
	var userIDs []int64
	db.Pluck("live_id", &userIDs)
	if len(userIDs) == 0 {
		return nil, nil
	}
	var err error
	param.inactiveAnchors, err = reportinactiveanchor.FindInactiveAnchorMap(userIDs, param.guildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.inactiveAnchors) == 0 {
		return nil, nil
	}

	liveIDs := make([]int64, 0, len(param.inactiveAnchors))
	for k := range param.inactiveAnchors {
		liveIDs = append(liveIDs, k)
	}
	return db.Where("l.live_id IN (?)", liveIDs), nil
}

// 搜索类型：按主播搜索，按主播所属经纪人搜索，搜索无经纪人的主播，搜索三方独家主播
const (
	searchTypeLive = iota
	searchTypeAgent
	searchNoAgentLive
	searchExclusiveCreator
)

type memberListParam struct {
	p            int64
	pageSize     int64
	liveID       int64
	liveUsername string
	sort         struct {
		orderByField string
		order        string
	}

	searchType          int
	searchWord          string
	isSearchWordInteger bool
	searchWordInteger   int64

	role guildrole.GuildRole
	user *user.User

	guildID             int64
	contracts           []*livecontract.LiveContract
	rateMap             map[int64]int                                  // live_id 作为键
	renewApplyments     map[int64]*contractapplyment.ContractApplyment // live_id 作为键
	agentCreators       agentCreators
	exclusiveCreatorIDs []int64

	liveType        int
	inactiveAnchors map[int64]*reportinactiveanchor.InactiveAnchor // live_id 作为键

	memberListResp
}

func (param *memberListParam) findApplicationRates() error {
	if len(param.contracts) <= 0 || !param.role.IsGuildManager() {
		return nil
	}

	var liveIDs []int64
	for _, ca := range param.contracts {
		if ca.Attr.IsSet(livecontract.AttrBitMaskLiveGuildRate) {
			liveIDs = append(liveIDs, ca.LiveID)
		}
	}
	if len(liveIDs) <= 0 {
		return nil
	}

	// 查找最近一次的降薪申请
	var applications []*contractapplyment.ContractApplyment
	if err := service.DB.Select("live_id, rate").Where("guild_id = ? AND live_id IN (?)", param.guildID, liveIDs).
		Where("status = ? AND type = ? AND expire_time > ?",
			contractapplyment.StatusPending, contractapplyment.TypeRateDown, goutil.TimeNow().Unix()).
		Find(&applications).Error; err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	param.rateMap = make(map[int64]int, len(param.contracts))
	for _, application := range applications {
		param.rateMap[application.LiveID] = application.Rate
	}
	return nil
}

type agentCreators []*guildagent.AgentCreator

func (creators agentCreators) agentCreatorIDs(userID int64, role guildrole.GuildRole, agentIDs ...int64) (creatorIDs []int64) {
	creatorIDs = make([]int64, 0, len(creators))
	if len(agentIDs) == 0 {
		isGuildOwner := role.IsGuildOwner()
		for _, item := range creators {
			if isGuildOwner || item.AgentID == userID {
				creatorIDs = append(creatorIDs, item.CreatorID)
			}
		}
	} else {
		for _, item := range creators {
			if goutil.HasElem(agentIDs, item.AgentID) {
				creatorIDs = append(creatorIDs, item.CreatorID)
			}
		}
	}

	return
}

func (creators agentCreators) allAgentIDs() (agentIDs []int64) {
	agentIDs = make([]int64, 0, len(creators))
	for _, item := range creators {
		agentIDs = append(agentIDs, item.AgentID)
	}

	return util.Uniq(agentIDs)
}

func (creators agentCreators) creatorAgentMap(creatorIDs []int64) (creatorIDAgentIDMap map[int64]int64) {
	creatorIDAgentIDMap = make(map[int64]int64, len(creators))
	for _, item := range creators {
		creatorIDAgentIDMap[item.CreatorID] = item.AgentID
	}
	return
}

func (param *memberListParam) orderExpression() string {
	// 增加 guild_live_contract.id ASC 排序来解决单一排序字段在值相同时的乱序问题
	return fmt.Sprintf("%s %s, l.id ASC", param.sort.orderByField, param.sort.order)
}

func newMemberListParam(c *handler.Context) (*memberListParam, error) {
	param := &memberListParam{}
	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}
	param.user, param.role, param.guildID = c.User(), role, guildInfo.ID
	if param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.p, param.pageSize, err = c.GetParamPage(); err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.liveID, _ = c.GetParamInt64("live_id"); param.liveID < 0 {
		return nil, actionerrors.ErrParams
	}
	param.liveUsername, _ = c.GetParam("live_username")
	sortStr := c.GetDefaultParamString("sort", "contract_end.asc")
	var ok bool
	if param.sort.orderByField, param.sort.order, ok = utils.ParseSortStr(sortStr, memberListSortMapSupported); !ok {
		return nil, actionerrors.ErrParams
	}
	param.searchType, _ = c.GetDefaultParamInt("search_type", -1)
	param.searchWord, _ = c.GetParam("search_word")
	if param.searchType == searchTypeAgent || param.searchType == searchTypeLive {
		if param.searchWord == "" {
			return nil, actionerrors.ErrParams
		}
		param.searchWordInteger, err = strconv.ParseInt(param.searchWord, 10, 64)
		if err == nil {
			param.isSearchWordInteger = true
		}
	}

	param.liveType, err = c.GetDefaultParamInt("live_type", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	param.contracts = make([]*livecontract.LiveContract, 0, param.pageSize)
	param.Data = make([]*memberElem, 0, param.pageSize)
	return param, nil
}

func (param *memberListParam) findNoAgentNum(db *gorm.DB) (err error) {
	if !param.role.IsGuildOwner() {
		return
	}

	allAgentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
	var noAgentNum int
	if len(allAgentCreatorIDs) == 0 {
		if err = db.Count(&noAgentNum).Error; err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	} else {
		if err = db.Where("l.live_id NOT IN (?)", allAgentCreatorIDs).Count(&noAgentNum).Error; err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	param.NoAgentNum = &noAgentNum

	return
}

func (param *memberListParam) findExclusiveCreator() error {
	if !param.role.IsGuildOwner() {
		return nil
	}

	creatorIDs, err := exclusivecreator.FindCreatorIDsByGuildID(param.guildID)
	if err != nil {
		return err
	}
	param.exclusiveCreatorIDs = creatorIDs
	num := len(creatorIDs)
	param.ExclusiveCreatorNum = &num

	return nil
}

// findContracts 查找公会主播的合同
func (param *memberListParam) findContracts(db *gorm.DB) (err error) {
	db, err = param.applyAgentFilter(db)
	if err != nil || db == nil {
		return err
	}
	db, err = param.applySearchFilter(db)
	if err != nil || db == nil {
		return err
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	if !param.Pagination.Valid() {
		return nil
	}
	db = db.Order(param.orderExpression())
	err = param.Pagination.ApplyTo(db).Find(&param.contracts).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *memberListParam) applyAgentFilter(db *gorm.DB) (*gorm.DB, error) {
	if !param.role.IsGuildOwner() {
		creatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(creatorIDs) == 0 {
			return nil, nil
		}
		db = db.Where("l.live_id IN (?)", creatorIDs)
	}

	return db, nil
}

func (param *memberListParam) applySearchFilter(db *gorm.DB) (*gorm.DB, error) {
	var isSearchByLiveUsername bool
	typeLiveSearchFilter := func() {
		if param.isSearchWordInteger {
			db = db.Where("l.live_id = ?", param.searchWordInteger)
		} else {
			isSearchByLiveUsername = true
			db = db.Joins(fmt.Sprintf("JOIN %s AS u ON l.live_id = u.id", mowangskuser.TableName())).
				Where("u.username LIKE ?", servicedb.ToLikeStr(param.searchWord))
		}
	}

	if param.role.IsGuildOwner() {
		switch param.searchType {
		case searchTypeLive:
			typeLiveSearchFilter()
		case searchTypeAgent:
			var creatorIDs []int64
			if param.isSearchWordInteger {
				creatorIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, param.searchWordInteger)
			} else {
				agentIDs := param.agentCreators.allAgentIDs()
				if len(agentIDs) == 0 {
					return nil, nil
				}
				agentIDsFounded, err := mowangskuser.SearchUserByUsername(param.searchWord, agentIDs)
				if err != nil {
					return nil, actionerrors.NewErrServerInternal(err, nil)
				}
				if len(agentIDsFounded) == 0 {
					return nil, nil
				}
				creatorIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDsFounded...)
			}
			if len(creatorIDs) == 0 {
				return nil, nil
			}
			db = db.Where("l.live_id IN (?)", creatorIDs)
		case searchNoAgentLive:
			allAgentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
			if len(allAgentCreatorIDs) != 0 {
				db = db.Where("l.live_id NOT IN (?)", allAgentCreatorIDs)
			}
		case searchExclusiveCreator:
			if len(param.exclusiveCreatorIDs) == 0 {
				// 若没有三方独家主播则不返回数据
				return nil, nil
			}
			db = db.Where("l.live_id IN (?)", param.exclusiveCreatorIDs)
		}
	} else if param.role.IsGuildAgent() && param.searchType == searchTypeLive {
		// 支持经纪人按关键词搜索主播
		typeLiveSearchFilter()
	}

	if param.liveUsername != "" {
		if isSearchByLiveUsername {
			db = db.Where("u.username LIKE ?", servicedb.ToLikeStr(param.liveUsername))
		} else {
			db = db.Joins(fmt.Sprintf("JOIN %s AS u ON l.live_id = u.id", mowangskuser.TableName())).
				Where("u.username LIKE ?", servicedb.ToLikeStr(param.liveUsername))
		}
	}
	if param.liveID > 0 {
		db = db.Where("l.live_id = ?", param.liveID)
	}

	return db, nil
}

// findRenews 查找待续约主播的续约申请/邀请
func (param *memberListParam) findRenews() {
	fifteenDayLater := goutil.TimeNow().Unix() + fifteenDaySec
	needRenewliveID := make([]int64, 0, len(param.contracts))
	for i := 0; i < len(param.contracts) &&
		param.contracts[i].ContractEnd < fifteenDayLater; i++ {
		needRenewliveID = append(needRenewliveID, param.contracts[i].LiveID)
	}
	if len(needRenewliveID) == 0 {
		return
	}
	var ca []*contractapplyment.ContractApplyment
	err := service.DB.Find(&ca,
		"guild_id = ? AND live_id IN (?) AND type IN (?) AND status = ? AND expire_time > ?",
		param.guildID, needRenewliveID, []int64{contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildRenew},
		contractapplyment.StatusPending, goutil.TimeNow().Unix()).Error
	if err != nil {
		logger.Error(err)
		return
	}
	param.renewApplyments = make(map[int64]*contractapplyment.ContractApplyment, len(ca))
	for i := 0; i < len(ca); i++ {
		param.renewApplyments[ca[i].LiveID] = ca[i]
	}
}

// buildResp 根据查找到的合同和申请构建响应结果
func (param *memberListParam) buildResp() (handler.ActionResponse, error) {
	if param.p == 1 && param.liveID == 0 && param.liveUsername == "" {
		// 只有在没有筛选参数并且取第一页的时候会返回这个字段
		param.Total = new(int64)
		*param.Total = param.Pagination.Count
	}
	liveIDs := make([]int64, len(param.contracts))
	for i := 0; i < len(param.contracts); i++ {
		c := param.contracts[i]
		elem := &memberElem{
			LiveID:        c.LiveID,
			ContractStart: c.ContractStart,
			ContractEnd:   c.ContractEnd,
			Settlement:    settlement,
			Rate:          guildrate.ApplymentRate(c.Rate),
		}
		// 获取签约时长
		durationStr, ok := contractapplyment.DurationLabelMap[c.ContractDuration]
		if ok {
			elem.Period = durationStr
		} else {
			// 以前签的合约没有合约期限
			elem.Period = "-"
		}
		liveIDs[i] = c.LiveID

		// 只有公会长可查看未处理的降薪申请
		if len(param.rateMap) != 0 {
			elem.EditRate = param.rateMap[c.LiveID]
			elem.RatePending = elem.EditRate > 0
		}
		param.Data = append(param.Data, elem)
	}
	userIDs := liveIDs
	// 三方独家主播
	exclusiveCreatorMap, err := exclusivecreator.FindMapByCreatorIDs(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var agentCreatorMap map[int64]int64
	if param.role.IsGuildOwner() {
		agentCreatorList, err := guildagent.FindGuildAgentListByCreator(liveIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if len(agentCreatorList) != 0 {
			agentIDs := make([]int64, len(agentCreatorList))
			agentCreatorMap = make(map[int64]int64, len(agentCreatorList))
			for i, item := range agentCreatorList {
				agentCreatorMap[item.CreatorID] = item.AgentID
				agentIDs[i] = item.AgentID
			}
			userIDs = append(liveIDs, agentIDs...)
		}
	}

	vitalityMap, err := liveaddendum.FindVitalityMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	fansNumMap, err := attentionuser.FindFansNumMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	lives, err := live.FindLives(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	liveMap := goutil.ToMap(lives, "UserID").(map[int64]*live.Live)

	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(param.Data); i++ {
		if u, ok := users[param.Data[i].LiveID]; ok {
			param.Data[i].LiveUsername = u.Username
			param.Data[i].LiveIconURL = u.IconURL
			if param.role.IsGuildOwner() {
				if agentID, ok := agentCreatorMap[param.Data[i].LiveID]; ok {
					if agentUser, ok := users[agentID]; ok {
						param.Data[i].AgentID = agentUser.ID
						param.Data[i].AgentUsername = agentUser.Username
						param.Data[i].AgentIconURL = agentUser.IconURL
					}
				}
			} else {
				param.Data[i].AgentID = param.user.ID
				param.Data[i].AgentUsername = param.user.Username
			}
		}

		param.Data[i].Vitality = vitalityMap[param.Data[i].LiveID]

		if fansNum := fansNumMap[param.Data[i].LiveID]; fansNum != nil {
			param.Data[i].FansNum = fansNum.FansNum
		}

		if roomID := liveMap[param.Data[i].LiveID]; roomID != nil {
			param.Data[i].RoomID = roomID.RoomID
		}

		if inactiveAnchor, ok := param.inactiveAnchors[param.Data[i].LiveID]; ok {
			param.Data[i].TotalRevenue = inactiveAnchor.GuildLiveIncome
			param.Data[i].LiveDays = inactiveAnchor.GuildLiveDays
			param.Data[i].LastLiveDate = inactiveAnchor.LastLiveDate.Format(goutil.TimeFormatYMD)
		}

		// 是否是三方独家主播标识
		if _, ok := exclusiveCreatorMap[param.Data[i].LiveID]; ok {
			param.Data[i].ExclusiveCreator = true
		}

		if renewApplyment := param.renewApplyments[param.Data[i].LiveID]; renewApplyment != nil {
			param.Data[i].RenewApplymentID = renewApplyment.ID
		}
	}

	// 如果是查看主播详情信息时，需要返回三方独家合约到期时间
	if param.liveID != 0 && len(param.Data) == 1 {
		if exclusiveCreator := exclusiveCreatorMap[param.Data[0].LiveID]; exclusiveCreator != nil {
			param.Data[0].ExclusiveContractEnd = exclusiveCreator.ContractEnd
		}
	}

	return &param.memberListResp, nil
}
