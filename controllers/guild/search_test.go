package guild

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionSearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用会长 ID 搜索
	ctx := handler.CreateTestContext(false)
	ctx.C.Request, _ = http.NewRequest("GET", "/api/v2/guild/search?s=12", nil)
	r, err := ActionSearch(ctx)
	require.NoError(err)

	b, err := json.Marshal(r)
	require.NoError(err)
	var resp []guildItem
	require.NoError(json.Unmarshal(b, &resp))
	assert.Equal(int64(3), resp[0].ID)

	g := guild.Guild{
		Name:    "test",
		Checked: 1,
	}
	err = service.DB.Where("id = 124").Assign(g).FirstOrCreate(&g).Error
	assert.NoError(err)

	// 测试用关键词搜索
	newCtx := handler.CreateTestContext(false)
	newCtx.C.Request, _ = http.NewRequest("GET", "/api/v2/guild/search?s=test", nil)
	a, err := ActionSearch(newCtx)
	assert.NotEmpty(a)
	assert.Nil(err)
}
