package livebackend

import (
	"fmt"
	"math"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type terminateForciblyResp struct {
	Price              string `json:"price"`               // 违约金 (元)
	Link               string `json:"link,omitempty"`      // 跳转支付链接
	TerminationApplied bool   `json:"termination_applied"` // 主播在本合约期内是否有申请过协商解约
}

// ActionTerminateForcely 主播强制解约
/**
 * @apiDeprecated 由 /api/v2/guild/livebackend/application/terminateforcibly 替换
 * @api {post} /api/v2/guild/livebackend/terminateforcely 主播强制解约
 * @apiVersion 0.1.0
 * @apiName terminateforcely
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "price": "3000.20",  // 违约金
 *       "link": "https://openapi.alipaydev.com/gateway.do?alipay_sdk=alipay-sdk-php-20161101&app_id=2016080..."
 *     }
 *   }
 *
 * @apiError (403) {Number} code 500050011
 * @apiError (403) {String} info 您还未加入公会，请先申请签约
 *
 * @apiError (403) {Number} code 500050017
 * @apiError (403) {String} info 签约未满 180 天，无法解约
 *
 */
func ActionTerminateForcely(c *handler.Context) (handler.ActionResponse, error) {
	params := terminateParams{
		creatorID: c.UserID(),
	}

	nowStamp := goutil.TimeNow().Unix()
	// 是否已加入公会
	contract, err := livecontract.FindInContractingByLiveID(c.UserID(), 0, "id, guild_id, guild_name, "+
		"contract_start, contract_end, contract_duration, rate")
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if contract == nil {
		return "", actionerrors.ErrShouldJoinGuild
	}
	// 受限房间禁止退会
	r, err := room.FindOne(bson.M{"creator_id": c.UserID()},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r != nil && r.Limit != nil {
		return nil, actionerrors.ErrNoAuthority
	}

	params.guildID = contract.GuildID
	params.liveContract = contract

	// 是否处于签约保护期中
	isProtected := livecontract.IsInProtecting(contract.ContractStart)
	if isProtected {
		return nil, actionerrors.ErrContractBeingProtected
	}

	// 公会是否已发送续约邀请
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id").
		Where("live_id = ? AND guild_id = ? AND status = ? AND type = ?",
			c.UserID(), contract.GuildID, contractapplyment.StatusPending, contractapplyment.TypeGuildRenew).
		Where("expire_time > ?", nowStamp).
		Scan(&applyment).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.ID > 0 {
		return "", actionerrors.ErrRenewApplymentPending
	}

	// 创建强制解约合约
	newApplyment := contractapplyment.ContractApplyment{
		LiveID:             c.User().ID,
		GuildID:            contract.GuildID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Type:               contractapplyment.TypeLiveTerminateForcely,
		Status:             contractapplyment.StatusPending, // 强制解约合约创建后本身不生效，需要等待支付后方更新为已处理状态
		ExpireTime:         nowStamp,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               contract.Rate,
	}

	err = service.DB.Create(&newApplyment).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	params.forciblyTerminateApplicationID = newApplyment.ID

	// 获取强制解约费用（分）
	price, err := params.terminateForciblyPrice()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	params.forciblyTerminatePrice = price

	// 创建违约金支付订单
	info, err := params.createLivePenaltyOrder()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return info, nil
}

// ActionApplicationTerminateForcibly 主播强制解约
/**
 * @api {post} /api/v2/guild/livebackend/application/terminateforcibly 主播强制解约
 * @apiVersion 0.1.0
 * @apiName application/terminateforcibly
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} 主播在本合约期内申请过协商解约
 *   {
 *     "code": 0,
 *     "info": {
 *       "price": "3000.20", // 违约金 (元)
 *       "link": "https://openapi.alipaydev.com/gateway.do?alipay_sdk=alipay-sdk-php-20161101&app_id=2016080...",
 *       "termination_applied": true // 主播在本合约期内是否有申请过协商解约
 *     }
 *   }
 *
 * @apiSuccessExample {json} 主播在本合约期内没有申请过协商解约
 *   {
 *     "code": 0,
 *     "info": {
 *       "price": "3000.20", // 违约金 (元)
 *       "termination_applied": false // 主播在本合约期内是否有申请过协商解约
 *     }
 *   }
 *
 * @apiSuccessExample {json} 协商解约时有公会清退申请
 *   {
 *     "code": 0,
 *     "info": "您已被原公会清退，现已正式解约成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 500050011
 * @apiError (403) {String} info 您还未加入公会，请先申请签约
 *
 * @apiError (403) {Number} code 500050017
 * @apiError (403) {String} info 签约未满 180 天，无法解约
 *
 * @apiError (403) {Number} code 500050018
 * @apiError (403) {String} info 请先处理续约邀请
 */
func ActionApplicationTerminateForcibly(c *handler.Context) (handler.ActionResponse, error) {
	params := terminateParams{
		creatorID: c.UserID(),
	}

	// 检查主播方是否满足解约要求
	err := params.CheckCreator()
	if err != nil {
		return nil, err
	}

	// 是否已发送协商解约申请和公会是否已发送续约邀请
	err = params.CheckPendingApplication()
	if err != nil {
		return nil, err
	}

	// 公会是否申请清退该主播，有则通过清退申请
	str, err := params.GuildExpelLive()
	if err != nil {
		return nil, err
	}
	if str != "" {
		return str, nil
	}

	// 创建强制解约申请
	err = params.createTerminateForciblyApplication()
	if err != nil {
		return nil, err
	}

	// 获取强制解约费用（分）
	price, err := params.terminateForciblyPrice()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	params.forciblyTerminatePrice = price
	params.clientIP = c.ClientIP()
	terminationApplied := params.liveContract.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated)
	if terminationApplied {
		// 创建违约金支付订单
		info, err := params.createLivePenaltyOrder()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		// 主播在本合约期内是否有申请过协商解约
		info.TerminationApplied = terminationApplied

		return info, nil
	}

	return &terminateForciblyResp{
		Price:              fmt.Sprintf("%.2f", float64(price)/100),
		TerminationApplied: terminationApplied,
	}, nil
}

// 强制解约相关限制
const (
	validLiveMonthMilliLimit       = 5 * 3600 * 1000 // 强制解约时是否为有效开播月的时长限制（毫秒）
	validLiveMonthCountLimit int   = 3               // 强制解约费用计算规则近 6 个月有效开播月数限制
	revenueLimit             int64 = 40000           // 强制解约时是否为有效开播月的月流水（分）限制
	totalRevenueLimit        int64 = 10000000        // 强制解约费用计算规则近 6 个月总流水（分）限制
)

// 解约费用
const (
	exceedTerminateForciblyPrice int64 = 2000000 // 高于近 6 个月总流水（分）限制应付解约费用
	belowTerminateForciblyPrice  int64 = 500000  // 低于近 6 个月总流水（分）限制应付解约费用
)

type monthRevenue struct {
	YearMonth string `gorm:"column:ym"`
	Revenue   int64  `gorm:"column:revenue"`
}

type terminatePriceParams struct {
	validLiveMonthCount        int   // 有效开播月数
	totalSixMonthsRevenue      int64 // 6 个月总流水（分）
	validLiveMonthTotalRevenue int64 // 有效开播月总流水（分）
}

func (p *terminateParams) createTerminateForciblyApplication() error {
	newApplication := contractapplyment.ContractApplyment{
		LiveID:             p.creatorID,
		GuildID:            p.liveContract.GuildID,
		GuildName:          p.liveContract.GuildName,
		ContractID:         p.liveContract.ID,
		ContractDuration:   p.liveContract.ContractDuration,
		ContractExpireTime: p.liveContract.ContractEnd,
		Type:               contractapplyment.TypeLiveTerminateForcely,
		Status:             contractapplyment.StatusPending, // 强制解约合约创建后本身不生效，需要等待支付后方更新为已处理状态
		ExpireTime:         goutil.TimeNow().Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               p.liveContract.Rate,
	}
	err := newApplication.DB().Create(&newApplication).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.forciblyTerminateApplicationID = newApplication.ID
	return nil
}

func (p *terminateParams) terminateForciblyPrice() (int64, error) {
	now := goutil.TimeNow()
	beginningOfMonth := goutil.BeginningOfMonth(now)
	firstDayOfFiveMonthsAgo := beginningOfMonth.AddDate(0, -5, 0)

	var monthRevenues []*monthRevenue
	// 统计近 6 个月每月流水（单位：分）= 礼物 + 提问 + 超粉开通 + 超粉续费 + (贵族开通 * 80%)
	err := transactionlog.ADB().
		Select("FROM_UNIXTIME(confirm_time, '%Y%m') AS ym, "+
			"CAST(IFNULL(SUM(FLOOR(ROUND(IFNULL(IF(attr IN (?), (income - tax) * 0.8, (income - tax)), 0) * 1000) / 10)), 0) AS SIGNED) AS revenue",
			transactionlog.NobleAttrs()).
		Where("suborders_num = ? AND type = ? AND status = ?",
			p.guildID, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("attr IN (?) AND to_id = ?", transactionlog.AllRevenueAttrs(), p.creatorID).
		Where("confirm_time >= ? AND confirm_time < ?", firstDayOfFiveMonthsAgo.Unix(), now.Unix()).
		Group("ym").Scan(&monthRevenues).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	monthRevenueMap := map[string]*monthRevenue{}
	if len(monthRevenues) != 0 {
		monthRevenueMap = goutil.ToMap(monthRevenues, "YearMonth").(map[string]*monthRevenue)
	}

	// 近 6 个月每月直播时长
	userMonthDurationMap, err := livelog.GetUserMonthLiveDuration(p.guildID, p.creatorID, firstDayOfFiveMonthsAgo, now)
	if err != nil {
		return 0, err
	}
	for i := 0; i <= 5; i++ {
		month := beginningOfMonth.AddDate(0, -i, 0).Format(util.TimeFormatYMWithNoSpace)
		var revenue int64
		var totalDuration int64
		if md := userMonthDurationMap[month]; md != nil {
			totalDuration = md.TotalDuration
		}
		if mr := monthRevenueMap[month]; mr != nil {
			revenue = mr.Revenue
			// 6 个月总流水
			p.terminatePriceParams.totalSixMonthsRevenue += mr.Revenue
		}
		if totalDuration >= validLiveMonthMilliLimit && revenue >= revenueLimit {
			// 有效开播月数
			p.terminatePriceParams.validLiveMonthCount++
			// 有效开播月的月流水之和
			p.terminatePriceParams.validLiveMonthTotalRevenue += revenue
		}
	}

	return p.calculateTerminateForciblyPrice(), nil
}

func (p *terminateParams) calculateTerminateForciblyPrice() int64 {
	// 近 6 个月有效开播月数大于等于 3 时（解约费 = N * 有效开播月的月流水之和）
	// 有效开播月 3 个月，N = 0.5
	// 有效开播月 4 个月，N = 0.4
	// 有效开播月 5 个月，N = 0.32
	// 有效开播月 6 个月，N = 0.25
	if p.terminatePriceParams.validLiveMonthCount >= validLiveMonthCountLimit {
		switch p.terminatePriceParams.validLiveMonthCount {
		case 3:
			return int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue) * 0.5))
		case 4:
			return int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue) * 0.4))
		case 5:
			return int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue) * 0.32))
		case 6:
			return int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue) * 0.25))
		default:
			panic(fmt.Sprintf("错误的有效开播月数: %d", p.terminatePriceParams.validLiveMonthCount))
		}
	}
	// 近六月有效开播月数小于 3 时
	if p.terminatePriceParams.totalSixMonthsRevenue >= totalRevenueLimit {
		// 近 6 个月总流水大于等于十万元
		return exceedTerminateForciblyPrice
	}
	// 近 6 个月总流水小于十万元
	return belowTerminateForciblyPrice
}

func (p *terminateParams) createLivePenaltyOrder() (*terminateForciblyResp, error) {
	resp, err := userapi.CreateLivePenaltyOrder(p.forciblyTerminateApplicationID, p.forciblyTerminatePrice, p.clientIP)
	if err != nil {
		return nil, err
	}
	info := &terminateForciblyResp{
		Price: fmt.Sprintf("%.2f", float64(p.forciblyTerminatePrice)/100),
		Link:  resp["link"].(string),
	}

	return info, err
}
