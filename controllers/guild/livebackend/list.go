package livebackend

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 筛选全部时约定值
	filterTypeAll   = 10
	filterStatusAll = 10
)

const settlement = "对公结算"

type listResp struct {
	Data []struct {
		ID        int64  `json:"id" gorm:"column:id"`
		UserID    int64  `json:"user_id" gorm:"column:user_id"`
		GuildName string `json:"guild_name" gorm:"column:guild_name"`
		Rate      int    `json:"rate" gorm:"column:rate"`

		ContractDuration int64  `json:"-" gorm:"column:contract_duration"`
		Duration         string `json:"duration"`

		Type       int64  `json:"type" gorm:"column:type"`
		Status     int64  `json:"status" gorm:"column:status"`
		Settlement string `json:"settlement"`
		ExpireTime int64  `json:"-" gorm:"column:expire_time"`

		CreateTime int64 `json:"create_time" gorm:"column:create_time"`
	} `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionInviteList 主播后台邀请记录
/**
 * @api {get} /api/v2/guild/livebackend/invitelist 主播后台邀请记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {String} [guild_name] 检索公会名
 * @apiParam {Number} [user_id] 检索会长 ID
 * @apiParam {number=2,4} [type] 类型筛选：2 签约邀请，4 续约邀请
 * @apiParam {number=-3,-2,-1,0,1} [status] 合约申请状态：-3 已失效，-2 已撤回，-1 已拒绝，0 未处理，1 已同意
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 * {
 *   "code":0,
 *   "info": {
 *     "Datas": [{
 *       "id": 415,  // 合约申请 ID
 *       "user_id": 346286,  // 会长 ID
 *       "guild_name": "测试公会",  // 公会名称
 *       "rate": 45,  // 最低分成比例
 *       "type": 2,
 *       "duration": "12 个月",  // 签约时限
 *       "status": 1,
 *       "settlement": "对公结算",  // 结算方式
 *       "create_time": 1348423150,  // 邀请时间
 *     }],
 *     "pagination": {
 *       "count": 1,
 *       "p": 1,
 *       "pagesize": 20,
 *       "maxpage": 1
 *     }
 *   }
 * }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionInviteList(c *handler.Context) (handler.ActionResponse, error) {
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	applymentType, err := c.GetDefaultParamInt64("type", filterTypeAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	status, err := c.GetDefaultParamInt64("status", filterStatusAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	guildOwnerID, _ := c.GetParamInt64("user_id")
	guildName := c.C.Query("guild_name")

	return getApplymentList(c.UserID(), contractapplyment.InitiatorGuild, page, pageSize, applymentType, status, guildOwnerID, guildName)
}

// ActionApplymentList 主播后台申请记录
/**
* @api {get} /api/v2/guild/livebackend/application/list 主播后台申请记录
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} [p=1] 第几页
* @apiParam {Number} [pagesize=20] 每页个数
* @apiParam {String} [guild_name] 检索公会名
* @apiParam {Number} [user_id] 检索会长 ID
* @apiParam {number=1,3,5,7} [type] 类型筛选：1 签约申请，3 续约申请，5 协商解约，7 强制解约
* @apiParam {number=-3,-2,-1,0,1,2} [status] 合约申请状态：-3 已失效，-2 已撤回，-1 已拒绝，0 未处理，1 已同意，2 已生效（仅强制解约显示）
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
* {
*   "code":0,
*   "info": {
*     "Datas": [{
*       "id": 415,  // 合约申请 ID
*       "user_id": 346286,  // 会长 ID
*       "guild_name": "测试公会",  // 公会名称
*       "rate": 45,  // 最低分成比例
*       "type": 2,
*       "duration": "12 个月",  // 签约时限
*       "status": 1,
*       "settlement": "对公结算",
*       "create_time": 1348423150,  // 邀请时间
*     }],
*     "pagination": {
*       "count": 1,
*       "p": 1,
*       "pagesize": 20,
*       "maxpage": 1
*     }
*   }
* }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
 */
func ActionApplymentList(c *handler.Context) (handler.ActionResponse, error) {
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	applymentType, err := c.GetDefaultParamInt64("type", filterTypeAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	status, err := c.GetDefaultParamInt64("status", filterStatusAll)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	guildOwnerID, _ := c.GetParamInt64("user_id")
	guildName := c.C.Query("guild_name")

	return getApplymentList(c.UserID(), contractapplyment.InitiatorLive, page, pageSize, applymentType, status, guildOwnerID, guildName)
}

func getApplymentList(liveID int64, initiator int, page, pageSize, applymentType, applymentStatus, guildOwnerID int64, guildName string) (listResp, error) {
	var resp listResp
	nowStamp := goutil.TimeNow().Unix()

	db := service.DB.Table(contractapplyment.TableName()+" AS c").
		Select("c.id, g.user_id, c.guild_id, c.guild_name, c.rate, c.contract_duration, c.type, c.status, c.expire_time, c.create_time").
		Where("c.live_id = ? AND c.initiator = ?", liveID, initiator).
		Joins(fmt.Sprintf("LEFT JOIN %s AS g ON g.id = c.guild_id", guild.TableName())).
		Order("c.id DESC")
	if initiator == contractapplyment.InitiatorLive {
		db = db.Where("c.type IN (?) OR (c.status = ? AND c.type = ?)",
			[]int64{contractapplyment.TypeLiveSign, contractapplyment.TypeLiveRenew, contractapplyment.TypeLiveTerminate},
			contractapplyment.StatusAgreed,
			contractapplyment.TypeLiveTerminateForcely)
	} else {
		db = db.Where("c.type IN (?)", []int64{contractapplyment.TypeGuildSign, contractapplyment.TypeGuildRenew})
	}

	if applymentStatus == contractapplyment.StatusTerminateForcelySuccess && (applymentType == filterTypeAll || applymentType == contractapplyment.TypeLiveTerminateForcely) {
		// 筛选已生效（强制解约）
		applymentStatus = contractapplyment.StatusAgreed
		applymentType = contractapplyment.TypeLiveTerminateForcely
	} else if applymentStatus == contractapplyment.StatusAgreed {
		// 筛选已同意（不显示强制解约）
		db = db.Where("c.type <> ?", contractapplyment.TypeLiveTerminateForcely)
	}
	if applymentType < filterTypeAll {
		db = db.Where("c.type = ?", applymentType)
	}
	if applymentStatus < filterStatusAll {
		if applymentStatus == contractapplyment.StatusInvalid {
			db = db.Where("c.status IN (?) OR (c.status = ? AND c.expire_time <= ?)",
				[]int64{contractapplyment.StatusOutdated, contractapplyment.StatusInvalid},
				contractapplyment.StatusPending, nowStamp)
		} else {
			db = db.Where("c.status = ?", applymentStatus)
		}
	}
	if guildOwnerID != 0 {
		db = db.Where("g.user_id = ?", guildOwnerID)
	}
	if guildName != "" {
		db = db.Where("g.name LIKE ?", servicedb.ToLikeStr(guildName))
	}

	var totalCount int64
	err := db.Count(&totalCount).Error
	if err != nil {
		return resp, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.Pagination = goutil.MakePagination(totalCount, page, pageSize)
	db = resp.Pagination.ApplyTo(db)
	err = db.Scan(&resp.Data).Error
	if err != nil {
		return resp, actionerrors.NewErrServerInternal(err, nil)
	}
	for i, v := range resp.Data {
		if v.Type == contractapplyment.TypeLiveTerminate || v.Type == contractapplyment.TypeLiveTerminateForcely {
			if v.Type == contractapplyment.TypeLiveTerminateForcely {
				resp.Data[i].Status = contractapplyment.StatusTerminateForcelySuccess
			}
			resp.Data[i].Duration = "-"
		} else {
			resp.Data[i].Duration = contractapplyment.DurationLabelMap[v.ContractDuration]
		}
		if v.Status == contractapplyment.StatusPending && v.ExpireTime <= nowStamp {
			resp.Data[i].Status = contractapplyment.StatusOutdated
		}
		resp.Data[i].Settlement = settlement
	}

	return resp, nil
}

type applymentPendingResp struct {
	InviteCount int `json:"invite_count"`
}

// ActionApplymentPendingCount 获取主播未处理的邀请数量
/**
 * @api {get} /api/v2/guild/livebackend/application/pending-count 获取主播未处理的邀请数量
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": {
 *     "invite_count": 3 // 邀请数量
 *   }
 * }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionApplymentPendingCount(c *handler.Context) (handler.ActionResponse, error) {
	// 未处理的（公会发起的）签约邀请，续约邀请
	applymentTypes := []int64{contractapplyment.TypeGuildSign, contractapplyment.TypeGuildRenew}

	var inviteCount int
	err := contractapplyment.ContractApplyment{}.DB().
		Where("live_id = ? AND status = ? AND expire_time > ? AND type IN (?)",
			c.UserID(), contractapplyment.StatusPending, goutil.TimeNow().Unix(), applymentTypes).
		Count(&inviteCount).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var resp applymentPendingResp
	resp.InviteCount = inviteCount
	return resp, nil
}
