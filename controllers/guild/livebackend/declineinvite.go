package livebackend

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionDeclineInvite 主播拒绝公会邀请
/**
 * @api {post} /api/v2/guild/livebackend/declineinvite 主播拒绝公会邀请
 * @apiVersion 0.1.0
 * @apiName declineinvite
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiParam {Number} applyment_id 合约申请 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "拒绝成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (400) {Number} code 500050004
 * @apiError (400) {String} info 该公会未向您发起邀请
 *
 * @apiError (403) {Number} code 500050015
 * @apiError (403) {String} info 不可操作已被处理或已失效的申请
 */
func ActionDeclineInvite(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		ApplymentID int64 `json:"applyment_id"`
	}
	err := c.BindJSON(&params)
	if err != nil || params.ApplymentID <= 0 {
		return "", actionerrors.ErrParams
	}

	// 邀请是否存在
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id, status, type, expire_time, guild_id").
		Where("type IN (?)", []int64{contractapplyment.TypeGuildSign, contractapplyment.TypeGuildRenew}).
		Where("id = ? AND live_id = ? AND initiator = ?", params.ApplymentID, c.UserID(), contractapplyment.InitiatorGuild).
		Scan(&applyment).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrGuildNotInvite
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.Status != contractapplyment.StatusPending || applyment.IsExpired() {
		return "", actionerrors.ErrApplymentFreezed
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 拒绝邀请
	err = service.DB.Model(applyment).
		Where("id = ? AND status = ?", applyment.ID, contractapplyment.StatusPending).
		Updates(map[string]interface{}{
			"status":       contractapplyment.StatusDeclined,
			"process_time": goutil.TimeNow().Unix(),
		}).Error
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 发送系统消息
	var sysTitle, sysContent, inviteType string
	if applyment.Type == contractapplyment.TypeGuildSign {
		inviteType = "签约"
	} else {
		inviteType = "续约"
	}
	sysTitle = fmt.Sprintf("您的%s邀请未通过", inviteType)
	sysContent = fmt.Sprintf("很遗憾 _(:3 」∠)_ 主播 %s（MID：%d）拒绝了您的%s邀请。", c.User().Username, c.UserID(), inviteType)

	var guildOwnerID int64
	err = service.DB.Table(guild.TableName()).Select("user_id").
		Where("id = ?", applyment.GuildID).Row().Scan(&guildOwnerID)
	if err == nil {
		if err = messageassign.SystemMessageAssign(guildOwnerID, sysTitle, sysContent); err != nil {
			logger.Error(err)
			// PASS
		}
	} else {
		logger.Error(err)
		// PASS
	}

	return "拒绝成功", nil
}
