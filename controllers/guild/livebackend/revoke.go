package livebackend

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var applymentRevokeTip = map[int64]string{
	contractapplyment.TypeLiveSign:             "主播签约申请需要在申请提交的 24 小时后方可撤回",
	contractapplyment.TypeLiveRenew:            "主播续约申请需要在申请提交的 24 小时后方可撤回",
	contractapplyment.TypeLiveTerminate:        "主播协商解约申请需要在申请提交的 3 个自然日撤回",
	contractapplyment.TypeLiveTerminateForcely: "强制解约已失效无需撤回",
}

// ActionRevoke 主播撤回申请
/**
* @api {post} /api/v2/guild/livebackend/revoke 主播撤回申请
* @apiVersion 0.1.0
* @apiName revoke
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} applyment_id 合约申请 ID
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "撤回成功"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (403) {Number} code 500050014 todo
* @apiError (403) {String} info 该申请不存在
*
* @apiError (403) {Number} code 500050015 todo
* @apiError (403) {String} info 不可操作已被处理或已失效的申请
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 主播签约申请需要在申请提交的 24 小时后方可撤回
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 主播续约申请需要在申请提交的 24 小时后方可撤回
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 主播协商解约申请需要在申请提交的 3 个自然日撤回
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 强制解约已失效无需撤回
 */
func ActionRevoke(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		ApplymentID int64 `json:"applyment_id"`
	}
	err := c.BindJSON(&params)
	if err != nil || params.ApplymentID <= 0 {
		return "", actionerrors.ErrParams
	}

	// 是否已发送申请
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id, live_id, guild_id, contract_id, status, type, more, expire_time, create_time").
		Where("id = ? AND live_id = ? AND initiator = ?", params.ApplymentID, c.UserID(), contractapplyment.InitiatorLive).
		Scan(&applyment).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrApplymentNotExist
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	if applyment.Status != contractapplyment.StatusPending || applyment.IsExpired() {
		return "", actionerrors.ErrApplymentFreezed
	}

	if !applyment.IsTimeToRevoke() {
		return "", actionerrors.ErrParamsMsg(applymentRevokeTip[applyment.Type])
	}
	if applyment.Type == contractapplyment.TypeLiveSign ||
		applyment.Type == contractapplyment.TypeLiveRenew {
		if err = checkPermission(c.UserID(), c.Token()); err != nil {
			return nil, err
		}
	}

	more, err := applyment.UnmarshalMore()
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	nowStamp := goutil.TimeNow().Unix()
	var err2 error
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 撤回申请
		db := tx.Table(contractapplyment.TableName()).
			Where("id = ? AND status = ? AND live_id = ? AND initiator = ?",
				applyment.ID, contractapplyment.StatusPending, c.UserID(), contractapplyment.InitiatorLive).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusRevoked,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			err2 = actionerrors.ErrApplymentNotExist
			return err2
		}

		// 撤回协商解约时，如果申请协商解约前「主播在合约期内是否申请过协商解约的状态」为否时重置「主播在合约期内是否申请过协商解约的状态」
		if applyment.Type == contractapplyment.TypeLiveTerminate &&
			more != nil && !more.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated) {
			db := tx.Table(livecontract.TableName()).
				Where("id = ? AND guild_id = ? AND live_id = ? AND status = ?",
					applyment.ContractID, applyment.GuildID, applyment.LiveID, livecontract.StatusContracting).
				Updates(map[string]interface{}{
					"attr":          livecontract.UnsetAttrBitMaskExpr(livecontract.AttrBitMaskLiveTerminated),
					"modified_time": nowStamp,
				})
			if err := db.Error; err != nil {
				return err
			}
			if db.RowsAffected == 0 {
				err2 = actionerrors.ErrContractNotExist
				return err2
			}
		}
		return nil
	})
	if err != nil {
		if err2 != nil {
			return nil, err2
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "撤回成功", nil
}
