package livebackend

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionRenew 主播申请续约
/**
* @api {post} /api/v2/guild/livebackend/renew 主播申请续约
* @apiVersion 0.1.0
* @apiName renew
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {number=2,3,4,5} duration 合约时长（2: 十二个月（已弃用），3: 二十四个月（已弃用），4: 3 年，5: 5 年）
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "申请成功"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (403) {Number} code 500050011 todo
* @apiError (403) {String} info 您还未加入公会，请先申请签约
*
* @apiError (403) {Number} code 500050012
* @apiError (403) {String} info 合约到期前 15 天可向发起续约
*
* @apiError (403) {Number} code 500050013 todo
* @apiError (403) {String} info 申请已发起，请等待答复
*
* @apiError (403) {Number} code 500050009
* @apiError (403) {String} info 该公会已经向您发起邀请，请查看邀请记录
 */
func ActionRenew(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		Duration int64 `json:"duration"`
	}
	err := c.BindJSON(&params)
	if err != nil || !contractapplyment.IsLegalContractDuration(params.Duration) {
		return "", actionerrors.ErrParams
	}

	// 是否已签约
	var contract livecontract.LiveContract
	err = service.DB.
		Table(livecontract.TableName()).
		Select("id, guild_id, guild_name, contract_end, rate").
		Where("live_id = ? AND status = ? AND contract_end > ?",
			c.User().ID, livecontract.StatusContracting, goutil.TimeNow().Unix()).
		Scan(&contract).Error

	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrShouldJoinGuild
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	if now.AddDate(0, 0, contractapplyment.RenewDaysBeforeContractEnd).Before(time.Unix(contract.ContractEnd, 0)) {
		return "", actionerrors.ErrRenewTooEarly
	}

	// 最低分成比例默认为当前合约分成比例
	rate := contract.Rate
	if rate <= 0 {
		rate = guildrate.RatePercent45
	}

	// 是否已发送续约申请或公会是否已发送续约申请
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id, type").
		Where("type IN (?)", []int64{contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildRenew}).
		Where("live_id = ? AND guild_id = ? AND status = ? AND expire_time > ?",
			c.UserID(), contract.GuildID, contractapplyment.StatusPending, now.Unix()).
		Scan(&applyment).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.ID > 0 {
		if applyment.Type == contractapplyment.TypeGuildRenew {
			return "", actionerrors.ErrGuildAlreadyInviteRenew
		}
		return "", actionerrors.ErrApplymentPending
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 创建续约申请
	newApplyment := contractapplyment.ContractApplyment{
		LiveID:             c.UserID(),
		GuildID:            contract.GuildID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   params.Duration,
		ContractExpireTime: contractapplyment.GetContractEnd(time.Unix(contract.ContractEnd, 0), params.Duration).Unix(),
		Type:               contractapplyment.TypeLiveRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               rate,
	}

	err = service.DB.Create(&newApplyment).Error
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	return "申请成功", nil
}
