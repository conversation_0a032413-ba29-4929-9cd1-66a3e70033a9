package livebackend

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionTerminate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 未加入公会
	ctx := handler.CreateTestContext(true)
	ctx.User().ID = testLiveID1
	require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "live_id = ?", ctx.User().ID).Error)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/terminate", nil)
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	la := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      room.TestLimitedRoomCreatorID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "受限房间禁止退会",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&la).Error
	require.NoError(err)
	defer service.DB.Table(livecontract.TableName()).Delete(&la)
	c := handler.NewTestContext(http.MethodPost, "terminate", true, nil)
	c.User().ID = la.LiveID
	_, err = ActionTerminate(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 处于签约保护期
	now := goutil.TimeNow()
	ctx.User().ID = testLiveID2
	contract1 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -10).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_处于签约保护期",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract1).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract1.ID)
	}()
	_, err = ActionTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrContractBeingProtected, err)

	// 已发送协商解约申请
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_已发送协商解约申请",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract2).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract2.ID)
	}()
	applyment := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_已发送协商解约申请",
		Type:             contractapplyment.TypeLiveTerminate,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
	}
	err = service.DB.
		Table(contractapplyment.TableName()).
		Create(&applyment).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment.ID)
	}()
	_, err = ActionTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentPending, err)

	// 协商解约申请提交成功
	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_协商解约申请提交成功",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract3).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract3.ID)
	}()
	r, err := ActionTerminate(ctx)
	require.NoError(err)
	assert.Equal("提交成功", r)

	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "live_id = ? AND guild_id = ? AND contract_id = ?", ctx.User().ID, testGuildID, contract3.ID)
	}()
}

func TestActionApplicationTerminateCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 主播未加入公会
	ctx := handler.NewTestContext(http.MethodPost, "application/terminate-check", true, nil)
	ctx.User().ID = testLiveID1
	_, err := ActionApplicationTerminateCheck(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	la := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      room.TestLimitedRoomCreatorID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "受限房间禁止退会",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&la).Error)
	defer service.DB.Table(livecontract.TableName()).Delete(&la)

	ctx.User().ID = la.LiveID
	_, err = ActionApplicationTerminateCheck(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 处于签约保护期
	now := goutil.TimeNow()
	ctx.User().ID = testLiveID2
	contract1 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -10).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_处于签约保护期",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract1).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract1.ID)

	_, err = ActionApplicationTerminateCheck(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrContractBeingProtected, err)

	// 已发送协商解约申请
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_已发送协商解约申请",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract2).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract2.ID)

	ca1 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_已发送协商解约申请",
		Type:             contractapplyment.TypeLiveTerminate,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&ca1).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca1.ID)

	_, err = ActionApplicationTerminateCheck(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentPending, err)

	// 测试协商解约时有公会清退申请
	ctx.User().ID = testLiveID5
	contract4 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -182).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_公会申请清退过该主播",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract4).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract4.ID)

	ca2 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_公会申请清退过该主播",
		Type:             contractapplyment.TypeGuildExpel,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 3).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&ca2).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca2.ID)

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()
	info, err := ActionApplicationTerminateCheck(ctx)
	require.NoError(err)
	resp := info.(*terminateCheckResp)
	assert.Equal(1, resp.Status)
	assert.Equal("您已被原公会清退，现已正式解约成功", resp.Msg)

	// 测试检查通过
	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "测试检查通过",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract3).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract3.ID)

	r, err := ActionApplicationTerminateCheck(ctx)
	require.NoError(err)
	resp = r.(*terminateCheckResp)
	assert.Equal(0, resp.Status)
	defer service.DB.Table(contractapplyment.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND contract_id = ?", ctx.User().ID, testGuildID, contract3.ID)
}

func TestActionApplicationTerminate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 主播未加入公会
	ctx := handler.NewTestContext(http.MethodPost, "application/terminate", true, nil)
	ctx.User().ID = testLiveID1
	_, err := ActionApplicationTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	la := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      room.TestLimitedRoomCreatorID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "受限房间禁止退会",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&la).Error)
	defer service.DB.Table(livecontract.TableName()).Delete(&la)

	c := handler.NewTestContext(http.MethodPost, "application/terminate", true, nil)
	c.User().ID = la.LiveID
	_, err = ActionApplicationTerminate(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 处于签约保护期
	now := goutil.TimeNow()
	ctx.User().ID = testLiveID2
	contract1 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -10).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_处于签约保护期",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract1).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract1.ID)

	_, err = ActionApplicationTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrContractBeingProtected, err)

	// 已发送协商解约申请
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_已发送协商解约申请",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract2).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract2.ID)

	ca1 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_已发送协商解约申请",
		Type:             contractapplyment.TypeLiveTerminate,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&ca1).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca1.ID)

	_, err = ActionApplicationTerminate(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentPending, err)

	// 协商解约申请提交成功
	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -180).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_协商解约申请提交成功",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract3).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract3.ID)

	r, err := ActionApplicationTerminate(ctx)
	require.NoError(err)
	assert.Equal("提交成功", r)
	defer service.DB.Table(contractapplyment.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND contract_id = ?", ctx.User().ID, testGuildID, contract3.ID)

	// 测试协商解约时有公会清退申请
	ctx.User().ID = testLiveID5
	contract4 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -182).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "协商解约_公会申请清退过该主播",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract4).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract4.ID)

	ca2 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_公会申请清退过该主播",
		Type:             contractapplyment.TypeGuildExpel,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 3).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&ca2).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca2.ID)

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	info, err := ActionApplicationTerminate(ctx)
	require.NoError(err)
	assert.Equal("您已被原公会清退，现已正式解约成功", info)
}
