package livebackend

import (
	"fmt"
	"math"
	"net/http"
	"regexp"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func mockAppRPCServer() func() {
	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"/financial/create-live-penalty-order": handler.NewAction(handler.POST, func(c *handler.Context) (response handler.ActionResponse, e error) {
				return handler.M{
					"order": map[string]interface{}{
						"price":         1,
						"live_id":       400791,
						"guild_id":      3,
						"applyment_id":  80484,
						"status":        0,
						"type":          1,
						"create_time":   goutil.TimeNow().Unix(),
						"modified_time": goutil.TimeNow().Unix(),
						"id":            8975,
					},
					"link":  "https://openapi.alipay.com/gateway.do?alipay_sdk=alipay-sdk&...version=1.0",
					"price": "0.01",
				}, nil
			}, false),
		},
	}
	addr := tutil.RunMockServer(r, 18017, &h)

	appRPCEntryOriginal := service.MRPC.Config["app"]
	service.MRPC.Config["app"] = mrpc.ConfigEntry{
		URL: "http://" + addr + "/",
		Key: "testkey",
	}
	return func() {
		service.MRPC.Config["app"] = appRPCEntryOriginal
	}
}

func TestActionTerminateForcely(t *testing.T) {
	cleanup := mockAppRPCServer()
	defer cleanup()

	assert := assert.New(t)
	require := require.New(t)

	// 未加入公会
	ctx := handler.CreateTestContext(true)
	ctx.User().ID = testLiveID1
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/terminate", nil)
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionTerminateForcely(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	la := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      room.TestLimitedRoomCreatorID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "受限房间禁止退会",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&la).Error
	require.NoError(err)
	defer service.DB.Table(livecontract.TableName()).Delete(&la)
	c := handler.NewTestContext(http.MethodPost, "terminate", true, nil)
	c.User().ID = la.LiveID
	_, err = ActionTerminateForcely(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 处于签约保护期
	now := goutil.TimeNow()
	ctx.User().ID = testLiveID2
	contract1 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -10).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_处于签约保护期",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract1).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract1.ID)
	}()
	_, err = ActionTerminateForcely(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrContractBeingProtected, err)

	// 需要先处理公会的续约邀请
	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -200).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_需要先处理公会的续约邀请",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract3).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract3.ID)
	}()
	applyment := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "强制解约_需要先处理公会的续约邀请",
		Type:             contractapplyment.TypeGuildRenew,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
		ContractID:       contract3.ID,
	}
	err = service.DB.
		Table(contractapplyment.TableName()).
		Create(&applyment).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment.ID)
	}()
	_, err = ActionTerminateForcely(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrRenewApplymentPending, err)

	// 强制解约申请提交成功
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -200).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_强制解约申请提交成功",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract2).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract2.ID)
	}()
	require.NoError(service.DB.First(&contract2, "id = ?", contract2.ID).Error)
	r, err := ActionTerminateForcely(ctx)
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "live_id = ? AND guild_id = ? AND contract_id = ?", ctx.User().ID, testGuildID, contract2.ID)
	}()
	resp := r.(*terminateForciblyResp)
	assert.NoError(err)
	assert.Equal(fmt.Sprintf("%.2f", float64(belowTerminateForciblyPrice)/100), resp.Price)
	assert.Regexp(regexp.MustCompile(`^https?:\/\/.+`), resp.Link)

	// 测试有效开播月数小于 3
	context, cancel := service.MongoDB.Context()
	defer cancel()
	firstDayOfMonth := goutil.BeginningOfMonth(now).AddDate(0, -1, 1)
	lastDayOfMonth := firstDayOfMonth.AddDate(0, 1, 0)
	st := goutil.NewTimeUnixMilli(firstDayOfMonth)
	et := goutil.NewTimeUnixMilli(lastDayOfMonth)
	rec := []interface{}{
		livelog.Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID3,
			CatalogID: 115,
			StartTime: st,
			EndTime:   st + validLiveMonthMilliLimit,
			Duration:  validLiveMonthMilliLimit},
		livelog.Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID3,
			CatalogID: 115,
			StartTime: et + 1000,
			EndTime:   et + 1000 + validLiveMonthMilliLimit,
			Duration:  validLiveMonthMilliLimit},
	}
	_, err = livelog.Collection().InsertMany(context, rec)
	require.NoError(err)

	t1 := transactionlog.TransactionLog{
		Title:        "测试近 6 个月流水之和大于等于 10 万",
		Income:       500000,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: testGuildID,
		ToID:         testLiveID3,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterSuperFan,
		CTime:        firstDayOfMonth.Unix(),
		CreateTime:   firstDayOfMonth.Unix(),
		ModifiedTime: firstDayOfMonth.Unix(),
		ConfirmTime:  firstDayOfMonth.Unix(),
	}
	require.NoError(transactionlog.ADB().Create(&t1).Error)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/terminateforcely", true, nil)
	c.User().ID = testLiveID3
	r, err = ActionTerminateForcely(c)
	assert.NoError(err)
	resp = r.(*terminateForciblyResp)
	assert.Equal(fmt.Sprintf("%.2f", float64(exceedTerminateForciblyPrice)/100), resp.Price)
	assert.Regexp(regexp.MustCompile(`^https?:\/\/.+`), resp.Link)

	defer func() {
		transactionlog.ADB().
			Delete("", "suborders_num = ? AND to_id = ?", testGuildID, testLiveID3)

		_, err := livelog.Collection().DeleteMany(context, bson.M{"guild_id": testGuildID, "creator_id": testLiveID3})
		assert.NoError(err)
	}()
}

func TestTerminateForciblyRespTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).Check(terminateForciblyResp{}, "price", "link", "termination_applied")
}

func TestActionApplicationTerminateForcibly(t *testing.T) {
	now := goutil.TimeNow()
	cleanup := mrpc.SetMock(userapi.URICreateLivePenaltyOrder,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"order": map[string]interface{}{
					"price":         1,
					"live_id":       400791,
					"guild_id":      3,
					"applyment_id":  80484,
					"status":        0,
					"type":          1,
					"create_time":   now.Unix(),
					"modified_time": now.Unix(),
					"id":            8975,
				},
				"link":  "https://openapi.alipay.com/gateway.do?alipay_sdk=alipay-sdk&...version=1.0",
				"price": "5000.00",
			}, nil
		})
	defer cleanup()

	assert := assert.New(t)
	require := require.New(t)

	// 未加入公会
	ctx := handler.NewTestContext(http.MethodPost, "application/terminateforcibly", true, nil)
	ctx.User().ID = testLiveID1
	_, err := ActionApplicationTerminateForcibly(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	la := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      room.TestLimitedRoomCreatorID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "受限房间禁止退会",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&la).Error)
	defer service.DB.Table(livecontract.TableName()).Delete(&la)

	c := handler.NewTestContext(http.MethodPost, "application/terminateforcibly", true, nil)
	c.User().ID = la.LiveID
	_, err = ActionApplicationTerminateForcibly(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 处于签约保护期
	ctx.User().ID = testLiveID2
	contract1 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -179).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_处于签约保护期",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract1).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract1.ID)

	_, err = ActionApplicationTerminateForcibly(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrContractBeingProtected, err)

	// 需要先处理公会的续约邀请
	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -200).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_需要先处理公会的续约邀请",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract3).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract3.ID)

	applyment := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "强制解约_需要先处理公会的续约邀请",
		Type:             contractapplyment.TypeGuildRenew,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
		ContractID:       contract3.ID,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&applyment).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", applyment.ID)

	_, err = ActionApplicationTerminateForcibly(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrRenewApplymentPending, err)

	// 强制解约申请提交成功（合约期内没有申请过协商解约）
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		ContractStart: now.AddDate(0, 0, -200).Unix(),
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
		Type:          livecontract.FromGuild,
		GuildOwner:    100008,
		GuildName:     "强制解约_强制解约申请提交成功",
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract2).Error)
	defer service.DB.Table(livecontract.TableName()).Delete("", "id = ?", contract2.ID)

	require.NoError(service.DB.First(&contract2, "id = ?", contract2.ID).Error)
	r, err := ActionApplicationTerminateForcibly(ctx)
	require.NoError(err)
	defer service.DB.Table(contractapplyment.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND contract_id = ?", ctx.User().ID, testGuildID, contract2.ID)

	resp := r.(*terminateForciblyResp)
	assert.NoError(err)
	assert.Equal(fmt.Sprintf("%.2f", float64(belowTerminateForciblyPrice)/100), resp.Price)
	assert.Empty(resp.Link)

	// 测试有效开播月数小于 3
	context, cancel := service.MongoDB.Context()
	defer cancel()

	firstDayOfMonth := goutil.BeginningOfMonth(now).AddDate(0, -1, 1)
	lastDayOfMonth := firstDayOfMonth.AddDate(0, 1, 0)
	st := goutil.NewTimeUnixMilli(firstDayOfMonth)
	et := goutil.NewTimeUnixMilli(lastDayOfMonth)
	rec := []interface{}{
		livelog.Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID3,
			CatalogID: 115,
			StartTime: st,
			EndTime:   st + validLiveMonthMilliLimit,
			Duration:  validLiveMonthMilliLimit},
		livelog.Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID3,
			CatalogID: 115,
			StartTime: et + 1000,
			EndTime:   et + 1000 + validLiveMonthMilliLimit,
			Duration:  validLiveMonthMilliLimit},
	}
	_, err = livelog.Collection().InsertMany(context, rec)
	require.NoError(err)

	t1 := transactionlog.TransactionLog{
		Title:        "测试近 6 个月流水之和大于等于 10 万",
		Income:       500000,
		Tax:          10,
		Rate:         0.5,
		SubordersNum: testGuildID,
		ToID:         testLiveID3,
		Type:         transactionlog.TypeGuildLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrLiveRegisterSuperFan,
		CTime:        firstDayOfMonth.Unix(),
		CreateTime:   firstDayOfMonth.Unix(),
		ModifiedTime: firstDayOfMonth.Unix(),
		ConfirmTime:  firstDayOfMonth.Unix(),
	}
	require.NoError(transactionlog.ADB().Create(&t1).Error)

	// 测试合约期内没有申请过协商解约
	c = handler.NewTestContext(http.MethodPost, "application/terminateforcibly", true, nil)
	c.User().ID = testLiveID3
	r, err = ActionApplicationTerminateForcibly(c)
	assert.NoError(err)
	resp = r.(*terminateForciblyResp)
	assert.Equal(fmt.Sprintf("%.2f", float64(exceedTerminateForciblyPrice)/100), resp.Price)
	assert.Empty(resp.Link)

	require.NoError(contract2.DB().Where("id = ?", contract2.ID).Updates(handler.M{"attr": 1}).Error)

	// 测试合约期内申请过协商解约
	c = handler.NewTestContext(http.MethodPost, "application/terminateforcibly", true, nil)
	c.User().ID = testLiveID3
	r, err = ActionApplicationTerminateForcibly(c)
	assert.NoError(err)
	resp = r.(*terminateForciblyResp)
	assert.Equal(fmt.Sprintf("%.2f", float64(exceedTerminateForciblyPrice)/100), resp.Price)
	assert.Regexp(regexp.MustCompile(`^https?:\/\/.+`), resp.Link)

	defer func() {
		transactionlog.ADB().
			Delete("", "suborders_num = ? AND to_id = ?", testGuildID, testLiveID3)

		_, err := livelog.Collection().
			DeleteMany(context, bson.M{"guild_id": testGuildID, "creator_id": testLiveID3})
		assert.NoError(err)
	}()

	// 测试协商解约时有公会清退申请
	ca2 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "协商解约_公会申请清退过该主播",
		Type:             contractapplyment.TypeGuildExpel,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       now.AddDate(0, 0, 3).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
	}
	require.NoError(service.DB.Table(contractapplyment.TableName()).Create(&ca2).Error)
	defer service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca2.ID)

	cancel1 := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel1()

	info, err := ActionApplicationTerminateForcibly(ctx)
	require.NoError(err)
	assert.Equal("您已被原公会清退，现已正式解约成功", info)
}

func TestCalculateTerminateForcelyPrice(t *testing.T) {
	assert := assert.New(t)

	// 测试有效开播月数小于 3，近 6 个月总流水大于等于十万元
	params := terminatePriceParams{
		validLiveMonthCount:        2,
		totalSixMonthsRevenue:      20000000,
		validLiveMonthTotalRevenue: 9000000,
	}
	p := terminateParams{
		terminatePriceParams: params,
	}
	price := p.calculateTerminateForciblyPrice()
	assert.Equal(exceedTerminateForciblyPrice, price)

	// 测试有效开播月数小于 3，近 6 个月总流水小于十万元
	p.terminatePriceParams.totalSixMonthsRevenue = 9000000
	price = p.calculateTerminateForciblyPrice()
	assert.Equal(belowTerminateForciblyPrice, price)

	// 测试有效开播月 3 个月，N = 0.5
	p.terminatePriceParams.validLiveMonthCount = 3
	price = p.calculateTerminateForciblyPrice()
	assert.Equal(int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue)*0.5)), price)

	// 测试有效开播月 4 个月，N = 0.4
	p.terminatePriceParams.validLiveMonthCount = 4
	price = p.calculateTerminateForciblyPrice()
	assert.Equal(int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue)*0.4)), price)

	// 测试有效开播月 5 个月，N = 0.32
	p.terminatePriceParams.validLiveMonthCount = 5
	price = p.calculateTerminateForciblyPrice()
	assert.Equal(int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue)*0.32)), price)

	// 测试有效开播月 6 个月，N = 0.25
	p.terminatePriceParams.validLiveMonthCount = 6
	price = p.calculateTerminateForciblyPrice()
	assert.Equal(int64(math.Floor(float64(p.terminatePriceParams.validLiveMonthTotalRevenue)*0.25)), price)
}
