package livebackend

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionSign 主播申请签约
/**
* @api {post} /api/v2/guild/livebackend/sign 主播申请签约
* @apiVersion 0.1.0
* @apiName sign
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} guild_id 公会 ID
* @apiParam {number=2,3,4,5} duration 合约时长（2: 十二个月（已弃用），3: 二十四个月（已弃用），4: 3 年，5: 5 年）
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "申请成功"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (403) {Number} code 500050010  todo
* @apiError (403) {String} info 您已经在此公会中，无需重复加入
*
* @apiError (403) {Number} code 500050002
* @apiError (403) {String} info 已加入另一公会，不能加入多个公会
*
* @apiError (404) {Number} code 500050001
* @apiError (404) {String} info 公会不存在
*
* @apiError (403) {Number} code 500050003
* @apiError (403) {String} info 已经申请该公会，请等待公会答复
*
* @apiError (403) {Number} code 500050009
* @apiError (403) {String} info 该公会已邀请您签约，请先处理签约邀请
 */
func ActionSign(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID  int64 `json:"guild_id"`
		Duration int64 `json:"duration"`
	}
	err := c.BindJSON(&params)
	if err != nil || params.GuildID <= 0 || !contractapplyment.IsLegalContractDuration(params.Duration) {
		return "", actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	// 是否已加入公会
	var lc livecontract.LiveContract
	err = service.DB.
		Table(livecontract.TableName()).
		Select("guild_id").
		Where("live_id = ? AND status = ? AND contract_end > ?",
			c.UserID(), livecontract.StatusContracting, now.Unix()).
		Scan(&lc).Error

	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	if lc.GuildID > 0 {
		if lc.GuildID == params.GuildID {
			return "", actionerrors.ErrAlreadyJoinGuild
		}
		return "", actionerrors.ErrCannotJoinTwice
	}

	// 公会是否存在
	var guildData guild.Guild
	err = service.DB.
		Table(guild.TableName()).
		Select("id, name").
		Where("id = ? AND checked = ?", params.GuildID, guild.CheckedPass).
		Scan(&guildData).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrGuildNotExist
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 是否已发送签约申请或公会是否已邀请
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id, type").
		Where("type IN (?)", []int64{contractapplyment.TypeLiveSign, contractapplyment.TypeGuildSign}).
		Where("live_id = ? AND guild_id = ? AND status = ? AND expire_time > ?",
			c.UserID(), params.GuildID, contractapplyment.StatusPending, now.Unix()).
		Scan(&applyment).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.ID > 0 {
		if applyment.Type == contractapplyment.TypeGuildSign {
			return "", actionerrors.ErrGuildAlreadyInviteSign
		}
		return "", actionerrors.ErrAlreadyRequestJoin
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 创建签约申请
	newApplyment := contractapplyment.ContractApplyment{
		LiveID:             c.UserID(),
		GuildID:            guildData.ID,
		GuildName:          guildData.Name,
		ContractID:         0,
		ContractDuration:   params.Duration,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeLiveSign,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               guildrate.RatePercent45,
	}

	err = service.DB.Create(&newApplyment).Error
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	return "申请成功", nil
}
