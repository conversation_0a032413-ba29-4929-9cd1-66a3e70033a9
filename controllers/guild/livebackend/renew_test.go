package livebackend

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRenew(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	params := map[string]interface{}{"duration": 10}
	data, _ := json.Marshal(params)
	ctx := handler.NewTestContext("POST", "/api/v2/guild/livebackend/renew", true, bytes.NewBuffer(data))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionRenew(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)

	// 尚未签约需要先申请签约
	ctx.User().ID = testLiveID1
	require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "live_id = ?", ctx.User().ID).Error)
	params = map[string]interface{}{"duration": contractapplyment.ContractDurationThreeYear}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/renew", bytes.NewBuffer(data))
	_, err = ActionRenew(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrShouldJoinGuild, err)

	// 续约需要在合约到期前 15 天发起
	ctx.User().ID = testLiveID2
	contract := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      ctx.User().ID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "测试合约要在到期前 15 天发起",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract.ID)
	}()
	params = map[string]interface{}{"duration": contractapplyment.ContractDurationThreeYear}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/renew", bytes.NewBuffer(data))
	_, err = ActionRenew(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrRenewTooEarly, err)

	now := goutil.TimeNow()
	// 续约申请已经发起，不能重复申请
	ctx.User().ID = testLiveID3
	contract2 := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      ctx.User().ID,
		ContractEnd: now.AddDate(0, 0, 11).Unix(),
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "测试续约申请已发起不能重复申请",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract2).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract2.ID)
	}()
	applyment := contractapplyment.ContractApplyment{
		GuildID:    testGuildID,
		LiveID:     ctx.User().ID,
		Type:       contractapplyment.TypeLiveRenew,
		Status:     contractapplyment.StatusPending,
		ExpireTime: now.AddDate(0, 0, 10).Unix(),
		Initiator:  contractapplyment.InitiatorLive,
		GuildName:  "测试续约申请已发起不能重复申请",
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment.ID)
	}()
	params = map[string]interface{}{"duration": contractapplyment.ContractDurationThreeYear}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/renew", bytes.NewBuffer(data))
	_, err = ActionRenew(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentPending, err)

	ctx.User().ID = testLiveID4
	contract3 := livecontract.LiveContract{
		GuildID:     testGuildID,
		LiveID:      ctx.User().ID,
		ContractEnd: now.AddDate(0, 0, 11).Unix(),
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "测试续约申请成功",
	}
	err = service.DB.Table(livecontract.TableName()).Create(&contract3).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract3.ID)
	}()
	params = map[string]interface{}{"duration": contractapplyment.ContractDurationThreeYear}
	data, _ = json.Marshal(params)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = true
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/renew", bytes.NewBuffer(data))
	ctx.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionRenew(ctx)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = false

	// 申请成功
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/renew", bytes.NewBuffer(data))
	r, err := ActionRenew(ctx)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "live_id = ? AND guild_id = ?", testLiveID4, testGuildID)
	}()
	require.Nil(err)
	assert.Equal("申请成功", r)

	// 测试 rate
	var dbApplyment contractapplyment.ContractApplyment
	require.NoError(service.DB.Table(contractapplyment.TableName()).
		Where("live_id = ? AND guild_id = ?", ctx.User().ID, testGuildID).
		Find(&dbApplyment).Error)
	assert.Equal(guildrate.RatePercent45, dbApplyment.Rate)
}
