package livebackend

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionApplymentDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/livebackend/application/detail?applyment_id=-100", true, nil)
	c.User().ID = testLiveID1
	_, err := ActionApplymentDetail(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)

	// 申请不存在
	c = handler.NewTestContext(http.MethodGet, "/livebackend/application/detail?applyment_id=987654321", true, nil)
	c.User().ID = testLiveID1
	_, err = ActionApplymentDetail(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentNotExist, err)

	// 获取详情
	nowUnix := goutil.TimeNow().Unix() - 1
	applyment1 := contractapplyment.ContractApplyment{
		LiveID:           testLiveID1,
		GuildID:          testGuildID,
		GuildName:        "申请已失效",
		Type:             contractapplyment.TypeLiveSign,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		ExpireTime:       nowUnix,
		ProcessTime:      nowUnix,
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
		Rate:             guildrate.RatePercent50,
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment1).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment1.ID)
	}()

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/livebackend/application/detail?applyment_id=%d",
		applyment1.ID), true, nil)
	c.User().ID = testLiveID1
	r, err := ActionApplymentDetail(c)
	require.NoError(err)

	resp := r.(applymentDetail)
	assert.Equal(applyment1.ID, resp.ID)
	assert.Equal(contractapplyment.StatusOutdated, resp.Status)
	assert.Equal("50%", resp.RateStr)
	assert.NotZero(resp.ProcessTime)
}
