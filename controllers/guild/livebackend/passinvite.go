package livebackend

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionPassInvite 主播同意公会邀请
/**
* @api {post} /api/v2/guild/livebackend/passinvite 主播同意公会邀请
* @apiDescription 主播同意续约后还未处理的公会降薪申请失效
* @apiVersion 0.1.0
* @apiName passinvite
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} applyment_id 合约申请 ID
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "签约成功"
*   }
*
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "续约成功"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (404) {Number} code 500050016
* @apiError (404) {String} info 该邀请不存在
*
* @apiError (403) {Number} code 500050015
* @apiError (403) {String} info 不可操作已被处理或已失效的申请
*
* @apiError (403) {Number} code 500050002
* @apiError (403) {String} info 已加入另一公会，不能加入多个公会
 */
func ActionPassInvite(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		ApplymentID int64 `json:"applyment_id"`
	}
	err := c.BindJSON(&params)
	if err != nil || params.ApplymentID <= 0 {
		return "", actionerrors.ErrParams
	}

	// 邀请是否存在
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Where("type IN (?)", []int64{contractapplyment.TypeGuildSign, contractapplyment.TypeGuildRenew}).
		Where("id = ? AND live_id = ? AND initiator = ?", params.ApplymentID, c.UserID(), contractapplyment.InitiatorGuild).
		Scan(&applyment).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrInvitationNotExist
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.Status != contractapplyment.StatusPending || applyment.IsExpired() {
		return "", actionerrors.ErrApplymentFreezed
	}

	// 现有合约情况
	var contract livecontract.LiveContract
	err = service.DB.
		Table(livecontract.TableName()).
		Select("id, guild_id, contract_end, guild_owner").
		Where("live_id = ? AND status = ? AND contract_end > ?",
			c.UserID(), livecontract.StatusContracting, goutil.TimeNow().Unix()).
		Scan(&contract).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if err = checkPermission(c.UserID(), c.Token()); err != nil {
		return nil, err
	}

	// 同意公会签约邀请
	if applyment.Type == contractapplyment.TypeGuildSign {
		err = passSignInvitation(&contract, &applyment)
		if applyment.AgentID > 0 {
			err = guildagent.AgentCreator{}.DB().Create(&guildagent.AgentCreator{
				GuildID:   applyment.GuildID,
				CreatorID: applyment.LiveID,
				AgentID:   applyment.AgentID,
			}).Error
			if err != nil {
				return "", actionerrors.NewErrServerInternal(err, nil)
			}
		}
	}
	// 同意公会续约邀请
	if applyment.Type == contractapplyment.TypeGuildRenew {
		err = passRenewInvitation(&contract, &applyment)
	}
	if err != nil {
		return "", err
	}
	// 同意公会协议
	agreeGuildAgreement(c.UserID())
	// 发送系统消息
	var sysTitle, sysContent, inviteType string
	var guildOwner int64
	if applyment.Type == contractapplyment.TypeGuildSign {
		inviteType = "签约"
		err = service.DB.Table(guild.TableName()).
			Select("user_id").
			Where("id = ?", applyment.GuildID).
			Row().Scan(&guildOwner)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	} else {
		inviteType = "续约"
		guildOwner = contract.GuildOwner
	}
	sysTitle = fmt.Sprintf("您的%s邀请已通过", inviteType)
	sysContent = fmt.Sprintf("主播 %s（MID：%d）已通过您的直播公会%s邀请。您于%s与该主播成功%s。",
		c.User().Username, c.UserID(), inviteType, goutil.TimeNow().Format(" 2006 年 01 月 02 日"), inviteType)
	err = messageassign.SystemMessageAssign(guildOwner, sysTitle, sysContent)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return inviteType + "成功", nil
}

// 同意公会签约邀请
func passSignInvitation(contract *livecontract.LiveContract, applyment *contractapplyment.ContractApplyment) error {
	if contract.ID > 0 {
		return actionerrors.ErrCannotJoinTwice
	}
	var guildData guild.Guild
	err := service.DB.
		Table(guild.TableName()).
		Select("id, name, user_id").
		Where("id = ? AND checked = ?", applyment.GuildID, guild.CheckedPass).
		Scan(&guildData).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return actionerrors.ErrGuildNotExist
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		nowStamp := goutil.TimeNow().Unix()
		// 创建合约
		contract := livecontract.LiveContract{
			GuildID:          guildData.ID,
			LiveID:           applyment.LiveID,
			ContractStart:    nowStamp,
			ContractEnd:      contractapplyment.GetContractEnd(time.Unix(nowStamp, 0), applyment.ContractDuration).Unix(),
			ContractDuration: applyment.ContractDuration,
			Rate:             guildrate.ApplymentRate(applyment.Rate),
			KPI:              "",
			Status:           livecontract.StatusContracting,
			Type:             livecontract.FromGuild,
			GuildOwner:       guildData.UserID,
			GuildName:        applyment.GuildName,
		}
		err := tx.Create(&contract).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 更新主播数冗余字段
		err = guild.IncreaseLiveNum(guildData.ID, 1, tx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 同意合约申请
		err = tx.Table(contractapplyment.TableName()).
			Where("id = ? AND status = ?", applyment.ID, contractapplyment.StatusPending).
			Updates(map[string]interface{}{
				"status":               contractapplyment.StatusAgreed,
				"process_time":         nowStamp,
				"modified_time":        nowStamp,
				"contract_id":          contract.ID,
				"contract_expire_time": contract.ContractEnd,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 使其它的签约申请失效
		err = tx.Table(contractapplyment.TableName()).
			Where("type IN (?)", []int64{contractapplyment.TypeGuildSign, contractapplyment.TypeLiveSign}).
			Where("status = ? AND live_id = ?", contractapplyment.StatusPending, applyment.LiveID).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusInvalid,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 更新直播间公会信息
		err = room.UpdateGuildID(applyment.LiveID, guildData.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	})
}

// 同意公会续约邀请
func passRenewInvitation(contract *livecontract.LiveContract, applyment *contractapplyment.ContractApplyment) error {
	if contract.ID == 0 {
		return actionerrors.ErrContractNotExist
	}
	if contract.GuildID != applyment.GuildID {
		return actionerrors.ErrCannotJoinTwice
	}

	nowStamp := goutil.TimeNow().Unix()
	newContractEnd := contractapplyment.GetContractEnd(time.Unix(contract.ContractEnd, 0), applyment.ContractDuration)
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 未处理的降薪申请失效
		err := contractapplyment.ApplicationEditRateInvalid(contract.GuildID, applyment.LiveID, tx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		err = tx.Table(contractapplyment.TableName()).
			Where("id = ? AND status = ?",
				applyment.ID, contractapplyment.StatusPending).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusAgreed,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		err = tx.Table(livecontract.TableName()).
			Where("id = ?", contract.ID).
			Updates(map[string]interface{}{
				"contract_end":      newContractEnd.Unix(),
				"contract_duration": applyment.ContractDuration,
				"rate":              guildrate.ApplymentRate(applyment.Rate),
				// 续约后重置主播在合约期内是否申请过协商解约的状态
				"attr":          livecontract.UnsetAttrBitMaskExpr(livecontract.AttrBitMaskLiveTerminated),
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 更新直播间公会信息
		err = room.UpdateGuildID(applyment.LiveID, contract.GuildID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	})
}

// 同意公会协议
func agreeGuildAgreement(userID int64) {
	err := liveaddendum.Agree(userID, liveaddendum.AgreeGuildAgreement)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
