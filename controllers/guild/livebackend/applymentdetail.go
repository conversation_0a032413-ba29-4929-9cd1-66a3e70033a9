package livebackend

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type applymentDetail struct {
	ID int64 `gorm:"column:id;primary_key" json:"id"`

	UserID    int64  `gorm:"column:user_id" json:"user_id"`
	GuildID   int64  `gorm:"column:guild_id" json:"guild_id"`
	GuildName string `gorm:"column:guild_name" json:"guild_name"`

	ContractID         int64  `gorm:"column:contract_id" json:"-"`
	ContractDuration   int64  `gorm:"column:contract_duration" json:"-"`
	ContractExpireTime int64  `gorm:"column:contract_expire_time" json:"contract_expire_time"`
	Duration           string `gorm:"-" json:"duration"`

	Type        int64  `gorm:"column:type" json:"type"`
	Status      int64  `gorm:"column:status" json:"status"`
	ExpireTime  int64  `gorm:"column:expire_time" json:"expire_time"`
	LeftTime    string `gorm:"-" json:"left_time"`
	ProcessTime int64  `gorm:"column:process_time" json:"process_time,omitempty"`

	Rate       int    `gorm:"column:rate" json:"-"`
	RateStr    string `gorm:"-" json:"rate_string"`
	Settlement string `gorm:"-" json:"settlement"`

	CreateTime int64 `gorm:"column:create_time" json:"create_time"`
}

// ActionApplymentDetail 合约申请内容弹窗
/**
 * @api {get} /api/v2/guild/livebackend/application/detail 合约申请内容弹窗
 * @apiVersion 0.1.0
 * @apiName applymentdetail
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiParam {Number} applyment_id 合约申请 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "id": 6,  // 合约申请 ID
 *       "user_id": 3013100,  // 会长 ID
 *       "guild_id": 3954,  // 公会 ID
 *       "guild_name": "测试公会",  // 公会名称
 *       "contract_expire_time": 0,  // 合约延长至
 *       "duration": "6 个月",  // 签约时限
 *       "type": 4,
 *       "status": 1, // -4 已过期（超时未处理），-3 已失效（公会/用户状态发生变更），-2 已撤回，-1 已拒绝，0 未处理，1 已同意
 *       "expire_time": 1575501304,  // 失效时间
 *       "left_time": "2 天",  // 合约有效期剩余时长
 *       "process_time": 1575167704,  // 合约处理时间，秒级时间戳，合约未被处理时不下发该字段
 *       "rate_string": "45%",  // 分成比例
 *       "settlement": "对公结算",  // 结算方式
 *       "create_time": 1575167704 // 申请时间，秒级时间戳
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 500050014
 * @apiError (403) {String} info 该申请不存在
 */
func ActionApplymentDetail(c *handler.Context) (handler.ActionResponse, error) {
	applymentID, err := c.GetParamInt64("applyment_id")
	if err != nil || applymentID <= 0 {
		return nil, actionerrors.ErrParams
	}

	var detail applymentDetail
	err = service.DB.
		Table(contractapplyment.TableName()+" AS c").
		Select("c.id, g.user_id, c.guild_id, g.name AS guild_name, c.contract_id, c.contract_duration, c.contract_expire_time, c.type, c.status, c.expire_time, c.process_time, c.rate, c.create_time").
		Where("c.id = ? AND c.live_id = ?", applymentID, c.UserID()).
		Joins(fmt.Sprintf("LEFT JOIN %s AS g ON g.id = c.guild_id", guild.TableName())).
		Scan(&detail).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrApplymentNotExist
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	detail.Duration = contractapplyment.DurationLabelMap[detail.ContractDuration]
	detail.RateStr = guildrate.FormatRateToString(detail.Rate)
	detail.Settlement = contractapplyment.SettlementDefault
	detail.LeftTime = contractapplyment.GetLeftTimeString(time.Until(time.Unix(detail.ExpireTime, 0)))

	// 超时的申请未被定时任务修改状态时，这里返回的状态需要置为已超时
	if detail.Status == contractapplyment.StatusPending && goutil.TimeNow().Unix() >= detail.ExpireTime {
		detail.Status = contractapplyment.StatusOutdated
		detail.ProcessTime = detail.ExpireTime
	}

	return detail, nil
}
