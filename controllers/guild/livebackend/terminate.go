package livebackend

import (
	"encoding/json"
	"fmt"
	"html"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionTerminate 主播申请协商解约
/**
* @apiDeprecated 由 /api/v2/guild/livebackend/application/terminate 替换
* @api {post} /api/v2/guild/livebackend/terminate 主播申请协商解约
* @apiVersion 0.1.0
* @apiName terminate
* @apiGroup /api/v2/guild/livebackend
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "提交成功"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (403) {Number} code 500050011
* @apiError (403) {String} info 您还未加入公会，请先申请签约
*
* @apiError (403) {Number} code 500050017
* @apiError (403) {String} info 签约未满 180 天，无法解约
*
* @apiError (403) {Number} code 500050013
* @apiError (403) {String} info 申请已发起，请等待答复
*
* @apiError (403) {Number} code 500050018
* @apiError (403) {String} info 请先处理续约邀请
 */
func ActionTerminate(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()

	// 是否已加入公会
	contract, err := livecontract.FindInContractingByLiveID(c.UserID(), 0)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if contract == nil {
		return "", actionerrors.ErrShouldJoinGuild
	}
	// 受限房间禁止退会
	r, err := room.FindOne(bson.M{"creator_id": c.UserID()},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r != nil && r.Limit != nil {
		return nil, actionerrors.ErrNoAuthority
	}
	// 是否处于签约保护期中
	isProtected := livecontract.IsInProtecting(contract.ContractStart)
	if isProtected {
		return nil, actionerrors.ErrContractBeingProtected
	}

	// 是否已发送协商解约申请
	var applyment contractapplyment.ContractApplyment
	err = service.DB.
		Table(contractapplyment.TableName()).
		Select("id, type").
		Where("type IN (?)", []int64{contractapplyment.TypeLiveTerminate, contractapplyment.TypeGuildRenew}).
		Where("live_id = ? AND guild_id = ? AND status = ?",
			c.UserID(), contract.GuildID, contractapplyment.StatusPending).
		Where("expire_time > ?", now.Unix()).
		Scan(&applyment).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment.ID > 0 {
		if applyment.Type == contractapplyment.TypeGuildRenew {
			return "", actionerrors.ErrRenewApplymentPending
		}
		return "", actionerrors.ErrApplymentPending
	}

	// 创建协商解约申请
	newApplyment := contractapplyment.ContractApplyment{
		LiveID:             c.UserID(),
		GuildID:            contract.GuildID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Type:               contractapplyment.TypeLiveTerminate,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, contractapplyment.ExpireDaysTerminateApplyment).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               contract.Rate,
	}

	err = service.DB.Create(&newApplyment).Error
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	return "提交成功", nil
}

type terminateParams struct {
	guildID          int64
	creatorID        int64
	liveContract     *livecontract.LiveContract
	isGuildExpelLive bool // 公会是否申请清退该主播

	terminatePriceParams           terminatePriceParams
	forciblyTerminatePrice         int64
	forciblyTerminateApplicationID int64
	clientIP                       string
}

type terminateCheckResp struct {
	Status int    `json:"status"`        // 状态码
	Msg    string `json:"msg,omitempty"` // 提示信息
}

// ActionApplicationTerminateCheck 主播协商解约检查
/**
 * @api {post} /api/v2/guild/livebackend/application/terminate-check 主播协商解约检查
 * @apiVersion 0.1.0
 * @apiName application/terminate-check
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 0 // 协商解约状态正常
 *     }
 *   }
 *
 * @apiSuccessExample {json} 协商解约时有公会清退申请
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1, // 协商解约时有公会清退申请的状态码
 *       "msg": "您已被原公会清退，现已正式解约成功"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 500050011
 * @apiError (403) {String} info 您还未加入公会，请先申请签约
 *
 * @apiError (403) {Number} code 500050017
 * @apiError (403) {String} info 签约未满 180 天，无法解约
 *
 * @apiError (403) {Number} code 500050013
 * @apiError (403) {String} info 申请已发起，请等待答复
 *
 * @apiError (403) {Number} code 500050018
 * @apiError (403) {String} info 请先处理续约邀请
 */
func ActionApplicationTerminateCheck(c *handler.Context) (handler.ActionResponse, error) {
	params := terminateParams{
		creatorID: c.UserID(),
	}

	// 检查主播方是否满足解约要求
	err := params.CheckCreator()
	if err != nil {
		return nil, err
	}

	// 是否已发送协商解约申请和公会是否已发送续约邀请
	err = params.CheckPendingApplication()
	if err != nil {
		return nil, err
	}

	// 公会是否申请清退该主播，有则通过清退申请
	str, err := params.GuildExpelLive()
	if err != nil {
		return nil, err
	}
	if str != "" {
		return &terminateCheckResp{Status: 1, Msg: str}, nil
	}

	return &terminateCheckResp{Status: 0}, nil
}

// ActionApplicationTerminate 主播申请协商解约
/**
 * @api {post} /api/v2/guild/livebackend/application/terminate 主播申请协商解约
 * @apiVersion 0.1.0
 * @apiName application/terminate
 * @apiGroup /api/v2/guild/livebackend
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample {json} 提交成功
 *   {
 *     "code": 0,
 *     "info": "提交成功"
 *   }
 *
 * @apiSuccessExample {json} 协商解约时有公会清退申请
 *   {
 *     "code": 0,
 *     "info": "您已被原公会清退，现已正式解约成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 500050011
 * @apiError (403) {String} info 您还未加入公会，请先申请签约
 *
 * @apiError (403) {Number} code 500050017
 * @apiError (403) {String} info 签约未满 180 天，无法解约
 *
 * @apiError (403) {Number} code 500050013
 * @apiError (403) {String} info 申请已发起，请等待答复
 *
 * @apiError (403) {Number} code 500050018
 * @apiError (403) {String} info 请先处理续约邀请
 */
func ActionApplicationTerminate(c *handler.Context) (handler.ActionResponse, error) {
	params := terminateParams{
		creatorID: c.UserID(),
	}

	// 检查主播方是否满足解约要求
	err := params.CheckCreator()
	if err != nil {
		return nil, err
	}

	// 是否已发送协商解约申请和公会是否已发送续约邀请
	err = params.CheckPendingApplication()
	if err != nil {
		return nil, err
	}

	// 公会是否申请清退该主播，有则通过清退申请
	str, err := params.GuildExpelLive()
	if err != nil {
		return nil, err
	}
	if str != "" {
		return str, nil
	}

	// 创建协商解约申请
	err = params.createTerminateApplication()
	if err != nil {
		return nil, err
	}

	return "提交成功", nil
}

// createTerminateApplication 创建协商解约申请
func (p *terminateParams) createTerminateApplication() error {
	more := contractapplyment.More{
		Attr: &p.liveContract.Attr,
	}
	moreJSON, err := json.Marshal(more)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	now := goutil.TimeNow()

	// 创建协商解约申请
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		newApplyment := contractapplyment.ContractApplyment{
			LiveID:             p.creatorID,
			GuildID:            p.liveContract.GuildID,
			GuildName:          p.liveContract.GuildName,
			ContractID:         p.liveContract.ID,
			ContractDuration:   p.liveContract.ContractDuration,
			ContractExpireTime: p.liveContract.ContractEnd,
			Type:               contractapplyment.TypeLiveTerminate,
			Status:             contractapplyment.StatusPending,
			ExpireTime:         now.AddDate(0, 0, contractapplyment.ExpireDaysTerminateApplyment).Unix(),
			Initiator:          contractapplyment.InitiatorLive,
			Rate:               p.liveContract.Rate,
			More:               string(moreJSON),
		}
		err = tx.Save(&newApplyment).Error
		if err != nil {
			return err
		}

		// 合约期内第一次申请协商解约时将主播在合约期内是否申请过协商解约的状态修改为是
		if !p.liveContract.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated) {
			db := tx.Table(livecontract.TableName()).
				Where("id = ? AND live_id = ? AND status = ? AND contract_end > ?",
					p.liveContract.ID, p.creatorID, livecontract.StatusContracting, now.Unix()).
				Updates(map[string]interface{}{
					"attr":          livecontract.SetAttrBitMaskExpr(livecontract.AttrBitMaskLiveTerminated),
					"modified_time": now.Unix(),
				})
			if err := db.Error; err != nil {
				return err
			}
			if db.RowsAffected == 0 {
				return servicedb.ErrNoRowsAffected
			}
		}
		return nil
	})
	if err != nil {
		if err == servicedb.ErrNoRowsAffected {
			return actionerrors.ErrContractNotExist
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

// CheckCreator 检查主播方是否满足解约要求
func (p *terminateParams) CheckCreator() error {
	// 三方独家主播不允许解约
	exclusive, err := exclusivecreator.IsExclusiveCreator(p.creatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exclusive {
		return actionerrors.ErrExclusiveCreatorConflicted("您是三方独家主播，无法解约")
	}

	// 是否已加入公会
	contract, err := livecontract.FindInContractingByLiveID(p.creatorID, 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if contract == nil {
		return actionerrors.ErrShouldJoinGuild
	}

	p.guildID = contract.GuildID
	p.liveContract = contract

	// 受限房间禁止退会
	r, err := room.FindOne(bson.M{"creator_id": p.creatorID, "limit": bson.M{"$exists": true}},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r != nil {
		return actionerrors.ErrNoAuthority
	}

	// 是否处于签约保护期中
	isProtected := livecontract.IsInProtecting(p.liveContract.ContractStart)
	if isProtected {
		return actionerrors.ErrContractBeingProtected
	}

	return nil
}

// GuildExpelLive 公会是否申请清退该主播，有则通过清退申请
func (p *terminateParams) GuildExpelLive() (string, error) {
	if !p.isGuildExpelLive {
		return "", nil
	}

	// 通过清退申请
	err := livecontract.AgreeExpelGuildLive(p.guildID, p.creatorID)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 清退后给公会长和主播发系统通知
	p.AfterExpelLiveSendSystemMsg()

	return "您已被原公会清退，现已正式解约成功", nil
}

// CheckPendingApplication 主播是否已发送协商解约申请和公会是否已发送续约邀请
func (p *terminateParams) CheckPendingApplication() error {
	types := []int64{
		contractapplyment.TypeLiveTerminate,
		contractapplyment.TypeGuildRenew,
		contractapplyment.TypeGuildExpel,
	}
	var cas []*contractapplyment.ContractApplyment
	err := contractapplyment.ContractApplyment{}.DB().Select("id, type").
		Where("type IN (?) AND live_id = ? AND guild_id = ? AND status = ?",
			types, p.creatorID, p.guildID, contractapplyment.StatusPending).
		Where("expire_time > ?", goutil.TimeNow().Unix()).
		Scan(&cas).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 公会发起续约邀请时会让公会先处理相关未处理的申请，主播发起协商解约时会让主播先处理未处理的续约邀请
	// 未处理的续约邀请和未处理的协商解约申请不会同时存在
	if len(cas) > 0 {
		for _, ca := range cas {
			switch ca.Type {
			case contractapplyment.TypeGuildRenew:
				return actionerrors.ErrRenewApplymentPending
			case contractapplyment.TypeLiveTerminate:
				return actionerrors.ErrApplymentPending
			case contractapplyment.TypeGuildExpel:
				p.isGuildExpelLive = true
			default:
				panic("type is wrong")
			}
		}
	}

	return nil
}

// AfterExpelLiveSendSystemMsg 清退主播后给公会长和主播发系统通知
func (p *terminateParams) AfterExpelLiveSendSystemMsg() {
	u, err := mowangskuser.FindByUserID(p.creatorID)
	if err != nil {
		logger.Error(err)
		return
	}
	if u == nil {
		logger.Errorf("主播 %d 不存在", p.creatorID)
		return
	}

	g, err := guild.Find(p.guildID)
	if err != nil {
		logger.Error(err)
		return
	}
	if g == nil {
		logger.Errorf("公会 %d 不存在", p.guildID)
		return
	}

	todayStr := " " + goutil.TimeNow().Format(util.TimeFormatChineseYMD)

	// 系统通知
	sysMsgs := []pushservice.SystemMsg{
		// 主播通知
		{
			Title:   "公会已与您解约",
			UserID:  p.creatorID,
			Content: fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 于%s与您解约。", html.EscapeString(g.Name), todayStr),
		},
		// 公会通知
		{
			Title:   "主播已被清退",
			UserID:  g.UserID,
			Content: fmt.Sprintf("主播 %s（MID：%d）已被清退。您于%s与该主播成功解约。", html.EscapeString(u.Username), p.creatorID, todayStr),
		},
	}
	err = service.PushService.SendSystemMsg(sysMsgs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
