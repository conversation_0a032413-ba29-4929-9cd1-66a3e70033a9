package livebackend

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
)

func TestAgreeGuildAgreement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(20210604)
	require.NoError(service.DB.Table(liveaddendum.TableName()).Delete("", "user_id = ?", testUserID).Error)

	agreeGuildAgreement(testUserID)
	la, err := liveaddendum.Find(testUserID)
	require.NoError(err)
	require.NotNil(la)
	assert.True(la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement))
}
