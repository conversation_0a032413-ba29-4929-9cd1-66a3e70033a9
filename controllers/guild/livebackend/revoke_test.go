package livebackend

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRevoke(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	ctx := handler.CreateTestContext(true)
	ctx.User().ID = testLiveID1
	params := map[string]int64{"applyment_id": -100}
	data, _ := json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))
	ctx.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionRevoke(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)

	// 申请未发送
	params = map[string]int64{"applyment_id": 987654321}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))
	_, err = ActionRevoke(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentNotExist, err)

	now := goutil.TimeNow()
	// 申请已超时失效
	ctx.User().ID = testLiveID2
	applyment1 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "申请已失效",
		Type:             contractapplyment.TypeLiveSign,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       now.AddDate(0, 0, -1).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment1).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment1.ID)
	}()
	params = map[string]int64{"applyment_id": applyment1.ID}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))
	_, err = ActionRevoke(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrApplymentFreezed, err)

	// 未到撤回的时间
	ctx.User().ID = testLiveID3
	applyment2 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "未到撤回的时间",
		Type:             contractapplyment.TypeLiveSign,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
	}
	err = service.DB.Table(contractapplyment.TableName()).Create(&applyment2).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "id = ?", applyment2.ID)
	}()
	params = map[string]int64{"applyment_id": applyment2.ID}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))
	_, err = ActionRevoke(ctx)
	require.Error(err)
	assert.Equal(applymentRevokeTip[contractapplyment.TypeLiveSign], err.Error())

	// 测试用户需要验证后才可操作
	require.NoError(applyment2.DB().Update("create_time", now.AddDate(0, 0, -2).Unix()).Error)
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = true
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))
	ctx.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionRevoke(ctx)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = false

	ctx.User().ID = testLiveID4
	contract := livecontract.LiveContract{
		GuildID:       testGuildID,
		LiveID:        ctx.User().ID,
		GuildOwner:    8,
		Status:        livecontract.StatusContracting,
		Attr:          1,
		ContractStart: 23333333,
		ContractEnd:   now.AddDate(2, 0, 0).Unix(),
		GuildName:     "测试撤回协商解约申请",
	}
	require.NoError(contract.DB().Create(&contract).Error)

	var attr goutil.BitMask
	more := contractapplyment.More{
		Attr: &attr,
	}
	moreJSON, err := json.Marshal(more)
	require.NoError(err)

	applyment3 := contractapplyment.ContractApplyment{
		LiveID:           ctx.User().ID,
		GuildID:          testGuildID,
		GuildName:        "撤回成功",
		ContractID:       contract.ID,
		Type:             contractapplyment.TypeLiveTerminate,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       now.AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorLive,
		More:             string(moreJSON), // 申请协商解约前主播在合约期内申请过协商解约的状态为否
	}
	require.NoError(applyment3.DB().Create(&applyment3).Error)
	defer func() {
		applyment3.DB().Delete("", "id = ?", applyment3.ID)
		contract.DB().Delete("", "id = ?", contract.ID)
	}()

	params = map[string]int64{"applyment_id": applyment3.ID}
	data, _ = json.Marshal(params)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/livebackend/revoke", bytes.NewBuffer(data))

	// 撤回成功
	r, err := ActionRevoke(ctx)
	require.NoError(err)
	assert.Equal("撤回成功", r)

	// 测试撤回协商解约时，如果申请协商解约前「主播在合约期内是否申请过协商解约的状态」为否时重置「主播在合约期内是否申请过协商解约的状态」
	contract1 := new(livecontract.LiveContract)
	require.NoError(contract1.DB().Where("id = ?", contract.ID).First(&contract1).Error)
	assert.False(contract1.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated))
}
