package livebackend

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSign(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	params := map[string]interface{}{"duration": 10, "guild_id": testGuildID}
	ctx := handler.NewTestContext("POST", "/", true, params)
	_, err := ActionSign(ctx)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)

	// 已加入公会，不可重复加入
	contract := livecontract.LiveContract{
		GuildID:     5,
		LiveID:      testLiveID1,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusContracting,
		Type:        livecontract.FromGuild,
		GuildOwner:  100008,
		GuildName:   "测试主播已加入公会",
	}
	require.NoError(service.DB.Table(contract.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND status = ?",
			testLiveID1, contract.GuildID, livecontract.StatusContracting).Error)
	err = service.DB.
		Table(livecontract.TableName()).
		Create(&contract).Error
	require.NoError(err)
	defer func() {
		service.DB.
			Table(livecontract.TableName()).
			Delete("", "id = ?", contract.ID)
	}()
	params = map[string]interface{}{"duration": contractapplyment.ContractDurationFiveYear, "guild_id": testGuildID}
	ctx = handler.NewTestContext("POST", "/", true, params)
	ctx.User().ID = testLiveID1
	_, err = ActionSign(ctx)
	assert.Equal(actionerrors.ErrCannotJoinTwice, err)

	// 公会已经发起签约邀请
	require.NoError(service.DB.Table(contract.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND status = ?",
			testLiveID2, testGuildID, livecontract.StatusContracting).Error)
	applyment := contractapplyment.ContractApplyment{
		LiveID:           testLiveID2,
		GuildID:          testGuildID,
		GuildName:        "测试公会已发送签约邀请",
		Type:             contractapplyment.TypeGuildSign,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ExpireTime:       goutil.TimeNow().AddDate(0, 0, 7).Unix(),
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
	}
	err = service.DB.
		Table(contractapplyment.TableName()).
		Create(&applyment).Error
	require.NoError(err)
	ctx = handler.NewTestContext("POST", "/", true, params)
	ctx.User().ID = testLiveID2
	_, err = ActionSign(ctx)
	assert.EqualError(err, actionerrors.ErrGuildAlreadyInviteSign.Message)

	require.NoError(service.DB.Table(contract.TableName()).
		Delete("", "live_id = ? AND guild_id = ? AND status = ?",
			testLiveID3, testGuildID, livecontract.StatusContracting).Error)

	// 测试用户需要验证后才可操作
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = true
	ctx = handler.NewTestContext("POST", "/", true, params)
	ctx.User().ID = testLiveID3
	ctx.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testToken"})
	_, err = ActionSign(ctx)
	assert.EqualError(err, "用户需要身份验证")
	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = false

	// 申请成功
	ctx = handler.NewTestContext("POST", "/", true, params)
	ctx.User().ID = testLiveID3
	r, err := ActionSign(ctx)
	defer func() {
		service.DB.
			Table(contractapplyment.TableName()).
			Delete("", "live_id = ? AND guild_id = ?", ctx.User().ID, testGuildID)
	}()
	require.NoError(err)
	assert.Equal("申请成功", r)

	// 测试 rate
	var dbApplyment contractapplyment.ContractApplyment
	require.NoError(service.DB.Table(contractapplyment.TableName()).
		Where("live_id = ? AND guild_id = ?", ctx.User().ID, testGuildID).
		Find(&dbApplyment).Error)
	assert.Equal(guildrate.RatePercent45, dbApplyment.Rate)
}
