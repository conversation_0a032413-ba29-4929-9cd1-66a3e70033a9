package livebackend

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionDeclineInvite(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有收到邀请的情况
	params := map[string]int64{"applyment_id": 999999999999}
	c := handler.NewTestContext(http.MethodPost, "declineinvite", true, params)
	_, err := ActionDeclineInvite(c)
	assert.Equal(actionerrors.ErrGuildNotInvite, err)

	// 测试正常拒绝邀请
	lc := &contractapplyment.ContractApplyment{
		GuildID:    3,
		LiveID:     1502,
		Status:     contractapplyment.StatusPending,
		Type:       contractapplyment.TypeGuildRenew,
		Initiator:  contractapplyment.InitiatorGuild,
		ExpireTime: goutil.TimeNow().Add(1 * time.Minute).Unix(),
	}
	err = service.DB.Create(lc).Error
	require.NoError(err)
	params = map[string]int64{"applyment_id": lc.ID}
	c = handler.NewTestContext(http.MethodPost, "declineinvite", true, params)
	c.User().ID = lc.LiveID
	_, err = ActionDeclineInvite(c)
	require.NoError(err)
	lc, err = contractapplyment.FindByID(lc.ID)
	require.NoError(err)
	require.NotNil(lc)
	assert.NotZero(lc.ProcessTime)
}
