package livebackend

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type paramsEditRate struct {
	ApplicationID int64 `json:"application_id"`
}

// ActionRateAgree 主播同意公会降低最低分成比例
/**
* @api {post} /api/v2/guild/livebackend/rate/agree 主播同意公会降低最低分成比例
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} application_id 合约申请 ID
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "2006-01-02 15:04 已接受调整"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (404) {Number} code 500050016
* @apiError (404) {String} info 该邀请不存在
*
* @apiError (403) {Number} code 500050015
* @apiError (403) {String} info 不可操作已被处理或已失效的申请
*
 */
func ActionRateAgree(c *handler.Context) (handler.ActionResponse, error) {
	var params paramsEditRate
	err := c.Bind(&params)
	if err != nil || params.ApplicationID <= 0 {
		return nil, actionerrors.ErrParams
	}
	res, err := params.operateApplication(c, contractapplyment.AgreeEditRate)
	return res, err
}

// ActionRateDisagree 主播拒绝公会降低最低分成比例
/**
* @api {post} /api/v2/guild/livebackend/rate/disagree 主播拒绝公会降低最低分成比例
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} application_id 合约申请 ID
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": "2006-01-02 15:04 已拒绝调整"
*   }
*
* @apiError (400) {Number} code 501010000
* @apiError (400) {String} info 参数错误
*
* @apiError (404) {Number} code 500050016
* @apiError (404) {String} info 该邀请不存在
*
* @apiError (403) {Number} code 500050015
* @apiError (403) {String} info 不可操作已被处理或已失效的申请
*
 */
func ActionRateDisagree(c *handler.Context) (handler.ActionResponse, error) {
	var params paramsEditRate
	err := c.Bind(&params)
	if err != nil || params.ApplicationID <= 0 {
		return nil, actionerrors.ErrParams
	}
	res, err := params.operateApplication(c, contractapplyment.DisagreeEditRate)
	return res, err
}

func (params *paramsEditRate) operateApplication(ctx *handler.Context, operate int) (handler.ActionResponse, error) {
	// 获取申请记录
	application := new(contractapplyment.ContractApplyment)
	err := application.DB().Where("type = ?", contractapplyment.TypeRateDown).
		Where("id = ? AND live_id = ? AND initiator = ?", params.ApplicationID, ctx.User().ID, contractapplyment.InitiatorGuild).
		First(application).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return "", actionerrors.ErrApplymentNotExist
		}
		return "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取申请发起前主播与公会最低分成比例
	more, err := application.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if more == nil || more.Rate == nil {
		return nil, actionerrors.ErrReviseGuildRate("数据异常，请联系客服处理")
	}

	// 未在规定时间操作申请，返回错误
	endTime := time.Unix(application.ExpireTime, 0)
	if application.IsExpired() || application.Status == contractapplyment.StatusOutdated {
		message := fmt.Sprintf("未在 %s 前处理，系统已自动拒绝调整", endTime.Format(util.TimeFormatYMDHHMM))
		return nil, actionerrors.ErrReviseGuildRate(message)
	}

	// 申请已被操作
	if application.Status != contractapplyment.StatusPending {
		return "", actionerrors.ErrApplymentFreezed
	}

	// 判断用户是否是公会的签约主播
	contract := new(livecontract.LiveContract)
	err = contract.DB().First(contract,
		"guild_id  = ? AND live_id = ? AND status = ?",
		application.GuildID, application.LiveID, livecontract.StatusContracting).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrReviseGuildRate("您已不属于此公会，该申请自动失效")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var resp string
	var title string
	var message string
	now := goutil.TimeNow()
	switch operate {
	case contractapplyment.AgreeEditRate:
		title = "主播接受最低分成比例调整"
		message = fmt.Sprintf("主播 %s 于 %s 已接受最低分成比例调整（%d%% → %d%%），调整已实时生效。",
			ctx.User().Username, now.Format(util.TimeFormatYMDHHMM), *more.Rate, application.Rate)
		resp = fmt.Sprintf("%s 已接受调整", now.Format(util.TimeFormatYMDHHMM))
	case contractapplyment.DisagreeEditRate:
		title = "主播拒绝最低分成比例调整"
		message = fmt.Sprintf("主播 %s 于 %s 已拒绝最低分成比例调整（%d%% → %d%%），%s 后可再次申请。",
			ctx.User().Username, now.Format(util.TimeFormatYMDHHMM), *more.Rate, application.Rate, endTime.Format(util.TimeFormatYMDHHMM))
		resp = fmt.Sprintf("%s 已拒绝调整", now.Format(util.TimeFormatYMDHHMM))
	default:
		panic("错误的操作类型")
	}

	// 更新数据
	if err = contract.UpdateGuildRate(operate, application); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 发送系统通知
	sysMsg := []pushservice.SystemMsg{{
		Title:   title,
		UserID:  contract.GuildOwner,
		Content: message,
	}}
	err = service.PushService.SendSystemMsgWithOptions(sysMsg, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return resp, nil
}

type respEditRateDetail struct {
	Rate         int    `json:"rate"`
	EditRate     int    `json:"edit_rate"`
	Status       int64  `json:"status"`
	GuildName    string `json:"guild_name"`
	ExpireTime   int64  `json:"expire_time"`
	CreateTime   int64  `json:"create_time"`
	ModifiedTime int64  `json:"modified_time"`
}

// ActionRateInfo 主播获取公会修改主播最低分成比例详情
/**
* @api {get} /api/v2/guild/livebackend/rate/info 主播获取公会修改主播最低分成比例详情
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild/livebackend
*
* @apiParam {Number} application_id 合约 ID
*
* @apiSuccessExample Success-Response:
*   {
*     "code": 0,
*     "info": {
*       "rate": 45, // 申请发起前主播与公会最低分成比例
*       "edit_rate": 43, // 公会申请最低分成比例
*       "guild_name": "剑来" // 公会名
*       "status": 1, // 合约状态，-3：已失效；-1：已拒绝；0：待处理；1：已同意；2：超时系统拒绝
*       "expire_time": 123123123, // 合约过期时间
*       "create_time": 123456233, // 合约创建时间
*       "modified_time": 123456123 // 合约更新时间
*     }
*   }
*
* @apiError (500) {Number} code 100010002
* @apiError (500) {String} info 数据库错误
*
* @apiError (403) {Number} code 200020003
* @apiError (403) {String} info 用户没有权限
 */
func ActionRateInfo(c *handler.Context) (handler.ActionResponse, error) {
	id, err := c.GetParamInt64("application_id")
	if err != nil || id <= 0 {
		return nil, actionerrors.ErrParams
	}
	application := new(contractapplyment.ContractApplyment)
	err = application.DB().Where("id = ?", id).Find(&application).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取申请发起前主播与公会最低分成比例
	more, err := application.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if more == nil || more.Rate == nil {
		return nil, actionerrors.ErrReviseGuildRate("数据异常，请联系客服处理")
	}

	// 返回数据
	resp := respEditRateDetail{
		Rate:         *more.Rate,
		EditRate:     application.Rate,
		Status:       application.Status,
		GuildName:    application.GuildName,
		ExpireTime:   application.ExpireTime,
		CreateTime:   application.CreateTime,
		ModifiedTime: application.ModifiedTime,
	}
	if application.Status == contractapplyment.StatusOutdated || application.IsExpired() {
		resp.Status = contractapplyment.StatusOutdated
	}

	// 判断用户是否是公会的签约主播
	contract := new(livecontract.LiveContract)
	err = contract.DB().Select("id").First(contract,
		"guild_id  = ? AND live_id = ? AND status = ?",
		application.GuildID, application.LiveID, livecontract.StatusContracting).Error
	if err != nil {
		if !servicedb.IsErrNoRows(err) {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		// 主播与公会状态发生变化，还未处理的降薪申请置为失效
		if resp.Status == contractapplyment.StatusPending {
			resp.Status = contractapplyment.StatusInvalid
		}
	}
	return resp, nil
}
