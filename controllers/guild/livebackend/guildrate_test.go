package livebackend

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRateAgree(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建主播与公会合约
	now := goutil.TimeNow()
	contract := livecontract.LiveContract{
		LiveID:       1234,
		Status:       livecontract.StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	contract.Attr.Set(livecontract.AttrBitMaskLiveGuildRate)
	require.NoError(contract.DB().Table(contract.TableName()).Create(&contract).Error)
	defer func() {
		require.NoError(contract.DB().Delete("", "id = ?", contract.ID).Error)
	}()

	application := contractapplyment.ContractApplyment{
		GuildID:      3,
		LiveID:       1234,
		Status:       contractapplyment.StatusPending,
		Rate:         40,
		Type:         contractapplyment.TypeRateDown,
		Initiator:    contractapplyment.InitiatorGuild,
		ExpireTime:   now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	application.More = "{\"rate\":45}"
	require.NoError(application.DB().Create(&application).Error)
	defer func() {
		require.NoError(application.DB().Delete("", "id = ?", application.ID).Error)
	}()

	// 参数错误
	ctx := handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/agree", true, nil)
	_, err := ActionRateAgree(ctx)
	assert.EqualError(err, "参数错误")

	// 测试同意降低最低分成比例
	params := paramsEditRate{
		ApplicationID: application.ID,
	}
	ctx = handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/agree", true, params)
	ctx.User().ID = 1234
	res, err := ActionRateAgree(ctx)
	require.NoError(err)
	assert.Contains(res, "已接受调整")

	// 查看数据库数据是否正常
	newApplication := &contractapplyment.ContractApplyment{}
	require.NoError(newApplication.DB().Where("guild_id = ? AND live_id = ?", application.GuildID, application.LiveID).First(newApplication).Error)
	assert.Equal(contractapplyment.StatusAgreed, newApplication.Status)
	newContract := new(livecontract.LiveContract)
	require.NoError(newContract.DB().First(newContract, "id = ?", contract.ID).Error)
	assert.Equal(40, newContract.Rate)
}

func TestActionRateDisagree(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建主播与公会合约
	now := goutil.TimeNow()
	contract := livecontract.LiveContract{
		LiveID:       1234,
		Status:       livecontract.StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	contract.Attr.Set(livecontract.AttrBitMaskLiveGuildRate)
	require.NoError(contract.DB().Table(contract.TableName()).Create(&contract).Error)
	defer func() {
		require.NoError(contract.DB().Delete("", "id = ?", contract.ID).Error)
	}()

	application := contractapplyment.ContractApplyment{
		GuildID:      3,
		LiveID:       1234,
		Status:       contractapplyment.StatusPending,
		Rate:         40,
		Type:         contractapplyment.TypeRateDown,
		Initiator:    contractapplyment.InitiatorGuild,
		ExpireTime:   now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	application.More = "{\"rate\":45}"
	require.NoError(application.DB().Create(&application).Error)
	defer func() {
		require.NoError(application.DB().Delete("", "id = ?", application.ID).Error)
	}()

	// 参数错误
	ctx := handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/disagree", true, nil)
	_, err := ActionRateAgree(ctx)
	assert.EqualError(err, "参数错误")

	// 主播不同意降低最低分成比例
	params := paramsEditRate{
		ApplicationID: application.ID,
	}
	ctx = handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/disagree", true, params)
	ctx.User().ID = 1234
	res, err := ActionRateDisagree(ctx)
	require.NoError(err)
	assert.Contains(res, "已拒绝调整")

	// 查看数据库数据
	newContract := new(livecontract.LiveContract)
	require.NoError(contract.DB().First(newContract, "id = ?", contract.ID).Error)
	assert.Equal(goutil.BitMask(0), newContract.Attr)

	// 测试重复调接口
	ctx = handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/disagree", true, params)
	ctx.User().ID = 1234
	_, err = ActionRateDisagree(ctx)
	assert.EqualError(err, "不可操作已被处理或已失效的申请")

	// 测试系统自动拒绝
	lastDays := goutil.TimeNow().Add(-8 * 24 * time.Hour)
	updates := map[string]interface{}{
		"status":      contractapplyment.StatusPending,
		"expire_time": lastDays.Unix(),
	}
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ?", application.GuildID, application.LiveID).Updates(updates).Error)
	ctx = handler.NewTestContext(http.MethodPost, "/api/v2/guild/livebackend/rate/disagree", true, params)
	ctx.User().ID = 1234
	_, err = ActionRateDisagree(ctx)
	assert.Contains(err.Error(), "系统已自动拒绝调整")
}

func TestActionRateInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建主播与公会合约
	now := goutil.TimeNow()
	contract := livecontract.LiveContract{
		LiveID:       1234,
		Status:       livecontract.StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	require.NoError(contract.DB().Table(contract.TableName()).Create(&contract).Error)
	defer func() {
		require.NoError(contract.DB().Delete("", "id = ?", contract.ID).Error)
	}()

	application := contractapplyment.ContractApplyment{
		LiveID:       1234,
		GuildID:      3,
		Rate:         40,
		Status:       contractapplyment.StatusPending,
		ExpireTime:   now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	require.NoError(application.DB().Create(&application).Error)
	api := fmt.Sprintf("/api/v2/guild/livebackend/rate/info?application_id=%d", application.ID)

	// 测试数据异常
	ctx := handler.NewTestContext(http.MethodGet, api, true, nil)
	_, err := ActionRateInfo(ctx)
	assert.EqualError(err, "数据异常，请联系客服处理")

	// 测试合约在有效期内
	more := contractapplyment.More{
		Rate: &contract.Rate,
	}
	encodeMore, err := json.Marshal(more)
	require.NoError(err)
	require.NoError(application.DB().Update("more", string(encodeMore)).Error)
	ctx = handler.NewTestContext(http.MethodGet, api, true, nil)
	res, err := ActionRateInfo(ctx)
	require.NoError(err)
	r := res.(respEditRateDetail)
	assert.Equal(45, r.Rate)
	assert.Equal(40, r.EditRate)
	assert.Equal(contractapplyment.StatusPending, r.Status)

	// 公会关系发生变化
	require.NoError(contract.DB().Update("status", livecontract.StatusUseless).Error)
	ctx = handler.NewTestContext(http.MethodGet, api, true, nil)
	res, err = ActionRateInfo(ctx)
	require.NoError(err)
	r = res.(respEditRateDetail)
	assert.Equal(contractapplyment.StatusInvalid, r.Status)

	// 测试合约超时未处理
	require.NoError(application.DB().Update("status", contractapplyment.StatusPending).Error)
	require.NoError(application.DB().Update("expire_time", now.Add(-time.Hour).Unix()).Error)
	ctx = handler.NewTestContext(http.MethodGet, api, true, nil)
	res, err = ActionRateInfo(ctx)
	require.NoError(err)
	r = res.(respEditRateDetail)
	assert.Equal(contractapplyment.StatusOutdated, r.Status)
}
