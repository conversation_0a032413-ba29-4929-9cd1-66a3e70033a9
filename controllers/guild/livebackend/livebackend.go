package livebackend

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
)

// 发起操作前主播是否通过了身份认证
func checkPermission(creatorID int64, token string) error {
	if config.Conf.Params.GuildOperate.CreatorPermissionSwitch {
		pass, err := guild.CheckOperatePermission(creatorID, token)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !pass {
			return actionerrors.ErrLoginIdentityVerificationRequired
		}
	}
	return nil
}
