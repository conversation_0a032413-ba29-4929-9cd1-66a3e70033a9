package livebackend

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func newGetC(uri string) *handler.Context {
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	return c
}

func TestApplymentPendingRespTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).Check(applymentPendingResp{}, "invite_count")
}

func TestActionApplymentPendingCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试数量为 0
	c := newGetC("application/pending-count")
	r, _ := ActionApplymentPendingCount(c)
	resp := r.(applymentPendingResp)
	assert.Equal(0, resp.InviteCount)

	// 生成未处理的邀请
	testCreatorID := int64(12)
	contractApplyment1 := contractapplyment.ContractApplyment{
		GuildID:          107,
		LiveID:           testCreatorID,
		Status:           contractapplyment.StatusPending,
		Initiator:        contractapplyment.InitiatorGuild,
		ContractDuration: contractapplyment.ContractDurationThreeYear,
		GuildName:        "test_1",
		Type:             contractapplyment.TypeGuildSign,
		ExpireTime:       goutil.TimeNow().Unix() + 100,
	}
	require.NoError(contractapplyment.ContractApplyment{}.DB().Create(&contractApplyment1).Error)

	defer func() {
		assert.NoError(contractapplyment.ContractApplyment{}.DB().
			Delete("", "id = ?", contractApplyment1.ID).Error)
	}()

	// 测试请求成功
	c = newGetC("application/pending-count")
	r, _ = ActionApplymentPendingCount(c)
	resp = r.(applymentPendingResp)
	assert.Equal(1, resp.InviteCount)
}
