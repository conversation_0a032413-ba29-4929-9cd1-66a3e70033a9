package livebackend

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	testGuildID int64 = 3
	testLiveID1 int64 = 400789
	testLiveID2 int64 = 400790
	testLiveID3 int64 = 400791
	testLiveID4 int64 = 400792
	testLiveID5 int64 = 400793
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

const checkCodeUserID = 2333333

func TestCheckPermission(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config.Conf.Params.GuildOperate.CreatorPermissionSwitch = true
	defer func() {
		config.Conf.Params.GuildOperate.CreatorPermissionSwitch = false
	}()
	key := keys.GuildUserCritialOperation1.Format(checkCodeUserID)
	require.NoError(service.Redis.Del(key).Err())
	assert.EqualError(checkPermission(checkCodeUserID, "testToken"), "用户需要身份验证")

	require.NoError(service.Redis.Set(key, "testToken", time.Minute).Err())
	require.NoError(checkPermission(checkCodeUserID, "testToken"))
}
