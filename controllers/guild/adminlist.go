package guild

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	typeAll = iota
	typeChecking
	typeRejected
	typePass
)

type adminlistParm struct {
	adminlistResp
	Type      int
	OwnerID   int64
	OwnerName string
	GuildID   int64
	GuildName string

	db                  *gorm.DB
	isLiveFinanceRole   bool
	isGuildAccountAdmin bool
}

type adminlistResp struct {
	Guilds     []*guild.Guild         `json:"guilds"`
	Pagination goutil.Pagination      `json:"pagination"`
	Users      []*mowangskuser.Simple `json:"users"`
}

// ActionAdminlist 超管审核公会列表
// TODO: 目前按照申请日期排序，未添加其他排序方式
/**
 * @api {get} /api/v2/admin/guild/adminlist 超管审核公会列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [type = 0] 状态，0：全部，1：待审核，2：已拒绝, 3: 已通过
 * @apiParam {Number} [p] 页码
 * @apiParam {Number} [owner_id] 公会会长 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "guilds": [],
 *       "users": [],
 *       "pagination":{
 *         "count": 0,
 *         "maxpage": 0,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionAdminlist(c *handler.Context) (handler.ActionResponse, error) {
	access, _, err := liveuser.IsStaff(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}
	var param adminlistParm
	err = param.Load(c)
	if err != nil {
		return nil, err
	}
	param.ApplyParams()
	err = param.BuildResp()
	if err != nil {
		return nil, err
	}
	return param.adminlistResp, nil
}

func (param *adminlistParm) Load(c *handler.Context) error {
	param.Pagination.P, _ = c.GetParamInt64("p")
	if param.Pagination.P <= 0 {
		param.Pagination.P = 1
	}
	param.Type, _ = c.GetParamInt("type")
	if param.Type < typeAll || param.Type > typePass {
		return actionerrors.ErrParams
	}
	param.OwnerID, _ = c.GetParamInt64("owner_id")
	param.GuildID, _ = c.GetParamInt64("guild_id")
	param.OwnerName, _ = c.GetParam("owner_name")
	param.GuildName, _ = c.GetParam("guild_name")
	param.isLiveFinanceRole, _ = c.User().IsRole(role.LiveFinance)
	param.isGuildAccountAdmin, _ = c.User().IsRole(role.GuildAccountAdmin)
	return nil
}

func (param *adminlistParm) ApplyParams() {
	db := service.DB.Table(guild.TableName() + " AS g").Order("g.apply_time DESC").Select("g.*")
	switch param.Type {
	case typeAll:
		db = db.Where("g.checked IN (?)", []int64{guild.CheckedReject, guild.CheckedChecking, guild.CheckedPass})
	case typeChecking:
		db = db.Where("g.checked = ?", guild.CheckedChecking)
	case typeRejected:
		db = db.Where("g.checked = ?", guild.CheckedReject)
	case typePass:
		db = db.Where("g.checked = ?", guild.CheckedPass)
	}
	if param.GuildID != 0 {
		db = db.Where("g.id = ?", param.GuildID)
	}
	if param.GuildName != "" {
		db = db.Where("g.name LIKE ?", servicedb.ToLikeStr(param.GuildName))
	}
	if param.OwnerID != 0 {
		db = db.Where("g.user_id = ?", param.OwnerID)
	}
	if param.OwnerName != "" {
		db = db.Joins(
			fmt.Sprintf("JOIN %s AS u ON g.user_id = u.id", mowangskuser.TableName())).
			Where("u.username LIKE ?", servicedb.ToLikeStr(param.OwnerName))
	}
	param.db = db
}

func (param *adminlistParm) BuildResp() error {
	err := param.db.Count(&param.Pagination.Count).Error
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	param.Pagination = goutil.MakePagination(param.Pagination.Count, param.Pagination.P, goutil.DefaultPageSize)
	param.Guilds = make([]*guild.Guild, 0, goutil.DefaultPageSize)
	err = param.Pagination.ApplyTo(param.db).Find(&param.Guilds).Error
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	ids := make(map[int64]int64, len(param.Guilds))
	for i := 0; i < len(param.Guilds); i++ {
		ids[param.Guilds[i].UserID] = param.Guilds[i].UserID
		err := param.Guilds[i].Decrypt()
		if err != nil {
			return actionerrors.ErrServerInternal.New(err, nil)
		}
		param.Guilds[i].Format(param.isLiveFinanceRole, param.isGuildAccountAdmin)
	}
	userIDs := make([]int64, 0, len(ids))
	for _, id := range ids {
		userIDs = append(userIDs, id)
	}
	users, err := mowangskuser.FindSimpleList(userIDs)
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	if len(userIDs) != len(users) {
		logger.Warn("lack of users")
	}
	param.Users = users
	return nil
}
