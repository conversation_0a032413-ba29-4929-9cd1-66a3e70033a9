package guild

import (
	"net/http"
	"net/http/httptest"
	"regexp"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildDataReport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("param error", func(t *testing.T) {
		ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&sort=reward_income.desc&p=-1", true, nil)
		_, err := ActionGuildDataReport(ctx)
		assert.Error(err, actionerrors.ErrParams)
	})

	t.Run("order by new_fans_num desc", func(t *testing.T) {
		ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&sort=new_fans_num.desc", true, nil)
		r, err := ActionGuildDataReport(ctx)
		require.NoError(err)
		require.NotNil(r)
		require.NotNil(r.(*dataReportResp))
		assert.Greater(r.(*dataReportResp).Data[0].NewFansNum, r.(*dataReportResp).Data[1].NewFansNum)
	})

	t.Run("search by id", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&s=12", true, nil)
		r, err := ActionGuildDataReport(c)
		require.NoError(err)
		require.NotNil(r)
		require.NotNil(r.(*dataReportResp))
		assert.GreaterOrEqual(len(r.(*dataReportResp).Data), 1)
	})

	t.Run("search by name, order by listener_duration_avg", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&sort=listener_duration_avg&s=零月", true, nil)
		r, err := ActionGuildDataReport(c)
		require.NoError(err)
		require.NotNil(r)
		require.NotNil(r.(*dataReportResp))
		assert.GreaterOrEqual(len(r.(*dataReportResp).Data), 1)
	})
}

func TestNewDataReportParam(t *testing.T) {
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&sort=reward_income.desc&p=-1", true, nil)
	_, err := newDataReportParam(ctx)
	assert.Error(err, actionerrors.ErrParams)

	ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2018-03-25&sort=reward_income.desc", true, nil)
	_, err = newDataReportParam(ctx)
	assert.Error(err, actionerrors.ErrParams)

	ctx = handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-01&end_date=2022-05-04&sort=reward_income.descss", true, nil)
	_, err = newDataReportParam(ctx)
	assert.Error(err, actionerrors.ErrParams)
}

func TestDataReportResp_setEmptyList(t *testing.T) {
	assert := assert.New(t)

	resp := new(dataReportResp)
	pa := dataReportParam{
		page:     1,
		pageSize: 10,
	}
	resp.setEmptyList(&pa)
	assert.Len(resp.Data, 0)
	assert.Equal(goutil.MakePagination(0, 1, 10), resp.Pagination)
}

func TestDataReportResp_buildList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := livelog.Collection().FindOneAndReplace(ctx, bson.M{"room_id": 11223344, "creator_id": 11, "guild_id": 3, "duration": 108000001},
		bson.M{
			"room_id":    11223344,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2022, 05, 03, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2022, 05, 04, 0, 0, 0, 0, time.Local)),
			"duration":   108000001,
			"creator_id": 11,
			"guild_id":   3,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report?start_date=2022-05-03&end_date=2022-05-04&sort=new_fans_num.desc", true, nil)
	param, err := newDataReportParam(c)
	assert.NoError(err)
	resp := new(dataReportResp)
	err = resp.buildList(param)
	require.NoError(err)
	assert.EqualValues(40, resp.Data[0].NewFansNum)
	assert.EqualValues(11, resp.Data[0].LiveID)
	assert.Equal(util.Float2DP(108000001)/1000/60, resp.Data[0].LiveDuration)
	assert.EqualValues(15, resp.Data[1].NewFansNum)
}

func TestDataReportResp_buildNewFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testCreatorID := int64(2333666)
	param := dataReportParam{
		userIDs:   []int64{testCreatorID},
		startDate: time.Date(2021, 06, 03, 8, 0, 0, 0, time.Local),
		endDate:   time.Date(2021, 06, 07, 8, 0, 0, 0, time.Local),
	}
	resp := dataReportResp{
		Data: []DataReportItem{
			{LiveID: testCreatorID},
			{LiveID: 1919810},
		},
	}
	require.NoError(resp.buildNewFans(&param))
	assert.Zero(resp.Data[1].NewFansNum)
	assert.EqualValues(40, resp.Data[0].NewFansNum)
}

func TestDataReportParamApplySearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/api/v2/guild/guild-data-report", true, nil)

	t.Run("搜索主播", func(t *testing.T) {
		param, err := newDataReportParam(ctx)
		require.NoError(err)
		require.NotNil(param)

		db := livecontract.ADB("lc").
			Where("lc.guild_id = ?", 3).
			// 获取状态为：合约解约、合约失效、合约生效中 的合约
			Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})

		emptyDB, err := param.applySearch(db)
		require.NoError(err)
		assert.Equal(db, emptyDB)

		// param.searchCreator.Word != "" & param.searchCreator.IsInteger = true
		param.searchCreator.Word = "233"
		param.searchCreator.IsInteger = true
		param.searchCreator.WordInteger = 233
		wordIntegerDB, err := param.applySearch(db)
		require.NoError(err)
		newDB := db.Where("lc.live_id = ?", param.searchCreator.WordInteger)
		assert.Equal(newDB, wordIntegerDB)

		// param.searchCreator.Word != "" & param.searchCreator.IsInteger = false & Word = 666
		param.searchCreator.Word = "666"
		param.searchCreator.IsInteger = false
		param.searchCreator.WordInteger = 666
		wordIntegerDB, err = param.applySearch(db)
		require.NoError(err)
		var nilDB *gorm.DB
		assert.Equal(nilDB, wordIntegerDB)

		require.NoError(mowangskuser.Simple{}.DB().Save(mowangskuser.Simple{ID: 11, Username: "666456"}).Error)
		wordDB, err := param.applySearch(db)
		require.NoError(err)
		inDB := db.Where("lc.live_id IN (?)", []int64{11})
		assert.Equal(inDB, wordDB)
	})

	t.Run("搜索经纪人", func(t *testing.T) {
		param, err := newDataReportParam(ctx)
		require.NoError(err)
		require.NotNil(param)

		db := livecontract.ADB("lc").
			Where("lc.guild_id = ?", 3).
			// 获取状态为：合约解约、合约失效、合约生效中 的合约
			Where("lc.status IN (?)", []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})

		emptyDB, err := param.applySearch(db)
		require.NoError(err)
		assert.Equal(db, emptyDB)

		// param.searchAgent.Word != "" & param.searchAgent.IsInteger = true
		param.searchAgent.Word = "233"
		param.searchAgent.IsInteger = true
		param.searchAgent.WordInteger = 233
		wordIntegerAgentDB, err := param.applySearch(db)
		require.NoError(err)
		var nilDB *gorm.DB
		assert.Equal(nilDB, wordIntegerAgentDB)

		gac := &guildagent.AgentCreator{
			GuildID:   3,
			AgentID:   355,
			CreatorID: 555,
		}
		err = guildagent.AgentCreator{}.DB().Create(gac).Error
		require.NoError(err)

		param, err = newDataReportParam(ctx)
		require.NoError(err)
		require.NotNil(param)
		param.searchAgent.Word = "355"
		param.searchAgent.IsInteger = true
		param.searchAgent.WordInteger = 355
		wordIntegerDB, err := param.applySearch(db)
		require.NoError(err)
		newDB := db.Where("lc.live_id IN (?)", []int64{555})
		assert.Equal(newDB, wordIntegerDB)

		// param.searchAgent.Word != "" & param.searchAgent.IsInteger = false & Word = 2456
		param.searchAgent.Word = "2456"
		param.searchAgent.IsInteger = false
		wordIntegerAgentDB, err = param.applySearch(db)
		require.NoError(err)
		assert.Equal(nilDB, wordIntegerAgentDB)

		require.NoError(mowangskuser.Simple{}.DB().Save(mowangskuser.Simple{ID: 355, Username: "2456"}).Error)
		wordDB, err := param.applySearch(db)
		require.NoError(err)
		inDB := db.Where("lc.live_id IN (?)", []int64{555})
		assert.Equal(inDB, wordDB)
	})
}

func TestDataReportParam_fillUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := user.User{IUser: user.IUser{ID: 12, Username: "零月"}}
	ac1 := guildagent.AgentCreator{AgentID: 10, CreatorID: 12}
	ac2 := guildagent.AgentCreator{AgentID: 10, CreatorID: 13}
	agentCreators := agentCreators{&ac1, &ac2}
	param := &dataReportParam{
		agentCreators: agentCreators,
		guildID:       3,
		page:          1,
		pageSize:      20,
		role:          guildrole.GuildRole{BitMask: goutil.BitMask(3)},
		user:          &u,
	}
	resp := &dataReportResp{
		Data: []DataReportItem{{LiveID: 12}, {LiveID: 13}},
	}
	creatorIDs := []int64{12, 13}

	// 测试用户是公会会长时
	err := resp.fillUserInfo(param, creatorIDs)
	require.NoError(err)
	assert.Equal(ac1.AgentID, resp.Data[0].AgentID)
	assert.Equal(ac2.AgentID, resp.Data[1].AgentID)

	// 测试用户不是公会会长时
	param.role = guildrole.GuildRole{BitMask: goutil.BitMask(4)}
	err = resp.fillUserInfo(param, creatorIDs)
	require.NoError(err)
	assert.Equal(param.user.ID, resp.Data[0].AgentID)
	assert.Equal(param.user.ID, resp.Data[1].AgentID)
}

func TestActionGuildDataReportExport(t *testing.T) {
	assert := assert.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	f := func(c *gin.Context) (*user.User, error) {
		return handler.CreateTestUser(), nil
	}
	c.Set("user", user.GetUserFunc(f))
	ctx := &handler.Context{C: c}
	ctx.C.Request = httptest.NewRequest(http.MethodGet, "/", nil)

	_, err := ActionGuildDataReportExport(ctx)
	assert.Equal(handler.ErrRawResponse, err)

	assert.Equal(http.StatusOK, w.Code)
	assert.NotEmpty(w.Body.String())
	assert.Equal("application/octet-stream", w.Header().Get("Content-Type"))
	length, _ := strconv.Atoi(w.Header().Get("Content-Length"))
	assert.NotZero(length)
	assert.Regexp(regexp.MustCompile(`^attachment; filename=".+"; filename\*=UTF-8''.+$`), w.Header().Get("Content-Disposition"))
}
