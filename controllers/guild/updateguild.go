package guild

import (
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildedithistory"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/useroa"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type updateGuildParams struct {
	ID                           int64            `form:"id" json:"id" binding:"required"`
	UserID                       int64            `form:"user_id" json:"user_id"`
	Name                         string           `form:"name" json:"name"`
	Intro                        string           `form:"intro" json:"intro"`
	Mobile                       string           `form:"mobile" json:"mobile"`
	Email                        string           `form:"email" json:"email"`
	QQ                           string           `form:"qq" json:"qq"`
	OwnerName                    string           `form:"owner_name" json:"owner_name"`
	OwnerIDNumber                string           `form:"owner_id_number" json:"owner_id_number"`
	OwnerIDPeopleURL             upload.SourceURL `form:"owner_id_people_url" json:"owner_id_people_url"`
	OwnerBackcoverURL            upload.SourceURL `form:"owner_backcover_url" json:"owner_backcover_url"`
	CorporationName              string           `form:"corporation_name" json:"corporation_name"`
	CorporationAddress           string           `form:"corporation_address" json:"corporation_address"`
	CorporationPhone             string           `form:"corporation_phone" json:"corporation_phone"`
	BusinessLicenseNumber        string           `form:"business_license_number" json:"business_license_number"`
	BusinessLicenseFrontcoverURL upload.SourceURL `form:"business_license_frontcover_url" json:"business_license_frontcover_url"`
	TaxAccount                   string           `form:"tax_account" json:"tax_account"`
	BankAccount                  string           `form:"bank_account" json:"bank_account"`
	BankAccountName              string           `form:"bank_account_name" json:"bank_account_name"`
	Bank                         string           `form:"bank" json:"bank"`
	BankAddress                  string           `form:"bank_address" json:"bank_address"`
	BankBranch                   string           `form:"bank_branch" json:"bank_branch"`
	InvoiceRate                  int              `form:"invoice_rate" json:"invoice_rate"`
	Confirm                      int              `form:"confirm" json:"confirm"`

	userOA            *useroa.UserOA
	newGuildInfo      guild.Guild
	originalGuildInfo guild.Guild
	confirmInfo       string
	updateGuildName   bool
	updateGuildUserID bool
}

// ActionUpdateGuild 公会资料变更
/**
 * @api {post} /api/v2/admin/guild/updateguild 公会资料变更
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} id 公会 ID
 * @apiParam {Number} [user_id] 公会长 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiParam {String} [name] 公会名称
 * @apiParam {String} [intro] 公会简介
 * @apiParam {String} [mobile] 手机号
 * @apiParam {String} [email] 邮箱
 * @apiParam {String} [qq] QQ
 * @apiParam {String} [owner_name] 法人代表姓名
 * @apiParam {String} [owner_id_number] 法人代表身份证号
 * @apiParam {String} [owner_id_people_url] 法人代表手持身份证正面照
 * @apiParam {String} [owner_backcover_url] 法人代表身份证背面
 * @apiParam {String} [corporation_name] 公司名称
 * @apiParam {String} [corporation_address] 公司地址
 * @apiParam {String} [corporation_phone] 法人代表手机号
 * @apiParam {String} [business_license_number] 营业执照号
 * @apiParam {String} [business_license_frontcover_url] 营业执照扫描件
 * @apiParam {String} [tax_account] 纳税人识别号
 * @apiParam {String} [bank_account] 银行卡号
 * @apiParam {String} [bank_account_name] 银行开户名
 * @apiParam {String} [bank] 开户行
 * @apiParam {String} [bank_address] 开户行所在地
 * @apiParam {String} [bank_branch] 开户支行
 * @apiParam {number=0,-1,-2} [invoice_rate=0] 是否可开发票（-2 可开发票，税率未知，-1 不开具发票）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample 更改成功:
 *   {
 *     "code": 0,
 *     "info": "更改成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": *********,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": ""
 *     }
 *   }
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code *********
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code *********
 * @apiError (403) {String} info 需要弹窗进行确认
 */
func ActionUpdateGuild(c *handler.Context) (handler.ActionResponse, error) {
	var params updateGuildParams

	err := params.loadUpdateGuildParams(c)
	if err != nil {
		return nil, err
	}

	err = params.loadConfirmInfo()
	if err != nil {
		return nil, err
	}
	// 更新公会
	err = params.updateGuild()
	if err != nil {
		return nil, err
	}
	params.addAdminLogSendEmail(c)
	return "公会更改成功", nil
}

func (p *updateGuildParams) loadUpdateGuildParams(c *handler.Context) error {
	err := c.Bind(p)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.userOA, err = useroa.FindByUserID(c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.userOA == nil {
		return actionerrors.ErrParamsMsg("未绑定 OA 账号")
	}
	if p.ID <= 0 {
		return actionerrors.ErrParamsMsg("公会 ID 必填")
	}
	g, err := guild.Find(p.ID)
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	// 只允许编辑审核通过的公会
	if g == nil || g.Checked != guild.CheckedPass {
		return actionerrors.ErrGuildNotExist
	}
	p.newGuildInfo = *g
	p.originalGuildInfo = *g
	p.Email = strings.TrimSpace(p.Email)
	if p.Email != "" && !regEmail.MatchString(p.Email) {
		return actionerrors.ErrParamsMsg("邮箱格式有误")
	}
	p.Mobile = strings.TrimSpace(p.Mobile)
	if p.Mobile != "" {
		_, err = vcode.RegionMobile(p.Mobile, vcode.DefaultRegion)
		if err != nil {
			return actionerrors.ErrParamsMsg("请输入正确的中国大陆手机号码")
		}
	}
	if !p.checkQQ() {
		return actionerrors.ErrParamsMsg("QQ 号填写有误")
	}
	if p.OwnerIDNumber != "" && !regIDNumber.MatchString(p.OwnerIDNumber) {
		return actionerrors.ErrParamsMsg("请输入正确的身份证号")
	}
	if !p.checkInvoiceRate() {
		return actionerrors.ErrParamsMsg("是否开具发票选择有误")
	}
	return nil
}

func (p *updateGuildParams) loadConfirmInfo() error {
	info := ""
	checkStrChanged := func(after, before string) bool {
		return after != "" && before != after
	}
	if p.UserID != 0 && p.originalGuildInfo.UserID != p.UserID {
		u, err := mowangskuser.FindByUserID(p.UserID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if u == nil {
			return actionerrors.ErrCannotFindUser
		}

		// 判断该用户是否是其他公会会长
		g, err := guild.SimpleInfoByUserID(p.UserID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if g != nil {
			return actionerrors.ErrParamsMsg("填写的公会长 M 号已是其他公会会长")
		}

		// 判断该用户是否是其他公会经纪人
		agentGuildID, err := guildagent.GuildID(p.UserID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if agentGuildID > 0 && agentGuildID != p.ID {
			return actionerrors.ErrParamsMsg("填写的公会长 M 号已是其他公会经纪人")
		}
		p.updateGuildUserID = true
		info += fmt.Sprintf("<p>会长 ID (昵称)：%d (%s)</p>", u.ID, html.EscapeString(u.Username))
		p.newGuildInfo.UserID = p.UserID
	}
	p.Name = strings.TrimSpace(p.Name)
	if checkStrChanged(p.Name, p.originalGuildInfo.Name) {
		exists, err := guild.ExistsByName(p.Name)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if exists {
			return actionerrors.ErrGuildNameExists
		}
		p.updateGuildName = true
		info += "<p>公会名：" + html.EscapeString(p.Name) + "</p>"
		p.newGuildInfo.Name = p.Name
	}
	p.Intro = strings.TrimSpace(p.Intro)
	if checkStrChanged(p.Intro, p.originalGuildInfo.Intro) {
		info += "<p>公会简介：" + html.EscapeString(p.Intro) + "</p>"
		p.newGuildInfo.Intro = p.Intro
	}
	p.BankAccountName = strings.TrimSpace(p.BankAccountName)
	if checkStrChanged(p.BankAccountName, p.originalGuildInfo.BankAccountName) {
		info += "<p>公司开户名：" + html.EscapeString(p.BankAccountName) + "</p>"
		p.newGuildInfo.BankAccountName = p.BankAccountName
	}
	p.Bank = strings.TrimSpace(p.Bank)
	if checkStrChanged(p.Bank, p.originalGuildInfo.Bank) {
		info += "<p>开户行：" + html.EscapeString(p.Bank) + "</p>"
		p.newGuildInfo.Bank = p.Bank
	}
	p.BankBranch = strings.TrimSpace(p.BankBranch)
	if checkStrChanged(p.BankBranch, p.originalGuildInfo.BankBranch) {
		info += "<p>开户支行：" + html.EscapeString(p.BankBranch) + "</p>"
		p.newGuildInfo.BankBranch = p.BankBranch
	}
	p.BankAddress = strings.TrimSpace(p.BankAddress)
	if checkStrChanged(p.BankAddress, p.originalGuildInfo.BankAddress) {
		info += "<p>开户行所在地：" + html.EscapeString(p.BankAddress) + "</p>"
		p.newGuildInfo.BankAddress = p.BankAddress
	}
	if p.InvoiceRate != 0 && p.originalGuildInfo.InvoiceRate != p.InvoiceRate {
		pInfo := guild.InvoiceRateStr(p.InvoiceRate)
		info += "<p>是否可开专票：" + pInfo + "</p>"
		p.newGuildInfo.InvoiceRate = p.InvoiceRate
	}
	if p.OwnerIDPeopleURL != "" {
		info += "<p>身份证正面照</p>"
		if p.Confirm == 1 {
			ownerIDPeopleURL, err := checkThenUpload(p.OwnerIDPeopleURL)
			if err != nil {
				return actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
			}
			p.newGuildInfo.OwnerIDPeople = ownerIDPeopleURL
		}
	}
	if p.OwnerBackcoverURL != "" {
		info += "<p>身份证反面照</p>"
		if p.Confirm == 1 {
			ownerBackcoverURL, err := checkThenUpload(p.OwnerBackcoverURL)
			if err != nil {
				return actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
			}
			p.newGuildInfo.OwnerBackcover = ownerBackcoverURL
		}
	}
	if p.BusinessLicenseFrontcoverURL != "" {
		info += "<p>营业执照</p>"
		if p.Confirm == 1 {
			businessLicenseFrontcoverURL, err := checkThenUpload(p.BusinessLicenseFrontcoverURL)
			if err != nil {
				return actionerrors.ErrParamsMsg("上传图片失败，如果多次失败请联系管理员")
			}
			p.newGuildInfo.BusinessLicenseFrontcover = businessLicenseFrontcoverURL
		}
	}
	p.CorporationName = strings.TrimSpace(p.CorporationName)
	if checkStrChanged(p.CorporationName, p.originalGuildInfo.CorporationName) {
		info += "<p>公司名称：" + html.EscapeString(p.CorporationName) + "</p>"
		p.newGuildInfo.CorporationName = p.CorporationName
	}
	p.CorporationAddress = strings.TrimSpace(p.CorporationAddress)
	if checkStrChanged(p.CorporationAddress, p.originalGuildInfo.CorporationAddress) {
		info += "<p>公司地址：" + html.EscapeString(p.CorporationAddress) + "</p>"
		p.newGuildInfo.CorporationAddress = p.CorporationAddress
	}
	p.BusinessLicenseNumber = strings.TrimSpace(p.BusinessLicenseNumber)
	if checkStrChanged(p.BusinessLicenseNumber, p.originalGuildInfo.BusinessLicenseNumber) {
		info += "<p>营业执照号：" + html.EscapeString(p.BusinessLicenseNumber) + "</p>"
		p.newGuildInfo.BusinessLicenseNumber = p.BusinessLicenseNumber
	}
	p.TaxAccount = strings.TrimSpace(p.TaxAccount)
	if checkStrChanged(p.TaxAccount, p.originalGuildInfo.TaxAccount) {
		info += "<p>纳税人识别号：" + html.EscapeString(p.TaxAccount) + "</p>"
		p.newGuildInfo.TaxAccount = p.TaxAccount
	}
	if checkStrChanged(p.Mobile, p.originalGuildInfo.Mobile) {
		info += "<p>手机号：" + html.EscapeString(p.Mobile) + "</p>"
		p.newGuildInfo.Mobile = p.Mobile
	}
	if checkStrChanged(p.Email, p.originalGuildInfo.Email) {
		info += "<p>邮箱：" + html.EscapeString(p.Email) + "</p>"
		p.newGuildInfo.Email = p.Email
	}
	if checkStrChanged(p.QQ, p.originalGuildInfo.QQ) {
		info += "<p>联系人 QQ 号：" + html.EscapeString(p.QQ) + "</p>"
		p.newGuildInfo.QQ = p.QQ
	}
	p.OwnerName = strings.TrimSpace(p.OwnerName)
	if checkStrChanged(p.OwnerName, p.originalGuildInfo.OwnerName) {
		info += "<p>法人：" + html.EscapeString(p.OwnerName) + "</p>"
		p.newGuildInfo.OwnerName = p.OwnerName
	}
	if checkStrChanged(p.OwnerIDNumber, p.originalGuildInfo.OwnerIDNumber) {
		info += "<p>身份证号：" + html.EscapeString(p.OwnerIDNumber) + "</p>"
		p.newGuildInfo.OwnerIDNumber = p.OwnerIDNumber
	}
	p.CorporationPhone = strings.TrimSpace(p.CorporationPhone)
	if checkStrChanged(p.CorporationPhone, p.originalGuildInfo.CorporationPhone) {
		info += "<p>电话：" + html.EscapeString(p.CorporationPhone) + "</p>"
		p.newGuildInfo.CorporationPhone = p.CorporationPhone
	}
	p.BankAccount = strings.TrimSpace(p.BankAccount)
	if checkStrChanged(p.BankAccount, p.originalGuildInfo.BankAccount) {
		info += "<p>银行卡号：" + html.EscapeString(p.BankAccount) + "</p>"
		p.newGuildInfo.BankAccount = p.BankAccount
	}
	if info == "" {
		return actionerrors.ErrParamsMsg("请输入要修改的内容")
	}
	if p.Confirm == 0 {
		msg := fmt.Sprintf("<p>请确认此次修改</p>%s", info)
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	p.confirmInfo = info
	return nil
}

func (p *updateGuildParams) updateGuild() error {
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Save(&p.newGuildInfo).Error
		if err != nil {
			return err
		}

		nowStamp := goutil.TimeNow().Unix()
		// 如果公会长有更改，更新合约表公会长 ID
		if p.UserID != 0 && p.updateGuildUserID {
			// 更改合约表公会长 ID
			err = tx.Table(livecontract.TableName()).
				Where("guild_id = ?", p.ID).
				Update(map[string]interface{}{
					"guild_owner":   p.newGuildInfo.UserID,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
		}

		// 如果公会名有更改并且公会名不重复，更新合约表和合约申请表
		if p.Name != "" && p.updateGuildName {
			// 更改合约表公会名
			err = tx.Table(livecontract.TableName()).
				Where("guild_id = ?", p.ID).
				Update(map[string]interface{}{
					"guild_name":    p.newGuildInfo.Name,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
			// 更改合约申请表公会名
			err = tx.Table(contractapplyment.TableName()).
				Where("guild_id = ?", p.ID).
				Update(map[string]interface{}{
					"guild_name":    p.newGuildInfo.Name,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
		}
		// 公会更改历史记录
		p.originalGuildInfo.Encrypt()
		guildEditHistory := guildedithistory.NewGuildEditHistory(p.originalGuildInfo, p.userOA.UserID)
		err = guildEditHistory.DB().Create(&guildEditHistory).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *updateGuildParams) checkQQ() bool {
	if p.QQ == "" {
		return true
	}
	qqNum, err := strconv.ParseInt(p.QQ, 10, 64)
	if err == nil && qqNum > 0 {
		return true
	}
	return false
}

func (p *updateGuildParams) checkInvoiceRate() bool {
	if p.InvoiceRate == 0 {
		return true
	}
	return p.InvoiceRate == guild.InvoiceRateNoIssue || p.InvoiceRate == guild.InvoiceRateUnknown
}

func (p *updateGuildParams) addAdminLogSendEmail(c *handler.Context) {
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogUpdateGuild,
		fmt.Sprintf("【%s】直播公会资料编辑，公会 ID：%d", p.originalGuildInfo.Name, p.originalGuildInfo.ID),
		goclient.AdminLogOptions{
			ChannelID: &p.originalGuildInfo.ID,
		})
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 发邮件
	toEmails := config.Conf.Params.GuildNoticeEmails.GuildUpdateNoticeEmails
	email := pushservice.Email{
		To:      toEmails,
		Subject: fmt.Sprintf("【%s】直播公会资料编辑", p.originalGuildInfo.Name),
	}
	email.Body = fmt.Sprintf("<p>编辑时间：%s</p><p>操作人：%s</p><p>编辑公会 ID：%d</p><p>编辑公会名称：%s</p>",
		time.Unix(goutil.TimeNow().Unix(), 0).Format(util.TimeFormatYMDHMS),
		html.EscapeString(p.userOA.OAName),
		p.newGuildInfo.ID,
		html.EscapeString(p.originalGuildInfo.Name))
	err = service.PushService.SendEmail(email)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
