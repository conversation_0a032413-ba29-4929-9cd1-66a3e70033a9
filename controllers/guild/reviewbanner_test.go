package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildbanner"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionReviewBannerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/review/list?guild_id=3&creator_id=12&status=1", true, nil)
	resp, err := ActionReviewBannerList(c)
	require.NoError(err)
	r := resp.(*reviewBannerListResp)
	assert.Len(r.Data, 1)
	assert.Equal("零月", r.Data[0].CreatorUsername)
	assert.Equal("测试公会（匆删）", r.Data[0].GuildName)
}

func TestNewReviewBannerListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/review/list", true, nil)
	param, err := newReviewBannerListParam(c)
	require.NoError(err)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/review/list?guild_id=123&creator_id=111", true, nil)
	param, err = newReviewBannerListParam(c)
	require.NoError(err)
	assert.Equal(int64(123), param.guildID)
	assert.Equal(int64(111), param.creatorID)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
}

func TestReviewBannerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := reviewBannerListParam{
		p:        1,
		pageSize: 20,
	}
	resp, err := param.reviewBannerList()
	require.NoError(err)
	assert.Len(resp.Data, 10)

	param.creatorID = 10102
	resp, err = param.reviewBannerList()
	require.NoError(err)
	assert.Len(resp.Data, 4)
}

func TestActionReviewBannerPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := reviewRejectParam{
		Data: []bannerID{
			{ID: 8},
		},
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/pass", true, body)
	resp, err := ActionReviewBannerPass(c)
	require.NoError(err)
	assert.Equal("操作成功", resp)
}

func TestNewReviewPassParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/pass", true, nil)
	_, err := newReviewPassParam(c)
	assert.EqualError(err, actionerrors.ErrParams.Message)
	body := reviewRejectParam{
		Data: []bannerID{
			{ID: 8},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/pass", true, body)
	param, err := newReviewPassParam(c)
	require.NoError(err)
	assert.Len(param.Data, 1)
}

func TestReviewPassParamPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/pass", true, nil)
	param := reviewPassParam{
		Data: []bannerID{
			{ID: 12},
		},
		c: c,
	}
	err := param.pass()
	require.EqualError(err, "当前时段 banner 数已满")

	param.Data = []bannerID{
		{ID: 7},
	}
	err = param.pass()
	require.NoError(err)
	var banner guildbanner.GuildRecommendBanner
	require.NoError(service.LiveDB.First(&banner, "id = ?", 7).Error)
	assert.Equal(guildbanner.StatusPass, banner.Status)
	var element liverecommendedelements.Model
	require.NoError(liverecommendedelements.TableLiveBanners(service.DB).First(&element, "element_id = ?", 345).Error)
	assert.NotEmpty(element)

	param.Data = []bannerID{
		{ID: 11},
	}
	err = param.pass()
	assert.EqualError(err, "主播不可在同一时段进行多次排期")
}

func TestActionReviewBannerReject(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := reviewRejectParam{
		Reason: "1234",
		Data: []bannerID{
			{ID: 1},
		},
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/reject", true, body)
	resp, err := ActionReviewBannerReject(c)
	require.NoError(err)
	assert.Equal("操作成功", resp)
}

func TestNewReviewRejectParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/reject", true, nil)
	_, err := newReviewRejectParam(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	body := reviewRejectParam{
		Reason: "1234",
		Data: []bannerID{
			{ID: 8},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/reject", true, body)
	param, err := newReviewRejectParam(c)
	require.NoError(err)
	assert.Equal("1234", param.Reason)
	assert.Len(param.Data, 1)
}

func TestReviewRejectParamReject(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/guild/banner/review/reject", true, nil)
	param := reviewRejectParam{
		Reason: "test",
		Data: []bannerID{
			{ID: 5},
		},
		c: c,
	}
	err := param.reject()
	require.NoError(err)

	var banner guildbanner.GuildRecommendBanner
	require.NoError(service.LiveDB.First(&banner, "id = ?", 5).Error)
	assert.Equal(guildbanner.StatusReject, banner.Status)
	assert.Equal("test", banner.Reason)
}

func TestActionGuildBannerCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 13, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/count?guild_name=公会", true, nil)
	resp, err := ActionGuildBannerCount(c)
	require.NoError(err)
	r := resp.(*bannerCountResp)
	assert.Len(r.Data, 1)
	assert.Equal(int64(1), r.Pagination.Count)
}

func TestNewBannerCountParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/count?guild_id=123", true, nil)
	param, err := newBannerCountParam(c)
	require.NoError(err)
	assert.Len(param.guildIDs, 1)
	assert.Equal(int64(123), param.guildIDs[0])

	c = handler.NewTestContext(http.MethodGet, "/api/v2/admin/guild/banner/count?guild_name=公会", true, nil)
	param, err = newBannerCountParam(c)
	require.NoError(err)
	assert.GreaterOrEqual(len(param.guildIDs), 3)
}

func TestBannerCountParamGuildList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 13, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	param := bannerCountParam{guildIDs: []int64{}, p: 1, pageSize: 10}
	resp, err := param.guildList()
	require.NoError(err)
	assert.Len(resp.Data, 2)
	assert.Equal(int64(2), resp.Pagination.Count)

	param = bannerCountParam{guildIDs: []int64{111, 222, 333}, p: 1, pageSize: 10}
	resp, err = param.guildList()
	require.NoError(err)
	assert.Len(resp.Data, 1)
	assert.Equal(int64(1), resp.Pagination.Count)
}
