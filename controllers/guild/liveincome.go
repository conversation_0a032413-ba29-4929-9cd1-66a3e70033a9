package guild

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LiveIncomeData 直播收益记录
type LiveIncomeData struct {
	UserID   int64         `gorm:"column:user_id" json:"user_id"`
	Username string        `gorm:"-" json:"username"`
	Title    string        `gorm:"column:title" json:"title"`
	Revenue  util.Float2DP `gorm:"column:revenue" json:"revenue"`
	Status   uint8         `gorm:"column:status" json:"status"`
	CTime    int64         `gorm:"column:c_time" json:"c_time"`
	Type     uint8         `gorm:"column:type" json:"-"`
	GiftID   int64         `gorm:"column:gift_id" json:"-"`
	Attr     int64         `gorm:"column:attr" json:"-"`
	Num      int64         `gorm:"column:num" json:"-"`
}

type liveIncomeResp struct {
	Total      util.Float2DP     `gorm:"column:total" json:"total"`
	Data       []*LiveIncomeData `gorm:"-" json:"data"`
	Pagination goutil.Pagination `gorm:"-" json:"pagination"`
}

type withdrawalRecordResp struct {
	Data       []*withdrawalrecord.WithdrawRecordData `json:"data"`
	Pagination goutil.Pagination                      `json:"pagination"`
}

// ActionLiveIncomeList 主播获取公会收益
/**
 * @api {get} /api/v2/guild/liveincomelist 获取主播收益
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1} [type=0] 收益类型，0：打赏收益，1：贵族收益，2：超粉收益
 * @apiParam {String} start_date 起始日期（例 2019-05-01）
 * @apiParam {String} end_date 截止日期（例 2019-05-15）
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "total": 1.35,
 *         "data": [{
 *           "user_id": 70,
 *           "username": "Mic",
 *           "title": "礼物--直播收益",
 *           "revenue": 0.45,
 *           "status": 1,
 *           "c_time": 1556640000
 *         },
 *         {
 *           "user_id": 70
 *           "username": "Mic",
 *           "title": "礼物--直播收益",
 *           "revenue": 0.45,
 *           "status": 1,
 *           "c_time": 1556640000
 *         }],
 *         "pagination": {
 *           "count": 2,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 */
func ActionLiveIncomeList(c *handler.Context) (handler.ActionResponse, error) {
	guildID, err := livecontract.GetGuildID(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if guildID == 0 {
		return nil, actionerrors.ErrShouldJoinGuild
	}
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	incomeTypes, err := getIncomeTypes(c)
	if err != nil {
		return nil, err
	}

	now := goutil.TimeNow()
	startDate, endDate, err := c.GetParamDateRange(now.AddDate(0, -1, 0).Format("2006-01-02"), now.Format("2006-01-02"))
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	var resp liveIncomeResp
	adbQuery := transactionlog.ADB().
		Select("SUM(FLOOR(ROUND(IFNULL((income - tax), 0) * 1000) / 10) / 100) AS total").
		Where("suborders_num = ? AND to_id = ? AND status = ? AND type = ?", guildID, c.User().ID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", incomeTypes)
	adbQuery = addIncomeTimeFilter(adbQuery, "confirm_time", startDate, endDate)
	err = adbQuery.Scan(&resp).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	db := transactionlog.DB()
	if servicedb.Driver != servicedb.DriverSqlite {
		// SQLite 不支持 FORCE INDEX 关键字，SQLite 强制索引关键字：INDEXED BY
		// 线上大部分执行时都使用了 idx_toid_type_status_confirmtime
		// 部分用户因执行计划有时使用错误索引查询时扫描行数过大，导致执行超时
		// 强制指定 idx_toid_type_status_confirmtime 解决查询缓慢
		db = db.Table(
			transactionlog.TransactionLog{}.TableName() + " AS t FORCE INDEX(idx_toid_type_status_confirmtime)",
		)
	}
	db = db.
		Select("gift_id, from_id AS user_id, title, (FLOOR(ROUND((income - tax) * 1000) / 10) / 100) AS revenue, status, confirm_time AS c_time, attr, num").
		Where("suborders_num = ? AND to_id = ? AND type = ? AND status = ?", guildID, c.User().ID, transactionlog.TypeGuildLive, transactionlog.StatusSuccess).
		Where("attr IN (?)", incomeTypes)
	db = addIncomeTimeFilter(db, "confirm_time", startDate, endDate)

	var totalCount int64
	err = db.Count(&totalCount).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Pagination = goutil.MakePagination(totalCount, page, pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]*LiveIncomeData, 0)
		return resp, nil
	}
	db = resp.Pagination.ApplyTo(db)
	err = db.Order("confirm_time DESC").Scan(&resp.Data).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if len(resp.Data) > 0 {
		fromUserIDs := make([]int64, len(resp.Data))
		for i := range resp.Data {
			fromUserIDs[i] = resp.Data[i].UserID
		}
		userInfos, err := mowangskuser.FindSimpleMap(fromUserIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		for i := range resp.Data {
			if u := userInfos[resp.Data[i].UserID]; u != nil {
				resp.Data[i].Username = u.Username
			}
			resp.Data[i].Title = getIncomeTitle(resp.Data[i].Title, resp.Data[i].Attr, resp.Data[i].GiftID, resp.Data[i].Num, true)
		}
	}
	return resp, nil
}

func getIncomeTitle(originalTitle string, attr int64, giftID int64, giftNum int64, isSelf bool) string {
	typeName, err := transactionlog.IncomeTypeName(attr, giftID)
	if err != nil {
		// 降级显示为 title（后续可能会增加新的收益项，需要同步调整）
		logger.Error(err)
		return originalTitle
	}
	if transactionlog.IsSuperFanIncome(attr) {
		if isSelf {
			// 超粉收益记录不显示主播自己的名称
			return fmt.Sprintf("%s--%d 个月", typeName, giftNum)
		}
		return fmt.Sprintf("%s--%d 个月（主播：%s）", typeName, giftNum, originalTitle)
	}

	if goutil.HasElem(transactionlog.GiftAttrs(), attr) && giftID != 0 {
		if attr == transactionlog.AttrLiveLuckyGift {
			giftNum = 1
		}
		if attr == transactionlog.AttrLiveGashaponGift {
			return fmt.Sprintf("%s--%s× %d", typeName, originalTitle, giftNum)
		}

		return fmt.Sprintf("%s--%s × %d", typeName, originalTitle, giftNum)
	}

	return fmt.Sprintf("%s--%s", typeName, originalTitle)
}

// ActionLiveWithdrawRecord 主播后台获取主播提现记录
/**
* @api {get} /api/v2/guild/livewithdrawrecord 获取主播提现记录
* @apiVersion 0.1.0
* @apiGroup /api/v2/guild
*
* @apiParam {Number} [p=1] 第几页
* @apiParam {Number} [pagesize=20] 每页个数
* @apiParam {number=0,1} [old=0] 是否获取旧提现记录
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
*
* @apiSuccessExample Success-Response:
*     {
*       "code": 0,
*       "info": {
*         "data": [{
*           "id": 2052,
*           "account": "",
*           "mobile": "139******34",
*           "real_name": "哦**",
*           "id_number": "35***************9",
*           "bank": "中国农业银行",
*           "bank_account": "62***************28",
*           "status": 1,
*           "create_time": **********,
*           "profit": "200"
*         }],
*         "pagination": {
*           "count": 2,
*           "maxpage": 1,
*           "p": 1,
*           "pagesize": 20
*         }
*       }
*     }
*
* @apiError (400) {Number} code *********
* @apiError (400) {String} info 参数错误
 */
func ActionLiveWithdrawRecord(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, err
	}
	old, err := c.GetDefaultParamInt64("old", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	recordType := withdrawalrecord.TypeWithDrawLive
	if now.Unix() > transactionlog.TimeStampShowFrozenProfit && old == 0 {
		recordType = withdrawalrecord.TypeWithDrawLiveNew
	}
	resp := new(withdrawalRecordResp)
	resp.Data, resp.Pagination, err = withdrawalrecord.GetWithdrawRecord(c.User().ID, recordType, p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}
