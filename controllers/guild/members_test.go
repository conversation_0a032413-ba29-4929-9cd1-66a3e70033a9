package guild

import (
	"fmt"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livedb/reportinactiveanchor"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

/*
func TestActionMembers(t *testing.T) {
	assert := assert.New(t)
	ctx := handler.CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("GET", "/api/v2/guild/members?type=1", nil)
	_, err := ActionMembers(ctx)
	assert.NoError(err)
	// TODO
	// res := r.(membersResp)
	// assert.NotEqual(0, len(res.Contracts))
	// b, _ := json.Marshal(res)
	// logger.Debug(string(b))
}
*/

func TestNewMemberListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := newGetC("/member/list")
	c.User().ID = 99999
	_, err := newMemberListParam(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)
	_, err = newMemberListParam(newGetC("/member/list?live_id=-100"))
	assert.Equal(actionerrors.ErrParams, err)
	param, err := newMemberListParam(newGetC("/member/list?live_id=100&live_username=aaa"))
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
	assert.Equal(int64(100), param.liveID)
	assert.Equal("aaa", param.liveUsername)
	assert.NotNil(param.contracts)
	assert.NotNil(param.Data)
	assert.Equal(int64(3), param.guildID)
}

func TestMemberListFindContracts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lc := &livecontract.LiveContract{
		LiveID:      rand.Int63n(10000),
		GuildID:     3,
		Status:      livecontract.StatusContracting,
		ContractEnd: goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(lc).Error)
	defer func() {
		require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "id = ?", lc.ID).Error)
	}()

	require.NoError(service.DB.FirstOrCreate(mowangskuser.Simple{
		ID:       lc.LiveID,
		Username: "TestMemberListFindContracts",
	}).Error)
	u, err := mowangskuser.FindByUserID(lc.LiveID)
	require.NoError(err)
	require.NotNil(u)
	param, err := newMemberListParam(newGetC(fmt.Sprintf("?live_id=%d&live_username=%s",
		u.ID, u.Username)))
	require.NoError(err)
	db := service.DB.Table(livecontract.TableName()+" AS l").
		Select("l.live_id, l.contract_start, l.contract_end, l.rate, l.contract_duration").
		Where("l.guild_id = ?", param.guildID).
		Where("l.status = ? AND l.contract_end > ?", livecontract.StatusContracting, goutil.TimeNow().Unix())
	require.NoError(param.findContracts(db))
	require.Len(param.contracts, 1)
	assert.Equal(lc.LiveID, param.contracts[0].LiveID)
	assert.Equal(int64(1), param.Pagination.Count)
}

func TestApplySearchFilter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := memberListParam{
		searchWord: "小蜜",
		role:       guildrole.GuildRole{},
		user:       &user.User{},
	}

	// 经纪人按照关键词搜索
	p.role.Set(guildrole.RoleGuildAgent)
	db := livecontract.LiveContract{}.DB().Select("l.live_id").Table(livecontract.TableName() + " AS l")
	wantDB := db.Joins(fmt.Sprintf("JOIN %s AS u ON l.live_id = u.id", mowangskuser.TableName())).
		Where("u.username LIKE ?", servicedb.ToLikeStr(p.searchWord))
	db, err := p.applySearchFilter(db)
	require.NoError(err)
	assert.Equal(wantDB, db)
}

func TestMemberListFindRenews(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ca := contractapplyment.ContractApplyment{
		LiveID:     12,
		GuildID:    4,
		GuildName:  "测试公会管理-查找待续约合同用",
		ExpireTime: 3876543210,
		Type:       contractapplyment.TypeLiveRenew,
		Status:     contractapplyment.StatusPending,
	}
	require.NoError(service.DB.Create(&ca).Error)
	defer func() {
		require.NoError(service.DB.Table(contractapplyment.TableName()).Delete("", "id = ?", ca.ID).Error)
	}()
	param := &memberListParam{
		contracts: []*livecontract.LiveContract{{
			LiveID:      12,
			ContractEnd: 3987654321,
			GuildID:     4,
		}},
		guildID: 4,
	}
	param.findRenews()
	assert.Empty(param.renewApplyments)

	param.contracts[0].ContractEnd = goutil.TimeNow().AddDate(0, 0, 1).Unix()
	param.findRenews()
	assert.Len(param.renewApplyments, 1)
	assert.NotNil(param.renewApplyments[12])
}

func TestMemberListBuildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       12,
		Username: "TestMemberListBuildResp_user1",
	}).Error)
	require.NoError(service.DB.FirstOrCreate(&mowangskuser.Simple{
		ID:       123,
		Username: "TestMemberListBuildResp_user2",
	}).Error)
	role := guildrole.GuildRole{}
	role.Set(guildrole.RoleGuildOwner)
	param := &memberListParam{
		contracts: []*livecontract.LiveContract{{
			GuildOwner:  233,
			LiveID:      12,
			ContractEnd: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
			GuildID:     4,
			Attr:        goutil.BitMask(livecontract.AttrBitMaskLiveGuildRate),
		}, {
			GuildOwner:       233,
			LiveID:           123,
			ContractEnd:      goutil.TimeNow().AddDate(0, 0, 2).Unix(),
			ContractDuration: 1,
			Attr:             goutil.BitMask(livecontract.AttrBitMaskLiveGuildRate),
		}, {
			GuildOwner:       233,
			LiveID:           13,
			ContractEnd:      29876543210,
			ContractDuration: 2,
			Attr:             goutil.BitMask(livecontract.AttrBitMaskLiveGuildRate),
		}},
		p:    1,
		role: role,
	}
	param.rateMap = make(map[int64]int)
	param.rateMap[12] = 40
	param.Data = make([]*memberElem, 0, 3)
	param.renewApplyments = map[int64]*contractapplyment.ContractApplyment{
		12: {LiveID: 12, ID: 100},
	}
	param.Pagination.Count = 10
	param.user = &user.User{
		IUser: user.IUser{
			ID: 233,
		},
	}
	r, err := param.buildResp()
	require.NoError(err)
	resp := r.(*memberListResp)
	assert.True(resp.Total != nil && *resp.Total == 10)
	require.Len(param.Data, 3)
	assert.Equal([]string{"-", "6 个月", "12 个月"},
		[]string{resp.Data[0].Period, resp.Data[1].Period, resp.Data[2].Period})
	assert.Equal([]int64{100, 0, 0}, []int64{resp.Data[0].RenewApplymentID,
		resp.Data[1].RenewApplymentID, resp.Data[2].RenewApplymentID})
	for i := 0; i < len(resp.Data); i++ {
		assert.NotEmpty(resp.Data[i].LiveUsername)
	}
	assert.True(resp.Data[0].RatePending)
	assert.Equal(resp.Data[0].EditRate, 40)
}

func TestActionMemberList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionMemberList(newGetC("/member/list?live_id=-1"))
	assert.Equal(actionerrors.ErrParams, err)

	r, err := ActionMemberList(newGetC("/member/list?live_id=9999"))
	require.NoError(err)
	resp, ok := r.(*memberListResp)
	require.True(ok)
	assert.Nil(resp.Total)
	assert.Equal(int64(len(resp.Data)), resp.Pagination.Count) // 控制了主播 id, 所以可以这么测
	assert.NotNil(resp.NoAgentNum)
	assert.NotNil(resp.ExclusiveCreatorNum)

	r, err = ActionMemberList(newGetC("/member/list?live_type=1"))
	require.NoError(err)
	resp, ok = r.(*memberListResp)
	require.True(ok)
	assert.Nil(resp.Total)
	assert.Nil(resp.NoAgentNum)
	assert.Nil(resp.ExclusiveCreatorNum)

	// 测试非当前公会
	require.NoError(service.LiveDB.FirstOrCreate(&reportinactiveanchor.InactiveAnchor{
		UserID:          2333,
		GuildLiveDays:   1,
		GuildID:         2333,
		LastLiveDate:    time.Date(2020, 04, 27, 0, 0, 0, 0, time.Local),
		GuildLiveIncome: 300,
		GmtCreate:       goutil.TimeNow(),
		GmtModified:     goutil.TimeNow(),
	}).Error)
	r, err = ActionMemberList(newGetC("/member/list?live_type=1"))
	require.NoError(err)
	resp, ok = r.(*memberListResp)
	require.True(ok)
	assert.Len(resp.Data, 0)
	assert.Nil(resp.NoAgentNum)
	assert.Nil(resp.ExclusiveCreatorNum)

	require.NoError(service.LiveDB.Create(&reportinactiveanchor.InactiveAnchor{
		UserID:          6666,
		GuildLiveDays:   1,
		GuildID:         3,
		LastLiveDate:    time.Date(2020, 04, 27, 0, 0, 0, 0, time.Local),
		GuildLiveIncome: 300,
		GmtCreate:       goutil.TimeNow(),
		GmtModified:     goutil.TimeNow(),
	}).Error)

	r, err = ActionMemberList(newGetC("/member/list?live_type=1"))
	require.NoError(err)
	resp, ok = r.(*memberListResp)
	require.True(ok)
	assert.Equal(int64(1), resp.Pagination.Count)
	assert.Equal(int64(6666), resp.Data[0].LiveID)
	assert.Equal("2020-04-27", resp.Data[0].LastLiveDate)
	assert.NotNil(resp.NoAgentNum)
	assert.NotNil(resp.ExclusiveCreatorNum)
}

const (
	testFirstID int64 = 33333474 // 测试主键 ID
	testLastID  int64 = 33333479
)

func TestFindApplicationRates(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建 5 个用户与公会合约
	params := new(memberListParam)
	params.guildID = 32222
	params.role.Set(3)
	params.contracts = make([]*livecontract.LiveContract, 0)
	for id := testFirstID; id < testLastID; id++ {
		ca := livecontract.LiveContract{
			ID:         id,
			GuildOwner: 777,
			LiveID:     23333 + id,
			GuildID:    32222,
		}
		ca.Attr.Set(livecontract.AttrBitMaskLiveGuildRate)
		params.contracts = append(params.contracts, &ca)
		require.NoError(ca.DB().Create(ca).Error)
	}

	// 创建 5 个用户修改最低分成比例申请表
	now := goutil.TimeNow()
	for id := testFirstID; id < testLastID; id++ {
		application := contractapplyment.ContractApplyment{
			ID:         id,
			LiveID:     23333 + id,
			GuildID:    32222,
			Rate:       45,
			ExpireTime: now.AddDate(0, 0, contractapplyment.ExpireDaysSignApplyment).Unix(),
			Status:     contractapplyment.StatusPending,
			Type:       contractapplyment.TypeRateDown,
		}
		if application.ID == testFirstID {
			application.ExpireTime = now.Add(-time.Hour).Unix()
		}
		require.NoError(application.DB().Create(application).Error)
	}

	params.user = &user.User{
		IUser: user.IUser{ID: 777},
	}
	require.NoError(params.findApplicationRates())
	assert.Equal(4, len(params.rateMap))
	for _, rate := range params.rateMap {
		assert.Equal(45, rate)
	}
}

func TestFindExclusiveCreator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := &memberListParam{guildID: 3}

	// 测试不是会长获取三方独家主播数量
	err := params.findExclusiveCreator()
	require.NoError(err)
	require.Nil(params.ExclusiveCreatorNum)

	// 测试会长获取三方独家主播数量
	params.role.Set(2)
	err = params.findExclusiveCreator()
	require.NoError(err)
	require.NotNil(params.ExclusiveCreatorNum)
	assert.Equal(2, *params.ExclusiveCreatorNum)
	assert.Equal([]int64{1, 2}, params.exclusiveCreatorIDs)
}
