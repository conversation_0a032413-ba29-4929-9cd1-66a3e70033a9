package guild

/*
func TestJoin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	liveID := 196101

	// 为防止冲突先从数据库删除上一次的数据
	err := service.DB.Table(livecontract.TableName()).Where("live_id = ?", liveID).Delete("").Error
	require.NoError(err)
	time.Sleep(time.Millisecond * 200)

	var contract livecontract.LiveContract
	ctx := handler.CreateTestContext(true)
	ctx.User().ID = int64(liveID)
	body := map[string]interface{}{"guild_id": int64(3)}

	// 测试正常加入公会
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/join", paramToRequestBody(body))
	r, err := ActionJoin(ctx)
	require.NoError(err)
	assert.True(r.(bool))
	err = service.DB.Table(livecontract.TableName()).Select("*").Where("live_id = ?", liveID).Scan(&contract).Error
	require.NoError(err)
	time.Sleep(time.Millisecond * 200)
	assert.Equal(int64(liveID), contract.LiveID)

	// 测试 HasOngoingContract 方法
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/join", paramToRequestBody(body))
	r, err = ActionJoin(ctx)
	assert.False(r.(bool))
	assert.Equal("已经申请该公会，请等待公会答复", err.Error())

	body["guild_id"] = int64(1010)
	ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/join", paramToRequestBody(body))
	r, err = ActionJoin(ctx)
	assert.False(r.(bool))
	assert.Equal("公会不存在", err.Error())
}
*/
