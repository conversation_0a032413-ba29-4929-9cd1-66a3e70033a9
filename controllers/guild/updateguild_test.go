package guild

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildedithistory"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUpdateGuildTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.JSON).Check(updateGuildParams{}, "id", "user_id", "name", "intro", "mobile", "email", "qq", "owner_name",
		"owner_id_number", "owner_id_people_url", "owner_backcover_url", "corporation_name", "corporation_address", "corporation_phone",
		"business_license_number", "business_license_frontcover_url", "tax_account", "bank_account", "bank_account_name",
		"bank", "bank_address", "bank_branch", "invoice_rate", "confirm")

	tutil.NewKeyChecker(t, tutil.FORM).Check(updateGuildParams{}, "id", "user_id", "name", "intro", "mobile", "email", "qq", "owner_name",
		"owner_id_number", "owner_id_people_url", "owner_backcover_url", "corporation_name", "corporation_address", "corporation_phone",
		"business_license_number", "business_license_frontcover_url", "tax_account", "bank_account", "bank_account_name",
		"bank", "bank_address", "bank_branch", "invoice_rate", "confirm")
}

func TestActionUpdateGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := map[string]interface{}{
		"id":              "",
		"name":            "测试更改公会",
		"intro":           "测试公会介绍 2",
		"owner_name":      "钢铁侠",
		"owner_id_number": "123456789012345678",
		"mobile":          "***********",
		"email":           "<EMAIL>",
		"qq":              "********",
		"invoice_rate":    guild.InvoiceRateNoIssue,
	}
	nowStamp := goutil.TimeNow().Unix()
	g := guild.Guild{
		ID:                        5,
		Name:                      "测试更改公会1",
		Intro:                     "测试公会介绍",
		OwnerName:                 "钢铁侠",
		OwnerIDNumber:             "123456789012345678",
		OwnerIDPeople:             "oss://userid/201906/17/6b48915bd3c236e711ce4916272b10d6183144.jpg",
		OwnerBackcover:            "oss://userid/201906/14/6b3d208c1f4e7ebfa93cd3a472a3756e145622.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		QQ:                        "********",
		CorporationName:           "测试公司名称",
		CorporationAddress:        "***********",
		CorporationPhone:          "324234599452345689",
		BusinessLicenseNumber:     "324234599452345689",
		BusinessLicenseFrontcover: "oss://userid/202007/22/fef8b261100e8bd20a6c2311dd22c9d3150909.jpg",
		TaxAccount:                "23333",
		BankAccount:               "91360403MA399FU36E",
		BankAccountName:           "测试银行开户名",
		Bank:                      "测试银行",
		BankAddress:               "开户行所在地",
		BankBranch:                "开户支行",
		InvoiceRate:               guild.InvoiceRateUnknown,
		Type:                      guild.TypeUnencrypted,
		Checked:                   guild.CheckedPass,
		CreateTime:                nowStamp,
		ModifiedTime:              nowStamp,
		ApplyTime:                 nowStamp,
		UserID:                    11,
	}
	g.Encrypt()

	// 生成测试合约数据
	contract := livecontract.LiveContract{
		ID:               100,
		GuildID:          g.ID,
		LiveID:           105,
		GuildOwner:       g.UserID,
		Status:           livecontract.StatusContracting,
		ContractStart:    nowStamp,
		ContractDuration: 2,
		ContractEnd:      goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:        g.Name,
	}
	require.NoError(service.DB.Table(livecontract.TableName()).Create(&contract).Error)

	// 测试 id 为空参数错误
	c := handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err := ActionUpdateGuild(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 公会不存在
	body["id"] = 9999999999
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err = ActionUpdateGuild(c)
	assert.Equal(actionerrors.ErrGuildNotExist, err)

	// 生成重名公会数据
	g1 := g
	g1.ID = 1000
	g1.Name = "测试更改公会"
	body["id"] = 5
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", g1.ID).FirstOrCreate(&g1).Error)

	// 测试公会名称已存在
	require.NoError(service.DB.Table(guild.TableName()).Where("id = ?", body["id"]).FirstOrCreate(&g).Error)
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err = ActionUpdateGuild(c)
	assert.Equal(actionerrors.ErrGuildNameExists, err)

	// 测试 confirm = 0 时
	u, err := mowangskuser.FindByUserID(1)
	assert.NoError(err)
	assert.NotNil(u)

	body["name"] = "测试公会 2"

	// 测试填写的公会长 M 号已是其他公会会长
	body["user_id"] = 12
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err = ActionUpdateGuild(c)
	r := err.(*handler.ActionError)
	assert.Equal("填写的公会长 M 号已是其他公会会长", r.Message)

	// 测试填写的公会长 M 号已是其他公会经纪人
	require.NoError(guildagent.GuildAgent{}.DB().Create(guildagent.GuildAgent{
		ID:         12345,
		GuildID:    100,
		AgentID:    106,
		DeleteTime: 0,
	}).Error)
	defer guildagent.AgentCreator{}.DB().Delete("", "id = ?", 12345)

	body["user_id"] = 106
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err = ActionUpdateGuild(c)
	r = err.(*handler.ActionError)
	assert.Equal("填写的公会长 M 号已是其他公会经纪人", r.Message)

	body["user_id"] = 1
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	_, err = ActionUpdateGuild(c)
	r = err.(*handler.ActionError)
	_ = g.Decrypt()
	message := fmt.Sprintf("<p>请确认此次修改</p><p>会长 ID (昵称)：%d (%s)</p><p>公会名：%s</p><p>公会简介：%s</p><p>是否可开专票：%s</p>"+
		"<p>手机号：%s</p><p>邮箱：%s</p><p>联系人 QQ 号：%s</p>",
		body["user_id"], u.Username, body["name"], body["intro"], guild.InvoiceRateNoIssueStr, body["mobile"],
		body["email"], body["qq"])
	assert.Equal(actionerrors.ErrConfirmRequired(message, 1, true), err)
	assert.Equal(1, r.Info["confirm"])

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/email",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	// 测试 confirm = 1 时
	body["confirm"] = 1
	c = handler.NewTestContext(http.MethodPost, "api/v2/admin/updateguild", true, body)
	config.Conf.Params.GuildNoticeEmails.GuildUpdateNoticeEmails = "<EMAIL>,<EMAIL>"
	res, err := ActionUpdateGuild(c)
	assert.NoError(err)
	assert.Equal("公会更改成功", res)

	// 测试公会更改历史记录是否插入成功
	var geh guildedithistory.GuildEditHistory
	err = service.LiveDB.Table(guildedithistory.GuildEditHistory{}.TableName()).
		Where("guild_id = ?", body["id"]).First(&geh).Error
	assert.NoError(err)
	assert.Equal(g.Name, geh.Name)

	// 测试合约数据公会长 ID 是否更改
	cl := new(livecontract.LiveContract)
	require.NoError(service.DB.Table(livecontract.TableName()).Where("id = ?", contract.ID).First(&cl).Error)
	assert.Equal(int64(1), cl.GuildOwner)
}

func TestCheckGuildQQ(t *testing.T) {
	assert := assert.New(t)

	p := updateGuildParams{QQ: "①②③④⑤⑥"}
	assert.False(p.checkQQ())

	p.QQ = "0"
	assert.False(p.checkQQ())

	p.QQ = "-123456"
	assert.False(p.checkQQ())

	p.QQ = "123456"
	assert.True(p.checkQQ())
}

func TestCheckInvoiceRate(t *testing.T) {
	assert := assert.New(t)

	p := updateGuildParams{InvoiceRate: 9********}
	assert.False(p.checkInvoiceRate())

	p.InvoiceRate = 0
	assert.True(p.checkInvoiceRate())

	p.InvoiceRate = guild.InvoiceRateUnknown
	assert.True(p.checkInvoiceRate())

	p.InvoiceRate = guild.InvoiceRateNoIssue
	assert.True(p.checkInvoiceRate())
}
