package guild

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildLiveIncomeV3(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	g := guild.Guild{
		Name:         "测试公会-TestActionGuildLiveIncomeV3",
		Checked:      guild.CheckedPass,
		UserID:       999999,
		ApplyTime:    goutil.TimeNow().Unix(),
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
	}
	require.NoError(g.DB().Create(&g).Error)
	defer func() {
		g.DB().Delete("", "id = ?", g.ID)
	}()

	lc := livecontract.LiveContract{
		GuildID:          g.ID,
		GuildOwner:       g.UserID,
		GuildName:        g.Name,
		LiveID:           7777777,
		ContractDuration: 30 * 24 * 60 * 60,
		ContractStart:    time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local).Unix(),
		ContractEnd:      goutil.TimeNow().Add(time.Hour).Unix(),
		Rate:             45,
		KPI:              "",
		Status:           livecontract.StatusContracting,
		CreateTime:       goutil.TimeNow().Unix(),
		ModifiedTime:     goutil.TimeNow().Unix(),
	}
	require.NoError(lc.DB().Create(&lc).Error)
	defer func() {
		lc.DB().Delete("", "id = ?", lc.ID)
	}()

	tradelog := transactionlog.TransactionLog{
		FromID:       99,
		ToID:         lc.LiveID,
		GiftID:       888,
		Title:        "测试礼物-1",
		IOSCoin:      100,
		AndroidCoin:  0,
		PayPalCoin:   0,
		AllCoin:      100,
		Income:       10,
		Tax:          1,
		Rate:         0.5,
		Num:          1,
		Status:       transactionlog.StatusSuccess,
		Type:         transactionlog.TypeGuildLive,
		SubordersNum: g.ID,
		Attr:         transactionlog.AttrCommon,
		CTime:        time.Date(2024, 2, 16, 0, 0, 0, 0, time.Local).Unix(),
		CreateTime:   time.Date(2024, 2, 16, 0, 0, 0, 0, time.Local).Unix(),
		ModifiedTime: time.Date(2024, 2, 16, 0, 0, 0, 0, time.Local).Unix(),
		ConfirmTime:  time.Date(2024, 2, 16, 0, 0, 0, 0, time.Local).Unix(),
	}
	require.NoError(transactionlog.DB().Create(&tradelog).Error)
	defer func() {
		transactionlog.DB().Delete("", "id = ?", tradelog.ID)
	}()

	ctx := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/guild/guildliveincome-v3?creator_id=%d&start_date=%s&end_date=%s&type=%d",
			lc.LiveID, "2024-02-15", "2024-02-20", utils.IncomeTypeGift,
		),
		true,
		nil)
	ctx.User().ID = g.UserID

	resp, err := ActionGuildLiveIncomeV3(ctx)
	require.NoError(err)
	result, ok := resp.(*utils.LiveIncomeListResp)
	require.True(ok)
	assert.Len(result.Data, 1)
}
