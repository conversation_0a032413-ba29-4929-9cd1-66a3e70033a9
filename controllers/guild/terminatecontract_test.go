package guild

/*
func TestActionPassTerminateContract(t *testing.T) {
	// 创建终止合同的表
	c := newContract(191, 1503, "测试主播同意公会的终止合同请求")
	// 测试同意合同终止申请
	assertTermination(c, "pass", livecontract.StatusUseless, t)
}

func TestActionDeclineTerminateContract(t *testing.T) {
	// 创建终止合同的表
	c := newContract(192, 1504, "测试主播拒绝公会的终止合同请求")
	// 测试拒绝合同终止申请
	assertTermination(c, "decline", livecontract.StatusContracting, t)
}

func newContract(id int64, liveID int64, message string) livecontract.LiveContract {
	c := livecontract.LiveContract{
		ID:          id,
		GuildID:     3,
		LiveID:      liveID,
		ContractEnd: 1999999999,
		Status:      livecontract.StatusTerminating,
		Type:        livecontract.FromGuild,
		GuildOwner:  12,
		GuildName:   message,
	}
	return c
}

func assertTermination(c livecontract.LiveContract, operation string, finalStatus int64, t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	t.Helper()

	err := service.DB.Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	params := map[string]int64{"guild_id": 3}
	ctx := handler.CreateTestContext(true)
	ctx.User().ID = c.LiveID

	// 根据是操作要求是同意还是拒绝来执行不同的函数
	if operation == "pass" {
		ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/passterminatecontract", paramToRequestBody(params))
		r, err := ActionPassTerminateContract(ctx)
		require.NoError(err)
		assert.True(r.(bool))
	} else if operation == "decline" {
		ctx.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/passterminatecontract", paramToRequestBody(params))
		r, err := ActionDeclineTerminateContract(ctx)
		require.NoError(err)
		assert.True(r.(bool))
	}
	time.Sleep(time.Millisecond * 200)

	// 确认修改后数据无误
	contract, err := livecontract.GetByPK(c.ID)
	require.NoError(err)
	assert.Equal(c.LiveID, contract.LiveID)
	assert.Equal(finalStatus, contract.Status)
}
*/
