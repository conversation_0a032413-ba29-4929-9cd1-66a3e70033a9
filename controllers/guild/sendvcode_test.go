package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	mserviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestActionSendCode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(pushservice.Scheme+"://api/sms", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	require.NoError(service.Redis.Del(mserviceredis.LockMobileVCode1.Format("8615512341234")).Err())
	m := handler.M{
		"region":    "CN",
		"mobile":    "15512341234",
		"post_type": 14,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, m)
	c.C.Request.Header.Set("X-Real-Ip", "127.0.0.1")
	r, err := ActionSendVCode(c)
	require.NoError(err)
	assert.Equal("success", r)

	require.NoError(service.Redis.Del(mserviceredis.LockMobileVCode1.Format("86***********")).Err())
	m = handler.M{
		"post_type": 18,
		"token":     "2333",
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, m)
	c.User().ID = 101
	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return &sso.Account{
			Region: 86,
			Mobile: "***********",
		}, nil
	})
	defer cancel()
	r, err = ActionSendVCode(c)
	require.NoError(err)
	assert.Equal("success", r)

	// 测试超过 IP 次数限制时
	require.NoError(service.Redis.Del(mserviceredis.LockMobileVCode1.Format("86***********")).Err())
	m = handler.M{
		"post_type": 18,
		"token":     "2333",
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, m)
	c.SetClientIP("127.0.0.101")

	keyIPSendCount := mserviceredis.KeyCounterVCodeIP1.Format(c.ClientIP())
	require.NoError(service.Redis.Set(keyIPSendCount, vcode.LimitVCodeIPCount, 5*time.Second).Err())

	_, err = ActionSendVCode(c)
	errInfo := vcode.ErrSendVCodeMoreTimesDaily.Error()
	assert.Equal(err, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, errInfo))
	require.NoError(service.Redis.Del(keyIPSendCount).Err())

	defer func() {
		config.Conf.Service.Captcha.Enabled = false
	}()
	config.Conf.Service.Captcha.Enabled = true
	c = handler.NewTestContext(http.MethodPost, "/", true, m)
	_, err = ActionSendVCode(c)
	assert.Equal(actionerrors.ErrSlideCaptchaRequired, err)
}
