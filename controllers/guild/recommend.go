package guild

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type squareHotVacancyUpdateParam struct {
	Position int       `form:"position" json:"position"`
	Data     []guildID `form:"data" json:"data"`
	Vacancy  int64     `form:"vacancy" json:"vacancy"`
}

type guildID struct {
	GuildID int64 `form:"guild_id" json:"guild_id"`
}

// ActionSquareHotVacancyUpdate 更新公会推荐主播上直播广场热门位置的次数
/**
 * @api {post} /api/v2/admin/guild/recommend/squarehot/vacancy/update 更新公会推荐主播上直播广场热门位置的次数
 * @apiVersion 0.1.0
 * @apiName recommendsquarehotvacancyupdate
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {number=4,6} position 热门推荐位置，4: 热门 4, 6: 热门 6
 * @apiParam {number{0-30}} vacancy 次数
 * @apiParam {Object[]} data 包含要更新的公会 ID 列表，如 {"data":[{"guild_id":111}]}
 * @apiParam {Number} data.guild_id 公会 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "设置成功"
 *     }
 */
func ActionSquareHotVacancyUpdate(c *handler.Context) (handler.ActionResponse, error) {
	var param squareHotVacancyUpdateParam
	if err := c.Bind(&param); err != nil {
		return nil, actionerrors.ErrParams
	}
	if !validPosition(param.Position) || param.Vacancy < 0 || param.Vacancy > 999 {
		return nil, actionerrors.ErrParams
	}
	for _, v := range param.Data {
		g, err := guild.Find(v.GuildID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if g == nil {
			return nil, actionerrors.ErrGuildNotExist
		}
		if err := squareHotVacancyUpdate(v.GuildID, param.Vacancy, param.Position); err != nil {
			return nil, err
		}
	}
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("更新公会推荐主播上直播广场热门位置的次数，次数: %d，热门推荐位: %d，公会 ID 列表: %v", param.Vacancy, param.Position, param.Data)
	box.Add(userapi.CatalogUpdateSquareHotVacancy, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

// squareHotVacancyUpdate 设置公会本期推荐次数
func squareHotVacancyUpdate(guildID, vacancy int64, position int) error {
	startTime, _ := currentPeriod()
	guildTimesUsed, err := recommend.TimesUsed(service.LiveDB, []int64{guildID}, startTime, position)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	timesUsed := guildTimesUsed[guildID]
	if vacancy < timesUsed {
		msg := fmt.Sprintf("编辑公会 %d 的推荐次数 %d 错误，不可小于公会已用推荐次数 %d", guildID, vacancy, timesUsed)
		return actionerrors.NewErrForbidden(msg)
	}
	err = recommend.UpdateByGuildAndPosition(service.LiveDB, guildID, startTime,
		position, map[string]interface{}{"edited_vacancy": vacancy, "vacancy": vacancy - timesUsed})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type squareHotVacancyListParam struct {
	Position  int
	GuildID   int64
	GuildName string
	P         int64
	PageSize  int64
}

type squarehotRecommendVacancyInfo struct {
	GuildID        int64  `json:"guild_id"`
	GuildName      string `json:"guild_name"`
	InitialVacancy int64  `json:"initial_vacancy"`
	EditedVacancy  int64  `json:"edited_vacancy"`
	TimesUsed      int64  `json:"times_used"`
	StartTime      int64  `json:"start_time"`
	ExpireTime     int64  `json:"expire_time"`
}

type squareHotVacancyListResp struct {
	Data       []squarehotRecommendVacancyInfo `json:"data"`
	Pagination goutil.Pagination               `json:"pagination"`
}

// ActionSquareHotVacancyList 查看各公会热门列表推荐位的次数
/**
 * @api {get} /api/v2/admin/guild/recommend/squarehot/vacancy/list 查看各公会热门列表推荐位的次数
 * @apiVersion 0.1.0
 * @apiName recommendsquarehotvacancylist
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {number=4,6} position 申请位置，4: 热门 4, 6: 热门 6
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {String} guild_name 公会名
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页长度
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [
 *           {
 *             "guild_id": 1804,
 *             "guild_name": "我的测试公会",
 *             "initial_vacancy": 20,
 *             "edited_vacancy": 21,
 *             "times_used": 1,
 *             "start_time": 1625155200,
 *             "expire_time": 1627833600
 *           }
 *         ],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 */
func ActionSquareHotVacancyList(c *handler.Context) (handler.ActionResponse, error) {
	var p squareHotVacancyListParam
	var err error
	p.P, p.PageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	p.Position, _ = c.GetParamInt("position")
	p.GuildID, _ = c.GetParamInt64("guild_id")
	p.GuildName, _ = c.GetParamString("guild_name")
	if p.Position != 0 && !validPosition(p.Position) {
		return nil, actionerrors.ErrParams
	}
	return p.list()
}

func (p squareHotVacancyListParam) list() (*squareHotVacancyListResp, error) {
	resp := new(squareHotVacancyListResp)
	resp.Pagination = goutil.MakePagination(0, p.P, p.PageSize)
	resp.Data = make([]squarehotRecommendVacancyInfo, 0)
	startTime, endTime := currentPeriod()
	var inputGuildIDs []int64
	// 输入了公会 ID 和公会名的情况下只需要根据公会 ID 查询
	if p.GuildID != 0 {
		// TODO: 需要先判断公会 ID 的合法性，比如公会是否存在，公会是否通过审核等
		inputGuildIDs = append(inputGuildIDs, p.GuildID)
	} else if p.GuildName != "" {
		err := service.DB.Table(guild.TableName()).Select("id").
			Where("name LIKE ? AND checked = ?", servicedb.ToLikeStr(p.GuildName), guild.CheckedPass).
			Pluck("id", &inputGuildIDs).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if len(inputGuildIDs) == 0 {
			return resp, nil
		}
	}
	vacancyDB := recommend.GuildRecommendVacancy{}.DB().Select("guild_id, initial_vacancy, edited_vacancy, vacancy").
		Where("position = ? AND start_time <= ? AND end_time > ?", p.Position, startTime.Unix(), startTime.Unix()).
		Order("vacancy DESC").Order("guild_id DESC")
	if len(inputGuildIDs) != 0 {
		vacancyDB = vacancyDB.Where("guild_id IN (?)", inputGuildIDs)
	}
	var totalCount int64
	err := vacancyDB.Count(&totalCount).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Pagination = goutil.MakePagination(totalCount, p.P, p.PageSize)
	if !resp.Pagination.Valid() {
		return resp, nil
	}
	var guildVacancy []struct {
		GuildID        int64 `gorm:"column:guild_id"`
		InitialVacancy int64 `gorm:"column:initial_vacancy"`
		EditedVacancy  int64 `gorm:"column:edited_vacancy"`
		Vacancy        int64 `gorm:"column:vacancy"`
	}
	if err := resp.Pagination.ApplyTo(vacancyDB).Find(&guildVacancy).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	allGuildIDs := make([]int64, len(guildVacancy))
	for k, v := range guildVacancy {
		allGuildIDs[k] = v.GuildID
	}
	var guildsInfo []struct {
		ID   int64  `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}
	if err := service.DB.Table(guild.TableName()).Select("id, name").
		Where("checked = ? AND id IN (?)", guild.CheckedPass, allGuildIDs).
		Scan(&guildsInfo).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	guildIDToName := make(map[int64]string, len(guildsInfo))
	for _, v := range guildsInfo {
		guildIDToName[v.ID] = v.Name
	}
	data := make([]squarehotRecommendVacancyInfo, 0, len(guildVacancy))
	for _, v := range guildVacancy {
		timesUsed := v.EditedVacancy - v.Vacancy
		info := squarehotRecommendVacancyInfo{
			GuildID:        v.GuildID,
			GuildName:      guildIDToName[v.GuildID],
			TimesUsed:      timesUsed,
			InitialVacancy: v.InitialVacancy,
			EditedVacancy:  v.EditedVacancy,
			StartTime:      startTime.Unix(),
			ExpireTime:     endTime.Unix(),
		}
		data = append(data, info)
	}
	resp.Data = data
	return resp, nil
}

// validPosition 判断位置是否合法
func validPosition(position int) bool {
	if position != 4 && position != 6 {
		return false
	}
	return true
}
