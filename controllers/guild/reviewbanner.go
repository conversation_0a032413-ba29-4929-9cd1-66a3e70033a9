package guild

import (
	"fmt"
	"html"
	"strings"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbanner"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type reviewBannerListParam struct {
	creatorID int64
	guildID   int64

	p        int64
	pageSize int64
}

type reviewBannerListResp struct {
	Data       []*guildbanner.GuildRecommendBanner `json:"data"`
	Pagination *goutil.Pagination                  `json:"pagination"`
}

// ActionReviewBannerList 管理员列出 banner 审核列表
/**
 * @api {get} /api/v2/admin/guild/recommend/banner/review/list 管理员列出 banner 审核列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播名称
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名称
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccessExample Success-Response:
 *    {
 *      "code": 0,
 *      "info": {
 *        "data": [
 *          {
 *            "id": 123,
 *            "creator_id": 123,
 *            "creator_username": "test",
 *            "guild_id": 456,
 *            "guild_name": "公会名称",
 *            "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "create_time": 1584806400
 *          },
 *          {
 *            "id": 123,
 *            "creator_id": 123,
 *            "creator_username": "test",
 *            "guild_id": 456,
 *            "guild_name": "公会名称",
 *            "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "create_time": 1584806400
 *          }
 *        ],
 *        "pagination": {
 *          "count": 1,
 *          "maxpage": 1,
 *          "p": 1,
 *          "pagesize": 20
 *        }
 *      }
 *    }
 */
func ActionReviewBannerList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReviewBannerListParam(c)
	if err != nil {
		return nil, err
	}
	return param.reviewBannerList()
}

func newReviewBannerListParam(c *handler.Context) (*reviewBannerListParam, error) {
	param := new(reviewBannerListParam)
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.creatorID, _ = c.GetParamInt64("creator_id")
	param.guildID, _ = c.GetParamInt64("guild_id")
	return param, nil
}

func (param reviewBannerListParam) reviewBannerList() (*reviewBannerListResp, error) {
	banners, pagination, err := guildbanner.FindBannerPendingList(param.creatorID, param.guildID, param.p, param.pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &reviewBannerListResp{
		Data:       banners,
		Pagination: pagination,
	}, nil
}

type bannerID struct {
	ID int64 `form:"id" json:"id"` // 公会只能创建申请，不能修改和删除申请记录，所以审核的就是上线的图片
}

type reviewPassParam struct {
	Data []bannerID `form:"data" json:"data"`

	c *handler.Context
}

// ActionReviewBannerPass 管理员审核通过 banner
/**
 * @api {post} /api/v2/admin/guild/recommend/banner/review/pass 管理员审核通过 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number[]} banner_ids
 *
 * @apiParamExample {json} Request-Example:
 *   {
 *     "data": [
 *       {"id": 4711},
 *       {"id": 1234}
 *     ]
 *   }
 *
 * @apiSuccessExample Success-Response:
 *    {
 *      "code": 0,
 *      "info": "操作成功"
 *    }
 */
func ActionReviewBannerPass(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReviewPassParam(c)
	if err != nil {
		return nil, err
	}
	err = param.pass()
	if err != nil {
		return nil, err
	}
	return "操作成功", nil
}

func newReviewPassParam(c *handler.Context) (*reviewPassParam, error) {
	param := new(reviewPassParam)
	err := c.Bind(&param)
	if err != nil || len(param.Data) == 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param reviewPassParam) pass() error {
	bannerIDs := make([]int64, 0, len(param.Data))
	for _, v := range param.Data {
		bannerIDs = append(bannerIDs, v.ID)
	}

	guildBanners, err := guildbanner.FindBannerByIDs(bannerIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	elements := make([]liverecommendedelements.LiveRecommendedElements, 0, len(bannerIDs))
	for _, banner := range guildBanners {
		if banner.Status != guildbanner.StatusPending {
			return actionerrors.NewErrForbidden("存在状态不正确的申请记录，请刷新重试")
		}
		bannerElementsCount, err := liverecommendedelements.BannerPeriodCount(banner.StartTime, banner.EndTime)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 超管创建和审核通过的 banner 数量是否超过最大数量
		if bannerElementsCount >= guildbanner.BannerMaxCount {
			return actionerrors.ErrParamsMsg("当前时段 banner 数已满")
		}
		now := goutil.TimeNow().Unix()
		elements = append(elements, liverecommendedelements.LiveRecommendedElements{
			ElementID:   banner.RoomID,
			ElementType: liverecommendedelements.ElementLiveBanner,
			Name:        banner.Title,
			// WORKAROUND: 后续移除
			Cover:        banner.ImageURL + ";", // 分号兼容之前超管 banner 逻辑
			StartTime:    &banner.StartTime,
			ExpireTime:   banner.EndTime,
			CreateTime:   now,
			ModifiedTime: now,
		})
	}
	exists, err := liverecommendedelements.LiveBannerExists(elements)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return actionerrors.NewErrForbidden("主播不可在同一时段进行多次排期")
	}

	ok, err := guildbanner.UpdateStatus(bannerIDs, guildbanner.StatusPass, "")
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("更新失败")
	}
	err = servicedb.BatchInsert(service.DB, liverecommendedelements.TableName(), elements)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 管理员操作日志
	box := goclient.NewAdminLogBox(param.c)
	box.Add(userapi.CatalogManageGuildBanner, fmt.Sprintf("banner 审核批量通过: %v", bannerIDs))
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

type reviewRejectParam struct {
	Reason string     `form:"reason" json:"reason"`
	Data   []bannerID `form:"data" json:"data"`

	c *handler.Context
}

// ActionReviewBannerReject 管理员审核拒绝 banner
/**
 * @api {post} /api/v2/admin/guild/recommend/banner/review/reject 管理员审核拒绝 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {String} reason 拒绝原因
 * @apiParam {Number[]} banner_ids
 *
 * @apiParamExample {json} Request-Example:
 *    {
 *      "reason": "拒绝原因",
 *      "data": [
 *        {"id": 4711},
 *        {"id": 1234}
 *      ]
 *    }
 *
 * @apiSuccessExample Success-Response:
 *    {
 *      "code": 0,
 *      "info": "操作成功"
 *    }
 */
func ActionReviewBannerReject(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReviewRejectParam(c)
	if err != nil {
		return nil, err
	}
	err = param.reject()
	if err != nil {
		return nil, err
	}
	return "操作成功", nil
}

func newReviewRejectParam(c *handler.Context) (*reviewRejectParam, error) {
	param := new(reviewRejectParam)
	err := c.Bind(&param)
	// 过滤只有空格的情况
	param.Reason = strings.TrimSpace(param.Reason)
	if err != nil || len(param.Data) == 0 || param.Reason == "" {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param reviewRejectParam) reject() error {
	bannerIDs := make([]int64, 0, len(param.Data))
	for _, v := range param.Data {
		bannerIDs = append(bannerIDs, v.ID)
	}
	banners, err := guildbanner.FindBannerByIDs(bannerIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	sysMsgList := make([]pushservice.SystemMsg, 0, len(banners))
	for _, banner := range banners {
		if banner.Status != guildbanner.StatusPending {
			return actionerrors.NewErrForbidden("存在状态不正确的申请记录，请刷新重试")
		}
		if banner.OperatorID == 0 {
			continue
		}
		title, content := formatSystemMsg(banner.CreatorUsername, param.Reason)
		sysMsgList = append(sysMsgList, pushservice.SystemMsg{UserID: banner.OperatorID, Title: title, Content: content})
	}
	ok, err := guildbanner.UpdateStatus(bannerIDs, guildbanner.StatusReject, param.Reason)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("更新失败")
	}
	// 发送私信通知
	box := goclient.NewAdminLogBox(param.c)
	if err = service.PushService.SendSystemMsg(sysMsgList); err != nil {
		logger.Error(err)
		// PASS
	}
	// 管理员操作日志
	box.Add(userapi.CatalogManageGuildBanner, fmt.Sprintf("banner 审核批量拒绝: %v", bannerIDs))
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func formatSystemMsg(creatorUsername, reason string) (title, content string) {
	title = fmt.Sprintf("主播 %s 的直播 banner 申请未通过审核", creatorUsername)
	content = fmt.Sprintf("亲爱的公会管理员，非常抱歉，主播 %s 的直播 banner 申请未通过审核。拒绝原因：【%s】",
		creatorUsername, reason)
	content = html.EscapeString(content)
	return
}

type bannerCountParam struct {
	guildIDs []int64
	p        int64
	pageSize int64
}

type bannerCountResp struct {
	Data       []guildbanner.GuildRecommendCount `json:"data"`
	Pagination *goutil.Pagination                `json:"pagination"`
}

// ActionGuildBannerCount 获取当月公会申请 Banner 次数
/**
 * @api {get} /api/v2/admin/guild/recommend/banner/count 获取当月公会申请 Banner 次数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名称
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "guild_id": 456,
 *           "guild_name": "公会名称",
 *           "count": 1
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 */
func ActionGuildBannerCount(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newBannerCountParam(c)
	if err != nil {
		return nil, err
	}
	return param.guildList()
}

func newBannerCountParam(c *handler.Context) (*bannerCountParam, error) {
	var err error
	param := new(bannerCountParam)
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildName, _ := c.GetParamString("guild_name")
	if guildName != "" {
		param.guildIDs, err = guild.FindIDsByGuildNameLike(guildName)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	guildID, _ := c.GetParamInt64("guild_id")
	if guildID != 0 {
		param.guildIDs = append(param.guildIDs, guildID)
	}
	return param, nil
}

func (param bannerCountParam) guildList() (*bannerCountResp, error) {
	var resp bannerCountResp
	var err error
	now := goutil.TimeNow()
	monthBegin := util.BeginningOfMonth(now)
	monthEnd := util.EndOfMonth(now)
	resp.Data, resp.Pagination, err = guildbanner.FindGuildRecommendCount(param.guildIDs, monthBegin, monthEnd, param.p, param.pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &resp, nil
}

// ActionBannerAbsenceList 列出 banner 请假列表，根据操作时间倒序返回
/**
 * @api {get} /api/v2/admin/guild/recommend/banner/absence/list 列出 banner 请假列表，根据操作时间倒序返回
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播昵称
 * @apiParam {Number} [room_id] 房间 ID
 * @apiParam {String} [operator_username] 操作人
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名称
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 2,
 *           "creator_id": 123,
 *           "creator_username": "test",
 *           "room_id": 123,
 *           "guild_id": 456,
 *           "guild_name": "公会名称",
 *           "start_time": 1584806400,
 *           "operator_username": "操作人",
 *           "create_time": 1584806400 // 操作时间
 *         },
 *         {
 *           "id": 1,
 *           "creator_id": 123,
 *           "creator_username": "test2",
 *           "room_id": 1234,
 *           "guild_id": 456,
 *           "guild_name": "公会名称",
 *           "start_time": 1584806400,
 *           "operator_username": "操作人",
 *           "create_time": 1584806400
 *         }
 *       ],
 *       "pagination": {
 *         "count": 2,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 */
func ActionBannerAbsenceList(c *handler.Context) (handler.ActionResponse, error) {
	return nil, nil
}
