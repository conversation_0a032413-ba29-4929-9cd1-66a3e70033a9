package guild

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// ActionGetGuildInfo 获取公会创建信息
/**
 * @api {get} /guild/getguildinfo 获取公会创建信息
 * @apiExample {curl} Example usage:
 *     curl -i http://{{host}}/guild/getguildinfo
 * @apiSampleRequest /guild/getguildinfo
 *
 * @apiVersion 0.1.0
 * @apiName getguildinfo
 * @apiGroup /api/v2/guild
 *
 * @apiSuccess {Number} code 请求状态
 * @apiSuccess {object} info 公会信息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "id": 3,
 *         "name": "测试公会（匆删）",
 *         "intro": "简介3",
 *         "owner_name": "法人",
 *         "owner_id_number": "30***************3",
 *         "owner_id_people_url": "*",
 *         "owner_backcover_url": "*",
 *         "mobile": "133******39",
 *         "email": "nih****@163.com",
 *         "corporation_name": "三体公司",
 *         "corporation_address": "三体星",
 *         "corporation_phone": "186******88",
 *         "business_license_number": "*****************",
 *         "business_license_frontcover_url": "*",
 *         "tax_account": "*****************",
 *         "bank_account": "62*************34",
 *         "bank_account_name": "三体",
 *         "bank": "建设银行",
 *         "bank_address": "上海",
 *         "bank_branch": "上海分行",
 *         "checked": 1,
 *         "user_id": 12,
 *         "apply_time": 0
 *       }
 *     }
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": null // 还未创建公会
 *     }
 */
func ActionGetGuildInfo(c *handler.Context) (handler.ActionResponse, error) {
	var g guild.Guild

	// 仅查询用户下拒审、在审及审核通过公会
	err := service.DB.Where("user_id = ?", c.UserID()).
		Where("checked IN (?)", []int{guild.CheckedReject, guild.CheckedChecking, guild.CheckedPass}).
		Take(&g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = g.Decrypt()
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	g.Format(false, false)
	return g, nil
}
