package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestAdminlistParmLoad(t *testing.T) {
	assert := assert.New(t)
	c := handler.CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "/admin/guild/adminlist?type=-10", nil)
	var param adminlistParm
	assert.Equal(actionerrors.ErrParams, param.Load(c))
	c = handler.CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "/admin/guild/adminlist?owner_id=12&guild_id=3&owner_name=aaa&guild_name=bbb", nil)
	require.NoError(t, param.Load(c))
	assert.Equal(int64(1), param.Pagination.P)
	assert.Equal(int64(12), param.OwnerID)
	assert.Equal(int64(3), param.GuildID)
	assert.Equal("aaa", param.OwnerName)
	assert.Equal("bbb", param.GuildName)
}

func TestAdminlistParamBuildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	db := service.DB.Table(guild.TableName()).Order("create_time DESC")
	db = db.Where("id = ?", 3)
	param := adminlistParm{db: db}
	param.Pagination.P = 1
	assert.NoError(param.BuildResp())
	require.NotEmpty(param.Guilds)
	guild := param.Guilds[0]
	assert.Contains(guild.OwnerIDNumber, "*") // guilds.OwnerIDNumber: 12***6
	assert.Equal(int64(1), param.Pagination.MaxPage)
	require.Equal(len(param.Guilds), int(param.Pagination.Count))
	assert.Equal(int64(3), param.Guilds[0].ID)
	require.Equal(1, len(param.Users))
	assert.Equal(int64(12), param.Users[0].ID)

	// 设置测试用户为：公会账号管理员，所有信息无需脱敏
	param.isGuildAccountAdmin = true
	assert.NoError(param.BuildResp())
	require.NotEmpty(param.Guilds)
	guild = param.Guilds[0]
	assert.NotContains(guild.OwnerIDNumber, "*") // guilds.OwnerIDNumber: 123456
}

func TestActionAdminlist(t *testing.T) {
	// assert := assert.New(t)
	require := require.New(t)
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "admin/guild/adminlist", nil)
	_, err := ActionAdminlist(c)
	require.NoError(err)
	// resp := r.(adminlistResp)
	// require.Equal(1, len(resp.Users))
	// require.Equal(4, len(resp.Guilds))
	// assert.Equal(resp.Pagination.Count, int64(len(resp.Guilds)))
	// assert.Equal(int64(3), resp.Guilds[0].ID)
	// assert.Equal(resp.Guilds[0].UserID, resp.Users[0].ID)

	// TODO: 覆盖全部
	// b, _ := json.Marshal(r)
	// logger.Debug(string(b))
}
