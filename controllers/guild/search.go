package guild

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type guildItem struct {
	ID        int64  `gorm:"column:id" json:"id"`
	GuildName string `gorm:"column:name" json:"guild_name"`
	Intro     string `gorm:"column:intro" json:"intro"`
	UserID    int64  `gorm:"column:user_id" json:"user_id"`

	Settlement string `json:"settlement"`
	RateStr    string `json:"rate_string"`
}

// ActionSearch 查询公会
/**
 * @api {get} /api/v2/guild/search 查询公会
 * @apiDescription 当前只支持通过会长 ID 查询公会（即 s 为整型）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {Number} s 关键词
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "id": 10,
 *       "guild_name": "天音纪",
 *       "intro": "独立音乐厂牌，求有实力的家养歌手。",
 *       "user_id": 4421256,
 *       "settlement": "对公结算",
 *       "rate_string": "主播分成不低于 45%"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionSearch(c *handler.Context) (handler.ActionResponse, error) {
	var guilds []guildItem

	s, _ := c.GetParamString("s")
	if s == "" {
		return nil, actionerrors.ErrParams
	}

	guilds, err := searchGuild(s)
	if err != nil {
		return nil, err
	}

	return guilds, nil
}

// searchGuild search guild
func searchGuild(keyword string) ([]guildItem, error) {
	var guilds []guildItem
	var err error

	guildOwnerID, err := strconv.ParseInt(keyword, 10, 64)
	if err == nil {
		err = service.DB.Table(guild.TableName()).
			Where("user_id = ? AND checked = ?", guildOwnerID, guild.CheckedPass).
			Scan(&guilds).Error
	} else {
		err = service.DB.Table(guild.TableName()).
			Where("checked = ? AND (name LIKE ? OR intro LIKE ?)", guild.CheckedPass, servicedb.ToLikeStr(keyword), servicedb.ToLikeStr(keyword)).
			Scan(&guilds).Error
	}
	for i := range guilds {
		guilds[i].Settlement = contractapplyment.SettlementDefault
		guilds[i].RateStr = contractapplyment.RateTipDefault
	}

	return guilds, err
}
