package guild

import (
	"bytes"
	"database/sql"
	"encoding/csv"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	incomeTypeGift = iota
	incomeTypeNoble
	incomeTypeSuperFan
)

// itemType for response data list
type itemType struct {
	LiveID        int64         `json:"live_id" gorm:"column:live_id"`
	IconURL       string        `json:"iconurl"`
	Username      string        `json:"username"`
	GuildTotal    util.Float2DP `json:"guild_total" gorm:"column:guild_total"`
	ContractStart int64         `json:"-" gorm:"column:contract_start"`
	Status        int64         `json:"status" gorm:"column:status"`
	Duration      int64         `json:"duration"`
	Days          int           `json:"days"` // 有效天

	AgentID       int64  `json:"agent_id"`
	AgentUsername string `json:"agent_username"`
}

type guildIncomeResp struct {
	// TODO: 调整字段名为 data 及 total_revenue
	Data         []itemType        `gorm:"-" json:"Datas"`
	Pagination   goutil.Pagination `gorm:"-" json:"pagination"`
	TotalRevenue util.Float2DP     `gorm:"column:total" json:"total"`
}

type guildIncomeParam struct {
	page     int64
	pageSize int64
	sort     struct {
		orderByField string
		order        string
	}

	startDate time.Time
	endDate   time.Time

	guildID     int64
	incomeTypes []int64

	// TODO: 封装成至 util 中统一调用
	searchCreator struct {
		word        string
		isInteger   bool
		wordInteger int64
	}
	searchAgent struct {
		word        string
		isInteger   bool
		wordInteger int64
	}

	role guildrole.GuildRole
	user *user.User

	agentCreators agentCreators
}

func (param *guildIncomeParam) orderExpression() string {
	return param.sort.orderByField + " " + param.sort.order
}

var guildIncomeSortMapSupported = map[string]bool{
	"guild_total.asc":     true,
	"guild_total.desc":    true,
	"guild_total":         true,
	"contract_start.asc":  true,
	"contract_start.desc": true,
	"contract_start":      true,
	"guild_id.desc":       true,
	"guild_id.asc":        true,
	"guild_id":            true,
}

// ActionGuildIncome 公会创建者获取公会收益
/**
 * @api {get} /api/v2/guild/guildincome 公会创建者获取公会收益
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1,2} [type=0] 收益类型，0：打赏收益，1：贵族收益，2：超粉收益
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2019-05-01）
 * @apiParam {String} [end_date=今天] 截止日期（例 2019-05-15）
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 * @apiParam {String} [search_agent] 通过经纪人昵称或 ID 搜索
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {String} [sort=guild_total.desc] 排序方式：contract_start.asc、contract_start.desc、guild_total.asc、guild_total.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "Datas": [{
 *           "live_id": 346285,
 *           "iconurl": "http://static.missevan.com/avatars/201609/18/8bab6898a560f32f9893d6df816c493a121724.jpg",
 *           "username": "T2ye_ZJ",
 *           "guild_total": 0.45,
 *           "status": 1,  // 合约状态（-3 已解约, -2 已失效, 1 生效中, 2 生效中但正在解约）
 *           "duration": 240306210,
 *           "days": 1, // 有效天
 *           "agent_username": "invincible",  // 经纪人昵称
 *           "agent_id": 1  // 经纪人 ID
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         },
 *         "total": 0.45
 *       }
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionGuildIncome(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(guildIncomeParam)
	if err := param.load(ctx); err != nil {
		return nil, err
	}
	resp, err := guildIncome(param)
	if err != nil {
		return nil, err
	}
	if resp.TotalRevenue, err = param.totalRevenue(); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return resp, nil
}

func (param *guildIncomeParam) emptyResp() *guildIncomeResp {
	return &guildIncomeResp{
		Data:       make([]itemType, 0),
		Pagination: goutil.MakePagination(0, param.page, param.pageSize),
	}
}

func guildIncome(param *guildIncomeParam) (resp *guildIncomeResp, err error) {
	// 获取合约生效中，或曾签约过但已解约/已失效的记录
	db := livecontract.ADB("lc").Where("lc.guild_id = ?", param.guildID).
		Where("lc.status NOT IN (?)", []int64{livecontract.StatusRefused, livecontract.StatusUntreated})
	if db, err = param.applySearch(db); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if db == nil {
		return param.emptyResp(), nil
	}
	if !param.role.IsGuildOwner() {
		creatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(creatorIDs) == 0 {
			return param.emptyResp(), nil
		}
		db = db.Where("lc.live_id IN (?)", creatorIDs)
	}

	var totalCount int64
	if err = db.Select("COUNT(DISTINCT live_id) AS live_count").Row().Scan(&totalCount); err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if totalCount == 0 {
		return param.emptyResp(), nil
	}

	resp = new(guildIncomeResp)
	resp.Pagination = goutil.MakePagination(totalCount, param.page, param.pageSize)
	db = db.Select("lc.live_id, lc.contract_start, lc.status, IFNULL(t.rev, 0) AS guild_total").
		Joins("LEFT JOIN ? AS t ON t.to_id = lc.live_id", param.revenueSubquery()).
		Group("lc.live_id").
		Order(param.orderExpression())
	if err = resp.Pagination.ApplyTo(db).Scan(&resp.Data).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(resp.Data) == 0 {
		return resp, nil
	}

	userIDs := make([]int64, len(resp.Data))
	for i, item := range resp.Data {
		userIDs[i] = item.LiveID
	}
	if err = param.fillUserInfo(resp, userIDs); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err = param.fillLiveDuration(resp, userIDs); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return resp, nil
}

func (param *guildIncomeParam) revenueSubquery() interface{} {
	query := transactionlog.ADB().
		Select("to_id, SUM("+transactionlog.RevenueExpr()+" / 100) AS rev").
		Where("suborders_num = ? AND status = ? AND type = ?",
			param.guildID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", param.incomeTypes).
		Group("to_id")
	return addIncomeTimeFilter(query, "confirm_time", param.startDate, param.endDate).SubQuery()
}

func (param *guildIncomeParam) applySearch(db *gorm.DB) (*gorm.DB, error) {
	if param.searchCreator.word != "" {
		if param.searchCreator.isInteger {
			db = db.Where("lc.live_id = ?", param.searchCreator.wordInteger)
		} else {
			var allCreatorIDs []int64
			// 该公会下所有的主播 ID
			err := db.Pluck("live_id", &allCreatorIDs).Error
			if err != nil {
				return nil, err
			}
			if len(allCreatorIDs) == 0 {
				return nil, nil
			}
			// 从该公会下所有的主播 ID 中匹配用户
			liveIDs, err := mowangskuser.SearchUserByUsername(param.searchCreator.word, allCreatorIDs)
			if err != nil {
				return nil, err
			}
			if len(liveIDs) == 0 {
				return nil, nil
			}
			db = db.Where("lc.live_id IN (?)", liveIDs)
		}
	}
	if param.searchAgent.word != "" {
		var liveIDs []int64
		if param.searchAgent.isInteger {
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, param.searchAgent.wordInteger)
		} else {
			allAgentIDs := param.agentCreators.allAgentIDs()
			if len(allAgentIDs) == 0 {
				return nil, nil
			}
			agentIDs, err := mowangskuser.SearchUserByUsername(param.searchAgent.word, allAgentIDs)
			if err != nil {
				return nil, err
			}
			if len(agentIDs) == 0 {
				return nil, nil
			}
			liveIDs = param.agentCreators.agentCreatorIDs(param.user.ID, param.role, agentIDs...)
		}
		if len(liveIDs) == 0 {
			return nil, nil
		}
		db = db.Where("lc.live_id IN (?)", liveIDs)
	}

	return db, nil
}

func (param *guildIncomeParam) fillUserInfo(resp *guildIncomeResp, userIDs []int64) error {
	var creatorAgentMap map[int64]int64
	if param.role.IsGuildOwner() {
		agentIDs := make([]int64, len(param.agentCreators))
		creatorAgentMap = make(map[int64]int64, len(param.agentCreators))
		for i, item := range param.agentCreators {
			agentIDs[i] = item.AgentID
			creatorAgentMap[item.CreatorID] = item.AgentID
		}
		userIDs = append(userIDs, agentIDs...)
	}

	userInfoMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return err
	}
	for i, item := range resp.Data {
		creatorInfo, ok := userInfoMap[item.LiveID]
		if ok {
			resp.Data[i].IconURL = creatorInfo.IconURL
			resp.Data[i].Username = creatorInfo.Username
		}
		if param.role.IsGuildOwner() {
			if agentID, ok := creatorAgentMap[item.LiveID]; ok {
				agentInfo, ok := userInfoMap[agentID]
				if ok {
					resp.Data[i].AgentID = agentInfo.ID
					resp.Data[i].AgentUsername = agentInfo.Username
				}
			}
		} else {
			resp.Data[i].AgentID = param.user.ID
			resp.Data[i].AgentUsername = param.user.Username
		}
	}

	return nil
}

func (param *guildIncomeParam) fillLiveDuration(resp *guildIncomeResp, userIDs []int64) error {
	userDurationMap, err := livelog.GetUserLiveTotalDuration(param.guildID, userIDs, param.startDate, param.endDate.AddDate(0, 0, 1))
	if err != nil {
		return err
	}

	for i, item := range resp.Data {
		if duration, ok := userDurationMap[item.LiveID]; ok {
			resp.Data[i].Duration = duration.TotalDuration
			resp.Data[i].Days = duration.Days
		}
	}

	return nil
}

func (param *guildIncomeParam) totalRevenue() (revenue util.Float2DP, err error) {
	db := transactionlog.ADB().
		Select("IFNULL(SUM("+transactionlog.RevenueExpr()+" / 100), 0) AS total").
		Where("suborders_num = ? AND status = ? AND type = ?",
			param.guildID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", param.incomeTypes)
	db = addIncomeTimeFilter(db, "confirm_time", param.startDate, param.endDate)

	if !param.role.IsGuildOwner() {
		agentCreatorIDs := param.agentCreators.agentCreatorIDs(param.user.ID, param.role)
		if len(agentCreatorIDs) == 0 {
			return
		}
		db = db.Where("to_id IN (?)", agentCreatorIDs)
	}

	err = db.Row().Scan(&revenue)
	if err == sql.ErrNoRows {
		err = nil
	}

	return
}

func (param *guildIncomeParam) load(ctx *handler.Context) (err error) {
	role, guildInfo, err := guildrole.UserGuildRole(ctx.UserID())
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return actionerrors.ErrNoAuthority
	}
	param.user, param.role, param.guildID = ctx.User(), role, guildInfo.ID
	if param.page, param.pageSize, err = ctx.GetParamPage(); err != nil {
		return actionerrors.ErrParams
	}
	if param.incomeTypes, err = getIncomeTypes(ctx); err != nil {
		return actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	param.startDate, param.endDate, err = ctx.GetParamDateRange(
		now.AddDate(0, -1, 0).Format(goutil.TimeFormatYMD),
		now.Format(goutil.TimeFormatYMD),
	)
	if err != nil {
		return actionerrors.ErrParams
	}

	var ok bool
	param.sort.orderByField, param.sort.order, ok = utils.ParseSortStr(
		ctx.GetDefaultParamString("sort", "guild_total.desc"),
		guildIncomeSortMapSupported,
	)
	if !ok {
		return actionerrors.ErrParams
	}
	// TODO: 目前输入任何字符都能搜索，之后需要对输入的关键字做限制
	if param.searchAgent.word, _ = ctx.GetParamString("search_agent"); param.searchAgent.word != "" {
		param.searchAgent.wordInteger, err = strconv.ParseInt(param.searchAgent.word, 10, 64)
		if err == nil {
			param.searchAgent.isInteger = true
		}
	}
	// TODO: 目前输入任何字符都能搜索，之后需要对输入的关键字做限制
	if param.searchCreator.word, _ = ctx.GetParamString("s"); param.searchCreator.word != "" {
		param.searchCreator.wordInteger, err = strconv.ParseInt(param.searchCreator.word, 10, 64)
		if err == nil {
			param.searchCreator.isInteger = true
		}
	}
	param.agentCreators, err = guildagent.FindAgentCreatorListByGuild(param.guildID)
	if err != nil {
		return actionerrors.ErrServerInternal.New(err, nil)
	}

	return nil
}

// ActionGuildIncomeExport 公会创建者导出公会收益 csv
/**
 * @api {get} /api/v2/guild/guildincomeexport 公会创建者导出公会收益 csv
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1,2} [type=0] 收益类型，0：打赏收益，1：贵族收益，2：超粉收益
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2019-05-01）
 * @apiParam {String} [end_date=今天] 截止日期（例 2019-05-15）
 * @apiParam {String} [s] 搜索词（主播 ID 或主播昵称）
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildIncomeExport(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(guildIncomeParam)
	if err := param.load(ctx); err != nil {
		return nil, err
	}
	param.page = 1
	param.pageSize = 10000 // 设置一个大值，避免遗漏数据

	resp, err := guildIncome(param)
	if err != nil {
		return nil, err
	}

	var records [][]string
	titleLine := []string{"主播 ID", "主播昵称", "经纪人 ID", "经纪人", "收入（元）", "直播时长（秒）", "有效天"}
	records = append(records, titleLine)
	for _, v := range resp.Data {
		var agentIDStr string
		if v.AgentID != 0 {
			agentIDStr = strconv.FormatInt(v.AgentID, 10)
		}
		records = append(records, []string{
			strconv.FormatInt(v.LiveID, 10),
			v.Username,
			agentIDStr,
			v.AgentUsername,
			strconv.FormatFloat(float64(v.GuildTotal), 'f', 2, 64),
			strconv.FormatInt(v.Duration/1000, 10),
			strconv.Itoa(v.Days),
		})
	}

	filename := fmt.Sprintf("公会收益_%s_%s.csv", param.startDate.Format(goutil.TimeFormatYMD), param.endDate.Format(goutil.TimeFormatYMD))
	return writeDataToCsv(ctx, records, filename)
}

func writeDataToCsv(ctx *handler.Context, records [][]string, filename string) (handler.ActionResponse, error) {
	var b bytes.Buffer
	b.Write([]byte{0xEF, 0xBB, 0xBF}) // UTF-8 BOM
	w := csv.NewWriter(&b)

	for _, record := range records {
		if err := w.Write(record); err != nil {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
	}

	w.Flush()

	if err := w.Error(); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	ctx.SendDownload(&b, int64(b.Len()), filename)

	return nil, handler.ErrRawResponse
}

type guildLiveIncomeResp struct {
	Total      util.Float2DP         `json:"total"`
	Data       []guildLiveIncomeItem `json:"Datas"`
	Pagination goutil.Pagination     `json:"pagination"`
}

type guildLiveIncomeItem struct {
	CTime    int64         `gorm:"column:c_time" json:"c_time"`
	Username string        `gorm:"column:username" json:"username"`
	Title    string        `gorm:"column:title" json:"title"`
	Revenue  util.Float2DP `gorm:"column:revenue" json:"revenue"`
	Status   int           `gorm:"column:status" json:"status"`
	GiftID   int64         `gorm:"column:gift_id" json:"-"`
	Attr     int64         `gorm:"column:attr" json:"-"`
	Num      int64         `gorm:"column:num" json:"-"`
	FromID   int64         `gorm:"column:from_id" json:"-"`
}

// ActionGuildLiveIncome 公会创建者获取公会主播收益流水
/**
 * @api {get} /api/v2/guild/guildliveincome 公会创建者获取公会主播收益流水
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/guild
 *
 * @apiParam {number=0,1,2} [type=0] 收益类型，0：打赏收益，1：贵族收益，2：超粉收益
 * @apiParam {String} [start_date=一个月前] 起始日期（例 2019-05-01）
 * @apiParam {String} [end_date=今天] 截止日期（例 2019-05-15）
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 * @apiParam {Number} live_id 主播 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "total": 1.35,
 *         "Datas": [{
 *           "c_time": 1556640000,
 *           "username": "违规昵称_Q61dOKx3",
 *           "title": "直播收益",
 *           "revenue": 0.45,
 *           "status": 1
 *         },
 *         {
 *           "c_time": 1556640000,
 *           "username": "违规昵称_Q61dOKx3",
 *           "title": "直播收益",
 *           "revenue": 0.45,
 *           "status": 1
 *         }],
 *         "pagination": {
 *           "count": 2,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGuildLiveIncome(c *handler.Context) (handler.ActionResponse, error) {
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	incomeTypes, err := getIncomeTypes(c)
	if err != nil {
		return nil, err
	}

	now := goutil.TimeNow()
	startDate, endDate, err := c.GetParamDateRange(now.AddDate(0, -1, 0).Format("2006-01-02"), now.Format("2006-01-02"))
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	liveID, err := c.GetParamInt64("live_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	role, guildInfo, err := guildrole.UserGuildRole(c.UserID())
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !role.IsGuildManager() {
		return nil, actionerrors.ErrNoAuthority
	}
	if !role.IsGuildOwner() {
		isAssigned, err := guildagent.IsAssigned(liveID, c.UserID())
		if err != nil {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
		if !isAssigned {
			return nil, actionerrors.ErrCreatorNotAssignedToYou
		}
	}

	var resp guildLiveIncomeResp
	query := transactionlog.ADB().
		Select("SUM("+transactionlog.RevenueExpr()+" / 100) AS total").
		Where("suborders_num = ? AND to_id = ? AND status = ? AND type = ?", guildInfo.ID, liveID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", incomeTypes)
	query = addIncomeTimeFilter(query, "confirm_time", startDate, endDate)
	err = query.Scan(&resp).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	db := transactionlog.DB()
	if servicedb.Driver != servicedb.DriverSqlite {
		// SQLite 不支持 FORCE INDEX 关键字，SQLite 强制索引关键字：INDEXED BY
		// 线上大部分执行时都使用了 idx_toid_type_status_confirmtime
		// 部分用户因执行计划有时使用错误索引查询时扫描行数过大，导致执行超时
		// 强制指定 idx_toid_type_status_confirmtime 解决查询缓慢
		db = db.Table(
			transactionlog.TransactionLog{}.TableName() + " AS t FORCE INDEX(idx_toid_type_status_confirmtime)",
		)
	}

	db = db.
		Select("confirm_time AS c_time, from_id, title, ("+transactionlog.RevenueExpr()+" / 100) AS revenue, status, gift_id, attr, num").
		Where("suborders_num = ? AND to_id = ? AND status = ? AND type = ?", guildInfo.ID, liveID, transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", incomeTypes)
	db = addIncomeTimeFilter(db, "confirm_time", startDate, endDate)

	var totalCount int64
	err = db.Count(&totalCount).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Pagination = goutil.MakePagination(totalCount, page, pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]guildLiveIncomeItem, 0)
		return resp, nil
	}
	db = resp.Pagination.ApplyTo(db)
	err = db.Order("confirm_time DESC").Scan(&resp.Data).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	fromUserIDs := make([]int64, len(resp.Data))
	for i := range resp.Data {
		fromUserIDs[i] = resp.Data[i].FromID
	}
	userInfos, err := mowangskuser.FindSimpleMap(fromUserIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	for i := range resp.Data {
		if u := userInfos[resp.Data[i].FromID]; u != nil {
			resp.Data[i].Username = u.Username
		}
		resp.Data[i].Title = getIncomeTitle(resp.Data[i].Title, resp.Data[i].Attr, resp.Data[i].GiftID, resp.Data[i].Num, true)
	}

	return resp, nil
}

// ActionGuildsProfit 平台获取所有公会收益信息
// TODO: 相关面板迁移到 Grafana 后可删除此接口
/**
 * @api {get} /api/v2/admin/guild/guildsprofit 平台获取所有公会收益信息
 * @apiExample {curl} Example usage:
 *     curl -i http://{{host}}/api/v2/admin/guild/guildsprofit
 * @apiSampleRequest /api/v2/admin/guild/guildsprofit
 *
 * @apiVersion 0.1.0
 * @apiName guildsprofit
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [guild_id=0] 公会 ID
 * @apiParam {String} [guild_name=""] 公会名
 * @apiParam {String} [start_date=一个月前] 筛选数据的开始日期，如：2019-05-05
 * @apiParam {String} [end_date=今天] 筛选数据的结束日期，如：2019-05-05
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页多少条数据
 * @apiParam {String} [sort=guild_id.desc] 排序规则
 *
 * @apiSuccess {Number} code 请求状态
 * @apiSuccess {object} info 公会收益信息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "total_profit": 100.29,
 *         "pagination": {
 *           "p": 1,
 *           "count": 2,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *         "Datas": [
 *           {
 *             "id": 1,
 *             "guild_name": "三体会",
 *             "gross_profit": 100,
 *             "duration": 240306210
 *           }
 *         ]
 *       }
 *     }
 */
func ActionGuildsProfit(c *handler.Context) (handler.ActionResponse, error) {
	access, err := liveuser.IsProfitAdmin(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}

	// pagination
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// time range
	now := goutil.TimeNow()
	startDate, endDate, err := c.GetParamDateRange(now.AddDate(0, -1, 0).Format("2006-01-02"), now.Format("2006-01-02"))
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildID, err := c.GetDefaultParamInt64("guild_id", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// TODO: 目前输入任何字符都能搜索，之后需要对输入的关键字做限制
	guildName, _ := c.GetParamString("guild_name")

	sortStr := c.GetDefaultParam("sort", "guild_id.desc")
	_, _, ok := utils.ParseSortStr(sortStr, guildIncomeSortMapSupported)
	if !ok {
		return nil, actionerrors.ErrParams
	}
	result, err := getGuildProfit(guildID, guildName, startDate, endDate, page, pageSize, sortStr)
	return result, err
}

// ProfitDataType used in ProfitResult
type ProfitDataType struct {
	ID          int64         `json:"id"`
	GuildName   string        `json:"guild_name"`
	GrossProfit util.Float2DP `json:"gross_profit"`
	Duration    int64         `json:"duration"`
}

// ProfitResult is the result type of ActionGuildsProfit
type ProfitResult struct {
	Data       []ProfitDataType  `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

func getGuildProfit(guildID int64, guildName string, startDate, endDate time.Time, p, pageSize int64, sort string) (*ProfitResult, error) {
	db := transactionlog.ADB()
	db = db.Where("status = ? AND type = ?", transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", transactionlog.GiftAttrs())
	db = addIncomeTimeFilter(db, "confirm_time", startDate, endDate)
	if guildID != 0 {
		db = db.Where("suborders_num = ?", guildID)
	}

	var guildMap map[int64]*guild.Guild
	if guildName != "" {
		guilds, err := guild.SearchByGuildName(guildName)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if len(guilds) == 0 {
			pr := &ProfitResult{
				Data:       make([]ProfitDataType, 0),
				Pagination: goutil.MakePagination(0, p, pageSize),
			}
			return pr, nil
		}

		ids := make([]int64, 0, len(guilds))
		for _, v := range guilds {
			ids = append(ids, v.ID)
		}
		db = db.Where("suborders_num IN (?)", ids)
		guildMap = goutil.ToMap(guilds, "ID").(map[int64]*guild.Guild)
	}
	var count int64
	err := db.Select("COUNT(DISTINCT suborders_num) AS count").Row().Scan(&count)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	pagination := goutil.MakePagination(count, p, pageSize)
	if !pagination.Valid() {
		pr := &ProfitResult{
			Data:       make([]ProfitDataType, 0),
			Pagination: pagination,
		}
		return pr, nil
	}

	var profits []struct {
		GuildID   int64   `gorm:"column:guild_id"`
		GuildName string  `gorm:"column:guild_name"`
		Income    float64 `gorm:"column:income"`
	}
	db = db.Select("suborders_num AS guild_id, SUM(" + transactionlog.RevenueExpr() + " / 100) AS income").Group("suborders_num")
	if sort == "guild_id.desc" {
		// 目前仅按公会 ID 进行排序
		db = db.Order("suborders_num DESC")
	} else {
		db = db.Order("suborders_num ASC")
	}
	db = pagination.ApplyTo(db)
	err = db.Find(&profits).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var guildIDs []int64
	for _, v := range profits {
		guildIDs = append(guildIDs, v.GuildID)
	}
	if len(guildMap) == 0 && len(guildIDs) >= 0 {
		gs := make([]*guild.Guild, 0, len(guildIDs))
		err := guild.Guild{}.DB().Select("id, name").Where("id IN (?)", guildIDs).Find(&gs).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		guildMap = goutil.ToMap(gs, "ID").(map[int64]*guild.Guild)
	}

	durationByGuild, err := livelog.GetGuildTotalDuration(guildIDs, startDate, endDate.AddDate(0, 0, 1))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	guilds := make([]ProfitDataType, 0, len(profits))
	for _, v := range profits {
		pdt := ProfitDataType{
			ID:          v.GuildID,
			GrossProfit: util.Float2DP(v.Income),
			Duration:    durationByGuild[v.GuildID],
		}
		if g := guildMap[v.GuildID]; g != nil {
			pdt.GuildName = g.Name
		}
		guilds = append(guilds, pdt)
	}
	// 调整返回的数据结构
	return &ProfitResult{
		Data:       guilds,
		Pagination: pagination,
	}, nil
}

// ActionGuildProfitView 平台获取公会收益详情
/**
 * @api {get} /api/v2/admin/guild/guildprofitview 平台获取公会收益详情
 * @apiExample {curl} Example usage:
 *     curl -i http://{{host}}/api/v2/admin/guild/guildprofitview
 * @apiSampleRequest /api/v2/admin/guild/guildprofitview
 *
 * @apiVersion 0.1.0
 * @apiName guildprofitview
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {String} [start_date=一个月前] 筛选数据的开始日期，如：2019-05-05
 * @apiParam {String} [end_date=今天] 筛选数据的结束日期，如：2019-05-05
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页多少条数据
 *
 * @apiSuccess {Number} code 请求状态
 * @apiSuccess {object} info 公会收益信息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "guild": {
 *           "id": 1,
 *           "name": "公会名"
 *         },
 *         "pagination": {
 *           "p": 1,
 *           "count": 2,
 *           "pagesize": 20,
 *           "maxpage": 1
 *         }
 *         "Datas": [
 *           {
 *             "date": "2019-06-04",
 *             "gross_profit": 100,
 *             "creator_num": 6,
 *             "duration": 240306210
 *           }
 *         ]
 *       }
 *     }
 */
func ActionGuildProfitView(c *handler.Context) (handler.ActionResponse, error) {
	access, err := liveuser.IsProfitAdmin(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal.New(err, nil)
	}
	if !access {
		return nil, actionerrors.ErrNoAuthority
	}
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// time range
	now := goutil.TimeNow()
	startDate, endDate, err := c.GetParamDateRange(now.AddDate(0, -1, 0).Format("2006-01-02"), now.Format("2006-01-02"))
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	guildID, err := c.GetDefaultParamInt64("guild_id", 0)
	if err != nil || guildID == 0 {
		return nil, actionerrors.ErrParams
	}
	result, err := getGuildProfitView(guildID, startDate, endDate.AddDate(0, 0, 1), page, pageSize)
	return result, err
}

// ProfitViewDataType used in ProfitViewResult
type ProfitViewDataType struct {
	Date        string        `json:"date"`
	GrossProfit util.Float2DP `json:"gross_profit"`

	CreatorNum int64 `json:"creator_num"` // 开播人数 / 人
	Duration   int64 `json:"duration"`    // 直播时长
}

// ProfitViewResult is the result type of ActionGuildProfitView
type ProfitViewResult struct {
	Guild struct {
		ID   int64  `json:"id"`
		Name string `json:"name"`
	} `json:"guild"`

	Pagination goutil.Pagination    `json:"pagination"`
	Data       []ProfitViewDataType `json:"Datas"`
}

func getGuildProfitView(guildID int64, startTime, endTime time.Time, p, pageSize int64) (*ProfitViewResult, error) {
	var guildOne guild.Guild
	err := service.DB.Take(&guildOne, "id = ? AND checked = ?", guildID, guild.CheckedPass).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrGuildNotExist
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	db := transactionlog.ADB().
		Where("confirm_time >= ? AND confirm_time < ?", startTime.Unix(), endTime.Unix()).
		Where("status = ? AND type = ?", transactionlog.StatusSuccess, transactionlog.TypeGuildLive).
		Where("attr IN (?)", transactionlog.GiftAttrs()).
		Where("suborders_num = ?", guildID)
	var count int64
	err = db.Select("COUNT(DISTINCT FROM_UNIXTIME(confirm_time, '%Y-%m-%d')) AS count").Row().Scan(&count)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	pagination := goutil.MakePagination(count, p, pageSize)

	var (
		out []struct {
			Income float64 `gorm:"column:income"`
			Date   string  `gorm:"column:date"`
		}
		dailyCreatorNum map[string]int64
		dailyDuration   map[string]int64
	)

	if pagination.Valid() {
		db = db.Select("SUM(" + transactionlog.RevenueExpr() + " / 100) AS income, " +
			"FROM_UNIXTIME(confirm_time, '%Y-%m-%d') AS date")
		db = db.Group("date")
		db = db.Order("date DESC")

		db = pagination.ApplyTo(db)
		err = db.Find(&out).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		dailyCreatorNum, dailyDuration, err = livelog.GetDailyCreatorNumAndDuration(guildID, startTime, endTime.AddDate(0, 0, 1))
		if err != nil {
			return nil, actionerrors.ErrServerInternal.New(err, nil)
		}
	}

	profits := make([]ProfitViewDataType, 0, len(out))
	for _, v := range out {
		profits = append(profits, ProfitViewDataType{
			Date:        v.Date,
			GrossProfit: util.Float2DP(v.Income),

			CreatorNum: dailyCreatorNum[v.Date],
			Duration:   dailyDuration[v.Date],
		})
	}
	// 调整返回的数据结构
	r := ProfitViewResult{
		Pagination: pagination,
		Data:       profits,
	}
	r.Guild.ID = guildOne.ID
	r.Guild.Name = guildOne.Name
	return &r, nil
}

func getIncomeTypes(c *handler.Context) (types []int64, err error) {
	incomeType, err := c.GetDefaultParamInt64("type", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	switch incomeType {
	case incomeTypeGift:
		types = transactionlog.GiftAttrs()
	case incomeTypeNoble:
		types = transactionlog.NobleAttrs()
	case incomeTypeSuperFan:
		types = transactionlog.SuperFanAttrs()
	default:
		return nil, actionerrors.ErrParams
	}
	return
}

// NOTICE: endDate 需要为截止日的零点
// 例：要查询 2021.09 月（2021.09.01 00:00:00~2021.09.30 23:59:59）的数据，则 startDate 应为 2021-09-01 00:00:00，endDate 应为 2021-09-30 00:00:00
func addIncomeTimeFilter(query *gorm.DB, column string, startDate, endDate time.Time) *gorm.DB {
	return query.Where(column+" BETWEEN ? AND ?", startDate.Unix(), endDate.AddDate(0, 0, 1).Unix()-1)
}
