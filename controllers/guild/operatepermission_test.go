package guild

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const checkCodeUserID = 2333333

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(checkCodeParam{}, "identify_code", "objective_type")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(checkCodeParam{}, "identify_code", "objective_type")
}

func TestActionGetUserMobile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 用户未绑定手机号
	ctx := handler.NewTestContext(http.MethodPost, "", true, nil)
	_, err := ActionGetUserMobile(ctx)
	assert.EqualError(err, "用户未绑定手机")

	ctx = handler.NewTestContext(http.MethodPost, "", true, nil)
	ctx.User().Mobile = "132******27"
	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return &sso.Account{
			Mobile: "***********",
		}, nil
	})
	defer cancel()

	res, err := ActionGetUserMobile(ctx)
	require.NoError(err)
	assert.Equal("132******27", res.(string))
}

func TestActionSetUserPermission(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 添加 key
	key := keys.GuildUserCritialOperation1.Format(checkCodeUserID)
	require.NoError(service.Redis.Set(key, "芜湖起飞", time.Minute).Err())
	defer func() {
		require.NoError(service.Redis.Del(key).Err())
	}()

	redisParam := map[string]interface{}{
		vcode.FieldVCode:          "555513",
		vcode.FieldVCodeCounter:   0,
		vcode.FieldVCodeObjective: vcode.ObjectiveTypeOperateGuild,
	}
	require.NoError(service.Redis.HMSet("mobile_86***********", redisParam).Err())
	defer func() {
		require.NoError(service.Redis.Del("mobile_86***********").Err())
	}()

	param := checkCodeParam{
		IdentifyCode:  "555513",
		ObjectiveType: vcode.ObjectiveTypeOperateGuild,
	}
	// 验证失败
	ctx := handler.NewTestContext(http.MethodPost, "", true, param)
	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return nil, &mrpc.ClientError{Code: actionerrors.CodeSSOGetUserNotFound, Message: "用户不存在"}
	})
	_, err := ActionSetUserPermission(ctx)
	assert.EqualError(err, "该用户不存在")
	cancel()

	// 验证成功
	ctx = handler.NewTestContext(http.MethodPost, "", true, param)
	ctx.User().ID = checkCodeUserID
	r, err := service.SSO.Session("testtoken", "127.0.0.1")
	require.NoError(err)
	ctx.C.Request.AddCookie(&http.Cookie{Name: "token", Value: r.Token})
	cancel = mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return &sso.Account{
			Region: 86,
			Mobile: "***********",
		}, nil
	})
	defer cancel()

	res, err := ActionSetUserPermission(ctx)
	require.NoError(err)
	assert.Equal("验证成功", res)

	dbRes, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.Equal(r.Token, dbRes)
}

func TestCheckPermission(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config.Conf.Params.GuildOperate.GuildPermissionSwitch = true
	defer func() {
		config.Conf.Params.GuildOperate.GuildPermissionSwitch = false
	}()
	key := keys.GuildUserCritialOperation1.Format(checkCodeUserID)
	require.NoError(service.Redis.Del(key).Err())
	assert.EqualError(checkPermission(checkCodeUserID, "testToken"), "用户需要身份验证")

	require.NoError(service.Redis.Set(key, "testToken", time.Minute).Err())
	require.NoError(checkPermission(checkCodeUserID, "testToken"))
}
