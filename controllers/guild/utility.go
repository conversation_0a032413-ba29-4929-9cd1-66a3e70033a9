package guild

import (
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
)

// GetUserPassedGuildID gets ID of passed guild
// NOTICE: 此函数会直接返回 ActionError，故放在 controllers 中
func GetUserPassedGuildID(userID int64) (int64, error) {
	g := new(guild.Guild)
	err := service.DB.Table(guild.TableName()).
		Select("id").
		Where("user_id = ? AND checked = ?", userID, guild.CheckedPass).First(g).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return 0, actionerrors.ErrGuildNotExist
		}
		return 0, actionerrors.ErrServerInternal.New(err, nil)
	}

	return g.ID, nil
}

// TODO: 这个函数必要性存疑
func parseSortStr(s string) string {
	if strings.HasSuffix(s, ".desc") {
		s = s[0:len(s)-5] + " DESC"
	}
	return s
}
