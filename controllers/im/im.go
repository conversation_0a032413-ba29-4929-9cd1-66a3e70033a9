// Package im 实现了直播间聊天室服务的广播功能
// TODO: 需要重构
package im

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	invalidLimit = 5
)

var (
	upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin:     func(r *http.Request) bool { return true },
	}
)

// Handler handler
func Handler() *handler.Handler {
	initController()
	h := handler.Handler{
		Name:        "",
		Middlewares: gin.HandlersChain{UnmarshalUser},
		Actions: map[string]*handler.Action{
			"ws": handler.NewAction(handler.GET, ActionWS, false),

			// TODO: 移除活动相关 ws
			// "ws/activity": handler.NewAction(handler.GET, ActionWSActivity, false),
		},
	}
	return &h
}

// ActionWS 连接 websocket 的 Action
// NOTICE: 这个 ActionFunc 不能使用 handler.NewAction
/**
 * @api {websocket} /ws 直播间内收取消息
 * @apiDescription 客户端使用 websocket 的 ping 方法进行心跳，网页使用字符串「❤️」进行心跳 \
 * 从预览页进入直播间需要断开重连
 * @apiVersion 0.1.0
 * @apiGroup im
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [preview=0] 是否为直播间预览页，0: 否；1: 是
 * @apiParam {number=0,1} [nocompression=0] 是否强制接收未压缩的消息，1: 开启
 *
 * @apiSuccessExample {json} 连接成功收到消息
 *   {
 *     "type": "user",
 *     "event": "connect"
 *     "user": {
 *       "user_id": 12
 *     }
 *   }
 *
 * @apiSuccessExample {text} 网页心跳回应
 *   ❤️
 */
func ActionWS(c *handler.Context) (handler.ActionResponse, error) {
	wsConn, err := NewWSConn(c, DefaultBc, TypeBroadcasterRoom)
	if err != nil {
		return nil, err
	}
	defer wsConn.Close()

	wsConn.Receive()
	return nil, handler.ErrRawResponse
}

// ActionWSActivity 连接 websocket 的 Action
// NOTICE: 这个 ActionFunc 不能使用 handler.NewAction
func ActionWSActivity(c *handler.Context) (handler.ActionResponse, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	exists, err := room.Exists(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}
	wsConn, err := NewWSConn(c, DefaultBc, TypeBroadcasterActivity)
	if err != nil {
		return nil, err
	}

	defer wsConn.Close()
	wsConn.roomID = roomID
	wsConn.ActivityReceive()
	return nil, handler.ErrRawResponse
}
