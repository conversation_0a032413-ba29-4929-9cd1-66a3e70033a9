package im

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/cache/usersession"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/guest"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewWsConn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/ws?room_id=123", false, nil)
	_, err := NewWSConn(c, DefaultBc, TypeBroadcasterRoom)
	assert.Equal(actionerrors.ErrConnRole, err)

	// 测试没有中间件的情况
	c = handler.NewTestContext(http.MethodGet, "/ws?room_id=123", false, nil)
	_, err = NewWSConn(c, DefaultBc, TypeBroadcasterRoom)
	assert.Equal(actionerrors.ErrConnRole, err)

	cancel := mrpc.SetMock(vip.URLUserVips, func(interface{}) (interface{}, error) {
		return handler.M{"vips": handler.M{}}, nil
	})
	defer cancel()
	cancel2 := mrpc.SetMock(liverpc.URILiveRoomInfo, func(interface{}) (interface{}, error) {
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: 123},
		}}, nil
	})
	defer cancel2()
	cancel3 := mrpc.SetMock(liverpc.URILiveRoomUserInfo, func(interface{}) (interface{}, error) {
		return handler.M{"user": &liveuser.User{
			UID: 12,
		}}, nil
	})
	defer cancel3()

	ch := make(chan int)

	// 测试中间件
	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		gc, _ := gin.CreateTestContext(w)
		gc.Request = r
		c := &handler.Context{C: gc}
		c.C.Set("user", user.User{})
		c.Equip().FromApp = true
		c.Equip().OS = goutil.Android
		c.Equip().AppVersion = "5.6.7"
		conn, err := NewWSConn(c, DefaultBc, TypeBroadcasterRoom)
		require.NoError(err)
		require.NotNil(conn)
		assert.True(conn.supportCompression)
		assert.EqualValues(123, conn.roomID)
		conn.Close()
		ch <- 1
	}
	_, _, err = websocket.DefaultDialer.Dial(wsURLStr("/hook?room_id=123"), nil)
	require.NoError(err)

	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		gc, _ := gin.CreateTestContext(w)
		gc.Request = r
		c := &handler.Context{C: gc}
		c.C.Set("user", User{id: 12})
		conn, err := NewWSConn(c, DefaultBc, TypeBroadcasterRoom)
		require.NoError(err)
		require.NotNil(conn)
		assert.True(conn.supportCompression)
		conn.Close()
		assert.NotNil(conn.userInfo)
		ch <- 2
	}
	_, _, err = websocket.DefaultDialer.Dial(wsURLStr("/hook?room_id=123"), nil)
	require.NoError(err)

	assert.Equal(1, <-ch)
	assert.Equal(2, <-ch)
}

func TestWSConnSetConn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ch := make(chan int)
	timer := time.NewTimer(time.Second)
	defer timer.Stop()
	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		ws, err := upgrader.Upgrade(w, r, nil)
		require.NoError(err)
		defer ws.Close()
		ws.SetPongHandler(func(data string) error {
			assert.Equal(heartbeat, []byte(data))
			ch <- 1
			return nil
		})
		require.NoError(ws.WriteControl(websocket.PingMessage, heartbeat, goutil.TimeNow().Add(writeWait)))
		for {
			messageType, p, err := ws.ReadMessage()
			if err != nil {
				logger.Debugf("read: %v", err)
				return
			}
			if err := ws.WriteMessage(messageType, p); err != nil {
				logger.Debugf("write: %v", err)
				return
			}
		}
	}
	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/hook"), nil)
	require.NoError(err)
	conn := WSConn{Bc: DefaultBc, C: handler.NewTestContext("GET", "/ws", false, nil)}
	conn.setConn(ws)
	conn.run()
	go conn.Receive()
	defer conn.Close()
	select {
	case <-timer.C:
		assert.Fail("test ping pong timeout")
	case <-ch:
		timer.Stop()
	}
}

func TestHandleMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(liverpc.URILiveRoomInfo, func(input interface{}) (interface{}, error) {
		if input.(map[string]interface{})["room_id"].(int64) != existsRoomID {
			return nil, &mrpc.ClientError{
				Code: actionerrors.ErrCannotFindRoom.ErrorCode(),
			}
		}
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: existsRoomID},
		}}, nil
	})
	defer cancel()
	ch := DefaultBc.Broadcaster(existsRoomID, true)
	require.NoError(service.IMRedis.Del(ch.KeyNotify, ch.KeyUsers, ch.keyMember).Err())

	conn, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err, "WebSocket handshake failed")
	_ = conn.SetReadDeadline(goutil.TimeNow().Add(20 * time.Second))
	_ = conn.SetWriteDeadline(goutil.TimeNow().Add(20 * time.Second))

	watcherConn, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err, "WebSocket handshake failed")
	_ = watcherConn.SetReadDeadline(goutil.TimeNow().Add(20 * time.Second))
	_ = watcherConn.SetWriteDeadline(goutil.TimeNow().Add(20 * time.Second))

	// 模仿注册用户
	testUser := User{id: 123, userID: "123"}
	testUser.SetRole(RoleMember)
	wsUser := WSConn{UUID: uuid.New(), User: testUser, Bc: DefaultBc}
	wsUser.setConn(conn)
	wsUser.run()
	defer wsUser.Close()
	wsUser.C = handler.CreateTestContext(false)
	wsUser.C.C.Request, _ = http.NewRequest("GET", "", nil)

	// 模仿注册用户
	watcherUser := User{id: 456, userID: "456"}
	watcherUser.SetRole(RoleTest)
	watcher := WSConn{UUID: uuid.New(), User: watcherUser, Bc: DefaultBc}
	watcher.setConn(watcherConn)
	watcher.run()
	defer watcher.Close()
	watcher.C = handler.CreateTestContext(false)
	watcher.C.C.Request, _ = http.NewRequest("GET", "", nil)
	b := watcher.Bc.Broadcaster(existsRoomID, true)
	b.ChFromUser <- &userMethod{Method: methodJoin, Conn: &watcher, Operator: operatorUser}
	time.Sleep(time.Second)

	_, _, err = conn.ReadMessage()
	require.NoError(err)
	wsUser.handleMessage(websocket.TextMessage, heartbeat)
	_, pong, err := conn.ReadMessage()
	require.NoError(err)
	assert.Equal(heartbeat, pong)

	t.Run("undefined action", func(t *testing.T) {
		receive := receiveMessage{RoomID: existsRoomID}
		receive.Action = "123test"
		receive.UUID = uuid.New().String()
		receiverTemp, err := json.Marshal(receive)
		require.NoError(err, "json.Marshal receive message failed, err: %v", err)

		wsUser.roomID = receive.RoomID
		assert.False(wsUser.handleMessage(websocket.TextMessage, receiverTemp), "handleMessage return an incorrect value")
		var actualSend msgJoinLeave
		err = conn.ReadJSON(&actualSend)
		assert.NoError(err)
		assert.True(actualSend.Code == 501010000, "request wrong")
	})
	// receive.Action = eventJoin
	roomIDs := [3]int64{existsRoomID, existsRoomID, 2}
	errorCode := [3]int{0, 0, 500030004}
	require.NoError(service.IMRedis.SAdd(
		keys.KeyIMRoomNotify1.Format(existsRoomID),
		testUser.ID()).Err())
	for i := range roomIDs {
		t.Run(fmt.Sprintf("test join room %d", roomIDs[i]), func(t *testing.T) {
			newUUID := uuid.New().String()
			receive := map[string]interface{}{
				"uuid":   newUUID,
				"action": "join",
			}
			if i&1 == 0 {
				// 房间号是数字
				receive["room_id"] = roomIDs[i]
			} else {
				// 房间号是字符串
				receive["room_id"] = strconv.FormatInt(roomIDs[i], 10)
			}
			receiverTemp, err := json.Marshal(receive)
			require.NoError(err)
			wsUser.roomID = roomIDs[i]
			assert.True(wsUser.handleMessage(websocket.TextMessage, receiverTemp), "handleMessage return an incorrect value")

			timeout := false
			sended := false
			go func() {
				timer := time.NewTimer(5 * time.Second)
				<-timer.C
				timeout = true
			}()
			_ = conn.SetReadDeadline(goutil.TimeNow().Add(5 * time.Second))
			for !(sended || timeout) {
				_, p, err := conn.ReadMessage()
				require.NoError(err)
				logger.Debug(string(p))
				var actualSend msgJoinLeave
				err = json.Unmarshal(p, &actualSend)
				if err != nil {
					continue
				}
				if actualSend.UUID != newUUID {
					continue
				}
				sended = true
				assert.Equalf(errorCode[i], actualSend.Code, "index: %d", i)
			}
			assert.True(sended, "send request failed")
		})
	}

	// receive.Action = eventLeave
	errorCode[1], errorCode[2] = 0, 0 // 只要房间号正常，无论用户是否在房间里或者房间找不到，都返回成功
	for i := range roomIDs {
		t.Run(fmt.Sprintf("test leave room %d", roomIDs[i]), func(t *testing.T) {
			receive := map[string]interface{}{
				"uuid":   uuid.New().String(),
				"action": "leave",
			}
			if i&1 == 0 {
				// 房间号是数字
				receive["room_id"] = roomIDs[i]
			} else {
				// 房间号是字符串
				receive["room_id"] = strconv.FormatInt(roomIDs[i], 10)
			}
			receiverTemp, err := json.Marshal(receive)
			require.NoError(err)
			wsUser.roomID = roomIDs[i]
			assert.True(wsUser.handleMessage(websocket.TextMessage, receiverTemp),
				"handleMessage return an incorrect value")

			timeout := false
			sended := false
			go func() {
				timer := time.NewTimer(5 * time.Second)
				<-timer.C
				timeout = true
			}()
			for !(sended || timeout) {
				var actualSend msgJoinLeave
				err = conn.ReadJSON(&actualSend)
				assert.NoError(err)
				if actualSend.UUID != receive["uuid"].(string) {
					continue
				}
				sended = true
				assert.Equal(errorCode[i], actualSend.Code, roomIDs[i])
			}
			assert.True(sended, "send request failed")
		})
	}

	t.Run("test receive notify message", func(t *testing.T) {
		// 通过加入离开房间的广播通知判断是否能收取消息
		// {"type":"member","event":"join","room_id":18113499,"user":{"user_id":123}}
		// {"type":"member","event":"leave","room_id":18113499,"user":{"user_id":123}}
		_ = watcherConn.SetReadDeadline(goutil.TimeNow().Add(5 * time.Second))
		var received int
		for {
			_, p, err := watcherConn.ReadMessage()
			if err != nil {
				break
			}
			logger.Debug(string(p))
			var notify userJoinLeaveNotify
			err = json.Unmarshal(p, &notify)
			if err != nil {
				continue
			}
			if notify.User.UserID == 123 && notify.Type == liveim.TypeMember && (notify.Event == liveim.EventJoin || notify.Event == liveim.EventLeave) {
				received++
			}
		}
		assert.Equal(2, received)
	})
}

func TestHandleActivityMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conn, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err, "WebSocket handshake failed")
	_ = conn.SetReadDeadline(goutil.TimeNow().Add(20 * time.Second))
	_ = conn.SetWriteDeadline(goutil.TimeNow().Add(20 * time.Second))

	// 模仿注册用户
	testUser := User{id: 123, userID: "123"}
	testUser.SetRole(RoleMember)
	wsUser := WSConn{UUID: uuid.New(), User: testUser, Bc: DefaultBc, Type: TypeBroadcasterActivity}
	wsUser.setConn(conn)
	wsUser.run()
	defer wsUser.Close()
	wsUser.C = handler.CreateTestContext(false)
	wsUser.C.C.Request, _ = http.NewRequest("GET", "", nil)

	_, _, err = conn.ReadMessage()
	require.NoError(err)
	wsUser.handleActivityMessage(heartbeat)
	_, pong, err := conn.ReadMessage()
	require.NoError(err)
	assert.Equal(heartbeat, pong)

	t.Run("undefined action", func(t *testing.T) {
		receive := receiveMessage{RoomID: existsRoomID}
		receive.Action = "123test"
		receive.UUID = uuid.New().String()
		receiverTemp, err := json.Marshal(receive)
		require.NoError(err, "json.Marshal receive message failed, err: %v", err)

		assert.False(wsUser.handleActivityMessage(receiverTemp), "handleMessage return an incorrect value")
		var actualSend msgJoinLeave
		err = conn.ReadJSON(&actualSend)
		assert.NoError(err)
		assert.True(actualSend.Code == 501010000, "request wrong")
	})

	t.Run("connect action", func(t *testing.T) {
		receive := receiveMessage{RoomID: existsRoomID}
		receive.Action = "connect"
		receive.UUID = uuid.New().String()
		receiverTemp, err := json.Marshal(receive)
		require.NoError(err, "json.Marshal receive message failed, err: %v", err)
		wsUser.roomID = 11111
		assert.True(wsUser.handleActivityMessage(receiverTemp), "handleMessage return an incorrect value")
	})
}

func TestWSConnCleanupChannel(t *testing.T) {
	assert := assert.New(t)

	conn := WSConn{
		chSend:  make(chan wsMessage, 1),
		chClose: make(chan struct{}, 1),
	}
	var w sync.WaitGroup
	w.Add(1)
	go func() {
		conn.chSend <- wsMessage{Type: websocket.TextMessage, Data: []byte{}}
		conn.chClose <- struct{}{}
		w.Done()
	}()
	w.Wait()
	conn.CleanupChannel()
	assert.Len(conn.chSend, 1)
	assert.Len(conn.chClose, 1)
	conn.status = statusClose
	conn.CleanupChannel()
	assert.Empty(conn.chSend)
	assert.Empty(conn.chClose)
}

func TestWSConnTimeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conn, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err, "WebSocket handshake failed")

	orgReadTime := readWait
	defer func() {
		readWait = orgReadTime
	}()
	readWait = time.Second

	wsConn := WSConn{Bc: DefaultBc, C: handler.NewTestContext("GET", "/ws", false, nil)}
	wsConn.setConn(conn)
	wsConn.run()
	defer wsConn.Close()

	_, _, err = wsConn.conn.ReadMessage() // 连接成功的通知消息
	require.NoError(err)
	wsConn.handleMessage(websocket.TextMessage, []byte(heartbeat))
	before := goutil.TimeNow()
	_, p, err := wsConn.conn.ReadMessage()
	assert.NoError(err)
	assert.Equal(heartbeat, p)

	_, _, err = wsConn.conn.ReadMessage()
	require.Error(err)
	assert.Contains(err.Error(), "i/o timeout")
	after := goutil.TimeNow()
	assert.GreaterOrEqual(after.Sub(before).Seconds(), time.Second.Seconds())
}

func TestWSConnFindUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 10
	)
	cleanup := mrpc.SetMock(liverpc.URILiveRoomUserInfo,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) != testUserID {
				return nil, &mrpc.ClientError{
					Code: actionerrors.ErrCannotFindUser.ErrorCode(),
				}
			}
			return handler.M{
				"user": &liveuser.Simple{
					UID: testUserID,
				},
				"has_invisible_roles": true,
				"is_valuable_user":    true,
				"is_invisible":        true,
			}, nil
		})
	defer cleanup()

	wc := WSConn{
		User: User{
			id: testUserID,
		},
	}
	require.NoError(wc.findUserInfo())
	require.NotNil(wc.userInfo)
	require.NotNil(wc.userInfo.User)
	assert.Equal(testUserID, wc.userInfo.User.UserID())
	assert.True(wc.userInfo.HasInvisibleRoles)
	assert.True(wc.userInfo.IsValuableUser)
	assert.True(wc.userInfo.IsInvisible)

	wc = WSConn{
		User: User{
			id: 99999,
		},
	}
	require.NoError(wc.findUserInfo())
	assert.Nil(wc.userInfo)

	// 测试游客
	var testSessionKey = "testsessionKey"
	wc = WSConn{C: handler.NewTestContext("GET", "/ws", false, nil)}
	wc.C.C.Request.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName, Value: testSessionKey})
	us := usersession.UserSession{
		Guest: &guest.Guest{
			UserID: "test-guest-id",
		},
	}
	b, _ := json.Marshal(us)
	cacheKey := config.Conf.HTTP.SessionPrefix + testSessionKey
	require.NoError(service.LRURedis.Set(cacheKey, string(b), time.Minute).Err())
	require.NoError(wc.findUserInfo())
	assert.Equal(us.Guest.UserID, wc.GuestID)
	assert.Nil(wc.userInfo)
}

func TestWsConn_createLog(t *testing.T) {
	assert := assert.New(t)

	conn := &WSConn{
		C: handler.NewTestContext("GET", "/ws", false, nil),
		User: User{
			id: 123,
		},
		roomID: 123,
	}
	conn.createLog()
	assert.False(conn.logOID.IsZero())
}

func TestWSConn_updateLogRenewTime(t *testing.T) {
	assert := assert.New(t)

	conn := &WSConn{
		C: handler.NewTestContext("GET", "/ws", false, nil),
		User: User{
			id: 123,
		},
		roomID: 123,
		logOID: primitive.NewObjectID(),
	}
	assert.NotPanics(func() { conn.updateLogRenewTime() })
}

func TestWSConn_updateLogJoinUUID(t *testing.T) {
	assert := assert.New(t)

	testUUID := "2a2418b9-f422-42a9-9a76-a8c7ad288d76"
	conn := &WSConn{
		C: handler.NewTestContext("GET", "/ws", false, nil),
		User: User{
			id: 123,
		},
		roomID: 123,
		logOID: primitive.NewObjectID(),
	}
	assert.NotPanics(func() { conn.updateLogJoinUUID(testUUID) })
	assert.Equal(testUUID, conn.eventJoinUUID)
}

func TestWSConn_checkConnectRisk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "/ws", false, nil)
	testIP := "*********"
	c.SetClientIP(testIP)
	conn := &WSConn{
		C: c,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := imuserlogs.Collection().DeleteMany(ctx, bson.M{"ip": testIP})
	require.NoError(err)

	require.NotPanics(func() { conn.checkConnectRisk() })
	assert.Equal(riskStatusDisableAllMsg, conn.riskStatus)

	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	require.NotPanics(func() { conn.checkConnectRisk() })
	assert.Equal(riskStatusDisableAllMsg, conn.riskStatus)

	now := goutil.TimeNow()
	inserts := make([]interface{}, 0, 50)
	for i := 0; i < 50; i++ {
		inserts = append(inserts, &imuserlogs.Log{
			IP:        testIP,
			RenewTime: now.Unix(),
		})
	}
	_, err = imuserlogs.Collection().InsertMany(ctx, inserts)
	require.NoError(err)
	require.NotPanics(func() { conn.checkConnectRisk() })
	assert.Equal(riskStatusDisableAllMsg, conn.riskStatus)

	conn = &WSConn{
		isPreview: true,
	}
	require.NotPanics(func() { conn.checkConnectRisk() })
	assert.Equal(riskStatusDisablePurchasedMsg, conn.riskStatus)
}

func TestWSConn_checkJoinRisk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testIP := "*********"
	testJoinUUID := "1b8e5a08-055a-4485-b464-cc7528941bb7"
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := imuserlogs.Collection().DeleteMany(ctx,
		bson.M{
			"$or": bson.A{
				bson.M{"ip": testIP},
				bson.M{"join_uuid": testJoinUUID},
			}})
	require.NoError(err)
	conn := &WSConn{
		C: handler.NewTestContext("GET", "/ws", false, nil),
		User: User{
			id: 123,
		},
		roomID:        123,
		logOID:        primitive.NewObjectID(),
		eventJoinUUID: testJoinUUID,
	}
	assert.NotPanics(func() { conn.checkJoinRisk() })
	assert.Equal(riskStatusDisablePurchasedMsg, conn.riskStatus)

	inserts := make([]interface{}, 0, 50)
	for i := 0; i < 50; i++ {
		inserts = append(inserts, &imuserlogs.Log{
			IP:        testIP,
			JoinUUID:  testJoinUUID,
			RenewTime: goutil.TimeNow().Unix(),
		})
	}
	_, err = imuserlogs.Collection().InsertMany(ctx, inserts)
	require.NoError(err)

	assert.NotPanics(func() { conn.checkJoinRisk() })
	assert.Equal(riskStatusDisableAllMsg, conn.riskStatus)
}

func TestWSConnReceive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(liverpc.URILiveRoomInfo, func(input interface{}) (interface{}, error) {
		if input.(map[string]interface{})["room_id"].(int64) != existsRoomID {
			return nil, &mrpc.ClientError{
				Code: actionerrors.ErrCannotFindRoom.ErrorCode(),
			}
		}
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: existsRoomID},
		}}, nil
	})
	defer cancel()

	var conn *WSConn
	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		ws, err := upgrader.Upgrade(w, r, nil)
		require.NoError(err)
		conn = &WSConn{Bc: DefaultBc, C: handler.NewTestContext("GET", "/ws", false, nil)}
		conn.setConn(ws)
		conn.run()
		defer conn.Close()
		conn.Receive()
	}
	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/hook"), nil)
	require.NoError(err)
	defer ws.Close()
	require.NoError(ws.SetReadDeadline(goutil.TimeNow().Add(time.Second)))
	require.NoError(ws.WriteMessage(websocket.TextMessage, []byte(heartbeat)))
	_, _, err = ws.ReadMessage() // 通知连接成功的消息
	require.NoError(err)
	_, v, err := ws.ReadMessage()
	require.NoError(err)
	defer ws.Close()
	assert.Equal(heartbeat, v)

	testUUID := "ea90a4d0-0f46-11ef-a014-afbe04ef3d01"
	// 错误的 json
	require.NoError(ws.WriteJSON(handler.M{
		"room_id": testUUID,
	}))
	// 加入错误的房间
	require.NoError(ws.WriteJSON(receiveMessage{
		UUID:   testUUID,
		Action: liveim.EventJoin,
		RoomID: existsRoomID + 1,
	}))
	conn.roomID = existsRoomID
	_, v, err = ws.ReadMessage()
	require.NoError(err)
	assert.JSONEq(fmt.Sprintf(`{"type":"room","event":"join","uuid":%q,"room_id":18113500,`+
		`"code":500030004,"info":"无法找到该聊天室"}`, testUUID), string(v))
	// 在不加入房间的情况下直接离开房间
	require.NoError(ws.WriteJSON(receiveMessage{
		UUID:   testUUID,
		Action: liveim.EventLeave,
		RoomID: existsRoomID,
	}))
	conn.roomID = existsRoomID
	_, v, err = ws.ReadMessage()
	require.NoError(err)
	assert.JSONEq(fmt.Sprintf(`{"type":"room","event":"leave","uuid":%q,"room_id":18113499,`+
		`"code":0,"info":"leave room success"}`, testUUID), string(v))
	// 加入正确的房间
	require.NoError(ws.WriteJSON(receiveMessage{
		UUID:   testUUID,
		Action: liveim.EventJoin,
		RoomID: existsRoomID,
	}))
	_, v, err = ws.ReadMessage()
	require.NoError(err)
	assert.JSONEq(fmt.Sprintf(`{"type":"room","event":"join","uuid":%q,"room_id":18113499,`+
		`"code":0,"info":{"room":{"status":{"open":0}}}}`, testUUID), string(v))
	// 离开正确的房间
	require.NoError(ws.WriteJSON(receiveMessage{
		UUID:   testUUID,
		Action: liveim.EventLeave,
		RoomID: existsRoomID,
	}))
	_, v, err = ws.ReadMessage()
	require.NoError(err)
	assert.JSONEq(fmt.Sprintf(`{"type":"room","event":"leave","uuid":%q,"room_id":18113499,`+
		`"code":0,"info":"leave room success"}`, testUUID), string(v))
}

func TestWSConnMakeLoggerFields(t *testing.T) {
	assert := assert.New(t)
	c := handler.NewTestContext("GET", "/ws", false, nil)
	c.Equip().EquipID = "test-equip-id"
	c.Request().RemoteAddr = "*********:0"
	conn := WSConn{
		User:   User{userID: "12"},
		C:      c,
		roomID: 1234,
	}
	f := conn.makeLoggerFields()
	assert.JSONEq(tutil.SprintJSON(logger.Fields{
		"user_id":  "12",
		"ip":       "*********",
		"room_id":  1234,
		"equip_id": "test-equip-id",
	}), tutil.SprintJSON(f))
}

func TestMsgJoinLeaveWSMessage(t *testing.T) {
	assert := assert.New(t)

	msg := msgJoinLeave{}
	res1 := msg.wsMessage(false)
	assert.Equal(websocket.TextMessage, res1.Type)
	assert.JSONEq(`{"type":"","event":"","uuid":"","code":0,"info":null}`,
		string(res1.Data))

	res2 := msg.wsMessage(true)
	assert.Equal(websocket.BinaryMessage, res2.Type)
	assert.NotEqual(res1.Data, res2.Data)

	msg.Info = make(chan int)
	assert.Panics(
		func() { msg.wsMessage(true) },
	)
}

func TestMessenger(t *testing.T) {
	// 验证是否是 messenger
	var _ messenger = &msgConnectSuccess{}
	var _ messenger = &msgJoinLeave{}
	var _ messenger = &imMessage{}
}

func BenchmarkBytes(b *testing.B) {
	heartbeatStr := string(heartbeat)

	// goos: darwin
	// goarch: arm64
	// pkg: github.com/MiaoSiLa/live-service/controllers/im
	// 	BenchmarkBytes
	// 	BenchmarkBytes/string
	// 	BenchmarkBytes/string-8         	218249408	         5.039 ns/op
	// 	BenchmarkBytes/[]byte
	// 	BenchmarkBytes/[]byte-8         	529181803	         2.286 ns/op
	b.Run("string", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = heartbeatStr == string(heartbeat)
			_ = []byte(heartbeatStr)
		}
	})

	b.Run("[]byte", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = bytes.Equal(heartbeat, heartbeat)
		}
	})
}
