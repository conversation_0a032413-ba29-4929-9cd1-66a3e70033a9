package im

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestActionConn(t *testing.T) {
	assert := assert.New(t)
	beforeCount := connCount
	r, _ := ActionConn(nil)
	res := r.(handler.M)
	instanceID := logger.InstanceID()
	assert.Equal(beforeCount, res["conn_count"])
	assert.Equal(instanceID, res["instance_id"])
}
