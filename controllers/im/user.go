package im

// TODO: 待弃用

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// RoleTest 角色为测试用户
	RoleTest UserRole = iota
	// RoleGuest 角色为游客
	RoleGuest
	// RoleMember 角色为注册用户
	RoleMember
)

// TODO: 后面改用 config 中的值
const sessionMemcachePrefix string = "fm:sess:"

// UserRole 标记用户角色，目前有测试用户，游客，注册用户三个
type UserRole int

// UserID 对于注册用户, 该值应为用户 ID 的字符串形式
type UserID string

// User 标记访问的用户
// 对于游客和测试用户，ID = 0
// TODO: 在用户非注册用户的情况，各种操作需要特殊考虑
type User struct {
	role     UserRole
	id       int64
	userID   UserID
	Username string
}

// UnmarshalUser 从 cookies 中获取数据，解析成 User
func UnmarshalUser(c *gin.Context) {
	defer c.Next()
	if unmarshalFromSession(c) {
		return
	}
	unmarshalFromToken(c)
}

// unmarshalFromSession 从 session 得到 User 信息，存入 "user" 字段
func unmarshalFromSession(c *gin.Context) (success bool) {
	sessionKey, err := c.Cookie(config.Conf.HTTP.SessionCookieName)
	if err != nil {
		if err != http.ErrNoCookie {
			logger.WithField("ip", c.ClientIP()).Error(err)
		}
		return
	}

	var val string
	val, err = service.LRURedis.Get(sessionMemcachePrefix + sessionKey).Result()
	if err != nil && err != redis.Nil {
		logger.WithFields(
			logger.Fields{"ip": c.ClientIP(), "session_key": sessionKey}).Debugf("LRURedis: %v", err)
		return
	}

	if val == "" {
		return
	}

	type userInSession struct {
		Guest *struct {
			UserID   string `json:"user_id"`
			Username string `json:"username"`
		} `json:"guest,omitempty"`
		Member *struct {
			ID       int64  `json:"id"`
			Username string `json:"username"`
			Token    string `json:"token"`
			ExpireAt int64  `json:"expire_at"`
		} `json:"user,omitempty"`
	}

	var userTemp userInSession
	err = json.Unmarshal([]byte(val), &userTemp)
	if err != nil {
		logger.WithField("session", val).Debug(err)
		return
	}

	var user User
	token, _ := c.Cookie("token")
	switch {
	case userTemp.Member != nil && userTemp.Member.Token == token && userTemp.Member.ExpireAt > goutil.TimeNow().Unix():
		user.SetRole(RoleMember)
		user.SetUserID(userTemp.Member.ID)
		user.Username = userTemp.Member.Username
		c.Set("user", user)
		success = true
	case userTemp.Guest != nil:
		user.SetRole(RoleGuest)
		user.SetUserID(userTemp.Guest.UserID)
		user.Username = userTemp.Guest.Username
		c.Set("user", user)
		success = true
	}

	return
}

// unmarshalFromToken 从 token 得到 User 信息，存入 "user" 字段
func unmarshalFromToken(c *gin.Context) bool {
	muser, err := user.GetUser(c)
	if err != nil {
		if err != http.ErrNoCookie {
			logger.WithField("ip", c.ClientIP()).Errorf("Get user from token failed: %v", err)
		}
		return false
	}
	if muser == nil {
		return false
	}
	user := User{Username: muser.Username}
	user.SetRole(RoleMember)
	user.SetUserID(muser.ID)
	c.Set("user", user)
	return true
}

// SetRole 设置用户角色
func (user *User) SetRole(role UserRole) {
	switch role {
	case RoleTest, RoleGuest, RoleMember:
		user.role = role
	default:
		user.role = RoleGuest
	}
}

// Role 返回用户角色
func (user *User) Role() UserRole {
	return user.role
}

func makeTestUser() User {
	var user User
	user.SetRole(RoleTest)
	user.id = 0
	user.userID = MakeTestUserID()
	return user
}

// SetUserID 设置 user.userID 和 user.ID
// 只支持 int64 和 string
func (user *User) SetUserID(userID interface{}) {
	switch userID := userID.(type) {
	case int64:
		user.id = int64(userID)
		user.userID = MakeUserID(userID)
	case string:
		user.userID = MakeUserID(userID)
		user.id = 0
	default:
		panic("unsupported userID.(type)")
	}
}

// UserID 得到 user.userID
func (user *User) UserID() UserID {
	return user.userID
}

// ID 返回 user.id
func (user *User) ID() int64 {
	return user.id
}

// MakeTestUserID 建立测试用户 ID
func MakeTestUserID() UserID {
	return MakeUserID("test-" + strconv.FormatInt(goutil.TimeNow().Unix(), 10))
}

// MakeUserID 根据传入值建立用户 ID
func MakeUserID(from interface{}) UserID {
	switch from := from.(type) {
	case int64:
		return UserID(strconv.FormatInt(from, 10))
	case string:
		return UserID(from)
	default:
		panic("unsupported from.(type)")
	}
}
