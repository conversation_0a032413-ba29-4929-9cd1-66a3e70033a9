package im

import (
	"encoding/json"
	"net/http"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewBroadcaster(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := NewBroadcaster(1234, DefaultBc)
	require.NotNil(b)
	defer func() { b.ChClose <- struct{}{} }()
	assert.Equal(int64(1234), b.roomID)
	assert.NotNil(b.Conns)
	assert.NotNil(b.ChFromUser)
	assert.NotNil(b.ChFromPubsub)
	assert.NotNil(b.ChRefresh)
	assert.NotNil(b.ChClose)
	assert.Equal("live-service:im:room:1234:members", b.keyMember)
	assert.Equal("live-service:im:room:1234:users", b.KeyUsers)
	assert.Equal("live-service:im:room:1234:notify", b.KeyNotify)
	assert.Equal("live-service:im:room:1234:join_queue", b.keyJoinQueue)
	assert.Equal("lock:live-service:im:room:1234:join_queue", b.lockJoinQueue)
}

func TestBroadcasterRefresh(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := NewBroadcaster(12345, DefaultBc)
	defer func() { b.ChClose <- struct{}{} }()
	require.NoError(service.IMRedis.Del(b.keyMember, b.KeyUsers).Err())
	b.Conns[uuid.New()] = nil
	conn := WSConn{UUID: uuid.New()}
	b.Conns[conn.UUID] = &conn
	// defer delete(b.Conns, conn.UUID) // 这个并不是正常加入房间的，代码时会导致 broadcaster 无法正常退出
	connClose := WSConn{
		UUID:    uuid.New(),
		status:  statusClose,
		chSend:  make(chan wsMessage, 1),
		chClose: make(chan struct{}, 1),
	}
	var w sync.WaitGroup
	w.Add(1)
	go func() {
		connClose.chSend <- wsMessage{Type: websocket.TextMessage, Data: []byte{}}
		connClose.chClose <- struct{}{}
		w.Done()
	}()
	w.Wait()
	b.Conns[connClose.UUID] = &connClose

	b.ChRefresh <- struct{}{}
	require.NoError(service.IMRedis.HSet(b.KeyUsers, 1234, -1).Err())
	time.Sleep(100 * time.Millisecond)
	val, err := service.IMRedis.ZCard(b.keyMember).Result()
	require.NoError(err)
	assert.Equal(int64(1), val)
	val, err = service.IMRedis.Exists(b.KeyUsers).Result()
	require.NoError(err)
	assert.Zero(val)
	assert.Len(b.Conns, 1)
	b.Conns = make(map[uuid.UUID]*WSConn)
}

func TestNewActivityBroadcaster(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := NewActivityBroadcaster(1234, DefaultBc)
	require.NotNil(b)
	defer func() { b.ChClose <- struct{}{} }()
	assert.Equal(int64(1234), b.roomID)
	assert.NotNil(b.Conns)
	assert.NotNil(b.ChFromUser)
	assert.NotNil(b.ChFromPubsub)
	assert.NotNil(b.ChRefresh)
	assert.NotNil(b.ChClose)
}

func TestBroadcasterSendJoins(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(3192516)
	cancel := mrpc.SetMock(liverpc.URILiveRoomUsers, func(input interface{}) (interface{}, error) {
		return []*liveuser.Simple{
			{UID: 3456835},
			{UID: 10},
			{UID: 12},
		}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(liverpc.URILiveRoomInfo, func(input interface{}) (interface{}, error) {
		return &liverpc.RoomInfoResp{
			Room: &liverpc.RoomSimple{RoomID: roomID},
		}, nil
	})
	defer cancel()

	b := DefaultBc.Broadcaster(roomID, true)
	require.NotNil(b)
	require.NoError(service.IMRedis.Del(liveim.KeyRoomJoined(b.roomID)).Err())
	defer func() {}()
	pubsub := tutil.NewPubSub(service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)))
	defer pubsub.Close()
	var ok bool
	pipe := service.IMRedis.Pipeline()
	for i := 0; i < 2; i++ {
		b.joinSet[uuid.New()] = struct{}{}
		pipe.Del(b.lockJoinQueue, b.keyJoinQueue)
		if i == 0 {
			pipe.RPush(b.keyJoinQueue, 3456835, 0, 10, 0, 12, 0)
		} else {
			pipe.RPush(b.keyJoinQueue, 3456835, 10, 12)
		}
		_, err := pipe.Exec()
		require.NoError(err)
		b.sendJoins(time.Second) // 第二遍应该没有用户
	}
	for i := 0; i < 2; i++ {
		v := pubsub.Receive()
		msg := decodeIMMessage(v)
		require.NotNil(msg)
		var r struct {
			Type   string        `json:"type"`
			Event  string        `json:"event"`
			RoomID int64         `json:"room_id"`
			Queue  []*guestCount `json:"queue"`
		}
		_ = json.Unmarshal(msg.Payload, &r)
		if r.Type == "member" {
			ok = true
			assert.Equal("join_queue", r.Event)
			assert.Equal(int64(3192516), r.RoomID)
			require.Len(r.Queue, 4)
			assert.Equal([]int64{3456835, 10, 12, 0}, []int64{r.Queue[0].UserID,
				r.Queue[1].UserID, r.Queue[2].UserID, r.Queue[3].UserID})
			assert.Equal(3, r.Queue[3].Count)
			break
		}
	}
	assert.True(ok)
}

var existsOpenRoomID int64 = 172842330

func TestBroadcasterHandleUserMethod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(liverpc.URILiveRoomInfo, func(interface{}) (interface{}, error) {
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: existsOpenRoomID},
		}}, nil
	})
	defer cancel()

	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err)
	userID := int64(12)
	conn := &WSConn{
		userInfo: &liverpc.RoomUserInfoResp{
			User: &liveuser.Simple{UID: userID},
		},
		User: User{id: 12},
		Bc:   DefaultBc,
		C:    handler.NewTestContext(http.MethodGet, "ws", true, nil),
	}
	conn.setConn(ws)
	conn.run()
	defer conn.Close()
	b := DefaultBc.Broadcaster(existsOpenRoomID, true)
	assert.Panics(func() { b.handleUserMethod(&userMethod{Method: -1, Conn: conn, Operator: operatorSystem}) })
	b.handleUserMethod(&userMethod{Method: methodJoin, Conn: conn, Operator: operatorSystem})
	defer b.handleUserMethod(&userMethod{Method: methodLeave, Conn: conn, Operator: operatorSystem})
	assert.NoError(ws.SetReadDeadline(goutil.TimeNow().Add(5 * time.Second)))
	_, _, err = ws.ReadMessage()
	require.NoError(err)
	_, _, err = ws.ReadMessage()
	require.NoError(err)
	// TODO: 超粉有影响
	// assert.JSONEq(`
	// 	{
	// 		"type": "room",
	// 		"event": "join",
	// 		"uuid": "",
	// 		"room_id": 172842330,
	// 		"code": 0,
	// 		"info": {
	// 			"room": {
	// 				"status": {
	// 					"open": 1
	// 				}
	// 			},
	// 			"status": {
	// 				"invisible": false
	// 			},
	// 			"user": {
	// 				"user_id": 0,
	// 				"username": "",
	// 				"iconurl": ""
	// 			}
	// 		}
	// 	}`, string(v))

	// 用户当前房间有勋章
	conn.userInfo.User.UID = 12
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	s := &livemedal.Simple{RoomID: existsOpenRoomID, CreatorID: 3457111, UserID: userID, Status: livemedal.StatusOwned,
		Mini: livemedal.Mini{Name: "test_im"}, Point: 60000000}
	err = collection.FindOneAndUpdate(ctx, bson.M{"room_id": existsOpenRoomID, "user_id": userID},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && err != mongo.ErrNoDocuments, err)
	b.handleUserMethod(&userMethod{Method: methodJoin, Conn: conn, Operator: operatorSystem})
	_, _, err = ws.ReadMessage()
	require.NoError(err)
	// TODO: 超粉有影响
	// assert.JSONEq(`
	// 	{
	// 		"type": "room",
	// 		"event": "join",
	// 		"uuid": "",
	// 		"room_id": 172842330,
	// 		"code": 0,
	// 		"info": {
	// 			"room": {
	// 				"status": {
	// 					"open": 1
	// 				}
	// 			},
	// 			"medal": {
	// 				"name": "test_im",
	// 				"level": 40,
	// 				"frame_url": "https://static-test.missevan.com/live/medalframes/normal/level40_0_9_0_54.webp",
	// 			},
	// 			"status": {
	// 				"invisible": false
	// 			},
	// 			"user": {
	// 				"user_id": 12,
	// 				"username": "",
	// 				"iconurl": ""
	// 			}
	// 		}
	// 	}`, string(v))

	t.Run("TestBroadcasterBroadCastAll", func(t *testing.T) {
		require.NoError(liveim.Publish(&liveim.IMMessage{
			Type:    liveim.IMMessageTypeAll,
			Payload: json.RawMessage(`{"test_receive_broadcast_all":1}`),
		}))
		_, v, err := ws.ReadMessage()
		require.NoError(err)
		assert.Equal(`{"test_receive_broadcast_all":1}`, string(v))
	})

	// 测试超管、风纪委员、运营进入房间不提示
	conn = &WSConn{
		userInfo: &liverpc.RoomUserInfoResp{
			User: &liveuser.Simple{UID: userID},
		},
		User: User{id: 3456835},
		Bc:   DefaultBc,
		C:    handler.NewTestContext(http.MethodGet, "ws", true, nil),
	}
	conn.setConn(ws)
	conn.run()
	b.handleUserMethod(&userMethod{Method: methodJoin, Conn: conn, Operator: operatorSystem})
	userIDStrList, err := service.IMRedis.LRange(b.keyJoinQueue, 0, -1).Result()
	require.NoError(err)
	assert.NotContains(strconv.FormatInt(conn.User.ID(), 10), userIDStrList)

	t.Run("TestJoinPreviewRoom", func(t *testing.T) {
		conn = &WSConn{
			isPreview: true,
			userInfo: &liverpc.RoomUserInfoResp{
				User: &liveuser.Simple{UID: userID},
			},
			User: User{id: 12},
			Bc:   DefaultBc,
			C:    handler.NewTestContext(http.MethodGet, "ws", true, nil),
		}
		b.Conns[conn.UUID] = conn
		conn.setConn(ws)
		conn.run()
		require.NoError(service.IMRedis.Del(b.keyMember, b.keyJoinQueue).Err())
		b.handleUserMethod(&userMethod{Method: methodJoin, Conn: conn, Operator: operatorSystem})
		length, err := service.IMRedis.ZCard(b.keyMember).Result()
		require.NoError(err)
		assert.Zero(length)
		length, err = service.IMRedis.LLen(b.keyJoinQueue).Result()
		require.NoError(err)
		assert.Zero(length)
	})

	t.Run("TestLeavePreviewRoom", func(t *testing.T) {
		conn = &WSConn{
			UUID:      uuid.New(),
			isPreview: true,
			userInfo: &liverpc.RoomUserInfoResp{
				User: &liveuser.Simple{UID: userID},
			},
			User: User{id: 12},
			Bc:   DefaultBc,
			C:    handler.NewTestContext(http.MethodGet, "ws", true, nil),
		}
		conn.setConn(ws)
		conn.run()
		b.Conns[conn.UUID] = conn
		pipe := service.IMRedis.Pipeline()
		pipe.Del(b.keyMember, b.keyJoinQueue)
		pipe.ZAdd(b.keyMember, &redis.Z{Member: conn.redisMemberValue(), Score: float64(goutil.TimeNow().Unix())})
		_, err = pipe.Exec()
		require.NoError(err)
		b.handleUserMethod(&userMethod{Method: methodLeave, Conn: conn, Operator: operatorSystem})
		length, err := service.IMRedis.ZCard(b.keyMember).Result()
		require.NoError(err)
		assert.Equal(int64(1), length)
	})
}

func TestBroadcasterLeaveRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err)
	userID := int64(987452)
	userIDStr := strconv.FormatInt(userID, 10)
	conn := &WSConn{
		userInfo: &liverpc.RoomUserInfoResp{
			User:           &liveuser.Simple{UID: userID},
			IsInvisible:    true,
			IsValuableUser: true,
		},
		User: User{id: userID, userID: UserID(userIDStr)},
		Bc:   DefaultBc,
		UUID: uuid.New(),
	}
	conn.setConn(ws)
	conn.run()
	defer conn.Close()
	cancel := mrpc.SetMock(liverpc.URILiveRoomInfo, func(input interface{}) (interface{}, error) {
		return &liverpc.RoomInfoResp{
			Room: &liverpc.RoomSimple{RoomID: existsOpenRoomID},
		}, nil
	})
	defer cancel()
	b := DefaultBc.Broadcaster(existsOpenRoomID, true)
	b.Conns[conn.UUID] = conn
	b.joinSet[conn.UUID] = struct{}{}
	inRoomCount++
	roomCountBefore := inRoomCount
	pipe := service.IMRedis.Pipeline()
	pipe.Del(b.KeyUsers, b.keyMember, b.KeyValuableUsers)
	pipe.ZAdd(b.keyMember, &redis.Z{Member: conn.redisMemberValue(), Score: float64(goutil.TimeNow().Unix())})
	pipe.HSet(b.KeyUsers, userID, 1)
	pipe.SAdd(b.KeyValuableUsers, userID)
	_, err = pipe.Exec()
	require.NoError(err)

	b.handleUserMethod(&userMethod{Method: methodLeave,
		Conn:     conn,
		Operator: operatorUser,
		uuid:     "test",
	})
	_, ok := b.joinSet[conn.UUID]
	assert.False(ok)
	assert.Equal(roomCountBefore-1, inRoomCount)
	assert.Nil(b.Conns[conn.UUID])
	memberCmd := pipe.ZScore(b.keyMember, conn.redisMemberValue())
	userCmd := pipe.HGet(b.KeyUsers, userIDStr)
	vuCmd := pipe.SIsMember(b.KeyValuableUsers, userIDStr)
	_, _ = pipe.Exec()
	assert.True(serviceredis.IsRedisNil(memberCmd.Err()))
	assert.True(serviceredis.IsRedisNil(userCmd.Err()))
	ok, err = vuCmd.Result()
	require.NoError(err)
	assert.False(ok)
}

func TestBroadcasterBroadCastUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(liverpc.URILiveRoomInfo, func(interface{}) (interface{}, error) {
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: existsOpenRoomID},
		}}, nil
	})
	defer cancel()

	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err)
	userID := int64(12)
	conn := &WSConn{
		userInfo: &liverpc.RoomUserInfoResp{
			User: &liveuser.Simple{UID: userID},
		},
		roomID: existsOpenRoomID,
		User:   User{id: userID},
		Bc:     DefaultBc,
		C:      handler.NewTestContext(http.MethodGet, "ws", true, nil),
	}
	b := conn.Bc.Broadcaster(existsOpenRoomID, true)
	conn.userInfo.User.UID = userID
	conn.setConn(ws)
	conn.run()
	defer conn.Close()
	assert.NoError(ws.SetReadDeadline(goutil.TimeNow().Add(5 * time.Second)))
	_, _, err = conn.Conn().ReadMessage()
	require.NoError(err)
	b.handleUserMethod(&userMethod{Method: methodJoin, Conn: conn, Operator: operatorSystem})
	_, _, err = conn.Conn().ReadMessage()
	require.NoError(err)

	require.NoError(liveim.Publish(&liveim.IMMessage{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  existsOpenRoomID,
		UserID:  10,
		Payload: json.RawMessage(`{"test_broadcast_user":0}`),
	}))
	require.NoError(liveim.Publish(&liveim.IMMessage{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  existsOpenRoomID,
		UserID:  userID,
		Payload: json.RawMessage(`{"test_broadcast_user":1}`),
	}))

	require.NoError(err)
	_, v, err := ws.ReadMessage()
	require.NoError(err)
	assert.Equal(`{"test_broadcast_user":1}`, string(v))
}

func TestBroadcasterBroadCastActivity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/echo"), nil)
	require.NoError(err)
	userID := int64(12)
	conn := &WSConn{
		userInfo: &liverpc.RoomUserInfoResp{
			User: &liveuser.Simple{UID: userID},
		},
		roomID: existsOpenRoomID,
		User:   User{id: userID},
		Bc:     DefaultBc,
	}
	b := conn.Bc.BuildActivityBroadcaster(existsOpenRoomID)
	conn.userInfo.User.UID = userID
	conn.setConn(ws)
	conn.run()
	defer conn.Close()
	b.Conns[conn.UUID] = conn
	DefaultBc.ActivityBroadcasters[existsOpenRoomID] = b
	assert.NoError(ws.SetReadDeadline(goutil.TimeNow().Add(5 * time.Second)))
	_, _, err = conn.Conn().ReadMessage()
	require.NoError(err)

	require.NoError(liveim.Publish(&liveim.IMMessage{
		Type:    liveim.IMMessageTypeActivity,
		RoomID:  existsOpenRoomID,
		UserID:  userID,
		Payload: json.RawMessage(`{"test_activity_broadcast_user":1}`),
	}))

	require.NoError(err)
	_, v, err := ws.ReadMessage()
	require.NoError(err)
	assert.Equal(`{"test_activity_broadcast_user":1}`, string(v))
}

func TestRefresh(t *testing.T) {
	assert := assert.New(t)
	assert.NotPanics(func() { DefaultBc.ChRefresh <- struct{}{} })
	time.Sleep(time.Second)
}

func TestImMessage_isMsgDisabled(t *testing.T) {
	assert := assert.New(t)

	msg := imMessage{Priority: BroadcastPriorityNormal}
	assert.False(msg.isMsgDisabled(&WSConn{}))
	assert.True(msg.isMsgDisabled(&WSConn{riskStatus: riskStatusDisableAllMsg}))
	assert.False(msg.isMsgDisabled(&WSConn{riskStatus: riskStatusDisablePurchasedMsg}))

	msg = imMessage{Priority: BroadcastPriorityPurchased}
	assert.False(msg.isMsgDisabled(&WSConn{}))
	assert.True(msg.isMsgDisabled(&WSConn{riskStatus: riskStatusDisableAllMsg}))
	assert.True(msg.isMsgDisabled(&WSConn{riskStatus: riskStatusDisablePurchasedMsg}))
}

func TestDecodeIMMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var raw liveim.IMMessage
	require.NoError(raw.SetPayload([]byte("{}")))

	config.Conf.AB["im_message_decode"] = true
	rawData, err := raw.Encode()
	require.NoError(err)
	msg := decodeIMMessage(string(rawData))
	require.NotNil(msg)
	assert.Equal(raw.CompressedPayload, msg.CompressedPayload)

	config.Conf.AB["im_message_decode"] = false
	rawData, err = raw.Encode()
	require.NoError(err)
	msg = decodeIMMessage(string(rawData))
	require.NotNil(msg)
	assert.Nil(msg.CompressedPayload)
}

func TestBroadcasterController_allRoomBroadcasters(t *testing.T) {
	assert := assert.New(t)

	res := BroadcasterController{
		Broadcasters: make(map[int64]*Broadcaster),
	}
	assert.Empty(res.allRoomBroadcasters())

	res.Broadcasters[1234] = &Broadcaster{}
	assert.Len(res.allRoomBroadcasters(), 1)
}
