package im

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// ----- utility for test -----

const existsRoomID int64 = 18113499

func TestMain(m *testing.M) {
	runTest(m)
}

func runTest(m *testing.M) int {
	handler.SetMode(handler.TestMode)
	service.InitTest(true)

	clearIMWaitDuration = time.Second // 测试环境下改成 1 秒
	DefaultBc = NewBroadcasterController()
	DefaultSc = NewSubscriptionController(DefaultBc)
	defer func() {
		ClearIM() // TestClearIM 已测试该函数
		if CountBroadcaster != 0 {
			// 如果有运行中的 broadcaster 仅记录下日志
			logger.Errorf("broadcaster release failed")
		}
	}()

	addTestData()
	startServer()

	return m.Run()
}

var serverAddr string

var hookFunc func(http.ResponseWriter, *http.Request)

func hookHandler(w http.ResponseWriter, r *http.Request) {
	hookFunc(w, r)
}

func echoHandler(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		logger.Debugf("Can't Upgrade http to websocket, error %v", err)
		return
	}
	defer conn.Close()
	for {
		messageType, p, err := conn.ReadMessage()
		if err != nil {
			logger.Debugf("read: %v", err)
			return
		}
		if err := conn.WriteMessage(messageType, p); err != nil {
			logger.Debugf("write: %v", err)
			return
		}
	}
}

func startServer() {
	http.HandleFunc("/echo", echoHandler)
	http.HandleFunc("/hook", hookHandler)
	server := httptest.NewServer(nil)
	serverAddr = server.Listener.Addr().String()
	logger.Debug("Test WebSocket server listening on ", serverAddr)
}

func wsURLStr(path string) string {
	return fmt.Sprintf("ws://%s%s", serverAddr, path)
}

func addTestData() {
	// 添加佩戴勋章
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	s := &livemedal.Simple{RoomID: 22489473, CreatorID: 10, UserID: 123, Status: livemedal.StatusShow, Point: 200}
	s.Name = "test"
	err := collection.FindOneAndUpdate(ctx, bson.M{"creator_id": 10, "user_id": 123},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil && err != mongo.ErrNoDocuments {
		logger.Fatal(err)
	}
}

// ----- test functions -----

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	ClearIM()
	h := Handler()
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "ws")
	assert.Len(h.Middlewares, 1)
	assert.NotNil(DefaultBc)
	assert.NotNil(DefaultSc)
}

func TestActionWS(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/ws?room_id=123", false, nil)
	_, err := ActionWS(c)
	assert.Error(err)

	ch := make(chan int)
	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		gc, _ := gin.CreateTestContext(w)
		gc.Request = r
		c = &handler.Context{C: gc}
		gc.Set("user", user.User{})
		_, err := ActionWS(c)
		assert.Equal(handler.ErrRawResponse, err)
		ch <- 1
	}
	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/hook?room_id=123"), nil)
	require.NoError(err)
	assert.NoError(ws.Close())
	<-ch
}

func TestActionWSActivity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "?room_id=22489473", true, nil)
	_, err := ActionWSActivity(c)
	assert.Equal(actionerrors.ErrConnRole, err)

	ch := make(chan int)
	hookFunc = func(w http.ResponseWriter, r *http.Request) {
		gc, _ := gin.CreateTestContext(w)
		gc.Request = r
		form := url.Values{}
		form.Add("room_id", "22489473")
		gc.Request.PostForm = form
		c = &handler.Context{C: gc}
		gc.Set("user", user.User{})
		_, err := ActionWSActivity(c)
		assert.Equal(handler.ErrRawResponse, err)
		ch <- 1
	}
	ws, _, err := websocket.DefaultDialer.Dial(wsURLStr("/hook"), nil)
	require.NoError(err)
	assert.NoError(ws.Close())
	<-ch
}
