package im

import (
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"github.com/MiaoSiLa/live-service/service"
)

var (
	connGauges      *prometheus.GaugeVec
	broadcastGauges *prometheus.GaugeVec
	messageGauges   *prometheus.GaugeVec
	meticsOnce      sync.Once
)

// MetricsHandler /metrics [get]
// 响应格式：
/*
# HELP im_websocket_connections websocket connection count
# TYPE im_websocket_connections gauge
im_websocket_connections{status="room"} 5
im_websocket_connections{status="ready"} 10
# HELP http_requests_total The total number of HTTP requests.
# TYPE http_requests_total counter
http_requests_total{status="200"} 1
*/
func MetricsHandler() gin.HandlerFunc {
	meticsOnce.Do(func() {
		connGauges = prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: "im",
				Subsystem: "websocket",
				Name:      "connections",
				Help:      "websocket connections count",
			},
			[]string{"status"},
		)
		service.PromRegistry.MustRegister(connGauges)
		broadcastGauges = prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: "im",
				Subsystem: "",
				Name:      "broadcasters",
				Help:      "im broadcasters count",
			},
			[]string{"status"},
		)
		service.PromRegistry.MustRegister(broadcastGauges)
		messageGauges = prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: "im",
				Subsystem: "websocket",
				Name:      "messages",
				Help:      "websocket messages count",
			},
			[]string{"status"},
		)
		service.PromRegistry.MustRegister(messageGauges)
	})
	next := promhttp.HandlerFor(service.PromRegistry, promhttp.HandlerOpts{})

	return func(c *gin.Context) {
		connGauges.WithLabelValues("ready").Set(float64(connCount - inRoomCount))
		connGauges.WithLabelValues("room").Set(float64(inRoomCount))
		broadcastGauges.WithLabelValues("all").Set(float64(CountBroadcaster))
		broadcastGauges.WithLabelValues("registered").Set(float64(len(DefaultBc.Broadcasters)))
		messageGauges.WithLabelValues("queued").Set(float64(queuedMsgCount))
		messageGauges.WithLabelValues("sent").Set(float64(sentMsgCount))
		next.ServeHTTP(c.Writer, c.Request)
	}
}
