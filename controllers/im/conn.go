package im

import (
	"bytes"
	"net"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/cache/usersession"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/redis/imuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/security"
)

func init() {
	extra.RegisterFuzzyDecoders()
}

var (
	heartbeat = []byte("❤️")
)

// durations
var (
	readWait  = 2 * time.Minute
	writeWait = 5 * time.Second
)

const (
	statusRun = iota + 1
	statusClose
)

const (
	riskStatusNormal = iota
	riskStatusDisableAllMsg
	riskStatusDisablePurchasedMsg
)

var (
	// connCount 连接数
	connCount int64
	// inRoomCount 在房间中的连接数
	inRoomCount int64
	// queuedMsgCount 等待发送的消息总数
	queuedMsgCount int64
	// sentMsgCount 已发送的消息总数
	sentMsgCount int64
)

var json2 = jsoniter.ConfigCompatibleWithStandardLibrary

// WSConn 是连接到服务器的有效 websocket 连接
// 连接根据客户端发送的 ping 确认连接是否有效
// 收到 ping 后将超时时间设置成当时两分钟后
type WSConn struct {
	UUID uuid.UUID // 标记这个链接用
	C    *handler.Context
	Bc   *BroadcasterController

	Type      int
	User      User
	conn      *websocket.Conn
	roomID    int64
	isPreview bool // 是否为预览页
	logOID    primitive.ObjectID
	chSend    chan wsMessage
	chClose   chan struct{}

	userInfo *liverpc.RoomUserInfoResp

	status int32

	supportCompression bool // 用户是否支持压缩后的消息

	eventJoinUUID string // 用户 join 时发送的 uuid
	GuestID       string // 游客 ID
	riskStatus    int    // 风险状态
}

type wsMessage struct {
	Type int
	Data []byte
}

type messenger interface {
	wsMessage(supportCompression bool) wsMessage
}

// NewWSConn new WSConn，返回的 error 不需要再封装
// TODO: 整合逻辑
func NewWSConn(c *handler.Context, bc *BroadcasterController, connType int) (*WSConn, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID == 0 {
		return nil, actionerrors.ErrParams
	}
	preview, err := c.GetDefaultParamInt("preview", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	wsConn := WSConn{
		UUID:      uuid.New(),
		C:         c,
		Bc:        bc,
		Type:      connType,
		roomID:    roomID,
		isPreview: goutil.IntToBool(preview),
	}
	// WORKAROUND: Android < 5.6.7 iOS < 4.7.9 不支持压缩
	e := c.Equip()
	wsConn.supportCompression = !e.IsOldApp(goutil.AppVersions{
		Android: "5.6.7",
		IOS:     "4.7.9",
	})
	if noCompression, _ := c.GetParamInt("nocompression"); goutil.IntToBool(noCompression) {
		wsConn.supportCompression = false
	}
	// c.User() 会导致 panic, 因为设置 user 的中间件不同
	if u, ok := c.C.Get("user"); ok {
		switch u := u.(type) {
		case user.User:
			wsConn.User.SetRole(RoleMember)
			wsConn.User.SetUserID(u.ID)
			wsConn.User.Username = u.Username
		case User:
			wsConn.User = u
		default:
			return nil, actionerrors.ErrConnRole
		}
	} else if config.Conf.IM.AllowTestUser ||
		// WORKAROUND: 安卓有 bug, 未登录用户暂时允许连接
		connType == TypeBroadcasterActivity {
		// TODO: 增加测试方案避免添加测试用户
		wsConn.User = makeTestUser()
	} else {
		return nil, actionerrors.ErrConnRole
	}
	err = wsConn.findUserInfo()
	if err != nil {
		return nil, err
	}

	conn, err := upgrader.Upgrade(c.C.Writer, c.C.Request, nil)
	if err != nil {
		logger.Errorf("Can't Upgrade http to websocket, error %v", err)
		return nil, actionerrors.ErrParams
	}

	wsConn.setConn(conn)
	wsConn.run()
	wsConn.checkConnectRisk()
	wsConn.createLog()
	return &wsConn, nil
}

type receiveMessage struct {
	UUID      string `json:"uuid"`
	Action    string `json:"action"`
	RoomID    int64  `json:"room_id"`
	Reconnect int    `json:"reconnect,omitempty"` // 是否是重连，重连不广播进场消息
}

func timeScore() float64 {
	return float64(goutil.TimeNow().Unix())
}

// RoomID 返回连接加入的聊天室房间
func (wsConn *WSConn) RoomID() int64 {
	return wsConn.roomID
}

// Conn 返回 websocket 连接，不要用连接发消息
func (wsConn *WSConn) Conn() *websocket.Conn {
	return wsConn.conn
}

// Send 发送消息
// TODO: 等待批量发送操作
func (wsConn *WSConn) Send(msg wsMessage) {
	if atomic.LoadInt32(&wsConn.status) <= statusRun {
		atomic.AddInt64(&queuedMsgCount, 1)
		wsConn.chSend <- msg
	}
}

// SendNow 立刻发送消息
// TODO: 等待批量发送操作
func (wsConn *WSConn) SendNow(msg wsMessage) {
	if atomic.LoadInt32(&wsConn.status) <= statusRun {
		atomic.AddInt64(&queuedMsgCount, 1)
		wsConn.chSend <- msg
	}
}

// setConn 设置 websocket 连接
func (wsConn *WSConn) setConn(conn *websocket.Conn) {
	h := func(message string) error {
		wsConn.updateLogRenewTime()
		_ = conn.SetReadDeadline(goutil.TimeNow().Add(readWait))
		err := conn.WriteControl(websocket.PongMessage, []byte(message), goutil.TimeNow().Add(writeWait))
		if err == websocket.ErrCloseSent {
			return nil
		} else if e, ok := err.(net.Error); ok && e.Temporary() {
			return nil
		}
		return err
	}
	conn.SetPingHandler(h)
	_ = conn.SetReadDeadline(goutil.TimeNow().Add(readWait))

	wsConn.chSend = make(chan wsMessage, 128)
	wsConn.chClose = make(chan struct{}, 2)
	wsConn.conn = conn
}

// Close WSConn
func (wsConn *WSConn) Close() {
	if atomic.LoadInt32(&wsConn.status) >= statusClose {
		// 已经关闭的只清理 channel
		wsConn.CleanupChannel()
		return
	}
	wsConn.chClose <- struct{}{}
}

// CleanupChannel 仅在 run 协程不起作用的时候处理
func (wsConn *WSConn) CleanupChannel() {
	if atomic.LoadInt32(&wsConn.status) < statusClose {
		return
	}
	for len(wsConn.chSend) != 0 {
		<-wsConn.chSend
		atomic.AddInt64(&queuedMsgCount, -1)
	}
	for len(wsConn.chClose) != 0 {
		<-wsConn.chClose
	}
}

func (wsConn *WSConn) handleClose() {
	switch wsConn.Type {
	case TypeBroadcasterRoom:
		if b := wsConn.Bc.Broadcaster(wsConn.RoomID(), false); b != nil {
			b.ChFromUser <- &userMethod{Method: methodLeave, Conn: wsConn, Operator: operatorSystem}
		}
	case TypeBroadcasterActivity:
		if b := wsConn.Bc.ActivityBroadcaster(wsConn.RoomID()); b != nil {
			b.ChFromUser <- &userMethod{Method: methodDisconnectActivity, Conn: wsConn, Operator: operatorSystem}
		}
	}

	_ = wsConn.conn.Close()
}

func (wsConn *WSConn) findUserInfo() error {
	id := wsConn.User.ID()
	if id == 0 {
		session, err := usersession.SessionFromCookie(wsConn.C.Request())
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if session != nil && session.Guest != nil {
			wsConn.GuestID = session.Guest.UserID
		}
		return nil
	}
	resp, err := liverpc.RoomUserInfo(id, wsConn.roomID)
	if err != nil {
		if rpcError, ok := err.(*mrpc.ClientError); ok && (rpcError.Code == actionerrors.ErrCannotFindUser.Code) {
			logger.WithFields(wsConn.makeLoggerFields()).Warn("Can't find user.")
			return nil
		}
		return err
	}
	wsConn.userInfo = resp
	return nil
}

// Receive 接收消息并处理
func (wsConn *WSConn) Receive() {
	atomic.AddInt64(&connCount, 1)
	defer func() {
		atomic.AddInt64(&connCount, -1)
	}()
	defer func() {
		if bc := wsConn.Bc.Broadcaster(wsConn.RoomID(), false); bc != nil {
			bc.ChFromUser <- &userMethod{Method: methodLeave, Conn: wsConn, Operator: operatorSystem}
		}
	}()

	// ----- 接收消息 -----

	// invalidCount 在出现一次正确之后重置
	var invalidCount uint
	// 退出机制：
	// 1. 收消息出现错误
	// 2. 连续出现多次无效的 method
	for {
		messageType, reply, err := wsConn.Conn().ReadMessage()
		if err != nil {
			// 收不到消息说明连接断开了
			logger.WithFields(wsConn.makeLoggerFields()).Debugf("websocket closed reason: %v", err)
			return
		}

		if wsConn.handleMessage(messageType, reply) {
			invalidCount = 0
		} else {
			invalidCount++
			if invalidCount > invalidLimit {
				logger.WithFields(wsConn.makeLoggerFields()).Info("too many invalid message")
				return
			}
		}
	}
}

func (wsConn *WSConn) run() {
	goutil.Go(func() {
		closeLimit := 20
		errLimit := 5
		unsentCount := 0
		atomic.AddInt32(&wsConn.status, 1)
		for {
			select {
			case msg := <-wsConn.chSend:
				// REVIEW: 考虑优化未发送消息的计数器的处理
				if unsentCount >= errLimit {
					// 一般触发此情况是因为连接已断开，但是仍有很多消息未发送
					unsentCount++
					if unsentCount == closeLimit {
						// 出错太多，尝试强制关闭连接（可能是带宽达到上限导致的错误，不会主动断开）
						wsConn.Close()
					}
					atomic.AddInt64(&queuedMsgCount, -1)
					continue
				}
				err := wsConn.conn.WriteMessage(msg.Type, msg.Data)
				if err != nil {
					// 这里出错一般是因为连接已经断开了，所有后续发消息肯定也会失败
					unsentCount++
					if unsentCount == errLimit {
						// 发送失败次数过多不 return，以防阻塞
						logger.Warnf("Send message failed: %v", err)
					}
				}
				atomic.AddInt64(&queuedMsgCount, -1)
				atomic.AddInt64(&sentMsgCount, 1)
			case <-wsConn.chClose:
				atomic.AddInt32(&wsConn.status, 1)
				wsConn.handleClose()
				// 清空 wsConn.send
				for len(wsConn.chSend) > 0 {
					<-wsConn.chSend
					unsentCount++
					atomic.AddInt64(&queuedMsgCount, -1)
				}
				if unsentCount >= errLimit {
					logger.WithField("unsent_count", unsentCount).
						Warnf("Too many unsent messages")
				}
				wsConn.CleanupChannel()
				return
			}
		}
	})

	// 通知连接成功
	mcs := newMsgConnectSuccess(wsConn.User.ID())
	wsConn.SendNow(mcs.wsMessage(wsConn.supportCompression))
}

func (wsConn *WSConn) createLog() {
	if wsConn.isPreview {
		// 预览页不记录连接日志
		return
	}
	status := imuserlogs.StatusNormal
	switch wsConn.riskStatus {
	case riskStatusDisableAllMsg:
		status = imuserlogs.StatusDisableAllMsg
	case riskStatusDisablePurchasedMsg:
		status = imuserlogs.StatusDisablePurchasedMsg
	}
	l := imuserlogs.Log{
		UserID:         wsConn.User.ID(),
		GuestID:        wsConn.GuestID,
		ConnectionUUID: wsConn.UUID.String(),
		Status:         status,
		RoomID:         wsConn.RoomID(),
		IP:             wsConn.C.ClientIP(),
		UserAgent:      wsConn.C.UserAgent(),
		EquipID:        wsConn.C.EquipID(),
		BUVID:          wsConn.C.BUVID(),
		RenewTime:      goutil.TimeNow().Unix(),
	}
	if wsConn.eventJoinUUID != "" {
		// 首次创建日志失败，心跳重试创建日志时也需要保存 join_uuid
		l.JoinUUID = wsConn.eventJoinUUID
	}
	err := l.Create()
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": wsConn.RoomID(),
			"user_id": wsConn.User.ID(),
		}).Error(err)
		return
	}
	wsConn.logOID = l.OID
}

func (wsConn *WSConn) checkConnectRisk() {
	if wsConn.isPreview {
		// 预览页不进行风险检查, 但需要屏蔽付费类消息（红包、福袋）
		// FIXME: 预览页需要礼物消息，不完全适用 riskStatusDisablePurchasedMsg 场景，后续修改
		wsConn.riskStatus = riskStatusDisablePurchasedMsg
		return
	}
	if wsConn.C.UserAgent() == "" {
		wsConn.riskStatus = riskStatusDisableAllMsg
		return
	}
	equip, err := goutil.ParseEquipment(wsConn.C.C.Request)
	if err != nil {
		logger.Errorf("parse equipment error: %v", err)
		// PASS
	} else {
		if equip.FromApp && equip.EquipID == "" {
			wsConn.riskStatus = riskStatusDisableAllMsg
			return
		}
	}
	isHighRisk, err := imuserlogs.IsHighRisk(wsConn.C.ClientIP(), "")
	if err != nil {
		logger.WithField("ip", wsConn.C.ClientIP()).Error(err)
		return
	}
	if isHighRisk {
		wsConn.riskStatus = riskStatusDisableAllMsg
	}
}

func (wsConn *WSConn) checkJoinRisk() {
	isHighRisk, err := imuserlogs.IsHighRisk("", wsConn.eventJoinUUID)
	if err != nil {
		logger.WithField("join_uuid", wsConn.eventJoinUUID).Error(err)
		return
	}
	if isHighRisk {
		wsConn.riskStatus = riskStatusDisableAllMsg
		return
	}
	equip, err := goutil.ParseEquipment(wsConn.C.C.Request)
	if err != nil {
		logger.Errorf("parse equipment error: %v", err)
		return
	}
	if equip.IsAppOlderThan("6.0.7", "6.0.7") {
		// 客户端 < 6.0.7 的版本不进行 uuid 校验
		return
	}
	ok := security.IsValidSecurityUUID(wsConn.eventJoinUUID, config.Conf.IM.JoinSignHmacKey, config.Conf.IM.JoinUUIDValidStartTime)
	if !ok {
		wsConn.riskStatus = riskStatusDisablePurchasedMsg
	}
}

func (wsConn *WSConn) updateLogRenewTime() {
	if wsConn.isPreview {
		// 预览页不记录连接日志
		return
	}
	if wsConn.logOID.IsZero() {
		wsConn.createLog() // 连接时没有创建成功, 获取不到 logOID, 重新创建日志
		return
	}
	now := goutil.TimeNow().Unix()
	err := imuserlogs.UpdateOne(
		bson.M{"_id": wsConn.logOID},
		bson.M{
			"renew_time":    now,
			"modified_time": now,
		})
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": wsConn.RoomID(),
			"user_id": wsConn.User.ID(),
		}).Error(err)
	}
}

func (wsConn *WSConn) updateLogJoinUUID(joinUUID string) {
	if wsConn.isPreview {
		// 预览页不记录连接日志
		return
	}
	if wsConn.logOID.IsZero() || joinUUID == "" {
		return
	}
	wsConn.eventJoinUUID = joinUUID
	// 设置 join_uuid 之后再检查是否是高风险用户
	wsConn.checkJoinRisk()
	update := bson.M{
		"join_uuid":     wsConn.eventJoinUUID,
		"modified_time": goutil.TimeNow().Unix(),
	}
	switch wsConn.riskStatus {
	case riskStatusDisableAllMsg:
		update["status"] = imuserlogs.StatusDisableAllMsg
	case riskStatusDisablePurchasedMsg:
		update["status"] = imuserlogs.StatusDisablePurchasedMsg
	}
	err := imuserlogs.UpdateOne(
		bson.M{"_id": wsConn.logOID},
		update,
	)
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": wsConn.RoomID(),
			"user_id": wsConn.User.ID(),
		}).Error(err)
	}
}

type msgConnectSuccess struct {
	Type  string `json:"type"`
	Event string `json:"event"`
	User  struct {
		UserID int64 `json:"user_id"`
	} `json:"user"`
}

func newMsgConnectSuccess(userID int64) *msgConnectSuccess {
	mcs := &msgConnectSuccess{
		Type:  typeUser,
		Event: eventConnect,
	}
	mcs.User.UserID = userID
	return mcs
}

func (mcs *msgConnectSuccess) wsMessage(supportCompression bool) wsMessage {
	raw, err := json2.Marshal(mcs)
	if err != nil {
		panic(err)
	}
	if !supportCompression {
		return wsMessage{Type: websocket.TextMessage, Data: raw}
	}
	compressed, err := liveim.BrotliEncode(raw)
	if err != nil {
		logger.Error(err)
		// PASS
		return wsMessage{Type: websocket.TextMessage, Data: raw}
	}
	return wsMessage{Type: websocket.BinaryMessage, Data: compressed}
}

func (wsConn *WSConn) handleMessage(messagetype int, reply []byte) bool {
	if bytes.Equal(heartbeat, reply) {
		// 网页的 ping
		wsConn.updateLogRenewTime() // 更新心跳时间
		_ = wsConn.conn.SetReadDeadline(goutil.TimeNow().Add(readWait))
		wsConn.SendNow(wsMessage{Type: websocket.TextMessage, Data: heartbeat})
		return true
	}
	var receive receiveMessage
	// WORKAROUND: 防止某些字段混用字符串和数字导致的解析失败
	err := json2.Unmarshal(reply, &receive)
	if err != nil {
		f := wsConn.makeLoggerFields()
		f["receive"] = string(reply)
		logger.WithFields(f).Debug(err)
		return false
	}
	// 检查是否是加入、离开请求的房间
	if wsConn.RoomID() != receive.RoomID {
		msg := msgJoinLeave{
			UUID:   receive.UUID,
			Type:   liveim.TypeRoom,
			Event:  liveim.EventJoin,
			RoomID: receive.RoomID,
			Code:   actionerrors.ErrCannotFindRoom.Code,
			Info:   actionerrors.ErrCannotFindRoom.Message,
		}
		wsConn.SendNow(msg.wsMessage(wsConn.supportCompression))
		return false
	}
	switch receive.Action {
	case liveim.EventJoin:
		bc := wsConn.Bc.Broadcaster(receive.RoomID, true)
		if bc == nil {
			msg := msgJoinLeave{
				UUID:   receive.UUID,
				Type:   liveim.TypeRoom,
				Event:  liveim.EventJoin,
				RoomID: wsConn.RoomID(),
				Code:   actionerrors.ErrCannotFindRoom.Code,
				Info:   actionerrors.ErrCannotFindRoom.Message,
			}
			wsConn.SendNow(msg.wsMessage(wsConn.supportCompression))
			return true
		}
		bc.ChFromUser <- &userMethod{
			Conn:      wsConn,
			Method:    methodJoin,
			Operator:  operatorUser,
			uuid:      receive.UUID,
			reconnect: goutil.IntToBool(receive.Reconnect),
		}
		wsConn.updateLogJoinUUID(receive.UUID)
		return true
	case liveim.EventLeave:
		// 离开房间不用在 mongodb 查询
		bc := wsConn.Bc.Broadcaster(receive.RoomID, false)
		if bc == nil {
			msg := msgJoinLeave{
				UUID:   receive.UUID,
				Type:   liveim.TypeRoom,
				Event:  liveim.EventLeave,
				RoomID: wsConn.RoomID(),
				Info:   "leave room success",
			}
			wsConn.SendNow(msg.wsMessage(wsConn.supportCompression))
			return true
		}
		bc.ChFromUser <- &userMethod{Conn: wsConn, Method: methodLeave, Operator: operatorUser, uuid: receive.UUID}
		return true
	default:
		message := msgJoinLeave{
			UUID:  receive.UUID,
			Type:  liveim.TypeRoom,
			Event: receive.Action,
			Code:  actionerrors.ErrParams.Code,
			Info:  "unsupported method",
		}
		wsConn.SendNow(message.wsMessage(wsConn.supportCompression))
		return false
	}
}

// ActivityReceive 接收消息并处理
func (wsConn *WSConn) ActivityReceive() {
	atomic.AddInt64(&connCount, 1)
	defer func() {
		atomic.AddInt64(&connCount, -1)
	}()
	defer func() {
		if bc := wsConn.Bc.ActivityBroadcaster(wsConn.RoomID()); bc != nil {
			bc.ChFromUser <- &userMethod{Method: methodDisconnectActivity, Conn: wsConn, Operator: operatorSystem}
		}
	}()

	// ----- 接收消息 -----

	// invalidCount 在出现一次正确之后重置
	var invalidCount uint
	// 退出机制：
	// 1. 收消息出现错误
	// 2. 连续出现多次无效的 method
	for {
		_, reply, err := wsConn.Conn().ReadMessage()
		if err != nil {
			// 收不到消息说明连接断开了
			logger.WithFields(wsConn.makeLoggerFields()).Debugf("websocket closed reason: %v", err)
			return
		}

		if wsConn.handleActivityMessage(reply) {
			invalidCount = 0
		} else {
			invalidCount++
			if invalidCount > invalidLimit {
				logger.WithFields(wsConn.makeLoggerFields()).Info("too many invalid message")
				return
			}
		}
	}
}

func (wsConn *WSConn) handleActivityMessage(reply []byte) bool {
	if bytes.Equal(heartbeat, reply) {
		// 网页的 ping
		_ = wsConn.conn.SetReadDeadline(goutil.TimeNow().Add(readWait))
		wsConn.SendNow(wsMessage{Type: websocket.TextMessage, Data: heartbeat})
		return true
	}
	var receive receiveMessage
	// WORKAROUND: 防止某些字段混用字符串和数字导致的解析失败
	err := json2.Unmarshal(reply, &receive)
	if err != nil {
		f := wsConn.makeLoggerFields()
		f["receive"] = string(reply)
		logger.WithFields(f).Debug(err)
		return false
	}
	switch receive.Action {
	case liveim.EventConnectActivity:
		// wsConn.RoomID() 值为 query 中读取到的 room_id
		b := wsConn.Bc.BuildActivityBroadcaster(wsConn.RoomID())
		if b == nil {
			msg := msgJoinLeave{
				UUID:   receive.UUID,
				Type:   liveim.TypeActivity,
				Event:  liveim.EventConnectActivity,
				RoomID: wsConn.RoomID(),
				Code:   actionerrors.ErrCannotFindRoom.Code,
				Info:   actionerrors.ErrCannotFindRoom.Message,
			}
			wsConn.SendNow(msg.wsMessage(wsConn.supportCompression))
			return true
		}
		b.ChFromUser <- &userMethod{Conn: wsConn, Method: methodConnectActivity, Operator: operatorUser}
		return true
	default:
		message := msgJoinLeave{
			UUID:  receive.UUID,
			Type:  liveim.TypeActivity,
			Event: receive.Action,
			Code:  actionerrors.ErrParams.Code,
			Info:  "unsupported method",
		}
		wsConn.SendNow(message.wsMessage(wsConn.supportCompression))
		return false
	}
}

func (wsConn *WSConn) redisMemberValue() string {
	var isValuableUser bool
	if wsConn.userInfo != nil {
		isValuableUser = wsConn.userInfo.IsValuableUser
	}
	return imuser.NewIMConnMember(string(wsConn.User.UserID()),
		wsConn.UUID.String(), isValuableUser)
}

func (wsConn *WSConn) makeLoggerFields() logger.Fields {
	res := logger.Fields{"user_id": wsConn.User.UserID()}
	if wsConn.C != nil {
		res["ip"] = wsConn.C.ClientIP()
		if equipID := wsConn.C.EquipID(); equipID != "" {
			res["equip_id"] = equipID
		}
	}
	if wsConn.RoomID() != 0 {
		res["room_id"] = wsConn.RoomID()
	}
	return res
}

// msgJoinLeave 加入离开房间的消息
type msgJoinLeave struct {
	Type   string      `json:"type"`
	Event  string      `json:"event"`
	UUID   string      `json:"uuid"`
	RoomID int64       `json:"room_id,omitempty"`
	Code   int         `json:"code"`
	Info   interface{} `json:"info"`
}

func (msg *msgJoinLeave) wsMessage(supportCompression bool) wsMessage {
	raw, err := json2.Marshal(msg)
	if err != nil {
		// 正常情况不应该出现这种情况
		panic(err)
	}
	if !supportCompression {
		return wsMessage{Type: websocket.TextMessage, Data: raw}
	}
	compressed, err := liveim.BrotliEncode(raw)
	if err != nil {
		logger.Error(err)
		// PASS
		return wsMessage{Type: websocket.TextMessage, Data: raw}
	}
	return wsMessage{Type: websocket.BinaryMessage, Data: compressed}
}
