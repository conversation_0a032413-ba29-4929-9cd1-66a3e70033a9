package im

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync/atomic"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestMetricHandler(t *testing.T) {
	assert := assert.New(t)
	oldConn := atomic.SwapInt64(&connCount, 15)
	defer atomic.SwapInt64(&connCount, oldConn)
	oldInRoom := atomic.SwapInt64(&inRoomCount, 5)
	defer atomic.SwapInt64(&inRoomCount, oldInRoom)
	oldQueued := atomic.SwapInt64(&queuedMsgCount, 10)
	defer atomic.SwapInt64(&queuedMsgCount, oldQueued)
	oldSend := atomic.SwapInt64(&sentMsgCount, 20)
	defer atomic.SwapInt64(&sentMsgCount, oldSend)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/metrics", nil)
	MetricsHandler()(c)
	got := strings.Split(w.Body.String(), "\n")
	expected := []string{`# HELP im_broadcasters im broadcasters count`,
		`# TYPE im_broadcasters gauge`,
		fmt.Sprintf(`im_broadcasters{status="all"} %d`, len(DefaultBc.Broadcasters)),
		fmt.Sprintf(`im_broadcasters{status="registered"} %d`, CountBroadcaster),
		`# HELP im_websocket_connections websocket connections count`,
		`# TYPE im_websocket_connections gauge`,
		`im_websocket_connections{status="ready"} 10`,
		`im_websocket_connections{status="room"} 5`,
		`# HELP im_websocket_messages websocket messages count`,
		`# TYPE im_websocket_messages gauge`,
		`im_websocket_messages{status="queued"} 10`,
		`im_websocket_messages{status="sent"} 20`,
		"",
	}
	assert.Equal(expected, got)
}
