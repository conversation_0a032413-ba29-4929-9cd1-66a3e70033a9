package im

import (
	"sync"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 默认订阅控制器
var (
	DefaultSc *SubscriptionController
	DefaultBc *BroadcasterController
)

var clearIMWaitDuration = 5 * time.Second

// ClearIM 程序结束时清理 im
func ClearIM() {
	if DefaultSc == nil {
		return
	}
	DefaultSc.ChClose <- struct{}{}
	// 需要预留足够的清理时间
	<-time.After(clearIMWaitDuration)
	DefaultSc = nil
	DefaultBc = nil
}

func initController() {
	DefaultBc = NewBroadcasterController()
	DefaultSc = NewSubscriptionController(DefaultBc)
}

// Subscription 订阅
type Subscription struct {
	Key     string
	Sub     *redis.PubSub
	ChClose chan struct{}
	BC      *BroadcasterController
}

// NewSubScription new subscription
func NewSubScription(index int, bC *BroadcasterController) *Subscription {
	key := liveim.KeyIMPubSubByIndex(index)
	s := &Subscription{
		Key:     key,
		Sub:     service.IMRedis.Subscribe(key),
		ChClose: make(chan struct{}, 1),
		BC:      bC,
	}
	s.run()
	return s
}

func (s *Subscription) run() {
	goutil.Go(func() {
		for {
			select {
			case msg, ok := <-s.Sub.Channel():
				if !ok {
					_ = s.Sub.Close()
					s.Sub = service.IMRedis.Subscribe(s.Key)
					logger.Warnf("restart subscription: %s", s.Key)
					continue
				}
				goutil.Go(func() {
					if s.BC != nil {
						s.BC.ChSubMessage <- msg.Payload
					}
				})
			case <-s.ChClose:
				_ = s.Sub.Close()
				s.BC = nil
				close(s.ChClose)
				return
			}
		}
	})
}

// SubscriptionController 订阅控制器
type SubscriptionController struct {
	Size int
	Lock sync.Mutex

	Subs []*Subscription

	Bc      *BroadcasterController
	ChClose chan struct{}
}

// NewSubscriptionController new SubscriptionController
func NewSubscriptionController(bc *BroadcasterController) *SubscriptionController {
	sc := &SubscriptionController{
		Size:    config.Conf.IM.PubsubSize,
		Bc:      bc,
		ChClose: make(chan struct{}),
	}
	if config.Conf.IM.PubsubSize <= 0 {
		logger.Fatalf("cannot init im pubsub, configured pubsub_size: %d", config.Conf.IM.PubsubSize)
	}
	sc.Subs = make([]*Subscription, config.Conf.IM.PubsubSize)
	for i := 0; i < config.Conf.IM.PubsubSize; i++ {
		sc.Subs[i] = NewSubScription(i, bc)
	}
	sc.run()
	return sc
}

func (sc *SubscriptionController) run() {
	goutil.Go(func() {
		t := time.NewTicker(time.Hour)
		defer t.Stop()
		for {
			select {
			case <-t.C:
				sc.refresh()
			case <-sc.ChClose:
				sc.close()
				return
			}
		}
	})
}

func (sc *SubscriptionController) refresh() {
	sc.Lock.Lock()
	defer sc.Lock.Unlock()
	if sc.Bc != nil {
		sc.Bc.ChRefresh <- struct{}{}
	}
}

func (sc *SubscriptionController) close() {
	for i := range sc.Subs {
		sc.Subs[i].ChClose <- struct{}{}
		sc.Subs[i] = nil
	}
	if sc.Bc != nil {
		sc.Bc.ChClose <- struct{}{}
		sc.Bc = nil
	}
}
