package im

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUserSetFunc(t *testing.T) {
	assert := assert.New(t)
	var u User
	// ----- SetRole -----
	u.SetRole(RoleGuest)
	assert.Equal(RoleGuest, u.Role())
	u.SetRole(UserRole(-1))
	assert.Equal(RoleGuest, u.Role())
	// ----- SetUserID -----
	u.SetUserID(int64(123))
	assert.Equal(int64(123), u.ID())
	assert.Equal(UserID("123"), u.UserID())
	u.SetUserID("123")
	assert.Equal(UserID("123"), u.UserID())
	assert.Zero(u.ID())
	assert.PanicsWithValue("unsupported userID.(type)", func() { u.SetUserID(true) })
}

func TestUnmarshalFromSession(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	sessionKey := sessionMemcachePrefix + "test"
	require.NoError(service.LRURedis.Del(sessionKey).Err())

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	assert.False(unmarshalFromSession(c))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName, Value: "test"})
	assert.False(unmarshalFromSession(c))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName, Value: "test"})
	assert.False(unmarshalFromSession(c))

	v := map[string]interface{}{
		"guest": map[string]interface{}{
			"user_id":  "123",
			"username": "guest",
		},
		"user": map[string]interface{}{
			"id":        456,
			"username":  "member",
			"token":     "123456789",
			"expire_at": goutil.TimeNow().Unix() + 100000000,
		},
	}
	require.NoError(service.LRURedis.Set(sessionKey, tutil.SprintJSON(v), time.Minute).Err())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName, Value: "test"})
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: "123456789"})
	UnmarshalUser(c)
	u, ok := c.Get("user")
	require.True(ok)
	assert.Equal(int64(456), u.(User).id)

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName, Value: "test"})
	UnmarshalUser(c)
	u, ok = c.Get("user")
	require.True(ok)
	assert.Equal(UserID("123"), u.(User).userID)
}

func TestUnmarshalFromToken(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	assert.False(unmarshalFromToken(c))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: "test"})
	assert.False(unmarshalFromToken(c))

	r, err := service.SSO.Session("testtoken", "127.0.0.1")
	require.NoError(err)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: r.Token})
	UnmarshalUser(c)
	u, ok := c.Get("user")
	require.True(ok)
	assert.Equal(r.User.UserID, u.(User).id)
}

func TestMakeUserID(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(UserID("123"), MakeUserID(int64(123)))
	assert.Equal(UserID("guest"), MakeUserID("guest"))
	assert.PanicsWithValue("unsupported from.(type)", func() { MakeUserID(true) })
	// testUser
	u := makeTestUser()
	assert.Equal(RoleTest, u.Role())
	assert.Zero(u.ID())
	assert.True(strings.HasPrefix(string(u.UserID()), "test-"))
}
