package im

import (
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	methodJoin method = iota
	methodLeave
	methodConnectActivity
	methodDisconnectActivity
)

// conn broadcaster type
const (
	TypeBroadcasterRoom = iota
	TypeBroadcasterActivity
)

// 用户行为发起方
const (
	operatorUser   string = "user"
	operatorSystem string = "system"
	// TODO: 可能需要支持管理员
)

// CountBroadcaster 运行中的 broadcaster 数量
var CountBroadcaster int64

type method int

type userMethod struct {
	Method method
	Conn   *WSConn
	// Operator 发起方
	Operator  string
	uuid      string // 用户传过来的 uuid
	reconnect bool   // 是否是重连
}

// Broadcaster 收到广播消息，然后广播给 Users
type Broadcaster struct {
	roomID int64
	Type   int
	bc     *BroadcasterController
	uuid   uuid.UUID
	Conns  map[uuid.UUID]*WSConn // 用 WsConnection 的 UUID 做键

	ChFromUser   chan *userMethod // 用户来的
	ChFromPubsub chan *imMessage  // 订阅来的
	ChClose      chan struct{}
	ChRefresh    chan struct{}

	keyMember        string // ZSET, 统计在线人数
	KeyUsers         string // HASH, 统计某个用户是否在线
	KeyNotify        string // SET, 存储需要及时广播加入离开的用户，比如主播和连麦用户
	KeyValuableUsers string // SET, 贵宾榜和统计在线热度用

	keyJoinQueue  string
	lockJoinQueue string

	joinSet map[uuid.UUID]struct{}
}

// NewBroadcaster new Broadcaster
func NewBroadcaster(roomID int64, bc *BroadcasterController) *Broadcaster {
	b := Broadcaster{
		roomID: roomID,
		Type:   TypeBroadcasterRoom,
		bc:     bc,
		uuid:   uuid.New(),
		Conns:  make(map[uuid.UUID]*WSConn),

		ChFromUser:   make(chan *userMethod),
		ChFromPubsub: make(chan *imMessage, config.Conf.IM.PubsubSize), // 收消息不阻塞

		ChRefresh: make(chan struct{}, 1),
		ChClose:   make(chan struct{}, 1),

		keyMember:        keys.KeyIMRoomMembers1.Format(roomID),
		KeyUsers:         keys.KeyIMRoomUsers1.Format(roomID),
		KeyNotify:        keys.KeyIMRoomNotify1.Format(roomID),
		KeyValuableUsers: keys.IMKeyRoomValuableUsers1.Format(roomID),

		keyJoinQueue:  keys.KeyIMRoomJoinQueue1.Format(roomID),
		lockJoinQueue: keys.LockIMRoomJoinQueue1.Format(roomID),

		joinSet: make(map[uuid.UUID]struct{}),
	}
	go b.run()
	return &b
}

// NewActivityBroadcaster new  activity Broadcaster
func NewActivityBroadcaster(roomID int64, bc *BroadcasterController) *Broadcaster {
	b := Broadcaster{
		roomID: roomID,
		Type:   TypeBroadcasterActivity,
		bc:     bc,
		uuid:   uuid.New(),
		Conns:  make(map[uuid.UUID]*WSConn),

		ChFromUser:   make(chan *userMethod),
		ChFromPubsub: make(chan *imMessage, config.Conf.IM.PubsubSize), // 收消息不阻塞

		ChRefresh: make(chan struct{}, 1),
		ChClose:   make(chan struct{}, 1),
	}
	go b.run()
	return &b
}

func (b *Broadcaster) run() {
	atomic.AddInt64(&CountBroadcaster, 1)
	defer atomic.AddInt64(&CountBroadcaster, -1)
	tenSecond := 10 * time.Second
	sendJoinTimer := time.NewTicker(tenSecond)
	defer sendJoinTimer.Stop()
	checkTicker := time.NewTicker(10 * time.Minute)
	defer checkTicker.Stop()
	for {
		select {
		case <-b.ChClose:
			b.close()
			return
		case <-b.ChRefresh: // 刷新 channel
			b.refresh()
		case pm := <-b.ChFromPubsub: // 从订阅来的操作
			// TODO: 目前只支持广播消息
			if pm.UserID == 0 {
				// 房间内消息
				for _, user := range b.Conns {
					if pm.isMsgDisabled(user) {
						continue
					}
					go user.Send(pm.wsMessage(user.supportCompression))
				}
			} else {
				// 房间内指定用户消息，一个用户可能会有多个连接
				for _, user := range b.Conns {
					if user.userInfo != nil && user.userInfo.User != nil &&
						user.userInfo.User.UserID() == pm.UserID && !pm.isMsgDisabled(user) {
						go user.Send(pm.wsMessage(user.supportCompression))
					}
				}
			}
		case joinLeave := <-b.ChFromUser: // 从用户来的操作
			b.handleUserMethod(joinLeave)
		case <-sendJoinTimer.C: // 定时广播加入消息
			b.sendJoins(tenSecond)
		case <-checkTicker.C:
			var stored *Broadcaster
			switch b.Type {
			case TypeBroadcasterActivity:
				stored = b.bc.ActivityBroadcaster(b.roomID)
			default:
				stored = b.bc.Broadcaster(b.roomID, false)
			}
			if stored == nil || stored.uuid != b.uuid {
				// 这个已经是游离中的连接了
				logger.WithField("room_id", b.roomID).Error("this broadcaster is dissociated")
				b.close()
				return
			}
		}
	}
}

// refresh 刷新 Broadcaster
func (b *Broadcaster) refresh() {
	switch b.Type {
	case TypeBroadcasterRoom:
		b.refreshRoom()
	case TypeBroadcasterActivity:
		b.refreshActivity()
	}
}

func (b *Broadcaster) refreshActivity() {
	var connIDs []uuid.UUID
	var conns []*WSConn
	for connID, conn := range b.Conns {
		if conn == nil {
			connIDs = append(connIDs, connID)
			continue
		}
		if atomic.LoadInt32(&conn.status) >= statusClose {
			conns = append(conns, conn)
			continue
		}
	}
	// 清理连接
	for i := range connIDs {
		delete(b.Conns, connIDs[i])
	}
	for i := range conns {
		b.disconnectActivity(&userMethod{Method: methodDisconnectActivity, Conn: conns[i], Operator: operatorSystem})
		conns[i].Close()
	}
}

// refresh 刷新 Broadcaster
func (b *Broadcaster) refreshRoom() {
	// 更新 members ZSET
	pipe := service.IMRedis.Pipeline()
	score := timeScore()
	var connIDs []uuid.UUID
	var conns []*WSConn
	for connID, conn := range b.Conns {
		if conn == nil {
			connIDs = append(connIDs, connID)
			continue
		}
		if atomic.LoadInt32(&conn.status) >= statusClose {
			conns = append(conns, conn)
			continue
		}
		// 更新连接时间戳
		if !conn.isPreview {
			// 更新连接时间戳
			pipe.ZAdd(b.keyMember,
				&redis.Z{Score: score, Member: conn.redisMemberValue()})
		}
	}
	_, err := pipe.Exec()
	if err != nil {
		logger.WithField("room_id", b.roomID).Errorf("fresh members failed: %v", err)
	}

	// 清理连接
	for i := range connIDs {
		delete(b.Conns, connIDs[i])
	}
	for i := range conns {
		b.leaveRoom(&userMethod{Method: methodLeave, Conn: conns[i], Operator: operatorSystem})
		conns[i].Close()
	}

	// 清理 users HSET
	fields, err := service.IMRedis.HGetAll(b.KeyUsers).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	deleteFields := make([]string, 0, len(fields))
	for field, value := range fields {
		count, _ := strconv.ParseInt(value, 10, 64)
		if count <= 0 {
			deleteFields = append(deleteFields, field)
		}
	}
	if len(deleteFields) != 0 {
		_, err = service.IMRedis.HDel(b.KeyUsers, deleteFields...).Result()
		if err != nil {
			logger.Errorf("fresh users failed: %v", err)
		}
	}
}

func (b *Broadcaster) close() {
	switch b.Type {
	case TypeBroadcasterRoom:
		for _, conn := range b.Conns {
			if conn != nil {
				b.leaveRoom(&userMethod{Method: methodLeave, Conn: conn, Operator: operatorSystem})
				conn.Close()
			}
		}
	case TypeBroadcasterActivity:
		for _, conn := range b.Conns {
			if conn != nil {
				b.disconnectActivity(&userMethod{Method: methodDisconnectActivity, Conn: conn, Operator: operatorSystem})
				conn.Close()
			}
		}
	}
}

// handleUserMethod 处理加入离开
// NOTICE: 由 channel.actionControl 调用
func (b *Broadcaster) handleUserMethod(um *userMethod) {
	switch um.Method {
	case methodJoin:
		b.joinRoom(um)
	case methodLeave:
		b.leaveRoom(um)
	case methodConnectActivity:
		b.connectActivity(um)
	case methodDisconnectActivity:
		b.disconnectActivity(um)
	default:
		// 正常情况时不会触发到这里的
		panic(
			fmt.Sprintf("Unsupported join leave method, method code: %d", um.Method))
	}
}

type guestCount struct {
	UserID int64 `json:"user_id"`
	Count  int   `json:"count"`
}

// sendJoins 发送进场提示
// NOTICE: channel.actionControl 定时调用
func (b *Broadcaster) sendJoins(lockTime time.Duration) {
	// 非直播间类型连接或空房间不尝试获取进场消息
	if b.Type != TypeBroadcasterRoom || len(b.joinSet) == 0 {
		return
	}
	b.joinSet = make(map[uuid.UUID]struct{})
	ok, err := service.IMRedis.SetNX(b.lockJoinQueue, "1", lockTime).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	if !ok {
		return
	}
	pipe := service.IMRedis.TxPipeline()
	cmd := pipe.LRange(b.keyJoinQueue, 0, -1)
	pipe.Del(b.keyJoinQueue)
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
	uidStr := cmd.Val()
	joinCount := len(uidStr)
	if joinCount == 0 {
		return
	}
	userIDs := make([]int64, 0, joinCount)
	userIDSet := make(map[int64]struct{}, joinCount)
	for i := 0; i < joinCount; i++ {
		userID, _ := strconv.ParseInt(uidStr[i], 10, 64) // 解析出来的非 0 用户 ID 都是不隐身的用户 ID
		if _, ok := userIDSet[userID]; !ok || userID == 0 {
			userIDs = append(userIDs, userID)
			userIDSet[userID] = struct{}{}
		}
	}

	// userIDs 包含匿名用户，FilterJoined 会把匿名用户给过滤掉
	notifyUserIDs := liveim.FilterJoined(b.roomID, util.Uniq(userIDs))
	userMap := map[int64]*liveuser.Simple{} // 默认代表全都是匿名用户或者数据库没有查询到用户的情况
	if len(notifyUserIDs) != 0 {
		simples, err := liverpc.RoomUsers(notifyUserIDs, b.roomID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if len(simples) != 0 {
			userMap = goutil.ToMap(simples, "UID").(map[int64]*liveuser.Simple)
		}
	}
	queue := make([]interface{}, 0, joinCount)
	var anonCount int
	for i := range userIDs {
		if userIDs[i] == 0 {
			anonCount++
			continue
		}
		if s := userMap[userIDs[i]]; s != nil {
			queue = append(queue, s)
		}
	}
	if anonCount > 0 {
		// 匿名用户合并为一条消息
		queue = append(queue, guestCount{UserID: 0, Count: anonCount})
	}
	if len(queue) == 0 {
		return
	}
	payload := map[string]interface{}{
		"type":    liveim.TypeMember,
		"event":   liveim.EventJoinQueue,
		"room_id": b.roomID,
		"queue":   queue,
	}
	m := &liveim.IMMessage{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: b.roomID,
	}
	err = m.SetPayload(payload)
	if err != nil {
		logger.Error(err)
		return
	}
	err = liveim.Publish(m)
	if err != nil {
		logger.Error(err)
		return
	}
}

// 广播消息
func (b *Broadcaster) broadcast(pm *imMessage) {
	b.ChFromPubsub <- pm
}

func (b *Broadcaster) joinRoom(um *userMethod) {
	// 加入房间默认都是用户操作的
	var err error
	info := make(handler.M)
	message := msgJoinLeave{
		UUID:   um.uuid,
		Type:   liveim.TypeRoom,
		Event:  eventJoin,
		RoomID: b.roomID,
		Info:   info,
	}
	defer func() {
		if err != nil {
			if v, ok := err.(*mrpc.ClientError); ok {
				message.Code = v.Code
				message.Info = v.Message
			} else {
				reqErr := actionerrors.ErrDatabaseLegacy
				message.Code = reqErr.Code
				message.Info = reqErr.Message
			}
		} else if u := um.Conn.userInfo; u != nil && u.User != nil {
			info["user"] = u.User
			info["status"] = joinStatus{Invisible: u.IsInvisible}
			// medal 当前房间当前用户的粉丝勋章，根据勋章等级判断是否拥有赠送对应等级的粉丝礼物的资格
			if u.RoomMedal != nil {
				info["medal"] = u.RoomMedal.Mini
			}
		}
		um.Conn.SendNow(message.wsMessage(um.Conn.supportCompression))
	}()
	userID := um.Conn.User.ID()
	r, err := liverpc.RoomInfo(b.roomID, userID)
	if err != nil {
		return
	}
	info["room"] = handler.M{"status": handler.M{"open": r.Room.Status.Open}}

	if b.Conns[um.Conn.UUID] == um.Conn {
		// 已经在房间里面了
		return
	}
	addConns := func() {
		b.Conns[um.Conn.UUID] = um.Conn
		logger.Debugf("user %s (connection ID: %s) join room %d, operator: %s",
			um.Conn.User.UserID(), um.Conn.UUID, b.roomID, um.Operator)
		atomic.AddInt64(&inRoomCount, 1)
	}
	if um.Conn.isPreview {
		// 预览用户不计入直播间在线人数，无入场通知
		addConns()
		return
	}
	pipe := service.IMRedis.TxPipeline()
	pipe.ZAdd(b.keyMember, &redis.Z{
		Score:  timeScore(),
		Member: um.Conn.redisMemberValue()})
	var cmd *redis.IntCmd
	if userID != 0 {
		// 不统计非注册用户
		cmd = pipe.HIncrBy(b.KeyUsers, string(um.Conn.User.UserID()), 1)
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
	addConns()

	if cmd != nil && cmd.Val() == 0 {
		// 重复加入的情况
		return
	}
	// 第一次加入的情况
	b.joinSet[um.Conn.UUID] = struct{}{}
	notifyJoinLeave(b, um.Conn, eventJoin)
	pipe = service.IMRedis.Pipeline()
	u := um.Conn.userInfo
	if !(r.Room.Config != nil && r.Room.Config.DisableJoinQueue) && !um.reconnect {
		// 后台特殊角色不加入进场队列
		if u == nil || !u.HasInvisibleRoles {
			id := int64(0) // 0 代表匿名用户（包含未登录用户和进场隐身用户）
			if u != nil && !u.IsInvisible {
				id = userID
			}
			pipe.RPush(b.keyJoinQueue, id)
		}
	}
	if u != nil && u.IsValuableUser {
		pipe.SAdd(b.KeyValuableUsers, userID)
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (b *Broadcaster) leaveRoom(um *userMethod) {
	if um.uuid != "" {
		// 如果是用户请求的，总是需要响应
		defer func() {
			msg := msgJoinLeave{
				UUID:   um.uuid,
				Type:   liveim.TypeRoom,
				Event:  eventLeave,
				RoomID: b.roomID,
				Info:   "leave room success",
			}
			um.Conn.SendNow(msg.wsMessage(um.Conn.supportCompression))
		}()
	}
	if c := b.Conns[um.Conn.UUID]; c == nil {
		return
	}
	delete(b.Conns, um.Conn.UUID)
	logger.Debugf("user %s (connection ID: %s) leave room %d, operator: %s",
		um.Conn.User.UserID(), um.Conn.UUID, b.roomID, um.Operator)
	if after := atomic.AddInt64(&inRoomCount, -1); after < 0 {
		atomic.AddInt64(&inRoomCount, 1)
	}
	if um.Conn.isPreview {
		return
	}
	delete(b.joinSet, um.Conn.UUID)

	pipe := service.IMRedis.Pipeline() // NOTICE: 离开房间不考虑事务
	pipe.ZRem(b.keyMember, um.Conn.redisMemberValue())
	userID := um.Conn.User.ID()
	var cmd *redis.IntCmd
	if userID != 0 {
		cmd = pipe.HIncrBy(b.KeyUsers, string(um.Conn.User.UserID()), -1)
	}
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
	if cmd == nil || cmd.Val() != 0 {
		// 用户并非完全离开房间（一个用户在此房间有多个连接）
		return
	}
	// 用户完全离开房间后的操作
	notifyJoinLeave(b, um.Conn, eventLeave)
	pipe = service.IMRedis.Pipeline()
	pipe.HDel(b.KeyUsers, string(um.Conn.User.UserID()))
	u := um.Conn.userInfo
	if u != nil && u.IsValuableUser {
		pipe.SRem(b.KeyValuableUsers, userID)
	}
	if u != nil && !u.IsInvisible {
		// 只有登录用户最后一次离开房间需要从进场队列中移除
		pipe.LRem(b.keyJoinQueue, -1, userID)
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
}

type joinStatus struct {
	Invisible bool `json:"invisible"`
}

type userJoinLeaveNotify struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	User   struct {
		UserID int64 `json:"user_id"`
	} `json:"user"`
}

func notifyJoinLeave(bc *Broadcaster, conn *WSConn, event string) {
	if conn.User.ID() == 0 {
		return
	}
	exist, err := service.IMRedis.SIsMember(bc.KeyNotify, conn.User.ID()).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	if !exist {
		return
	}
	notify := userJoinLeaveNotify{Type: typeMember, Event: event, RoomID: bc.roomID}
	notify.User.UserID = conn.User.ID()

	m := &liveim.IMMessage{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: bc.roomID,
	}
	err = m.SetPayload(notify)
	if err != nil {
		logger.Error(err)
		return
	}
	err = liveim.Publish(m)
	if err != nil {
		logger.Errorf("notify user %d %s room %d failed",
			notify.User.UserID, notify.Event, notify.RoomID)
		return
	}
}

// BroadcasterController broadcaster controller
type BroadcasterController struct {
	rwLock               sync.RWMutex
	Broadcasters         map[int64]*Broadcaster
	ActivityBroadcasters map[int64]*Broadcaster

	ChSubMessage chan string
	ChClose      chan struct{}
	ChRefresh    chan struct{}
}

// NewBroadcasterController new BroadcasterController
func NewBroadcasterController() *BroadcasterController {
	res := BroadcasterController{
		Broadcasters:         make(map[int64]*Broadcaster),
		ActivityBroadcasters: make(map[int64]*Broadcaster),

		ChSubMessage: make(chan string, 5),
		ChClose:      make(chan struct{}),
		ChRefresh:    make(chan struct{}),
	}
	goutil.Go(func() { res.run() })
	return &res
}

// 获取已经存在的房间连接
func (bc *BroadcasterController) broadcaster(roomID int64) *Broadcaster {
	bc.rwLock.RLock()
	defer bc.rwLock.RUnlock()
	return bc.Broadcasters[roomID]
}

// 通过加读锁的方式获取所有直播间连接, 并转换为 slice, 防止 map 的并发读写造成的 panic
func (bc *BroadcasterController) allRoomBroadcasters() []*Broadcaster {
	bc.rwLock.RLock()
	defer bc.rwLock.RUnlock()

	broadcasters := make([]*Broadcaster, 0, len(bc.Broadcasters))
	for _, b := range bc.Broadcasters {
		broadcasters = append(broadcasters, b)
	}
	return broadcasters
}

// Broadcaster 获取房间
// isNewConn 为 true 时，会尝试查询房间是否存在
func (bc *BroadcasterController) Broadcaster(roomID int64, isNewConn bool) *Broadcaster {
	b := bc.broadcaster(roomID)
	if b != nil || !isNewConn {
		return b
	}
	_, err := liverpc.RoomInfo(roomID, 0)
	if err != nil {
		var clientErr *mrpc.ClientError
		ok := errors.As(err, &clientErr)
		if ok && clientErr.Code == actionerrors.ErrBannedRoom.Code {
			logger.WithField("room_id", roomID).Warn(err)
		} else {
			logger.WithField("room_id", roomID).Error(err)
		}
		// PASS
		return nil
	}

	// 新建连接使用写锁
	bc.rwLock.Lock()
	defer bc.rwLock.Unlock()
	if b = bc.Broadcasters[roomID]; b != nil {
		// 已存在，不会再覆盖，直接返回
		return b
	}
	b = NewBroadcaster(roomID, bc)
	bc.Broadcasters[roomID] = b
	return b
}

// ActivityBroadcaster 获取已存在的连接
func (bc *BroadcasterController) ActivityBroadcaster(roomID int64) *Broadcaster {
	bc.rwLock.RLock()
	defer bc.rwLock.RUnlock()
	return bc.ActivityBroadcasters[roomID]
}

// 通过加读锁的方式获取所有直播间的活动 ws 连接, 并转换为 slice, 防止 map 的并发读写造成的 panic
func (bc *BroadcasterController) allActivityRoomBroadcasters() []*Broadcaster {
	bc.rwLock.RLock()
	defer bc.rwLock.RUnlock()

	broadcasters := make([]*Broadcaster, 0, len(bc.ActivityBroadcasters))
	for _, b := range bc.ActivityBroadcasters {
		broadcasters = append(broadcasters, b)
	}
	return broadcasters
}

// BuildActivityBroadcaster 连接不存在时创建新连接
// NOTICE: 新建连接时需要先判断房间是否存在
func (bc *BroadcasterController) BuildActivityBroadcaster(roomID int64) *Broadcaster {
	// 新建连接时添加写锁
	bc.rwLock.Lock()
	defer bc.rwLock.Unlock()

	b := bc.ActivityBroadcasters[roomID]
	if b != nil {
		return b
	}
	// 新建连接
	b = NewActivityBroadcaster(roomID, bc)
	bc.ActivityBroadcasters[roomID] = b
	return b
}

func (b *Broadcaster) connectActivity(um *userMethod) {
	b.Conns[um.Conn.UUID] = um.Conn
}

func (b *Broadcaster) disconnectActivity(um *userMethod) {
	delete(b.Conns, um.Conn.UUID)
}

func (bc *BroadcasterController) run() {
	for {
		select {
		case message, ok := <-bc.ChSubMessage:
			if !ok {
				return
			}
			m := decodeIMMessage(message)
			switch m.Type {
			case liveim.IMMessageTypeNormal:
				// 广播消息只向已有连接的房间中执行
				b := bc.Broadcaster(m.RoomID, false)
				if b == nil {
					continue
				}
				goutil.Go(func() { b.broadcast(m) })
			case liveim.IMMessageTypeAll:
				goutil.Go(func() {
					broadcasters := bc.allRoomBroadcasters()
					for i := range broadcasters {
						broadcasters[i].broadcast(m)
					}
				})
			case liveim.IMMessageTypeActivity:
				// 广播消息只向已有连接的房间中执行
				b := bc.ActivityBroadcaster(m.RoomID)
				if b == nil {
					continue
				}
				goutil.Go(func() { b.broadcast(m) })
			case liveim.IMMessageTypeActivityAll:
				goutil.Go(func() {
					broadcasters := bc.allActivityRoomBroadcasters()
					for i := range broadcasters {
						broadcasters[i].broadcast(m)
					}
				})
			}
		case <-bc.ChClose:
			bc.close()
			return
		case <-bc.ChRefresh:
			bc.refresh()
		}
	}
}

// autoRefresh 自动刷新连接中的房间
func (bc *BroadcasterController) refresh() {
	bc.rwLock.Lock()
	defer bc.rwLock.Unlock()
	var emptyRoomIDs []int64
	for roomID, b := range bc.Broadcasters {
		if b == nil || len(b.Conns) == 0 {
			emptyRoomIDs = append(emptyRoomIDs, roomID)
			continue
		}
		b.ChRefresh <- struct{}{}
	}
	if len(emptyRoomIDs) != 0 {
		logger.Infof("clean rooms: %v", emptyRoomIDs)
		for i := range emptyRoomIDs {
			b := bc.Broadcasters[emptyRoomIDs[i]]
			if b != nil {
				b.ChClose <- struct{}{}
			}
			delete(bc.Broadcasters, emptyRoomIDs[i])
		}
	}

	// refresh 活动相关连接
	var emptyActivityRoomIDs []int64
	for roomID, b := range bc.ActivityBroadcasters {
		if b == nil || len(b.Conns) == 0 {
			emptyActivityRoomIDs = append(emptyActivityRoomIDs, roomID)
			continue
		}
		b.ChRefresh <- struct{}{}
	}
	if len(emptyActivityRoomIDs) != 0 {
		logger.Infof("clean activity rooms: %v", emptyActivityRoomIDs)
		for i := range emptyActivityRoomIDs {
			b := bc.ActivityBroadcasters[emptyActivityRoomIDs[i]]
			if b != nil {
				b.ChClose <- struct{}{}
			}
			delete(bc.ActivityBroadcasters, emptyActivityRoomIDs[i])
		}
	}
}

// close 关闭 Subscription
func (bc *BroadcasterController) close() {
	bc.rwLock.Lock()
	defer bc.rwLock.Unlock()
	logger.WithFields(logger.Fields{
		"broadcasters_size":          len(bc.Broadcasters),
		"activity_broadcasters_size": len(bc.ActivityBroadcasters),
	}).Info("close subscription")
	for _, b := range bc.Broadcasters {
		if b != nil {
			b.ChClose <- struct{}{}
		}
	}
	bc.Broadcasters = make(map[int64]*Broadcaster)
	// 关闭活动相关 Broadcasters
	for _, b := range bc.ActivityBroadcasters {
		if b != nil {
			b.ChClose <- struct{}{}
		}
	}
	bc.ActivityBroadcasters = make(map[int64]*Broadcaster)
	// 收取所有消息
	for len(bc.ChSubMessage) > 0 {
		<-bc.ChSubMessage
	}
	close(bc.ChSubMessage)
	close(bc.ChRefresh)
	close(bc.ChClose)
}

type imMessage struct {
	Type              int             `json:"type"`
	RoomID            int64           `json:"room_id"`
	UserID            int64           `json:"user_id"`
	Priority          int             `json:"priority"`
	Payload           json.RawMessage `json:"payload"`
	CompressedPayload []byte          `json:"-"`
}

func (pm *imMessage) wsMessage(supportCompression bool) wsMessage {
	if supportCompression && pm.CompressedPayload != nil {
		return wsMessage{
			Type: websocket.BinaryMessage,
			Data: pm.CompressedPayload,
		}
	}
	return wsMessage{
		Type: websocket.TextMessage,
		Data: pm.Payload,
	}
}

// 广播的优先级
const (
	BroadcastPriorityNormal    = iota // 常规消息
	BroadcastPriorityPurchased        // 消费消息
)

func (pm *imMessage) isMsgDisabled(conn *WSConn) bool {
	switch conn.riskStatus {
	case riskStatusDisableAllMsg:
		return true
	case riskStatusDisablePurchasedMsg:
		return !config.Conf.IM.DisableIMHighRiskCheck && pm.Priority == BroadcastPriorityPurchased
	default:
		return false
	}
}

// 第一个字节不是 0x00 则说明是普通 json
/* 格式化后的数据
第一个字节：固定 0x00
第二到第四个字节：小端字节序的未压缩的 IMMessage json 格式长度
第五个字节到长度所指范围：IMMessage json data
剩下数据: imMessage.CompressedPayload
*/
// TODO: 后续将使用 pb 进行 encode/decode
func decodeIMMessage(message string) *imMessage {
	data := []byte(message)
	if len(data) < 1 {
		return nil
	}
	var pm imMessage
	var jsonStart int
	var jsonEnd int
	if data[0] == liveim.DataTypeIMMessage {
		jsonStart = 4
		lengthBytes := []byte{data[1], data[2], data[3], 0}
		jsonEnd = int(binary.LittleEndian.Uint32(lengthBytes) + 4)
	} else {
		jsonEnd = len(data)
	}
	err := json2.Unmarshal(data[jsonStart:jsonEnd], &pm)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if jsonStart == 4 {
		pm.CompressedPayload = data[jsonEnd:]
	}
	return &pm
}
