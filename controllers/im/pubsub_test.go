package im

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestClearIM(t *testing.T) {
	assert := assert.New(t)

	_ = DefaultSc.Bc.Broadcaster(existsRoomID, true)
	assert.NotPanics(func() {
		ClearIM()
		ClearIM()
	})
	time.Sleep(time.Second)
	assert.Zero(CountBroadcaster)
	DefaultBc = NewBroadcasterController()
	DefaultSc = NewSubscriptionController(DefaultBc)
}

func TestSCRefresh(t *testing.T) {
	assert := assert.New(t)

	bc := NewBroadcasterController()
	sc := NewSubscriptionController(bc)
	defer func() {
		sc.ChClose <- struct{}{}
	}()
	sc.Lock.Lock()
	go func() {
		<-time.NewTimer(200 * time.Millisecond).C
		sc.Lock.Unlock()
	}()
	before := goutil.TimeNow()
	assert.NotPanics(func() { sc.refresh() })
	after := goutil.TimeNow()
	assert.True(after.After(before.Add(200 * time.Millisecond)))
}
