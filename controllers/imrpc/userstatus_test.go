package imrpc

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionUserStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyIMRoomUsers1.Format(room.TestExistsRoomID)
	_, err := service.IMRedis.HSet(key, "123", 1).Result()
	require.NoError(err)
	defer service.IMRedis.HDel(key, "123")

	c := handler.NewTestContext(http.MethodPost, "/userstatus", false, "[]")
	_, err = ActionUserStatus(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/userstatus", false,
		handler.M{"room_id": room.TestExistsRoomID, "user_ids": []int64{123, 456}})
	r, err := ActionUserStatus(c)
	require.NoError(err)
	res := r.(OnlineStatusResp)
	assert.EqualValues(2, len(res.UserStatus))
	assert.EqualValues(room.TestExistsRoomID, res.RoomID)
	assert.True(res.UserStatus[0].Online)
	assert.False(res.UserStatus[1].Online)
}
