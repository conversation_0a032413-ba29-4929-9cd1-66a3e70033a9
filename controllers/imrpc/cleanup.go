package imrpc

import (
	"sync"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/imuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type cleanUpParam struct {
	RoomID  int64   `json:"room_id"`
	RoomIDs []int64 `json:"room_ids"`
}

// ActionCleanup 清理 im 连接
/**
 * @api {post} /rpc/cron/cleanup 清理 im 直播间连接
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} [room_id] 清理指定直播间，room_id 和 room_ids 都不传则清理全部直播间
 * @apiParam {Number[]} [room_ids] 清理指定直播间，room_id 和 room_ids 都不传则清理全部直播间
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "清理成功"
 *   }
 */
func ActionCleanup(c *handler.Context) (handler.ActionResponse, error) {
	var param cleanUpParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.cleanup()
	return "清理成功", nil
}

func (param *cleanUpParam) cleanup() {
	beforeTime := goutil.TimeNow()
	logger.Info("开始清理 im")
	if param.RoomID != 0 {
		param.RoomIDs = append(param.RoomIDs, param.RoomID)
	}
	if len(param.RoomIDs) == 0 {
		err := param.findRoomIDs(1000)
		if err != nil {
			logger.Error(err)
			return
		}
	}
	d := goutil.TimeNow().Sub(beforeTime)
	logger.Infof("获取待清理房间成功，开始清理房间，待清理房间数量：%d，耗时：%s",
		len(param.RoomIDs), d.String())

	beforeTime = goutil.TimeNow()
	const workerNum = 2
	maxDuration := param.cleanUpRoom(workerNum)
	d = goutil.TimeNow().Sub(beforeTime)
	logger.Infof("清理房间完成，总耗时：%s，单次清理最长耗时：%s",
		d.String(), maxDuration.String())
}

func (param *cleanUpParam) findRoomIDs(scanCount int64) error {
	appendRoomIDs := func(imKeys []string) {
		for i := range imKeys {
			roomID := keys.ParseIMRoomID(imKeys[i])
			if roomID != 0 {
				param.RoomIDs = append(param.RoomIDs, roomID)
			}
		}
	}
	imKeys, cursor, err := service.IMRedis.Scan(0, "live-service:im:room:*:members", scanCount).Result()
	if err != nil {
		logger.Error(err)
		return err
	}
	param.RoomIDs = make([]int64, 0, len(imKeys))
	appendRoomIDs(imKeys)
	for cursor != 0 {
		imKeys, cursor, err = service.IMRedis.Scan(cursor, "live-service:im:room:*:members", scanCount).Result()
		if err != nil {
			logger.WithField("cursor", cursor).Error(err)
			return err
		}
		appendRoomIDs(imKeys)
	}
	return nil
}

func (param *cleanUpParam) cleanUpRoom(workerNum int) time.Duration {
	minValidTimeUnix := goutil.TimeNow().Add(-time.Hour).Unix()

	roomIDCh := make(chan int64)
	var maxDuration time.Duration
	var durationMutex sync.Mutex
	var wg sync.WaitGroup

	refresh := func() {
		defer wg.Done()
		var maxDurationLocal time.Duration
		for roomID := range roomIDCh {
			startTime := goutil.TimeNow()
			err := imuser.RefreshUsers(roomID, minValidTimeUnix)
			if err != nil {
				logger.WithField("room_id", roomID).Error(err)
				continue
			}
			endTime := goutil.TimeNow()
			d := endTime.Sub(startTime)
			if d > maxDurationLocal {
				maxDurationLocal = d
			}
		}
		durationMutex.Lock()
		if maxDurationLocal > maxDuration {
			maxDuration = maxDurationLocal
		}
		durationMutex.Unlock()
	}

	for i := 0; i < workerNum; i++ {
		wg.Add(1)
		util.Go(refresh)
	}
	wg.Add(1)
	util.Go(func() {
		defer wg.Done()
		for i := range param.RoomIDs {
			roomIDCh <- param.RoomIDs[i]
		}
		close(roomIDCh)
	})
	wg.Wait()
	return maxDuration
}
