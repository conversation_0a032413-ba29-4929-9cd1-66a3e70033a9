package imrpc

import (
	"encoding/json"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type broadcastParam struct {
	Type     int             `json:"type"`
	RoomID   int64           `json:"room_id,omitempty"`
	UserID   int64           `json:"user_id,omitempty"`
	Priority int             `json:"priority,omitempty"`
	Method   string          `json:"method"`
	Payload  json.RawMessage `json:"payload"`
}

// ActionBroadcast 广播房间内消息
/**
 * @api {post} /rpc/broadcast 广播房间内消息
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [priority=0] 消息类型的优先级, 0 是普通消息, 1 是付费消息
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionBroadcast(c *handler.Context) (handler.ActionResponse, error) {
	var param broadcastParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Payload) == 0 || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	msg := liveim.IMMessage{
		Type:     liveim.IMMessageTypeNormal,
		RoomID:   param.RoomID,
		Priority: param.Priority,
	}
	err = msg.SetPayload(param.Payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID})
	}
	err = liveim.Publish(&msg)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID})
	}
	return "success", nil
}

// ActionBroadcastAll 广播全局消息
/**
 * @api {post} /rpc/broadcast/all 广播全局消息
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionBroadcastAll(c *handler.Context) (handler.ActionResponse, error) {
	var param broadcastParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Payload) == 0 {
		return nil, actionerrors.ErrParams
	}
	// 从 payload 获取 roomID
	// TODO: 改成从请求参数 from_room_id 获取发送的房间
	var r struct {
		RoomID int64 `json:"room_id"`
	}
	_ = json2.Unmarshal(param.Payload, &r)
	msg := liveim.IMMessage{
		Type:     liveim.IMMessageTypeAll,
		RoomID:   r.RoomID,
		Priority: param.Priority,
	}
	if goutil.HasElem(room.OpenListExcludeRoomIDs(), r.RoomID) {
		// 对于指定的测试用直播间，不发送全站广播
		msg.Type = liveim.IMMessageTypeNormal
	}
	err = msg.SetPayload(param.Payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": msg.RoomID})
	}
	err = liveim.Publish(&msg)
	if err != nil {
		logger.WithField("room_id", msg.RoomID).Errorf("broadcast all failed: %v", err)
		// PASS
	}
	return "success", nil
}

// ActionBroadcastUser 给房间内特定用户发送消息
/**
 * @api {post} /rpc/broadcast/user 给房间内特定用户发送消息
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [priority=0] 消息类型的优先级, 0 是普通消息, 1 是付费消息
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionBroadcastUser(c *handler.Context) (handler.ActionResponse, error) {
	var param broadcastParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Payload) == 0 || param.RoomID <= 0 || param.UserID == 0 {
		return nil, actionerrors.ErrParams
	}
	msg := liveim.IMMessage{
		Type:     liveim.IMMessageTypeNormal,
		RoomID:   param.RoomID,
		UserID:   param.UserID,
		Priority: param.Priority,
	}
	err = msg.SetPayload(param.Payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID})
	}
	err = liveim.Publish(&msg)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID, "user_id": param.UserID})
	}
	return "success", nil
}

// ActionBroadcastMany 对 websocket 广播多条消息
/**
 * @api {post} /rpc/broadcast/many 给广播多条消息
 * @apiDescription 广播多条消息，广播类型支持房间内广播、全局广播、房间内用户广播
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} [type=0] 消息类型，0: 房间内消息，1: 全局广播
 * @apiParam {Number} room_id 房间号，全局广播此字段无效
 * @apiParam {Number} user_id 用户 ID，不为 0 则房间内消息给特定用户发
 * @apiParam {Number} [priority=0] 消息类型的优先级, 0 是普通消息, 1 是付费消息
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiParamExample {json} Request-Example:
 *     [{
 *       "type": 0, // 房间内消息
 *       "room_id": 123456,
 *       "priority": 1,
 *       "payload": {}
 *     },
 *     {
 *       "type": 1, // 全局广播
 *       "room_id": 123456,
 *       "payload": {}
 *     },
 *     {
 *       "type": 0, // 直播间内
 *       "room_id": 123456,
 *       "user_id": 123456, // 指定用户
 *       "payload": {}
 *     }]
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionBroadcastMany(c *handler.Context) (handler.ActionResponse, error) {
	var body []broadcastParam
	err := c.BindJSON(&body)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	for i := range body {
		err = body[i].checkNotifyType()
		if err != nil {
			continue
		}
		msg := &liveim.IMMessage{
			Type:     body[i].Type,
			RoomID:   body[i].RoomID,
			UserID:   body[i].UserID,
			Priority: body[i].Priority,
		}
		err = msg.SetPayload(body[i].Payload)
		if err != nil {
			logger.WithField("room_id", body[i].RoomID).Error(err)
			continue
		}
		goutil.Go(func() {
			err := liveim.Publish(msg)
			if err != nil {
				logger.Error(err)
			}
		})
	}
	return "success", nil
}

// checkNotifyType 根据广播类型检查参数是否正确
// 全局广播可能退化成单个房间
func (param *broadcastParam) checkNotifyType() error {
	if len(param.Payload) == 0 {
		return actionerrors.ErrParams
	}
	switch param.Type {
	case liveim.IMMessageTypeNormal, liveim.IMMessageTypeActivity:
		if param.RoomID <= 0 || param.UserID < 0 {
			// 用户 ID 可以为 0
			return actionerrors.ErrParams
		}
	case liveim.IMMessageTypeAll:
		// 从 payload 获取 roomID
		var r struct {
			RoomID int64 `json:"room_id"`
		}
		_ = json.Unmarshal(param.Payload, &r)
		if goutil.HasElem(room.OpenListExcludeRoomIDs(), r.RoomID) {
			// 对于指定的测试用直播间，不发送全站广播
			param.Type = liveim.IMMessageTypeNormal
			param.RoomID = r.RoomID
			param.UserID = 0 // 防止错误传参
		}
	case liveim.IMMessageTypeActivityAll:
		// 从 payload 获取 roomID
		var r struct {
			RoomID int64 `json:"room_id"`
		}
		_ = json.Unmarshal(param.Payload, &r)
		if goutil.HasElem(room.OpenListExcludeRoomIDs(), r.RoomID) {
			// 对于指定的测试用直播间，不发送全站广播
			param.Type = liveim.IMMessageTypeActivity
			param.RoomID = r.RoomID
			param.UserID = 0 // 防止错误传参
		}
	default:
		return actionerrors.ErrParams
	}
	return nil
}

// ActionActivityBroadcast 活动房间内消息
/**
 * @api {post} /rpc/activity/broadcast 活动广播房间内消息
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionActivityBroadcast(c *handler.Context) (handler.ActionResponse, error) {
	var param broadcastParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Payload) == 0 || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	msg := liveim.IMMessage{
		Type:   liveim.IMMessageTypeActivity,
		RoomID: param.RoomID,
	}
	err = msg.SetPayload(param.Payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID})
	}
	err = liveim.Publish(&msg)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": param.RoomID})
	}
	return "success", nil
}

// ActionActivityBroadcastAll 活动广播全局消息
/**
 * @api {post} /rpc/activity/broadcast/all 活动广播全局消息
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Object} payload 要广播的消息
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionActivityBroadcastAll(c *handler.Context) (handler.ActionResponse, error) {
	var param broadcastParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Payload) == 0 {
		return nil, actionerrors.ErrParams
	}
	// 从 payload 获取 roomID
	// TODO: 改成从请求参数 from_room_id 获取发送的房间
	var r struct {
		RoomID int64 `json:"room_id"`
	}
	_ = json2.Unmarshal(param.Payload, &r)
	msg := liveim.IMMessage{
		Type:   liveim.IMMessageTypeActivityAll,
		RoomID: r.RoomID,
	}
	if goutil.HasElem(room.OpenListExcludeRoomIDs(), r.RoomID) {
		// 对于指定的测试用直播间，不发送全站广播
		msg.Type = liveim.IMMessageTypeActivity
	}
	err = msg.SetPayload(param.Payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"room_id": msg.RoomID})
	}
	err = liveim.Publish(&msg)
	if err != nil {
		logger.WithField("room_id", msg.RoomID).Errorf("activity broadcast all failed: %v", err)
		// PASS
	}
	return "success", nil
}
