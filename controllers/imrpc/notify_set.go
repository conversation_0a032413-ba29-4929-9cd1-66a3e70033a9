package imrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type editNotifyParam struct {
	RoomID  int64   `json:"room_id"`
	UserIDs []int64 `json:"user_ids"`
}

// ActionNotifySet 设置直播间需要广播的加入离开房间的用户
/**
 * @api {post} /notify/set 设置需要广播加入离开的用户
 * @apiDescription 设置直播间需要广播的加入离开房间的用户，会覆盖原有存储
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number[]} user_ids 需要添加的用户 ID, 为空则清空结果
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionNotifySet(c *handler.Context) (handler.ActionResponse, error) {
	var param editNotifyParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID == 0 {
		return nil, actionerrors.ErrParams
	}
	key := keys.KeyIMRoomNotify1.Format(param.RoomID)
	pipe := service.IMRedis.Pipeline()
	pipe.Del(key)
	if l := len(param.UserIDs); l != 0 {
		members := make([]interface{}, l)
		for i := 0; i < l; i++ {
			members[i] = param.UserIDs[i]
		}
		pipe.SAdd(key, members...)
	}
	_, err = pipe.Exec()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
