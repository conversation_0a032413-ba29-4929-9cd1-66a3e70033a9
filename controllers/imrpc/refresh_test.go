package imrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionRefreshUsers(t *testing.T) {
	assert := assert.New(t)
	param := map[string]interface{}{
		"room_ids": nil,
	}
	c := handler.NewRPCTestContext("/room/refresh", param)
	_, err := ActionRefreshUsers(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = map[string]interface{}{
		"room_ids": []int64{99999},
	}
	c = handler.NewRPCTestContext("/room/refresh", param)
	_, err = ActionRefreshUsers(c)
	assert.NoError(err)
}
