package imrpc

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionNotifySet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	uids := []int64{123, 456}
	uidsStr := []string{"123", "456"}
	param := map[string]interface{}{
		"room_id":  1234,
		"user_ids": uids,
	}
	ctx := handler.CreateTestContext(false)
	ctx.C.Request, _ = http.NewRequest("POST", "/rpc/notify/set", paramToRequestBody(param))
	_, err := ActionNotifySet(ctx)
	require.NoError(err)
	time.Sleep(time.Second)
	key := keys.KeyIMRoomNotify1.Format(param["room_id"])
	exists, err := service.IMRedis.SMembers(key).Result()
	require.NoError(err)
	require.Equal(2, len(exists))
	if exists[0] == "456" {
		exists[0], exists[1] = exists[1], exists[0]
	}
	assert.Equal(uidsStr, exists)
}
