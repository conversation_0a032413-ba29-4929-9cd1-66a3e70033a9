package imrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	param := listParam{RoomID: 999}
	kc.Check(param, "room_id", "users", "conns", "valuable_users")
	c := handler.NewRPCTestContext("/room/list", param)
	_, err := ActionList(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = listParam{
		RoomID:        999,
		Users:         true,
		Conns:         true,
		ValuableUsers: true,
	}
	c = handler.NewRPCTestContext("/room/list", param)
	r, err := ActionList(c)
	require.NoError(err)
	kc.Check(r, "user_ids", "conns", "valuable_user_ids", "users")
}
