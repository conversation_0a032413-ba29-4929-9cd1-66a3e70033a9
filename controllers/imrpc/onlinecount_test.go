package imrpc

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestOnlineNumberPairTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(OnlineNumberPair{}, "room_id", "count", "valuable_count")
}

func TestActionOnlineCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/online/count", false, nil)
	_, err := ActionOnlineCount(c)
	assert.Equal(actionerrors.ErrParams, err)

	roomID := int64(9876)
	countKey := keys.KeyIMRoomMembers1.Format(roomID)
	vuKey := keys.IMKeyRoomValuableUsers1.Format(roomID)
	pipe := service.IMRedis.Pipeline()
	pipe.Del(countKey, vuKey)
	pipe.ZAdd(countKey, &redis.Z{
		Score:  0,
		Member: "TestActionOnlineCount"})
	pipe.SAdd(vuKey, 12)
	_, err = pipe.Exec()
	require.NoError(err)
	// room_id
	c = handler.NewTestContext("POST", "/online/count", false, handler.M{"room_id": roomID})
	r, err := ActionOnlineCount(c)
	require.NoError(err)
	assert.EqualValues(1, r.(OnlineNumberPair).Count)
	assert.EqualValues(1, r.(OnlineNumberPair).ValuableCount)

	c = handler.NewTestContext("POST", "/online/count", false,
		handler.M{"room_ids": []int64{roomID, roomID, -1123}})
	r, err = ActionOnlineCount(c)
	require.NoError(err)
	resp := r.([]OnlineNumberPair)
	require.Len(resp, 3)
	assert.EqualValues(roomID, resp[0].RoomID)
	assert.EqualValues(1, resp[0].Count)
	assert.EqualValues(1, resp[0].ValuableCount)
	assert.EqualValues(roomID, resp[1].RoomID)
	assert.EqualValues(1, resp[1].Count)
	assert.EqualValues(1, resp[1].ValuableCount)
	assert.EqualValues(-1123, resp[2].RoomID)
}
