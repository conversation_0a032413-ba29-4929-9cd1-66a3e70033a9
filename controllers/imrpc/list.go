package imrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/imuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type listResp struct {
	UserIDs         []int64  `json:"user_ids,omitempty"`
	Conns           []string `json:"conns,omitempty"`
	ValuableUserIDs []int64  `json:"valuable_user_ids,omitempty"`

	// WORKAROUND: 待删除
	Users []int64 `json:"users,omitempty"`
}

type listParam struct {
	RoomID        int64 `json:"room_id"`
	Users         bool  `json:"users"`
	Conns         bool  `json:"conns"`
	ValuableUsers bool  `json:"valuable_users"`
}

// ActionList 列出所给房间在线用户和连接用户
/**
 * @api {post} /room/list 列出所给房间在线用户和连接用户
 * @apiDescription 列出所给房间的在线用户和链接用户
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Boolean} users 是否返回在线用户
 * @apiParam {Boolean} conns 是否返回连接数
 * @apiParam {Boolean} valuable_users 是否返回 valuable_user_ids
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_ids": [123, ..., 233],
 *       "conns": ["123-aa-bb-cc-dd", ..., "233-aa-bb-cc-dde"],
 *       "valuable_user_ids": [123, ..., 233]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionList(c *handler.Context) (handler.ActionResponse, error) {
	var param listParam
	err := c.BindJSON(&param)
	if err != nil || param.RoomID == 0 ||
		!(param.Users || param.Conns || param.ValuableUsers) {
		// 导致 listResp 为空的请求返回参数错误
		return nil, actionerrors.ErrParams
	}
	res, err := imuser.List(param.RoomID, &imuser.ListOptions{
		ListUserIDs:         param.Users,
		ListConns:           param.Conns,
		ListValuableUserIDs: param.ValuableUsers,
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return listResp{
		UserIDs:         res.UserIDs,
		Conns:           res.Conns,
		ValuableUserIDs: res.ValuableUserIDs,
		Users:           res.UserIDs,
	}, nil
}
