package imrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/imuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionRefreshUsers 从数据库记录的聊天室连接中重新计算在线人数
/**
 * @api {post} /room/refresh 刷新聊天室在线人数
 * @apiDescription 从数据库记录的聊天室连接中重新计算在线人数
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number[]} room_ids 房间号数组
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRefreshUsers(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomIDs []int64 `json:"room_ids"`
	}
	err := c.BindJSON(&param)
	if err != nil || len(param.RoomIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	for i := 0; i < len(param.RoomIDs); i++ {
		err = imuser.RefreshUsers(param.RoomIDs[i], 0)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return "success", nil
}
