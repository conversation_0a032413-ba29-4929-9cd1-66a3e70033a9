package imrpc

import (
	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// OnlineNumberPair 房间和在线人数
type OnlineNumberPair struct {
	RoomID        int64 `json:"room_id"`
	Count         int64 `json:"count"`
	ValuableCount int64 `json:"valuable_count"`

	countCmd         *redis.IntCmd
	valuableCountCmd *redis.IntCmd
}

// ActionOnlineCount 查询房间在线人数
/**
 * @api {post} /rpc/online/count 查询房间在线人数
 * @apiDescription 只支持查询 room_ids 和 room_id 均有时，只查询 room_ids
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {Number} [room_id] 房间号
 * @apiParam {Number[]} [room_ids] 房间号数组
 *
 * @apiSuccessExample {json} Success-Response:
 *   // room_id
 *   {
 *     "code": 0,
 *     "info": {
 *       "room_id": 123,
 *       "count": 10,
 *       "valuable_count": 0
 *     }
 *   }
 *   // room_ids
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "room_id": 123,
 *         "count": 10,
 *         "valuable_count": 0
 *       },
 *       {
 *         "room_id": 456,
 *         "count": 12,
 *         "valuable_count": 12
 *       }
 *     ]
 *   }
 *
 */
func ActionOnlineCount(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID  int64   `json:"room_id"`
		RoomIDs []int64 `json:"room_ids"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID != 0 {
		param.RoomIDs = append(param.RoomIDs, param.RoomID)
	}
	if len(param.RoomIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	info := make([]OnlineNumberPair, 0, len(param.RoomIDs))
	pipe := service.IMRedis.Pipeline()
	for i := range param.RoomIDs {
		p := OnlineNumberPair{
			RoomID:           param.RoomIDs[i],
			countCmd:         pipe.ZCard(keys.KeyIMRoomMembers1.Format(param.RoomIDs[i])),
			valuableCountCmd: pipe.SCard(keys.IMKeyRoomValuableUsers1.Format(param.RoomIDs[i])),
		}
		info = append(info, p)
	}
	_, err = pipe.Exec()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range info {
		info[i].Count = info[i].countCmd.Val()
		info[i].ValuableCount = info[i].valuableCountCmd.Val()
	}
	if param.RoomID != 0 {
		return info[0], nil
	}
	return info, nil
}
