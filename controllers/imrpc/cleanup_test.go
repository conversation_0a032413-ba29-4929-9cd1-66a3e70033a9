package imrpc

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCleanup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewRPCTestContext("cleanup", handler.M{"room_id": "a"})
	_, err := ActionCleanup(c)
	assert.Equal(actionerrors.ErrParams, err)

	roomID := int64(room.TestExistsRoomID)
	now := goutil.TimeNow()
	membersKey := keys.KeyIMRoomMembers1.Format(roomID)
	usersKey := keys.KeyIMRoomUsers1.Format(roomID)
	vuKey := keys.IMKeyRoomValuableUsers1.Format(roomID)
	init := func() {
		pipe := service.IMRedis.TxPipeline()
		pipe.Del(membersKey, usersKey, vuKey)
		pipe.SAdd(vuKey, 12)
		pipe.ZAdd(membersKey,
			&redis.Z{Score: float64(now.Unix() - 3600 - 1), Member: "12|test1|0"},
			&redis.Z{Score: float64(now.Unix()), Member: "12|test2|0"},
			&redis.Z{Score: float64(now.Unix()), Member: "11|test1|0"},
			&redis.Z{Score: float64(now.Unix()), Member: "11|test2|1"},
		)
		pipe.HSet(usersKey, 12, 10, 11, 4)
		pipe.Expire(membersKey, time.Second)
		pipe.Expire(usersKey, time.Second)
		_, err := pipe.Exec()
		require.NoError(err)
	}
	check := func(msg string) {
		err := service.IMRedis.ZScore(membersKey, "12|test1|0").Err()
		assert.True(serviceredis.IsRedisNil(err), msg)
		score, err := service.IMRedis.ZScore(membersKey, "12|test2|0").Result()
		require.NoError(err)
		assert.Equal(now.Unix(), int64(score), msg)
		v, err := service.IMRedis.HGet(usersKey, "12").Result()
		require.NoError(err, msg)
		assert.Equal("1", v, msg)
		v, err = service.IMRedis.HGet(usersKey, "11").Result()
		require.NoError(err, msg)
		assert.Equal("2", v, msg)
		ok, err := service.IMRedis.SIsMember(vuKey, 11).Result()
		require.NoError(err)
		assert.True(ok)
	}

	init()
	c = handler.NewRPCTestContext("cleanup", handler.M{})
	r, err := ActionCleanup(c)
	require.NoError(err)
	assert.Equal("清理成功", r)
	check("无参数的情况")

	init()
	c = handler.NewRPCTestContext("cleanup", handler.M{"room_id": roomID})
	r, err = ActionCleanup(c)
	require.NoError(err)
	assert.Equal("清理成功", r)
	check("room_id 情况")

	init()
	c = handler.NewRPCTestContext("cleanup", handler.M{"room_ids": []int64{roomID}})
	r, err = ActionCleanup(c)
	require.NoError(err)
	assert.Equal("清理成功", r)
	check("room_ids 情况")
}

func TestCleanUpParam_findRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	membersKeys := []string{
		keys.KeyIMRoomMembers1.Format(room.TestExistsRoomID),
		keys.KeyIMRoomMembers1.Format(room.TestLimitedRoomID),
	}
	pipe := service.IMRedis.TxPipeline()
	for _, key := range membersKeys {
		pipe.ZAdd(key, &redis.Z{Score: 1, Member: "12|test1|0"})
		pipe.Expire(key, 10*time.Second)
	}
	_, err := pipe.Exec()
	require.NoError(err)

	var param cleanUpParam
	require.NoError(param.findRoomIDs(1))
	assert.Contains(param.RoomIDs, room.TestExistsRoomID)
	assert.Contains(param.RoomIDs, room.TestLimitedRoomID)
}
