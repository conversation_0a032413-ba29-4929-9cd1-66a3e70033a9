package imrpc

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// OnlineStatusResp ActionUserStatus 响应
type OnlineStatusResp struct {
	RoomID     int64        `json:"room_id"`
	UserStatus []UserStatus `json:"userstatus"`
}

// UserStatus user status
type UserStatus struct {
	UserID int64 `json:"user_id"`
	Online bool  `json:"online"`
}

// ActionUserStatus 返回注册用户在线状态
func ActionUserStatus(c *handler.Context) (handler.ActionResponse, error) {
	var receive struct {
		RoomID  int64   `json:"room_id"`
		UserIDs []int64 `json:"user_ids"`
	}
	err := c.BindJSON(&receive)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	userstatus := make([]UserStatus, 0, len(receive.UserIDs))
	for i := 0; i < len(receive.UserIDs); i++ {
		status := UserStatus{UserID: receive.UserIDs[i]}
		status.Online, err = userInRoom(receive.RoomID, receive.UserIDs[i])
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		userstatus = append(userstatus, status)
	}

	return OnlineStatusResp{RoomID: receive.RoomID, UserStatus: userstatus}, nil
}

// userInRoom 判断用户是否在房间中，返回的错误只可能是 redis 的错误
func userInRoom(roomID int64, userID int64) (bool, error) {
	key := keys.KeyIMRoomUsers1.Format(roomID)
	s, err := service.IMRedis.HGet(key, strconv.FormatInt(userID, 10)).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return false, nil
		}
		logger.Error(err)
		return false, err
	}
	count, _ := strconv.ParseInt(s, 10, 64)
	return count > 0, nil
}
