package imrpc

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/broadcast", false, nil)
	_, err := ActionBroadcast(c)
	assert.Equal(actionerrors.ErrParams, err)
	param := map[string]interface{}{
		"method":  "room",
		"room_id": -1,
		"payload": struct{}{},
	}
	c = handler.NewTestContext(http.MethodPost, "/broadcast", false, param)
	_, err = ActionBroadcast(c)
	assert.Equal(actionerrors.ErrParams, err)
	watcher := tutil.NewPubSub(
		service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)),
		100*time.Millisecond,
	)
	defer watcher.Close()
	time.Sleep(100 * time.Millisecond)
	param = map[string]interface{}{
		"room_id":  112,
		"method":   "room",
		"priority": 1,
		"payload":  handler.M{"test": 1},
	}
	c = handler.NewTestContext(http.MethodPost, "/broadcast", false, param)
	_, err = ActionBroadcast(c)
	require.NoError(err)
	assert.True(func() bool {
		var expected map[string]interface{}
		require.NoError(json.Unmarshal([]byte(`{"type":0,"room_id":112,"priority":1,"payload":{"test":1}}`), &expected))
		vals := watcher.ReceiveMany(2)
		for i := range vals {
			var actual map[string]interface{}
			_ = json.Unmarshal([]byte(vals[i]), &actual)
			if reflect.DeepEqual(expected, actual) {
				return true
			}
		}
		return false
	}())
}

func TestActionBroadcastAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ActionBroadcastAll(handler.NewTestContext(http.MethodPost, "/broadcast/all", false, nil))
	assert.Equal(actionerrors.ErrParams, err)

	watcher := tutil.NewPubSub(
		service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)),
		500*time.Millisecond,
	)
	defer watcher.Close()
	time.Sleep(100 * time.Millisecond)
	param := map[string]interface{}{"payload": json.RawMessage(`{"notify":1}`)}
	_, err = ActionBroadcastAll(handler.NewTestContext(http.MethodPost, "/broadcast/all", false, param))
	require.NoError(err)
	time.Sleep(100 * time.Millisecond)
	param = map[string]interface{}{"priority": 1, "payload": handler.M{"room_id": room.OpenListExcludeRoomIDs()[0]}}
	_, err = ActionBroadcastAll(handler.NewTestContext(http.MethodPost, "/broadcast/all", false, param))
	require.NoError(err)
	assert.True(func() bool {
		var equal1 map[string]interface{}
		require.NoError(json.Unmarshal([]byte(`{"type":1,"payload":{"notify":1}}`), &equal1))

		var equal2 map[string]interface{}
		require.NoError(json.Unmarshal([]byte(`{"type":0,"room_id":10652247,"priority":1,"payload":{"room_id":10652247}}`), &equal2))
		vals := watcher.ReceiveMany(3)
		status := 0
		for i := range vals {
			logger.Debug(vals[i])
			var actual map[string]interface{}
			_ = json.Unmarshal([]byte(vals[i]), &actual)
			if reflect.DeepEqual(equal1, actual) {
				status++
			}
			if reflect.DeepEqual(equal2, actual) {
				status++
			}
		}
		return status == 2
	}())
}

func TestActionBroadcastUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := map[string]interface{}{
		"room_id": -1,
		"payload": struct{}{},
	}
	c := handler.NewTestContext(http.MethodPost, "/broadcast", false, param)
	_, err := ActionBroadcastUser(c)
	assert.Equal(actionerrors.ErrParams, err)

	watcher := tutil.NewPubSub(
		service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)),
		100*time.Millisecond,
	)
	defer watcher.Close()
	param = map[string]interface{}{
		"room_id": 112,
		"user_id": 112,
		"method":  "room",
		"payload": handler.M{"test": 1},
	}
	c = handler.NewTestContext(http.MethodPost, "/broadcast/user", false, param)
	_, err = ActionBroadcastUser(c)
	require.NoError(err)
	assert.NoError(watcher.ReceiveUntil(func(val string) (bool, error) {
		var expected map[string]interface{}
		require.NoError(json.Unmarshal([]byte(`{"type":0,"room_id":112,"user_id":112,"payload":{"test":1}}`), &expected))
		var actual map[string]interface{}
		_ = json.Unmarshal([]byte(val), &actual)
		return reflect.DeepEqual(expected, actual), nil
	}))
}

func TestBroadcastCheckNotifyType(t *testing.T) {
	assert := assert.New(t)

	param := broadcastParam{
		RoomID: 1,
	}
	assert.Equal(actionerrors.ErrParams, param.checkNotifyType(), "payload 为空")

	param.Payload = json.RawMessage("{}")
	assert.NoError(param.checkNotifyType())
	param.RoomID = 0
	assert.Equal(actionerrors.ErrParams, param.checkNotifyType(), "room_id 为空")

	param.RoomID = 1
	assert.NoError(param.checkNotifyType())
	param.UserID = -1
	assert.Equal(actionerrors.ErrParams, param.checkNotifyType(), "user_id 错误")

	param.RoomID = 0
	param.UserID = 0
	param.Type = liveim.IMMessageTypeAll
	assert.NoError(param.checkNotifyType())
	assert.Zero(param.RoomID)
	assert.Zero(param.UserID)

	// 全局广播退化成单个房间
	param.Payload = json.RawMessage(fmt.Sprintf(`{"room_id":%d}`, room.OpenListExcludeRoomIDs()[0]))
	assert.NoError(param.checkNotifyType())
	assert.Equal(room.OpenListExcludeRoomIDs()[0], param.RoomID)
	assert.Zero(param.UserID)
	// type 错误
	param.Type = -1
	assert.Equal(actionerrors.ErrParams, param.checkNotifyType())
}

func TestActionActivityBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/activity/broadcast", false, nil)
	_, err := ActionActivityBroadcast(c)
	assert.Equal(actionerrors.ErrParams, err)
	param := map[string]interface{}{
		"method":  "room",
		"room_id": -1,
		"payload": struct{}{},
	}
	c = handler.NewTestContext(http.MethodPost, "/activity/broadcast", false, param)
	_, err = ActionActivityBroadcast(c)
	assert.Equal(actionerrors.ErrParams, err)
	watcher := tutil.NewPubSub(
		service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)),
		100*time.Millisecond,
	)
	defer watcher.Close()

	params := broadcastParam{
		RoomID:  12345,
		Method:  "room",
		Payload: json.RawMessage(`{"test":1}`),
	}
	c = handler.NewTestContext(http.MethodPost, "/activity/broadcast", false, params)
	_, err = ActionActivityBroadcast(c)
	require.NoError(err)
	val := watcher.Receive()
	logger.Debug(val)
	var receive broadcastParam
	_ = json.Unmarshal([]byte(val), &receive)
	assert.Equal(int64(12345), receive.RoomID)
	assert.Equal(json.RawMessage(`{"test":1}`), receive.Payload)
	assert.Equal(liveim.IMMessageTypeActivity, receive.Type)
}

func TestActionActivityBroadcastAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "activity/broadcast/all", false, nil)
	_, err := ActionActivityBroadcastAll(c)
	assert.Equal(actionerrors.ErrParams, err)
	param := map[string]interface{}{
		"method":  "room",
		"room_id": -1,
		"payload": struct{}{},
	}
	c = handler.NewTestContext(http.MethodPost, "/activity/broadcast/all", false, param)
	_, err = ActionActivityBroadcast(c)
	assert.Equal(actionerrors.ErrParams, err)
	watcher := tutil.NewPubSub(
		service.IMRedis.Subscribe(liveim.KeyIMPubSubByIndex(0)),
		100*time.Millisecond,
	)
	defer watcher.Close()

	params := broadcastParam{
		RoomID:  12345,
		Method:  "room",
		Payload: json.RawMessage(`{"test":1}`),
	}
	c = handler.NewTestContext(http.MethodPost, "/activity/broadcast/all", false, params)
	_, err = ActionActivityBroadcastAll(c)
	require.NoError(err)
	val := watcher.Receive()
	logger.Debug(val)
	var receive broadcastParam
	_ = json.Unmarshal([]byte(val), &receive)
	assert.Equal(json.RawMessage(`{"test":1}`), receive.Payload)
	assert.Equal(liveim.IMMessageTypeActivityAll, receive.Type)
}

func TestActionBroadcastMany(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "", false, "123")
	_, err := ActionBroadcastMany(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext(http.MethodPost, "", false, []broadcastParam{
		{Type: liveim.IMMessageTypeNormal, RoomID: 1, Payload: json.RawMessage("{}")},
		{Type: liveim.IMMessageTypeAll, RoomID: 1, Payload: json.RawMessage("{}")},
		{Type: liveim.IMMessageTypeActivity, RoomID: 1, Payload: json.RawMessage("{}")},
		{Type: liveim.IMMessageTypeActivityAll, RoomID: 1, Payload: json.RawMessage("{}")},
		{Type: -1, RoomID: 1, Payload: json.RawMessage("{}")},
	})
	_, err = ActionBroadcastMany(c)
	assert.NoError(err)
}
