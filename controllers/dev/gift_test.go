package dev

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGiftTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(giftUpload{}, "gift_id", "icon_url", "web_icon_url",
		"icon_active_url", "web_icon_active_url",
		"effect_url", "web_effect_url",
		"combo_effect_url", "web_combo_effect_url",
		"lucky_effect_url", "web_lucky_effect_url",
	)
}

func TestActionGiftUpload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/gift/upload", true, handler.M{"gift_id": "test"})
	_, err := ActionGiftUpload(c)
	assert.Equal(actionerrors.ErrParams, err)

	gu := giftUpload{
		GiftID:     999,
		IconURL:    []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		WebIconURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},

		IconActiveURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		// 特殊情况单独测试
		// WebIconActiveURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},

		EffectURL:    []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		WebEffectURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},

		ComboEffectURL:    []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		WebComboEffectURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},

		LuckyEffectURL:    []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		WebLuckyEffectURL: []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
	}
	c = handler.NewTestContext("POST", "/gift/upload", true, gu)
	_, err = ActionGiftUpload(c)
	assert.EqualError(err, "无法找到该礼物")

	gu.GiftID = 1
	c = handler.NewTestContext("POST", "/gift/upload", true, gu)
	_, err = ActionGiftUpload(c)
	assert.NoError(err)

	// 查看是否上传成功
	r, err := http.Get("http://static-test.missevan.com/live/gifts/luckyeffects/001.jpg")
	require.NoError(err)
	assert.Equal(http.StatusOK, r.StatusCode)
}

func TestGiftUploadWebIconActive(t *testing.T) {
	assert := assert.New(t)

	gu := giftUpload{
		GiftID:   999,
		adminlog: goclient.NewAdminLogBox(goutil.SmartUserContext{Req: httptest.NewRequest("POST", "/", nil)}),
	}
	assert.NoError(gu.uploadWebIconActive())
	gu.WebIconActiveURL = []upload.SourceURL{
		"https://fm.example.com/testdata/test.webp",
	}
	assert.EqualError(gu.uploadWebIconActive(), "web_icon_active 至少需要 WebP 和 APNG 两张动图")
	gu.WebIconActiveURL = []upload.SourceURL{
		"https://fm.example.com/testdata/test.webp",
		"https://fm.example.com/testdata/test.png",
	}
	assert.NoError(gu.uploadWebIconActive())
}
