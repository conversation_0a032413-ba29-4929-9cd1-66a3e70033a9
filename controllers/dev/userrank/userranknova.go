package userrank

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	rooms "github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionRankNovaAdd 添加符合新人榜资格用户
/**
 * @api {post} /api/v2/dev/user/rank/nova/add 添加符合新人榜资格用户
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionRankNovaAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID int64 `form:"user_id" json:"user_id"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	roomID, err := rooms.FindRoomID(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomID <= 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}

	err = service.Redis.SAdd(
		usersrank.NovaKey(goutil.TimeNow(), usersrank.NovaKeySlot(param.UserID)), param.UserID).Err()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	adminLog := goclient.NewAdminLogBox(c)
	adminLog.Add(userapi.CatalogManageUserRank, fmt.Sprintf("添加用户 %d 的新人榜资格", param.UserID))
	err = adminLog.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}
