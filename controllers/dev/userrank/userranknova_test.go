package userrank

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)
	m.Run()
}

func TestActionRankNovaAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(10)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	require.NoError(service.Redis.Del(usersrank.NovaKey(now, usersrank.NovaKeySlot(testUserID))).Err())

	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/user/rank/nova/add", true, handler.M{
		"user_id": testUserID,
	})
	resp, err := ActionRankNovaAdd(c)
	require.NoError(err)
	assert.Equal("success", resp)

	exists, err := service.Redis.SIsMember(usersrank.NovaKey(now, usersrank.NovaKeySlot(testUserID)), testUserID).Result()
	require.NoError(err)
	assert.True(exists)
}
