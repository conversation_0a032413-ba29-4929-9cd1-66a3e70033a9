package superfan

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestActionSetExpire(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRoomID := int64(22489473)
	testUserID := int64(9074509)
	testCreatorID := int64(10)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx,
		bson.M{"room_id": testRoomID, "user_id": testUserID})
	require.NoError(err)

	err = service.LiveDB.Where("buyer_id IN (?)", testRoomID).Delete(&livetxnorder.LiveTxnOrder{}).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, nil)
	_, err = ActionSetExpire(c)
	assert.Equal(actionerrors.ErrParams, err)

	s := livemedal.Simple{RoomID: testRoomID, UserID: testUserID}
	s.SuperFan = &livemedal.SuperFan{
		ExpireTime: util.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	_, err = livemedal.Collection().InsertOne(ctx, s)
	require.NoError(err)

	now := util.TimeNow()
	lto := &livetxnorder.LiveTxnOrder{
		BuyerID:    testUserID,
		SellerID:   testCreatorID,
		GoodsType:  livegoods.GoodsTypeSuperFan,
		Status:     livetxnorder.StatusSuccess,
		ExpireTime: now.AddDate(0, 0, 1).Unix(),
	}
	err = service.LiveDB.Create(lto).Error
	require.NoError(err)

	body := handler.M{"room_id": testRoomID, "user_ids": "9074509"}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, body)
	resp, err := ActionSetExpire(c)
	require.NoError(err)
	assert.Equal("success", resp)

	var superFanOrder livetxnorder.LiveTxnOrder
	err = service.LiveDB.Table(livetxnorder.LiveTxnOrder{}.TableName()).
		Take(&superFanOrder, "buyer_id = ? AND seller_id = ?", testUserID, testCreatorID).Error
	require.NoError(err)
	assert.LessOrEqual(superFanOrder.ExpireTime, now.Unix())
}

func TestNewSuperFanSetExpireParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRoomID := int64(22489473)
	testUserID := int64(10010)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteOne(ctx,
		bson.M{"room_id": testRoomID, "user_id": testUserID})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, nil)
	_, err = newSuperFanSetExpireParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body := handler.M{"room_id": 12345}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, body)
	_, err = newSuperFanSetExpireParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["user_ids"] = "10010,10011"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, body)
	_, err = newSuperFanSetExpireParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	body["room_id"] = testRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, body)
	_, err = newSuperFanSetExpireParam(c)
	assert.EqualError(err, "当前房间没有找到可以设置过期的用户数据")

	s := livemedal.Simple{RoomID: testRoomID, UserID: testUserID}
	s.SuperFan = &livemedal.SuperFan{
		ExpireTime: util.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	_, err = livemedal.Collection().InsertOne(ctx, s)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/superfan/set-expire", true, body)
	param, err := newSuperFanSetExpireParam(c)
	require.NoError(err)
	assert.EqualValues(10, param.creatorID)
	assert.Equal(1, len(param.userIDs))
}

func TestSuperFanSetExpireParam_setExpire(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRoomID := int64(10110102)
	testUserID := int64(9074509)
	testCreatorID := int64(9074510)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx,
		bson.M{"room_id": testRoomID, "user_id": testUserID})
	require.NoError(err)

	err = service.LiveDB.Where("buyer_id IN (?)", testRoomID).Delete(&livetxnorder.LiveTxnOrder{}).Error
	require.NoError(err)

	s := livemedal.Simple{RoomID: testRoomID, UserID: testUserID}
	s.SuperFan = &livemedal.SuperFan{
		ExpireTime: util.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	_, err = livemedal.Collection().InsertOne(ctx, s)
	require.NoError(err)

	now := util.TimeNow()
	lto := &livetxnorder.LiveTxnOrder{
		BuyerID:    testUserID,
		SellerID:   testCreatorID,
		GoodsType:  livegoods.GoodsTypeSuperFan,
		Status:     livetxnorder.StatusSuccess,
		ExpireTime: now.AddDate(0, 0, 1).Unix(),
	}
	err = service.LiveDB.Create(lto).Error
	require.NoError(err)

	param := superFanSetExpireParam{
		RoomID:    testRoomID,
		userIDs:   []int64{testUserID},
		creatorID: testCreatorID,
	}
	err = param.setExpire()
	require.NoError(err)
	assert.False(livemedal.IsRoomSuperFan(testRoomID, testUserID))

	var superFanOrder livetxnorder.LiveTxnOrder
	err = service.LiveDB.Table(livetxnorder.LiveTxnOrder{}.TableName()).
		Take(&superFanOrder, "buyer_id = ? AND seller_id = ?", testUserID, testCreatorID).Error
	require.NoError(err)
	assert.LessOrEqual(superFanOrder.ExpireTime, now.Unix())
}
