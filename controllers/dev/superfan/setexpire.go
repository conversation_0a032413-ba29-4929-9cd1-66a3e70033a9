package superfan

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type superFanSetExpireParam struct {
	UserIDs string `json:"user_ids"`
	RoomID  int64  `json:"room_id"`

	userIDs   []int64
	creatorID int64
}

// ActionSetExpire 设置超粉过期
/**
 * @api {post} /api/v2/dev/superfan/set-expire 设置超粉过期
 *
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiParam {String} user_ids 用户 ID, 批量修改使用半角逗号分割, 如: 123,234,456
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionSetExpire(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSuperFanSetExpireParam(c)
	if err != nil {
		return nil, err
	}
	if err = param.setExpire(); err != nil {
		return nil, err
	}

	return "success", nil
}

func newSuperFanSetExpireParam(c *handler.Context) (*superFanSetExpireParam, error) {
	var param superFanSetExpireParam
	err := c.BindJSON(&param)
	if err != nil || param.UserIDs == "" || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	userIDs, err := util.SplitToInt64Array(param.UserIDs, ",")
	if err != nil || len(userIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	param.creatorID, err = room.FindCreatorID(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.creatorID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}

	userSuperFanMap, err := livemedal.FindRoomSuperFanMap(param.RoomID, userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userSuperFanMap) == 0 {
		return nil, actionerrors.ErrNotFound("当前房间没有找到可以设置过期的用户数据")
	}
	param.userIDs = make([]int64, 0, len(userSuperFanMap))
	for userID := range userSuperFanMap {
		param.userIDs = append(param.userIDs, userID)
	}

	return &param, nil
}

func (s *superFanSetExpireParam) setExpire() error {
	err := livemedal.SetSuperFanExpired(s.RoomID, s.userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	err = livetxnorder.SetSuperFanOrderExpired(s.userIDs, s.creatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}
