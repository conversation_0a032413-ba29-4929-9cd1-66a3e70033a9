package dev

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
)

type giftUpload struct {
	GiftID int64 `json:"gift_id"`

	IconURL          []upload.SourceURL `json:"icon_url"`
	WebIconURL       []upload.SourceURL `json:"web_icon_url"`
	IconActiveURL    []upload.SourceURL `json:"icon_active_url"`
	WebIconActiveURL []upload.SourceURL `json:"web_icon_active_url"`

	EffectURL    []upload.SourceURL `json:"effect_url"`
	WebEffectURL []upload.SourceURL `json:"web_effect_url"`

	ComboEffectURL    []upload.SourceURL `json:"combo_effect_url"`
	WebComboEffectURL []upload.SourceURL `json:"web_combo_effect_url"`

	LuckyEffectURL    []upload.SourceURL `json:"lucky_effect_url"`
	WebLuckyEffectURL []upload.SourceURL `json:"web_lucky_effect_url"`

	giftIDStr string
	adminlog  *goclient.AdminLogBox
}

func (gu *giftUpload) sendAdminLog() {
	err := gu.adminlog.Send()
	if err != nil {
		logger.Error(err)
		return
	}
}

func (gu *giftUpload) uploadIcon() error {
	for _, source := range gu.IconURL {
		target :=
			fmt.Sprintf("%sicons/%s%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadWebIcon() error {
	for _, source := range gu.WebIconURL {
		target :=
			fmt.Sprintf("%sicons/%s-web%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadIconActive() error {
	for _, source := range gu.IconActiveURL {
		target :=
			fmt.Sprintf("%sicons/active/%s%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadWebIconActive() error {
	if len(gu.WebIconActiveURL) == 0 {
		return nil
	}
	mustHave := map[string]struct{}{".webp": {}, ".png": {}}
	for _, source := range gu.WebIconActiveURL {
		delete(mustHave, source.Ext())
		if len(mustHave) == 0 {
			break
		}
	}
	if len(mustHave) != 0 {
		return actionerrors.ErrParamsMsg("web_icon_active 至少需要 WebP 和 APNG 两张动图")
	}
	for _, source := range gu.WebIconActiveURL {
		target :=
			fmt.Sprintf("%sicons/active/%s-web%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadEffect() error {
	for _, source := range gu.EffectURL {
		target :=
			fmt.Sprintf("%seffects/%s%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadWebEffect() error {
	for _, source := range gu.WebEffectURL {
		target :=
			fmt.Sprintf("%seffects/%s-web%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadComboEffect() error {
	for _, source := range gu.ComboEffectURL {
		target :=
			fmt.Sprintf("%scomboeffects/%s%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadWebComboEffect() error {
	for _, source := range gu.WebComboEffectURL {
		target :=
			fmt.Sprintf("%scomboeffects/%s-web%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadLuckyEffect() error {
	for _, source := range gu.LuckyEffectURL {
		target :=
			fmt.Sprintf("%sluckyeffects/%s%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

func (gu *giftUpload) uploadWebLuckyEffect() error {
	for _, source := range gu.WebLuckyEffectURL {
		target :=
			fmt.Sprintf("%sluckyeffects/%s-web%s", storage.PathPrefixGift, gu.giftIDStr, source.Ext())
		err := storage.UploadToTarget(source, target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gu.adminlog.Add(userapi.CatalogUploadToTarget,
			fmt.Sprintf("上传礼物资源文件到：%s://%s", config.DefaultCDNScheme, target))
	}
	return nil
}

// ActionGiftUpload 对指定礼物 ID 上传资源文件，每个字段只支持单个文件
// TODO: 目前不支持跟礼物 ID 不绑定的资源文件
/**
 * @api {post} /api/v2/dev/upload/gift 上传礼物资源文件
 * @apiDescription 对指定礼物 ID 上传资源文件，每个字段支持多个文件，未对文件格式做限制\
 *   不能对不存在的礼物上传资源文件
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {String} [icon_url] App 图标
 * @apiParam {String} [web_icon_url] Web 图标
 * @apiParam {String} [icon_active_url] App 激活图标
 * @apiParam {String} [web_icon_active_url] Web 激活图标，这个必须要每次传两个文件，一个 webp, 一个 png(apng 格式)
 * @apiParam {String} [effect_url] App 礼物特效
 * @apiParam {String} [web_effect_url] Web 礼物特效
 * @apiParam {String} [combo_effect_url] App 连击特效
 * @apiParam {String} [web_combo_effect_url] Web 连击特效
 * @apiParam {String} [lucky_effect_url] App 随机礼物开箱特效
 * @apiParam {String} [web_lucky_effect_url] Web 随机礼物开箱特效
 *
 * @apiParamExample {json} 更新图标:
 *   {
 *     "gift_id": 1,
 *     "icon_url": ["http://001.webp"]
 *     "web_icon_url": ["http://001-web.webp"],
 *     "icon_active_url": ["http://001-active.webp"],
 *     "web_icon_active_url": ["http://001-active-web.webp", "http://001-active-web.png"] // 此字段必须传两个
 *   }
 *
 * @apiParamExample {json} 更新特效:
 *   {
 *     "gift_id": 1,
 *     "effect_url": ["http://001.webp", "http://001.webm", "http://001.png"]
 *     "web_effect_url": ["http://001-web.webp", "http://001-web.webm", "http://001-web.png"]
 *   }
 *
 * @apiParamExample {json} 更新连击特效:
 *   {
 *     "gift_id": 1,
 *     "combo_effect_url": ["http://001.svga"]
 *     "web_combo_effect_url": ["http://001-web.svga"]
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "新建成功"
 *   }
 */
func ActionGiftUpload(c *handler.Context) (handler.ActionResponse, error) {
	var gu giftUpload
	err := c.BindJSON(&gu)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	g, err := gift.FindGiftMapByGiftIDs([]int64{gu.GiftID})
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if g[gu.GiftID] == nil {
		return nil, actionerrors.ErrNotFound("无法找到该礼物")
	}
	gu.giftIDStr = fmt.Sprintf("%03d", gu.GiftID)
	gu.adminlog = goclient.NewAdminLogBox(c)
	defer gu.sendAdminLog()

	err = gu.uploadIcon()
	if err != nil {
		return nil, err
	}
	err = gu.uploadWebIcon()
	if err != nil {
		return nil, err
	}

	err = gu.uploadIconActive()
	if err != nil {
		return nil, err
	}
	err = gu.uploadWebIconActive()
	if err != nil {
		return nil, err
	}

	err = gu.uploadEffect()
	if err != nil {
		return nil, err
	}
	err = gu.uploadWebEffect()
	if err != nil {
		return nil, err
	}

	err = gu.uploadComboEffect()
	if err != nil {
		return nil, err
	}
	err = gu.uploadWebComboEffect()
	if err != nil {
		return nil, err
	}

	err = gu.uploadLuckyEffect()
	if err != nil {
		return nil, err
	}
	err = gu.uploadWebLuckyEffect()
	if err != nil {
		return nil, err
	}

	return "上传成功", nil
}
