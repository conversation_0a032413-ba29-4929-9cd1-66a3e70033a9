package dev

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("dev", h.Name)
	assert.Len(h.Middlewares, 1)
	assert.Len(h.SubHandlers, 3)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "upload/gift", "flush-cache", "databus/send")
}

func TestRankHandler(t *testing.T) {
	assert := assert.New(t)

	h := rankHandler()
	assert.Equal("rank", h.Name)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "nova/add")
}

func TestVipHandler(t *testing.T) {
	assert := assert.New(t)

	h := vipHandler()
	assert.Equal("vip", h.Name)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "set-expire")
}

func TestSuperFanHandler(t *testing.T) {
	assert := assert.New(t)

	h := superFanHandler()
	assert.Equal("superfan", h.Name)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "set-expire")
}
