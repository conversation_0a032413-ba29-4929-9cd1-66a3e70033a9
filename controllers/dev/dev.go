package dev

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/controllers/dev/superfan"
	"github.com/MiaoSiLa/live-service/controllers/dev/userrank"
	"github.com/MiaoSiLa/live-service/controllers/dev/vip"
	"github.com/MiaoSiLa/live-service/middlewares"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

// RoleDeveloperBackend 用户角色 developer_backend
// TODO: 迁移至 missevan-go models/role/role.go
const RoleDeveloperBackend = "developer_backend"

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name:        "dev",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.Developer, RoleDeveloperBackend)},
		SubHandlers: []handler.Handler{
			rankHandler(),
			vipHandler(),
			superFanHandler(),
		},
		Actions: map[string]*handler.Action{
			"upload/gift":  handler.NewAction(handler.POST, ActionGiftUpload, true),
			"flush-cache":  handler.NewAction(handler.GET, ActionFlushCache, true),
			"databus/send": handler.NewAction(handler.POST, ActionDatabusSend, true),
		},
	}
}

func rankHandler() handler.Handler {
	return handler.Handler{
		Name: "rank",
		Actions: map[string]*handler.Action{
			"nova/add": handler.NewAction(handler.POST, userrank.ActionRankNovaAdd, true),
		},
	}
}

func vipHandler() handler.Handler {
	return handler.Handler{
		Name: "vip",
		Actions: map[string]*handler.Action{
			"set-expire": handler.NewAction(handler.POST, vip.ActionSetExpire, true),
		},
	}
}

func superFanHandler() handler.Handler {
	return handler.Handler{
		Name: "superfan",
		Actions: map[string]*handler.Action{
			"set-expire": handler.NewAction(handler.POST, superfan.ActionSetExpire, true),
		},
	}
}
