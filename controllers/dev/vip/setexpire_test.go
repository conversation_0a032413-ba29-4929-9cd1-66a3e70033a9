package vip

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestActionSetExpire(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/vip/set-expire", true, nil)
	_, err := ActionSetExpire(c)
	require.Equal(actionerrors.ErrParams, err)

	testUserIDs := []int64{10010, 10011}
	testVipTypes := []int{vip.TypeLiveNoble, vip.TypeLiveHighness}

	now := util.TimeNow().Unix()
	defer mrpc.SetMock(vip.URLVipSetExpire, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userIDs, ok := body["user_ids"].([]int64)
		require.True(ok)
		require.Equal(testUserIDs, userIDs)
		vipTypes, ok := body["vip_types"].([]int)
		require.True(ok)
		require.Equal(testVipTypes, vipTypes)
		return vip.SetExpireResp{
			UserIDs:    testUserIDs,
			ExpireTime: now,
		}, nil
	})()

	defer mrpc.SetMock(vip.URLLiveUserLevel, func(input interface{}) (output interface{}, err error) {
		resp := vip.LiveUsersLevelResp{
			Data: []*vip.UserVip{
				{
					UserID:     testUserIDs[1],
					VipID:      vip.HighnessVipID,
					Level:      vip.HighnessLevel,
					Type:       vip.TypeLiveHighness,
					ExpireTime: now,
				},
			},
		}
		return resp, nil
	})()

	body := handler.M{
		"user_ids":  "10010, 10011",
		"vip_types": testVipTypes,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/vip/set-expire", true, body)
	resp, err := ActionSetExpire(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestNewVipSetExpireParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/vip/set-expire", true, nil)
	_, err := newVipSetExpireParam(c)
	require.Equal(actionerrors.ErrParams, err)

	testVipTypes := []int{vip.TypeLiveNoble, vip.TypeLiveHighness}
	body := handler.M{"user_ids": "10010, 10011"}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/vip/set-expire", true, body)
	_, err = newVipSetExpireParam(c)
	require.Equal(actionerrors.ErrParams, err)

	body["vip_types"] = testVipTypes
	c = handler.NewTestContext(http.MethodPost, "/api/v2/dev/vip/set-expire", true, body)
	param, err := newVipSetExpireParam(c)
	require.NoError(err)
	assert.NotEmpty(param)
	assert.Equal(2, len(param.userIDs))
}

func TestVipSetExpireParam_setExpire(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserIDs := []int64{10010, 10011}
	testVipTypes := []int{vip.TypeLiveNoble, vip.TypeLiveHighness}
	now := util.TimeNow().Unix()
	defer mrpc.SetMock(vip.URLVipSetExpire, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userIDs, ok := body["user_ids"].([]int64)
		require.True(ok)
		require.Equal(testUserIDs, userIDs)
		vipTypes, ok := body["vip_types"].([]int)
		require.True(ok)
		require.Equal(testVipTypes, vipTypes)
		return vip.SetExpireResp{
			UserIDs:    testUserIDs,
			ExpireTime: now,
		}, nil
	})()

	param := vipSetExpireParam{
		userIDs:  testUserIDs,
		VipTypes: testVipTypes,
	}
	require.NoError(param.setExpire())
	assert.Equal(testUserIDs, param.userIDs)
}

func TestVipSetExpireParam_clearUserNobleData(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserIDs := []int64{1000010, 1000011, 1000012}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": bson.M{"$in": testUserIDs}}
	_, err := userstatus.UserMetaCollection().DeleteMany(ctx, filter)
	assert.NoError(err)
	_, err = service.MongoDB.Collection("live_users").DeleteMany(ctx, filter)
	assert.NoError(err)

	// 插入测试数据
	// 外观测试数据
	ua1000010 := &userappearance.UserAppearance{
		UserID: testUserIDs[0],
		From:   appearance.FromNoble,
	}
	ua1000011 := &userappearance.UserAppearance{
		UserID: testUserIDs[1],
		From:   appearance.FromHighness,
	}
	ua1000012 := &userappearance.UserAppearance{
		UserID: testUserIDs[2],
		From:   appearance.FromNoble,
	}
	_, err = userappearance.Collection().InsertMany(ctx, []interface{}{ua1000010, ua1000011, ua1000012})
	require.NoError(err)

	// usermeta 测试数据
	usermeta1000010 := &userstatus.GeneralStatus{
		UserID:       testUserIDs[0],
		NobleHornNum: 1,
		RecommendNum: util.NewInt(1),
		Invisible:    util.NewBool(true),
	}
	usermeta1000012 := &userstatus.GeneralStatus{
		UserID:       testUserIDs[2],
		NobleHornNum: 1,
		RecommendNum: util.NewInt(1),
		Invisible:    util.NewBool(true),
	}
	_, err = userstatus.UserMetaCollection().InsertMany(ctx, []interface{}{usermeta1000010, usermeta1000012})
	require.NoError(err)

	// liveuser 测试数据
	type testLiveUsers struct {
		UserID        int64 `bson:"user_id"`
		RankInvisible bool  `bson:"rank_invisible"`
	}
	liveuser1000010 := &testLiveUsers{
		UserID:        testUserIDs[0],
		RankInvisible: true,
	}
	liveuser1000012 := &testLiveUsers{
		UserID:        testUserIDs[2],
		RankInvisible: true,
	}
	_, err = service.MongoDB.Collection("live_users").InsertMany(ctx, []interface{}{liveuser1000010, liveuser1000012})
	require.NoError(err)

	defer mrpc.SetMock(vip.URLLiveUserLevel, func(input interface{}) (output interface{}, err error) {
		resp := vip.LiveUsersLevelResp{
			Data: []*vip.UserVip{
				{
					UserID:     testUserIDs[2],
					VipID:      1,
					Level:      1,
					Type:       vip.TypeLiveNoble,
					ExpireTime: util.TimeNow().Unix(),
				},
			},
		}
		return resp, nil
	})()

	param := vipSetExpireParam{
		userIDs:  testUserIDs,
		VipTypes: []int{vip.TypeLiveNoble, vip.TypeLiveHighness},
	}
	require.NotPanics(func() {
		param.clearUserNobleData()
	})

	filter["from"] = bson.M{"$in": []int{appearance.FromNoble, appearance.FromHighness}}
	count, err := userappearance.Collection().CountDocuments(ctx, filter)
	require.NoError(err)
	assert.Zero(count)

	filter1 := bson.M{
		"user_id":        bson.M{"$in": testUserIDs},
		"noble_horn_num": bson.M{"$gt": 0},
		"recommend_num":  bson.M{"$exists": true},
		"invisible":      bson.M{"$exists": true},
	}
	count, err = userstatus.UserMetaCollection().CountDocuments(ctx, filter1)
	require.NoError(err)
	assert.Zero(count)

	filter2 := bson.M{"user_id": bson.M{"$in": testUserIDs}, "rank_invisible": true}
	count, err = service.MongoDB.Collection("live_users").CountDocuments(ctx, filter2)
	require.NoError(err)
	assert.Zero(count)

	filter3 := bson.M{
		"user_id": bson.M{"$in": testUserIDs},
		"from":    bson.M{"$in": []int{appearance.FromNoble, appearance.FromHighness}},
	}
	count, err = userappearance.Collection().CountDocuments(ctx, filter3)
	require.NoError(err)
	assert.Zero(count)
}
