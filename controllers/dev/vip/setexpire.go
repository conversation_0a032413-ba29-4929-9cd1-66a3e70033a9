package vip

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

type vipSetExpireParam struct {
	UserIDs    string `json:"user_ids"`
	VipTypes   []int  `json:"vip_types"`
	Protection bool   `json:"protection"`

	userIDs []int64
}

// ActionSetExpire 设置贵族过期时间
/**
 * @api {post} /api/v2/dev/vip/set-expire 设置贵族过期时间
 *
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiParam {String} user_ids 用户 ID, 批量修改使用半角逗号分割, 如: 123,234,456
 * @apiParam {Number[]} vip_types 会员类型，1：直播贵族；2：直播上神贵族
 * @apiParam {Boolean} [protection=false] 是否处理至续费保护期的时间，true：更新至保护期时间；false：更新至过期时间；
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionSetExpire(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newVipSetExpireParam(c)
	if err != nil {
		return nil, err
	}
	if err = param.setExpire(); err != nil {
		return nil, err
	}
	// 在续费保护期中，一般情况下相关权益也会失效
	param.clearUserNobleData()
	return "success", nil
}

func newVipSetExpireParam(c *handler.Context) (*vipSetExpireParam, error) {
	var param vipSetExpireParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserIDs == "" || len(param.VipTypes) == 0 {
		return nil, actionerrors.ErrParams
	}
	param.userIDs, err = util.SplitToInt64Array(param.UserIDs, ",")
	if err != nil || len(param.userIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	return &param, nil
}

func (v *vipSetExpireParam) setExpire() error {
	resp, err := vip.SetExpire(v.userIDs, v.VipTypes, v.Protection)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	v.userIDs = resp.UserIDs
	return nil
}

func (v *vipSetExpireParam) clearUserNobleData() {
	// 移除过期的贵族外观
	userappearance.ClearUserVipAppearance(v.userIDs, v.VipTypes)

	// 根据获取设置过期后最新用户未过期的贵族信息，来判断清空哪些贵族权益
	// 如：上神设置过期大咖没有过期，全站喇叭不清空，其他权益要清空
	vipLevel, err := vip.LiveUserLevelByUserIDs(v.userIDs)
	if err != nil {
		logger.Error(err)
		return
	}
	vipLevelMap := util.ToMap(vipLevel, "UserID").(map[int64]*vip.UserVip)
	// 清空贵族权益
	vip.ClearUserVipPrivilege(v.userIDs, vipLevelMap)

	// 删除用户过期贵族缓存
	vip.ClearUserVipCache(v.userIDs...)
}
