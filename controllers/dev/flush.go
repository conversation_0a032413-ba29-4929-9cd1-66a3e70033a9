package dev

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// ActionFlushCache flush cache
// TODO: 清理其他缓存，比如活动缓存
/**
 * @api {get} /api/v2/dev/flush-cache 刷新当前实例的本地缓存
 * @apiDescription 清理本地缓存
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionFlushCache(c *handler.Context) (handler.ActionResponse, error) {
	service.Cache5Min.Flush()
	lruKeys := []string{
		keys.KeyParams1.Format(params.KeySoundRecommend),
		keys.KeyParams1.Format(params.KeyGashapon),
		keys.KeyParams1.Format(params.KeyRank),
		keys.KeyLiveCatalogs1.Format(0),
		keys.KeyLiveCatalogs1.Format(1),
	}
	if len(lruKeys) != 0 {
		err := service.LRURedis.Del(lruKeys...).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err := userapi.FlushCache(c.UserContext())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
