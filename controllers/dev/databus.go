package dev

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionDatabusSend 向消息队列中发送自定义消息
/**
 * @api {post} /api/v2/dev/databus/send 向消息队列中发送自定义消息
 * @apiVersion 0.1.0
 * @apiGroup dev
 *
 * @apiParam {String} key 分区名称
 * @apiParam {Object} value 自定义消息内容
 * @apiParam {Number} [send_time] 发送消息时刻时间戳（单位：秒，发送时间需在 5 分钟之内，延时消息会单独走延时消息的队列），不传此参数会立即向消息队列中发送自定义消息
 *
 * @apiParamExample {json} 向消息队列中发送自定义消息
 *   {
 *      "key": "test_key",
 *      "value": "test_value",
 *      "send_time": 1643010232
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "消息发送成功"
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "延时消息发送成功"
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 501010000,
 *     "info": "参数错误"
 *   }
 */
func ActionDatabusSend(c *handler.Context) (handler.ActionResponse, error) {
	if goutil.IsProdEnv() {
		return nil, actionerrors.NewErrForbidden("线上环境禁用此接口")
	}

	var param struct {
		Key      string          `json:"key"`
		Value    json.RawMessage `json:"value"`
		SendTime int64           `json:"send_time"`
	}

	err := c.BindJSON(&param)
	if err != nil || param.Key == "" || param.Value == nil || param.SendTime < 0 {
		return nil, actionerrors.ErrParams
	}

	if param.SendTime == 0 {
		err = service.DatabusSend(param.Key, param.Value)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		return "消息发送成功", nil
	}

	sendTime := time.Unix(param.SendTime, 0)
	now := goutil.TimeNow()
	if now.After(sendTime) || now.Add(time.Minute*5).Before(sendTime) {
		return nil, actionerrors.NewErrForbidden("延时消息支持最长等待五分钟，以免等待时间过长忘记验收")
	}

	err = service.DatabusSendDelay(param.Key, param.Value, sendTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "延时消息发送成功", nil
}
