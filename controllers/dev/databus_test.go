package dev

import (
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionDatabusSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_key"
	value := "test_value"

	c := handler.NewTestContext("POST", "/databus/send", false, handler.M{
		"key":   key,
		"value": value,
	})

	os.Setenv(util.EnvDeploy, util.DeployEnvProd)
	_, err := ActionDatabusSend(c)
	assert.EqualError(err, "线上环境禁用此接口")

	os.Setenv(util.EnvDeploy, "")
	res, err := ActionDatabusSend(c)
	assert.NoError(err)
	assert.Equal("消息发送成功", res)

	m, ok := <-service.DatabusPub.DebugPubMsgs()
	require.True(ok)
	var str string
	err = json.Unmarshal(m.Value, &str)
	require.NoError(err)
	assert.Equal(key, m.Key)
	assert.Equal(value, str)

	delayMessage := "test_delay_message"
	c = handler.NewTestContext("POST", "/databus/send", false, handler.M{
		"key":       key,
		"value":     delayMessage,
		"send_time": goutil.TimeNow().Add(time.Minute).Unix(),
	})
	res, err = ActionDatabusSend(c)
	assert.Equal("延时消息发送成功", res)
	assert.NoError(err)

	m, ok = <-service.DatabusDelayPub.DebugPubMsgs()
	require.True(ok)
	err = json.Unmarshal(m.Value, &str)
	require.NoError(err)
	assert.Equal(key, m.Key)
	assert.Equal(delayMessage, str)

	c = handler.NewTestContext("POST", "/databus/send", false, handler.M{
		"key": key,
	})
	res, err = ActionDatabusSend(c)
	assert.Equal(err, actionerrors.ErrParams)
	assert.Nil(res)

	c = handler.NewTestContext("POST", "/databus/send", false, handler.M{
		"key":       key,
		"value":     value,
		"send_time": goutil.TimeNow().Add(-time.Minute).Unix(),
	})
	res, err = ActionDatabusSend(c)
	assert.EqualError(err, "延时消息支持最长等待五分钟，以免等待时间过长忘记验收")
	assert.Nil(res)

	c = handler.NewTestContext("POST", "/databus/send", false, handler.M{
		"key":       key,
		"value":     value,
		"send_time": goutil.TimeNow().Add(time.Minute * 6).Unix(),
	})
	res, err = ActionDatabusSend(c)
	assert.EqualError(err, "延时消息支持最长等待五分钟，以免等待时间过长忘记验收")
	assert.Nil(res)
}
