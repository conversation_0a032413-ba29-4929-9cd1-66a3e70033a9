package cron

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSponsorGenerator_GenerateAndInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tests := []struct {
		name     string
		records  []*giftwall.RoomGiftTopUser
		gift     *gift.Gift
		period   *giftwall.Period
		giftWall *params.GiftWall
	}{
		{
			name:    "普通周期达标",
			records: []*giftwall.RoomGiftTopUser{{}},
			gift: &gift.Gift{
				GiftID:    91001,
				Name:      "单测礼物1",
				NameClean: "单测礼物1-91001",
				Price:     100,
			},
			period: &giftwall.Period{
				OID:         primitive.NewObjectID(),
				ShowGiftIDs: []int64{91001},
				Type:        giftwall.PeriodTypeNormal,
			},
			giftWall: &params.GiftWall{
				NormalGiftWall: &params.GiftWallSponsor{SponsorThreshold: 10000},
			},
		},
		{
			name:    "普通周期未达标",
			records: []*giftwall.RoomGiftTopUser{{}},
			gift: &gift.Gift{
				GiftID:    91002,
				Name:      "单测礼物2",
				NameClean: "单测礼物2-91002",
				Price:     100,
			},
			period: &giftwall.Period{
				OID:         primitive.NewObjectID(),
				ShowGiftIDs: []int64{91002},
				Type:        giftwall.PeriodTypeNormal,
			},
			giftWall: &params.GiftWall{
				NormalGiftWall: &params.GiftWallSponsor{SponsorThreshold: 10000},
			},
		},
		{
			name:    "高级周期达标",
			records: []*giftwall.RoomGiftTopUser{{}},
			gift: &gift.Gift{
				GiftID:    91003,
				Name:      "单测礼物3",
				NameClean: "单测礼物3-91003",
				Price:     200,
			},
			period: &giftwall.Period{
				OID:         primitive.NewObjectID(),
				ShowGiftIDs: []int64{91003},
				Type:        giftwall.PeriodTypePremium,
			},
			giftWall: &params.GiftWall{
				PremiumGiftWall: &params.GiftWallSponsor{SponsorThreshold: 20000},
			},
		},
		{
			name:    "高级周期未达标",
			records: []*giftwall.RoomGiftTopUser{{}},
			gift: &gift.Gift{
				GiftID:    91004,
				Name:      "单测礼物4",
				NameClean: "单测礼物4-91004",
				Price:     200,
			},
			period: &giftwall.Period{
				OID:         primitive.NewObjectID(),
				ShowGiftIDs: []int64{91004},
				Type:        giftwall.PeriodTypePremium,
			},
			giftWall: &params.GiftWall{
				PremiumGiftWall: &params.GiftWallSponsor{SponsorThreshold: 20000},
			},
		},
		{
			name:    "礼物不存在",
			records: []*giftwall.RoomGiftTopUser{{}},
			gift:    nil,
			period: &giftwall.Period{
				OID:         primitive.NewObjectID(),
				ShowGiftIDs: []int64{91005},
				Type:        giftwall.PeriodTypeNormal,
			},
			giftWall: &params.GiftWall{
				NormalGiftWall: &params.GiftWallSponsor{SponsorThreshold: 10000},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := service.MongoDB.Context()
			defer cancel()

			// 唯一测试数据
			roomID := int64(80000 + tt.period.OID.Timestamp().Second())
			userID := int64(70000 + tt.period.OID.Timestamp().Second())
			var giftID int64
			if tt.gift != nil {
				giftID = tt.gift.GiftID
			} else {
				giftID = 91005
			}

			// 清理
			_, _ = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"_id": tt.period.OID})
			_, _ = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": giftID})
			_, _ = giftwall.SponsorCollection().DeleteMany(ctx, bson.M{"_period_id": tt.period.OID})

			// 插入 period
			now := goutil.TimeNow()
			startTime := now.Add(-giftwall.PeriodDuration)
			endTime := goutil.BeginningOfWeek(goutil.BeginningOfDay(now)).Unix()
			period := &giftwall.Period{
				OID:          tt.period.OID,
				CreateTime:   now.Unix(),
				ModifiedTime: now.Unix(),
				StartTime:    startTime.Unix(),
				EndTime:      endTime,
				ShowGiftIDs:  tt.period.ShowGiftIDs,
				Type:         tt.period.Type,
			}
			_, err := giftwall.PeriodCollection().InsertOne(ctx, period)
			require.NoError(err)

			// 插入 gift
			if tt.gift != nil {
				_, err = gift.Collection().InsertOne(ctx, tt.gift)
				require.NoError(err)
			}

			// 构造 records
			var activatedNum int64
			if tt.name == "普通周期达标" || tt.name == "高级周期达标" {
				if tt.period.Type == giftwall.PeriodTypeNormal {
					activatedNum = 100 // 100*100=10000
				} else {
					activatedNum = 100 // 100*200=20000
				}
			} else {
				activatedNum = 99 // 不达标
			}
			records := []*giftwall.RoomGiftTopUser{
				{
					PeriodOID:    tt.period.OID,
					RoomID:       roomID,
					UserID:       userID,
					ShowGiftID:   giftID,
					ActivatedNum: activatedNum,
				},
			}

			// 构造 giftMap
			giftMap := make(map[int64]*gift.Gift)
			if tt.gift != nil {
				giftMap[tt.gift.GiftID] = tt.gift
			}

			// 构造 periodMap
			periodMap := map[primitive.ObjectID]*giftwall.Period{
				period.OID: period,
			}

			generator := &SponsorGenerator{
				records:       records,
				giftMap:       giftMap,
				giftWall:      tt.giftWall,
				lastPeriodMap: periodMap,
			}
			err = generator.GenerateAndInsert()
			assert.NoError(err)

			// 校验 sponsor 是否生成
			var sponsor giftwall.Sponsor
			err = giftwall.SponsorCollection().FindOne(ctx, bson.M{"_period_id": period.OID}).Decode(&sponsor)
			if tt.name == "普通周期达标" || tt.name == "高级周期达标" {
				assert.NoError(err)
				assert.Equal(roomID, sponsor.RoomID)
				assert.Equal(userID, sponsor.UserID)
				assert.Equal(giftID, sponsor.GiftID)
			} else {
				assert.Error(err)
			}

			// 清理
			_, _ = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"_id": period.OID})
			_, _ = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": giftID})
			_, _ = giftwall.SponsorCollection().DeleteMany(ctx, bson.M{"_period_id": period.OID})
		})
	}
}

func TestActionCronGiftWallSponsor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 固定测试数据 OID
	currentPeriodOID, _ := primitive.ObjectIDFromHex("6849361780f716bd0797c582")
	prevPeriodOID, _ := primitive.ObjectIDFromHex("6849361780f716bd0797c583")
	testGiftID := int64(90001)
	testRoomID := int64(80001)
	testUserID := int64(70001)

	// 设置测试时间（周一 0 点）
	testNow := time.Date(2024, 3, 18, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time { return testNow })
	defer goutil.SetTimeNow(nil)

	periodStartTime := goutil.BeginningOfWeek(goutil.BeginningOfDay(testNow)).Unix()
	prevStartTime := periodStartTime - int64(giftwall.PeriodDuration.Seconds())
	prevEndTime := periodStartTime
	currEndTime := periodStartTime + int64(giftwall.PeriodDuration.Seconds())

	// 清理相关 collection
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{currentPeriodOID, prevPeriodOID}}})
	require.NoError(err)
	_, err = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)
	_, err = giftwall.SponsorCollection().DeleteMany(ctx, bson.M{"_period_id": bson.M{"$in": []primitive.ObjectID{currentPeriodOID, prevPeriodOID}}})
	require.NoError(err)
	_, err = giftwall.RankCollection().DeleteMany(ctx, bson.M{"_period_id": bson.M{"$in": []primitive.ObjectID{currentPeriodOID, prevPeriodOID}}})
	require.NoError(err)
	require.NoError(service.LRURedis.Del(keys.KeyParams1.Format(params.KeyGiftWall)).Err())

	// 插入周期（先上一周期，再当前周期）
	_, err = giftwall.PeriodCollection().InsertMany(ctx, []any{
		&giftwall.Period{
			OID:          prevPeriodOID,
			CreateTime:   testNow.Unix(),
			ModifiedTime: testNow.Unix(),
			StartTime:    prevStartTime,
			EndTime:      prevEndTime,
			ShowGiftIDs:  []int64{testGiftID},
			Type:         giftwall.PeriodTypeNormal,
		},
		&giftwall.Period{
			OID:          currentPeriodOID,
			CreateTime:   testNow.Unix(),
			ModifiedTime: testNow.Unix(),
			StartTime:    prevEndTime,
			EndTime:      currEndTime,
			ShowGiftIDs:  []int64{testGiftID},
			Type:         giftwall.PeriodTypeNormal,
		},
	})
	require.NoError(err)

	// 插入礼物
	_, err = gift.Collection().InsertOne(ctx, &gift.Gift{
		GiftID:    testGiftID,
		Name:      "单测礼物",
		NameClean: "单测礼物90001",
		Price:     100,
	})
	require.NoError(err)

	// 插入点亮记录（上一周期）
	_, err = giftwall.RankCollection().InsertOne(ctx, &giftwall.ActivatedRank{
		PeriodOID:    prevPeriodOID,
		RoomID:       testRoomID,
		UserID:       testUserID,
		ShowGiftID:   testGiftID,
		ActivatedNum: 200, // 200*100=20000
		CreateTime:   testNow.Unix(),
		ModifiedTime: testNow.Unix(),
	})
	require.NoError(err)

	// 插入礼物墙配置前清理
	_, err = params.Collection().DeleteMany(ctx, bson.M{"key": params.KeyGiftWall})
	require.NoError(err)
	// 插入礼物墙配置
	_, err = params.Collection().InsertOne(ctx, &params.GiftWall{
		Key:      params.KeyGiftWall,
		OpenTime: testNow.Add(-24 * time.Hour).Unix(),
		NormalGiftWall: &params.GiftWallSponsor{
			SponsorThreshold: 10000, // 100*100
		},
		PremiumGiftWall: &params.GiftWallSponsor{
			SponsorThreshold: 20000,
		},
	})
	require.NoError(err)

	// 执行 ActionCronGiftWallSponsor
	_, msg, err := ActionCronGiftWallSponsor(&handler.Context{})
	assert.NoError(err)
	assert.Equal("生成冠名记录成功", msg)

	// 校验 sponsor 记录（上一周期）
	var sponsor giftwall.Sponsor
	err = giftwall.SponsorCollection().FindOne(ctx, bson.M{"_period_id": prevPeriodOID}).Decode(&sponsor)
	assert.NoError(err)
	assert.Equal(testRoomID, sponsor.RoomID)
	assert.Equal(testUserID, sponsor.UserID)
	assert.Equal(testGiftID, sponsor.GiftID)
	assert.Equal(int64(200), sponsor.ActivatedNum)
	assert.Equal(int64(20000), sponsor.Revenue)
}
