package cron

import (
	"errors"
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCronGiftWallPeriodExtend 礼物墙自动生成新周期
// 运行周期：0 30 23 * * 0
/**
 * @api {post} /rpc/cron/giftwall/period/extend 礼物墙自动生成新周期
 * @apiDescription 在当前周期即将结束新周期仍未创建时，使用当前周期信息创建新周期
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "创建周期成功"
 *     }
 */
func ActionCronGiftWallPeriodExtend(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	errs := make([]error, 0, 2)
	normalResp, err := extendGiftWallPeriod(now, giftwall.PeriodTypeNormal)
	if err != nil {
		errs = append(errs, fmt.Errorf("普通礼物墙错误：%w ", err))
	}
	premiumResp, err := extendGiftWallPeriod(now, giftwall.PeriodTypePremium)
	if err != nil {
		errs = append(errs, fmt.Errorf("甄选礼物墙错误：%w", err))
	}

	if len(errs) > 0 {
		return "", actionerrors.NewErrServerInternal(errors.Join(errs...), nil)
	}
	return fmt.Sprintf("普通礼物墙：%s，甄选礼物墙：%s", normalResp, premiumResp), nil
}

func extendGiftWallPeriod(now time.Time, periodType int) (string, error) {
	p, err := giftwall.CurrentPeriodInfoByType(now, periodType)
	if err != nil {
		return "", err
	}
	if p == nil {
		return "当前没有生效周期", nil
	}
	if now.AddDate(0, 0, 1).Unix() <= p.EndTime {
		return "最多允许在当前周期即将结束的最后一天生成新周期", nil
	}

	newStartTime := time.Unix(p.EndTime, 0)
	newEndTime := newStartTime.Add(giftwall.PeriodDuration)
	period, err := giftwall.FindPeriodsByType(newStartTime, newEndTime, periodType)
	if err != nil {
		return "", err
	}
	if period != nil {
		return "下周期已存在", nil
	}

	err = giftwall.CreatePeriodWithType(newStartTime, newEndTime, periodType, p.ShowGiftIDs, p.Rewards, p.CreatorIDs)
	if err != nil {
		return "", err
	}
	return "创建周期成功", nil
}
