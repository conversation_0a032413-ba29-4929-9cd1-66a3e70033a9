package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronGiftWallPeriodExtend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mockTime := time.Date(2100, 1, 1, 0, 0, 0, 0, time.Local)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": mockTime.Unix(),
	})
	require.NoError(err)

	now := mockTime.Add(-30 * time.Minute)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	nowFormat := now.Format(util.TimeFormatYMDWithNoSpace)
	service.Cache5Min.Delete(keys.KeyCurrentGiftWallPeriod2.Format(nowFormat, giftwall.PeriodTypeNormal))
	service.Cache5Min.Delete(keys.KeyCurrentGiftWallPeriod2.Format(nowFormat, giftwall.PeriodTypePremium))
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	resp, err := ActionCronGiftWallPeriodExtend(c)
	require.NoError(err)
	assert.Equal("普通礼物墙：当前没有生效周期，甄选礼物墙：当前没有生效周期", resp)
}

func TestExtendGiftWallPeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mockTime := time.Date(2100, 1, 1, 0, 0, 0, 0, time.Local)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": mockTime.Unix(),
	})
	require.NoError(err)

	now := mockTime.Add(-30 * time.Minute)
	key := keys.KeyCurrentGiftWallPeriod2.Format(
		now.Format(util.TimeFormatYMDWithNoSpace),
		giftwall.PeriodTypeNormal)
	service.Cache5Min.Delete(key)
	info, err := extendGiftWallPeriod(now, giftwall.PeriodTypeNormal)
	require.NoError(err)
	assert.Equal("当前没有生效周期", info)

	p := &giftwall.Period{
		StartTime: mockTime.AddDate(0, 0, -14).Unix(),
		EndTime:   mockTime.AddDate(0, 0, 1).Unix(),
	}
	service.Cache5Min.Set(key, p, 0)
	info, err = extendGiftWallPeriod(now, giftwall.PeriodTypeNormal)
	require.NoError(err)
	assert.Equal("最多允许在当前周期即将结束的最后一天生成新周期", info)

	p.EndTime = mockTime.Unix()
	service.Cache5Min.Set(key, p, 0)
	info, err = extendGiftWallPeriod(now, giftwall.PeriodTypeNormal)
	require.NoError(err)
	assert.Equal("创建周期成功", info)
	info, err = extendGiftWallPeriod(now, giftwall.PeriodTypeNormal)
	require.NoError(err)
	assert.Equal("下周期已存在", info)
}
