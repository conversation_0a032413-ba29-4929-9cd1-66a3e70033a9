package channel

import (
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionConnectCallback(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", false, "")
	_, err := ActionConnectCallback(c)
	assert.Equal(actionerrors.ErrParams, err)

	q := url.Values{}
	q.Add("ts", "1680245200")
	q.Add("appkey", config.Conf.Params.ChannelCallback.AppKey)
	q.Add("type", "1")
	q.Add("channel", "1xxsfsds")
	q.Add("sign", "xxxxxx")
	c = handler.NewTestContext(http.MethodPost, "", false, q)
	_, err = ActionConnectCallback(c)
	assert.Equal(actionerrors.ErrSignNotValid, err)

	q.Set("sign", "0d05754e446d9897d0f70b4a8dffeae5")
	c = handler.NewTestContext(http.MethodPost, "", false, q)
	res, err := ActionConnectCallback(c)
	require.NoError(err)
	assert.Equal("success", res)
}
