package channel

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestInfoSummary(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var resp *StreamListResp

	room, err := room.FindOne(bson.M{"channel.provider": "bvc"})
	require.NoError(err)
	require.NotNil(room)

	streamName := service.BvcLive.StreamName(room.RoomID, room.CreatorID)
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/channel/stream/list?query=%d", room.RoomID), false, nil)
	r, err := ActionStreamList(c)
	require.NoError(err)
	resp = r.(*StreamListResp)
	require.NotEmpty(resp.Data)
	assert.Equal(streamName, resp.Data[0].StreamName)
	assert.Equal(room.RoomID, resp.Data[0].RoomID)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/channel/stream/list?query=%s", streamName), false, nil)
	r, err = ActionStreamList(c)
	require.NoError(err)
	resp = r.(*StreamListResp)
	require.NotEmpty(resp.Data)
	assert.Equal(streamName, resp.Data[0].StreamName)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/channel/stream/list?query=128128", false, nil)
	r, err = ActionStreamList(c)
	require.NoError(err)
	resp = r.(*StreamListResp)
	assert.Empty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/channel/stream/list", false, nil)
	_, err = ActionStreamList(c)
	require.Equal(actionerrors.ErrParams, err)
}

func TestInfoFull(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var resp *room.Room

	roomInfo, err := room.FindOne(bson.M{"channel.provider": "bvc"})
	require.NoError(err)
	require.NotNil(roomInfo)

	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/channel/stream/info?stream_name=%s", service.BvcLive.StreamName(roomInfo.RoomID, roomInfo.CreatorID)), false, nil)
	r, err := ActionStreamInfo(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*room.Room)
	assert.Equal(roomInfo.RoomID, resp.RoomID)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/channel/stream/info?stream_name=%s", service.BvcLive.StreamName(int64(128128), int64(1))), false, nil)
	r, err = ActionStreamInfo(c)
	require.Error(err)
	assert.Nil(r)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/channel/stream/info?stream_name=_test", false, nil)
	r, err = ActionStreamInfo(c)
	require.Error(err)
	assert.Nil(r)
}
