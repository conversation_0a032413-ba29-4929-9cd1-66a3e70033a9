package channel

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionConnectConfirm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIIMBroadcast, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIIMNotifySet, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()

	var (
		testUserID int64 = 10
	)
	r, err := room.FindOne(bson.M{"status.open": room.StatusOpenTrue, "creator_id": bson.M{"$ne": testUserID}})
	require.NoError(err)
	require.NotNil(r)
	_, err = livepk.PoolCollection().DeleteMany(context.Background(), bson.M{"room_id": r.RoomID})
	require.NoError(err)
	_, err = livepk.PKCollection().DeleteMany(context.Background(), bson.M{"fighters.room_id": r.RoomID})
	require.NoError(err)
	err = livemulticonnect.DB().
		Where("from_room_id = ? OR to_room_id = ?", r.RoomID, r.RoomID).
		Delete(livemulticonnect.Match{}).Error
	require.NoError(err)

	p := connectConfirmParam{
		RoomID: r.RoomID,
		UserID: testUserID,
	}
	c := handler.NewTestContext(http.MethodPost, "/connect/confirm", true, p)
	c.User().ID = r.CreatorID
	_, err = ActionConnectConfirm(c)
	assert.Equal(actionerrors.ErrCannotFindConnectUserInQueue, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow().UnixMilli()
	_, err = liveconnect.Collection().InsertOne(ctx, liveconnect.LiveConnect{
		Helper: liveconnect.Helper{
			RoomID:      r.RoomID,
			UserID:      testUserID,
			Status:      liveconnect.StatusQueued,
			CreatedTime: now,
			UpdatedTime: now,
		},
	})
	defer func() {
		_, err = liveconnect.Collection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "user_id": testUserID})
		assert.NoError(err)
	}()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/connect/confirm", true, p)
	c.User().ID = r.CreatorID
	_, err = ActionConnectConfirm(c)
	require.NoError(err)
	lc, err := liveconnect.FindOne(bson.M{"room_id": r.RoomID, "user_id": testUserID})
	require.NoError(err)
	require.NotNil(lc)
	assert.Equal(liveconnect.StatusJoined, lc.Status)
}

func TestConnectConfirmParam_updateRoomConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(172842330)
	r, err := room.Find(testRoomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)

	key := keys.KeyRoomsEnableAgora0.Format()
	require.NoError(service.Redis.SRem(key, testRoomID).Err())
	r.Connect.Provider = room.ProviderAgora
	p := connectConfirmParam{
		RoomID:       r.RoomID,
		r:            r,
		queueConnect: &liveconnect.LiveConnect{Helper: liveconnect.Helper{PushType: "web"}},
	}

	// 使用 bvc
	require.NoError(p.updateRoomConnect())
	p.r, err = room.FindOne(bson.M{"room_id": testRoomID}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(p.r)
	assert.Equal(room.ProviderBvclive, p.r.Connect.Provider)
	assert.NotEmpty(p.r.Connect.ID)

	// 使用 bililive
	p.r.Connect.PushType = "web"
	bililive.SetMockResult(bililive.ActionCreateChannel, bililive.Channel{ChannelID: 123456})
	require.NoError(p.updateRoomConnect())
	p.r, err = room.Find(testRoomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(p.r)
	assert.Equal(room.ProviderBililive, p.r.Connect.Provider)
	assert.NotEmpty(p.r.Connect.ID)

	// 使用 agora
	require.NoError(service.Redis.SAdd(key, testRoomID).Err())
	require.NoError(p.updateRoomConnect())
	res, err := room.FindOne(bson.M{"room_id": testRoomID})
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(room.ProviderAgora, res.Connect.Provider)
	assert.NotEmpty(res.Connect.ID)
}
