package channel

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type diagnosticResp struct {
	TestSpeedAddrs []string `json:"test_speed_addrs"`
	IPs            []string `json:"ips"`
}

// ActionDiagnostic 推流测速
/**
 * @api {get} /api/v2/channel/diagnostic 获取推流测速参数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiSuccessExample 需要测试的情况:
 *   {
 *     "code": 0,
 *     "info": {
 *       "test_speed_addrs": ["rtmp://test1", "rtmp://test2"],
 *       "ips": ["127.0.0.1", "*********"]
 *     }
 *   }
 *
 * @apiSuccessExample 不需要测试的情况（小运营商不支持边缘推流等情况）:
 *   {
 *     "code": 0,
 *     "info": {
 *       "test_speed_addrs": null,
 *       "ips": null
 *     }
 *   }
 *
 */
func ActionDiagnostic(c *handler.Context) (handler.ActionResponse, error) {
	ip := c.ClientIP()
	res, err := service.BvcLive.SpeedList(ip)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"ip": ip})
	}
	return &diagnosticResp{
		TestSpeedAddrs: res.TestSpeedAddrs,
		IPs:            res.IPs,
	}, nil
}
