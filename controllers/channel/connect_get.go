package channel

import (
	"slices"
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/agora"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const (
	connectTypePull = iota
	connectTypeConnect
	connectTypeList
	connectTypeCreatorPK
	connectTypeMultiConnect

	connectTypeCount
)

type connectGetParam struct {
	resp connectGetResp

	roomID      int64
	connectType int
	c           *handler.Context
	r           *room.Room

	level int // 调用者等级, 默认听众
}

type connectGetResp struct {
	CreatorID int64        `json:"creator_id"` // 房主不走缓存
	Channel   room.Channel `json:"channel"`
	Connect   room.Connect `json:"connect"`
}

// ActionConnectGet 获取连麦信息
/**
 * @api {get} /api/v2/channel/connect/get 获取连麦信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {number=0,1,2,3,4} type 调用情景 0: 获取拉流信息；1: 主播通过连麦后，连麦者获取连麦信息；2: 查看连麦列表；3: 主播获取 PK 连麦信息；4: 主播连线
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "channel": {
 *         "flv_pull_url": "http://test.flv",
 *         "hls_pull_url": "http://test.m3u8",
 *         "rtmp_pull_url": "rtmp://test",
 *         "push_url": "rtmp://test"
 *       },
 *       "connect": {
 *         "id": "213354", // agora 使用 id, key 作为标识; bvclive 使用 id, name, key 作为标识, bililive 使用 id, key 为标识
 *         "provider": "agora", // 需要支持 agora、bvclive、bililive
 *         "created_time": 12345,
 *         "join": [],
 *         "queue": [],
 *         "finish": [],
 *         "name": "213354", // agora 时不使用该字段做为连麦标识; bvclive 时该字段为 stream_name (maoer_xxxx_xxxx);
 *         "key": "aaaabbbcc", // agora 时表示声网请求令牌; bvclive 时该字段为推流 KEY; bililive 时为 access_token
 *         "agora_uid": "1234123" // 字符串声网用户 ID, 没有该字段则按照旧逻辑本地生成数字 ID
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 500030014
 * @apiError (400) {String} info 当前客户端版本过低，请更新客户端
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionConnectGet(c *handler.Context) (handler.ActionResponse, error) {
	param := connectGetParam{c: c}
	err := param.load()
	if err != nil {
		return nil, err
	}
	switch param.connectType {
	case connectTypePull, connectTypeConnect, connectTypeList:
		// 会存入/使用缓存
		return param.normalResp()
	case connectTypeCreatorPK, connectTypeMultiConnect:
		// PK 和主播连线，不使用缓存
		return param.creatorResp()
	}
	return nil, actionerrors.ErrParams
}

func (param *connectGetParam) normalResp() (handler.ActionResponse, error) {
	if param.loadCache() {
		return &param.resp, nil
	}
	err := param.findRoom()
	if err != nil {
		return nil, err
	}
	err = param.buildResp()
	if err != nil {
		return nil, err
	}
	param.saveCache()
	err = param.buildConnect()
	if err != nil {
		return nil, err
	}
	return &param.resp, nil
}

func (param *connectGetParam) creatorResp() (handler.ActionResponse, error) {
	err := param.findRoom()
	if err != nil {
		return nil, err
	}
	if param.c.UserID() == 0 || param.c.UserID() != param.r.CreatorID {
		// 只允许主播获取自己直播间 PK 连麦信息
		return nil, actionerrors.ErrForbidden
	}
	err = param.buildResp()
	if err != nil {
		return nil, err
	}
	err = param.buildConnect()
	if err != nil {
		return nil, err
	}
	return &param.resp, nil
}

func (param *connectGetParam) loadCache() bool {
	param.connectType, _ = param.c.GetParamInt("type")
	// 只有在用户是获取拉流地址的时候会走缓存
	// 大流量主播刷新重连的时候，会导致用户短时大量请求
	if param.connectType != connectTypePull {
		return false
	}
	param.roomID, _ = param.c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return false
	}
	key := keys.KeyConnectGet1.Format(param.roomID)
	v, ok := service.Cache5s.Get(key)
	if !ok {
		return false
	}
	cache := v.(connectGetResp)
	param.resp = cache
	param.resp.Connect.Queue = copyLiveConnect(cache.Connect.Queue)
	param.resp.Connect.Join = copyLiveConnect(cache.Connect.Join)
	param.resp.Connect.Finish = copyLiveConnect(cache.Connect.Finish)

	if param.c.UserID() == param.resp.CreatorID ||
		liveconnect.IsUserInQueue(param.c.UserID(), param.resp.Connect.Join) {
		return false // 房主和连麦用户不走缓存
	}

	e := param.c.Equip()
	param.resp.Channel.BuildAuthedURL(param.roomID, param.resp.CreatorID,
		param.c.UserID(), param.c.ClientIP(), e, false, false)
	err := param.buildConnect()
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

func (param *connectGetParam) load() error {
	param.roomID, _ = param.c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return actionerrors.ErrParams
	}
	param.connectType, _ = param.c.GetParamInt("type")
	if param.connectType < connectTypePull || param.connectType >= connectTypeCount {
		return actionerrors.ErrParams
	}
	return nil
}

func (param *connectGetParam) findRoom() (err error) {
	param.r, err = room.Find(param.roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil || param.r.IsBan() {
		return actionerrors.ErrCannotFindRoom
	}
	if param.r.Status.Open == room.StatusOpenFalse {
		return actionerrors.ErrClosedRoomAlt
	}

	param.r.TrySwitchProvider(param.r.IsOwner(param.c))
	return nil
}

func (param *connectGetParam) buildResp() error {
	param.resp.CreatorID = param.r.CreatorID
	param.resp.Channel = param.r.Channel
	e := param.c.Equip()
	param.resp.Channel.BuildAuthedURL(param.roomID, param.r.CreatorID, param.c.UserID(), param.c.ClientIP(), e,
		param.r.IsOwner(param.c), false)

	param.resp.Connect = param.r.Connect
	openTime := util.UnixMilliToTime(param.r.Status.OpenTime)
	err := param.resp.Connect.ListConnects(param.r.OID, openTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	level := agora.LevelListener
	switch {
	case liveconnect.IsUserInQueue(param.c.UserID(), param.resp.Connect.Join):
		level = agora.LevelBroadCaster
	case param.r.IsOwner(param.c):
		level = agora.LevelChannelOwner
	}
	param.level = level
	return nil
}

// buildConnect 房主和连麦用户保留连麦必要信息
// 普通用户删除信息
func (param *connectGetParam) buildConnect() error {
	defer func() {
		if param.level != agora.LevelBroadCaster &&
			param.level != agora.LevelChannelOwner {
			param.resp.Connect.ID = ""
		}
	}()
	// 不是连麦请求或 PK 或主播连线不走推流逻辑
	if !slices.Contains([]int{connectTypeConnect, connectTypeCreatorPK, connectTypeMultiConnect}, param.connectType) {
		return nil
	}

	e := param.c.Equip()
	switch param.resp.Connect.Provider {
	case room.ProviderAgora:
		agoraID := agora.UserIDToAgoraID(param.c.UserID(), param.c.Equip().OS)
		param.resp.Connect.BuildAgora(agoraID, param.level)
		// WORKAROUND: 兼容客户端, 安卓 >= 5.6.6 iOS >= 4.7.8, 不返回 name 字段
		if e.FromApp && !e.IsAppOlderThan("4.7.8", "5.6.6") {
			param.resp.Connect.Name = ""
		}
	case room.ProviderBvclive:
		// WORKAROUND: 兼容安卓 < 5.6.6 iOS < 4.7.8 的版本不支持 bvclive 推流, 返回客户端版本过低
		if e.IsAppOlderThan("4.7.8", "5.6.6") {
			return actionerrors.ErrOutdatedClient
		}
		err := param.resp.Connect.BuildBvclive(
			param.roomID, param.c.UserID(), param.resp.Channel.PushURL, param.c.ClientIP())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	case room.ProviderBililive:
		// 目前仅主播 PK 会有 bililive 的情况
		channelID, err := strconv.ParseInt(param.r.Connect.ID, 10, 64)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		err = param.resp.Connect.BuildBililive(param.c.UserID(), channelID, param.c.ClientIP())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

func (param *connectGetParam) saveCache() {
	cache := param.resp
	cache.Connect.Queue = copyLiveConnect(param.resp.Connect.Queue)
	cache.Connect.Join = copyLiveConnect(param.resp.Connect.Join)
	cache.Connect.Finish = copyLiveConnect(param.resp.Connect.Finish)
	cache.Channel.ChainedPushURL = nil
	cache.Channel.PushURL = ""
	key := keys.KeyConnectGet1.Format(param.roomID)
	service.Cache5s.Set(key, cache, 0)
}

func copyLiveConnect(lcs []*liveconnect.LiveConnect) []*liveconnect.LiveConnect {
	res := make([]*liveconnect.LiveConnect, 0, len(lcs))
	for i := range lcs {
		lc := new(liveconnect.LiveConnect)
		*lc = *lcs[i]
		res = append(res, lc)
	}
	return res
}
