package channel

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/cdn/agora"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestConnectGetFindRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := connectGetParam{
		c: handler.NewTestContext(http.MethodGet, "/", true, nil),
	}

	// 直播间封禁
	meta, err := livemeta.FindOneSimple(bson.M{"ban.type": livemeta.TypeBanForever})
	require.NoError(err)
	require.NotNil(meta)
	param.roomID = meta.RoomID
	assert.Equal(actionerrors.ErrCannotFindRoom, param.findRoom())

	// 直播间关播
	r, err := room.FindOne(bson.M{"status.open": room.StatusOpenFalse})
	require.NoError(err)
	require.NotNil(r)
	param.roomID = r.RoomID
	assert.Equal(actionerrors.ErrClosedRoomAlt, param.findRoom())

	param.roomID = testRoomID
	err = param.findRoom()
	require.NoError(err)
	assert.Equal(room.ProviderAgora, param.r.Connect.Provider)
}

func TestConnectGetBuildConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := connectGetParam{
		c:           handler.NewTestContext(http.MethodGet, "/api/v2/channel/connect/get", true, nil),
		r:           &room.Room{Helper: room.Helper{CreatorID: 12}},
		roomID:      10,
		connectType: connectTypeConnect,
	}
	// 这里让 role = 2, 测试 action 的位置让 role = 0
	param.resp.Connect.Provider = room.ProviderAgora
	param.resp.Connect.ID = "1234"
	param.level = agora.LevelChannelOwner
	param.r.Connect.Provider = room.ProviderAgora
	err := param.buildConnect()
	require.NoError(err)
	assert.Equal("1234", param.resp.Connect.ID)
	assert.Equal("1234", param.resp.Connect.Name)

	bvc.SetMockResult(bvc.ActionUpset, bvc.Channel{
		Channel: &bvc.ChannelInfo{
			PushURL: "rtmp://live-push.bilivideo.com/live-bvc/maoer_test_12_10?key=854c7246288611eba218f281bfa61990",
		},
	})
	param.resp.Connect.Provider = room.ProviderBvclive
	param.resp.Connect.ID = "4321"
	e := param.c.Equip()
	e.FromApp = true
	e.OS = goutil.IOS
	e.AppVersion = "4.7.8"
	err = param.buildConnect()
	require.NoError(err)
	assert.Equal("4321", param.resp.Connect.ID)
	assert.Equal("maoer_test_12_10", param.resp.Connect.Name)
	assert.Equal("854c7246288611eba218f281bfa61990", param.resp.Connect.Key)
	param.resp.Channel.PushURL = "rtmp://txy.maoer-push.bilivideo.com/live-bvc/maoer_9075077_373206839?key=233b1e589d3411ebba1e5a01179c8800"
	err = param.buildConnect()
	require.NoError(err)
	assert.Equal("maoer_9075077_373206839", param.resp.Connect.Name)
	assert.Equal("233b1e589d3411ebba1e5a01179c8800", param.resp.Connect.Key)
	// 低于兼容的版本
	e.OS = goutil.IOS
	e.AppVersion = "4.7.7"
	err = param.buildConnect()
	assert.EqualError(err, "当前客户端版本过低，请更新客户端")

	param.resp.Connect.Provider = "default"
	param.resp.Connect.ID = "4321"
	param.resp.Connect.Name = ""
	err = param.buildConnect()
	require.NoError(err)
	assert.Empty(param.resp.Connect.Name)

	param.resp.Connect.Provider = room.ProviderBililive
	param.r.Connect.ID = "1234"
	bililive.SetMockResult(bililive.ActionGetAccessToken, bililive.AccessTokenInfo{AccessToken: "test_eeexxx"})
	err = param.buildConnect()
	require.NoError(err)
	assert.Equal("test_eeexxx", param.resp.Connect.Key)
	assert.NotEmpty(param.resp.Connect.ID)
}

func TestActionConnectGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	url := "/connect/get?room_id=" + testRoomIDStr
	c := handler.NewTestContext("GET", url, true, nil)
	r, err := ActionConnectGet(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*connectGetResp)
	assert.Equal(room.ProviderAgora, resp.Connect.Provider)
	assert.NotEmpty(resp.Channel.FLVPullURL)
	assert.Empty(resp.Channel.PushURL)

	// 测试缓存
	testRoom, err := room.FindOneSimple(bson.M{"status.open": 1}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(testRoom)
	url = "/connect/get?room_id=" + strconv.FormatInt(testRoom.RoomID, 10)
	c = handler.NewTestContext("GET", url, true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionConnectGet(c)
	require.NoError(err)
	tutil.PrintJSON(r)
	resp = r.(*connectGetResp)
	resp.CreatorID = 123 // 测试修改了响应数据之后，从缓存读取的数据是正常的
	c = handler.NewTestContext("GET", url, true, nil)
	c.User().ID = resp.CreatorID
	r, err = ActionConnectGet(c)
	require.NoError(err)
	tutil.PrintJSON(r)
	resp = r.(*connectGetResp)
	assert.Equal(resp.CreatorID, testRoom.CreatorID)
	assert.Nil(resp.Channel.ChainedPushURL)
}

func TestCreatorResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.Update(347142109, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	c := handler.NewTestContext("GET", "", true, nil)
	param := connectGetParam{
		c:      c,
		roomID: r.RoomID,
	}
	_, err = param.creatorResp()
	assert.Equal(actionerrors.ErrForbidden, err)

	r, err = room.Find(r.RoomID)
	require.NoError(err)
	require.NotNil(r)
	param.roomID = r.RoomID
	param.c.User().ID = r.CreatorID
	resp, err := param.creatorResp()
	require.NoError(err)
	res, ok := resp.(*connectGetResp)
	require.True(ok)
	assert.Equal(r.Connect.Name, res.Connect.Name)
	assert.Equal(r.Connect.Provider, res.Connect.Provider)
}

func TestBuildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// iOS 4.5.6 安卓 5.4.9 以前的的版本需要返回 RTMP 的流
	c := handler.NewTestContext("GET", "", true, nil)
	c.Equip().OS = goutil.Android
	c.Equip().FromApp = true
	c.Equip().AppVersion = "5.4.8"
	param := connectGetParam{
		c: c,
		r: new(room.Room),
	}
	param.resp.Channel = room.Channel{
		Provider: room.ChannelProviderKsyun,
	}
	require.NoError(param.buildResp())
	assert.NotEmpty(param.resp.Channel.RTMPPullURL)

	// WORKAROUND: 仅 iOS 4.5.6 安卓 5.4.6 以前的版本返回 BVC RTMP 的流
	param.r.Channel.Provider = room.ChannelProviderBvc
	param.c.Equip().AppVersion = "5.4.6"
	require.NoError(param.buildResp())
	assert.Empty(param.resp.Channel.RTMPPullURL)
}
