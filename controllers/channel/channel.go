package channel

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "channel",
		Actions: map[string]*handler.Action{
			"connect/get":     handler.NewAction(handler.GET, ActionConnectGet, false),
			"connect/confirm": handler.NewAction(handler.POST, ActionConnectConfirm, false),
			"diagnostic":      handler.NewAction(handler.GET, ActionDiagnostic, false),

			"connect/callback": handler.NewAction(handler.POST, ActionConnectCallback, false),

			// 房间的简要和详细信息
			"stream/list": handler.NewAction(handler.GET, ActionStreamList, false),
			"stream/info": handler.NewAction(handler.GET, ActionStreamInfo, false),
		},
	}
}
