package channel

import (
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/connectcheck"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type connectConfirmParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`
	UserID int64 `form:"user_id" json:"user_id" `

	c            *handler.Context
	r            *room.Room
	queueConnect *liveconnect.LiveConnect
	connectUser  *liveuser.User
}

// ActionConnectConfirm 同意连麦
/**
 * @api {post} /api/v2/channel/connect/confirm 同意连麦
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} user_id 连麦用户 ID
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     {
 *       "type": "connect",
 *       "event": "confirm",
 *       "room_id": 65261414,
 *       "status": 1,
 *       "time": 1576748853091,
 *       "target": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "introduction": "",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static-test.maoercdn.com/gifts/avatarframes/006.png"
 *         }]
 *       },
 *       "user": {
 *         "user_id": 2847947
 *       }
 *     }
 */
func ActionConnectConfirm(c *handler.Context) (handler.ActionResponse, error) {
	param := connectConfirmParam{c: c}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.connect()
	if err != nil {
		return nil, err
	}
	return handler.M{"ok": 1}, nil
}

func (p *connectConfirmParam) check() error {
	var err error
	p.r, err = room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if p.r.CreatorID != p.c.UserID() || p.r.CreatorID == p.UserID {
		return actionerrors.ErrForbidden
	}
	if !p.r.IsOpen() {
		return actionerrors.ErrClosedRoom
	}
	err = connectcheck.NewConnectComponent(p.r).Check()
	if err != nil {
		return err
	}
	connecting, err := liveconnect.IsRoomConnecting(p.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if connecting {
		return actionerrors.ErrRoomChannelConnecting
	}
	p.queueConnect, err = liveconnect.FindOne(bson.M{
		"room_id":      p.RoomID,
		"user_id":      p.UserID,
		"status":       liveconnect.StatusQueued,
		"created_time": bson.M{"$gte": p.r.Status.OpenTime}})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.queueConnect == nil {
		return actionerrors.ErrCannotFindConnectUserInQueue
	}
	p.connectUser, err = liveuser.Find(p.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.connectUser == nil {
		return actionerrors.ErrCannotFindUser
	}
	p.connectUser.MakeTitles(&liveuser.FindOptions{RoomID: p.RoomID})
	return nil
}

func (p *connectConfirmParam) connect() error {
	nowUnixMilli := goutil.TimeNow().UnixMilli()
	ok, err := liveconnect.UpdateOne(
		bson.M{"_id": p.queueConnect.OID, "status": liveconnect.StatusQueued},
		bson.M{"status": liveconnect.StatusJoined, "updated_time": nowUnixMilli, "created_time": nowUnixMilli})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrCannotFindConnectUserInQueue
	}
	err = p.updateRoomConnect()
	if err != nil {
		return err
	}

	err = userapi.IMNotifySet(p.c.UserContext(), p.RoomID, []int64{p.UserID, p.r.CreatorID})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	notify := map[string]interface{}{
		"type":    liveim.TypeConnect,
		"event":   liveim.EventConfirm,
		"room_id": p.RoomID,
		"status":  liveconnect.StatusJoined,
		"time":    nowUnixMilli,
		"target":  p.connectUser,
		"user": map[string]interface{}{
			"user_id": p.c.UserID(),
		},
	}
	if err := userapi.Broadcast(p.RoomID, notify); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func (p *connectConfirmParam) updateRoomConnect() error {
	enableAgora, err := service.Redis.SIsMember(keys.KeyRoomsEnableAgora0.Format(), p.RoomID).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	update := make(map[string]interface{})
	if enableAgora {
		update["connect.id"] = room.NewConnectID()
		if p.r.Connect.Provider != room.ProviderAgora {
			update["connect.provider"] = room.ProviderAgora
		}
	} else if p.r.Connect.PushType != "" && p.queueConnect.PushType != "" {
		// 创建 bililive 连麦频道
		channel, err := service.BiliLive.CreateChannel(&bililive.CreateChannelRequestParams{
			BusinessLabel: bililive.BusinessLabelConnect,
		})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if channel == nil {
			return actionerrors.ErrNotFound("获取连麦频道失败")
		}
		update = bson.M{
			"connect.id": strconv.FormatInt(channel.ChannelID, 10),
		}
		if p.r.Connect.Provider != room.ProviderBililive {
			update["connect.provider"] = room.ProviderBililive
		}
	} else {
		update["connect.id"] = room.NewConnectID()
		if p.r.Connect.Provider != room.ProviderBvclive {
			update["connect.provider"] = room.ProviderBvclive
		}
	}

	if len(update) == 0 {
		return nil
	}
	_, err = room.Update(p.RoomID, update)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	room.ClearRoomCache(p.RoomID)
	return nil
}
