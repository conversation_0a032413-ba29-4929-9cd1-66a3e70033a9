package channel

import (
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// StreamListQuery 提供了 Query 队列，Context 以及 Response
type StreamListQuery struct {
	C     *handler.Context
	Queue []int64
	Resp  *StreamListResp
}

// StreamList 提供了简要信息的结构
type StreamList struct {
	StreamName string `json:"stream_name"`

	RoomID int64  `json:"room_id"`
	Name   string `json:"name"`

	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
}

// StreamListResp 提供了简要信息的结构
type StreamListResp struct {
	Data []StreamList `json:"data"`
}

// StreamInfoQuery 提供了 StreamName 流名，Context 以及 Response
type StreamInfoQuery struct {
	C          *handler.Context
	StreamName string
	Resp       *room.Room
}

// ActionStreamList 搜索并返回直播间简要信息
/**
 * @api {get} /api/v2/channel/stream/list 搜索并返回直播间简要信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiParam {String} query 搜索关键词，可能是房间号、主播 ID 或流名之一
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": {
 *     "data": [
 *       {
 *         "stream_name": "test_1_123456", // 流名
 *         "room_id": 123456, // 直播间 ID
 *         "name": "房间名", // 直播间名称
 *         "creator_id": 12, // 主播 ID
 *         "creator_username": "主播昵称" // 主播昵称
 *       }
 *     ]
 *   }
 * }
 */
func ActionStreamList(c *handler.Context) (handler.ActionResponse, error) {
	queries := new(StreamListQuery)
	query := c.C.Query("query")
	if query == "" {
		return nil, actionerrors.ErrParams
	}

	roomID, creatorID, err := service.BvcLive.ParseStreamName(query)
	if err == nil {
		queries.Queue = append(queries.Queue, roomID)
		queries.Queue = append(queries.Queue, creatorID)
	}

	id, err := strconv.ParseInt(query, 10, 64)
	if err == nil {
		queries.Queue = append(queries.Queue, id)
	}

	err = queries.QuerySummary()
	if err != nil {
		return nil, err
	}

	return queries.Resp, nil
}

// ActionStreamInfo 按流名查找并返回直播间具体信息
/**
 * @api {get} /api/v2/channel/stream/info 搜索并返回直播间具体信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiParam {String} stream_name 流名
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": {
 *     "room_id": 123456, // 直播间 ID
 *     "catalog_id": 0,
 *     "name": "房间名", // 直播间名称
 *     "announcement": "公告",
 *     "type": "live",
 *     "channel": {
 *       "flv_pull_url": "",
 *       "hls_pull_url": ""
 *     },
 *     "guild_id": 0,
 *     "creator_id": 1, // 主播 ID
 *     "creator_username": "主播昵称", // 主播昵称
 *     "status": {
 *       "open": 1,
 *       "open_question_count": 0,
 *       "open_revenue": 0,
 *       "open_time": 0,
 *       "close_time": 1503566286005,
 *       "channel": {
 *         "type": "",
 *         "time": 0
 *       },
 *       "broadcasting": false
 *     },
 *     "notice": null,
 *     "statistics": {
 *       "revenue": 26,
 *       "accumulation": 0,
 *       "online": 0,
 *       "attention": false,
 *       "attention_count": 0,
 *       "vip": 0,
 *       "score": 0
 *     },
 *     "connect": {
 *       "provider": "",
 *       "forbidden": false
 *     },
 *     "question": {
 *       "min_price": 0
 *     },
 *     "activity_catalog_id": 0,
 *     "members": {
 *       "admin": null,
 *       "mute": null
 *     },
 *     "cover_url": "https://static-test.missevan.com/avatars/icon01.png", // 直播间封面
 *     "creator_iconurl": "https://static-test.missevan.com/avatars/icon01.png" // 主播头像
 *   }
 * }
 */
func ActionStreamInfo(c *handler.Context) (handler.ActionResponse, error) {
	param := new(StreamInfoQuery)
	param.StreamName = c.C.Query("stream_name")
	if param.StreamName == "" {
		return nil, actionerrors.ErrParams
	}

	err := param.QueryFull()
	if err != nil {
		return nil, err
	}

	return param.Resp, nil
}

// QuerySummary 通过查询目标查询简要信息
func (query *StreamListQuery) QuerySummary() error {
	query.Resp = &StreamListResp{Data: make([]StreamList, 0)}
	if len(query.Queue) == 0 {
		return nil
	}

	r, err := query.FindRoom()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	query.Resp.Data = r
	return nil
}

// FindRoom 根据传入的 targetIDs 查询房间数据
func (query *StreamListQuery) FindRoom() ([]StreamList, error) {
	resp, err := room.FindAll(bson.M{
		"channel.provider": "bvc",
		"$or": []bson.M{
			{"creator_id": bson.M{"$in": query.Queue}},
			{"room_id": bson.M{"$in": query.Queue}},
		},
	})
	if err != nil {
		return nil, err
	}

	r := make([]StreamList, 0, len(resp))
	for _, value := range resp {
		r = append(r, StreamList{
			RoomID:          value.RoomID,
			Name:            value.Name,
			CreatorID:       value.CreatorID,
			CreatorUsername: value.CreatorUsername,
			StreamName:      service.BvcLive.StreamName(value.RoomID, value.CreatorID),
		})
	}
	return r, nil
}

// QueryFull 查询流名并返回详细数据
func (query *StreamInfoQuery) QueryFull() error {
	roomID, creatorID, err := service.BvcLive.ParseStreamName(query.StreamName)
	if err != nil {
		return actionerrors.ErrParams
	}

	opt := &room.FindOptions{FindCreator: true}
	resp, err := room.FindOne(bson.M{
		"room_id":    roomID,
		"creator_id": creatorID,
	}, opt)
	if err != nil {
		return err
	}

	if resp == nil {
		return actionerrors.ErrCannotFindRoom
	}

	query.Resp = resp
	return nil
}
