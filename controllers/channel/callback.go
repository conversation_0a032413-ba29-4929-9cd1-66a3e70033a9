package channel

import (
	"net/http"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
)

// ActionConnectCallback 连麦信息回调
/**
 * @api {post} /api/v2/channel/connect/callback 连麦信息回调
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/channel
 *
 * @apiParam {Number} ts 秒级时间戳
 * @apiParam {String} sign
 * @apiParam {String} appkey
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (401) {Number} code 501010001
 * @apiError (401) {String} info 签名校验失败
 *
 */
func ActionConnectCallback(c *handler.Context) (handler.ActionResponse, error) {
	params := c.Request().URL.Query()
	// TODO: 后续封装成中间件放到 missevan-go
	// Give priority to sign in url query, otherwise check sign in post form.
	if c.Request().Method == http.MethodPost && params.Get("sign") == "" {
		// 调用 PostForm 会对 form 进行解析返回要获取的值，并对 Request().Form 赋值
		if sign := c.C.PostForm("sign"); sign == "" {
			return nil, actionerrors.ErrParams
		}
		params = c.Request().Form
	}
	if !blademaster.IsValidSign(config.Conf.Params.ChannelCallback, params) {
		logger.Warnf("channel connect callback not valid: %s", params.Encode())
		return nil, actionerrors.ErrSignNotValid
	}
	// TODO: 后续使用各场景的回调进行连麦的扩展
	// 场景: https://git.bilibili.co/bapis/bapis/-/blob/feature/open-rtc/live/rtc/v2/event.proto
	logger.Infof("channel connect callback: %s", params.Encode())
	return "success", nil
}
