package channel

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// const roomOIDHex = "5ab9d5d9bc9b53298ce5a5a5"
// const roomIDStr = "22489473"
const normalRoomID = int64(22489473)
const closedRoomID = int64(65150486)

// const bannedRoomIDStr = "369892"
const bannedRoomID = int64(369892) // room_test banned

const testRoomID = int64(3192516)
const testRoomIDStr = "3192516"

var (
	normalRoom *room.Room
)

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()
	service.InitTest()
	handler.SetMode(handler.TestMode)
	service.SetDBUseSQLite()
	initTestData()
	os.Exit(m.Run())
}

func initTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)

	update := bson.M{
		"room_id":          testRoomID,
		"name_clean":       "测试用",
		"creator_id":       516,
		"type":             room.TypeLive,
		"status.open":      room.StatusOpenTrue,
		"connect.provider": room.ProviderAgora,
	}
	_, err := collection.UpdateOne(ctx, bson.M{"room_id": 3192516},
		bson.M{"$set": update}, options.Update().SetUpsert(true))
	if err != nil {
		logger.Fatal(err)
	}
	normalRoom, _ = room.Find(normalRoomID, &room.FindOptions{DisableAll: true})
	if normalRoom == nil {
		logger.Fatal("normal room is nil.")
	}
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("channel", h.Name)
	checker.Check(h.Actions,
		"connect/get", "connect/confirm",
		"diagnostic",
		"connect/callback",
		"stream/list", "stream/info",
	)
}
