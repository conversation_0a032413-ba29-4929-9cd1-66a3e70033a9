package channel

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestDiagnosticTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(diagnosticResp{}, "test_speed_addrs", "ips")
}

func TestActionDiagnostic(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mockRes := bvc.SpeedListResult{
		TestSpeedAddrs: []string{"rtmp://1"},
		IPs:            []string{"127.0.0.1"},
	}
	bvc.SetMockResult(bvc.ActionSpeedList, mockRes)
	c := handler.NewTestContext(http.MethodGet, "/diagnostic", false, nil)
	r, err := ActionDiagnostic(c)
	require.NoError(err)
	var resp *diagnosticResp
	assert.IsType(resp, r)
	resp = r.(*diagnosticResp)
	assert.Equal(mockRes.TestSpeedAddrs, resp.TestSpeedAddrs)
	assert.Equal(mockRes.IPs, resp.IPs)
}
