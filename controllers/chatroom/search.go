package chatroom

import (
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/search"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	searchTypeDefault = iota
	searchTypeMultiConnect
)

type searchParam struct {
	P                int64
	PageSize         int64
	S                string
	InputWord        string
	SuggestRequestID string
	Type             int

	UserID   int64
	ClientIP string

	rooms          []roomInfoToReturn
	pagination     goutil.Pagination
	OpsRequestMisc string
}

// ActionSearch 搜索直播相关内容
/**
 * @api {get} /api/v2/chatroom/search 搜索直播相关内容
 * @apiDescription 搜索直播相关内容
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [p] 页码
 * @apiParam {Number} [page_size] 一页显示数目
 * @apiParam {String} s 实际搜索关键字
 * @apiParam {String} input_word 用户输入的搜索关键字，在热门搜索和历史搜索中传空字符串，参数必传
 * @apiParam {String} [suggest_request_id] 联想词的 request_id
 * @apiParam {number=0,1} [type] 搜索类型，0: 默认；1: 主播连线
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "room_id": 252021449,
 *           "name": "猫猫能有什么坏心眼",
 *           "announcement": "好耶好耶ヽ(✿ﾟ▽ﾟ)ノ！",
 *           "creator_id": 3457177,
 *           "creator_username": "阿橘_",
 *           "status": {
 *             "open": 1,
 *             "attention": true // 是否关注了主播
 *           },
 *           "statistics": {
 *             "score": 1278,
 *             "attention_count": 100 // 关注数
 *           },
 *           "catalog_id": 152,
 *           "catalog_name": "放松",
 *           "cover_url": "https://static-test.maoercdn.com/fmcovers/202104/08/778ef8f198d180b7b873ae155e4c991c.jpg",
 *           "creator_iconurl": "https://static-test.maoercdn.com/avatars/202102/25/1333d068e833dcfe928e344cf59d7345164741.png",
 *           "multi_connect": { // 仅在 type=1 时下发
 *             "status": 1, // 连线状态；1: 连线中；2: 已结束
 *             "group_id": 1, // 连线组 ID，仅在连线中下发
 *             "recent_connected": true, // 是否在最近 7 天连线过
 *             "member_num": 2, // 连线人数（仅在连线中下发）
 *             "join_type": 1, // 加入类型；1: 申请，2: 邀请
 *             "join_status": 1, // 加入状态；0: 未发起申请或邀请，1: 处理中
 *             "match_id": 1 // 连线匹配 ID，仅在处理中下发
 *           }
 *         }
 *       ],
 *       "ops_request_misc": "%7B%22request%5Fid%22%3A%22164750408116781913063425%22%2C%22scm%22%3A%2220140713.110143389..%22%7D",
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSearch(c *handler.Context) (handler.ActionResponse, error) {
	p := searchParam{
		UserID:   c.UserID(),
		ClientIP: c.ClientIP(),
	}
	s, ok := c.GetParamString("s")
	if !ok || s == "" {
		return nil, actionerrors.ErrParams
	}
	ua := c.UserAgent()
	// 针对 dayu-live-helper 的纯数字搜索请求返回空数据
	if strings.Contains(ua, "dayu-live-helper") && regexp.MustCompile(`^\d+$`).MatchString(s) {
		return handler.M{
			"data":             []roomInfoToReturn{},
			"pagination":       goutil.Pagination{},
			"ops_request_misc": "",
		}, nil
	}
	p.S = s
	var err error
	if p.P, p.PageSize, err = c.GetParamPage(); err != nil {
		return nil, actionerrors.ErrParams
	}
	p.InputWord, _ = c.GetParamString("input_word")
	p.SuggestRequestID, _ = c.GetParamString("suggest_request_id")
	p.Type, err = c.GetDefaultParamInt("type", searchTypeDefault)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if err := p.search(); err != nil {
		return nil, err
	}
	return handler.M{
		"data":             p.rooms,
		"pagination":       p.pagination,
		"ops_request_misc": p.OpsRequestMisc,
	}, nil
}

const (
	// MaxOnePage 每页最大数量
	MaxOnePage = 30
)

// StatusSuccess 搜索成功状态值
const StatusSuccess = "OK"

func (p *searchParam) search() error {
	defer func() {
		if p.rooms == nil {
			p.rooms = make([]roomInfoToReturn, 0)
		}
	}()
	if p.P <= 0 || (p.PageSize > MaxOnePage) {
		p.pagination = goutil.MakePagination(0, p.P, MaxOnePage)
		return nil
	}
	reqData := &userapi.DiscoverySearchParams{
		SearchType:       search.TypeLive,
		Keyword:          p.S,
		GuideWord:        p.InputWord,
		SuggestRequestID: p.SuggestRequestID,
		Page:             p.P,
		PageSize:         p.PageSize,
	}
	res, err := userapi.DiscoverySearch(reqData, p.ClientIP)
	if err != nil {
		return err
	}
	if res.Status != StatusSuccess {
		return nil
	}
	p.OpsRequestMisc = res.OpsRequestMisc
	result := res.Result
	p.pagination = goutil.MakePagination(int64(result.ViewTotal), p.P, p.PageSize)
	if !p.pagination.Valid() {
		return nil
	}
	roomIDs := make([]int64, 0, len(result.Items))
	type ItemContent struct {
		// opensearch 返回的 room_id 是字符串
		RoomID string `json:"room_id,omitempty"`
	}
	for _, v := range result.Items {
		var itemContent ItemContent
		err := json.Unmarshal(v, &itemContent)
		if err != nil {
			logger.Error(err)
			continue
		}
		roomID, err := strconv.ParseInt(itemContent.RoomID, 10, 64)
		if err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		roomIDs = append(roomIDs, roomID)
	}
	p.rooms = make([]roomInfoToReturn, 0, len(roomIDs))
	if len(roomIDs) > 0 {
		dbRooms, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil,
			&room.FindOptions{FindCatalogInfo: true, FindCreator: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		dbRoomMaps := goutil.ToMap(dbRooms, "RoomID").(map[int64]*room.Simple)
		for _, roomID := range roomIDs {
			if r := dbRoomMaps[roomID]; r != nil {
				parsedRoom := parseRoomResult(r)
				p.rooms = append(p.rooms, parsedRoom)
			}
		}
	}

	if p.UserID > 0 && p.Type == searchTypeMultiConnect {
		err = p.loadMultiConnectInfo()
		if err != nil {
			return err
		}
	}
	return nil
}

// loadMultiConnectInfo 加载搜索结果直播间主播连线相关信息
func (p *searchParam) loadMultiConnectInfo() error {
	room, err := room.FindOne(bson.M{"creator_id": p.UserID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if room == nil {
		return nil
	}

	roomIDs := make([]int64, 0, len(p.rooms))
	roomCreatorIDs := make([]int64, 0, len(p.rooms))
	for _, r := range p.rooms {
		roomIDs = append(roomIDs, r.RoomID)
		roomCreatorIDs = append(roomCreatorIDs, r.CreatorID)
	}

	// 获取对列表中房间的关注情况
	attentions, err := attentionuser.CheckAttention(p.UserID, roomCreatorIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	atteMap := attentionuser.AttentionSliceToMap(attentions)

	// 获取最近 7 天连线过的房间
	connectedMembers, err := livemulticonnect.FindRecentConnectedMembers(room.RoomID, time.Hour*24*7, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	connectedMembersMap := util.ToMap(connectedMembers, func(m *livemulticonnect.GroupMember) int64 {
		return m.RoomID
	})

	// 获取连线中房间所在组的人数
	roomMemberNumMap, err := livemulticonnect.FindMemberNumByRoomIDs(roomIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取当前房间进行中的匹配记录 FindPendingMatchesByRoomID
	matchingRoomIDMap, err := livemulticonnect.FindMatchingRoomIDMap(room.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	for idx, r := range p.rooms {
		if atte, ok := atteMap[r.CreatorID]; ok {
			r.Status.Attention = &atte.Followed
			r.Statistics.AttentionCount = &atte.FansNum
		} else {
			r.Status.Attention = goutil.NewBool(false)
			r.Statistics.AttentionCount = goutil.NewInt64(0)
		}

		mcInfo := &multiConnectInfo{}
		if groupMemberNum, ok := roomMemberNumMap[r.RoomID]; ok {
			mcInfo.Status = livemulticonnect.NotifyGroupStatusOngoing
			mcInfo.JoinType = livemulticonnect.NotifyGroupJoinTypeApply
			mcInfo.GroupID = &groupMemberNum.GroupID
			mcInfo.MemberNum = &groupMemberNum.Count
		} else {
			mcInfo.Status = livemulticonnect.NotifyGroupStatusFinish
			mcInfo.JoinType = livemulticonnect.NotifyGroupJoinTypeInvite
		}

		_, mcInfo.RecentConnected = connectedMembersMap[r.RoomID]

		if match, ok := matchingRoomIDMap[r.RoomID]; ok {
			mcInfo.JoinStatus = livemulticonnect.NotifyGroupJoinStatusPending
			mcInfo.MatchID = &match.ID
		} else {
			mcInfo.JoinStatus = livemulticonnect.NotifyGroupJoinStatusDefault
		}

		r.MultiConnect = mcInfo
		p.rooms[idx] = r
	}
	return nil
}

type roomInfoToReturn struct {
	RoomID          int64                 `json:"room_id"`
	Name            string                `json:"name"`
	Announcement    string                `json:"announcement"`
	CreatorID       int64                 `json:"creator_id"`
	CreatorUsername string                `json:"creator_username"`
	Status          room.SimpleRoomStatus `json:"status"`
	Statistics      statistics            `json:"statistics"`
	CatalogID       int64                 `json:"catalog_id"`
	CatalogName     string                `json:"catalog_name"`
	CoverURL        string                `json:"cover_url"`
	CreatorIconURL  string                `json:"creator_iconurl"`

	MultiConnect *multiConnectInfo `json:"multi_connect,omitempty"`
}

type statistics struct {
	Score          int64  `json:"score"`
	AttentionCount *int64 `json:"attention_count,omitempty"` // 仅主播连线搜索页下发
}

type multiConnectInfo struct {
	Status          int    `json:"status"`
	GroupID         *int64 `json:"group_id,omitempty"`   // 连线组 ID，仅在连线中下发
	RecentConnected bool   `json:"recent_connected"`     // 是否在最近 7 天连线过
	MemberNum       *int   `json:"member_num,omitempty"` // 连线人数，仅在连线中下发
	JoinType        int    `json:"join_type"`            // 加入类型；1: 申请，2: 邀请
	JoinStatus      int    `json:"join_status"`          // 加入状态；0: 未发起申请或邀请，1: 处理中
	MatchID         *int64 `json:"match_id,omitempty"`   // 连线匹配 ID，仅在处理中下发
}

func parseRoomResult(r *room.Simple) roomInfoToReturn {
	result := roomInfoToReturn{
		RoomID:       r.RoomID,
		Name:         r.Name,
		Announcement: r.Announcement,
		CatalogID:    r.CatalogID,
		CatalogName:  r.CatalogName,
	}
	result.CreatorID = r.CreatorID
	result.CreatorUsername = r.CreatorUsername
	if r.Statistics == nil {
		r.Statistics = &room.Statistics{}
	}
	result.Statistics = statistics{
		Score: r.Statistics.Score,
	}
	result.CoverURL = r.CoverURL
	if r.Status == nil {
		r.Status = &room.Status{}
	}
	result.Status = room.SimpleRoomStatus{
		Open: r.Status.Open,
	}
	result.CreatorIconURL = r.CreatorIconURL
	return result
}
