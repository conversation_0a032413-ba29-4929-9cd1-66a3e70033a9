package sticker

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/messagelimit"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestActionStickerTabs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	c := handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil)
	resp, err := ActionStickerTabs(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(1, resp.(*tabsResp).Tabs[0].PackageID)
	assert.Equal("1311fd553dad9a1b7f2aa76614c5f0eb", resp.(*tabsResp).Tabs[0].Version)
}

func TestNewTabsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil)
	param, err := newTabsParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.r)
}

func TestTabsParam_tabs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	param := &tabsParam{
		c:   handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil),
		r:   &room.Room{Helper: room.Helper{RoomID: 223344}},
		now: goutil.TimeNow(),
	}
	err := param.tabs()
	require.NoError(err)
	assert.NotEmpty(param.packages)
	assert.Equal("1311fd553dad9a1b7f2aa76614c5f0eb", param.versions[1])
}

func TestTabsParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	param := &tabsParam{
		c: handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil),
		r: &room.Room{Helper: room.Helper{RoomID: 223344}},
		packages: []*livesticker.Package{
			{
				ID:   1,
				Name: "超粉表情包",
				Type: livesticker.TypeSuperFans,
			},
		},
		versions: map[int64]string{
			1: "3bfeaa70694e93ec58fda45f8e40019c",
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"room_id": param.r.RoomID, "user_id": param.c.UserID()})
	require.NoError(err)
	resp, err := param.resp()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Tabs, 1)
	assert.Equal("3bfeaa70694e93ec58fda45f8e40019c", resp.Tabs[0].Version)
	assert.Equal("超粉表情包", resp.Tabs[0].PackageName)
	assert.True(resp.Tabs[0].Locked)
	assert.Empty(resp.Tabs[0].IconFrameURL)

	param = &tabsParam{
		c: handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil),
		r: &room.Room{Helper: room.Helper{RoomID: 223344}},
		packages: []*livesticker.Package{
			{
				ID:   2,
				Name: "用户专属",
				Type: livesticker.TypeUser,
			},
		},
		versions: map[int64]string{
			2: "3bfeaa70694e93ec58fda45f8e40019c",
		},
	}
	resp, err = param.resp()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Tabs, 1)
	assert.Equal("3bfeaa70694e93ec58fda45f8e40019c", resp.Tabs[0].Version)
	assert.Equal("用户专属", resp.Tabs[0].PackageName)
	assert.Equal(storage.ParseSchemeURL(config.Conf.Params.Sticker.UserIconFrame), resp.Tabs[0].IconFrameURL)

	param = &tabsParam{
		c: handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil),
		r: &room.Room{Helper: room.Helper{RoomID: 223344}},
		packages: []*livesticker.Package{
			{
				ID:   3,
				Name: "房间专属",
				Type: livesticker.TypeRoom,
			},
		},
		versions: map[int64]string{
			3: "3bfeaa70694e93ec58fda45f8e40019c",
		},
	}
	resp, err = param.resp()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Tabs, 1)
	assert.Equal("3bfeaa70694e93ec58fda45f8e40019c", resp.Tabs[0].Version)
	assert.Equal("房间专属", resp.Tabs[0].PackageName)
	assert.Equal(storage.ParseSchemeURL(config.Conf.Params.Sticker.RoomIconFrame), resp.Tabs[0].IconFrameURL)

	param = &tabsParam{
		c: handler.NewTestContext(http.MethodGet, "/tabs?room_id=223344", true, nil),
		r: &room.Room{Helper: room.Helper{RoomID: 223344}},
		packages: []*livesticker.Package{
			{
				ID:   1,
				Name: "超粉表情包",
			},
		},
		versions: map[int64]string{
			2: "3bfeaa70694e93ec58fda45f8e40019c",
		},
	}
	resp, err = param.resp()
	require.NoError(err)
	require.Zero(len(resp.Tabs))
}

func TestActionStickerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/sticker/list?package_id=1&room_id=223344", true, nil)
	resp, err := ActionStickerList(c)
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.(*listResp).Stickers, 2)
	assert.EqualValues(1, resp.(*listResp).Stickers[0].StickerID)
	assert.EqualValues(2, resp.(*listResp).Stickers[1].StickerID)
}

func TestNewListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/sticker/list?package_id=1&room_id=223344", true, nil)
	param, err := newListParam(c)
	require.NoError(err)
	assert.EqualValues(1, param.PackageID)
	assert.EqualValues(223344, param.RoomID)
}

func TestListParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &listParam{
		PackageID: 1,
		RoomID:    223344,
		c:         handler.NewTestContext(http.MethodGet, "/sticker/list?package_id=1&room_id=223344", true, nil),
		now:       time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local),
	}
	err := param.check()
	require.NoError(err)
	require.NotNil(param.pkg)

	param = &listParam{
		PackageID: 11111111,
		RoomID:    223344,
	}
	err = param.check()
	assert.EqualError(err, "表情包不存在")

	param = &listParam{
		PackageID: 1,
		RoomID:    18113499,
		c:         handler.NewTestContext(http.MethodGet, "/sticker/list?package_id=1&room_id=223344", true, nil),
	}
	err = param.check()
	assert.EqualError(err, "暂时无法使用该表情")
}

func TestListParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &listParam{
		PackageID: 1,
		now:       time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local),
		pkg:       &livesticker.Package{ID: 1, Name: "超粉专属表情", Type: livesticker.TypeSuperFans},
	}
	resp, err := param.resp()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(1, resp.PackageID)
	require.Len(resp.Stickers, 2)
	assert.EqualValues(1, resp.Stickers[0].StickerID)
	assert.EqualValues(2, resp.Stickers[1].StickerID)
	assert.Equal("超粉专属表情", resp.PackageName)
}

func TestActionStickerSend(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	if r.Status.Open != room.StatusOpenTrue {
		r, err = room.Update(r.RoomID, bson.M{"status.open": room.StatusOpenTrue})
		require.NoError(err)
	}

	c := handler.NewTestContext(http.MethodPost, "/send", true, handler.M{
		"sticker_id": 1,
		"package_id": 1,
		"room_id":    r.RoomID,
	})
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"room_id": r.RoomID, "user_id": c.UserID()},
		bson.M{"$set": bson.M{"super_fan.expire_time": goutil.TimeNow().Add(time.Hour).Unix()}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	require.NoError(blocklist.Remove(r.CreatorID, c.UserID()))
	now := goutil.TimeNow()
	err = service.Redis.Del(
		messagelimit.KeyRoomMessageLimit(r.RoomID, now),
		messagelimit.KeyUserMessageLimit(c.UserID(), now),
		messagelimit.KeyUserMessageRepeatLimit(c.UserID(), now),
	).Err()
	require.NoError(err)

	resp, err := ActionStickerSend(c)
	require.NoError(err)
	require.NotNil(resp)
}

func TestNewSendParam(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/send", true, handler.M{
		"sticker_id": 1,
		"package_id": 1,
		"room_id":    223344,
	})
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(c.UserID())).Err())
	param, err := newSendParam(c)
	require.NoError(err)
	require.NotNil(param)
}

func TestSendParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(223344)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	// 保证黑名单缓存总是存在
	require.NoError(service.LRURedis.SAdd(blocklist.KeyUserBlock(r.CreatorID), 0).Err())
	userLevel1, err := liveuser.FindOneSimple(bson.M{"contribution": 0}, nil)
	require.NoError(err)
	require.NotNil(userLevel1)

	param := &sendParam{
		RoomID:    testRoomID,
		c:         handler.NewTestContext(http.MethodPost, "/send", true, nil),
		PackageID: 1,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID, "user_id": param.c.UserID()})
	require.NoError(err)
	_, err = room.Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": bson.M{"status.open": room.StatusOpenTrue}})
	require.NoError(err)
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(param.c.UserID())).Err())
	require.NoError(blocklist.Remove(r.CreatorID, param.c.UserID()))
	err = param.check()
	assert.EqualError(err, "开通超粉才可使用哦")

	param = &sendParam{
		PackageID: 1,
		StickerID: 1,
		RoomID:    testRoomID,
		c:         handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}
	param.c.User().ID = userLevel1.UserID()
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID, "user_id": param.c.UserID()},
		bson.M{"$set": bson.M{"super_fan.expire_time": goutil.TimeNow().Add(time.Hour).Unix()}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(param.c.UserID())).Err())
	require.NoError(blocklist.Remove(r.CreatorID, param.c.UserID()))
	err = param.check()
	require.NoError(err)

	param = &sendParam{
		RoomID: testRoomID,
		c:      handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}
	param.c.User().ID = userLevel1.UserID()
	err = param.check()
	assert.EqualError(err, "操作太快啦，稍后再试吧~")

	param = &sendParam{
		RoomID: testRoomID,
		c:      handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(param.c.UserID())).Err())
	require.NoError(service.LRURedis.SAdd(blocklist.KeyUserBlock(r.CreatorID), param.c.UserID()).Err())
	err = param.check()
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")

	param = &sendParam{
		PackageID: 111111111111,
		StickerID: 1,
		RoomID:    testRoomID,
		c:         handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(param.c.UserID())).Err())
	require.NoError(blocklist.Remove(param.RoomID, param.c.UserID()))
	err = param.check()
	assert.EqualError(err, "表情包不存在")

	param = &sendParam{
		PackageID: 1,
		StickerID: 1111111111,
		RoomID:    testRoomID,
		c:         handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{
			"room_id": param.RoomID,
			"user_id": param.c.UserID(),
		},
		bson.M{
			"$set": bson.M{"super_fan.expire_time": goutil.TimeNow().AddDate(0, 0, 1).Unix()},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	require.NoError(service.Redis.Del(keys.LockStickerSend1.Format(param.c.UserID())).Err())
	err = param.check()
	assert.EqualError(err, "暂时无法使用该表情")
}

func TestSendParam_sendMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock("im://broadcast", func(interface{}) (interface{}, error) {
		count++
		return nil, nil
	})
	defer cleanup()

	param := &sendParam{
		c:   handler.NewTestContext(http.MethodPost, "/send", true, nil),
		r:   &room.Room{OID: primitive.NewObjectID(), Helper: room.Helper{RoomID: 223344}},
		u:   &liveuser.Simple{UID: 123456},
		pkg: &livesticker.Package{ID: 1, Name: "test"},
		s:   &livesticker.LiveSticker{ID: 1, Name: "test"},
	}
	now := goutil.TimeNow()
	err := service.Redis.Del(
		messagelimit.KeyRoomMessageLimit(param.r.RoomID, now),
		messagelimit.KeyUserMessageLimit(param.u.UserID(), now),
		messagelimit.KeyUserMessageRepeatLimit(param.u.UserID(), now),
	).Err()
	require.NoError(err)

	// msg normal
	resp, err := param.sendMsg()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(param.s.ID, resp.Sticker.StickerID)
	assert.Equal(param.pkg.ID, resp.Sticker.PackageID)
	assert.NotEmpty(resp.MsgID)
	assert.Equal(fmt.Sprintf("[%s]", param.s.Name), resp.Message)
	assert.Equal(1, count)

	// msg limit
	resp, err = param.sendMsg()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(1, count)

	// msg ignore
	param.r = &room.Room{
		OID: primitive.NewObjectID(),
		Helper: room.Helper{
			RoomID: 223344,
			Config: &room.Config{
				MessageLimit: &room.MessageLimit{Rate: 1},
			},
		},
	}
	resp, err = param.sendMsg()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(1, count)
}

func TestSendParam_incrRoomMsgCount(t *testing.T) {
	require := require.New(t)

	testRoomID := int64(223344)
	key := keys.KeyRoomsMeta1.Format(testRoomID)
	require.NoError(service.Redis.Del(key).Err())

	param := &sendParam{
		r: &room.Room{Helper: room.Helper{RoomID: 223344}},
	}
	param.incrRoomMsgCount()

	count, err := service.Redis.HGet(key, "message_count").Result()
	require.NoError(err)
	require.Equal("1", count)
}

func TestSendParam_incrUserMsgCount(t *testing.T) {
	require := require.New(t)

	when := time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return when
	})
	defer goutil.SetTimeNow(nil)

	testUserID := int64(123456)

	config.Conf.AB["new_usermeta_msg_count_time"] = when.Unix()
	key := keys.KeyUserMetaMessageCount2.Format(
		goutil.BeginningOfDay(goutil.TimeNow()).Format(util.TimeFormatYMDWithNoSpace),
		testUserID,
	)
	require.NoError(service.Redis.Del(key).Err())

	param := &sendParam{
		u: &liveuser.Simple{UID: testUserID},
	}
	param.incrUserMsgCount()

	count, err := service.Redis.Get(key).Result()
	require.NoError(err)
	require.Equal("1", count)
}

func TestSendParam_addRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID    = int64(223344)
		testCreatorID = int64(123456789)
		testUserID    = int64(123456)
		now           = goutil.TimeNow()

		userWeekKey = usersrank.Key(usersrank.TypeWeek, now)

		onceMsgkey   = keys.KeyRoomsRankPointMessageLock2.Format(testUserID, now.Format(util.TimeFormatYMDWithNoSpace))
		onceMsgfield = fmt.Sprintf("messagelock_%d", testRoomID)
	)
	require.NoError(service.Redis.HDel(onceMsgkey, onceMsgfield).Err())
	require.NoError(service.Redis.ZRem(userWeekKey, strconv.FormatInt(testCreatorID, 10)).Err())

	param := &sendParam{
		r: &room.Room{Helper: room.Helper{RoomID: testRoomID, CreatorID: testCreatorID}},
		u: &liveuser.Simple{UID: testUserID},
	}
	param.addRevenue()

	source, err := service.Redis.ZScore(userWeekKey, strconv.FormatInt(testCreatorID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(source, 1)
}
