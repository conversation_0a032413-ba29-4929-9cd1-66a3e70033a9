package chatroom

import (
	"errors"
	"fmt"
	"html"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifteffects"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userdiygifts"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type giftSendResp struct {
	User    *liveuser.Simple        `json:"user"`
	Bubble  *bubble.Simple          `json:"bubble,omitempty"`
	Balance *utils.BalanceAfterSend `json:"balance"`
	Combo   *livegifts.Combo        `json:"combo,omitempty"`
}

type diyGiftSendParam struct {
	GiftID     int64                  `form:"gift_id" json:"gift_id"`
	RoomID     int64                  `form:"room_id" json:"room_id"`
	FromRoomID int64                  `form:"from_room_id" json:"from_room_id"`
	Words      string                 `form:"words" json:"words"`
	Avatars    diyGiftSendAvatars     `form:"avatars" json:"avatars"`
	Dress      []diygifteffects.Dress `form:"dress" json:"dress"`

	c       *handler.Context
	uc      mrpc.UserContext
	userCtx userapi.UserContext

	r        *room.Room
	fromRoom *room.Room

	user      *liveuser.Simple
	uv        *vip.UserVip
	bubble    *bubble.Simple
	roomMedal *livemedal.LiveMedal // 当前房间的粉丝勋章

	g        *gift.Gift
	diygift  *diygifts.DiyGift
	uniqueID string
	effect   *diygifteffects.DiyGiftEffect

	userDiyGift    *userdiygifts.UserDiyGift
	lg             *livegifts.LiveGift
	broadcastElems []*userapi.BroadcastElem
}

type diyGiftSendAvatars struct {
	Creator bool `form:"creator" json:"creator"`
	User    bool `form:"user" json:"user"`
}

func newDiyGiftSendParam(c *handler.Context) (*diyGiftSendParam, error) {
	var param diyGiftSendParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.GiftID <= 0 ||
		param.FromRoomID < 0 || param.FromRoomID == param.RoomID {
		return nil, actionerrors.ErrParams
	}

	param.c = c
	param.uc = c.UserContext()
	param.userCtx = userapi.NewUserContext(c)
	param.Words = strings.TrimSpace(param.Words)

	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.FromRoomID > 0 {
		param.fromRoom, err = room.Find(param.FromRoomID, &room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.fromRoom == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
	}
	userID := c.UserID()
	if param.r.CreatorID == userID {
		return nil, actionerrors.ErrParamsMsg("无法给自己的直播间送礼物")
	}
	// 判断是不是礼物房
	if param.r.Limit != nil {
		return nil, actionerrors.NewErrForbidden("本直播间内无法赠送该礼物")
	}
	// NOTICE: 猫耳娘的零钱袋是活动发奖账号，不受黑名单的限制
	if userID != userstatus.MaoerWalletUserID {
		// 被主播拉黑无法送礼
		blocked, err := blocklist.IsBlocked(param.r.CreatorID, userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return nil, actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
		}
	}
	// 判断是否允许跨直播送礼
	if err = checkAllowCrossSend(param.FromRoomID, param.RoomID); err != nil {
		return nil, err
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": userID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	_, uv, err := userstatus.UserGeneral(userID, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}
	param.bubble, err = userappearance.FindMessageBubble(param.user.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 查询当前房间粉丝勋章
	if param.r.Medal != nil {
		param.roomMedal, err = livemedal.FindOwnedMedal(param.user.UserID(),
			param.r.RoomID, livemedal.FindOptions{OnlyMedal: true},
		)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	err = param.findGift()
	if err != nil {
		return nil, err
	}
	err = param.findDiyInfo()
	if err != nil {
		return nil, err
	}
	return &param, nil
}

// TODO: 合并使用 giftsend 文件内的 findgift 方法
func (param *diyGiftSendParam) findGift() error {
	var err error
	param.g, err = gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.g == nil || !gift.AbleToSend(param.g.Type) {
		return actionerrors.ErrNotFound("无法找到指定礼物")
	}

	// 检查赠送资格
	// 贵族资格，优先判断用户定制礼物的贵族要求
	if !param.g.OwnedByVIP(param.uv) {
		return actionerrors.NewErrForbidden("无法购买当前贵族礼物")
	}
	// 用户定制礼物
	if !param.g.OwnedByUser(param.user.UserID(), param.user.Level()) {
		return actionerrors.ErrNotFound("无法找到指定礼物")
	}
	// 房间定制礼物
	if !param.g.OwnedByRoom(param.r.RoomID) {
		return actionerrors.ErrNotFound("无法找到指定礼物")
	}
	// 粉丝礼物
	if !param.g.OwnedByFans(param.roomMedal) {
		return actionerrors.NewErrForbidden("无法购买当前粉丝礼物")
	}

	return nil
}

func (param *diyGiftSendParam) findDiyInfo() error {
	var err error
	param.diygift, err = diygifts.FindDiyGiftByGiftID(param.g.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.diygift == nil {
		return actionerrors.ErrNotFound("该礼物无法定制")
	}
	param.userDiyGift = &userdiygifts.UserDiyGift{
		UserID: param.user.UserID(),
		RoomID: param.r.RoomID,
		GiftID: param.g.GiftID,
		DiyOID: param.diygift.OID,
		Words:  param.Words,
		Dress:  make([]userdiygifts.Dress, 0, len(param.Dress)),
	}

	err = param.checkWords()
	if err != nil {
		return err
	}

	if param.Avatars.Creator {
		param.userDiyGift.IconConfig.Set(userdiygifts.IconConfigCreator)
	}
	if param.Avatars.User {
		param.userDiyGift.IconConfig.Set(userdiygifts.IconConfigUser)
	}
	if param.diygift.Avatars == nil && param.userDiyGift.IconConfig != 0 {
		return actionerrors.NewErrForbidden("该礼物不支持头像")
	}

	dressTypeSlot := make(map[int]int, len(param.diygift.DressTypes))
	for i := range param.Dress {
		d := userdiygifts.Dress{
			Type: param.Dress[i].Type,
		}
		dressTypeSlot[d.Type]++

		d.DressOID, err = primitive.ObjectIDFromHex(param.Dress[i].DressID)
		if err != nil {
			return actionerrors.ErrParamsMsg("装扮配置错误")
		}
		param.userDiyGift.Dress = append(param.userDiyGift.Dress, d)
	}
	if len(dressTypeSlot) != len(param.diygift.DressTypes) {
		return actionerrors.ErrParamsMsg("装扮配置错误")
	}
	for _, d := range param.diygift.DressTypes {
		if dressTypeSlot[d.Type] != 1 {
			return actionerrors.ErrParamsMsg("装扮配置错误")
		}
	}
	if len(param.Dress) == 0 {
		return nil
	}
	param.uniqueID = diygifteffects.UniqueID(param.Dress)
	param.effect, err = diygifteffects.FindValidEffect(param.g.GiftID, param.uniqueID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.effect == nil {
		return actionerrors.ErrNotFound("未找到对应装扮")
	}
	if param.effect.Effect == "" || param.effect.WebEffect == "" {
		return actionerrors.NewErrServerInternal(errors.New("装扮配置异常"), nil)
	}
	return nil
}

func (param *diyGiftSendParam) checkWords() error {
	if param.Words == "" {
		return nil
	}

	if param.diygift.Words == nil {
		return actionerrors.NewErrForbidden("该礼物不支持赠言")
	}

	if util.UTF8Width(param.Words) > 20 {
		return actionerrors.NewErrForbidden("赠言最多 10 个汉字或 20 个字母")
	}

	uuid, err := makeOSUUID(param.c.Equip().OS)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	isRoomAdmin, err := livemembers.IsRoomAdmin(param.r.OID, param.user.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	input := gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:    param.c.UserID(),
			EquipID:   param.c.EquipID(),
			IP:        param.c.ClientIP(),
			API:       param.c.Request().URL.Path,
			UserAgent: param.c.UserAgent(),
			Referer:   param.c.Request().Referer(),
			Content:   param.Words,
		},
		RoomID:        param.RoomID,
		RoomCatalogID: param.r.CatalogID,
		RoomCreatorID: param.r.CreatorID,
		MsgID:         uuid,
		UserIsAdmin:   isRoomAdmin,
		UserHasMedal:  param.roomMedal != nil,
		Scene:         gaia.IMSceneDiyGift,
	}
	result, err := userapi.CheckTextIM(param.c.UserContext(), input)
	if err != nil {
		return err
	}
	if result.NotPass {
		return actionerrors.NewErrForbidden("赠言内容含有违规信息")
	}
	if result.HasLabelEvil {
		return actionerrors.NewErrForbidden("赠言内容含有敏感信息")
	}
	return nil
}

func (param *diyGiftSendParam) send() (*giftSendResp, error) {
	apiResp, err := userapi.SendGift(param.user.UserID(),
		param.r.CreatorID, userapi.Gift{
			ID:    param.g.GiftID,
			Title: param.g.Name,
			Price: param.g.Price,
			Num:   1,
		}, param.uv != nil, param.r.Status.OpenLogID, param.userCtx)
	if err != nil {
		return nil, err
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.user, param.bubble).SetGift(param.g, 1).
		SetRoomOpenStatus(param.r.IsOpen())
	_, err = livegifts.UpdateSave(param.lg, nil, primitive.NilObjectID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = param.userDiyGift.Insert()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	goutil.Go(func() {
		param.addRevenueRank()
		param.addPK()
		param.addMedalPoint()
		param.addMultiConnectScore()
		param.addUserContribution()
		param.activatedGiftWall()
		param.buildIMMessage()
		param.broadcast()

		param.addActivity()
		param.addLiveShow()
		param.addHighnessSpend()
		param.addRoomPaidUser()
	})
	resp := &giftSendResp{
		User:   param.user,
		Bubble: param.bubble,
		Balance: &utils.BalanceAfterSend{
			Balance:                apiResp.Balance,
			LiveNobleBalance:       apiResp.LiveNobleBalance,
			LiveNobleBalanceStatus: util.BoolToInt(param.uv != nil),
		},
	}
	return resp, nil
}

func (param *diyGiftSendParam) addRevenueRank() {
	score := param.lg.Price
	if score == 0 {
		return
	}
	userID := param.user.UserID()
	err := roomsrank.AddRevenue(param.r.RoomID, userID, score, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddGiftRevenue(userID, param.r.OID, param.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = param.r.ReceiveGift(int(param.lg.GiftNum), param.lg.GiftPrice)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *diyGiftSendParam) addPK() {
	score, freeScore := param.g.PKScores(int(param.lg.GiftNum))
	elems, err := livepk.AddPKScore(param.r, param.user.UserID(), score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *diyGiftSendParam) addMedalPoint() {
	if param.r.Medal == nil {
		return
	}
	medalPoint := param.g.MedalPoint(1) // diy 礼物只会送出一个
	if medalPoint == 0 {
		return
	}
	medalParam := livemedal.AddPointParam{
		RoomOID:    param.r.OID,
		RoomID:     param.r.RoomID,
		CreatorID:  param.r.CreatorID,
		FromRoomID: param.FromRoomID,
		UserID:     param.user.UserID(),
		UV:         param.uv,
		MedalName:  param.r.Medal.Name,
		Type:       livemedal.TypeGiftAddMedalPoint,
		Source:     livemedal.ChangeSourceGift,
		PointAdd:   medalPoint,
		Scene:      livemedalpointlog.SceneTypePayGift,
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.user,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *diyGiftSendParam) addMultiConnectScore() {
	if !param.r.IsMultiConnect() {
		return
	}
	elems, err := livemulticonnect.ScoreHelper{
		Room: param.r,
		Gift: param.g,
		Num:  param.lg.GiftNum,
	}.AddScore()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *diyGiftSendParam) addUserContribution() {
	pointAdd := param.lg.Price * 10 // 1 钻石 = 10 经验
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.user.UserID(),
		param.RoomID, param.r.CreatorUsername, userstatus.FromGiftSend, param.uv)
	if param.fromRoom != nil {
		addParam.SetFromRoom(param.fromRoom.RoomID, param.fromRoom.CreatorUsername)
	}
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *diyGiftSendParam) activatedGiftWall() {
	notifyElem, err := giftwall.ActiveGift(param.r, param.user.UserID(), param.lg.GiftID, param.lg.Price, 1)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

func (param *diyGiftSendParam) buildIMMessage() {
	// 房间消息
	rm := param.lg.RoomMessage()
	if param.Words != "" {
		rm.Gift.SetEffectWords(&gift.EffectOptionWords{
			Text: fmt.Sprintf(`<font color="%s">%s</font>`, param.diygift.Words.TextColor,
				html.EscapeString(param.Words)),
			ImageURL: storage.ParseSchemeURL(param.diygift.Words.Image),
		})
	}
	if param.Avatars.Creator {
		rm.Gift.SetEffectCreatorIconURL(param.r.CreatorIconURL)
	}
	if param.Avatars.User {
		rm.Gift.SetEffectUserIconURL(param.user.IconURL)
	}
	if param.effect != nil {
		rm.Gift.WebEffectURL = storage.ParseSchemeURLs(param.effect.WebEffect)
		if param.effect.OldEffect != "" {
			rm.Gift.EffectURL = storage.ParseSchemeURLs(param.effect.OldEffect)
			rm.Gift.NewEffectURL = storage.ParseSchemeURLs(param.effect.Effect)
		} else {
			rm.Gift.EffectURL = storage.ParseSchemeURLs(param.effect.Effect)
		}
	}

	sendNotify := param.g.AlwaysNotify() || param.g.Price >= gift.ComboNotifyMinPrice

	param.broadcastElems = append(param.broadcastElems,
		param.lg.BuildBroadcastMessage(!sendNotify && param.r.FilterGiftMessage(), rm),
	)
	if param.FromRoomID != 0 {
		crm := param.lg.CrossRoomMessage(param.FromRoomID, param.r)
		crm.Gift = rm.Gift
		param.broadcastElems = append(param.broadcastElems,
			param.lg.BuildCrossRoomBroadcastMessage(param.FromRoomID, param.r, crm),
		)
	}

	// 全站飘屏
	if !sendNotify {
		// 未设置成总是飘屏和价值不够
		return
	}
	nb := gift.NotifyBuilder{
		RoomID:          param.RoomID,
		CreatorUsername: param.r.CreatorUsername,
		User:            param.user,
		NotifyGift:      rm.Gift,
		Gift:            param.g,
		GiftNum:         int(param.lg.GiftNum),
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  param.RoomID,
		Payload: nb.Build(),
	})
}

func (param *diyGiftSendParam) broadcast() {
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *diyGiftSendParam) addActivity() {
	box.SendQuestMessage(param.r.RoomID, param.r.CreatorID, box.QuestTypeGift, param.lg.Price)
	r := rank.
		NewSyncParam(param.r.RoomID, param.user.UserID(), param.r.CreatorID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGift(param.g, int(param.lg.GiftNum))
	r.AddRankPoint()
	r.SendLiveActivity(param.uc)
}

func (param *diyGiftSendParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.r.RoomID, param.user.UserID(), param.r.CreatorID).
		SetGift(param.g.GiftID, param.g.Price, int(param.lg.GiftNum)).
		Sync()
}

func (param *diyGiftSendParam) addHighnessSpend() {
	utils.SendHighnessSpend(param.user.UserID(), param.lg.Price)
}

func (param *diyGiftSendParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.user.UserID(), param.r.Status.OpenTime)
}

// ActionDiyGiftSend 赠送 diy 礼物
func ActionDiyGiftSend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDiyGiftSendParam(c)
	if err != nil {
		return nil, err
	}

	return param.send()
}
