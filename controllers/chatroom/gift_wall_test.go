package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGiftWallReward(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testShowGiftIDs := []int64{1000, 2000, 3000}
	testRoomID := int64(223344)
	testAppearanceID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = giftwall.PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()},
			"end_time":   bson.M{"$gt": now.Unix()},
		})
	require.NoError(err)
	_, err = appearance.Collection().DeleteOne(ctx, bson.M{"id": testAppearanceID})
	require.NoError(err)

	t.Run("测试没有配置的情况", func(t *testing.T) {
		t.Run("测试网页请求接口", func(t *testing.T) {
			c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
			c.User().ID = 100

			resp, err := ActionGiftWallReward(c)
			require.NoError(err)
			assert.Equal(rewardListResp{Rewards: []*rewardListItemNew{}}, resp)
		})

		t.Run("测试客户端请求接口", func(t *testing.T) {
			c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
			c.User().ID = 100
			c.Equip().FromApp = true
			c.Equip().OS = goutil.IOS

			resp, err := ActionGiftWallReward(c)
			require.NoError(err)
			assert.Equal(rewardListResp{Rewards: []*rewardListItemNew{}}, resp)
		})
	})

	service.Cache5Min.Flush()

	res, err := giftwall.PeriodCollection().InsertMany(ctx, []interface{}{
		&giftwall.Period{
			StartTime:   now.Unix(),
			EndTime:     now.Add(1 * time.Hour).Unix(),
			ShowGiftIDs: testShowGiftIDs,
			Type:        giftwall.PeriodTypeNormal,
			Rewards: []*giftwall.RewardInfo{
				{
					Threshold: 1,
					Type:      giftwall.RewardTypeCustomGift,
					ElementID: 1,
				},
				{
					Threshold: 2,
					Type:      giftwall.RewardTypeCustomGift,
					ElementID: 2,
				},
				{
					Threshold: 3,
					Type:      giftwall.RewardTypeAppearance,
					ElementID: 1000,
				},
				{
					Threshold: 4,
					Type:      giftwall.RewardTypeSticker,
					ElementID: 234456,
				},
			},
		},
		&giftwall.Period{
			StartTime:   now.Unix(),
			EndTime:     now.Add(1 * time.Hour).Unix(),
			ShowGiftIDs: testShowGiftIDs,
			Type:        giftwall.PeriodTypePremium,
			CreatorIDs:  []int64{223344},
			Rewards: []*giftwall.RewardInfo{
				{
					Threshold: 1,
					Type:      giftwall.RewardTypeCustomGift,
					ElementID: 1,
				},
				{
					Threshold: 2,
					Type:      giftwall.RewardTypeCustomGift,
					ElementID: 2,
				},
				{
					Threshold: 3,
					Type:      giftwall.RewardTypeAppearance,
					ElementID: 1000,
				},
				{
					Threshold: 4,
					Type:      giftwall.RewardTypeSticker,
					ElementID: 234456,
				},
			},
		},
	})
	require.NoError(err)
	p := livesticker.LiveSticker{
		ID:   234456,
		Name: "测试表情",
		Icon: "oss://sticker_package/superfans/icon.png",
	}
	require.NoError(service.LiveDB.Create(&p).Error)
	defer func() {
		require.NoError(service.LiveDB.Table(p.TableName()).Delete("", "id = ?", p.ID).Error)
	}()
	a := &appearance.Appearance{
		ID:             1000,
		Name:           "测试查询外观模版",
		Type:           appearance.TypeCardFrame,
		Icon:           "oss://live/cardframes/icons/test.webp",
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &appearance.MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime: now.Unix(),
	}
	_, err = appearance.Collection().InsertOne(ctx, a)
	require.NoError(err)

	// 测试获取普通礼物墙数据
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
	c.User().ID = 100
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	resp, err := ActionGiftWallReward(c)
	require.NoError(err)
	data, ok := resp.(rewardListResp)
	require.True(ok)
	require.Len(data.Rewards, 4)
	assert.EqualValues(giftwall.RewardTypeCustomGift, data.Rewards[0].Type)
	assert.EqualValues(giftwall.RewardTypeCustomGift, data.Rewards[1].Type)
	assert.EqualValues(giftwall.RewardTypeAppearance, data.Rewards[2].Type)
	assert.EqualValues(giftwall.RewardTypeSticker, data.Rewards[3].Type)
	assert.EqualValues(1, data.Rewards[0].ElementID)
	assert.EqualValues(2, data.Rewards[1].ElementID)
	assert.EqualValues(1000, data.Rewards[2].ElementID)
	assert.EqualValues(234456, data.Rewards[3].ElementID)
	assert.EqualValues("直播间专属礼物【红包礼物 1】", data.Rewards[0].ElementName)
	assert.EqualValues("直播间专属礼物【红包礼物 2】", data.Rewards[1].ElementName)
	assert.EqualValues("名片框【测试查询外观模版】", data.Rewards[2].ElementName)
	assert.EqualValues("直播间专属表情【测试表情】", data.Rewards[3].ElementName)
	assert.Equal("https://static-test.missevan.com/live/cardframes/icons/test.webp", data.Rewards[2].ElementIconURL)
	assert.Equal("https://static-test.missevan.com/sticker_package/superfans/icon.png", data.Rewards[3].ElementIconURL)
	assert.EqualValues(1, data.Rewards[0].Threshold)
	assert.EqualValues(2, data.Rewards[1].Threshold)
	assert.EqualValues(3, data.Rewards[2].Threshold)
	assert.EqualValues(4, data.Rewards[3].Threshold)
	assert.Zero(data.Rewards[0].Received, data.Rewards[1].Received, data.Rewards[2].Received, data.Rewards[3].Received)

	// 测试用户不在白名单中
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=2&type=1", true, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	resp, err = ActionGiftWallReward(c)
	require.NoError(err)
	assert.Equal(rewardListResp{Rewards: []*rewardListItemNew{}}, resp)

	// 测试获取甄选礼物墙数据
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344&type=1", true, nil)
	c.User().ID = 100
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	resp, err = ActionGiftWallReward(c)
	require.NoError(err)
	data, ok = resp.(rewardListResp)
	require.True(ok)
	require.Len(data.Rewards, 4)
	assert.EqualValues(giftwall.RewardTypeCustomGift, data.Rewards[0].Type)
	assert.EqualValues(giftwall.RewardTypeCustomGift, data.Rewards[1].Type)
	assert.EqualValues(giftwall.RewardTypeAppearance, data.Rewards[2].Type)
	assert.EqualValues(giftwall.RewardTypeSticker, data.Rewards[3].Type)
	assert.EqualValues(1, data.Rewards[0].ElementID)
	assert.EqualValues(2, data.Rewards[1].ElementID)
	assert.EqualValues(1000, data.Rewards[2].ElementID)
	assert.EqualValues(234456, data.Rewards[3].ElementID)
	assert.EqualValues("直播间专属礼物【红包礼物 1】", data.Rewards[0].ElementName)
	assert.EqualValues("直播间专属礼物【红包礼物 2】", data.Rewards[1].ElementName)
	assert.EqualValues("名片框【测试查询外观模版】", data.Rewards[2].ElementName)
	assert.EqualValues("直播间专属表情【测试表情】", data.Rewards[3].ElementName)
	assert.Equal("https://static-test.missevan.com/live/cardframes/icons/test.webp", data.Rewards[2].ElementIconURL)
	assert.Equal("https://static-test.missevan.com/sticker_package/superfans/icon.png", data.Rewards[3].ElementIconURL)
	assert.EqualValues(1, data.Rewards[0].Threshold)
	assert.EqualValues(2, data.Rewards[1].Threshold)
	assert.EqualValues(3, data.Rewards[2].Threshold)
	assert.EqualValues(4, data.Rewards[3].Threshold)
	assert.Zero(data.Rewards[0].Received, data.Rewards[1].Received, data.Rewards[2].Received, data.Rewards[3].Received)

	_, err = giftwall.RecordCollection().InsertMany(ctx, []interface{}{
		&giftwall.ActivatedRecord{
			RoomID:     testRoomID,
			ShowGiftID: 3000,
			PeriodOID:  res.InsertedIDs[0].(primitive.ObjectID),
		},
		&giftwall.ActivatedRecord{
			RoomID:     testRoomID,
			ShowGiftID: 2000,
			PeriodOID:  res.InsertedIDs[0].(primitive.ObjectID),
		},
		&giftwall.ActivatedRecord{
			RoomID:     testRoomID,
			ShowGiftID: 1000,
			PeriodOID:  res.InsertedIDs[0].(primitive.ObjectID),
		},
		&giftwall.ActivatedRecord{
			RoomID:     testRoomID,
			ShowGiftID: 4000,
			PeriodOID:  res.InsertedIDs[0].(primitive.ObjectID),
		},
	})
	require.NoError(err)
	// 测试完成任务获得奖励
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
	c.User().ID = 100
	resp, err = ActionGiftWallReward(c)
	require.NoError(err)
	data, ok = resp.(rewardListResp)
	require.True(ok)
	require.Len(data.Rewards, 4)
	assert.EqualValues(1, data.Rewards[0].Received)
	assert.EqualValues(1, data.Rewards[1].Received)
	assert.EqualValues(1, data.Rewards[2].Received)
	assert.EqualValues(1, data.Rewards[3].Received)
}

func TestGiftWallTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(activatedGift{}, "gift_id", "gift_name", "gift_price", "gift_icon_url", "target_gift_id")
	kc.Check(giftWallRankResp{}, "activated_gift", "activated_num", "data", "my_rank", "sponsor")
}

func TestActionGiftWallRank(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?room_id=-1&gift_id=1", true, nil)
	_, err := ActionGiftWallRank(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/?room_id=1&gift_id=1", true, nil)
	_, err = ActionGiftWallRank(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)
}

var testGiftWallRankRoomID int64 = 1

func createTestGiftWallRankData(periodOID primitive.ObjectID) (func(), error) {
	wallGifts := make([]interface{}, 7)
	for i := 0; i < 7; i++ {
		wallGifts[i] = &giftwall.ActivatedRank{
			CreateTime:   goutil.TimeNow().Unix(),
			ModifiedTime: goutil.TimeNow().Unix(),
			PeriodOID:    periodOID,
			RoomID:       testGiftWallRankRoomID,
			UserID:       int64(i + 1),
			ShowGiftID:   1,
			ActivatedNum: int64(i + 1),
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.RankCollection().InsertMany(ctx, wallGifts)
	if err != nil {
		return nil, err
	}
	return cleanupTestGiftWallRankData, nil
}

func cleanupTestGiftWallRankData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.RankCollection().DeleteMany(ctx, bson.M{"room_id": testGiftWallRankRoomID})
	if err != nil {
		logger.Error(err)
	}
}

func TestActionGiftWallInfo(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?room_id=0", false, nil)
	_, err := ActionGiftWallInfo(c)
	assert.Equal(actionerrors.ErrParams, err)
}

var testShowGiftIDs = []int64{1, 1000, 2000}

func createActionGiftWallInfoData(when time.Time) (*giftwall.Period, func(), error) {
	p := &giftwall.Period{
		CreateTime:   when.Unix(),
		ModifiedTime: when.Unix(),
		StartTime:    when.Add(-time.Minute).Unix(),
		EndTime:      when.Add(time.Minute).Unix(),
		ShowGiftIDs:  testShowGiftIDs,
		Rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1},
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	insertRes, err := giftwall.PeriodCollection().InsertOne(ctx, p)
	if err != nil {
		return nil, nil, err
	}
	p.OID = insertRes.InsertedID.(primitive.ObjectID)
	wallGifts := make([]interface{}, 0, len(testShowGiftIDs))
	for i, id := range testShowGiftIDs {
		wallGifts = append(wallGifts, &giftwall.Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: []int64{int64(i + 1)},
		})
	}
	_, err = giftwall.GiftCollection().InsertMany(ctx, wallGifts)
	if err != nil {
		return p, nil, err
	}

	_, err = giftwall.RecordCollection().InsertOne(ctx, &giftwall.ActivatedRecord{
		RoomID:       3192516,
		PeriodOID:    p.OID,
		ShowGiftID:   testShowGiftIDs[1],
		ActivatedNum: 100,
		Revenue:      50000,
	})
	if err != nil {
		return p, nil, err
	}

	return p, cleanActionGiftWallInfoData, nil
}

func cleanActionGiftWallInfoData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"show_gift_ids": testShowGiftIDs})
	if err != nil {
		logger.Error(err)
	}
	_, err = giftwall.GiftCollection().DeleteMany(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}})
	if err != nil {
		logger.Error(err)
	}
	_, err = giftwall.RecordCollection().DeleteMany(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}})
	if err != nil {
		logger.Error(err)
	}
}

func TestGiftWallRankResp_buildData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res := giftWallRankResp{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: testGiftWallRankRoomID,
			},
		},
		u: &user.User{},
		period: &giftwall.Period{
			OID: primitive.NewObjectID(),
		},
		showGift: &gift.Gift{GiftID: 1},
	}
	require.NoError(res.buildData())
	assert.Empty(res.Data)

	cleanup, err := createTestGiftWallRankData(res.period.OID)
	require.NoError(err)
	defer cleanup()
	require.NoError(res.buildData())
	assert.Len(res.Data, 5)
}

func TestGiftWallRankResp_buildMyRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res := giftWallRankResp{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: testGiftWallRankRoomID,
			},
		},
		u: &user.User{},
		period: &giftwall.Period{
			OID: primitive.NewObjectID(),
		},
		showGift: &gift.Gift{GiftID: 1},
	}

	require.NoError(res.buildMyRank())
	assert.Nil(res.MyRank)

	cleanup, err := createTestGiftWallRankData(res.period.OID)
	require.NoError(err)
	defer cleanup()
	res.u.ID = 1 // 第 7 名
	require.NoError(res.buildData())
	require.NoError(res.buildMyRank())
	require.NotNil(res.MyRank)
	assert.Zero(res.MyRank.Rank)
	assert.EqualValues(3, res.MyRank.RankUp)
	assert.EqualValues(1, res.MyRank.Score)

	res.u.ID = 6 // 第 2 名
	require.NoError(res.buildMyRank())
	require.NotNil(res.MyRank)
	assert.EqualValues(2, res.MyRank.Rank)
	assert.EqualValues(2, res.MyRank.RankUp)
	assert.EqualValues(6, res.MyRank.Score)
}

func TestGiftWallRankResp_buildActivatedInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res := giftWallRankResp{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 2,
			},
		},
		period: &giftwall.Period{
			OID: primitive.NewObjectID(),
		},
		showGift: &gift.Gift{GiftID: 1},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.RecordCollection().UpdateOne(ctx, bson.M{"room_id": 2, "show_gift_id": 1},
		bson.M{"$set": bson.M{
			"room_id":       2,
			"show_gift_id":  1,
			"_period_id":    res.period.OID,
			"activated_num": 99,
		}}, options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = giftwall.GiftCollection().UpdateOne(ctx, bson.M{"show_gift_id": 1},
		bson.M{"$set": bson.M{
			"show_gift_id":   1,
			"target_gift_id": 2,
		}}, options.Update().SetUpsert(true))
	require.NoError(err)
	require.NoError(res.buildActivatedInfo())
	assert.EqualValues(99, res.ActivatedNum)
	assert.EqualValues(2, res.ActivatedGift.TargetGiftID)
}

func TestGiftWallRankResp_buildSponsor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p, clean, err := createActionGiftWallInfoData(goutil.TimeNow())
	require.NoError(err)
	defer clean()

	// 基础测试数据
	resp := giftWallRankResp{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 3192516,
			},
		},
		period:       p,
		giftWallType: giftwall.PeriodTypeNormal,
		showGift: &gift.Gift{
			GiftID: testShowGiftIDs[1],
			Price:  5000,
			Type:   gift.TypeNormal, // 非随机礼物
		},
		Data: []*activatedRankElem{
			{
				Rank:   1,
				Score:  20,
				UserID: 1001, // 榜首用户
			},
		},
		ActivatedGift: &activatedGift{
			GiftPrice: 5000,
		},
	}

	// 测试场景1：用户未登录 = 不显示一键冠名按钮
	resp.u = nil
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	require.NotNil(resp.Sponsor.ShowObtainButton)
	assert.False(*resp.Sponsor.ShowObtainButton)

	// 测试场景2：非随机礼物 + 用户是榜首 = 不显示一键冠名按钮
	resp.u = &user.User{}
	resp.u.ID = 1001 // 榜首用户
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	require.NotNil(resp.Sponsor.ShowObtainButton)
	assert.False(*resp.Sponsor.ShowObtainButton)

	// 测试场景3：非随机礼物 + 用户不是榜首 = 显示一键冠名按钮
	resp.u = &user.User{}
	resp.u.ID = 1002 // 非榜首用户
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	require.NotNil(resp.Sponsor.ShowObtainButton)
	assert.True(*resp.Sponsor.ShowObtainButton)

	// 测试场景4：随机礼物 + 用户不是榜首 = 不显示一键冠名按钮
	resp.showGift.Type = gift.TypeDrawReceive // 随机礼物
	resp.u = &user.User{}
	resp.u.ID = 1002 // 非榜首用户
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	require.NotNil(resp.Sponsor.ShowObtainButton)
	assert.False(*resp.Sponsor.ShowObtainButton)

	// 测试场景5：非随机礼物 + 榜单为空 = 显示一键冠名按钮
	resp.showGift.Type = gift.TypeNormal // 非随机礼物
	resp.Data = []*activatedRankElem{}   // 空榜单
	resp.u = &user.User{}
	resp.u.ID = 1002
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	require.NotNil(resp.Sponsor.ShowObtainButton)
	assert.True(*resp.Sponsor.ShowObtainButton)

	// 测试甄选礼物墙
	resp.giftWallType = giftwall.PeriodTypePremium
	resp.Data = []*activatedRankElem{
		{
			Rank:   1,
			Score:  20,
			UserID: 1001,
		},
	}
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	assert.NotEmpty(resp.Sponsor.DetailImageURL)
	assert.NotEmpty(resp.Sponsor.LabelIconURL)

	// 测试消费不足的情况
	resp.Data[0].Score = 1
	resp.ActivatedGift.GiftPrice = 5000
	require.NoError(resp.buildSponsor())
	require.NotNil(resp.Sponsor)
	assert.NotEmpty(resp.Sponsor.DetailImageURL)
	assert.Empty(resp.Sponsor.LabelIconURL)
}

func TestNewGiftWallInfoResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()
	c := handler.NewTestContext(http.MethodGet, "/?room_id=3192516", false, nil)
	_, err := newGiftWallInfoResp(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	_, clean, err := createActionGiftWallInfoData(goutil.TimeNow())
	require.NoError(err)
	defer clean()
	service.Cache5Min.Flush()
	resp, err := newGiftWallInfoResp(c)
	require.NoError(err)
	assert.NotNil(resp.normalPeriod)
	assert.NotZero(resp.TotalNum)
}

func TestGiftWallInfoResp_buildActivatedInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p, clean, err := createActionGiftWallInfoData(goutil.TimeNow())
	require.NoError(err)
	defer clean()
	service.Cache5Min.Flush()
	resp := giftWallInfoResp{
		roomID:       3192516,
		normalPeriod: p,
	}
	require.NoError(resp.buildActivatedInfo())
	require.Len(resp.ActivatedGifts, 1)
	assert.Equal(testShowGiftIDs[1], resp.ActivatedGifts[0].GiftID)
	assert.Equal(1, resp.ActivatedGifts[0].Promotion)
	require.Len(resp.InactivatedGifts, 2)
	assert.Equal(testShowGiftIDs[0], resp.InactivatedGifts[0].GiftID)
	assert.Equal(testShowGiftIDs[2], resp.InactivatedGifts[1].GiftID)
}

func TestActionGiftWallSponsorList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=-1", true, nil)
	_, err := ActionGiftWallSponsorList(c)
	assert.Equal(actionerrors.ErrParams, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	_, err = giftwall.PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lte": now.Unix()},
		})
	require.NoError(err)

	p := []interface{}{
		giftwall.Period{
			StartTime:   now.Add(-time.Hour).Unix(),
			EndTime:     now.Unix(),
			Type:        giftwall.PeriodTypeNormal,
			ShowGiftIDs: []int64{100, 200},
		},
		giftwall.Period{
			StartTime:   now.Add(-time.Hour).Unix(),
			EndTime:     now.Unix(),
			Type:        giftwall.PeriodTypePremium,
			ShowGiftIDs: []int64{100, 200},
		},
		giftwall.Period{
			StartTime: now.Unix(),
			EndTime:   now.Add(time.Hour).Unix(),
			Type:      giftwall.PeriodTypeNormal,
		},
	}
	periodIDs, err := giftwall.PeriodCollection().InsertMany(ctx, p)
	require.NoError(err)

	// 测试没有冠名记录
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
	res, err := ActionGiftWallSponsorList(c)
	require.NoError(err)
	data, ok := res.(sponsorListResp)
	require.True(ok)
	assert.Len(data.Gifts, 0)
	assert.Len(data.PremiumGifts, 0)

	// 测试获取冠名记录
	_, err = giftwall.SponsorCollection().DeleteMany(ctx, bson.M{"room_id": 223344})
	require.NoError(err)
	r := []interface{}{
		giftwall.Sponsor{
			RoomID:    223344,
			PeriodOID: periodIDs.InsertedIDs[0].(primitive.ObjectID),
			GiftID:    100,
			UserID:    12,
		},
		giftwall.Sponsor{
			RoomID:    223344,
			PeriodOID: periodIDs.InsertedIDs[1].(primitive.ObjectID),
			GiftID:    200,
			UserID:    12,
		},
	}
	_, err = giftwall.SponsorCollection().InsertMany(ctx, r)
	require.NoError(err)

	_, err = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{100, 200}}})
	require.NoError(err)
	gifts := []interface{}{
		gift.Gift{
			GiftID:    100,
			Name:      "test1",
			NameClean: "clean_test1",
			Icon:      "oss://test/icon.png",
		},
		gift.Gift{
			GiftID:    200,
			Name:      "test2",
			NameClean: "clean_test2",
			Icon:      "oss://test/icon.png",
		},
	}
	_, err = gift.Collection().InsertMany(ctx, gifts)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/reward?room_id=223344", true, nil)
	res, err = ActionGiftWallSponsorList(c)
	require.NoError(err)
	data, ok = res.(sponsorListResp)
	require.True(ok)
	require.Len(data.Gifts, 1)
	assert.EqualValues(100, data.Gifts[0].GiftID)
	assert.Equal("test1", data.Gifts[0].GiftName)
	assert.Equal("https://static-test.missevan.com/test/icon.png", data.Gifts[0].GiftIconURL)
	assert.Equal(userInfo{
		UserID:   12,
		Username: "零月",
		IconURL:  "https://static-test.missevan.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png",
	}, data.Gifts[0].User)
	require.Len(data.PremiumGifts, 1)
	assert.EqualValues(200, data.PremiumGifts[0].GiftID)
	assert.Equal("test2", data.PremiumGifts[0].GiftName)
	assert.Equal("https://static-test.missevan.com/test/icon.png", data.PremiumGifts[0].GiftIconURL)
	assert.Equal(userInfo{
		UserID:   12,
		Username: "零月",
		IconURL:  "https://static-test.missevan.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png",
	}, data.PremiumGifts[0].User)
	assert.EqualValues(now.Add(time.Hour).Unix(), data.EndTime)
}
