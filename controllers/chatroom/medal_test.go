package chatroom

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemedalreview"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testMedalUserID = 11223344
	testMedalRoomID = 11223344
)

func TestActionEditMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cancel := mrpc.SetMock(userapi.URIUpdateUserPoint, func(interface{}) (interface{}, error) {
		count++
		return "success", nil
	})
	defer cancel()

	// 钻石不够 30000
	c := handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"999a"}`))
	c.User().ID = 892
	_, err := ActionMedalEdit(c)
	assert.EqualError(err, "该房间暂无法设置勋章")
	c = handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"999a"}`))
	c.User().ID = room.TestLimitedRoomCreatorID
	_, err = ActionMedalEdit(c)
	assert.EqualError(err, "该房间暂无法设置勋章", "受限房间")

	// 首次申请粉丝勋章 不扣除小鱼干
	_, err = room.Update(testMedalRoomID, bson.M{"statistics.revenue": 30000})
	require.NoError(err)
	require.NoError(service.DB.Table(livemedalreview.TableName()).Delete("", "user_id = ?", testMedalUserID).Error)
	c = handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"999b"}`))
	c.User().ID = testMedalUserID
	_, err = ActionMedalEdit(c)
	require.NoError(err)

	// 小鱼干不够 500
	require.NoError(service.DB.Table(livemedalreview.TableName()).
		Where("user_id = ?", testMedalRoomID).Updates(map[string]interface{}{
		"status":        livereview.StatusPassed,
		"modified_time": 123,
	}).Error)
	require.NoError(service.DB.Table(mowangskuser.TableName()).Where("id = ?", testMedalUserID).
		UpdateColumn("point", 499).Error)
	c = handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"999a"}`))
	c.User().ID = testMedalUserID
	_, err = ActionMedalEdit(c)
	assert.Equal(actionerrors.ErrNotEnoughPoint, err)

	// 修改勋章 扣除小鱼干
	require.NoError(service.DB.Table(mowangskuser.TableName()).Where("id = ?", testMedalUserID).
		Update("point", config.Conf.Params.MedalParams.CostPoint).Error)
	c = handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"999a"}`))
	c.User().ID = testMedalUserID
	resp, err := ActionMedalEdit(c)
	require.NoError(err)
	assert.Equal("success", resp)
	assert.Equal(1, count)

	// 修改拒审勋章 不扣除小鱼干
	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ?", 12, livemedalreview.StatusReviewing).
		Update("status", livemedalreview.StatusRefused).Error)
	c = handler.NewTestContext("POST", "/", true, strings.NewReader(`{"name":"不重复"}`))
	resp, err = ActionMedalEdit(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestIsInEditLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	m := livemedalreview.LiveMedalReview{UserID: 9999, RoomID: 22489473, Name: "原子"}
	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ?", 9999, livemedalreview.StatusReviewing).FirstOrCreate(&m).Error)
	p := editMedalParams{UserID: 9999, RoomID: 22489473, Name: "原子"}
	p.c = handler.CreateTestContext(true)
	p.c.C.Request, _ = http.NewRequest("POST", "/", nil)

	assert.EqualError(p.isInEditLimit(), "已有勋章在审核中")
	// mysql 数据查询不存在
	p.UserID = 99999999
	require.NoError(p.isInEditLimit())

	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ?", 9999, livemedalreview.StatusReviewing).
		Updates(map[string]interface{}{"status": livemedalreview.StatusPassed, "modified_time": goutil.TimeNow().Unix()}).Error)
	p.UserID = 9999
	require.EqualError(p.isInEditLimit(), "距离上次勋章修改通过未超过 30 天")

	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ?", 9999, livemedalreview.StatusPassed).
		Updates(map[string]interface{}{"status": livemedalreview.StatusPassed, "modified_time": goutil.TimeNow().AddDate(0, 0, -31).Unix()}).Error)
	require.NoError(p.isInEditLimit())

	// 违禁词
	p.Name = "nmsl"
	assert.EqualError(p.isInEditLimit(), "您申请的勋章名称含有违禁词，请重新输入")
}

func TestCheckMedalName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := editMedalParams{UserID: 99, RoomID: 12}
	p.c = handler.CreateTestContext(true)
	p.c.User().ID = 99
	rightNameList := []string{"小小风", "原A", "A原", "a1原", "原123", "小风12"}
	for _, name := range rightNameList {
		p.Name = name
		err := p.checkMedalName()
		require.NoError(err)
	}

	errorNameList := []string{"原子弹*", "原子弹 ", "*", " "}
	for _, name := range errorNameList {
		p.Name = name
		err := p.checkMedalName()
		assert.EqualError(err, "您申请的勋章名称含有特殊字符，请重新输入")
	}

	errorNameList = []string{"原", "原子弹四五", "原子弹12", "测q2d3", "rrrrrr", "123", "123456", "123abc"}
	for _, name := range errorNameList {
		p.Name = name
		err := p.checkMedalName()
		assert.EqualError(err, "请输入 2-4 个字符的非纯数字内容")
	}

	p.Name = "一二三四"
	err := p.checkMedalName()
	assert.EqualError(err, "您申请的勋章名称汉字字符超过 3 个，请重新输入")
}

func TestActionMedalEditStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 未满足开通条件
	c := handler.NewTestContext("GET", "/", true, nil)
	c.User().ID = 1234567890
	r, err := ActionMedalStatus(c)
	require.NoError(err)
	resp := r.(*medalEditStatusResp)
	assert.Equal(-1, resp.Status)
	// 首次提交申请
	require.NoError(service.DB.Table(livemedalreview.TableName()).Delete("", "user_id = ?", testMedalUserID).Error)

	// 可新开通
	c = handler.NewTestContext("GET", "/", true, nil)
	c.User().ID = testMedalUserID
	r, err = ActionMedalStatus(c)
	require.NoError(err)
	resp = r.(*medalEditStatusResp)
	assert.Equal(0, resp.Status) // 可新提交
	assert.Equal(0, resp.CostPoint)
	// 审核中
	m := livemedalreview.LiveMedalReview{UserID: testMedalUserID, RoomID: testMedalRoomID, ModifiedTime: goutil.TimeNow().AddDate(-1, 0, 0).Unix()}
	require.NoError(service.DB.Where("user_id = ? AND status = ?",
		m.UserID, livemedalreview.StatusReviewing).FirstOrCreate(&m).Error)
	c = handler.NewTestContext("GET", "/", true, nil)
	c.User().ID = testMedalUserID
	r, err = ActionMedalStatus(c)
	require.NoError(err)
	resp = r.(*medalEditStatusResp)
	assert.Equal(1, resp.Status)
	assert.Equal(config.Conf.Params.MedalParams.CostPoint, resp.CostPoint)
	assert.Equal(config.Conf.Params.MedalParams.ContentHTML, resp.Content)
	// 可修改（消耗小鱼干） 新版
	require.NoError(service.DB.Table(m.TableName()).Updates(map[string]interface{}{
		"status":        livemedalreview.StatusPassed,
		"modified_time": 1,
	}).Error)
	r, err = ActionMedalStatus(c)
	require.NoError(err)
	resp = r.(*medalEditStatusResp)
	assert.Equal(2, resp.Status)
	// 拒绝待修改
	m.Status = livemedalreview.StatusRefused
	require.NoError(service.DB.Save(&m).Error)
	r, err = ActionMedalStatus(c)
	require.NoError(err)
	resp = r.(*medalEditStatusResp)
	assert.Equal(3, resp.Status)
	// 不可编辑提交（间隔时间未满 30 天
	m.Status = livemedalreview.StatusPassed
	require.NoError(service.DB.Save(&m).Error)
	r, err = ActionMedalStatus(c)
	require.NoError(err)
	resp = r.(*medalEditStatusResp)
	assert.Equal(4, resp.Status)
}
