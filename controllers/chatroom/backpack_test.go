package chatroom

import (
	"errors"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBackpackTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(backpackSendParam{}, "room_id", "from_room_id", "gift_id", "gift_num")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(backpackSendParam{}, "room_id", "from_room_id", "gift_id", "gift_num")
	kc.Check(backpackSendGiftResp{}, "ok", "gift_id", "remain", "user", "bubble", "message")
}

func TestActionBackpackSend(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("POST", "/backpack/send", true, "gift_num=100&room_id="+roomIDStr)
	_, err := ActionBackpackSend(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", "/backpack/send", true, "gift_id=301&gift_num=100&room_id="+roomIDStr)
	_, err = ActionBackpackSend(c)
	assert.NoError(err)
}

func TestBackpackSendLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})()

	v := url.Values{}
	v.Set("room_id", "1248361")
	v.Set("gift_id", "200")
	v.Set("gift_num", "str")
	c := handler.NewTestContext("POST", "/backpack/send", true, v)
	var param backpackSendParam
	getLoadError := func() error {
		_, err := param.load(c)
		return err
	}
	assert.Equal(actionerrors.ErrParams, getLoadError())
	v.Set("gift_num", "100")
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	assert.Equal(actionerrors.ErrCannotFindRoom, getLoadError())
	v.Set("room_id", strconv.FormatInt(room.TestLimitedRoomID, 10))
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	assert.Equal(actionerrors.NewErrForbidden("本直播间内无法赠送该礼物"),
		getLoadError(), "受限房间")

	v.Set("room_id", roomIDStr)
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = testRoom.CreatorID
	assert.EqualError(getLoadError(), "无法给自己的直播间送礼物")

	v.Set("gift_id", "123456")
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	assert.EqualError(getLoadError(), "无法找到指定礼物")

	v.Set("gift_id", "3")
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	resp, err := param.load(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(util.NewInt64(0), resp.Remain)
	assert.Equal("遭遇到了意想不到的错误哦~", resp.Message)

	v.Set("gift_id", "310")
	v.Set("gift_num", "2")
	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)

	cacheKey := keys.KeyOnlineGifts0.Format()
	service.Cache5s.Set(cacheKey, append(gifts, gift.Gift{GiftID: 310, Type: gift.TypeFree, Attr: 128}), 0)

	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	require.Equal(actionerrors.ErrDisableUseHotCardInCloseRoom, getLoadError())
	service.Cache5s.Flush()

	g, err := gift.FindShowingGiftByGiftID(301)
	require.NoError(err)
	require.NotNil(g)

	v.Set("gift_id", "301")
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.Collection().UpdateOne(ctx, bson.M{"_id": g.OID},
		bson.M{"$set": bson.M{"allowed_nums": []int{1, 10, 100}}})
	require.NoError(err)
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = noble7UserID
	assert.Equal(actionerrors.ErrGiftNum, getLoadError())

	v.Set("gift_num", "100")
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = noble7UserID
	require.NoError(useritems.UnsetGift(noble7UserID, 301))
	resp, err = param.load(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(301), resp.GiftID)
	assert.Equal(util.NewInt64(0), resp.Remain)
	assert.Equal("礼物数量不足哦~", resp.Message)

	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = noble7UserID
	require.NoError(useritems.AddGiftToUsers([]int64{noble7UserID}, g, 10, useritems.SourceNormal, now.Unix(), now.Unix()+1000))
	resp, err = param.load(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(301), resp.GiftID)
	assert.Equal(util.NewInt64(10), resp.Remain)
	assert.Equal("礼物数量不足哦~", resp.Message)

	v.Set("gift_num", "10")
	expTime := now.Add(240 * time.Hour).Unix()
	col := userappearance.Collection()
	a := &userappearance.UserAppearance{
		Type:         appearance.TypeMessageBubble,
		Status:       userappearance.StatusWorn,
		UserID:       3457114,
		AppearanceID: 2000,
		Name:         "测试查询头像框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://show.png",
	}
	_, err = col.UpdateOne(ctx,
		bson.M{"user_id": a.UserID, "appearance_id": a.AppearanceID},
		bson.M{"$set": a}, options.Update().SetUpsert(true))
	require.NoError(err)
	userappearance.ClearCache(a.UserID)
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = noble7UserID
	resp, err = param.load(c)
	require.NoError(err)
	assert.Nil(resp)
	assert.NotNil(param.bubble)

	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = -20200426
	require.NoError(useritems.AddGiftToUsers([]int64{c.User().ID}, g, 10, useritems.SourceNormal, now.Unix(), now.Unix()+1000))
	assert.Equal(actionerrors.ErrCannotFindUser, getLoadError())

	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	testBlockUserID := int64(2222222222)
	key := blocklist.KeyUserBlock(r.CreatorID)
	require.NoError(service.LRURedis.SAdd(key, testBlockUserID).Err())
	defer func() {
		_ = blocklist.Clear(r.CreatorID)
	}()
	v.Set("room_id", strconv.FormatInt(r.RoomID, 10))
	c = handler.NewTestContext("POST", "/backpack/send", true, v)
	c.User().ID = testBlockUserID
	_, err = param.load(c)
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")
}

func TestBackpackSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g, err := gift.FindShowingGiftByGiftID(301)
	require.NoError(err)
	require.NotNil(g)
	param := backpackSendParam{
		userID:  12,
		GiftID:  301,
		GiftNum: 1,
		r:       new(room.Room),
		g:       g,
		u:       new(liveuser.Simple),
		bubble:  new(bubble.Simple),
	}
	now := goutil.TimeNow()

	require.NoError(useritems.UnsetGift(12, 301))
	resp, err := param.send()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(301), resp.GiftID)
	assert.False(util.IntToBool(resp.Ok))
	assert.Equal("礼物数量不足哦~", resp.Message)
	assert.Zero(*resp.Remain)
	require.NoError(useritems.AddGiftToUsers([]int64{12}, g, 10, useritems.SourceNormal, now.Unix(), now.Unix()+1000))
	resp, err = param.send()
	require.NoError(err)
	assert.True(util.IntToBool(resp.Ok), resp.Message)
	assert.Equal(int64(9), *resp.Remain)
	assert.Equal(int64(301), resp.GiftID)
	assert.NotNil(resp.User)
	assert.NotNil(resp.Bubble)
}

func TestBackpackAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	r, err := room.Find(roomID)
	require.NoError(err)
	require.NotNil(r)
	param := backpackSendParam{
		GiftNum: 1,
		r:       r,
		g: &gift.Gift{
			Price: 1,
			Point: 1,
			Type:  gift.TypeRebate,
		},
		userID: -12,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": roomID, "user_id": param.userID}
	_, err = livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)

	param.addMedalPoint()
	medal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(int64(2), medal.Point)

	param.g.Price = 0
	param.g.Attr.Set(gift.AttrDisableMedalPoint)
	param.addMedalPoint()
	medal = new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(int64(2), medal.Point)

	param.g = &gift.Gift{
		GiftID: 30001,
		Price:  1,
		Point:  6000,
		Type:   gift.TypeFree,
	}
	param.addMedalPoint()
	afterMedal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(afterMedal)
	require.NoError(err)
	assert.Equal(medal.Point, afterMedal.Point)
}

func TestBackpackSendParam_AddMultiConnectScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := backpackSendParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		g: &gift.Gift{
			Price: 10,
		},
		GiftNum: 50,
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestBackpackAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := backpackSendParam{
		GiftNum: 1,
		r:       new(room.Room),
		g: &gift.Gift{
			Price: 1,
		},
		userID: 12,
	}
	param.r.RoomID = 1478963
	now := goutil.TimeNow()
	keys := []string{
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeCurrent, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeHourly, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeWeek, now),
	}
	require.NoError(service.Redis.Del(keys...).Err())
	assert.NotPanics(func() { param.addRevenueRank() })
	// 通过判断键是否存在来看榜单是否添加成功
	val, err := service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.Equal(int64(2), val)

	param.r.Status.Open = room.StatusOpenTrue
	param.g.Attr.Set(gift.AttrPointAddRank)
	param.g.GiftID = 30005
	param.g.Point = 10
	require.NoError(service.Redis.Del(keys...).Err())
	assert.NotPanics(func() { param.addRevenueRank() })
	val, err = service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.Equal(int64(3), val)
}

func TestBackpackSendBuildIMMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := backpackSendParam{
		userID:  12,
		u:       &liveuser.Simple{UID: 12},
		GiftNum: 1,
		r:       testRoom,
		g: &gift.Gift{
			GiftID: 301,
			Point:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.u, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	assert.Len(param.broadcastElems, 1)

	param.g.Attr.Set(gift.AttrAlwaysNotify)
	param.buildIMMessage()
	assert.Len(param.broadcastElems, 3)

	param = backpackSendParam{
		FromRoomID: openingRoomID,
		userID:     12,
		u:          &liveuser.Simple{UID: 12},
		GiftNum:    1,
		r:          testRoom,
		g: &gift.Gift{
			GiftID: 301,
			Point:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.u, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	require.Len(param.broadcastElems, 2)
	assert.Zero(param.broadcastElems[0].UserID)
	assert.Equal(param.r.RoomID, param.broadcastElems[0].RoomID)
	assert.Equal(param.userID, param.broadcastElems[1].UserID)
	assert.Equal(param.FromRoomID, param.broadcastElems[1].RoomID)
}

func TestBackpackSendAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := backpackSendParam{g: &gift.Gift{Point: 1}, GiftNum: 1}
	assert.NotPanics(func() { param.addPK() })

	param.g.Price = 10
	param.r = new(room.Room)
	param.addPK()
	assert.Empty(param.broadcastElems)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"fighters": bson.M{"$exists": false}})
	require.NoError(err)
	var lp livepk.LivePK
	err = col.FindOne(ctx,
		bson.M{"status": livepk.PKRecordStatusFighting}).Decode(&lp)
	require.NoError(err)
	param.r.RoomID = lp.Fighters[1].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	assert.Len(param.broadcastElems, 2)
}

func TestBackpackSendAddActivity(t *testing.T) {
	assert := assert.New(t)

	assert.NotPanics(func() {
		param := &backpackSendParam{
			r:       testRoom,
			u:       &liveuser.Simple{UID: 12},
			g:       &gift.Gift{Point: 1},
			GiftNum: 10,
		}

		param.addActivity()
	})
}

func TestBackpackSendBroadcast(t *testing.T) {
	assert := assert.New(t)

	var notifyCount int
	cancel := mrpc.SetMock("im://broadcast/many", func(i interface{}) (interface{}, error) {
		notifyCount++
		tutil.PrintJSON(i)
		return true, errors.New("unittest error")
	})
	defer cancel()
	r := new(room.Room)
	r.CreatorUsername = "test"
	param := backpackSendParam{
		broadcastElems: []*userapi.BroadcastElem{
			{Type: 1, RoomID: 123, Payload: "test"},
		},
	}
	param.broadcast()
	assert.Equal(notifyCount, 1)
}
