package chatroom

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livevote"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionInteractionOptions 获取开始互动所需配置项
/**
 * @api {get} /api/v2/chatroom/interaction/options 获取开始互动所需配置项
 * @apiDescription 获取互动需要下发的配置项，若当前有互动在行进时，返回互动未结束异常
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [type=vote] 互动类型 vote: 礼物投票
 *
 * @apiSuccessExample {json} 互动礼物投票:
 *     {
 *       "duration": {
 *         "select": [{"show": "15s", "value": 15000}, {"show": "30s", "value": 30000}, {"show": "60s", "value": 60000}],
 *         "default": {"show": "30s", "value": 30000}
 *       }
 *     }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 500030024
 * @apiError (403) {String} info 您还有一个互动未结束哦~
 */
func ActionInteractionOptions(c *handler.Context) (handler.ActionResponse, error) {
	typeInteraction := c.GetDefaultParamString("type", interaction.TypeInteractionVote)

	roomID, err := room.FindRoomID(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	result, err := interaction.Find(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if result != "" {
		return nil, actionerrors.ErrInteractionOngoing
	}

	switch typeInteraction {
	case interaction.TypeInteractionVote:
		option, err := interaction.FindVoteOption()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		return handler.M{
			"duration": option.DurationOption,
		}, nil
	default:
		return nil, actionerrors.ErrParams
	}
}

type startVoteParam struct {
	Title    string   `form:"title" json:"title" binding:"required,max=15"`
	Duration int64    `form:"duration" json:"duration" binding:"required,gt=0"`
	Content  []string `form:"content" json:"content" binding:"gte=2,lte=4,dive,lte=5"`

	r      *room.Room
	option *interaction.VoteOption
}

// ActionInteractionStartVote 创建互动礼物投票
/**
 * @api {post} /api/v2/chatroom/interaction/startvote 创建互动礼物投票
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} title 投票主题
 * @apiParam {Number} duration 投票持续时长，单位毫秒
 * @apiParam {String[]} content 投票选项内容
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 投票开始
 *     {
 *       "type": "vote",
 *       "event": "start",
 *       "room_id": 65261414,
 *       "vote": {
 *         "vote_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "title": "pick 你喜欢的牛排",
 *         "duration": 600000, // 用户投票设置时长，单位毫秒，再来一局时使用
 *         "remain_duration": 600000, // 投票剩余时长，单位毫秒
 *         "announce_duration": 10000, // 投票结果公示时长，单位毫秒
 *         "content": [
 *           {
 *             "gift_id": 10001,
 *             "description": "生",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10001.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10001-web.png",
 *             "color": "#FE929B",
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10002,
 *             "description": "三分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10002.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10002-web.png",
 *             "color": "#B6E58B",
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10003,
 *             "description": "五分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10003.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10003-web.png",
 *             "color": "#F5CC6E",
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10004,
 *             "description": "七分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10004.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10004-web.png",
 *             "color": "#BEB2FF",
 *             "num": 0
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 投票（完整信息）
 *     {
 *       "type": "vote",
 *       "event": "update",
 *       "room_id": 65261414,
 *       "vote": {
 *         "vote_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "title": "pick 你喜欢的牛排",
 *         "duration": 600000, // 用户投票设置时长，单位毫秒，再来一局时使用
 *         "remain_duration": 600000, // 投票剩余时长，单位毫秒
 *         "announce_duration": 10000, // 投票结果公示时长，单位毫秒
 *         "content": [
 *           {
 *             "gift_id": 10001,
 *             "description": "生",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10001.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10001-web.png",
 *             "color": "#FE929B",
 *             "num": 10
 *           },
 *           {
 *             "gift_id": 10002,
 *             "description": "三分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10002.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10002-web.png",
 *             "color": "#B6E58B",
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10003,
 *             "description": "五分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10003.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10003-web.png",
 *             "color": "#F5CC6E",
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10004,
 *             "description": "七分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10004.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10004-web.png",
 *             "color": "#BEB2FF",
 *             "num": 0
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 投票（精简信息）
 *     {
 *       "type": "vote",
 *       "event": "update",
 *       "room_id": 65261414,
 *       "vote": {
 *         "vote_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "content": [
 *           {
 *             "gift_id": 10001,
 *             "num": 10
 *           },
 *           {
 *             "gift_id": 10002,
 *             "num": 0
 *           },
 *           {
 *             "gift_id": 10003,
 *             "num": 9
 *           },
 *           {
 *             "gift_id": 10004,
 *             "num": 0
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误, 相关错误信息
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 500030024
 * @apiError (403) {String} info 您还有一个互动未结束哦~
 */
func ActionInteractionStartVote(c *handler.Context) (handler.ActionResponse, error) {
	var param startVoteParam
	err := param.check(c)
	if err != nil {
		return nil, err
	}

	ok, err := interaction.LockOngoing(param.r.RoomID, interaction.TypeInteractionVote,
		param.option.LockDuration(param.Duration))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrInteractionOngoing
	}
	vote, err := livevote.AddVote(param.r, param.Duration, param.Title, param.Content, param.option)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = interaction.SetOngoingInteractionInfo(param.r.RoomID, interaction.Info{
		Vote: &interaction.VoteInfo{
			VoteID:  vote.OID,
			GiftIDs: vote.GiftIDs(),
		}}, param.Duration)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 发送房间内通知
	vote.SendBroadcast(liveim.EventVoteStart, "")
	return "success", nil
}

func (p *startVoteParam) check(c *handler.Context) error {
	err := c.Bind(p)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.option, err = interaction.FindVoteOption()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !goutil.HasElem(p.option.Durations(), p.Duration) {
		return actionerrors.ErrParams
	}

	p.r, err = room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if p.r.Medal == nil {
		return actionerrors.ErrNoAuthority
	}

	_, pass, err := goclient.CheckTexts(c, append(p.Content, p.Title), scan.SceneIntro)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !pass {
		return actionerrors.ErrParamsMsg("您发起的礼物投票中的主题或选项中含有违禁词，请修改后发布哦~")
	}
	return nil
}

// ActionInteractionCloseVote 提前关闭互动礼物投票
/**
 * @api {post} /api/v2/chatroom/interaction/closevote 提前关闭互动礼物投票
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     {
 *       "type": "vote",
 *       "event": "close",
 *       "room_id": 65261414,
 *       "message": "主播已提前结束此次礼物投票。",
 *       "vote": {
 *         "vote_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "title": "pick 你喜欢的牛排",
 *         "duration": 600000,  // 用户投票设置时长，单位毫秒，再来一局时使用
 *         "remain_duration": 0,
 *         "announce_duration": 10000, // 投票结果公示时长，毫秒
 *         "content": [
 *           {
 *             "gift_id": 10001,
 *             "description": "生",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10001.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10001-web.png",
 *             "color": "#BEB2FF",
 *             "num": 2
 *           },
 *           {
 *             "gift_id": 10002,
 *             "description": "三分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10002.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10002-web.png",
 *             "color": "#BEB2FF",
 *             "num": 5
 *           },
 *           {
 *             "gift_id": 10003,
 *             "description": "五分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10003.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10003-web.png",
 *             "color": "#BEB2FF",
 *             "num": 1
 *           },
 *           {
 *             "gift_id": 10004,
 *             "description": "七分熟",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/10004.png",
 *             "web_icon_url": "https://static-test.missevan.com/gifts/icons/10004-web.png",
 *             "color": "#BEB2FF",
 *             "num": 3
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionInteractionCloseVote(c *handler.Context) (handler.ActionResponse, error) {
	vote, err := livevote.CloseVote(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if vote == nil {
		return nil, actionerrors.ErrNotFound("当前没有进行中的礼物投票")
	}
	err = interaction.DelOngoingInteractionInfo(vote.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = interaction.Expire(vote.RoomID, time.Duration(vote.AnnounceDuration)*time.Millisecond)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 发送房间内通知
	vote.SendBroadcast(liveim.EventVoteClose, "主播已提前结束此次礼物投票")
	return "success", nil
}
