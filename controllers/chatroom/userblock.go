package chatroom

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type userBlockParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`
	UserID int64 `form:"user_id" json:"user_id"`

	c        *handler.Context
	r        *room.Room
	u        *liveuser.User
	messages []*userapi.BroadcastElem
}

// 拉黑用户并踢出消息提醒
type userBlockPayload struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	User   struct {
		UserID int64 `json:"user_id"`
	} `json:"user"`
}

// 取消提问消息提醒
type cancelQuestionPayload struct {
	Type       string `json:"type"`
	Event      string `json:"event"`
	AnswerType string `json:"answer_type"`
	RoomID     int64  `json:"room_id"`
	Question   struct {
		QuestionID string `json:"question_id"`
		Status     int32  `json:"status"`
	} `json:"question"`
}

// ActionUserBlock 拉黑并从直播间踢出用户
/**
 * @api {post} /api/v2/chatroom/user/block 拉黑并踢出直播间
 * @apiDescription 拉黑时如存在未完成的提问时会发送取消提问的消息，如正在连麦会发送停止连麦的消息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} user_id 被拉黑的用户 ID
 *
 * @apiSuccessExample {json} 成功拉黑并踢出
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 拉黑成功被拉黑方消息（仅被拉黑用户接受消息）
 *   {
 *     "type": "user",
 *     "event": "block_user",
 *     "room_id": 223344,
 *     "user": {
 *       "user_id": 10, // 被拉黑的用户 ID
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该直播间
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionUserBlock(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newUserBlockParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.block()
	if err != nil {
		return nil, err
	}
	err = param.afterBlock()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func newUserBlockParam(c *handler.Context) (*userBlockParam, error) {
	param := new(userBlockParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	if param.c.UserID() == param.UserID {
		return nil, actionerrors.NewErrForbidden("不能拉黑自己")
	}
	param.r, err = room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.c.UserID() != param.r.CreatorID {
		return nil, actionerrors.ErrForbidden
	}
	param.u, err = liveuser.Find(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	return param, nil
}

func (param *userBlockParam) check() error {
	// 被主播拉黑，无法二次拉黑
	blocked, err := blocklist.IsBlocked(param.r.CreatorID, param.u.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("此用户已经被您拉黑了")
	}
	// 校验是否是房管
	isRoomAdmin, err := livemembers.IsRoomAdmin(param.r.OID, param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isRoomAdmin {
		return actionerrors.NewErrForbidden("无法将房管踢出并拉入黑名单，请先取消其房管身份")
	}
	// 校验是否是超管
	isStaff, _, err := liveuser.IsStaff(param.u)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isStaff {
		return actionerrors.NewErrForbidden("无法将超管踢出并拉入黑名单")
	}
	return nil
}

func (param *userBlockParam) block() error {
	err := userapi.AddBlocklist(param.r.CreatorID, param.UserID, param.c.ClientIP())
	if err != nil {
		return err
	}
	// 清除用户的黑名单缓存
	err = blocklist.Clear(param.r.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 拉黑消息
	payload := &userBlockPayload{
		Type:   liveim.TypeUser,
		Event:  liveim.EventBlockUser,
		RoomID: param.RoomID,
	}
	payload.User.UserID = param.UserID
	param.messages = append(param.messages, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  param.RoomID,
		UserID:  param.UserID,
		Payload: payload,
	})
	return nil
}

func (param *userBlockParam) afterBlock() error {
	err := param.connectClose()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = param.cancelQuestion()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	err = userapi.BroadcastMany(param.messages)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func (param *userBlockParam) connectClose() error {
	_, err := liveconnect.ConnectClose(param.RoomID, param.UserID)
	if err != nil {
		return err
	}
	// TODO: 连麦断开消息
	return nil
}

func (param *userBlockParam) cancelQuestion() error {
	questionOIDs, transactionIDs, err := livequestion.BatchCancelByUserID(param.RoomID, param.UserID)
	if err != nil {
		return err
	}
	if len(transactionIDs) == 0 || len(questionOIDs) == 0 {
		return nil
	}
	// rpc cancel asks
	resp, err := userapi.CancelAsks(param.r.CreatorID, transactionIDs, userapi.NewUserContext(param.c))
	if err != nil {
		return err
	}
	// 检查并记录 cancel ask 中遇到的 resp.error
	livequestion.LogCancelAsks(transactionIDs, resp)

	// 取消提问消息
	payloads := make([]*cancelQuestionPayload, 0, len(questionOIDs))
	for _, oid := range questionOIDs {
		payload := &cancelQuestionPayload{
			Type:       liveim.TypeQuestion,
			Event:      liveim.EventAnswer,
			AnswerType: liveim.AnswerTypeCancel,
			RoomID:     param.RoomID,
		}
		payload.Question.QuestionID = oid.Hex()
		payload.Question.Status = livequestion.StatusCanceled
		payloads = append(payloads, payload)
	}
	param.messages = append(param.messages, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  param.RoomID,
		Payload: payloads,
	})
	return nil
}
