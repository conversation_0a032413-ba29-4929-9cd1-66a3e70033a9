package chatroom

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testBanForeverUserID = 20201104 // 来自 general_status_test.go
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listQuestionResp{}, "data", "total_value", "pagination")
	kc.Check(likeQuestionParam{}, "room_id", "question_id", "operation")
	kc.Check(appendQuestionLimitParam{}, "room_id")
	kc.Check(askQuestionParam{}, "room_id", "question", "price")
	kc.Check(askQuestionTransaction{}, "transaction_id", "price")
	kc.Check(askQuestionResp{}, "question", "transaction", "user", "balance")
	kc.Check(answerQuestionParam{}, "room_id", "question_id", "type")
	kc.Check(answerQuestionAttachPayload{}, "type", "event", "answer_type",
		"room_id", "user", "question", "current_revenue", "status")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(likeQuestionParam{}, "room_id", "question_id", "operation")
	kc.Check(appendQuestionLimitParam{}, "room_id")
	kc.Check(askQuestionParam{}, "room_id", "question", "price")
	kc.Check(answerQuestionParam{}, "room_id", "question_id", "type")
}

func TestActionListQuestions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/question/list", false, nil)
	_, err := ActionListQuestions(c)
	assert.Equal(actionerrors.ErrParams, err, "缺乏需要的参数")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/question/list?type=0&room_id=0", false, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrParams, err, "参数值不合法")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/question/list?type=error&room_id=1", false, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrParams, err, "type 参数值不可以为字符串")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/question/list?type=1&room_id=error", false, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrParams, err, "room_id 参数值不可以为字符串")

	// 房间错误
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/question/list?type=1&room_id=987123", false, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 被封禁的房间
	uri := fmt.Sprintf("/api/v2/chatroom/question/list?type=%d&room_id=%d", questionListQueueAndJoined, bannedRoomID)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrBannedRoom, err)

	// 未开播房间
	uri = fmt.Sprintf("/api/v2/chatroom/question/list?type=%d&room_id=%d", questionListFinished, closedRoomID)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	_, err = ActionListQuestions(c)
	assert.Equal(actionerrors.ErrClosedRoomAlt, err)

	// 插入测试数据
	room, err := findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(room)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	h := livequestion.Helper{
		CreatedTime: now,
		RoomID:      openingRoomID,
		RoomOID:     openingRoom.OID,
	}
	defer func() {
		_, err = livequestion.Collection().DeleteMany(ctx, bson.M{
			"room_id": bson.M{"$in": bson.A{openingRoomID}},
		})
		assert.NoError(err)
	}()

	testQueued := make([]*livequestion.LiveQuestion, 2)
	testFinish := make([]*livequestion.LiveQuestion, 3)
	sQuestions := make([]interface{}, 0)
	for i := 0; i < len(testQueued); i++ {
		testQueued[i] = &livequestion.LiveQuestion{
			OID:    primitive.NewObjectIDFromTimestamp(now),
			Helper: h,
		}
		testQueued[i].UserID = c.UserID() - int64(i)
		testQueued[i].Status = livequestion.StatusQueued
		testQueued[i].Price = 30
		testQueued[i].LikedIDs = []int64{}
		sQuestions = append(sQuestions, testQueued[i].Helper)
	}
	for i := 0; i < len(testFinish); i++ {
		testFinish[i] = &livequestion.LiveQuestion{
			OID:    primitive.NewObjectIDFromTimestamp(now),
			Helper: h,
		}
		testFinish[i].UserID = c.UserID() - int64(i)
		testFinish[i].Status = livequestion.StatusFinished
		testFinish[i].Price = 30
		testFinish[i].LikedIDs = []int64{}
		sQuestions = append(sQuestions, testFinish[i].Helper)
	}

	_, err = livequestion.Collection().InsertMany(ctx, sQuestions)
	require.NoError(err)

	// 正常情况
	page := int64(1)
	pageSize := int64(20)
	uri = fmt.Sprintf("/api/v2/chatroom/question/list?type=%d&room_id=%d&p=%d&pagesize=%d",
		questionListFinished, openingRoomID, page, pageSize)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)

	// 调用接口
	r, err := ActionListQuestions(c)
	require.NoError(err)
	resp := r.(listQuestionResp)
	require.NotEmpty(resp.Data)
	require.NotEmpty(resp.Pagination)
	require.NotZero(resp.TotalValue)
	assert.Equal(testFinish[0].Price*int64(len(testFinish)), *resp.TotalValue)

	// 模拟查询
	questions, err := livequestion.ListLiveQuestionsByPage(0, room.RoomID,
		util.UnixMilliToTime(room.Status.OpenTime), []int32{livequestion.StatusFinished}, nil)
	require.NoError(err)
	require.NotEmpty(questions)
	assert.Len(questions, len(resp.Data))

	// 正常情况，查询未回答/正在回答的问题
	uri = fmt.Sprintf("/api/v2/chatroom/question/list?type=%d&room_id=%d", questionListQueueAndJoined, openingRoomID)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	r, err = ActionListQuestions(c)
	require.NoError(err)
	resp = r.(listQuestionResp)
	require.NotEmpty(resp.Data)
	require.Zero(resp.TotalValue)
	require.Empty(resp.Pagination)

	questions, err = livequestion.ListLiveQuestionsByPage(0, room.RoomID,
		util.UnixMilliToTime(room.Status.OpenTime), []int32{livequestion.StatusQueued, livequestion.StatusJoined}, nil)
	require.NoError(err)
	require.NotEmpty(questions)
	assert.Len(questions, len(resp.Data))

	tutil.PrintJSON(resp)
}

func TestShouldUpdateLike(t *testing.T) {
	assert := assert.New(t)

	var param likeQuestionParam
	param.question = new(livequestion.LiveQuestion)
	param.question.Liked = true
	param.Operation = livequestion.OperationLike
	assert.False(param.shouldUpdateLike())

	param.Operation = livequestion.OperationDislike
	assert.True(param.shouldUpdateLike())

	param.question.Liked = false
	param.Operation = livequestion.OperationLike
	assert.True(param.shouldUpdateLike())

	param.Operation = livequestion.OperationDislike
	assert.False(param.shouldUpdateLike())
}

func TestActionLikeQuestion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, nil)
	_, err := ActionLikeQuestion(c)
	assert.Equal(actionerrors.ErrParams, err)

	var param likeQuestionParam
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	assert.Equal(actionerrors.ErrParams, err)

	param.RoomID = openingRoomID
	param.QuestionID = "test"
	param.Operation = livequestion.OperationLike
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	assert.Equal(actionerrors.ErrParams, err)

	now := goutil.TimeNow()
	param.QuestionID = primitive.NewObjectIDFromTimestamp(now.Add(time.Minute)).Hex()
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	assert.Equal(actionerrors.ErrQuestionNotFound, err)

	// 插入测试数据
	r, err := findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(r)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lq := &livequestion.LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(now),
		Helper: livequestion.Helper{
			CreatedTime: now,
			RoomID:      openingRoomID,
			RoomOID:     openingRoom.OID,
			UserID:      c.UserID(),
			Status:      livequestion.StatusQueued,
			Price:       30,
			LikedIDs:    []int64{},
		},
	}
	defer func() {
		_, err = livequestion.Collection().DeleteOne(ctx, bson.M{"_id": lq.OID})
		assert.NoError(err)
	}()
	_, err = livequestion.Collection().InsertOne(ctx, lq)
	require.NoError(err)

	param.QuestionID = lq.OID.Hex()
	// 模拟操作频繁
	lock := keys.LockUserQuestionUpdateLike2.Format(c.UserID(), param.QuestionID)
	ok, err := service.Redis.SetNX(lock, 1, 5*time.Second).Result()
	require.NoError(err)
	assert.True(ok)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	assert.Equal(actionerrors.NewErrForbidden("操作频繁，请稍后再试"), err)

	// 正常情况
	err = service.Redis.Del(lock).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	require.NoError(err)

	question, err := livequestion.FindOne(bson.M{"_id": lq.OID}, &livequestion.FindOptions{UserID: c.UserID()})
	require.NoError(err)
	require.NotNil(question)
	assert.Equal(1, question.Likes)

	// 取消点赞
	param.Operation = livequestion.OperationDislike
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/like", true, param)
	_, err = ActionLikeQuestion(c)
	require.NoError(err)

	question, err = livequestion.FindOne(bson.M{"_id": lq.OID}, &livequestion.FindOptions{UserID: c.UserID()})
	require.NoError(err)
	require.NotNil(question)
	assert.Zero(question.Likes)
}

func TestActionAppendQuestionLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, nil)
	_, err := ActionAppendQuestionLimit(c)
	assert.Equal(actionerrors.ErrParams, err, "无参数")

	var param appendQuestionLimitParam
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, param)
	_, err = ActionAppendQuestionLimit(c)
	assert.Equal(actionerrors.ErrParams, err, "参数不合法")

	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, param)
	_, err = ActionAppendQuestionLimit(c)
	assert.Equal(actionerrors.ErrForbidden, err, "无权限操作")

	// 插入测试数据
	r, err := findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(r)
	defer func() {
		_, err := room.UpdateOneRoom(bson.M{"room_id": openingRoomID}, bson.M{"question.limit": 100})
		assert.NoError(err)
	}()
	_, err = room.UpdateOneRoom(bson.M{"room_id": openingRoomID}, bson.M{"question.limit": 100})
	require.NoError(err)

	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, param)
	c.User().ID = r.CreatorID
	_, err = ActionAppendQuestionLimit(c)
	require.NoError(err)

	r, err = findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(100+room.QuestionAppendNum, r.Question.Limit)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, param)
	c.User().ID = r.CreatorID
	_, err = ActionAppendQuestionLimit(c)
	require.NoError(err)

	r, err = findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(100+room.QuestionAppendNum*2, r.Question.Limit)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/append-limit", true, param)
	c.User().ID = r.CreatorID
	_, err = ActionAppendQuestionLimit(c)
	assert.Equal(actionerrors.NewErrForbidden("追加次数已达上限"), err)
}

func TestActionQuestionAsk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(1)
		testToBlockUserID   = int64(12)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, nil)
	_, err := ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrParams, err, "无参数")

	var param askQuestionParam
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrParams, err, "参数不合法")

	param.Price = livequestion.MinPrice
	param.Question = strings.Repeat("测试", 129) // 测试超长提问
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	assert.EqualError(err, "提问内容过长")

	param.Question = "测试提问"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionQuestionAsk(c)
	assert.EqualError(err, "无法向自己提问")

	param.Price = 0
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrQuestionPriceTooLow, err, "提问价格过低")

	param.Price = livequestion.MinPrice
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	c.User().ID = -999
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrCannotFindUser, err, "找不到用户")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	c.User().ID = testBanForeverUserID
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrBannedUser, err, "用户已被封禁")

	// 测试禁言
	now := goutil.TimeNow()
	expireTime := now.Add(time.Minute)
	m := livemembers.Member{
		Helper: livemembers.Helper{
			UserID:      testBanForeverUserID,
			Username:    "test",
			Status:      livemembers.StatusMute,
			ExpireAt:    &expireTime,
			CreatedTime: now,
		},
	}
	err = m.SetMute()
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	c.User().ID = testBanForeverUserID
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrBannedUser, err, "用户已被封禁")

	cancelAskMock := mrpc.SetMock(userapi.URIAsk, func(input interface{}) (output interface{}, err error) {
		return &userapi.BalanceResp{
			TransactionID: 1,
			Price:         livequestion.MinPrice,
			Balance:       30,
		}, nil
	})
	defer cancelAskMock()

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	// 模拟操作过快
	lock := keys.LockRoomQuestion2.Format(param.RoomID, c.User().ID)
	require.NoError(service.Redis.Del(lock).Err())
	ok, err := service.Redis.SetNX(lock, 1, 2*time.Second).Result()
	require.NoError(err)
	require.True(ok)

	_, err = ActionQuestionAsk(c)
	assert.EqualError(err, "您提问太频繁啦~")

	// mock 文字检查
	rpcResp := []*scan.BaseCheckResult{{Pass: false}}
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return rpcResp, nil
	})
	defer cancel()

	// 清空锁
	require.NoError(service.Redis.Del(lock).Err())
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrQuestionSensitive, err)

	// mock 广告拦截
	rpcResp = []*scan.BaseCheckResult{{Pass: true, Labels: []string{"evil"}}}
	require.NoError(service.Redis.Del(lock).Err())
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	assert.Equal(actionerrors.ErrQuestionSensitive, err)

	rpcResp = []*scan.BaseCheckResult{{Pass: true}}
	require.NoError(service.Redis.Del(lock).Err())
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	r, err := ActionQuestionAsk(c)
	require.NoError(err)
	resp := r.(*askQuestionResp)
	require.NotNil(resp.Question)
	assert.NotEqual("000000000000000000000000", resp.Question.OID.Hex())
	assert.NotEqual("000000000000000000000000", resp.Question.RoomOID.Hex())

	// 测试被主播拉黑
	require.NoError(service.Redis.Del(lock).Err())
	room, err := room.FindOne(bson.M{"creator_id": testFromBlockUserID})
	require.NoError(err)
	require.NotNil(room)
	param.RoomID = room.RoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/ask", true, param)
	_, err = ActionQuestionAsk(c)
	require.EqualError(err, "您当前无法在本直播间内进行此操作")
}

func TestActionAnswerQuestionAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 没有勋章的直播间
	roomID := int64(22334)
	userID := int64(77785795)
	revenue := int64(6)
	param := &answerQuestionParam{
		question: &livequestion.LiveQuestion{
			Helper: livequestion.Helper{UserID: userID, RoomID: roomID, Price: revenue},
		},
		room: &room.Room{
			Helper: room.Helper{RoomID: roomID, CreatorID: 22334},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": roomID, "user_id": userID}
	_, err := livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)
	param.addMedalPoint()

	// 有勋章的直播间
	roomID = int64(10241)
	filter = bson.M{"room_id": roomID, "user_id": userID}
	_, err = livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)
	param = &answerQuestionParam{
		question: &livequestion.LiveQuestion{
			Helper: livequestion.Helper{UserID: userID, RoomID: roomID, Price: revenue},
		},
		room: &room.Room{
			Helper: room.Helper{RoomID: roomID, CreatorID: 10241, Medal: &room.Medal{Name: "提问勋章 Test"}},
		},
		user: &liveuser.Simple{
			UID: userID,
		},
	}
	param.addMedalPoint()
	medal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(livemedal.StatusPending, medal.Status)
	assert.Equal(revenue, medal.Point)
}

func TestActionAnswerQuestionAddUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22334)
	userID := int64(3456835)
	revenue := int64(6)
	param := &answerQuestionParam{
		question: &livequestion.LiveQuestion{
			Helper: livequestion.Helper{UserID: userID, RoomID: roomID, Price: revenue},
		},
		room: &room.Room{
			Helper: room.Helper{RoomID: roomID, CreatorID: 22334},
		},
	}

	key := keys.KeyNobleUserVips1.Format(userID)
	require.NoError(service.Redis.Set(key, "{}", 10*time.Second).Err())

	before, err := liveuser.Find(userID)
	require.NoError(err)
	require.NotNil(before)
	param.addUserContribution()
	after, err := liveuser.Find(userID)
	require.NoError(err)
	require.NotNil(after)
	assert.Equal(after.Contribution, before.Contribution+revenue*10)
}

func TestActionAnswerQuestionAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := answerQuestionParam{
		question: &livequestion.LiveQuestion{
			Helper: livequestion.Helper{
				UserID: 20201201,
			},
		},
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 20201202,
				RoomID:    20201202,
			},
		},
		userID: 20201202,
	}
	rankPre, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.room.CreatorID)
	require.NoError(err)
	assert.NotPanics(func() { param.addRevenueRank() })
	rankAfter, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.room.CreatorID)
	require.NoError(err)
	assert.Equal(rankPre, rankAfter)
	param.question.Price = 30
	assert.NotPanics(func() { param.addRevenueRank() })
	rankAfter, err = usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.room.CreatorID)
	require.NoError(err)
	assert.Equal(rankAfter.Revenue, rankPre.Revenue+30)
}

func TestActionAnswerQuestion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, nil)
	_, err := ActionAnswerQuestion(c)
	assert.Equal(actionerrors.ErrParams, err, "无参数")

	var param answerQuestionParam
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	_, err = ActionAnswerQuestion(c)
	assert.Equal(actionerrors.ErrParams, err, "参数不合法")

	param.Type = "invalid"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	_, err = ActionAnswerQuestion(c)
	assert.Equal(actionerrors.ErrParams, err, "answer_type 参数不合法")

	param.Type = questionAnswerJoin
	param.QuestionID = "invalid"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	_, err = ActionAnswerQuestion(c)
	assert.Equal(actionerrors.ErrParams, err, "question_id 参数不合法")

	now := goutil.TimeNow()
	// 插入测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lq := &livequestion.LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(now),
		Helper: livequestion.Helper{
			CreatedTime: now,
			RoomID:      openingRoomID,
			RoomOID:     openingRoom.OID,
			UserID:      c.UserID(),
			Status:      livequestion.StatusQueued,
			Price:       30,
			LikedIDs:    []int64{},
		},
	}
	lq2 := &livequestion.LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(now),
		Helper: livequestion.Helper{
			CreatedTime: now.Add(time.Minute),
			RoomID:      openingRoomID,
			RoomOID:     openingRoom.OID,
			UserID:      c.UserID(),
			Status:      livequestion.StatusQueued,
			Price:       30,
			LikedIDs:    []int64{},
		},
	}
	defer func() {
		_, err = livequestion.Collection().DeleteOne(ctx, bson.M{"_id": bson.M{"$in": bson.A{lq.OID, lq2.OID}}})
		assert.NoError(err)
	}()
	_, err = livequestion.Collection().InsertMany(ctx, []interface{}{lq, lq2})
	require.NoError(err)

	cancelConfirmAskMock := mrpc.SetMock(userapi.URIConfirmAsk, func(input interface{}) (output interface{}, err error) {
		return &userapi.BalanceResp{
			TransactionID: 1,
			Price:         livequestion.MinPrice,
			Balance:       30,
		}, nil
	})
	defer cancelConfirmAskMock()

	testOpenRoom, err := findOpenRoom(openingRoomID)
	require.NoError(err)
	require.NotNil(testOpenRoom)

	param.Type = questionAnswerJoin
	param.QuestionID = primitive.NewObjectIDFromTimestamp(now).Hex()
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionAnswerQuestion(c)
	assert.EqualError(err, "无法找到该提问或该提问已结束")

	// 回答提问
	param = answerQuestionParam{}
	param.Type = questionAnswerJoin
	param.QuestionID = lq.OID.Hex()
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionAnswerQuestion(c)
	require.NoError(err)

	joinedQuestion, err := livequestion.FindOne(bson.M{"_id": lq.OID}, &livequestion.FindOptions{UserID: c.UserID()})
	require.NoError(err)
	require.NotNil(joinedQuestion)
	assert.Equal(livequestion.StatusJoined, joinedQuestion.Status)

	// 测试在已经有提问在回答的情况下尝试回答
	param = answerQuestionParam{}
	param.Type = questionAnswerJoin
	param.QuestionID = lq2.OID.Hex()
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionAnswerQuestion(c)
	assert.EqualError(err, "当前正在回答提问中")

	// 结束回答
	param = answerQuestionParam{}
	param.Type = questionAnswerFinish
	param.QuestionID = joinedQuestion.OID.Hex()
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionAnswerQuestion(c)
	require.NoError(err)

	// 结束一个未被回答的提问
	param = answerQuestionParam{}
	param.Type = questionAnswerFinish
	param.QuestionID = lq2.OID.Hex()
	param.RoomID = openingRoomID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/answer", true, param)
	c.User().ID = openingRoom.CreatorID
	_, err = ActionAnswerQuestion(c)
	assert.EqualError(err, "无法结束该提问")
}

func TestAnswerQuestionOperate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	param := answerQuestionParam{
		room: testRoom,
		c:    handler.NewTestContext(http.MethodPost, "answer", true, nil),
		question: &livequestion.LiveQuestion{
			Helper: livequestion.Helper{
				RoomOID:     testRoom.OID,
				RoomID:      testRoom.RoomID,
				UserID:      123,
				Question:    "test",
				Price:       30,
				CreatedTime: now,
				UpdatedTime: now,
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livequestion.Collection()
	res, err := col.InsertOne(ctx, param.question)
	require.NoError(err)

	assert.PanicsWithValue("提问回答类型错误", func() {
		assert.NoError(param.operate())
	})

	// 开始回答
	var ok bool
	cancel = mrpc.SetMock(userapi.URIConfirmAsk, func(input interface{}) (output interface{}, err error) {
		ok = true
		return &userapi.BalanceResp{
			TransactionID: 123,
			Price:         30,
			Balance:       30,
		}, nil
	})
	defer cancel()
	param.question.OID = res.InsertedID.(primitive.ObjectID)
	param.Type = questionAnswerJoin
	require.NoError(param.operate())
	assert.False(ok)
	require.Equal(livequestion.StatusJoined, param.question.Status)

	// 结束回答，没有 TransactionID 的情况
	param.Type = questionAnswerFinish
	require.NoError(param.operate())
	assert.False(ok)
	assert.Equal(livequestion.StatusFinished, param.question.Status)
	var lq livequestion.LiveQuestion
	require.NoError(col.FindOne(ctx, bson.M{"_id": param.question.OID}).Decode(&lq))
	assert.Equal(livequestion.StatusFinished, lq.Status)

	// 结束回答，有 TransactionID 的情况
	_, err = col.UpdateOne(ctx, bson.M{"_id": param.question.OID},
		bson.M{"$set": bson.M{"status": livequestion.StatusJoined}})
	require.NoError(err)
	param.question.TransactionID = 123
	require.NoError(param.operate())
	assert.True(ok)
	assert.Equal(livequestion.StatusFinished, param.question.Status)
	var lq2 livequestion.LiveQuestion
	require.NoError(col.FindOne(ctx, bson.M{"_id": param.question.OID}).Decode(&lq2))
	assert.Equal(livequestion.StatusFinished, lq.Status)
}
