package chatroom

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionVipList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.Empty(tutil.KeyExists(tutil.JSON, vipListResp{}, "vip_num", "Datas", "user"))

	require.NoError(service.LRURedis.Del(keys.KeyChatroomVipList1.Format(147258)).Err())
	c := handler.NewTestContext(http.MethodGet, "vip/list?room_id=147258", false, nil)
	r, err := ActionVipList(c)
	require.NoError(err)
	resp := r.(*vipListResp)
	assert.EqualValues(resp.VipNum, len(resp.Data))
	// TODO: 详细测试

	key := keys.KeyNobleUserVips1.Format(12)
	pipe := service.Redis.Pipeline()
	pipe.Set(key, "null", 30*time.Second)
	_, err = pipe.Exec()
	require.NoError(err)
	defer func() {
		assert.NoError(service.Redis.Del(key).Err())
	}()
	c = handler.NewTestContext(http.MethodGet, "vip/list?room_id=147258", true, nil)
	r, err = ActionVipList(c)
	require.NoError(err)
	resp = r.(*vipListResp)
	require.NotEmpty(resp.User)
	assert.Equal(int64(12), resp.User.UID)
	require.NotEmpty(resp.User.Titles)
	require.NotNil(resp.User.Noble)
	assert.Equal("开通贵族优先上榜哦~", resp.User.Noble.Tip)
	assert.Nil(resp.User.Highness)
}

func TestActionVipNumber(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/rpc/chatroom/vip/num", false, nil)
	_, err := ActionVipNumber(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/rpc/chatroom/vip/num", false, tutil.ToRequestBody(map[string][]int64{"room_ids": {1234, 5678}}))
	r, err := ActionVipNumber(c)
	var resp vipNumberResp
	require.IsType(resp, r)
	resp = r.(vipNumberResp)
	assert.NoError(err)
	assert.Equal(handler.M{"1234": int64(0), "5678": int64(0)}, resp.Data)

	cacheKey := keys.KeyChatroomVipList1.Format(147258)
	data := []*liveuser.Simple{{UID: 147}}
	listResp := &vipListResp{Data: data, VipNum: 1000}
	b, err := json.Marshal(listResp)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(cacheKey, string(b), 10*time.Second).Err())
	c = handler.NewTestContext(http.MethodPost, "/rpc/chatroom/vip/num", false, tutil.ToRequestBody(map[string][]int64{"room_ids": {147258}}))
	r, err = ActionVipNumber(c)
	assert.NoError(err)
	assert.Equal(vipNumberResp{handler.M{"147258": int64(1000)}}, r)
}

func TestVipMakeAllVipIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := vipListParam{
		roomID:          12345,
		onlineNoble:     []int64{1, 2},
		medalVipUserIDs: []int64{3, 4},
	}
	key := vipNumberKey(param.roomID)
	require.NoError(service.LRURedis.Del(key).Err())
	assert.Equal(4, param.makeAllVipIDs())
	now := goutil.TimeNow().Unix()
	value, err := service.LRURedis.Get(key).Bytes()
	require.NoError(err)
	var res struct {
		VipNum      int   `json:"vip_num"`
		UpdatedTime int64 `json:"updated_time"`
	}
	require.NoError(json.Unmarshal(value, &res))
	assert.Equal(4, res.VipNum)
	assert.GreaterOrEqual(res.UpdatedTime, now)
	v, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.GreaterOrEqual(3600.0, v.Seconds())
}

func TestVipNumFromCache(t *testing.T) {
	assert := assert.New(t)

	saveVipNumToCache(123456, 10)
	v := vipNumFromCache(123456)
	assert.Equal(int64(10), v.VipNum)
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(v, "vip_num", "updated_time")
}

func TestVipFindVips(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param vipListParam
	res, err := param.findVips()
	assert.NoError(err)
	assert.Empty(res)

	// 构造贵族缓存
	key1 := keys.KeyNobleUserVipLevel1.Format(12)
	key2 := keys.KeyNobleUserVipLevel1.Format(3456835)
	pipe := service.Redis.Pipeline()
	pipe.Set(key1, "{}", 10*time.Second)
	pipe.Set(key2, `{"1":1}`, 10*time.Second)
	_, err = pipe.Exec()
	require.NoError(err)
	param.onlineNoble = []int64{12, 3456835}
	res, err = param.findVips()
	require.NoError(err)
	require.Len(res, 2)
	// 贵族在前，具体查看贵宾榜排序
	assert.Equal([]int64{3456835, 12}, []int64{res[0].UserID(), res[1].UserID()})
	assert.NotNil(res[0].VipInfo)
	assert.Nil(res[1].VipInfo)

	param.onlineNoble = []int64{3456835}
	param.medalName = "test"
	res, err = param.findVips()
	require.NoError(err)
	require.Len(res, 1)
	require.Len(res[0].Titles, 3)
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png", res[0].Titles[2].FrameURL)
}

func TestSortVips(t *testing.T) {
	assert := assert.New(t)

	p := []*liveuser.Simple{
		{
			UID:   1,
			Medal: &livemedal.Mini{Level: 10},
		},
		{
			UID:     2,
			VipInfo: &vip.UserInfo{LiveNoble: &vip.Info{Type: 1, Level: 1}},
		},
		{
			UID:     3,
			VipInfo: &vip.UserInfo{LiveNoble: &vip.Info{Type: 1, Level: 2}},
		},
		{
			UID:     4,
			VipInfo: &vip.UserInfo{LiveHighness: &vip.Info{Type: 2, Level: 1}},
		},
		{
			UID:          5,
			Contribution: 10,
			VipInfo:      &vip.UserInfo{LiveHighness: &vip.Info{Type: 2, Level: 1}},
		},
		{
			UID:     6,
			VipInfo: &vip.UserInfo{LiveNoble: &vip.Info{Type: 1, Level: 1}, LiveHighness: &vip.Info{Type: 2, Level: 1}},
			Medal:   &livemedal.Mini{Level: 10},
		},
		{
			UID:          7,
			Contribution: 10,
			VipInfo:      &vip.UserInfo{LiveNoble: &vip.Info{Type: 1, Level: 1}, LiveHighness: &vip.Info{Type: 2, Level: 1}},
			Medal:        &livemedal.Mini{Level: 10},
		},
		{
			UID:     8,
			VipInfo: &vip.UserInfo{LiveTrialNoble: &vip.Info{Type: 3, Level: 4}},
		},
	}

	sortVips(p)
	userIDs := make([]int64, len(p))
	for i := range p {
		userIDs[i] = p[i].UID
	}
	assert.Equal([]int64{7, 6, 5, 4, 8, 3, 2, 1}, userIDs)
}

func TestFindUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(3456835)
	key := keys.KeyNobleUserVips1.Format(testUserID)
	pipe := service.Redis.Pipeline()
	pipe.Set(key, `{"1":{"user_id":3456835,"type":1,"level":1,"title":"test","expire_time":9999999999999}}`, 30*time.Second)
	_, err := pipe.Exec()
	require.NoError(err)
	defer func() {
		assert.NoError(service.Redis.Del(key).Err())
	}()
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/vip/list", true, nil)
	c.User().ID = testUserID
	param := vipListParam{
		roomID: roomID,
		ctx:    c,
	}
	userInfo, err := param.findUserInfo()
	require.NoError(err)
	require.NotEmpty(userInfo)
	require.NotNil(userInfo.Noble)
	require.Nil(userInfo.Highness)
	assert.Equal("test", userInfo.Noble.Name)
	assert.Equal(1, userInfo.Noble.Status)
}

func TestVipListCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data := []*liveuser.Simple{{UID: 147}}
	t.Run("TestNewResp", func(t *testing.T) {
		param := vipListParam{
			roomID:          147258,
			valuableUserIDs: make([]int64, 0),
			ctx:             goutil.SmartUserContext{},
		}
		resp := new(vipListResp)
		param.cacheResp([]*liveuser.Simple{}, resp)
		assert.NotNil(resp)
		param.valuableUserIDs = make([]int64, 1000)
		param.allVipIDs = make([]int64, 1000)
		param.cacheResp(data, resp)
		assert.NotNil(resp)
	})
	t.Run("TestVipListFromCache", func(t *testing.T) {
		param := vipListParam{
			roomID: 147258,
			ctx:    goutil.SmartUserContext{},
		}
		resp := new(vipListResp)
		require.True(param.vipListFromCache(resp))
		assert.Equal(data, resp.Data)
		assert.EqualValues(1000, resp.VipNum)
	})
}
