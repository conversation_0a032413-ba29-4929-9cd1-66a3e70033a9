package chatroom

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionChatroomList 根据直播间 IDs 批量获取直播间信息
/**
 * @api {get} /api/v2/chatroom/list 根据直播间 IDs 批量获取直播间信息
 * @apiDescription 忽略不存在的房间错误 ID, 只返回正确查询到的房间信息, 暂最多支持 100 个房间号，用于活动页面
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} room_ids 房间 IDs 半角逗号分隔, 如: 123,456
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "room_id": 152,
 *           "catalog_id": 107,
 *           "name": "12345",
 *           "announcement": "12345",
 *           "creator_id": 12345,
 *           "creator_username": "1234",
 *           "cover_url": "http://static.example.com/cover.png",
 *           "statistics": {
 *             "accumulation": 123,
 *             "attention": true, // 是否关注
 *             ...
 *           },
 *           "status": {
 *             "open": 1 // 开播
 *             ...
 *           }
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionChatroomList(c *handler.Context) (handler.ActionResponse, error) {
	roomIDs, _ := c.GetParam("room_ids")
	roomIDList, err := util.SplitToInt64Array(roomIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 目前支持最大查询房间数 100
	if len(roomIDList) == 0 || len(roomIDList) > 100 {
		return nil, actionerrors.ErrParams
	}

	var simple []*room.Simple
	filter := bson.M{
		"room_id": bson.M{"$in": roomIDList},
		"limit":   bson.M{"$exists": false},
	}
	option := &room.FindOptions{FindCreator: true}
	if c.UserID() > 0 {
		option.ListenerID = c.UserID()
		option.FindFans = true
	}
	simple, err = room.ListSimples(filter, nil, option)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return simple, nil
}
