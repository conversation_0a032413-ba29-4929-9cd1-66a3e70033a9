package chatroom

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(10)
		testToBlockUserID   = int64(223344)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))

	// 测试参数错误的情况
	param := setAdminParam{
		RoomID: 0,
		UserID: 99999999,
	}
	c := handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testRoomCreatorID
	assert.EqualError(param.check(c, true), "参数错误")

	// 测试直播间不存在的情况
	param.RoomID = 10
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = 99999999
	assert.EqualError(param.check(c, true), "无法找到该聊天室")

	// 测试用户不是当前房间主播
	param.RoomID = roomID
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = 99999999
	assert.Equal(actionerrors.ErrNoAuthority, param.check(c, true))

	// 测试被设置用户不存在的情况
	param.UserID = 99999999
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testRoomCreatorID
	assert.EqualError(param.check(c, true), "无法找到该用户")

	// 测试房主设置自己为房管的情况
	param.UserID = testRoomCreatorID
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testRoomCreatorID
	assert.EqualError(param.check(c, true), "不能将自己设为房管哦~")

	// 测试房主设置移除自己房管的情况
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testRoomCreatorID
	assert.EqualError(param.check(c, false), "操作失败")

	// 测试拉黑
	param.RoomID = roomID
	param.UserID = testToBlockUserID
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testFromBlockUserID
	assert.EqualError(param.check(c, true), "此用户已经被您拉黑了")

	// 测试参数无误的情况
	param.UserID = 346286
	c = handler.NewTestContext("POST", "/", true, param)
	c.User().ID = testRoomCreatorID
	require.NoError(param.check(c, false))
	assert.Equal(param.UserID, param.user.UID)
	assert.Equal(roomID, param.room.RoomID)
}

// TestActionAddAdmin 测试设置房管接口
func TestActionAddAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()

	// 测试参数错误的情况
	body := handler.M{
		"room_id": roomID,
		"user_id": 0,
	}
	c := handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err := ActionAddAdmin(c)
	require.EqualError(err, "参数错误")

	// 测试直播间不存在的情况
	testUserID := int64(346286)
	body = handler.M{
		"room_id": 10,
		"user_id": testUserID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = 99999
	_, err = ActionAddAdmin(c)
	require.EqualError(err, "无法找到该聊天室")

	// 测试被设置用户不存在的情况
	body = handler.M{
		"room_id": roomID,
		"user_id": 9999999999,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionAddAdmin(c)
	require.EqualError(err, "无法找到该用户")

	// 测试房主设置自己为房管的情况
	body = handler.M{
		"room_id": roomID,
		"user_id": testRoomCreatorID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionAddAdmin(c)
	require.EqualError(err, "不能将自己设为房管哦~")

	// 测试设置超管为房管
	_, err = liveuser.Update(testUserID, bson.M{"group": liveuser.RoleStaff})
	require.NoError(err)
	body = handler.M{
		"room_id": roomID,
		"user_id": testUserID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionAddAdmin(c)
	require.EqualError(err, "您无权执行该操作")

	// 测试设置非超管用户为房管
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 设置用户为房间内禁言
	collection := service.MongoDB.Collection(livemembers.CollectionName)
	_, err = collection.UpdateOne(ctx, bson.M{"_room_id": roomID, "user_id": testUserID},
		bson.M{
			"$set": bson.M{
				"username":     "test",
				"iconurl":      "test.jpg",
				"operator_id":  1,
				"expire_at":    1,
				"updated_time": 1,
				"status":       livemembers.StatusMute,
			},
			"$setOnInsert": bson.M{
				"user_id":      testUserID,
				"room_id":      roomID,
				"created_time": 1,
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = liveuser.Update(testUserID, bson.M{"group": ""})
	require.NoError(err)
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	info, err := ActionAddAdmin(c)
	require.NoError(err)
	m := info.(handler.M)
	assert.EqualValues("添加成功", m["msg"])
	// 验证用户确实被添加为了房管
	err = collection.FindOne(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.NoError(err)
	// 验证禁言已被取消
	mute, err := livemembers.IsMute(testUserID, roomID)
	require.NoError(err)
	assert.False(mute.RoomMute)

	// 测试重复设置的情况（接口保持幂等性）
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	info, err = ActionAddAdmin(c)
	require.NoError(err)
	m = info.(handler.M)
	assert.EqualValues("添加成功", m["msg"])
	// 验证用户依然是房管且没有重复数据
	adminCount, err := collection.CountDocuments(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	})
	require.NoError(err)
	assert.Equal(int64(1), adminCount)
}

// TestActionRemoveAdmin 测试设置房管接口
func TestActionRemoveAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 清理脏数据
	collection := service.MongoDB.Collection(livemembers.CollectionName)
	testUserID := 346286
	_, err := collection.DeleteMany(ctx, bson.M{"_room_id": roomID, "user_id": testUserID})
	require.NoError(err)
	liveRoom, err := room.FindOne(bson.M{"creator_id": testUserID})
	require.NoError(err)
	now := util.TimeNow()
	_, err = collection.InsertOne(ctx, livemembers.Helper{
		RoomOID:     liveRoom.OID,
		RoomID:      roomID,
		UserID:      1,
		Username:    "test",
		IconURL:     "test.jpg",
		OperatorID:  int64(testUserID),
		Status:      livemembers.StatusAdmin,
		ExpireAt:    &now,
		UpdatedTime: now,
		CreatedTime: now,
	})
	require.NoError(err)

	// 测试参数错误的情况
	body := handler.M{
		"user_id": 0,
	}
	c := handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionRemoveAdmin(c)
	require.EqualError(err, "参数错误")

	// 测试直播间不存在的情况
	body = handler.M{
		"room_id": 10,
		"user_id": testUserID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = 99999
	_, err = ActionRemoveAdmin(c)
	assert.EqualError(err, "无法找到该聊天室")

	// 测试被设置用户不存在的情况
	body = handler.M{
		"room_id": roomID,
		"user_id": 9999999999,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionRemoveAdmin(c)
	require.EqualError(err, "无法找到该用户")

	// 测试房主取消自己房管的情况
	body = handler.M{
		"room_id": roomID,
		"user_id": testRoomCreatorID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	_, err = ActionRemoveAdmin(c)
	require.EqualError(err, "操作失败")

	// 测试取消房管的情况
	body = handler.M{
		"room_id": roomID,
		"user_id": testUserID,
	}
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	info, err := ActionRemoveAdmin(c)
	require.NoError(err)
	m := info.(handler.M)
	assert.EqualValues("移除成功", m["msg"])
	// 验证用户不再是房管
	err = collection.FindOne(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.Equal(mongo.ErrNoDocuments, err)

	// 测试重复取消房管的情况（保持接口幂等性）
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = testRoomCreatorID
	info, err = ActionRemoveAdmin(c)
	require.NoError(err)
	m = info.(handler.M)
	assert.EqualValues("移除成功", m["msg"])
	// 验证用户不再是房管
	err = collection.FindOne(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.Equal(mongo.ErrNoDocuments, err)
}

func TestSetRoomAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除脏数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(livemembers.CollectionName)
	testUserID := int64(3456835)
	_, err := collection.DeleteMany(ctx, bson.M{"room_id": roomID, "user_id": testUserID})
	require.NoError(err)

	// 测试给用户设置房管的情况
	// 生成缓存，用于验证该缓存在设置房管后被清理
	user, err := liveuser.Find(testUserID)
	require.NoError(err)
	room, err := room.Find(roomID)
	require.NoError(err)
	isRemoveMute, err := setRoomAdmin(user, room.OID, roomID, testRoomCreatorID, true)
	require.NoError(err)
	assert.False(isRemoveMute)
	// 验证相关数据是否生成
	err = collection.FindOne(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.NoError(err)

	// 测试取消房管的情况
	isRemoveMute, err = setRoomAdmin(user, room.OID, roomID, testRoomCreatorID, false)
	require.NoError(err)
	assert.False(isRemoveMute)
	err = collection.FindOne(ctx, bson.M{
		"room_id": roomID,
		"status":  livemembers.StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.Equal(mongo.ErrNoDocuments, err)
}
