package chatroom

import (
	"errors"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCreatorBackpackTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(creatorBackpackSendParam{}, "room_id", "gift_id", "gift_num")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(creatorBackpackSendParam{}, "room_id", "gift_id", "gift_num")
	kc.Check(creatorBackpackSendGiftResp{}, "ok", "gift_id", "remain", "message")
}

func TestActionCreatorBackpackUse(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("POST", "", true, "gift_num=100&room_id="+roomIDStr)
	_, err := ActionBackpackSend(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", "", true, "gift_id=301&gift_num=100&room_id="+roomIDStr)
	_, err = ActionBackpackSend(c)
	assert.NoError(err)
}

func TestCreatorBackpackSendParam_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	v := url.Values{}
	v.Set("room_id", "1248361")
	v.Set("gift_num", "100")
	c := handler.NewTestContext("POST", "/creator/use", true, v)
	var param creatorBackpackSendParam
	getLoadError := func() error {
		_, err := param.load(c)
		return err
	}
	assert.Equal(actionerrors.ErrParams, getLoadError())

	v.Set("gift_id", "123456")
	c = handler.NewTestContext("POST", "/creator/use", true, v)
	assert.EqualError(getLoadError(), "无法找到指定礼物")

	v.Set("gift_id", "3")
	c = handler.NewTestContext("POST", "/creator/use", true, v)
	resp, err := param.load(c)
	require.NoError(err)
	require.NotNil(resp)

	v.Set("room_id", roomIDStr)
	v.Set("gift_id", "310")
	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)

	cacheKey := keys.KeyOnlineGifts0.Format()
	service.Cache5s.Set(cacheKey, append(gifts, gift.Gift{GiftID: 310, Type: gift.TypeFree, Attr: 128}), 0)

	c = handler.NewTestContext("POST", "/creator/use", true, v)
	c.User().ID = testRoom.CreatorID
	assert.Equal(actionerrors.ErrDisableUseHotCardInCloseRoom, getLoadError())
	service.Cache5s.Flush()
}

func TestCreatorBackpackSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g, err := gift.FindShowingGiftByGiftID(301)
	require.NoError(err)
	require.NotNil(g)
	param := creatorBackpackSendParam{
		GiftID:     301,
		GiftNum:    1,
		r:          &room.Room{Helper: room.Helper{CreatorID: 12}},
		g:          g,
		sendUser:   new(liveuser.Simple),
		sendUserID: creatoritems.MaoerWalletUserID,
		bubble:     new(bubble.Simple),
		c:          handler.NewTestContext("POST", "", true, nil),
	}
	now := goutil.TimeNow()

	require.NoError(creatoritems.UnsetGift(12, 301))
	resp, err := param.send()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(301), resp.GiftID)
	assert.False(util.IntToBool(resp.Ok))
	assert.Equal("礼物数量不足哦~", resp.Message)
	assert.Zero(*resp.Remain)
	require.NoError(creatoritems.AddGiftToCreators([]int64{12}, g, 10, now.Unix(), now.Unix()+1000))
	resp, err = param.send()
	require.NoError(err)
	assert.True(util.IntToBool(resp.Ok), resp.Message)
	assert.Equal(int64(9), *resp.Remain)
	assert.Equal(int64(301), resp.GiftID)
}

func TestCreatorBackpackAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	r, err := room.Find(roomID)
	require.NoError(err)
	require.NotNil(r)
	param := creatorBackpackSendParam{
		GiftNum:    1,
		r:          r,
		sendUserID: creatoritems.MaoerWalletUserID,
		g: &gift.Gift{
			Price: 1,
			Point: 1,
			Type:  gift.TypeRebate,
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": roomID, "user_id": creatoritems.MaoerWalletUserID}
	_, err = livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)

	param.addMedalPoint()
	medal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(int64(2), medal.Point)

	param.g.Price = 0
	param.g.Attr.Set(gift.AttrDisableMedalPoint)
	param.addMedalPoint()
	medal = new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(int64(2), medal.Point)

	param.g = &gift.Gift{
		GiftID: 30001,
		Price:  1,
		Point:  6000,
		Type:   gift.TypeFree,
	}
	param.addMedalPoint()
	afterMedal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(afterMedal)
	require.NoError(err)
	assert.Equal(medal.Point, afterMedal.Point)
}

func TestCreatorBackpackAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := creatorBackpackSendParam{
		GiftNum:    1,
		r:          &room.Room{Helper: room.Helper{CreatorID: 12}},
		sendUserID: creatoritems.MaoerWalletUserID,
		g: &gift.Gift{
			Price: 1,
		},
	}
	param.r.RoomID = 1478963
	now := goutil.TimeNow()
	keys := []string{
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeCurrent, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeHourly, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeWeek, now),
	}
	require.NoError(service.Redis.Del(keys...).Err())
	assert.NotPanics(func() { param.addRevenueRank() })
	// 通过判断键是否存在来看榜单是否添加成功
	val, err := service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.EqualValues(2, val)
	// 测试开播状态下会增加本场榜
	param.r.Status.Open = room.StatusOpenTrue
	assert.NotPanics(func() { param.addRevenueRank() })
	val, err = service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.EqualValues(3, val)
}

func TestCreatorBackpackSendBuildIMMessage(t *testing.T) {
	assert := assert.New(t)

	param := creatorBackpackSendParam{
		sendUser:   &liveuser.Simple{UID: creatoritems.MaoerWalletUserID},
		sendUserID: creatoritems.MaoerWalletUserID,
		GiftNum:    1,
		r:          testRoom,
		g: &gift.Gift{
			GiftID: 301,
			Point:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.sendUser, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	assert.Len(param.broadcastElems, 1)

	param.g.Attr.Set(gift.AttrAlwaysNotify)
	param.buildIMMessage()
	assert.Len(param.broadcastElems, 3)
}

func TestCreatorBackpackSendAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := creatorBackpackSendParam{
		sendUserID: creatoritems.MaoerWalletUserID,
		g:          &gift.Gift{Point: 1},
		GiftNum:    1,
	}
	assert.NotPanics(func() { param.addPK() })

	param.g.Price = 10
	param.r = new(room.Room)
	param.addPK()
	assert.Empty(param.broadcastElems)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"fighters": bson.M{"$exists": false}})
	require.NoError(err)
	var lp livepk.LivePK
	err = col.FindOne(ctx,
		bson.M{"status": livepk.PKRecordStatusFighting}).Decode(&lp)
	require.NoError(err)
	param.r.RoomID = lp.Fighters[1].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	assert.Len(param.broadcastElems, 2)
}

func TestCreatorBackpackSendAddActivity(t *testing.T) {
	assert := assert.New(t)

	assert.NotPanics(func() {
		param := &creatorBackpackSendParam{
			uc:         mrpc.UserContext{},
			r:          testRoom,
			sendUserID: 12,
			sendUser:   &liveuser.Simple{UID: 12},
			g:          &gift.Gift{Point: 1},
			GiftNum:    10,
		}

		param.addActivity()
	})
}

func TestCreatorBackpackSendBroadcast(t *testing.T) {
	assert := assert.New(t)

	var notifyCount int
	cancel := mrpc.SetMock("im://broadcast/many", func(i interface{}) (interface{}, error) {
		notifyCount++
		tutil.PrintJSON(i)
		return true, errors.New("unittest error")
	})
	defer cancel()
	r := new(room.Room)
	r.CreatorUsername = "test"
	param := creatorBackpackSendParam{
		broadcastElems: []*userapi.BroadcastElem{
			{Type: 1, RoomID: 123, Payload: "test"},
		},
	}
	param.broadcast()
	assert.Equal(notifyCount, 1)
}
