package chatroom

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMessageKeys(t *testing.T) {
	var p messageHornParam

	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(p, "room_id", "message")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(p, "room_id", "message")
}

func TestActionMessageHorn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{{Pass: true}}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					VipID:      7,
					UserID:     noble7UserID,
					Type:       vip.TypeLiveNoble,
					Level:      7,
					ExpireTime: goutil.TimeNow().AddDate(1, 0, 1).Unix(),
				},
			},
		}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(vip.URLVipList, func(any) (any, error) {
		return handler.M{"Datas": []handler.M{
			{"id": 7, "level": 7, "title": "神话"},
		}}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock("im://broadcast/all", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	_, err := ActionMessageHorn(nil)
	assert.EqualError(err, "全站喇叭暂未开放，请稍作等待哦~")

	config.Conf.Web.HornOpen = true
	cfg := params.Global{
		Key: params.KeyGlobal,
		Maintain: &params.GlobalMaintain{
			StartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
			EndTime:   goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	key := keys.KeyParams1.Format(cfg.Key)
	hornConfigJSON, err := json.Marshal(cfg)
	require.NoError(err)
	err = service.LRURedis.Set(key, hornConfigJSON, time.Second).Err()
	require.NoError(err)
	_, err = ActionMessageHorn(nil)
	assert.EqualError(err, fmt.Sprintf("功能维护中，预计 %s 恢复使用。大咖及以上贵族用户已延长 3 天贵族有效期", goutil.TimeNow().Add(time.Hour).Format("01-02 15:04")))

	cfg = params.Global{
		Key: params.KeyGlobal,
		Maintain: &params.GlobalMaintain{
			StartTime: goutil.TimeNow().Add(-2 * time.Hour).Unix(),
			EndTime:   goutil.TimeNow().Add(-time.Hour).Unix(),
		},
	}
	hornConfigJSON, err = json.Marshal(cfg)
	require.NoError(err)
	err = service.LRURedis.Set(key, hornConfigJSON, 2*time.Second).Err()
	require.NoError(err)
	param := map[string]interface{}{
		"room_id": openingRoomID,
		"message": strings.Repeat("a", 42), // 超 40 了
	}
	c := handler.NewTestContext(http.MethodPost, "/message/horn", true, param)
	c.User().ID = 3456835
	_, err = ActionMessageHorn(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = map[string]interface{}{
		"room_id": openingRoomID,
		"message": "ActionMessageHorn",
	}
	c = handler.NewTestContext(http.MethodPost, "/message/horn", true, param)
	c.User().ID = noble7UserID
	assert.NoError(userstatus.GeneralSetOne(bson.M{"user_id": noble7UserID},
		bson.M{"user_id": noble7UserID, "noble_horn_num": 100}))
	r, err := ActionMessageHorn(c)
	require.NoError(err)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(r, "horn_num")

	// 本房禁言
	expireTime := goutil.TimeNow().Add(time.Minute)
	member := &livemembers.Member{
		Helper: livemembers.Helper{
			UserID:   noble7UserID,
			Status:   livemembers.StatusMute,
			RoomID:   114693474,
			ExpireAt: &expireTime,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemembers.Collection()
	_, err = col.DeleteMany(ctx, bson.M{"user_id": member.UserID})
	require.NoError(err)
	_, err = col.InsertOne(ctx, member)
	require.NoError(err)
	param = map[string]interface{}{
		"room_id": member.RoomID,
		"message": "ActionMessageHorn",
	}
	c = handler.NewTestContext(http.MethodPost, "/message/horn", true, param)
	c.User().ID = noble7UserID
	_, err = ActionMessageHorn(c)
	assert.EqualError(err, "您已被禁言，无法在本直播间发送全站喇叭")
}

func TestNewMessageHornParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	newC := func(body interface{}, userID ...int64) *handler.Context {
		c := handler.NewTestContext(http.MethodPost, "/message/horn", true, body)
		if len(userID) != 0 {
			c.User().ID = userID[0]
		}
		return c
	}

	filter := bson.M{"user_id": noble7UserID}
	_, err := livemembers.FindOneAndDelete(filter)
	require.NoError(err)

	// 参数不正确
	body := handler.M{"room_id": "123", "message": "test"}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrParams, err)

	// 消息过长
	body = handler.M{"room_id": openingRoomID, "message": strings.Repeat("a", 42)}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrParams, err)

	body = handler.M{"room_id": openingRoomID, "message": strings.Repeat("测", 21)}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrParams, err)

	// 找不到房间
	body = handler.M{"room_id": 1234567, "message": "test"}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 礼物房
	body = handler.M{"room_id": room.TestLimitedRoomID, "message": "test"}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 被封禁的房间
	body = handler.M{"room_id": bannedRoomID, "message": "test"}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrBannedRoom, err)

	// 未开播的房间
	r, err := room.FindOne(bson.M{"status.open": 0}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
	body = handler.M{"room_id": r.RoomID, "message": "test"}
	_, err = newMessageHornParam(newC(body))
	assert.Equal(actionerrors.ErrClosedRoomAlt, err)

	// 用户不存在
	body = handler.M{
		"room_id": openingRoomID,
		"message": "test",
	}
	_, err = newMessageHornParam(newC(body, 1234567))
	assert.Equal(actionerrors.ErrUserNotFound, err)

	assert.NoError(userstatus.GeneralSetOne(bson.M{"user_id": noble7UserID},
		bson.M{"noble_horn_num": 100}))

	body = handler.M{
		"room_id": openingRoomID,
		"message": strings.Repeat("a", 40),
	}
	param, err := newMessageHornParam(newC(body, noble7UserID))
	require.NoError(err)
	assert.NotNil(param.room)

	body = handler.M{
		"room_id": openingRoomID,
		"message": strings.Repeat("测", 20),
	}
	param, err = newMessageHornParam(newC(body, noble7UserID))
	require.NoError(err)
	assert.NotNil(param.room)

	now := goutil.TimeNow()
	expireAt := now.Add(time.Minute)
	m := &livemembers.Member{
		Helper: livemembers.Helper{
			RoomID:      0,
			UserID:      noble7UserID,
			CreatedTime: now,
			ExpireAt:    &expireAt,
			Status:      livemembers.StatusHornMute,
			UpdatedTime: now,
			OperatorID:  9074511,
		},
	}
	require.NoError(m.SetMute())

	_, err = newMessageHornParam(newC(body, noble7UserID))
	assert.EqualError(err, "全站喇叭已被封禁，暂时无法使用")
}

func TestMessageHornParam_Check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					VipID:      7,
					UserID:     noble7UserID,
					Type:       vip.TypeLiveNoble,
					Level:      7,
					ExpireTime: goutil.TimeNow().AddDate(1, 0, 1).Unix(),
				},
			},
		}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(vip.URLVipList, func(any) (any, error) {
		return handler.M{"Datas": []handler.M{
			{"id": 7, "level": 7, "title": "神话"},
		}}, nil
	})
	defer cancel()

	param := messageHornParam{
		RoomID:  openingRoomID,
		Message: "test",
		room:    new(room.Room),
		c:       handler.NewTestContext(http.MethodPost, "/message/horn", true, nil),
		user:    &liveuser.Simple{UID: 12},
	}
	key := keys.KeyUsersBlockList0.Format()
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{
			Score:  blocklist.StatusBlockUserForever,
			Member: param.user.UserID(),
		}).Err())
	assert.Equal(actionerrors.ErrBannedUser, param.check())

	m := livemembers.Member{
		Helper: livemembers.Helper{
			UserID: 88888,
			Status: livemembers.StatusMute,
		},
	}
	require.NoError(m.SetMute())
	param.user.UID = m.UserID
	assert.Equal(actionerrors.ErrGlobalMuteUser, param.check())

	param.user.UID = 12
	require.NoError(service.Redis.ZRem(key, param.user.UserID()).Err())
	assert.Equal(actionerrors.ErrNoAuthority, param.check())

	cancel = mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{{Pass: true}}, nil
	})
	defer cancel()
	param.user.UID = noble7UserID
	require.NoError(service.Redis.Del(keys.KeyNobleUserVips1.Format(param.user.UID)).Err())
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": param.user.UserID()},
		bson.M{"noble_horn_num": 0}))
	assert.EqualError(param.check(), "全站喇叭已用完")
	require.NoError(livemeta.SetSpeakSettings(param.room.RoomID, nil))
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": param.user.UserID()},
		bson.M{"noble_horn_num": 10}))
	assert.NoError(param.check())
}

func TestNewBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(12)
		now        = goutil.TimeNow()
	)

	paramsJSON, err := json.Marshal(params.DefaultBubble())
	require.NoError(err)
	key := keys.KeyParams1.Format(params.KeyBubble)
	err = service.LRURedis.Set(key, paramsJSON, time.Minute).Err()
	require.NoError(err)
	service.Cache10s.Flush()
	err = service.LiveDB.Delete(&livecustom.LiveCustom{}, "element_id = ? AND custom_type = ?", testUserID, livecustom.TypeNobleHornBubble).Error
	require.NoError(err)

	param := messageHornParam{
		uv: &vip.UserVip{
			UserID: testUserID,
			Level:  1,
			Type:   vip.TypeLiveNoble,
		},
	}
	param.newBubble()
	require.Nil(param.bubble)

	param.uv.Level = 4
	param.newBubble()
	require.NotNil(param.bubble)
	assert.Equal("#FFFFFF", param.bubble.NormalColor)
	assert.Equal("#790202", param.bubble.HighlightColor)

	param.uv.Level = 5
	param.newBubble()
	assert.Equal("#FFFFFF", param.bubble.NormalColor)
	assert.Equal("#3D1993", param.bubble.HighlightColor)

	param.uv.Level = 6
	param.newBubble()
	assert.Equal("#FFFFFF", param.bubble.NormalColor)
	assert.Equal("#842D11", param.bubble.HighlightColor)

	param.uv.Level = 7
	param.newBubble()
	assert.Equal("#FFFFFF", param.bubble.NormalColor)
	assert.Equal("#48FFF4", param.bubble.HighlightColor)

	highnessBubbleID := int64(90)
	service.Cache10s.Set(keys.KeyBubbles.Format(),
		[]bubble.Bubble{{BubbleID: highnessBubbleID, HighlightColor: "#F3DBB1", NormalColor: "#FFFFFF"}}, time.Second)
	highnessBubble, err := bubble.FindSimple(highnessBubbleID)
	require.NoError(err)
	require.NotNil(highnessBubble)
	param.uv.Type = vip.TypeLiveHighness
	param.uv.Level = 1
	param.newBubble()
	require.NotNil(param.bubble)
	assert.Equal(bubble.TypeStrCustom, param.bubble.Type)
	assert.Equal("#FFFFFF", param.bubble.NormalColor)
	assert.Equal("#F3DBB1", param.bubble.HighlightColor)
	assert.Equal(highnessBubble.ImageURL, param.bubble.ImageURL)

	// 测试自定义贵族喇叭
	p := &params.HornBubble{
		StartTime: now.AddDate(0, 0, -1).Unix(),
		EndTime:   now.AddDate(0, 0, 1).Unix(),
		NobleBubbles: []params.NobleBubble{
			{
				Type:     vip.TypeLiveNoble,
				Level:    vip.NobleLevel7,
				BubbleID: highnessBubbleID,
			},
		},
	}
	err = livecustom.AddUserCustomNobleHornBubble(testUserID, p, p.StartTime, p.EndTime)
	require.NoError(err)
	param.uv.Type = vip.TypeLiveNoble
	param.uv.Level = vip.NobleLevel7
	param.newBubble()
	require.NotNil(param.bubble)
	assert.Equal(bubble.TypeStrCustom, param.bubble.Type)
	assert.Equal(p.NobleBubbles[0].BubbleID, param.bubble.BubbleID)

	// 测试没有查询到定制气泡，使用默认的气泡
	service.Cache10s.Set(keys.KeyBubbles.Format(),
		[]bubble.Bubble{{BubbleID: 84, HighlightColor: "#F3DBB1", NormalColor: "#790202"}}, time.Second)
	param.uv.Type = vip.TypeLiveNoble
	param.uv.Level = vip.NobleLevel4
	param.newBubble()
	require.NotNil(param.bubble)
	assert.Equal(bubble.TypeStrCustom, param.bubble.Type)
	assert.EqualValues(84, param.bubble.BubbleID)
}

func TestNewMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache10s.Flush()

	param := messageHornParam{
		RoomID:  -1,
		room:    &room.Room{Helper: room.Helper{CreatorUsername: ""}},
		Message: " TestMessageHorn  Send ",
		c:       handler.NewTestContext(http.MethodPost, "/api", true, nil),
		uv:      new(vip.UserVip),
	}
	param.user = &liveuser.Simple{UID: 12}
	foundUser, err := mowangskuser.FindByUserID(param.c.UserID())
	require.NoError(err)
	require.NotNil(foundUser)

	// 贵族 7 级
	param.uv.Type = vip.TypeLiveNoble
	param.uv.Level = 7
	param.newBubble()
	require.NotNil(param.bubble)
	message := param.newMessage(foundUser.Username)
	assert.Equal(`<font color="#48FFF4">零月</font>`+
		`<font color="#FFFFFF">：&nbsp;TestMessageHorn&nbsp;&nbsp;Send&nbsp;（</font>`+
		`<font color="#48FFF4"></font>`+
		`<font color="#FFFFFF">的直播间）</font>`, message)

	// 上神 1 级
	param.uv.Type = vip.TypeLiveHighness
	param.uv.Level = 1
	param.bubble = nil
	param.newBubble()
	require.NotNil(param.bubble)
	message = param.newMessage(foundUser.Username)
	assert.Equal(`<font color="#F3DBB1">零月</font>`+
		`<font color="#FFFFFF">：&nbsp;TestMessageHorn&nbsp;&nbsp;Send&nbsp;（</font>`+
		`<font color="#F3DBB1"></font>`+
		`<font color="#FFFFFF">的直播间）</font>`, message)
}

func TestMessageHornSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("im://broadcast/all", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	paramsJSON, err := json.Marshal(params.DefaultBubble())
	require.NoError(err)
	key := keys.KeyParams1.Format(params.KeyBubble)
	err = service.LRURedis.Set(key, paramsJSON, time.Minute).Err()
	require.NoError(err)
	service.Cache10s.Flush()

	param := messageHornParam{
		RoomID:  -1,
		room:    &room.Room{Helper: room.Helper{CreatorUsername: ""}},
		Message: "TestMessageHornSend",
		c:       handler.NewTestContext(http.MethodPost, "/api", true, nil),
		uv:      &vip.UserVip{Type: vip.TypeLiveNoble, Level: 7},
	}
	param.uv.Level = 7
	param.user = &liveuser.Simple{UID: 12}
	param.c.C.Request, _ = http.NewRequest("POST", "/message/horn", nil)
	param.newBubble()
	require.NoError(param.send())
	require.NotNil(param.horn)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := notifymessages.Collection()
	assert.NoError(collection.FindOneAndDelete(ctx, bson.M{"_id": param.horn.OID}).Err())

	j1, _ := json.Marshal(param.horn)
	j2, _ := json.Marshal(map[string]interface{}{
		"type":        liveim.TypeNotify,
		"notify_type": liveim.TypeMessage,
		"event":       liveim.EventHorn,
		"room_id":     param.RoomID,
		"message":     param.horn.Message,
		"bubble": map[string]interface{}{
			"type":      "custom",
			"bubble_id": 87,
			"shine":     1,
		},
		"notify_bubble": map[string]interface{}{
			"type":      "custom",
			"bubble_id": 87,
			"shine":     1,
		},
	})
	assert.JSONEq(string(j1), string(j2))
	logger.Debug(string(j2))
}

func TestMessageHornParam_decHorn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.Zero(userstatus.HornNum(12345))
	param := messageHornParam{
		user:    &liveuser.Simple{UID: 12345},
		hornNum: 0,
	}
	assert.EqualError(param.decHorn(), "全站喇叭已用完")

	param.user.UID = 3456835
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": param.user.UserID()},
		bson.M{"noble_horn_num": 10}))
	require.NoError(param.decHorn())
	assert.Equal(int64(9), param.hornNum)
}

func TestMakeOSUUID(t *testing.T) {
	assert := assert.New(t)
	os := [3]goutil.Platform{goutil.Android, goutil.IOS, goutil.Web}
	prefix := [3]string{"1", "2", "3"}
	for i := 0; i < 3; i++ {
		if s, err := makeOSUUID(os[i]); assert.NoError(err) {
			assert.Truef(strings.HasPrefix(s, prefix[i]), "%d: %s", os, s)
		}
	}
}

func TestCheckText(t *testing.T) {
	assert := assert.New(t)

	f := func(test string) error {
		p := messageHornParam{
			c:       handler.NewTestContext("POST", "", true, nil),
			Message: test,
			RoomID:  15,
			room: &room.Room{
				Helper: room.Helper{
					RoomID:    15,
					CatalogID: 116,
					CreatorID: 15,
				},
			},
			horn: notifymessages.NewHorn(99, 15, test, nil, "127.0.0.1"),
		}
		p.c.C.Request, _ = http.NewRequest("POST", "/message/horn", nil)
		p.c.C.Request.Header.Set("UserAgent", "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)")
		return p.checkText()
	}

	rpcResp := []*scan.BaseCheckResult{{Pass: false}}
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return rpcResp, nil
	})
	defer cancel()

	// fat 没有配置黑名单，此处直接 mock
	list := []string{"妈的", "傻逼", "你麻痹", "法轮功", "赌博", "炸金花", "嫖娼", "鸦片", "冰毒"}
	for _, s := range list {
		err := f(s)
		assert.Equal(actionerrors.ErrHornMessageIllegal, err)
	}

	rpcResp = []*scan.BaseCheckResult{{Pass: true, Labels: []string{scan.LabelEvil}}}
	err := f("广告拦截测试")
	assert.Equal(actionerrors.ErrHornMessageSensitive, err)

	rpcResp[0].Labels = []string{}
	err = f("正常")
	assert.NoError(err)
}
