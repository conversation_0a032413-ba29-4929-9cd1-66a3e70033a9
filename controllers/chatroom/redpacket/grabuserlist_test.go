package redpacket

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

const (
	testGiftID1   = int64(1000)
	testGiftID2   = int64(2000)
	testRoomID    = int64(22489473)
	TestUserID    = int64(12)
	testCreatorID = int64(456623)
)

func TestRedPacketGrabUserListRespTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(grabUserListResp{}, "title", "data", "gift_remain_num", "pagination")
	kc.Check(logData{}, "user_id", "username", "luckiest", "gift")
	kc.Check(logGiftInfo{}, "gift_id", "name", "icon_url", "most_valuable")
}

func TestNewGrabUserListParam(t *testing.T) {
	assert := assert.New(t)

	// 测试房间 ID 参数错误
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/grabuserlist?room_id=0", false, nil)
	_, err := newGrabUserListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试红包 ID 错误
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/grabuserlist?room_id=1", false, nil)
	_, err = newGrabUserListParam(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestGrabUserListParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试红包不存在
	param := &grabUserListParam{
		roomID:       testRoomID,
		redPacketOID: primitive.NewObjectID(),
	}
	assert.EqualError(param.check(), "礼物红包不存在")

	// 测试红包已过期
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	redPacket := &redpacket.LiveRedPacket{
		OID:        primitive.NewObjectID(),
		GoodsID:    342345,
		UserID:     TestUserID,
		RoomID:     testRoomID,
		CreatorID:  testCreatorID,
		Status:     redpacket.StatusExpired,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, -7).Unix(),
	}
	_, err := redpacket.LiveRedPacketCollection().DeleteOne(ctx, bson.M{"user_id": TestUserID})
	require.NoError(err)
	_, err = redpacket.LiveRedPacketCollection().InsertOne(ctx, redPacket)
	require.NoError(err)

	param.redPacketOID = redPacket.OID
	assert.EqualError(param.check(), "礼物红包不存在")

	// 测试红包商品不存在
	_, err = redpacket.LiveRedPacketCollection().UpdateOne(ctx, bson.M{"_id": redPacket.OID},
		bson.M{"$set": bson.M{"status": redpacket.StatusGrabbing, "expire_time": 0}})
	require.NoError(err)
	assert.EqualError(param.check(), "商品不存在")

	// 测试红包商品配置错误
	goods := &livegoods.LiveGoods{
		ID:   342345,
		Sort: 1,
		Type: livegoods.GoodsTypeRedPacket,
	}
	require.NoError(service.LiveDB.Table(goods.TableName()).Where("id = ?", goods.ID).Delete("").Error)
	require.NoError(service.LiveDB.Create(goods).Error)
	assert.EqualError(param.check(), "商品不存在")

	// 测试红包礼物数量异常
	more := livegoods.More{
		RedPacket: &livegoods.RedPacket{
			Type: 1,
		},
	}
	decodeMore, err := json.Marshal(more)
	require.NoError(err)
	require.NoError(service.LiveDB.Table(goods.TableName()).Where("id = ?", goods.ID).Update("more", decodeMore).Error)
	assert.EqualError(param.check(), "商品不存在")
}

func TestActionRedPacketGrabUserList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 构建测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	nowUnix := goutil.TimeNow().Unix()
	goods := &livegoods.LiveGoods{
		ID:   342345,
		Sort: 1,
		Type: livegoods.GoodsTypeRedPacket,
	}
	more := livegoods.More{
		RedPacket: &livegoods.RedPacket{
			Gifts: []livegoods.RedPacketGift{
				{
					ID:  testGiftID2,
					Num: 4,
				},
				{
					ID:           testGiftID1,
					Num:          1,
					MostValuable: true,
				},
			},
		},
	}
	decodeMore, err := json.Marshal(more)
	require.NoError(err)
	goods.More = string(decodeMore)
	require.NoError(service.LiveDB.Table(goods.TableName()).Where("id = ?", goods.ID).Delete("").Error)
	require.NoError(service.LiveDB.Create(goods).Error)

	redPacket := &redpacket.LiveRedPacket{
		OID:           primitive.NewObjectID(),
		GoodsID:       goods.ID,
		UserID:        TestUserID,
		RoomID:        testRoomID,
		CreatorID:     testCreatorID,
		Status:        redpacket.StatusGrabbing,
		RemainGiftNum: 0,
		Info: &redpacket.PacketInfo{
			KeywordType: 1,
			Keyword:     "test",
		},
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
	}
	_, err = redpacket.LiveRedPacketCollection().DeleteOne(ctx, bson.M{"user_id": TestUserID})
	require.NoError(err)
	_, err = redpacket.LiveRedPacketCollection().InsertOne(ctx, redPacket)
	require.NoError(err)

	logs := make([]interface{}, 0, 4)
	for i := 0; i < 4; i++ {
		w := &redpacket.LiveRedPacketLog{
			RedPacketOID: redPacket.OID,
			UserID:       TestUserID + int64(i),
			GiftID:       2000,
			CreateTime:   nowUnix + int64(i),
			ModifiedTime: nowUnix + int64(i),
		}
		if i == 1 {
			w.GiftID = 1000
			w.Luckiest = true
		}
		logs = append(logs, w)
	}
	_, err = redpacket.LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"create_time": bson.M{"$gt": 0}})
	require.NoError(err)
	_, err = redpacket.LiveRedPacketLogCollection().InsertMany(ctx, logs)
	require.NoError(err)

	// 测试获取红包记录
	api := fmt.Sprintf("/api/v2/chatroom/redpacket/grabuserlist?room_id=%d&red_packet_id=%s&p=1&pagesize=2",
		testRoomID, redPacket.OID.Hex())
	c := handler.NewTestContext(http.MethodGet, api, false, nil)
	res, err := ActionRedPacketGrabUserList(c)
	require.NoError(err)

	r := res.(*grabUserListResp)
	require.Len(r.Data, 3)
	assert.Equal(testGiftID1, r.Data[0].Gift.GiftID)
	assert.Equal(1, r.Data[0].Gift.MostValuable)
	assert.Equal(1, r.Data[0].Luckiest)
	assert.EqualValues(0, r.GiftRemainNum)
	assert.EqualValues(3, r.Pagination.Count)
	assert.EqualValues(2, r.Pagination.PageSize)

	// 测试获取第二页
	api = fmt.Sprintf("/api/v2/chatroom/redpacket/grabuserlist?room_id=%d&red_packet_id=%s&p=2&pagesize=2",
		testRoomID, redPacket.OID.Hex())
	c = handler.NewTestContext(http.MethodGet, api, false, nil)
	res, err = ActionRedPacketGrabUserList(c)
	require.NoError(err)

	r = res.(*grabUserListResp)
	require.Len(r.Data, 1)
	assert.NotEqual(testGiftID1, r.Data[0].Gift.GiftID)

	// 测试没有记录情况
	api = fmt.Sprintf("/api/v2/chatroom/redpacket/grabuserlist?room_id=%d&red_packet_id=%s&p=3&pagesize=2",
		testRoomID, redPacket.OID.Hex())
	c = handler.NewTestContext(http.MethodGet, api, false, nil)
	res, err = ActionRedPacketGrabUserList(c)
	require.NoError(err)

	r = res.(*grabUserListResp)
	assert.Equal("大家的手气", r.Title)
	assert.Empty(r.Data)
}

func TestGrabUserListParam_findGrabUserList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := grabUserListParam{
		page:         1,
		pageSize:     2,
		redPacketOID: primitive.NewObjectID(),
		gifts: map[int64]*gift.Gift{
			testGiftID1: {
				GiftID: testGiftID1,
				Name:   "测试礼物 1",
			},
			testGiftID2: {
				GiftID: testGiftID2,
				Name:   "测试礼物 2",
			},
		},
		redPacket:      &redpacket.LiveRedPacket{},
		goodsRedPacket: &livegoods.RedPacket{},
	}

	nowUnix := goutil.TimeNow().Unix()
	logs := make([]interface{}, 0, 4)
	for i := 0; i < 4; i++ {
		w := &redpacket.LiveRedPacketLog{
			RedPacketOID: param.redPacketOID,
			UserID:       TestUserID + int64(i),
			GiftID:       2000,
			CreateTime:   nowUnix + int64(i),
			ModifiedTime: nowUnix + int64(i),
		}
		if i == 1 {
			w.GiftID = 1000
			w.Luckiest = true
		}
		logs = append(logs, w)
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := redpacket.LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"create_time": bson.M{"$gt": 0}})
	require.NoError(err)
	_, err = redpacket.LiveRedPacketLogCollection().InsertMany(ctx, logs)
	require.NoError(err)

	// 测试获取第一页
	require.NoError(param.findGrabUserList())
	assert.Len(param.resp.Data, 3)

	// 测试获取第二页
	param.page = 2
	require.NoError(param.findGrabUserList())
	assert.Len(param.resp.Data, 1)

	// 测试获取第三页
	param.page = 3
	require.NoError(param.findGrabUserList())
	assert.Empty(param.resp.Data)
}
