package redpacket

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liveuserspecialredpacket"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestConfigTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(configResp{}, "data", "rule_url", "tip")
	kc.Check(configData{}, "goods_id", "type", "corner_icon_url",
		"price", "is_free", "remain_num", "time_left", "gift_total_num", "gifts", "wait_durations")
	kc.Check(configGiftInfo{}, "gift_id", "name", "icon_url", "num")
}

func TestActionRedPacketConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=0", false, nil)
	_, err := ActionRedPacketConfig(c)
	assert.Equal(actionerrors.ErrParams, err)

	redPacketParam := params.RedPacket{
		Key:            params.KeyRedPacket,
		DisableRoomIDs: []int64{1},
	}
	cacheKey := keys.KeyParams1.Format(redPacketParam.Key)
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(redPacketParam), 10*time.Second).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=1", false, nil)
	param, err := ActionRedPacketConfig(c)
	assert.NoError(err)
	assert.Empty(param.(*configResp).Data)

	// 构建测试数据
	key := keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeRedPacket)
	_, err = service.LRURedis.Del(key).Result()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	g1 := gift.Gift{
		OID:       primitive.NewObjectID(),
		Name:      "测试红包礼物 1",
		NameClean: "测试红包礼物 1",
		GiftID:    40100,
		Icon:      "oss://live/gifts/icons/1000.png",
		Type:      gift.TypeNormal,
	}
	g2 := gift.Gift{
		OID:       primitive.NewObjectID(),
		Name:      "测试红包礼物 2",
		NameClean: "测试红包礼物 2",
		GiftID:    40101,
		Icon:      "oss://live/gifts/icons/1000.png",
		Type:      gift.TypeNormal,
	}
	_, err = gift.Collection().DeleteMany(ctx,
		bson.M{"gift_id": bson.M{"$in": []int64{40100, 40101, 40102, 40103, 40343}}})
	require.NoError(err)
	_, err = gift.Collection().InsertMany(ctx, []interface{}{g1, g2})
	require.NoError(err)

	// 创建仅主播可发送的红包商品
	require.NoError(createLiveGoods(233345, 1, 40100, livegoods.AttrBitMaskOnlyCreator, []int64{}))
	// 创建通用专属红包商品（仅专属用户可发送）
	require.NoError(createLiveGoods(233346, 1, 40101, 0, []int64{1, 2, 4381915, 4}))
	// 创建免费红包商品（仅发放了该红包的用户可发送）
	require.NoError(createLiveGoods(233347, 0, 40101, livegoods.AttrBitMaskSpecialFree, []int64{}))

	// 测试下发红包配置
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=22489473", false, nil)
	res, err := ActionRedPacketConfig(c)
	require.NoError(err)
	data, ok := res.(*configResp)
	require.True(ok)
	assert.Equal("关注主播的观众可领取", data.Tip)
	require.Len(data.Data, 1)
	assert.EqualValues(17, data.Data[0].GoodsID)
	require.Len(data.Data[0].Gifts, 2)
	assert.EqualValues(40100, data.Data[0].Gifts[0].GiftID)

	// 测试仅主播可用礼物红包
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=22489473", false, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 10,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	res, err = ActionRedPacketConfig(c)
	require.NoError(err)
	data, ok = res.(*configResp)
	require.True(ok)
	require.Len(data.Data, 2)
	assert.EqualValues(233345, data.Data[1].GoodsID)

	// 测试直播间是否在礼物红包白名单中
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=4381915", false, nil)
	res, err = ActionRedPacketConfig(c)
	require.NoError(err)
	data, ok = res.(*configResp)
	require.True(ok)
	require.Len(data.Data, 2)
	assert.EqualValues(233346, data.Data[1].GoodsID)

	// 测试用户有免费红包的情况
	now := goutil.TimeNow().Unix()
	require.NoError(createSpecialRedpacket(12, 233347, 3, now, now+60))
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=4381915", true, nil)
	res, err = ActionRedPacketConfig(c)
	require.NoError(err)
	data, ok = res.(*configResp)
	require.True(ok)
	require.Len(data.Data, 3)
	assert.EqualValues(233347, data.Data[2].GoodsID)

	// 测试客户端版本号低于 6.2.3 的情况
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/redpacket/config?room_id=4381915", true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.2.2 (iOS;11.3;iPhone8,1)")
	res, err = ActionRedPacketConfig(c)
	require.NoError(err)
	require.True(ok)
	data, ok = res.(*configResp)
	require.True(ok)
	require.Len(data.Data, 2)
}

func TestGetUserOwnedSpecialRedPacketMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGoods := &livegoods.GoodsWithMore{
		LiveGoods: livegoods.LiveGoods{
			ID:    647456,
			Type:  livegoods.GoodsTypeRedPacket,
			Price: 0,
		},
		MoreInfo: livegoods.More{
			RedPacket: &livegoods.RedPacket{Attr: livegoods.AttrBitMaskSpecialFree},
		},
	}
	testUserID := int64(215451)
	now := goutil.TimeNow().Unix()
	endTime := now + 60
	require.NoError(createSpecialRedpacket(testUserID, testGoods.ID, 3, now, endTime))

	// 测试用户未登录的情况
	res, err := getUserOwnedSpecialRedPacketMap(0, []*livegoods.GoodsWithMore{testGoods})
	require.NoError(err)
	assert.Empty(res)

	// 测试用户有特殊红包的情况
	res, err = getUserOwnedSpecialRedPacketMap(testUserID, []*livegoods.GoodsWithMore{testGoods})
	require.NoError(err)
	redPacket, ok := res[testGoods.ID]
	require.True(ok)
	assert.Equal(testUserID, redPacket.UserID)
	assert.Equal(testGoods.ID, redPacket.GoodsID)
	assert.EqualValues(3, redPacket.Num)
	assert.EqualValues(endTime, redPacket.EndTime)

	// 测试用户有多条相同的特殊红包发放记录的情况
	endTime = now + 10
	require.NoError(createSpecialRedpacket(testUserID, testGoods.ID, 1, now, endTime))
	res, err = getUserOwnedSpecialRedPacketMap(testUserID, []*livegoods.GoodsWithMore{testGoods, testGoods})
	require.NoError(err)
	redPacket, ok = res[testGoods.ID]
	require.True(ok)
	assert.Equal(testUserID, redPacket.UserID)
	assert.Equal(testGoods.ID, redPacket.GoodsID)
	assert.EqualValues(4, redPacket.Num)
	assert.EqualValues(endTime, redPacket.EndTime)

	// 测试用户无特殊红包的情况
	require.NoError(liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	res, err = getUserOwnedSpecialRedPacketMap(0, []*livegoods.GoodsWithMore{testGoods})
	require.NoError(err)
	assert.Empty(res)
}

func createSpecialRedpacket(userID, goodsID, num, startTime, endTime int64) error {
	err := liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().
		Delete("", "user_id = ?", testUserID).Error
	if err != nil {
		return err
	}
	lr := liveuserspecialredpacket.LiveUserSpecialRedPacket{
		UserID:    userID,
		GoodsID:   goodsID,
		GainNum:   num,
		Num:       num,
		StartTime: startTime,
		EndTime:   endTime,
	}
	return lr.DB().Create(&lr).Error
}

func createLiveGoods(goodsID int64, price int, giftID int64, redpacketAttr goutil.BitMask, roomAllowList []int64) error {
	err := livegoods.LiveGoods{}.DB().Delete("", "id = ?", goodsID).Error
	if err != nil {
		return err
	}
	now := goutil.TimeNow()
	gd := &livegoods.LiveGoods{
		ID:            goodsID,
		Type:          livegoods.GoodsTypeRedPacket,
		Price:         price,
		Sort:          1,
		SaleStartTime: now.Add(-time.Hour).Unix(),
		SaleEndTime:   now.Add(time.Hour).Unix(),
	}
	gdMore := livegoods.More{
		RedPacket: &livegoods.RedPacket{
			Attr:          redpacketAttr,
			RoomAllowList: roomAllowList,
			Gifts: []livegoods.RedPacketGift{
				{
					ID:  giftID,
					Num: 10,
				},
			},
		},
	}
	gdMoreEncode, err := json.Marshal(gdMore)
	if err != nil {
		return err
	}
	gd.More = string(gdMoreEncode)
	return gd.DB().Create(gd).Error
}
