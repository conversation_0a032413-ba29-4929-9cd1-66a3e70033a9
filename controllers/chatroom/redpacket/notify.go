package redpacket

import (
	"html"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const bubbleIDRedPacket int64 = 11

// RoomMessage 房间内消息
type RoomMessage struct {
	Type      string           `json:"type"`
	Event     string           `json:"event"`
	RoomID    int64            `json:"room_id"`
	User      *liveuser.Simple `json:"user"`
	Bubble    *bubble.Simple   `json:"bubble,omitempty"`
	RedPacket *Elem            `json:"red_packet"`
}

// NotifyPayload 发红包飘屏
type NotifyPayload struct {
	Type         string           `json:"type"`
	NotifyType   string           `json:"notify_type"`
	Event        string           `json:"event"`
	NotifyQueue  int              `json:"notify_queue,omitempty"`
	RoomID       int64            `json:"room_id"`
	User         *liveuser.Simple `json:"user"`
	RedPacket    *Elem            `json:"red_packet"`
	NotifyBubble *bubble.Simple   `json:"notify_bubble,omitempty"`
	Message      string           `json:"message"`
}

// broadcast 发红包直播间消息和全站飘屏
func (param *sendParam) broadcast() {
	defer func() {
		if len(param.broadcastElems) == 0 {
			return
		}
		err := userapi.BroadcastMany(param.broadcastElems)
		if err != nil {
			logger.Error(err)
			return
		}
	}()

	// 查询主播和发红包用户的信息（主播可以在自己的直播间发红包）
	userIDs := util.Uniq([]int64{param.room.CreatorID, param.userID})
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		logger.WithField("user_ids", userIDs).Errorf("用户信息查询失败: %v", err)
		return
	}
	sender, ok := userMap[param.userID]
	if !ok {
		logger.WithField("user_id", param.userID).Error("发红包用户的信息不存在")
		return
	}

	redPacketElem := &Elem{
		RedPacketID:    param.redPacket.OID.Hex(),
		Type:           param.liveGoodsMore.RedPacket.Type,
		CornerIconURL:  param.liveGoodsMore.RedPacket.CornerIconURL(),
		Price:          param.liveGoods.Price,
		GiftTotalPrice: param.giftTotalPrice,
		SkinURL:        param.skinURL,
		Sender:         sender,
		GiftTotalNum:   param.giftTotalNum,
		Gifts:          param.redPacketGifts,
		RemainDuration: param.Countdown,
	}
	// 直播间消息和全站飘屏
	param.broadcastElems = make([]*userapi.BroadcastElem, 0, 2)
	// 直播间消息
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:     liveim.IMMessageTypeNormal,
		RoomID:   param.RoomID,
		Priority: userapi.BroadcastPriorityPurchased,
		Payload: &RoomMessage{
			Type:      liveim.TypeRedPacket,
			Event:     liveim.EventRedPacketSend,
			RoomID:    param.RoomID,
			User:      param.user,
			Bubble:    param.bubble,
			RedPacket: redPacketElem,
		},
	})

	// 全站飘屏（发送的红包售价达到全站飘屏需要的红包最小售价时，发送全站飘屏）
	if param.liveGoods.Price < redpacket.MinPriceBroadcast {
		return
	}
	// 主播用户信息
	creator, ok := userMap[param.room.CreatorID]
	if !ok {
		logger.WithField("user_id", param.room.CreatorID).Error("主播的用户信息不存在")
		return
	}

	b, err := bubble.FindSimple(bubbleIDRedPacket)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	formatMap := map[string]string{
		"sender_username":  html.EscapeString(sender.Username),
		"creator_username": html.EscapeString(creator.Username),
	}
	if b != nil {
		b.AppendFormatParams(formatMap)
	}
	message := `<font color="${highlight_color}">${sender_username}</font>` +
		`<font color="${normal_color}"> 在 </font>` +
		`<font color="${highlight_color}">${creator_username}</font>` +
		`<font color="${normal_color}"> 的直播间发了一个超大红包，快来拼手速吧！</font>`
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:     liveim.IMMessageTypeAll,
		RoomID:   param.RoomID,
		Priority: userapi.BroadcastPriorityPurchased,
		Payload: &NotifyPayload{
			Type:         liveim.TypeNotify,
			NotifyType:   liveim.TypeRedPacket,
			Event:        liveim.EventRedPacketSend,
			NotifyQueue:  liveim.NotifyQueuePriority,
			RoomID:       param.RoomID,
			User:         param.user,
			RedPacket:    redPacketElem,
			Message:      goutil.FormatMessage(message, formatMap),
			NotifyBubble: b,
		},
	})
}
