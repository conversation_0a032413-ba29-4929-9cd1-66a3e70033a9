package redpacket

import (
	"context"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	rpdatabus "github.com/MiaoSiLa/live-service/models/mongodb/redpacket/databus"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/mysql/liveuserspecialredpacket"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type sendParam struct {
	RoomID    int64 `form:"room_id" json:"room_id"`     // 直播间 ID
	GoodsID   int64 `form:"goods_id" json:"goods_id"`   // 礼物红包商品 ID
	Countdown int64 `form:"countdown" json:"countdown"` // 倒计时时长，单位：毫秒

	c                  *handler.Context
	userID             int64
	user               *liveuser.Simple
	userVip            *vip.UserVip
	bubble             *bubble.Simple
	room               *room.Room
	liveGoods          *livegoods.LiveGoods
	liveGoodsMore      *livegoods.More
	giftTotalPrice     int64                  // 礼物总价值（单位：钻石）
	giftTotalNum       int64                  // 礼物总数
	giftNumMap         map[string]interface{} // 红包中配置的礼物数量 map, key: gift_id, value: num
	rpcResp            *userapi.BalanceResp
	redPacket          *redpacket.LiveRedPacket
	redPacketGifts     []*Gift
	broadcastElems     []*userapi.BroadcastElem
	skinURL            string // 红包皮肤包资源地址
	appearanceID       int64  // 红包皮肤外观 ID
	isFree             bool   // 是否是免费红包
	isSpecialRedPacket bool   // 是否是特殊红包
	specialRedPacketID int64  // 若为特殊红包时，特殊红包 ID
}

type sendResp struct {
	Message string              `json:"message"`
	Balance *userapi.BuyBalance `json:"balance,omitempty"`
}

// Elem 单个礼物红包信息
type Elem struct {
	RedPacketID    string               `json:"red_packet_id"`             // 礼物红包 ID
	Type           int                  `json:"type"`                      // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
	CornerIconURL  string               `json:"corner_icon_url,omitempty"` // 角标图
	Price          int                  `json:"price"`                     // 红包购买价格（单位：钻石）
	GiftTotalPrice int64                `json:"gift_total_price"`          // 礼物总价值（单位：钻石）
	SkinURL        string               `json:"skin_url"`                  // 红包皮肤压缩包资源地址
	Sender         *mowangskuser.Simple `json:"sender"`                    // 发红包用户的信息
	GiftTotalNum   int64                `json:"gift_total_num"`            // 礼物总数
	Gifts          []*Gift              `json:"gifts"`                     // 红包的礼物列表
	RemainDuration int64                `json:"remain_duration"`           // 倒计时，单位：毫秒，0 为当前可抢
}

// Gift 礼物红包的礼物
type Gift struct {
	GiftID       int64  `json:"gift_id"`                 // 礼物 ID
	Num          int64  `json:"num"`                     // 数量
	MostValuable bool   `json:"most_valuable,omitempty"` // 是否是最有价值礼物
	Name         string `json:"name"`                    // 礼物名称
	IconURL      string `json:"icon_url"`                // 礼物图标
}

// ActionRedPacketSend 发送红包接口
/**
 * @api {post} /api/v2/chatroom/redpacket/send 发送红包
 * @apiDescription 仅支持普通钻石购买
 *
 * @apiVersion 0.1.0
 * @apiName send
 * @apiGroup api/v2/chatroom/redpacket
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} goods_id 礼物红包商品 ID
 * @apiParam {Number} countdown 倒计时时长，单位：毫秒
 * @apiParam {String} [keyword] 口令，仅在发送口令红包时传递该参数（一期不需要该参数）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "message": "红包已发送~", // 提示文案
 *       "balance": { // 当发送红包花费了钻石时才返回
 *         "balance": 11479968,
 *         "live_noble_balance": 283748,
 *         "live_noble_balance_status": 1
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample WebSocket 房间内消息
 *   {
 *     "type": "red_packet",
 *     "event": "send",
 *     "room_id": 233,
 *     "user": { // 发红包的用户
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.maoercdn.com/test/test.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 9
 *         },
 *         {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         },
 *         {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }
 *       ]
 *     },
 *     "bubble": { // 用户的消息气泡，如果没有特殊气泡，这个字段不存在
 *       "type": "noble", // 气泡类型，当前是贵族气泡
 *       "noble_level": 2 // 使用对应等级的贵族气泡
 *     },
 *     "red_packet": {
 *       "red_packet_id": "abc", // 礼物红包 ID
 *       "type": 1, // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
 *       "corner_icon_url": "https://static-test.maoercdn.com/test/corner.png", // 角标图
 *       "price": 999, // 红包购买价格（单位：钻石）
 *       "gift_total_price": 1999, // 礼物总价值（单位：钻石）
 *       "skin_url": "https://static-test.maoercdn.com/test/skin.zip", // 红包皮肤资源压缩包地址
 *       "sender": { // 发红包用户的信息
 *         "user_id": 1, // 发送者用户 ID
 *         "username": "发送者用户名",
 *         "iconurl": "https://static-test.maoercdn.com/test/avatar.png" // 发送者用户头像
 *       },
 *       "gift_total_num": 233, // 礼物总数
 *       "gifts": [
 *         {
 *           "gift_id": 1,
 *           "name": "礼物名称",
 *           "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *           "num": 233 // 礼物数量
 *         }
 *       ],
 *       "remain_duration": 60000, // 倒计时，单位：毫秒。0 为当前可抢
 *       "keyword": "谢谢老板" // 红包口令
 *     }
 *   }
 *
 * @apiSuccessExample WebSocket 红包过期房间内消息
 *   {
 *     "type": "red_packet",
 *     "event": "expire",
 *     "room_id": 223344,
 *     "red_packet": {
 *       "red_packet_id": "6392a915436527648871cef5"
 *     }
 *   }
 *
 * @apiSuccessExample {json} 全站飘屏
 *   {
 *     "type": "notify",
 *     "notify_type": "red_packet",
 *     "event": "send",
 *     "room_id": 233,
 *     "user": {
 *       ... // 同 websocket 消息中的 user
 *     },
 *     "red_packet": {
 *       ... // 同 websocket 消息中的 red_packet
 *     },
 *     "notify_bubble": { // 飘屏气泡
 *       "type": "custom",
 *       "image_url": "https://example.com/b128_0_10_0_100.png",
 *       "float": 1, // 如果 float 为 1, 则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *       "shine": 0 // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *     },
 *     "message": "<b>加特林</b>在<b>哒咩</b> 的直播间发了一个超大礼物红包，快来拼手速吧！"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRedPacketSend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSendParam(c)
	if err != nil {
		return nil, err
	}

	err = param.sendRedPacket()
	if err != nil {
		return nil, err
	}

	if !param.isFree {
		goutil.Go(func() {
			// 增加直播间经验值及上神开通进度
			param.addUserContribution()
			param.addHighnessSpend()
		})
	}

	if param.Countdown > 0 {
		// 待抢红包转化为可抢红包 - 生产者
		rpdatabus.DelayRedPacketSetGrabbing(param.redPacket)
	}

	// 红包过期 - 生产者
	rpdatabus.DelayRedPacketExpired(param.redPacket)

	// 直播间消息和全站飘屏
	param.broadcast()

	resp := &sendResp{
		Message: "红包已发送~",
	}
	if !param.isFree {
		balance := userapi.NewBuyBalance(param.rpcResp, param.userVip != nil)
		resp.Balance = &balance
	}
	return resp, nil
}

func newSendParam(c *handler.Context) (*sendParam, error) {
	var param sendParam
	err := c.Bind(&param)
	if err != nil || param.GoodsID <= 0 || param.RoomID <= 0 || param.Countdown < 0 {
		return nil, actionerrors.ErrParams
	}
	redPacketParam, err := params.FindRedPacket()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if redPacketParam.IsRoomDisabled(param.RoomID) {
		return nil, actionerrors.NewErrForbidden("暂时无法操作")
	}

	// 获取房间信息
	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.room.Status.Open != room.StatusOpenTrue {
		// 直播间未开播时不能发红包
		return nil, actionerrors.ErrClosedRoomAlt
	}
	if param.room.Limit != nil {
		// 礼物房不支持发红包
		return nil, actionerrors.NewErrForbidden("本直播间内无法发红包")
	}

	param.userID = c.UserID()

	param.liveGoods, err = livegoods.FindShowingGoods(param.GoodsID, livegoods.GoodsTypeRedPacket)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.liveGoods == nil || !(param.liveGoods.IsValidSaleTime() && param.liveGoods.IsValidShowTime()) {
		return nil, actionerrors.ErrGoodsNotFound
	}
	param.liveGoodsMore, err = param.liveGoods.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !param.liveGoodsMore.IsValidRedPacket() {
		logger.WithField("goods_id", param.liveGoods.ID).Error("礼物红包商品配置错误")
		return nil, actionerrors.ErrGoodsNotFound
	}
	param.isFree = param.liveGoods.IsFree()
	param.isSpecialRedPacket = param.liveGoodsMore.RedPacket.IsSpecialRedPacket()
	if param.isFree && !param.isSpecialRedPacket {
		logger.WithField("goods_id", param.GoodsID).Error("礼物红包价格配置错误")
		return nil, actionerrors.ErrGoodsNotFound
	}

	// 判断礼物红包是否可用
	var userOwnedSP bool
	if param.isSpecialRedPacket {
		redPacket, err := liveuserspecialredpacket.FindUserValidSpecialRedPacket(param.userID, param.GoodsID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if redPacket != nil {
			userOwnedSP = true
			param.specialRedPacketID = redPacket.ID
		} else {
			// 系统不支持发放免费特殊红包，此时会在 param.liveGoodsMore.RedPacket.CanUse() 中统一判断，此处不用单独处理
		}
	}
	if !param.liveGoodsMore.RedPacket.CanUse(param.userID, param.RoomID, param.room.CreatorID, userOwnedSP) {
		return nil, actionerrors.NewErrForbidden("该红包不可用")
	}

	giftIDs := make([]int64, 0, len(param.liveGoodsMore.RedPacket.Gifts))
	for _, g := range param.liveGoodsMore.RedPacket.Gifts {
		giftIDs = append(giftIDs, g.ID)
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(giftMap) != len(param.liveGoodsMore.RedPacket.Gifts) {
		// 配置的礼物一般不会被删除，若配置的礼物不存在，需要记录日志
		logger.WithFields(logger.Fields{"goods_id": param.liveGoods.ID}).Error("礼物红包商品配置的礼物不存在")
		return nil, actionerrors.ErrCannotFindResource
	}
	param.redPacketGifts = make([]*Gift, 0, len(param.liveGoodsMore.RedPacket.Gifts))
	param.giftNumMap = make(map[string]interface{}, len(param.liveGoodsMore.RedPacket.Gifts))
	for _, goodsRedPacketGift := range param.liveGoodsMore.RedPacket.Gifts {
		gi, ok := giftMap[goodsRedPacketGift.ID]
		if !ok {
			// 配置的礼物一般不会被删除，若配置的礼物不存在，需要记录日志
			logger.WithFields(logger.Fields{"goods_id": param.GoodsID, "gift_id": goodsRedPacketGift.ID}).
				Error("礼物红包商品配置的礼物不存在")
			return nil, actionerrors.ErrCannotFindResource
		}

		// 礼物红包礼物数量
		param.giftNumMap[strconv.FormatInt(goodsRedPacketGift.ID, 10)] = goodsRedPacketGift.Num

		// 计算红包礼物的总价和总数
		param.giftTotalPrice += gi.Price * goodsRedPacketGift.Num
		param.giftTotalNum += goodsRedPacketGift.Num

		param.redPacketGifts = AppendGifts(param.redPacketGifts, goodsRedPacketGift, gi)
	}

	// 获取用户正在使用的红包封面皮肤资源地址
	userAppearanceItem, err := userappearance.FindWornAppearance(param.userID, appearance.TypeRedPacket)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if userAppearanceItem != nil {
		// 优先使用用户外观中心的红包封面皮肤资源地址
		param.skinURL = storage.ParseSchemeURL(userAppearanceItem.Resource)
		param.appearanceID = userAppearanceItem.AppearanceID
	} else {
		// 若用户外观中心没有红包封面皮肤资源地址，则使用红包商品配置的默认封面皮肤资源地址
		appearanceItem, err := appearance.FindOne(param.liveGoodsMore.RedPacket.AppearanceID, appearance.TypeRedPacket)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if appearanceItem == nil {
			logger.WithFields(logger.Fields{
				"goods_id":      param.liveGoods.ID,
				"appearance_id": param.liveGoodsMore.RedPacket.AppearanceID,
			}).Error("礼物红包商品红包皮肤外观 ID 对应的外观不存在")
			return nil, actionerrors.ErrCannotFindResource
		}
		param.skinURL = storage.ParseSchemeURL(appearanceItem.Resource)
		param.appearanceID = param.liveGoodsMore.RedPacket.AppearanceID
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, &liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	_, userVip, err := userstatus.UserGeneral(c.UserID(), c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if userVip != nil && userVip.IsActive() {
		param.userVip = userVip
	}
	param.bubble, err = userappearance.FindMessageBubble(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.c = c
	return &param, nil
}

// AppendGifts 组合礼物红包的礼物
func AppendGifts(gifts []*Gift, redPacketGift livegoods.RedPacketGift, g *gift.Gift) []*Gift {
	return append(gifts, &Gift{
		GiftID:       redPacketGift.ID,
		Num:          redPacketGift.Num,
		MostValuable: redPacketGift.MostValuable,
		Name:         g.Name,
		IconURL:      g.Icon,
	})
}

// sendRedPacket 发红包
func (param *sendParam) sendRedPacket() error {
	// 加锁防止多个用户在短期内购买造成限购失效（直播间【可抢红包】+【待抢红包】数量之和超过上限时，该直播间不可新增红包）
	lockKey := keys.LockRoomBuyRedPacketGoods1.Format(param.RoomID)
	ok, err := service.Redis.SetNX(lockKey, param.userID, 10*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("操作太快啦，请稍后再试~")
	}
	// 释放锁
	defer func() {
		err = service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}()

	// 查询直播间【可抢红包】+【待抢红包】数量之和
	count, err := redpacket.CountGrabbingAndWaiting(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 直播间【可抢红包】+【待抢红包】数量之和超过上限时，该直播间不可新增红包
	if count >= redpacket.MaxCountGrabbingAndWaiting {
		return actionerrors.NewErrForbidden("本直播间红包数量已达上限，稍后再来试试吧~")
	}
	if param.isSpecialRedPacket {
		// 特殊红包：扣减特殊红包数量
		err := param.deductSpecialRedPacket()
		if err != nil {
			return err
		}
	}
	if !param.isFree {
		// 付费红包：创建订单及礼物红包记录并扣费
		err = param.buyRedPacket()
		if err != nil {
			return err
		}
	}
	// 添加发送记录
	expireDuration, err := time.ParseDuration(config.Conf.Params.RedPacket.ExpireDuration)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	err = param.addRedPacket(expireDuration, 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// buyRedPacket 购买红包
func (param *sendParam) buyRedPacket() error {
	// 先创建订单，rpc 购买成功后再更新订单
	more := new(livetxnorder.MoreInfo)
	if param.room.IsOpen() {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusOpen)
	} else {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusClosed)
	}
	order := livetxnorder.NewOrder(param.liveGoods, param.userID, 0, more)
	db := livetxnorder.LiveTxnOrder{}.DB()
	err := db.Create(order).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":   param.userID,
			"goods_id":  param.GoodsID,
			"countdown": param.Countdown,
		}).Errorf("创建礼物红包订单失败：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// rpc 购买礼物红包
	buyGoodsParam := userapi.BuyLiveGoodsParam{
		BuyerID:    param.userID,
		ReceiverID: param.room.CreatorID,
		GoodsType:  userapi.GoodsTypeRedPacket,
		Goods: []userapi.LiveGoodsElem{
			{ID: param.liveGoods.ID, Title: param.liveGoods.Title, Price: param.liveGoods.Price, Num: 1},
		},
		Noble:     0, // 礼物红包不支持贵族钻石购买
		UserAgent: param.c.UserAgent(),
		EquipID:   param.c.EquipID(),
		BUVID:     param.c.BUVID(),
		IP:        param.c.ClientIP(),
	}
	param.rpcResp, err = userapi.BuyLiveGoods(param.c.UserContext(), buyGoodsParam, param.room.Status.OpenLogID)
	if err != nil {
		// 礼物红包不支持贵族钻石购买，此处余额不足的文案单独处理
		if v, ok := err.(*mrpc.ClientError); ok && v.Code == actionerrors.CodeBalanceNotEnough {
			return actionerrors.ErrSendRedPacketBalanceNotEnough
		}
		return err
	}

	// 更新订单
	now := goutil.TimeNow()
	err = livetxnorder.LiveTxnOrder{}.DB().
		Where("id = ?", order.ID).
		Updates(map[string]interface{}{
			"status":        livetxnorder.StatusSuccess,
			"tid":           param.rpcResp.TransactionID,
			"modified_time": now.Unix(),
		}).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":        param.userID,
			"goods_id":       param.GoodsID,
			"countdown":      param.Countdown,
			"order_id":       order.ID,
			"transaction_id": param.rpcResp.TransactionID,
		}).Errorf("更新礼物红包订单失败：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// deductSpecialRedPacket 扣减特殊红包
func (param *sendParam) deductSpecialRedPacket() error {
	ok, err := liveuserspecialredpacket.DeductNum(param.specialRedPacketID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		// 查询锁定的红包数量不足，但可能存在过期时间较长的红包还有余量的情况
		return actionerrors.NewErrForbidden("您当前可用红包数量不足，请刷新后重试")
	}
	return nil
}

// addRedPacket 添加发红包记录
func (param *sendParam) addRedPacket(expireDuration time.Duration, retry int) error {
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		// 创建发红包记录
		now := goutil.TimeNow()
		param.redPacket = &redpacket.LiveRedPacket{
			CreateTime:    now.Unix(),
			ModifiedTime:  now.Unix(),
			GoodsID:       param.liveGoods.GoodsID(),
			UserID:        param.userID,
			RoomID:        param.RoomID,
			CreatorID:     param.room.CreatorID,
			AppearanceID:  param.appearanceID,
			WaitDuration:  param.Countdown,
			StartGrabTime: now.Unix() + param.Countdown/1000,
			RemainGiftNum: param.giftTotalNum,
			Status:        redpacket.StatusWaiting,
		}
		if param.isSpecialRedPacket {
			param.redPacket.SpecialRedPacketID = param.specialRedPacketID
		}
		if !param.isFree {
			// 付费红包写入订单信息
			param.redPacket.TransactionID = param.rpcResp.TransactionID
			param.redPacket.Context = param.rpcResp.Context
		}
		if param.Countdown == 0 {
			// 倒计时时长为 0，表示红包立即可抢
			param.redPacket.Status = redpacket.StatusGrabbing
		}
		result, err := redpacket.LiveRedPacketCollection().InsertOne(ctx, param.redPacket)
		if err != nil {
			return err
		}
		param.redPacket.OID = result.InsertedID.(primitive.ObjectID)

		// 直播间红包数量加 1
		_, err = room.UpdateRoomRedPacketNum(ctx, param.RoomID, 1)
		if err != nil {
			return err
		}

		// 记录红包剩余礼物数量到 redis hash
		hKey := keys.KeyRoomRedPacketGiftNum1.Format(param.redPacket.OID.Hex())
		pipe := service.Redis.TxPipeline()
		pipe.HMSet(hKey, param.giftNumMap)
		// 过期时间为红包最大有效期时长 + 12h（红包过期后退还礼物需要用到该 key，故过期时间加 12h）
		serviceredis.Expire(pipe, hKey, expireDuration+12*time.Hour)
		_, err = pipe.Exec()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		if strings.Contains(err.Error(), "(WriteConflict)") {
			if retry < 2 {
				// 若重试次数小于 2 次时出现，随机延时 100 到 200ms 后重试
				randInt := rand.Intn(100)
				sleepDuration := time.Duration(randInt+100) * time.Millisecond
				<-time.After(sleepDuration)
				retry++
				err = param.addRedPacket(expireDuration, retry)
				// 重试成功，记录日志
				if err == nil {
					logger.WithFields(logger.Fields{
						"user_id":        param.userID,
						"transaction_id": param.rpcResp.TransactionID,
						"red_packet_id":  param.redPacket.OID.Hex(),
					}).Warnf("发红包失败（WriteConflict）重试成功，重试次数：%d", retry)
				}
				return err
			}
			// 重试后依然失败时，记录日志
			logger.WithFields(logger.Fields{
				"user_id":        param.userID,
				"transaction_id": param.rpcResp.TransactionID,
			}).Error(err)
			return err
		}
		return err
	}
	return nil
}

// addHighnessSpend 增加上神开通进度
func (param *sendParam) addHighnessSpend() {
	utils.SendHighnessSpend(param.userID, int64(param.liveGoods.Price))
}

// addUserContribution 增加用户直播间经验值
func (param *sendParam) addUserContribution() {
	// 按红包售价加经验值（1 钻石 = 10 经验）
	pointAdd := int64(param.liveGoods.Price) * 10
	if param.userVip != nil && param.userVip.Info != nil {
		pointAdd = param.userVip.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.userID, param.RoomID, param.room.CreatorUsername, userstatus.FromNormal, param.userVip)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}
