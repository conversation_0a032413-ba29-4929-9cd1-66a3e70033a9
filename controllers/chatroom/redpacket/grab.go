package redpacket

import (
	"context"
	"errors"
	"math/rand"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liveredpacketgrabblockuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type grabParam struct {
	RoomID      int64  `form:"room_id" json:"room_id"`
	RedPacketID string `form:"red_packet_id" json:"red_packet_id"`

	creatorID     int64
	liveRedPacket *redpacket.LiveRedPacket
	sender        *sender
	liveGoods     *livegoods.LiveGoods
	redPacket     *livegoods.RedPacket
	user          *user.User
	liveUser      *liveuser.Simple
	bubble        *bubble.Simple
	uc            mrpc.UserContext
	equip         *goutil.Equipment
	clientIP      string
}

type grabResp struct {
	Status       int            `json:"status"`
	ShowUserList int            `json:"show_user_list"`
	Message      string         `json:"message,omitempty"`
	RedPacket    *redPacketInfo `json:"red_packet,omitempty"`
}

type redPacketInfo struct {
	RedPacketID string   `json:"red_packet_id"`
	Type        int      `json:"type"`
	Sender      sender   `json:"sender"`
	Gift        giftInfo `json:"gift"`
}

type sender struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
}

type giftInfo struct {
	GiftID       int64  `json:"gift_id"`
	Name         string `json:"name"`
	IconURL      string `json:"icon_url"`
	MostValuable int    `json:"most_valuable,omitempty"`

	price int64
}

type grabNotify struct {
	Type      string           `json:"type"`
	Event     string           `json:"event"`
	RoomID    int64            `json:"room_id"`
	User      *liveuser.Simple `json:"user"`
	Bubble    *bubble.Simple   `json:"bubble,omitempty"`
	RedPacket *redPacketInfo   `json:"red_packet"`
}

const (
	statusGrabFail    = iota // 抢红包失败（未抢到）状态值
	statusGrabSuccess        // 抢红包成功状态值
)

// broadcastMinGiftPrice 抢到礼物时发送广播消息需要的最低礼物价值（单位：钻石）
const broadcastMinGiftPrice int64 = 100

// userListShow 展示“查看大家的手气”按钮
const userListShow = 1

// 抢红包失败提示语
const (
	msgGrabNothing       = "什么都没抢到啊<br />不要灰心，下次手气会更好~"
	msgCreatorCanNotGrab = "主播不能抢自己直播内的红包哦！"
)

// 抢红包限制
const (
	// grabRedPacketUserLimit 一个账号每个自然日最多抢到 15 个红包
	grabRedPacketUserLimit = 15
	// grabRedPacketIPLimit 一个 IP 每个自然日最多抢到 30 个红包
	grabRedPacketIPLimit = 30
)

var (
	randSource = goutil.NewLockedSource(goutil.TimeNow().Unix())
)

var errConcurrentUpdateFail = errors.New("并发更新信息失败")

// ActionRedPacketGrab 抢红包接口
/**
 * @api {post} /api/v2/chatroom/redpacket/grab 抢红包
 *
 * @apiVersion 0.1.0
 * @apiName grab
 * @apiGroup /api/v2/chatroom/redpacket
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {String} red_packet_id 红包 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 0, // 抢红包状态，0：未抢中；1：抢中
 *       "show_user_list": 1, // 是否展示“查看大家的手气”按钮。0：不展示；1: 展示；
 *       "message": "什么都没抢到啊<br />不要灰心，下次运气会更好~", // 未抢中时提示语，仅在未抢中时下发（需支持 HTML 标签）
 *       "red_packet": {
 *         "red_packet_id": "abc", // 礼物红包 ID
 *         "type": 1, // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
 *         "sender": { // 发红包用户的信息
 *           "user_id": 1, // 发送者用户 ID
 *           "username": "发送者用户名",
 *           "iconurl": "https://static-test.maoercdn.com/test/avatar.png" // 发送者用户头像
 *         },
 *         "gift": { // 礼物信息
 *           "gift_id": 1,
 *           "name": "礼物名称",
 *           "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *           "most_valuable": 1 // 当为最高价值的礼物时下发该字段
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 用户抢到礼物时的房间内消息
 *   {
 *     "type": "red_packet",
 *     "event": "grab",
 *     "room_id": 233,
 *     "user": { // 抢到红包的用户
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.maoercdn.com/test/test.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 9
 *         },
 *         {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         },
 *         {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }
 *       ]
 *     },
 *     "bubble": { // 用户的消息气泡，如果没有特殊气泡，这个字段不存在
 *       "type": "noble", // 气泡类型，当前是贵族气泡
 *       "noble_level": 2 // 使用对应等级的贵族气泡
 *     },
 *     "red_packet": {
 *       "red_packet_id": "abc", // 礼物红包 ID
 *       "type": 1, // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
 *       "sender": { // 发红包用户的信息
 *         "user_id": 1, // 发送者用户 ID
 *         "username": "发送者用户名",
 *         "iconurl": "https://static-test.maoercdn.com/test/avatar.png" // 发送者用户头像
 *       },
 *       "gift": { // 礼物信息
 *         "gift_id": 1,
 *         "name": "礼物名称",
 *         "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *         "most_valuable": 1 // 当为最高价值的礼物时下发该字段
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample WebSocket 红包抢完房间内消息
 *   {
 *     "type": "red_packet",
 *     "event": "empty",
 *     "room_id": 223344,
 *     "red_packet": {
 *       "red_packet_id": "6392a915436527648871cef5"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRedPacketGrab(c *handler.Context) (handler.ActionResponse, error) {
	if !c.Equip().FromApp {
		// 抢红包接口仅可通过客户端访问
		return nil, handler.ErrBadRequest
	}

	var param grabParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}
	if param.user.ID == param.creatorID {
		// 直播间主播不能抢自己直播间的红包
		return param.grabFail(msgCreatorCanNotGrab, true, false)
	}

	// 对抢过红包的用户做处理
	redPacketLog, err := redpacket.FindUserLog(param.liveRedPacket.OID, param.user.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if redPacketLog != nil {
		// 若已抢过红包，返回之前抢到的结果
		if redPacketLog.GiftID == 0 {
			// 未抢到礼物时，返回什么都没抢到的结果
			return param.grabFail(msgGrabNothing, true, false)
		}
		giftData, err := gift.FindByGiftID(redPacketLog.GiftID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		isMostValuable := param.redPacket.IsMostValuableGift(redPacketLog.GiftID)
		redPacketInfo := param.newRedPacket(giftData, isMostValuable)
		return param.grabSuccess(redPacketInfo)
	}

	// 检查抢红包用户是否有抢的资格
	pass, creatorBlockUser, err := param.checkUser()
	if err != nil {
		return nil, err
	}
	if !creatorBlockUser {
		// 主播未拉黑抢红包用户时，该用户需要自动关注主播
		if _, limited := param.followCreator(); limited {
			// 关注操作触发关注限制或已拉黑主播，提示什么都没抢到，可查看已抢到红包的用户
			return param.grabFail(msgGrabNothing, true, true)
		}
	}
	if !pass {
		// 用户抢红包受限时，提示什么都没抢到，不可查看已抢到红包的用户
		return param.grabFail(msgGrabNothing, false, true)
	}

	// 检查用户抢红包是否受限
	if param.grabRedPacketLimit() {
		// 用户抢红包受限时，提示什么都没抢到，可查看已抢到红包的用户
		return param.grabFail(msgGrabNothing, true, true)
	}

	// 检查红包是否可抢
	isGrabNothing, err := param.checkRedPacket()
	if err != nil {
		return nil, err
	}
	if isGrabNothing {
		return param.grabFail(msgGrabNothing, true, true)
	}

	// 抽红包中的礼物
	giftID, err := param.drawGift()
	if err != nil {
		return nil, err
	}
	if giftID == 0 {
		return param.grabFail(msgGrabNothing, true, true)
	}
	giftData, err := gift.FindByGiftID(giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if giftData == nil || !useritems.IsBackpackGift(giftData) {
		logger.WithFields(logger.Fields{
			"red_packet_id": param.RedPacketID,
			"gift_id":       giftID,
		}).Error("抢到的红包礼物不存在或非背包礼物")
		return param.grabFail(msgGrabNothing, true, true)
	}
	// 判断是否为最有价值礼物
	isMostValuable := param.redPacket.IsMostValuableGift(giftID)
	isGrabNothing, err = param.addGift(giftData, isMostValuable, 0)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if isGrabNothing {
		return param.grabFail(msgGrabNothing, true, true)
	}
	redPacketInfo := param.newRedPacket(giftData, isMostValuable)
	// 发送广播消息
	param.broadcast(redPacketInfo)
	return param.grabSuccess(redPacketInfo)
}

// addGift 发放抢到的礼物，第一个返回值返回是否响应什么都没抢到
func (param *grabParam) addGift(gift *gift.Gift, isMostValuable bool, retry int) (bool, error) {
	// 开启事务对数据进行更新
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		// 更新红包信息
		ok, err := param.liveRedPacket.AfterGrabUpdate(ctx)
		if err != nil {
			return err
		}
		if !ok {
			// 若更新失败，一般为并发导致，返回相关错误
			return errConcurrentUpdateFail
		}
		// 创建抢红包记录
		log, err := redpacket.CreateLog(ctx, param.liveRedPacket.OID, param.user.ID, gift.GiftID, isMostValuable, param.clientIP)
		if err != nil {
			return err
		}
		if log == nil {
			// 若创建失败，一般为并发导致，返回相关错误
			return errConcurrentUpdateFail
		}
		if param.liveRedPacket.RemainGiftNum == 0 {
			// 若更新后礼物数量为 0，则需要将房间内红包数量 -1
			_, err = room.UpdateRoomRedPacketNum(ctx, param.RoomID, -1)
			if err != nil {
				return err
			}
		}
		// 更新红包内相关礼物的剩余数量
		ok, err = param.liveRedPacket.DecGiftNumForCache(gift.GiftID)
		if err != nil {
			return err
		}
		if !ok {
			// 若更新失败，一般为并发导致，返回相关错误
			return errConcurrentUpdateFail
		}
		// 将礼物加入用户背包
		var (
			now       = goutil.TimeNow()
			startTime = now.Unix()
			endTime   = now.Add(24 * time.Hour).Unix()
		)
		// WORKAROUND: 2023-01-21 00:00 起，用户新抢到的红包礼物有效期变为 6h，这之前抢到的红包礼物有效期仍为 24h
		if now.Unix() >= 1674230400 {
			endTime = now.Add(6 * time.Hour).Unix()
		}
		// FIXME: 礼物加入背包方法需要传入 ctx 方便事务回滚
		err = useritems.NewTransactionAdder(param.user.ID, param.liveRedPacket.TransactionID, param.liveRedPacket.Context, 1).
			Append(gift, 1, startTime, endTime).
			Add()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		if err == errConcurrentUpdateFail {
			// 由于并发导致更新失败时视作未抢到抢红包（并发抢最后一个红包的礼物时可能出现此情况）
			return true, nil
		}
		if strings.Contains(err.Error(), "(WriteConflict)") {
			// FIXME: 大量用户同时抢某红包时可能出现该错误，需要修复
			if retry < 2 {
				// 若重试次数小于 2 次时出现，随机延时 100 到 200ms 后重试
				randInt := rand.Intn(100)
				sleepDuration := time.Duration(randInt+100) * time.Millisecond
				<-time.After(sleepDuration)
				retry += 1
				isGrabNothing, err := param.addGift(gift, isMostValuable, retry)
				// 重试成功，记录日志
				if err == nil {
					logger.WithFields(logger.Fields{
						"red_packet_id": param.RedPacketID,
						"user_id":       param.user.ID,
					}).Warnf("抢红包失败（WriteConflict）重试成功，重试次数：%d", retry)
				}
				return isGrabNothing, err
			}
			// 重试后依然出现时，记录日志，返回未抢到的结果
			logger.WithFields(logger.Fields{
				"red_packet_id": param.RedPacketID,
				"user_id":       param.user.ID,
			}).Warn(err)
			return true, nil
		}
		return false, err
	}
	return false, nil
}

func (param *grabParam) newRedPacket(gift *gift.Gift, isMostValuable bool) *redPacketInfo {
	return &redPacketInfo{
		RedPacketID: param.liveRedPacket.OID.Hex(),
		Type:        param.redPacket.Type,
		Sender:      *param.sender,
		Gift: giftInfo{
			GiftID:       gift.GiftID,
			Name:         gift.Name,
			IconURL:      gift.Icon,
			MostValuable: goutil.BoolToInt(isMostValuable),
			price:        gift.Price,
		},
	}
}

// grabFail 抢红包失败处理，返回失败响应
func (param *grabParam) grabFail(message string, showUserList, createLog bool) (handler.ActionResponse, error) {
	if createLog {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		// 抢红包失败也需要记录
		_, err := redpacket.CreateLog(ctx, param.liveRedPacket.OID, param.user.ID, 0, false, param.clientIP)
		if err != nil {
			logger.WithFields(logger.Fields{"red_packet_id": param.RedPacketID}).Errorf("新增抢红包记录失败，%v", err)
			// PASS
		}
	}
	return grabResp{
		Status:       statusGrabFail,
		ShowUserList: goutil.BoolToInt(showUserList),
		Message:      message,
	}, nil
}

// grabFail 抢红包成处理，返回成功响应
func (param *grabParam) grabSuccess(redPacket *redPacketInfo) (handler.ActionResponse, error) {
	return grabResp{
		Status:       statusGrabSuccess,
		ShowUserList: userListShow,
		RedPacket:    redPacket,
	}, nil
}

func (param *grabParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.RedPacketID == "" {
		return actionerrors.ErrParams
	}
	redPacketOID, err := primitive.ObjectIDFromHex(param.RedPacketID)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.user = c.User()
	// 单个用户在 2s 内仅可抢一次红包（不区分红包所属直播间，不区分是否抢到）
	key := keys.LockRedPacketUserGrab1.Format(param.user.ID)
	ok, err := service.Redis.SetNX(key, 1, 2*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		// 抢红包频率过快需要报错
		return actionerrors.ErrRedPacketGrabTooQuick
	}
	param.liveRedPacket, err = redpacket.FindRedPacketByOID(redPacketOID, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.liveRedPacket == nil {
		return actionerrors.ErrRedPacketNotFound
	}

	room, err := room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if room == nil {
		// 直播间不存在时报错
		return actionerrors.ErrCannotFindRoom
	}
	param.creatorID = room.CreatorID
	// 获取红包对应的商品信息（只要红包可抢，即使商品下架也需要可以抢，此处不做相关判断）
	param.liveGoods, err = livegoods.Find(param.liveRedPacket.GoodsID, livegoods.GoodsTypeRedPacket)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	lf := logger.Fields{
		"red_packet_id": param.RedPacketID,
		"goods_id":      param.liveRedPacket.GoodsID,
	}
	if param.liveGoods == nil {
		logger.WithFields(lf).Error("礼物红包所属商品不存在")
		return actionerrors.ErrGoodsNotFound
	}
	goodsMore, err := param.liveGoods.UnmarshalMore()
	if err != nil {
		logger.WithFields(lf).Errorf("礼物红包所属商品配置错误：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !goodsMore.IsValidRedPacket() {
		logger.WithFields(lf).Errorf("礼物红包所属商品配置错误：%v", err)
		return actionerrors.ErrGoodsNotFound
	}
	param.redPacket = goodsMore.RedPacket
	senderInfo, err := liveuser.FindOneSimple(bson.M{"user_id": param.liveRedPacket.UserID}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.sender = &sender{
		UserID:   senderInfo.UserID(),
		Username: senderInfo.Username,
		IconURL:  senderInfo.IconURL,
	}
	// 查询用户消息气泡
	// TODO: 当前查询气泡过于复杂，之后需要封装更易获取的方法
	param.liveUser, err = liveuser.FindOneSimple(bson.M{"user_id": param.user.ID}, &liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.bubble, err = userappearance.FindMessageBubble(param.user.ID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.uc = c.UserContext()
	param.equip = c.Equip()
	param.clientIP = c.ClientIP()
	return nil
}

// checkUser 检查用户是否有抢红包的资格，第一个返回值返回是否通过检查，第二个返回值返回主播是否拉黑了用户，出错时返回 err（客户端使用 toast 提示该 err）
func (param *grabParam) checkUser() (bool, bool, error) {
	if param.user.Mobile == "" {
		// 用户未绑定手机需要报错
		return false, false, actionerrors.ErrRedPacketUnbindMobileUser
	}
	userID := param.user.ID
	creatorBlockUser, _, err := userapi.UserBlockStatus(param.creatorID, userID)
	if err != nil {
		return false, false, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.sender.UserID == userID {
		// 红包为抢红包用户所发时，不进行检查
		return true, creatorBlockUser, nil
	}
	if creatorBlockUser {
		// 被主播拉黑的用户不可抢红包
		return false, creatorBlockUser, nil
	}
	// 是否在全站黑名单
	inBlockList, err := blocklist.Exists(userID)
	if err != nil {
		return false, creatorBlockUser, actionerrors.NewErrServerInternal(err, nil)
	}
	if inBlockList {
		// 全站黑名单用户不可抢红包
		return false, creatorBlockUser, nil
	}
	// 是否被禁言
	mute, err := livemembers.IsMute(userID, param.RoomID)
	if err != nil {
		return false, creatorBlockUser, actionerrors.NewErrServerInternal(err, nil)
	}
	if mute.GlobalMute || mute.RoomMute {
		// 禁言用户不可抢红包
		return false, creatorBlockUser, nil
	}
	// 用户是否在抢红包用户黑名单中
	isBlocked, err := liveredpacketgrabblockuser.IsBlocked(userID)
	if err != nil {
		return false, creatorBlockUser, actionerrors.NewErrServerInternal(err, nil)
	}
	if isBlocked {
		// 抢红包黑名单用户不可抢红包
		return false, creatorBlockUser, nil
	}

	senderBlockUser, _, err := userapi.UserBlockStatus(param.sender.UserID, userID)
	if err != nil {
		return false, creatorBlockUser, actionerrors.NewErrServerInternal(err, nil)
	}
	if senderBlockUser {
		// 被发红包者拉黑的用户不可抢红包
		return false, creatorBlockUser, nil
	}
	// 是否被直播间封禁
	isBanned, err := userstatus.IsBanned(userID)
	if err != nil {
		return false, creatorBlockUser, actionerrors.NewErrServerInternal(err, nil)
	}
	if isBanned {
		// 被直播间封禁的用户不可抢红包
		return false, creatorBlockUser, nil
	}

	// 检查用户是否在当前直播间中
	isNoRiskConnect, err := imuserlogs.IsNoRiskConnect(param.user.ID, param.RoomID)
	if err != nil {
		return false, creatorBlockUser, err
	}
	if !isNoRiskConnect {
		return false, creatorBlockUser, nil
	}

	// 检测阿里云营销风险
	pass := param.checkCouponAbuse()
	return pass, creatorBlockUser, nil
}

type checkCouponAbuseParam struct {
	UserID int64  `json:"user_id"`
	IP     string `json:"ip"`
	Scene  string `json:"scene"`
	BUVID  string `json:"buvid"`
}

// checkCouponAbuse 检查用户是否有营销风险
func (param *grabParam) checkCouponAbuse() bool {
	highRiskScore := config.Conf.Params.RedPacket.HighRiskScore
	if highRiskScore == 0 {
		// 高风险分数阈值为 0 时，视作通过检测
		return true
	}
	res, err := userapi.ScanRisk(param.uc, userapi.ScanRiskParam{
		UserID: param.user.ID,
		IP:     param.clientIP,
		Scene:  userapi.RiskSceneCoupon,
		BUVID:  param.equip.BUVID,
	})
	if err != nil {
		logger.Error(err)
		// PASS: 检测营销风险出错时，忽略该错误，视作检测通过，避免影响抢红包
		return true
	}
	return res.Score < highRiskScore
}

// checkRedPacket 检查红包状态，第一个返回值返回是否响应什么都没抢到
func (param *grabParam) checkRedPacket() (bool, error) {
	// 判断红包是否可抢
	if param.liveRedPacket.Status == redpacket.StatusExpired {
		return false, actionerrors.ErrRedPacketCanNotGrab
	} else if param.liveRedPacket.Status == redpacket.StatusEmpty {
		expireDuration, err := time.ParseDuration(config.Conf.Params.RedPacket.ExpireDuration)
		if err != nil {
			return false, actionerrors.NewErrServerInternal(err, nil)
		}
		if time.Unix(param.liveRedPacket.StartGrabTime, 0).Add(expireDuration).After(goutil.TimeNow()) {
			// 若红包已抢空且在红包有效期内，返回抢空的响应
			return true, nil
		}
		return false, actionerrors.ErrRedPacketCanNotGrab
	} else if param.liveRedPacket.Status == redpacket.StatusWaiting {
		if param.liveRedPacket.ExpireTime == 0 &&
			time.Unix(param.liveRedPacket.StartGrabTime, 0).Before(goutil.TimeNow()) {
			// 若抢红包时为待抢状态并且已经过了开抢时间，需要调整红包状态为可抢
			// 此处逻辑用来给红包变为可抢状态的延迟消息脚本做兜底
			_, err := redpacket.SetRedPacketGrabbing(param.liveRedPacket.OID)
			if err != nil {
				logger.WithField("red_packet_id", param.RedPacketID).Errorf("礼物红包状态更新失败：%v", err)
				// PASS
			}
		} else {
			logger.WithFields(logger.Fields{
				"red_packet_id": param.RedPacketID,
				"user_id":       param.user.ID,
			}).Warn("提前抢了红包")
			return false, actionerrors.ErrRedPacketCanNotGrab
		}
	}
	return false, nil
}

// drawGift 抽红包礼物
func (param *grabParam) drawGift() (int64, error) {
	// 获取剩余礼物数量
	giftNumMap, err := param.liveRedPacket.FindGiftNumFromCache()
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	// 剩余礼物数可以看作抽到权重
	giftIDs := make([]int64, 0, len(giftNumMap))
	weights := make([]int, 0, len(giftNumMap))
	for giftID, num := range giftNumMap {
		if num > 0 {
			// 仅在礼物有剩余数量时加入抽奖池
			giftIDs = append(giftIDs, giftID)
			weights = append(weights, int(num))
		}
	}
	if len(giftIDs) == 0 {
		return 0, nil
	}
	d, err := goutil.NewDiscreteDistribution(weights, randSource, false)
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	giftID := giftIDs[d.NextInt()]
	return giftID, nil
}

// followCreator 关注主播，返回值第一个为是否关注成功，第二个为是否被限制关注或已拉黑主播
func (param *grabParam) followCreator() (bool, bool) {
	followed, err := attentionuser.HasFollowed(param.user.ID, param.creatorID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":    param.user.ID,
			"creator_id": param.creatorID,
		}).Errorf("抢红包时查询和主播的关注关系出错：%v", err)
		// PASS: 关注过程出错时，不报错避免影响抢红包
		return false, false
	}
	if !followed {
		// 未关注主播时，进行关注
		err = attentionuser.RPCFollow(param.user.ID, param.creatorID, nil)
		if err != nil {
			if attentionuser.IsRPCUserError(err) {
				return false, true
			}
			logger.WithFields(logger.Fields{
				"user_id":    param.user.ID,
				"creator_id": param.creatorID,
			}).Errorf("抢红包时自动关注主播出错：%v", err)
			// PASS: 关注过程出错时，不报错避免影响抢红包
			return false, false
		}
	}
	return true, false
}

func (param *grabParam) broadcast(redPacket *redPacketInfo) {
	broadcastBox := userapi.NewBroadcastBox(2)
	if redPacket.Gift.price >= broadcastMinGiftPrice || redPacket.Gift.MostValuable != 0 {
		// 当礼物价值大于等于指定价值或为最高价值礼物时，广播消息
		notify := grabNotify{
			Type:      liveim.TypeRedPacket,
			Event:     liveim.EventRedPacketGrab,
			RoomID:    param.RoomID,
			User:      param.liveUser,
			Bubble:    param.bubble,
			RedPacket: redPacket,
		}
		broadcastBox.Add(&userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  param.RoomID,
			Payload: notify,
		})
	}
	if param.liveRedPacket.RemainGiftNum == 0 {
		// 若抢完后礼物数量为 0，需要发送红包被抢空的消息
		emptyNotify := redpacket.NewEmptyNotify(param.liveRedPacket.OID, param.liveRedPacket.RoomID)
		broadcastBox.Add(&userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  param.RoomID,
			Payload: emptyNotify,
		})
	}
	if len(broadcastBox.List) > 0 {
		err := broadcastBox.Send()
		if err != nil {
			logger.WithFields(logger.Fields{"red_packet_id": param.RedPacketID, "room_id": param.RoomID}).Error(err)
			// PASS
		}
	}
}

// grabRedPacketLimit 抢红包限制
func (param *grabParam) grabRedPacketLimit() bool {
	// 用户抢自己发的红包时没有限制
	if param.user.ID == param.liveRedPacket.UserID {
		return false
	}

	count, err := redpacket.CountOneDayRedPacketLog(param.user.ID, "")
	if err != nil {
		logger.WithField("user_id", param.user.ID).Error(err)
		// PASS
	}
	if count >= grabRedPacketUserLimit {
		return true
	}

	count, err = redpacket.CountOneDayRedPacketLog(0, param.clientIP)
	if err != nil {
		logger.WithField("ip", param.clientIP).Error(err)
		// PASS
	}
	return count >= grabRedPacketIPLimit
}
