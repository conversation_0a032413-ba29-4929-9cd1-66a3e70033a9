package redpacket

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 最佳手气标识
	luckiestGrabUser = 1
	// 最高价值礼物标识
	mostValuableGift = 1
)

type grabUserListResp struct {
	Title         string            `json:"title"`                     // 红包标题
	GiftRemainNum int64             `json:"gift_remain_num,omitempty"` // 剩余礼物数量，全部剩余或零剩余客户端不需要展示，此时不返回该字段
	Data          []*logData        `json:"data"`
	Pagination    goutil.Pagination `json:"pagination"`
}

type logData struct {
	UserID   int64       `json:"user_id"`
	Username string      `json:"username"`
	Luckiest int         `json:"luckiest,omitempty"` // 手气最佳用户标识
	Gift     logGiftInfo `json:"gift"`
}

type logGiftInfo struct {
	GiftID       int64  `json:"gift_id"`
	Name         string `json:"name"`
	IconURL      string `json:"icon_url"`                // 礼物图标
	MostValuable int    `json:"most_valuable,omitempty"` // 最高价值礼物
}

type grabUserListParam struct {
	roomID      int64
	redPacketID string
	page        int64
	pageSize    int64

	giftTotalNum   int64 // 红包礼物总数
	redPacketOID   primitive.ObjectID
	gifts          map[int64]*gift.Gift
	redPacket      *redpacket.LiveRedPacket // 红包信息
	goodsRedPacket *livegoods.RedPacket     // 红包商品信息

	resp *grabUserListResp
}

func newGrabUserListParam(c *handler.Context) (*grabUserListParam, error) {
	var err error
	params := new(grabUserListParam)
	params.roomID, err = c.GetParamInt64("room_id")
	if err != nil || params.roomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 获取礼物红包 ID
	params.redPacketID, _ = c.GetParamString("red_packet_id")
	if params.redPacketID == "" {
		return nil, actionerrors.ErrParams
	}
	params.redPacketOID, err = primitive.ObjectIDFromHex(params.redPacketID)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	params.page, params.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	return params, nil
}

func (p *grabUserListParam) check() (err error) {
	// 查询红包信息
	p.redPacket, err = redpacket.FindRedPacketByOID(p.redPacketOID, p.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 检查红包是否存在或失效
	if p.redPacket == nil || !p.redPacket.IsValid() {
		return actionerrors.ErrRedPacketNotFound
	}

	// 查询红包所属商品信息
	goods, err := livegoods.FindShowingGoods(p.redPacket.GoodsID, livegoods.GoodsTypeRedPacket)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if goods == nil {
		logger.WithField("goods_id", p.redPacket.GoodsID).Error("礼物红包商品不存在")
		return actionerrors.ErrGoodsNotFound
	}
	more, err := goods.UnmarshalMore()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !more.IsValidRedPacket() {
		logger.WithField("goods_id", goods.ID).Error("礼物红包配置错误")
		return actionerrors.ErrGoodsNotFound
	}
	p.goodsRedPacket = more.RedPacket

	// 获取红包中礼物总数量
	giftIDs := make([]int64, 0, len(more.RedPacket.Gifts))
	for _, v := range more.RedPacket.Gifts {
		p.giftTotalNum += v.Num
		giftIDs = append(giftIDs, v.ID)
	}
	if p.giftTotalNum == 0 {
		logger.WithField("goods_id", goods.ID).Error("礼物红包内礼物数配置错误")
		return actionerrors.ErrGoodsNotFound
	}

	// 根据 ID 查询礼物详情
	p.gifts, err = gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *grabUserListParam) findGrabUserList() (err error) {
	// 当 page 为 1 时，查询最佳手气红包记录
	var luckiestLog *redpacket.LiveRedPacketLog
	if p.page == 1 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		luckiestLog, err = redpacket.FindLuckiestLog(ctx, p.redPacketOID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	// 查询抽到非最佳手气红包记录
	logs, pa, err := redpacket.ListNotLuckiestRedPacketLog(p.redPacketOID, p.page, p.pageSize)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.resp = &grabUserListResp{
		Title:      "大家的手气",
		Pagination: pa,
	}

	// 红包记录为空且最佳手气记录不存在时返回结果
	if len(logs) == 0 && luckiestLog == nil {
		// 若红包礼物全部剩余时，客户端不需要显示剩余礼物数，不返回剩余礼物数，否则返回
		if p.giftTotalNum != p.redPacket.RemainGiftNum {
			p.resp.GiftRemainNum = p.redPacket.RemainGiftNum
		}
		return nil
	}

	// 最佳手气记录不为空时置顶最佳手气记录
	if luckiestLog != nil {
		logs = append([]*redpacket.LiveRedPacketLog{luckiestLog}, logs...)
	}

	// 根据 ID 查询用户详情
	userIDs := make([]int64, 0, len(logs))
	for _, v := range logs {
		userIDs = append(userIDs, v.UserID)
	}
	users, err := user.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	p.resp.Data = make([]*logData, 0, len(logs))
	for _, l := range logs {
		logFields := logger.Fields{
			"red_packet_id": p.redPacketID,
			"user_id":       l.UserID,
			"gift_id":       l.GiftID,
		}
		u, ok := users[l.UserID]
		if !ok {
			logger.WithFields(logFields).Error("未查询到用户")
			continue
		}
		g, ok := p.gifts[l.GiftID]
		if !ok {
			logger.WithFields(logFields).Error("未查询到礼物")
			continue
		}
		data := &logData{
			UserID:   u.ID,
			Username: u.UserName,
			Gift: logGiftInfo{
				GiftID:  g.GiftID,
				Name:    g.Name,
				IconURL: g.Icon,
			},
		}
		if l.Luckiest {
			data.Luckiest = luckiestGrabUser
		}
		if p.goodsRedPacket.IsMostValuableGift(g.GiftID) {
			data.Gift.MostValuable = mostValuableGift
		}
		p.resp.Data = append(p.resp.Data, data)
	}

	// 运行至此处一定存在抢红包记录，不会出现全部剩余的情况。
	// 若红包剩余礼物数为零，客户端不需要显示剩余礼物数，不返回剩余礼物数，否则返回
	if p.redPacket.RemainGiftNum > 0 {
		p.resp.GiftRemainNum = p.redPacket.RemainGiftNum
	}
	return nil
}

// ActionRedPacketGrabUserList 抢到红包用户列表接口
/**
 * @api {get} /api/v2/chatroom/redpacket/grabuserlist{?room_id,red_packet_id,p,pagesize} 抢到红包用户列表
 * @apiDescription 最佳手气存在且 p=1 时最多返回 1+pagesize 条数据，多的一条记录为最佳手气记录
 *
 * @apiVersion 0.1.0
 * @apiName grabuserlist
 * @apiGroup /api/v2/chatroom/redpacket
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {String} red_packet_id 红包 ID
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "title": "大家的手气", // 红包标题
 *       "gift_remain_num": 28, // 剩余礼物数量，全部剩余或零剩余客户端不需要展示，此时不返回该字段
 *       "data": [
 *         {
 *           "user_id": 1,  // 用户 ID
 *           "username": "用户昵称",
 *           "luckiest": 1, // 手气最佳用户标识
 *           "gift": { // 礼物信息
 *             "gift_id": 1,
 *             "name": "礼物名称",
 *             "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *             "most_valuable": 1 // 当为最高价值的礼物时下发该字段
 *           }
 *         },
 *         {
 *           "user_id": 2,
 *           "username": "用户 B",
 *           "gift": { // 礼物信息
 *             "gift_id": 1,
 *             "name": "礼物名称",
 *             "icon_url": "https://static-test.maoercdn.com/test/test.png" // 礼物图标
 *           }
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRedPacketGrabUserList(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newGrabUserListParam(c)
	if err != nil {
		return nil, err
	}
	err = params.check()
	if err != nil {
		return nil, err
	}
	err = params.findGrabUserList()
	if err != nil {
		return nil, err
	}
	return params.resp, nil
}
