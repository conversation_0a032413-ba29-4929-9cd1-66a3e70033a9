package redpacket

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liveredpacketgrabblockuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testUserID        int64 = 12
	testGrabCreatorID int64 = 15614
	testGrabRoomID    int64 = 8697456
	testNoneRoomID    int64 = 999999999
	testGiftID        int64 = 23332
	testIP                  = "127.0.0.1"
	testRedPacketOID        = primitive.NewObjectID()
	testRedPacketOID2       = primitive.NewObjectID()

	testGoodsID        int64
	testMoreErrGoodsID int64
)

func createTestData() error {
	nowUnix := goutil.TimeNow().Unix()
	var goods livegoods.LiveGoods
	err := service.LiveDB.Where("title = ? AND type = ?", "礼物红包 grab", livegoods.GoodsTypeRedPacket).First(&goods).Error
	if err != nil {
		return err
	}
	testGoodsID = goods.GoodsID()
	var moreErrGoods livegoods.LiveGoods
	err = service.LiveDB.Where("title = ? AND type = ?", "礼物红包 error more", livegoods.GoodsTypeRedPacket).First(&moreErrGoods).Error
	if err != nil {
		return err
	}
	testMoreErrGoodsID = moreErrGoods.GoodsID()
	redPackets := []interface{}{
		&redpacket.LiveRedPacket{
			OID:           testRedPacketOID,
			GoodsID:       goods.ID,
			UserID:        testUserID,
			RoomID:        testNoneRoomID,
			CreatorID:     testGrabCreatorID,
			Status:        redpacket.StatusWaiting,
			WaitDuration:  60000,
			StartGrabTime: nowUnix + 60,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
		&redpacket.LiveRedPacket{
			OID:           testRedPacketOID2,
			GoodsID:       goods.ID,
			UserID:        testUserID,
			RoomID:        testGrabRoomID,
			CreatorID:     12,
			Status:        redpacket.StatusGrabbing,
			RemainGiftNum: 1,
			StartGrabTime: nowUnix,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 创建房间
	_, err = room.Collection().DeleteMany(ctx, bson.M{"room_id": testGrabRoomID})
	if err != nil {
		return err
	}
	_, err = room.Collection().InsertOne(ctx, room.Room{
		Helper: room.Helper{
			CreatorID: testGrabCreatorID,
			RoomID:    testGrabRoomID,
			Name:      "测试直播间 3",
			NameClean: "测试直播间 3",
			Status:    room.Status{Open: room.StatusOpenTrue},
		},
	})
	if err != nil {
		return err
	}

	// 创建红包
	_, err = redpacket.LiveRedPacketCollection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{testNoneRoomID, testGrabRoomID}}})
	if err != nil {
		return err
	}
	_, err = redpacket.LiveRedPacketCollection().InsertMany(ctx, redPackets)
	return err
}

func TestGrabTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(grabParam{}, "room_id", "red_packet_id")
	kc.Check(grabResp{}, "status", "show_user_list", "message", "red_packet")
	kc.Check(redPacketInfo{}, "red_packet_id", "type", "sender", "gift")
	kc.Check(sender{}, "user_id", "username", "iconurl")
	kc.Check(grabNotify{}, "type", "event", "room_id", "user", "bubble", "red_packet")
}

func TestActionRedPacketGrab(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试接口请求失败的情况
	err := createTestData()
	require.NoError(err)
	param := &grabParam{
		RoomID:      testGrabRoomID,
		RedPacketID: testRedPacketOID2.Hex(),
	}
	api := "/api/v2/chatroom/redpacket/grab"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = ActionRedPacketGrab(c)
	require.EqualError(err, "错误的请求")

	// 测试接口请求成功的情况
	cancelRPCMock := mrpc.SetMock("go://user/block-status", func(input interface{}) (output interface{}, err error) {
		return map[string][]bool{
			"block_status": []bool{true, false},
		}, nil
	})
	defer cancelRPCMock()
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.Equip().FromApp = true
	c.User().Mobile = "12345678"
	result, err := ActionRedPacketGrab(c)
	require.NoError(err)
	resp, ok := result.(grabResp)
	require.True(ok)
	assert.Equal(statusGrabFail, resp.Status)
}

func TestGrabParam_addGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := createTestData()
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = redpacket.LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{"gift_id": 233})
	require.NoError(err)
	key := keys.KeyRoomRedPacketGiftNum1.Format(testRedPacketOID2.Hex())
	defer func() {
		_ = service.Redis.Del(key)
	}()
	err = service.Redis.HSet(key, "233", 1).Err()
	require.NoError(err)

	param := &grabParam{
		RedPacketID: testRedPacketOID2.Hex(),
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveRedPacket: &redpacket.LiveRedPacket{
			OID:    testRedPacketOID2,
			Status: redpacket.StatusExpired,
		},
	}
	gift := &gift.Gift{
		GiftID: 233,
		Name:   "test",
		Icon:   "https://static-test.maoercdn.com/test.jpg",
		Price:  1,
		Type:   gift.TypeRebate,
	}
	isGrabNothing, err := param.addGift(gift, true, 0)
	require.NoError(err)
	assert.False(isGrabNothing)
	// 验证数据
	assert.Zero(param.liveRedPacket.RemainGiftNum)
	var ui useritems.UserItem
	err = useritems.Collection().FindOne(ctx, bson.M{"gift_id": 233}).Decode(&ui)
	require.NoError(err)

	// 测试重复发放礼物的情况
	isGrabNothing, err = param.addGift(gift, true, 0)
	require.NoError(err)
	assert.True(isGrabNothing)
}

func TestGrabParam_newRedPacket(t *testing.T) {
	assert := assert.New(t)

	param := &grabParam{
		sender: &sender{
			UserID: 32424,
		},
		liveRedPacket: &redpacket.LiveRedPacket{
			OID: primitive.NewObjectID(),
		},
		redPacket: &livegoods.RedPacket{
			Type: livegoods.RedPacketTypeNormal,
		},
	}
	giftInfo := &gift.Gift{
		GiftID: 233,
		Name:   "test",
		Icon:   "https://static-test.maoercdn.com/test.jpg",
		Price:  1,
	}
	rp := param.newRedPacket(giftInfo, false)
	assert.Equal(param.sender.UserID, rp.Sender.UserID)
	assert.Equal(param.liveRedPacket.OID.Hex(), rp.RedPacketID)
	assert.Equal(param.redPacket.Type, rp.Type)
	assert.Equal(giftInfo.GiftID, rp.Gift.GiftID)
	assert.Equal(giftInfo.Name, rp.Gift.Name)
	assert.Equal(giftInfo.Icon, rp.Gift.IconURL)
	assert.Equal(giftInfo.Price, rp.Gift.price)
}

func TestGrabParam_grabFail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := redpacket.LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"_red_packet_id": testRedPacketOID})
	require.NoError(err)

	testUserID := int64(42342342)
	param := &grabParam{
		RedPacketID: testRedPacketOID.Hex(),
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveRedPacket: &redpacket.LiveRedPacket{
			OID: testRedPacketOID,
		},
	}
	result, err := param.grabFail("test", false, true)
	require.NoError(err)
	resp, ok := result.(grabResp)
	require.True(ok)
	assert.Equal(statusGrabFail, resp.Status)
	assert.Equal("test", resp.Message)
	assert.Equal(0, resp.ShowUserList)
	// 验证创建了抢红包记录
	log, err := redpacket.FindUserLog(testRedPacketOID, testUserID)
	require.NoError(err)
	require.NotNil(log)
	assert.Equal(int64(0), log.GiftID)

	result, err = param.grabFail("test", true, false)
	require.NoError(err)
	resp, ok = result.(grabResp)
	require.True(ok)
	assert.Equal(statusGrabFail, resp.Status)
	assert.Equal("test", resp.Message)
	assert.Equal(userListShow, resp.ShowUserList)
}

func TestGrabParam_grabSuccess(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &grabParam{}
	rp := &redPacketInfo{
		RedPacketID: testRedPacketOID.Hex(),
		Type:        livegoods.RedPacketTypeNormal,
		Gift: giftInfo{
			GiftID:  233,
			Name:    "test",
			IconURL: "https://static-test.maoercdn.com/test.jpg",
		},
	}
	result, err := param.grabSuccess(rp)
	require.NoError(err)
	resp, ok := result.(grabResp)
	require.True(ok)
	assert.Equal(statusGrabSuccess, resp.Status)
	assert.Equal(rp, resp.RedPacket)
	assert.Equal(userListShow, resp.ShowUserList)
}

func TestGrabParam_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	param := &grabParam{
		RoomID:      233,
		RedPacketID: "",
	}
	api := "/api/v2/chatroom/redpacket/grab"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	err := param.load(c)
	assert.Equal(actionerrors.ErrParams, err)
	param = &grabParam{
		RoomID:      0,
		RedPacketID: testRedPacketOID.Hex(),
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试直播间不存在的情况
	err = createTestData()
	require.NoError(err)
	key := keys.LockRedPacketUserGrab1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	param.RoomID = testNoneRoomID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 测试无问题的情况
	require.NoError(service.Redis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = redpacket.LiveRedPacketCollection().UpdateOne(ctx, bson.M{
		"_id": testRedPacketOID,
	}, bson.M{"$set": bson.M{"room_id": testGrabRoomID}})
	require.NoError(err)
	param.RoomID = testGrabRoomID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	require.NoError(err)
	// 验证数据
	assert.Equal(testGrabCreatorID, param.creatorID)
	require.NotNil(param.liveRedPacket)
	assert.Equal(testRedPacketOID, param.liveRedPacket.OID)
	require.NotNil(param.liveGoods)
	assert.Equal(testGoodsID, param.liveGoods.ID)
	require.NotNil(param.redPacket)
	assert.Equal(livegoods.RedPacketTypeNormal, param.redPacket.Type)
	assert.NotEmpty(param.redPacket.Gifts)
	require.NotNil(param.sender)
	assert.Equal(testUserID, param.sender.UserID)
	assert.NotEqual("", param.sender.Username)
	assert.NotEqual("", param.sender.IconURL)
	require.NotNil(param.user)
	assert.Equal(testUserID, param.user.ID)

	// 测试商品不存在的情况
	require.NoError(service.Redis.Del(key).Err())
	_, err = redpacket.LiveRedPacketCollection().UpdateOne(ctx, bson.M{
		"_id": testRedPacketOID,
	}, bson.M{"$set": bson.M{"goods_id": 999999999}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrGoodsNotFound, err)

	// 测试商品配置错误的情况
	require.NoError(service.Redis.Del(key).Err())
	_, err = redpacket.LiveRedPacketCollection().UpdateOne(ctx, bson.M{
		"_id": testRedPacketOID,
	}, bson.M{"$set": bson.M{"goods_id": testMoreErrGoodsID}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	// 此时也提示商品不存在
	assert.Equal(actionerrors.ErrGoodsNotFound, err)

	// 测试抢红包频率过快的情况
	require.NoError(service.Redis.Set(key, 1, 2*time.Second).Err())
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrRedPacketGrabTooQuick, err)
}

func TestGrabParam_checkUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(564654)
	testRoomID := int64(45645)
	blockListKey := keys.KeyUsersBlockList0.Format()
	require.NoError(service.Redis.ZRem(blockListKey, testUserID).Err())
	cancelRPCMock := mrpc.SetMock("go://user/block-status", func(input interface{}) (output interface{}, err error) {
		return map[string][]bool{
			"block_status": []bool{true, false},
		}, nil
	})
	defer cancelRPCMock()
	err := service.LRURedis.Del(imuserlogs.KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
	require.NoError(err)

	// 测试未绑定手机号时抢红包的情况
	param := grabParam{
		RoomID: testRoomID,
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		sender: &sender{
			UserID: 2,
		},
		creatorID: 999,
		equip: &goutil.Equipment{
			BUVID: "test",
		},
		clientIP: "127.0.0.1",
	}
	_, _, err = param.checkUser()
	require.Equal(actionerrors.ErrRedPacketUnbindMobileUser, err)

	// 测试抢红包用户为发红包用户的情况
	param.sender.UserID = testUserID
	param.user.Mobile = "123456789"
	pass, creatorBlockUser, err := param.checkUser()
	require.NoError(err)
	assert.True(creatorBlockUser)
	assert.True(pass)

	// 测试在全站黑名单内的情况
	param.sender.UserID = 782347
	require.NoError(service.Redis.ZAdd(blockListKey, &redis.Z{Score: blocklist.StatusBlockUserForever, Member: testUserID}).Err())
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.True(creatorBlockUser)
	assert.False(pass)

	// 测试被禁言的情况
	require.NoError(service.Redis.ZRem(blockListKey, testUserID).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": testUserID, "room_id": testRoomID}
	now := goutil.TimeNow()
	_, err = livemembers.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"updated_time": now.Unix(),
			},
			"$setOnInsert": bson.M{
				"user_id": testUserID,
				"status":  livemembers.StatusMute,
				"room_id": testRoomID,
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.True(creatorBlockUser)
	assert.False(pass)

	// 测试用户在抢红包黑名单中的情况
	_, err = livemembers.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	r := &liveredpacketgrabblockuser.RedpacketGrabBlockUser{
		UserID: testUserID,
	}
	require.NoError(r.DB().Create(r).Error)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.True(creatorBlockUser)
	assert.False(pass)

	// 测试抢红包用户被发送红包用户或主播拉黑的情况
	require.NoError(r.DB().Delete("", "user_id = ?", testUserID).Error)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.True(creatorBlockUser)
	assert.False(pass)

	// 测试封禁用户抢红包的情况
	cancelRPCMock = mrpc.SetMock("go://user/block-status", func(input interface{}) (output interface{}, err error) {
		return map[string][]bool{
			"block_status": []bool{false, false},
		}, nil
	})
	defer cancelRPCMock()
	require.NoError(userstatus.BanUser(testUserID, now.Unix(), 0, userstatus.TypeBanForever))
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.False(pass)

	// 测试 check pass 的情况
	inserted, err := imuserlogs.Collection().InsertOne(ctx, imuserlogs.Log{
		CreateTime:   time.Now().Unix(),
		ModifiedTime: time.Now().Unix(),
		UserID:       testUserID,
		Status:       imuserlogs.StatusNormal,
		RoomID:       testRoomID,
		RenewTime:    time.Now().Unix(),
	})
	require.NoError(err)
	defer func() {
		_, _ = imuserlogs.Collection().DeleteOne(ctx, bson.M{"_id": inserted.InsertedID})
	}()
	require.NoError(userstatus.Unban(testUserID))
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.True(pass)

	// 测试高风险连接的情况
	clearRiskConnectCache := func() {
		err = service.LRURedis.Del(imuserlogs.KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
		require.NoError(err)
	}
	clearRiskConnectCache()
	err = imuserlogs.UpdateOne(bson.M{"_id": inserted.InsertedID}, bson.M{"status": imuserlogs.StatusDisableAllMsg})
	require.NoError(err)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.False(pass)

	// 测试读取缓存的情况
	err = imuserlogs.UpdateOne(bson.M{"_id": inserted.InsertedID}, bson.M{"status": imuserlogs.StatusNormal})
	require.NoError(err)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.False(pass)

	// 测试缓存失效的情况
	clearRiskConnectCache()
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.True(pass)

	// 测试没有连接的情况
	clearRiskConnectCache()
	filter = bson.M{
		"user_id":    testUserID,
		"room_id":    testRoomID,
		"renew_time": bson.M{"$gte": now.Add(-7 * time.Minute).Unix()},
	}
	_, err = imuserlogs.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.False(pass)

	// 测试阿里云风控检测不通过的情况
	cancelRPCMock = mrpc.SetMock(userapi.URLGoScanRisk, func(input interface{}) (output interface{}, err error) {
		return scan.BaseCheckResult{
			Score: 100,
			Pass:  true,
		}, nil
	})
	defer cancelRPCMock()
	pass, creatorBlockUser, err = param.checkUser()
	require.NoError(err)
	assert.False(creatorBlockUser)
	assert.False(pass)
}

func TestGrabParam_checkCouponAbuse(t *testing.T) {
	assert := assert.New(t)

	// 测试 rpc 请求出错的情况
	param := grabParam{
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		equip: &goutil.Equipment{
			BUVID: "test",
		},
		clientIP: "127.0.0.1",
	}
	pass := param.checkCouponAbuse()
	assert.True(pass)

	// 测试营销风险分数小于高风险值的情况
	highRiskScore := config.Conf.Params.RedPacket.HighRiskScore
	defer func() {
		config.Conf.Params.RedPacket.HighRiskScore = highRiskScore
	}()
	cancelRPCMock := mrpc.SetMock(userapi.URLGoScanRisk, func(input interface{}) (output interface{}, err error) {
		return scan.BaseCheckResult{
			Score: highRiskScore - 1,
			Pass:  true,
		}, nil
	})
	defer cancelRPCMock()
	pass = param.checkCouponAbuse()
	assert.True(pass)

	// 测试营销风险分数不小于高风险值的情况
	config.Conf.Params.RedPacket.HighRiskScore = 1
	pass = param.checkCouponAbuse()
	assert.False(pass)

	// 测试高风险分数阈值设置为 0 时的情况
	config.Conf.Params.RedPacket.HighRiskScore = 0
	pass = param.checkCouponAbuse()
	assert.True(pass)
}

func TestGrabParam_checkRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试红包已过期的情况
	param := &grabParam{
		RedPacketID: testRedPacketOID.Hex(),
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveRedPacket: &redpacket.LiveRedPacket{
			OID:    testRedPacketOID,
			Status: redpacket.StatusExpired,
		},
	}
	_, err := param.checkRedPacket()
	require.Equal(actionerrors.ErrRedPacketCanNotGrab, err)

	// 测试红包处于可抢状态
	param.liveRedPacket.Status = redpacket.StatusGrabbing
	isGrabNothing, err := param.checkRedPacket()
	require.NoError(err)
	assert.False(isGrabNothing)

	// 测试红包处于待抢状态且未到开抢时间
	param.liveRedPacket.Status = redpacket.StatusWaiting
	param.liveRedPacket.StartGrabTime = goutil.TimeNow().Add(1 * time.Hour).Unix()
	_, err = param.checkRedPacket()
	require.Equal(actionerrors.ErrRedPacketCanNotGrab, err)

	// 测试红包处于可抢状态且已到开抢时间
	param.liveRedPacket.StartGrabTime = goutil.TimeNow().Add(-1 * time.Hour).Unix()
	isGrabNothing, err = param.checkRedPacket()
	require.NoError(err)
	assert.False(isGrabNothing)
}

func TestGrabParam_drawGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无礼物可抽的情况
	param := grabParam{
		liveRedPacket: &redpacket.LiveRedPacket{
			OID: primitive.NewObjectID(),
		},
	}
	giftID, err := param.drawGift()
	require.NoError(err)
	assert.Equal(int64(0), giftID)

	// 测试可抽到礼物的情况
	param.liveRedPacket.OID = testRedPacketOID
	key := keys.KeyRoomRedPacketGiftNum1.Format(testRedPacketOID.Hex())
	defer func() {
		_ = service.Redis.Del(key)
	}()
	_, err = service.Redis.Pipelined(func(pipeliner redis.Pipeliner) error {
		pipeliner.HSet(key, "1", 1)
		pipeliner.HSet(key, "2", 0)
		return nil
	})
	require.NoError(err)
	giftID, err = param.drawGift()
	require.NoError(err)
	assert.Equal(int64(1), giftID)
}

func TestGrabParam_followCreator(t *testing.T) {
	assert := assert.New(t)

	// 测试关注自己的情况
	param := grabParam{
		user: &user.User{
			IUser: user.IUser{
				ID: 2345543,
			},
		},
		creatorID: 4566546,
	}

	// 测试 rpc 网络出错的情况
	param.creatorID = 4566546
	followed, limited := param.followCreator()
	assert.False(followed)
	assert.False(limited)

	// 测试关注操作触发关注限制
	cancelErrorRPCMock := mrpc.SetMock(attentionuser.URLFollow, func(input interface{}) (output interface{}, err error) {
		return nil, &mrpc.ClientError{
			Code: handler.CodeUserFollowLimit,
		}
	})
	defer cancelErrorRPCMock()
	followed, limited = param.followCreator()
	assert.False(followed)
	assert.True(limited)

	// 测试关注成功的情况
	cancelRPCMock := mrpc.SetMock(attentionuser.URLFollow, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancelRPCMock()
	followed, limited = param.followCreator()
	assert.True(followed)
	assert.False(limited)
}

func TestGrabParam_grabRedPacketLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := redpacket.LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"ip": testIP})
	require.NoError(err)

	param := grabParam{
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveRedPacket: &redpacket.LiveRedPacket{},
		clientIP:      testIP,
	}

	// 测试没有限制
	assert.False(param.grabRedPacketLimit())

	logs := make([]interface{}, 0, 31)
	for i := 0; i < 31; i++ {
		log := &redpacket.LiveRedPacketLog{
			OID:          primitive.NewObjectID(),
			RedPacketOID: primitive.NewObjectID(),
			CreateTime:   goutil.TimeNow().Unix(),
			UserID:       testUserID,
			GiftID:       testGiftID,
			IP:           testIP,
		}
		if i >= 15 {
			log.UserID = testUserID + int64(i)
		}
		logs = append(logs, log)
	}
	_, err = redpacket.LiveRedPacketLogCollection().InsertMany(ctx, logs[:15])
	require.NoError(err)

	// 测试用户抢到红包数超过了限制
	assert.True(param.grabRedPacketLimit())

	// 测试 IP 抢到红包数超过了限制
	_, err = redpacket.LiveRedPacketLogCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = redpacket.LiveRedPacketLogCollection().InsertMany(ctx, logs[15:])
	require.NoError(err)

	assert.True(param.grabRedPacketLimit())

	// 测试自己抢自己红包没有限制
	param.liveRedPacket.UserID = testUserID
	assert.False(param.grabRedPacketLimit())
}
