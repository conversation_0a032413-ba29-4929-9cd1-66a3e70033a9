package redpacket

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liveuserspecialredpacket"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type configResp struct {
	Data    []*configData `json:"data"`
	RuleURL string        `json:"rule_url"`
	Tip     string        `json:"tip"`
}

type configData struct {
	GoodsID       int64             `json:"goods_id"`
	Type          int               `json:"type"`
	CornerIconURL string            `json:"corner_icon_url,omitempty"`
	Price         int               `json:"price"`
	Is<PERSON>ree        bool              `json:"is_free"`
	RemainNum     int64             `json:"remain_num,omitempty"`
	TimeLeft      int64             `json:"time_left,omitempty"`
	GiftTotalNum  int64             `json:"gift_total_num"`
	Gifts         []*configGiftInfo `json:"gifts"`
	WaitDurations []int64           `json:"wait_durations"`
}

type configGiftInfo struct {
	GiftID  int64  `json:"gift_id"`
	Name    string `json:"name"`
	IconURL string `json:"icon_url"`
	Num     int64  `json:"num"`
}

// ActionRedPacketConfig 获取可发送的红包配置接口
/**
 * @api {get} /api/v2/chatroom/redpacket/config{?room_id} 获取可发送的红包配置
 * @apiDescription 获取可发送红包信息
 *
 * @apiVersion 0.1.0
 * @apiName config
 * @apiGroup api/v2/chatroom/redpacket
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [ // 可发送的红包配置列表，数据较少，不做分页
 *         {
 *           "goods_id": 55, // 礼物红包商品 ID
 *           "type": 1, // 红包类型。1: 普通红包；2: 口令红包。一期仅下发 type = 1
 *           "corner_icon_url": "https://static-test.maoercdn.com/test/corner.png", // 角标图
 *           "price": 999, // 红包（购买）价格（单位：钻石）
 *           "is_free": true, // 是否为免费（发送）红包，为 true 时不需要支付钻石来发放红包
 *           "remain_num": 3, // 红包的剩余发放次数，红包无发放次数限制时不下发
 *           "time_left": 60, // 有效期剩余时长，不下发时表示无过期时间。单位：秒
 *           "gift_total_num": 233, // 礼物总数
 *           "gifts": [
 *             {
 *               "gift_id": 1,
 *               "name": "礼物名称",
 *               "icon_url": "https://static-test.maoercdn.com/test/test.png", // 礼物图标
 *               "num": 233 // 礼物数量
 *             }
 *           ],
 *           "wait_durations": [0, 60000, 180000], // 可选开启时间，0 为立即开启，单位：毫秒
 *         }
 *       ],
 *       "rule_url": "https://static-test.maoercdn.com/test/rule.html", // 玩法说明的跳转链接
 *       "tip": "关注主播的观众可领取" // “发红包”按钮下的提示文案
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRedPacketConfig(c *handler.Context) (handler.ActionResponse, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	redPacketParam, err := params.FindRedPacket()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if redPacketParam.IsRoomDisabled(roomID) {
		// 如直播间被禁止发送礼物红包，则返回空的红包配置列表
		return &configResp{
			RuleURL: config.Conf.Params.RedPacket.Rule,
			Tip:     "关注主播的观众可领取",
		}, nil
	}

	// 查询直播间
	roomInfo, err := room.Find(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomInfo == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	// 查询礼物红包商品
	goods, err := livegoods.ListValidRedPacketGoods()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(goods) == 0 {
		return nil, actionerrors.ErrGoodsNotFound
	}
	userID := c.UserID()
	var userOwnedSPMap map[int64]*liveuserspecialredpacket.LiveUserSpecialRedPacket
	if c.Equip().IsAppOlderThan("6.2.3", "6.2.3") {
		// WORKAROUND: 低于 6.2.3 的客户端版本不支持特殊红包
		userOwnedSPMap = make(map[int64]*liveuserspecialredpacket.LiveUserSpecialRedPacket)
	} else {
		userOwnedSPMap, err = getUserOwnedSpecialRedPacketMap(userID, goods)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	var giftIDs []int64
	showGoods := make([]*livegoods.GoodsWithMore, 0, len(goods))
	redPacketMapByGoodsID := make(map[int64]*livegoods.RedPacket, len(goods))
	for _, gd := range goods {
		_, userOwned := userOwnedSPMap[gd.ID]
		if !gd.MoreInfo.RedPacket.CanUse(userID, roomInfo.RoomID, roomInfo.CreatorID, userOwned) {
			continue
		}
		for _, g := range gd.MoreInfo.RedPacket.Gifts {
			giftIDs = append(giftIDs, g.ID)
		}
		redPacketMapByGoodsID[gd.ID] = gd.MoreInfo.RedPacket
		showGoods = append(showGoods, gd)
	}

	resp := &configResp{
		RuleURL: config.Conf.Params.RedPacket.Rule,
		Tip:     "关注主播的观众可领取",
	}
	if len(showGoods) == 0 || len(giftIDs) == 0 {
		logger.WithField("room_id", roomID).Error("礼物红包配置中礼物配置错误")
		return resp, nil
	}

	// 根据 ID 查询礼物信息
	gifts, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.Data = make([]*configData, 0, len(goods))
	for _, gd := range showGoods {
		redPacket, ok := redPacketMapByGoodsID[gd.ID]
		if !ok {
			logger.WithField("goods_id", gd.ID).Error("礼物红包配置不存在")
			continue
		}

		data := &configData{
			GoodsID:       gd.ID,
			Type:          redPacket.Type,
			CornerIconURL: redPacket.CornerIconURL(),
			Price:         gd.Price,
			WaitDurations: redPacket.WaitDurations,
			IsFree:        gd.IsFree(),
		}
		if redPacket.IsSpecialRedPacket() {
			// 若为特殊红包，则下发字段带上额外的信息
			userOwnedSP := userOwnedSPMap[gd.ID]
			data.RemainNum = userOwnedSP.Num
			data.TimeLeft = userOwnedSP.TimeLeft()
		}
		for _, redPacketGift := range redPacket.Gifts {
			data.GiftTotalNum += redPacketGift.Num
			giftInfo, ok := gifts[redPacketGift.ID]
			if !ok {
				logger.WithFields(logger.Fields{
					"goods_id": gd.GoodsID(),
					"gift_id":  redPacketGift.ID,
				}).Error("礼物红包商品配置的礼物不存在")
				break
			}
			confGift := &configGiftInfo{
				GiftID:  giftInfo.GiftID,
				Name:    giftInfo.Name,
				IconURL: giftInfo.Icon,
				Num:     redPacketGift.Num,
			}
			data.Gifts = append(data.Gifts, confGift)
		}
		// 下发礼物数与配置礼物数一致时下发礼物红包，否则会导致抢红包异常
		if len(data.Gifts) == len(redPacket.Gifts) {
			resp.Data = append(resp.Data, data)
		}
	}
	return resp, nil
}

// getUserOwnedSpecialRedPacketMap 获取用户可发放的特殊红包 map
func getUserOwnedSpecialRedPacketMap(userID int64, goods []*livegoods.GoodsWithMore) (map[int64]*liveuserspecialredpacket.LiveUserSpecialRedPacket, error) {
	userOwnedSPMap := make(map[int64]*liveuserspecialredpacket.LiveUserSpecialRedPacket)
	if userID == 0 {
		// 未登录用户无可发放的免费红包
		return userOwnedSPMap, nil
	}
	specialGoodsIDs := make([]int64, 0, len(goods))
	for _, gd := range goods {
		if gd.MoreInfo.RedPacket.IsSpecialRedPacket() {
			specialGoodsIDs = append(specialGoodsIDs, gd.ID)
		}
	}
	if len(specialGoodsIDs) == 0 {
		return userOwnedSPMap, nil
	}
	specialRedPackets, err := liveuserspecialredpacket.ListUserValidRedPacket(userID, specialGoodsIDs)
	if err != nil {
		return userOwnedSPMap, err
	}
	for _, sp := range specialRedPackets {
		goodsID := sp.GoodsID
		userOwnedSP, ok := userOwnedSPMap[goodsID]
		if ok {
			// 用户有多条发放记录时，发放数量累加
			userOwnedSP.Num += sp.Num
			if userOwnedSP.EndTime == 0 {
				// 使用最接近当前时间的的过期时间（传入的 goods 已按 end_time ASC 排序）
				userOwnedSP.EndTime = sp.EndTime
			}
		} else {
			userOwnedSPMap[goodsID] = sp
		}
	}
	return userOwnedSPMap, nil
}
