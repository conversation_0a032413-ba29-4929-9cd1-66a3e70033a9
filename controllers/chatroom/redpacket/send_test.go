package redpacket

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSendTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(sendParam{}, "room_id", "goods_id", "countdown")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(sendParam{}, "room_id", "goods_id", "countdown")
	kc.Check(sendResp{}, "message", "balance")
	kc.CheckOmitEmpty(sendResp{}, "balance")
	kc.Check(Elem{}, "red_packet_id", "type", "corner_icon_url", "price", "gift_total_price",
		"skin_url", "sender", "gift_total_num", "gifts", "remain_duration")
	kc.CheckOmitEmpty(Elem{}, "corner_icon_url")
	kc.Check(Gift{}, "gift_id", "num", "most_valuable", "name", "icon_url")
	kc.CheckOmitEmpty(Gift{}, "most_valuable")
}

func createTestGiftData(giftIDs []int64) error {
	now := goutil.TimeNow()
	gifts := make([]interface{}, 0, len(giftIDs))
	for index, giftID := range giftIDs {
		gifts = append(gifts, gift.Gift{
			OID:       primitive.NewObjectID(),
			GiftID:    giftID,
			Name:      fmt.Sprintf("礼物红包礼物 %d", index),
			NameClean: fmt.Sprintf("礼物红包礼物 %d", index),
			Type:      gift.TypeRebate,
			Order:     index + 1,
			AddedTime: now,
			Price:     100 * int64(index+1),
			Icon:      fmt.Sprintf("oss://test/icon%d.png ", index),
		})
	}
	// 生成礼物红包的礼物测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := gift.Collection().InsertMany(ctx, gifts)
	if err != nil {
		return err
	}
	return nil
}

func clearTestGiftData(giftIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除礼物红包的礼物测试数据
	_, err := gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs}})
	if err != nil {
		return err
	}
	return nil
}

func createTestAppearanceData(appearanceID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := appearance.Collection().InsertOne(ctx, &appearance.Appearance{
		ID:         appearanceID,
		Name:       "测试红包封面",
		Type:       appearance.TypeRedPacket,
		From:       appearance.FromDefault,
		Resource:   "oss://test/skin.zip",
		StartTime:  goutil.TimeNow().Unix() - 100,
		ExpireTime: nil,
	})
	if err != nil {
		return err
	}
	return nil
}

func clearTestAppearanceData(appearanceID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := appearance.Collection().DeleteOne(ctx, bson.M{"id": appearanceID})
	if err != nil {
		return err
	}
	return nil
}

func createTestUserAppearanceData(appearanceID, userID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := userappearance.Collection().InsertOne(ctx, &userappearance.UserAppearance{
		AppearanceID: appearanceID,
		Status:       userappearance.StatusWorn,
		UserID:       userID,
		Name:         "测试红包封面",
		Type:         appearance.TypeRedPacket,
		From:         appearance.FromDefault,
		Resource:     "oss://test/skin1.zip",
		StartTime:    goutil.TimeNow().Unix() - 100,
		ExpireTime:   nil,
	})
	if err != nil {
		return err
	}
	return nil
}

func clearTestUserAppearanceData(appearanceID, userID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := userappearance.Collection().DeleteOne(ctx, bson.M{"appearance_id": appearanceID, "user_id": userID})
	if err != nil {
		return err
	}
	return nil
}

func TestActionRedPacketSend(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gifts := []int64{40100, 40101}
	require.NoError(clearTestGiftData(gifts))

	cleanup := mrpc.SetMock(userapi.URIBuyGoods,
		func(input interface{}) (output interface{}, err error) {
			return userapi.BalanceResp{
				TransactionID:    23333,
				Balance:          123,
				LiveNobleBalance: 123,
				Price:            123,
				Context:          `{"transaction_id":22222,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}`,
			}, nil
		})
	defer cleanup()

	// 测试接口请求出错的情况
	param := &sendParam{
		RoomID: 12345,
	}
	api := "/api/v2/chatroom/redpacket/send"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	result, err := ActionRedPacketSend(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	cleanup = mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()

	param = &sendParam{
		RoomID:    testRoomID,
		GoodsID:   17,
		Countdown: 60000,
	}
	_, err = room.Update(testRoomID, bson.M{"status.open": room.StatusOpenFalse})
	require.NoError(err)

	// 测试直播间未开播时
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionRedPacketSend(c)
	assert.Equal(actionerrors.ErrClosedRoomAlt, err)
	assert.Nil(result)

	_, err = room.Update(testRoomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	// 测试商品对应的礼物不存在时
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionRedPacketSend(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)
	assert.Nil(result)

	require.NoError(clearTestGiftData(gifts))
	require.NoError(createTestGiftData([]int64{40100, 40101}))

	// 删除红包封面图默认外观数据
	require.NoError(clearTestAppearanceData(1000))
	// 生成红包封面图默认外观数据
	require.NoError(createTestAppearanceData(1000))

	// 测试发红包成功
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = redpacket.LiveRedPacketCollection().DeleteMany(ctx, bson.M{
		"room_id": testRoomID,
		"status":  bson.M{"$in": []int64{redpacket.StatusWaiting, redpacket.StatusGrabbing}},
	})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionRedPacketSend(c)
	require.NoError(err)
	require.NotNil(result)
	resp := result.(*sendResp)
	assert.Equal("红包已发送~", resp.Message)
	assert.Equal(int64(123), resp.Balance.Balance)
	assert.Equal(int64(123), resp.Balance.LiveNobleBalance)
	assert.Equal(0, resp.Balance.LiveNobleBalanceStatus)
	// 删除生成的红包数据
	_, err = redpacket.LiveRedPacketCollection().DeleteOne(ctx, bson.M{"room_id": param.RoomID, "user_id": c.User().ID})
	require.NoError(err)
}

func TestNewSendParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gifts := []int64{40100, 40101}
	require.NoError(clearTestGiftData(gifts))

	// 测试参数错误
	body := &sendParam{
		RoomID: 12345,
	}
	api := "/api/v2/chatroom/redpacket/send"
	c := handler.NewTestContext(http.MethodPost, api, true, body)
	param, err := newSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	redPacketParam := params.RedPacket{
		Key:            params.KeyRedPacket,
		DisableRoomIDs: []int64{1},
	}
	cacheKey := keys.KeyParams1.Format(redPacketParam.Key)
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(redPacketParam), 10*time.Second).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, handler.M{"room_id": 1, "goods_id": 100})
	_, err = newSendParam(c)
	assert.EqualError(err, "暂时无法操作")

	// 测试房间不存在
	body = &sendParam{
		RoomID:  123,
		GoodsID: 100,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newSendParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
	assert.Nil(param)

	// 测试房间未开播时
	_, err = room.Update(testRoomID, bson.M{"status.open": room.StatusOpenFalse})
	require.NoError(err)
	body = &sendParam{
		RoomID:    testRoomID,
		GoodsID:   999,
		Countdown: 60000,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newSendParam(c)
	assert.Equal(actionerrors.ErrClosedRoomAlt, err)
	assert.Nil(param)

	_, err = room.Update(testRoomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	// 测试礼物房不支持发红包
	_, err = room.Update(testRoomID, bson.M{"limit": &room.Limit{
		Type:           room.LimitTypeNormalGift,
		AllowedUserIDs: []int64{12, 123},
	}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newSendParam(c)
	assert.Equal(actionerrors.NewErrForbidden("本直播间内无法发红包"), err)
	assert.Nil(param)

	_, err = room.UpdateRoom(bson.M{"room_id": testRoomID}, bson.M{"$unset": bson.M{"limit": ""}})
	require.NoError(err)

	// 测试商品不存在
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newSendParam(c)
	assert.Equal(actionerrors.ErrGoodsNotFound, err)
	assert.Nil(param)

	// 测试礼物信息不存在
	body.GoodsID = 17
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newSendParam(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)
	assert.Nil(param)

	require.NoError(createTestGiftData(gifts))

	// 删除红包封面图默认外观数据
	require.NoError(clearTestAppearanceData(1000))
	// 生成红包封面图默认外观数据
	require.NoError(createTestAppearanceData(1000))

	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()

	// 测试正常
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	// 删除用户外观中心的红包封面外观数据
	require.NoError(clearTestUserAppearanceData(1001, c.UserID()))
	// 生成用户外观中心的红包封面外观数据
	require.NoError(createTestUserAppearanceData(1001, c.UserID()))
	param, err = newSendParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.room)
	assert.NotNil(param.liveGoods)
	assert.NotNil(param.liveGoodsMore)
	assert.NotNil(param.giftTotalPrice)
	require.NotNil(param.giftNumMap)
	require.Len(param.giftNumMap, 2)
	assert.EqualValues(10, param.giftNumMap["40100"])
	assert.EqualValues(15, param.giftNumMap["40101"])
	require.NotNil(param.redPacketGifts)
	require.Len(param.redPacketGifts, 2)
	assert.EqualValues(40100, param.redPacketGifts[0].GiftID)
	assert.EqualValues(40101, param.redPacketGifts[1].GiftID)
	// 测试优先使用用户外观中心的红包封面皮肤资源地址
	assert.Equal("https://static-test.missevan.com/test/skin1.zip", param.skinURL)
	assert.EqualValues(1001, param.appearanceID)

	// 测试用户外观中心没有红包封面皮肤资源地址，则使用红包商品配置的默认封面皮肤资源地址
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	// 删除用户外观中心的红包封面外观数据
	require.NoError(clearTestUserAppearanceData(1001, c.UserID()))
	param, err = newSendParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal("https://static-test.missevan.com/test/skin.zip", param.skinURL)
	assert.EqualValues(1000, param.appearanceID)
}

func TestSendParam_sendRedPacket(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(userapi.URIBuyGoods,
		func(input interface{}) (output interface{}, err error) {
			return userapi.BalanceResp{
				TransactionID:    23333,
				Balance:          123,
				LiveNobleBalance: 123,
				Price:            0,
				Context:          `{"transaction_id":22222,"tax":12.5,"price":0,"common_coin":{"ios":100,"android":0,"tmallios":0}`,
			}, nil
		})
	defer cleanup()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	goods, err := livegoods.Find(23, livegoods.GoodsTypeRedPacket)
	require.NoError(err)
	liveGoodsMore, err := goods.UnmarshalMore()
	require.NoError(err)
	param := &sendParam{
		RoomID:        testRoomID,
		GoodsID:       23,
		Countdown:     60000,
		userID:        11,
		giftTotalNum:  100,
		liveGoods:     goods,
		liveGoodsMore: liveGoodsMore,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 1234,
			},
		},
		giftNumMap: map[string]interface{}{
			"2001": 10,
			"2002": 15,
		},
		isFree:             true,
		isSpecialRedPacket: true,
		specialRedPacketID: 7,
		appearanceID:       1000,
		c:                  handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}

	// 测试成功发放红包
	key := keys.LockRoomBuyRedPacketGoods1.Format(testRoomID)
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	err = param.sendRedPacket()
	require.NoError(err)
	assert.NotNil(param.redPacket)

	redPacket := new(redpacket.LiveRedPacket)
	err = redpacket.LiveRedPacketCollection().FindOne(ctx,
		bson.M{"goods_id": param.liveGoods.ID, "user_id": param.userID},
		options.FindOne()).Decode(redPacket)
	require.NoError(err)
	assert.Equal(redpacket.StatusWaiting, redPacket.Status)
	assert.Equal(param.Countdown, redPacket.WaitDuration)
	assert.Equal(param.giftTotalNum, redPacket.RemainGiftNum)
	// 免费红包的发放记录中无订单信息
	assert.EqualValues(0, redPacket.TransactionID)
	assert.Empty(redPacket.Context)
	// 写入特殊红包记录 ID
	assert.EqualValues(7, redPacket.SpecialRedPacketID)

	// 断言红包剩余礼物数量
	hKey := keys.KeyRoomRedPacketGiftNum1.Format(param.redPacket.OID.Hex())
	res, err := service.Redis.HGetAll(hKey).Result()
	assert.NoError(err)
	assert.Equal("10", res["2001"])
	assert.Equal("15", res["2002"])
	// 断言缓存过期时长
	ttl, err := service.Redis.TTL(hKey).Result()
	require.NoError(err)
	assert.Less(36*time.Hour-5*time.Second, ttl)
	// 删除缓存
	service.Redis.Del(hKey)

	// 删除生成的红包数据
	_, err = redpacket.LiveRedPacketCollection().DeleteOne(ctx, bson.M{"room_id": param.RoomID, "user_id": param.userID})
	require.NoError(err)

	// 测试加锁失败
	_, err = service.Redis.SetNX(key, 18, 10*time.Second).Result()
	require.NoError(err)
	err = param.sendRedPacket()
	assert.EqualError(err, "操作太快啦，请稍后再试~")
	err = service.Redis.Del(key).Err()
	require.NoError(err)
}

func TestSendParam_deductSpecialRedPacket(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 用户正常发放免费红包
	goods, err := livegoods.Find(23, livegoods.GoodsTypeRedPacket)
	require.NoError(err)
	liveGoodsMore, err := goods.UnmarshalMore()
	require.NoError(err)
	param := &sendParam{
		RoomID:        testRoomID,
		GoodsID:       23,
		Countdown:     60000,
		userID:        13,
		giftTotalNum:  1000,
		liveGoods:     goods,
		liveGoodsMore: liveGoodsMore,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 1234,
			},
		},
		giftNumMap: map[string]interface{}{
			"2001": 10,
			"2002": 15,
		},
		specialRedPacketID: 6,
		appearanceID:       1000,
	}
	err = param.deductSpecialRedPacket()
	require.NoError(err)

	// 用户的免费红包已发完
	err = param.deductSpecialRedPacket()
	assert.Error(err, "您的可用免费红包数量不足")
}

func TestSendParam_addRedPacket(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	goods, err := livegoods.Find(17, livegoods.GoodsTypeRedPacket)
	require.NoError(err)
	liveGoodsMore, err := goods.UnmarshalMore()
	require.NoError(err)
	param := &sendParam{
		RoomID:        testRoomID,
		GoodsID:       17,
		Countdown:     60000,
		userID:        13,
		giftTotalNum:  100,
		liveGoods:     goods,
		liveGoodsMore: liveGoodsMore,
		isFree:        false,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 1234,
			},
		},
		giftNumMap: map[string]interface{}{
			"2001": 10,
			"2002": 15,
		},
		rpcResp: &userapi.BalanceResp{
			TransactionID: 123456,
			Context:       `{"transaction_id":22222,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}`,
		},
		appearanceID: 1000,
	}
	expireDuration, err := time.ParseDuration(config.Conf.Params.RedPacket.ExpireDuration)
	require.NoError(err)

	// 测试正常发红包
	err = param.addRedPacket(expireDuration, 0)
	require.NoError(err)
	assert.NotNil(param.redPacket)
	assert.Equal(param.rpcResp.TransactionID, param.redPacket.TransactionID)
	assert.Equal(param.rpcResp.Context, param.redPacket.Context)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 断言发红包记录数据
	redPacket := new(redpacket.LiveRedPacket)
	err = redpacket.LiveRedPacketCollection().FindOne(ctx,
		bson.M{"goods_id": param.liveGoods.ID, "user_id": param.userID, "transaction_id": param.rpcResp.TransactionID},
		options.FindOne().SetSort(bson.M{"_id": -1})).Decode(redPacket)
	require.NoError(err)
	require.NotNil(redPacket)
	assert.Equal(redpacket.StatusWaiting, redPacket.Status)
	assert.Equal(param.Countdown, redPacket.WaitDuration)
	assert.Equal(param.giftTotalNum, redPacket.RemainGiftNum)
	assert.EqualValues(param.rpcResp.TransactionID, redPacket.TransactionID)
	assert.Equal(param.rpcResp.Context, redPacket.Context)
	assert.NotNil(param.redPacket)
	assert.EqualValues(1000, param.redPacket.AppearanceID)

	// 测试红包剩余礼物数量
	hKey := keys.KeyRoomRedPacketGiftNum1.Format(param.redPacket.OID.Hex())
	res, err := service.Redis.HGetAll(hKey).Result()
	assert.NoError(err)
	assert.Equal("10", res["2001"])
	assert.Equal("15", res["2002"])

	// 测试缓存过期时长
	ttl, err := service.Redis.TTL(hKey).Result()
	require.NoError(err)
	assert.Less(36*time.Hour-5*time.Second, ttl)

	// 删除缓存
	service.Redis.Del(hKey)
}

func TestSendParam_buyRedPacket(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cleanup := mrpc.SetMock(userapi.URIBuyGoods,
		func(input interface{}) (output interface{}, err error) {
			return userapi.BalanceResp{
				TransactionID:    23333,
				Balance:          123,
				LiveNobleBalance: 123,
				Price:            123,
				Context:          `{"transaction_id":22222,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}`,
			}, nil
		})
	defer cleanup()

	goods, err := livegoods.Find(17, livegoods.GoodsTypeRedPacket)
	require.NoError(err)
	liveGoodsMore, err := goods.UnmarshalMore()
	require.NoError(err)
	param := &sendParam{
		RoomID:        testRoomID,
		GoodsID:       17,
		Countdown:     60000,
		userID:        12,
		giftTotalNum:  100,
		liveGoods:     goods,
		liveGoodsMore: liveGoodsMore,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 1234,
			},
		},
		giftNumMap: map[string]interface{}{
			"2001": 10,
			"2002": 15,
		},
		c: handler.NewTestContext(http.MethodPost, "/send", true, nil),
	}

	// 测试正常购买的情况
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = redpacket.LiveRedPacketCollection().DeleteMany(ctx, bson.M{
		"room_id": testRoomID,
		"status":  bson.M{"$in": []int64{redpacket.StatusWaiting, redpacket.StatusGrabbing}},
	})
	require.NoError(err)
	err = param.buyRedPacket()
	require.NoError(err)

	// 验证创建了商品订单数据
	order := livetxnorder.LiveTxnOrder{}
	err = service.LiveDB.Table(livetxnorder.TableName()).
		Where("goods_id = ? AND buyer_id = ?", 17, 12).
		Order("id DESC").Take(&order).Error
	require.NoError(err)
	assert.EqualValues(23333, order.TID)
	assert.Equal(param.liveGoods.Price, order.Price)
	assert.Equal(util.NewInt(livetxnorder.OpenStatusClosed), order.More.OpenStatus)
}

func TestSendParam_addUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &sendParam{
		userID:    12,
		liveGoods: &livegoods.LiveGoods{Price: 100},
		RoomID:    testRoomID,
		room: &room.Room{
			Helper: room.Helper{
				CreatorUsername: "测试用户名称",
			},
		},
	}
	before, err := liveuser.Find(param.userID)
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.userID)
	require.NoError(err)
	assert.EqualValues(param.liveGoods.Price*10, after.Contribution-before.Contribution)
	before = after
	param.userVip = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.userID)
	require.NoError(err)
	assert.EqualValues(param.liveGoods.Price*2*10, after.Contribution-before.Contribution)
}
