package redpacket

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestNotifyTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RoomMessage{}, "type", "event", "room_id", "user", "red_packet", "bubble")
	kc.Check(NotifyPayload{}, "type", "notify_type", "event", "notify_queue", "room_id", "user", "red_packet",
		"notify_bubble", "message")
	kc.CheckOmitEmpty(NotifyPayload{}, "notify_queue", "notify_bubble")
}

func TestSendParam_broadcast(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRoomID := int64(22489473)
	param := sendParam{
		userID: 12,
		RoomID: testRoomID,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID:       13,
				RoomID:          testRoomID,
				CreatorUsername: "主播用户名",
			},
		},
		giftTotalPrice: 21000,
		giftTotalNum:   120,
		Countdown:      60000,
		liveGoodsMore: &livegoods.More{
			RedPacket: &livegoods.RedPacket{
				CornerIcon: "oss://test/corner_icon.png",
				Type:       livegoods.RedPacketTypeNormal,
			},
		},
		redPacket: &redpacket.LiveRedPacket{
			OID: primitive.NewObjectID(),
		},
		liveGoods: &livegoods.LiveGoods{
			Price: redpacket.MinPriceBroadcast,
		},
		redPacketGifts: []*Gift{
			{GiftID: 1, Num: 100, Name: "礼物 1"},
			{GiftID: 2, Num: 49, Name: "礼物 2"},
			{GiftID: 3, Num: 1, Name: "礼物 3", MostValuable: true},
		},
		user:    &liveuser.Simple{UID: 12},
		bubble:  &bubble.Simple{Type: "test", BubbleID: 1},
		skinURL: "https://static-test.maoercdn.com/test/skin.zip",
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 设置飘屏气泡框
	b := bubble.Bubble{
		BubbleID:       bubbleIDRedPacket,
		Image:          "oss://live/bubbles/notify/11_0_66_0_108.png",
		Type:           bubble.TypeNotify,
		NormalColor:    "#FFEFD3",
		HighlightColor: "#F9F2A7",
	}
	_, err := bubble.Collection().UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)

	cancel = mrpc.SetMock(userapi.URIIMBroadcastMany,
		func(input interface{}) (interface{}, error) {
			type body struct {
				Payload NotifyPayload `json:"payload"`
			}
			var params []body
			err = json.Unmarshal(input.(json.RawMessage), &params)
			require.NoError(err)
			assert.EqualValues(bubbleIDRedPacket,
				params[1].Payload.NotifyBubble.BubbleID)
			assert.Len(params, 2)
			return "success", nil
		})
	defer cancel()

	// 测试全站飘屏和直播间消息都有时
	assert.NotPanics(func() { param.broadcast() })

	cancel = mrpc.SetMock(userapi.URIIMBroadcastMany,
		func(input interface{}) (interface{}, error) {
			type body struct {
				Payload NotifyPayload `json:"payload"`
			}
			var params []body
			err = json.Unmarshal(input.(json.RawMessage), &params)
			require.NoError(err)
			assert.Len(params, 1)
			return "success", nil
		})
	defer cancel()

	// 测试没有全站飘屏只有直播间消息时
	param.liveGoods.Price--
	assert.NotPanics(func() { param.broadcast() })
}
