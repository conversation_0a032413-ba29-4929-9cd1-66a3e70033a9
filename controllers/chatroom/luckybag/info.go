package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type luckyBagInfoResp struct {
	LuckyBag  *luckybag.FullInfo    `json:"lucky_bag"`
	MedalGift *gift.ObtainMedalGift `json:"medal_gift,omitempty"`
}

type luckyBagInfoParam struct {
	roomID     int64
	luckyBagID int64

	userID   int64
	r        *room.Room
	luckyBag *luckybag.InitiateRecord

	c *handler.Context
}

func newLuckyBagInfoParam(c *handler.Context) (*luckyBagInfoParam, error) {
	var param luckyBagInfoParam

	var err error
	param.roomID, err = c.GetParamInt64("room_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.luckyBagID, err = c.GetParamInt64("lucky_bag_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	param.r, err = room.Find(param.roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	param.luckyBag, err = luckybag.FindShowingInitiateRecordByID(param.luckyBagID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.luckyBag == nil {
		return nil, actionerrors.ErrNotFound("福袋不存在")
	}
	if param.luckyBag.RoomID != param.roomID {
		return nil, actionerrors.NewErrForbidden("福袋不属于当前房间")
	}
	param.userID = c.UserID()
	param.c = c
	return &param, nil
}

/*
FIXME: 第一个返回值是具体的结构体时可能出现 (xxx)nil != nil 判断为 true 的情况，
在 UAT 可能出现“action 返回的 data 和 err 自带的 data 冲突”错误
*/
func (param *luckyBagInfoParam) creatorResp() (handler.ActionResponse, string, error) {
	resp := &luckyBagInfoResp{
		LuckyBag: luckybag.NewFullInfo(param.luckyBag),
	}

	var err error
	resp.LuckyBag.LuckyUsers, err = param.findLuckyUsers()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 设置 increase_num
	resp.LuckyBag.IncreaseNum = param.findIncreaseNum()
	return resp, "", nil
}

func (param *luckyBagInfoParam) listenerResp() (handler.ActionResponse, string, error) {
	resp := &luckyBagInfoResp{
		LuckyBag: luckybag.NewFullInfo(param.luckyBag),
	}

	var err error
	resp.LuckyBag.LuckyUsers, err = param.findLuckyUsers()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	if resp.LuckyBag.TargetType == luckybag.TargetTypeMedal && param.r.CreatorID != param.userID {
		resp.MedalGift = gift.FindObtainMedalGift(param.r.CreatorID, param.userID, param.c.Equip())
	}

	if param.userID == 0 {
		resp.LuckyBag.JoinStatus = util.NewInt(0)
		return resp, "", nil
	}

	ok, err := luckybag.JoinRecordExists(param.luckyBag.ID, param.userID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if ok {
		resp.LuckyBag.JoinStatus = util.NewInt(1)
	} else {
		resp.LuckyBag.JoinStatus = util.NewInt(0)
	}
	return resp, "", nil
}

func (param *luckyBagInfoParam) findIncreaseNum() *int64 {
	if param.luckyBag.TargetType == luckybag.TargetTypeAll {
		return nil
	}

	if param.luckyBag.MoreInfo == nil || param.luckyBag.MoreInfo.StartTargetNum == nil {
		logger.WithField("lucky_bag_id", param.luckyBag.ID).Error("福袋 more 异常")
		return nil
	}

	// 福袋已结束，从发起记录取
	switch param.luckyBag.Status {
	case luckybag.StatusDrawing, luckybag.StatusFinish:
		num := luckybag.CalculateIncreaseNum(param.luckyBag.MoreInfo)
		return &num
	}

	// 福袋未结束，取实时结果
	curNum, _, err := luckybag.FindTargetNum(param.luckyBag)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	num := curNum - *param.luckyBag.MoreInfo.StartTargetNum
	if num < 0 {
		num = 0
	}
	return &num
}

func (param *luckyBagInfoParam) findLuckyUsers() ([]luckybag.LuckyUser, error) {
	if param.luckyBag.Status != luckybag.StatusFinish {
		return nil, nil
	}
	userIDs, err := luckybag.ListLuckyUserIDs(param.luckyBag.ID)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	luckyUsers, err := luckybag.NewLuckyUsers(userIDs)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return luckyUsers, nil
}

// ActionLuckyBagInfo 获取福袋详情
/**
 * @api {get} /api/v2/chatroom/luckybag/info 获取福袋详情
 * @apiDescription 获取福袋详情，主播打开半窗后在待开奖期间定时请求，直到状态改变。 \
 *   用户仅在打开半窗的时候请求一次，在半窗内开奖倒计时结束后未收到消息随机延迟 5 - 10s 请求进行兜底
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} lucky_bag_id 福袋 ID
 *
 * @apiSuccessExample 主播端:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "lucky_bag": {
 *         "lucky_bag_id": 1,
 *         "status": 0, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *         "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *         "reward_type": 2, // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *         "prize_name": "签名照", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *         "prize_num": 1, // 奖品数量
 *         "prize_price": 100, // 福袋每份价值，只有剧集福袋有价格，单位：钻
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图
 *         "target_type": 0, // 参与对象类型 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 *         "remain_duration": 1800, // 剩余时间（毫秒）
 *         "keyword": "点点关注抽福袋", // 参与默认口令
 *         "join_num": 19999, // 参与人数
 *         "increase_num": 10, // 新增人数
 *         "lucky_users": [ // 中奖用户，主播端开奖后返回
 *           {
 *             "user_id": 1223,
 *             "username": "userA"
 *           },
 *           {
 *             "user_id": 1224,
 *             "username": "userB"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample 用户端:
 *   // 用户端不显示新增人数但比主播端多参与状态和中奖状态
 *   // 参与对象类型为粉丝勋章并且用户没有当前房间的粉丝勋章时返回一键获取粉丝勋章的礼物信息
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "lucky_bag": {
 *         "lucky_bag_id": 1,
 *         "status": 0, // 福袋状态 1: 待开奖; 2: 已开奖
 *         "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *         "reward_type": 2, // 奖励类型 type 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *         "prize_name": "签名照", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *         "prize_num": 1, // 奖品数量
 *         "prize_price": 100, // 福袋每份价值，只有剧集福袋有价格，单位：钻
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图
 *         "join_num": 19999, // 参与人数
 *         "target_type": 0, // 参与对象类型 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 *         "remain_duration": 1800, // 剩余时间（毫秒）
 *         "keyword": "点点关注抽福袋", // 参与口令
 *         "lucky_users": [ // 中奖用户，主播端开奖后返回，用户中奖状态客户端直接判断是否在数组中有记录即可
 *           {
 *             "user_id": 1223,
 *             "username": "userA"
 *           },
 *           {
 *             "user_id": 1224,
 *             "username": "userB"
 *           }
 *         ],
 *         "join_status": 1 // 是否参与抽奖 0: 未参与; 1: 已参与
 *       },
 *       "medal_gift": { // 一键获取粉丝勋章的礼物信息，仅在参与对象类型是粉丝勋章且用户没有当前房间粉丝勋章时返回
 *         "gift_id": 1,
 *         "name": "礼物 A",
 *         "price": 1234, // 礼物价值（钻）
 *         "icon_url": "http://static.example.com/gift/001.png",
 *         "discount": { // 折扣信息，没有需要展示的折扣不下发此字段
 *           "tip": "粉团 1 折" // 获取粉丝勋章的优惠信息
 *         }
 *       }
 *     }
 *   }
 */
func ActionLuckyBagInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newLuckyBagInfoParam(c)
	if err != nil {
		return nil, "", err
	}
	if param.luckyBag.CreatorID == c.UserID() {
		return param.creatorResp()
	}
	return param.listenerResp()
}
