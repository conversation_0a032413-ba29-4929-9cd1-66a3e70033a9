package luckybag

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/mysql/liveredpacketgrabblockuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewJoinParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID     int64 = 18113499
		testLuckyBagID int64 = 11
	)

	uri := "/api/v2/chatroom/luckybag/join"
	c := handler.NewTestContext(http.MethodPost, uri, true, handler.M{"room_id": -1, "lucky_bag_id": 1})
	_, err := newJoinParams(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{"room_id": testRoomID, "lucky_bag_id": testLuckyBagID})
	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "6.1.3"
	lock := keys.LockUserLuckyBagJoin1.Format(c.UserID())
	require.NoError(service.Redis.Del(lock).Err())
	_, err = newJoinParams(c)
	assert.EqualError(err, "当前版本暂不支持该玩法哦~")

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{"room_id": testRoomID, "lucky_bag_id": testLuckyBagID})
	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "6.1.4"
	p, err := newJoinParams(c)
	require.NoError(err)
	require.NotNil(p)
	assert.NotNil(p.lb)
	assert.NotNil(p.msgID)
}

func TestJoinParam_checkUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(564654)
	testRoomID := int64(45645)
	blockListKey := keys.KeyUsersBlockList0.Format()
	require.NoError(service.Redis.ZRem(blockListKey, testUserID).Err())
	cancelRPCMock := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(input any) (output any, err error) {
		return map[string][]bool{
			"block_status": {true, false},
		}, nil
	})
	defer cancelRPCMock()
	err := service.LRURedis.Del(imuserlogs.KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
	require.NoError(err)

	// 测试未绑定手机号时参与福袋的情况
	param := joinParams{
		RoomID: testRoomID,
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		r: &room.Room{
			Helper: room.Helper{
				CreatorID: 999,
			},
		},
	}
	err = param.checkUser()
	assert.EqualError(err, "参与福袋需要绑定手机号哦~")

	// 测试主播自己参与福袋
	param.r.CreatorID = testUserID
	param.user.Mobile = "123456789"
	err = param.checkUser()
	assert.EqualError(err, "不可参与当前福袋")

	// 测试抢红包用户被发送红包用户或主播拉黑的情况
	param.r.CreatorID = 782347
	err = param.checkUser()
	assert.EqualError(err, "由于主播设置，您无法参与本次喵喵福袋")

	cancelRPCMock = mrpc.SetMock(userapi.URIGoUserBlockStatus, func(input any) (output any, err error) {
		return map[string][]bool{
			"block_status": {false, false},
		}, nil
	})
	defer cancelRPCMock()
	// 测试在全站黑名单内的情况
	require.NoError(service.Redis.ZAdd(blockListKey, &redis.Z{Score: blocklist.StatusBlockUserForever, Member: testUserID}).Err())
	err = param.checkUser()
	assert.EqualError(err, "您的账号已被封禁，无法参与福袋")

	// 测试被禁言的情况
	require.NoError(service.Redis.ZRem(blockListKey, testUserID).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": testUserID, "room_id": testRoomID}
	now := goutil.TimeNow()
	_, err = livemembers.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"updated_time": now.Unix(),
			},
			"$setOnInsert": bson.M{
				"user_id": testUserID,
				"status":  livemembers.StatusMute,
				"room_id": testRoomID,
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	err = param.checkUser()
	assert.EqualError(err, "您已被禁言，无法发送福袋口令")

	// 测试用户在抢红包黑名单中的情况
	_, err = livemembers.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	r := &liveredpacketgrabblockuser.RedpacketGrabBlockUser{
		UserID: testUserID,
	}
	require.NoError(r.DB().Create(r).Error)
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")
	require.NoError(r.DB().Delete("", "user_id = ?", testUserID).Error)

	// 测试封禁用户抢红包的情况
	cancelRPCMock = mrpc.SetMock(userapi.URIGoUserBlockStatus, func(input any) (output any, err error) {
		return map[string][]bool{
			"block_status": {false, false},
		}, nil
	})
	defer cancelRPCMock()
	require.NoError(userstatus.BanUser(testUserID, now.Unix(), 0, userstatus.TypeBanForever))
	err = param.checkUser()
	assert.EqualError(err, "您的账号已被封禁，无法参与福袋")

	// 测试 check pass 的情况
	nowUnix := goutil.TimeNow().Unix()
	inserted, err := imuserlogs.Collection().InsertOne(ctx, imuserlogs.Log{
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
		UserID:       testUserID,
		Status:       imuserlogs.StatusNormal,
		RoomID:       testRoomID,
		RenewTime:    nowUnix,
	})
	require.NoError(err)
	defer func() {
		_, _ = imuserlogs.Collection().DeleteOne(ctx, bson.M{"_id": inserted.InsertedID})
	}()
	require.NoError(userstatus.Unban(testUserID))
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")

	// 测试高风险连接的情况
	clearRiskConnectCache := func() {
		err = service.LRURedis.Del(imuserlogs.KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
		require.NoError(err)
	}
	clearRiskConnectCache()
	err = imuserlogs.UpdateOne(bson.M{"_id": inserted.InsertedID}, bson.M{"status": imuserlogs.StatusDisableAllMsg})
	require.NoError(err)
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")

	// 测试读取缓存的情况
	err = imuserlogs.UpdateOne(bson.M{"_id": inserted.InsertedID}, bson.M{"status": imuserlogs.StatusNormal})
	require.NoError(err)
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")

	// 测试缓存失效的情况
	clearRiskConnectCache()
	err = param.checkUser()
	require.NoError(err)

	// 测试没有连接的情况
	clearRiskConnectCache()
	filter = bson.M{
		"user_id":    testUserID,
		"room_id":    testRoomID,
		"renew_time": bson.M{"$gte": now.Add(-7 * time.Minute).Unix()},
	}
	_, err = imuserlogs.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")

	// 测试阿里云风控检测不通过的情况
	cancelRPCMock = mrpc.SetMock(userapi.URLGoScanRisk, func(input any) (output any, err error) {
		return scan.BaseCheckResult{
			Score: 100,
			Pass:  true,
		}, nil
	})
	defer cancelRPCMock()
	err = param.checkUser()
	assert.EqualError(err, "福袋参与失败")
}

func TestJoinParam_checkCouponAbuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(12)
	byteParams, err := json.Marshal(params.LuckyBagConfig{
		Key:           params.KeyLuckyBag,
		HighRiskScore: 70,
	})
	require.NoError(err)
	require.NotNil(byteParams)
	key := keys.KeyParams1.Format(params.KeyLuckyBag)
	require.NoError(service.LRURedis.Set(key, byteParams, time.Minute).Err())

	param := joinParams{
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
	}
	// 测试 rpc 出错降级
	cancelRPCMock := mrpc.SetMock(userapi.URLGoScanRisk, func(input any) (output any, err error) {
		return nil, actionerrors.ErrParams
	})
	defer cancelRPCMock()
	pass := param.checkCouponAbuse()
	assert.True(pass)

	cancelRPCMock = mrpc.SetMock(userapi.URLGoScanRisk, func(input any) (output any, err error) {
		return scan.BaseCheckResult{
			Score: 80,
			Pass:  true,
		}, nil
	})
	defer cancelRPCMock()
	// 测试营销风险分数大于高风险值的情况
	pass = param.checkCouponAbuse()
	assert.False(pass)

	cancelRPCMock = mrpc.SetMock(userapi.URLGoScanRisk, func(input any) (output any, err error) {
		return scan.BaseCheckResult{
			Score: 60,
			Pass:  true,
		}, nil
	})
	defer cancelRPCMock()
	// 测试营销风险分数小于高风险值的情况
	pass = param.checkCouponAbuse()
	assert.True(pass)

	// 测试没有设置高风险值的情况
	require.NoError(service.LRURedis.Del(key).Err())
	pass = param.checkCouponAbuse()
	assert.True(pass)
}

func TestJoinParam_followCreator(t *testing.T) {
	assert := assert.New(t)

	// 测试关注自己的情况
	param := joinParams{
		user: &user.User{
			IUser: user.IUser{
				ID: 2345543,
			},
		},
		r: &room.Room{
			Helper: room.Helper{
				CreatorID: 4566546,
			},
		},
	}

	// 测试 rpc 网络出错的情况
	follow := param.followCreator()
	assert.True(follow)

	// 测试关注操作触发关注限制
	cancelErrorRPCMock := mrpc.SetMock(attentionuser.URLFollow, func(input any) (output any, err error) {
		return nil, &mrpc.ClientError{
			Code: handler.CodeUserFollowLimit,
		}
	})
	defer cancelErrorRPCMock()
	follow = param.followCreator()
	assert.False(follow)

	// 测试关注成功的情况
	cancelRPCMock := mrpc.SetMock(attentionuser.URLFollow, func(input any) (output any, err error) {
		return nil, nil
	})
	defer cancelRPCMock()
	follow = param.followCreator()
	assert.True(follow)
}

func TestJoinParam_joinLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testIP            = "***************"
		testUserID        = int64(111)
		testApplicationID = int64(4)
	)
	require.NoError(service.LiveDB.Table(luckybag.JoinRecord{}.TableName()).
		Delete("", "user_id = ?", testUserID).Error)
	err := service.LiveDB.Table(application.Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testUserID).Error
	require.NoError(err)

	param := joinParams{
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveUser: &liveuser.Simple{
			UID:          testUserID,
			Contribution: usercommon.LevelStart[20],
		},
	}
	assert.False(param.joinLimit())
	blocked, err := application.IsUserReceivePrizeBlocked(testUserID)
	require.NoError(err)
	assert.False(blocked)

	data := make([]*luckybag.JoinRecord, 0, 50)
	for i := 0; i < 50; i++ {
		data = append(data, &luckybag.JoinRecord{
			UserID:     testUserID,
			IP:         testIP,
			CreateTime: goutil.TimeNow().Unix(),
		})
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, luckybag.JoinRecord{}.TableName(), data))
	// 大于特定等级的用户不会触发限制
	assert.False(param.joinLimit())
	blocked, err = application.IsUserReceivePrizeBlocked(testUserID)
	require.NoError(err)
	assert.False(blocked)

	// 低等级用户触发限制，自动加入黑名单
	param.liveUser.Contribution = 0
	assert.True(param.joinLimit())
	blocked, err = application.IsUserReceivePrizeBlocked(testUserID)
	require.NoError(err)
	assert.True(blocked)
}

func TestJoinParam_insertRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(123)
	testLuckyBagID := int64(11)
	key := keys.KeyLuckyBagJoinNum1.Format(testLuckyBagID)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.LiveDB.Table(luckybag.JoinRecord{}.TableName()).
		Delete("", "user_id = ?", testUserID).Error)

	param := joinParams{
		user: &user.User{
			IUser: user.IUser{
				ID: testUserID,
			},
		},
		liveUser: &liveuser.Simple{
			UID: testUserID,
		},
		lb: &luckybag.InitiateRecord{
			ID: testLuckyBagID,
		},
	}
	require.NoError(param.insertRecord())

	exists, err := servicedb.Exists(service.LiveDB.Table(luckybag.JoinRecord{}.TableName()).Where("user_id = ?", testUserID))
	require.NoError(err)
	assert.True(exists)
	val, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(1, val)
}
