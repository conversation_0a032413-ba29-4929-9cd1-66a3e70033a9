package luckybag

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestActionLuckyBagFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(12)
		testRoomID = int64(1)
	)

	record := luckybag.InitiateRecord{
		UserID:        12,
		RoomID:        1,
		Type:          luckybag.TypeDrama,
		RewardType:    luckybag.RewardTypeDrama,
		Status:        0,
		Num:           1,
		TargetType:    luckybag.TargetTypeMedal,
		TransactionID: 1,
		MoreInfo: &luckybag.MoreInfo{
			StartTargetNum: util.NewInt64(0),
		},
	}
	require.NoError(record.Create())
	joinRecords := []luckybag.JoinRecord{
		{
			UserID:     12,
			LuckyBagID: record.ID,
			Status:     luckybag.StatusRisk,
		},
		{
			UserID:     1,
			LuckyBagID: record.ID,
			Status:     luckybag.StatusNormal,
		},
	}
	require.NoError(servicedb.BatchInsert(luckybag.DB(), luckybag.JoinRecord{}.TableName(), joinRecords))
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := livemedal.Collection().FindOneAndReplace(ctx, bson.M{"user_id": testUserID, "room_id": testRoomID}, livemedal.LiveMedal{
		Simple: livemedal.Simple{
			UserID: testUserID,
			RoomID: testRoomID,
			Status: livemedal.StatusOwned,
			Point:  100,
		},
	}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      9999999,
		"lucky_bag_id": record.ID,
	})
	_, _, err = ActionLuckyBagFinish(c)
	assert.EqualError(err, "不可结束当前福袋")

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      record.RoomID,
		"lucky_bag_id": record.ID,
	})
	resp, _, err := ActionLuckyBagFinish(c)
	require.NoError(err)
	res := resp.(finishResp)
	assert.Equal(record.ID, res.LuckyBag.LuckyBagID)
	assert.Equal(luckybag.StatusFinish, res.LuckyBag.Status)
	assert.EqualValues(2, res.LuckyBag.JoinNum)
	assert.EqualValues(1, *res.LuckyBag.IncreaseNum)
	require.Equal(1, len(res.LuckyBag.LuckyUsers))
	assert.EqualValues(joinRecords[1].UserID, res.LuckyBag.LuckyUsers[0].UserID)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      record.RoomID,
		"lucky_bag_id": record.ID,
	})
	_, _, err = ActionLuckyBagFinish(c)
	assert.EqualError(err, "福袋已开奖，无法操作")
}
