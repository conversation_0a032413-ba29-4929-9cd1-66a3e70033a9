package luckybag

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionDramaList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	coverURL := "https://static-test.missevan.com/icon3.png"
	iconURL := "https://static-test.missevan.com/avatars/icon01.png"

	// mock
	cancel := mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{
			RankElementType: userapi.RankElementTypeDrama,
			RankElements: []userapi.RankElementItem{
				{DramaID: 223344, IPRID: 2233}, // 223344 没有福袋
				{DramaID: 6767, IPRID: 2233},
				{DramaID: 6768, IPRID: 0},
				{DramaID: 6769, IPRID: 2234},
			},
			Bizdate: "2024-09-02",
		}, nil
	})
	defer cancel()

	// 测试有人气周榜数据获取剧集福袋列表
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybag/drama/list", false, nil)
	resp, message, err := ActionDramaList(c)
	require.NoError(err)
	assert.Equal("success", message)
	assert.NotNil(resp)
	r, ok := resp.(*utils.DramaLuckyBagListResp)
	require.True(ok)
	assert.Nil(r.HasMore) // 断言没有 has_more 字段
	except := []*utils.DramaLuckyBagInfo{
		{
			IPRID: 2233, IPRName: "IP 名称", CoverURL: coverURL, Num: 2,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
				{RoomID: 100000008, CreatorID: 3457111, CreatorIconURL: iconURL},
			},
		},
		{
			DramaID: 6768, DramaName: "广播剧福袋", CoverURL: coverURL, Num: 1,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 2234, IPRName: "IP 名称", CoverURL: coverURL, Num: 1,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 4567, IPRName: "IP 名称", CoverURL: coverURL, Num: 4,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: iconURL},
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 654, IPRName: "删除福袋", CoverURL: coverURL, Num: 3,
			Rooms: []utils.RoomInfo{
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
			},
		},
	}
	assert.Equal(except, r.Data)

	// mock
	cancel = mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{}, nil
	})
	defer cancel()

	// 测试没有人气周榜数据获取剧集福袋列表
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybag/drama/list", false, nil)
	resp, message, err = ActionDramaList(c)
	require.NoError(err)
	assert.Equal("success", message)
	assert.NotNil(resp)
	r, ok = resp.(*utils.DramaLuckyBagListResp)
	require.True(ok)
	assert.Nil(r.HasMore)
	except = []*utils.DramaLuckyBagInfo{
		{
			IPRID: 2234, IPRName: "IP 名称", CoverURL: coverURL, Num: 1,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: iconURL},
			},
		},
		{
			DramaID: 6768, DramaName: "广播剧福袋", CoverURL: coverURL, Num: 1,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 2233, IPRName: "IP 名称", CoverURL: coverURL, Num: 2,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
				{RoomID: 100000008, CreatorID: 3457111, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 4567, IPRName: "IP 名称", CoverURL: coverURL, Num: 4,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: iconURL},
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
			},
		},
		{
			IPRID: 654, IPRName: "删除福袋", CoverURL: coverURL, Num: 3,
			Rooms: []utils.RoomInfo{
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
				{RoomID: 18113499, CreatorID: 3457181, CreatorIconURL: iconURL},
			},
		},
	}
	assert.Equal(except, r.Data)
}
