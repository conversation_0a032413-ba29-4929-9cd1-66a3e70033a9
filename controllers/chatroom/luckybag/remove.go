package luckybag

import (
	"errors"
	"fmt"
	"html"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

type luckyBagRemoveParam struct {
	LuckyBagID int64  `form:"lucky_bag_id" json:"lucky_bag_id"`
	Reason     string `form:"reason" json:"reason"`
	Confirm    int    `form:"confirm" json:"confirm"`

	c        *handler.Context
	luckyBag *luckybag.InitiateRecord
}

type luckyBagRoomMessage struct {
	Type     string        `json:"type"`
	Event    string        `json:"event"`
	RoomID   int64         `json:"room_id"`
	LuckyBag *luckyBagInfo `json:"lucky_bag"`
	Message  string        `json:"message"`
}

type luckyBagInfo struct {
	LuckyBagID int64 `json:"lucky_bag_id"`
	HasMore    bool  `json:"has_more"`
}

// ActionAdminRemove 超管删除福袋
/**
 * @api {post} /api/v2/admin/luckybag/remove 超管删除福袋
 * @apiDescription 仅能删除待开奖的福袋
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} lucky_bag_id 福袋 ID
 * @apiParam {String} reason 原因
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求不传
 *
 * @apiSuccessExample confirm 弹窗:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认要删除福袋吗？<br>福袋 ID: 12<br>主播昵称：test_user<br>奖品名称：删除福袋<br>奖品数量：10<br>口令：口令 1<br><font color=\"red\">删除原因：福袋口令内容含有违规内容</font>"
 *     }
 *   }
 *
 * @apiSuccessExample 响应:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample websocket 消息:
 *   {
 *     "type": "lucky_bag",
 *     "event": "remove",
 *     "room_id": 123456,
 *     "lucky_bag": {
 *       "lucky_bag_id": 123, // 客户端和网页端处理时需要验证房间和福袋是否正确
 *       "has_more": false // 是否显示更多福袋入口: true: 显示; false: 隐藏
 *     },
 *     "message": "本直播间的喵喵福袋经审核发现违规，现已被删除"
 *   }
 */
func ActionAdminRemove(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAdminLuckyBagRemoveParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.delete()
	if err != nil {
		return nil, "", err
	}
	return nil, "删除成功", nil
}

func newAdminLuckyBagRemoveParam(c *handler.Context) (*luckyBagRemoveParam, error) {
	param := new(luckyBagRemoveParam)
	err := c.Bind(&param)
	if err != nil || param.LuckyBagID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.Reason = strings.TrimSpace(param.Reason)
	if param.Reason == "" {
		return nil, actionerrors.ErrParamsMsg("请输入删除原因")
	}
	param.c = c
	return param, nil
}

func (param *luckyBagRemoveParam) check() error {
	var err error
	param.luckyBag, err = luckybag.FindShowingInitiateRecordByID(param.LuckyBagID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.luckyBag == nil {
		return actionerrors.ErrNotFound("福袋不存在或已删除")
	}
	if luckybag.StatusPending != param.luckyBag.Status {
		return actionerrors.NewErrForbidden("只能删除进行中的福袋")
	}
	if param.Confirm == 0 {
		user, err := mowangskuser.FindByUserID(param.luckyBag.CreatorID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if user == nil {
			return actionerrors.ErrCannotFindUser
		}

		msg := fmt.Sprintf("确认要删除福袋吗？<br>"+
			"福袋 ID: %d<br>主播昵称：%s<br>"+
			"奖品名称：%s<br>奖品数量：%d<br>"+
			"口令：%s<br>"+
			`<font color="red">删除原因：%s</font>`,
			param.LuckyBagID, html.EscapeString(user.Username),
			html.EscapeString(param.luckyBag.Name), param.luckyBag.Num,
			html.EscapeString(param.luckyBag.Message), html.EscapeString(param.Reason))
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return nil
}

var errDeleteConflict = errors.New("delete conflict")

func (param *luckyBagRemoveParam) delete() error {
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		// 删除福袋
		ok, err := luckybag.DeletePendingLuckyBag(tx, param.LuckyBagID, param.Reason)
		if err != nil {
			return err
		}
		if !ok {
			return errDeleteConflict
		}
		return nil
	})
	if err != nil {
		if errors.Is(err, errDeleteConflict) {
			return actionerrors.ErrNotFound("福袋不存在或已删除")
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 退款
	err = luckybag.Refund(param.c.UserContext(), param.luckyBag, param.luckyBag.Num)
	if err != nil {
		logger.WithField("lucky_bag_id", param.LuckyBagID).Error(err)
		// PASS
	}

	param.sendSystemMsg()
	param.sendRoomMsg()

	logbox := userapi.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("删除福袋：%d；主播M号：%d；删除原因：%s", param.LuckyBagID, param.luckyBag.CreatorID, param.Reason)
	logbox.AddAdminLog(intro, userapi.CatalogManageLuckyBag)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}

func (param luckyBagRemoveParam) sendSystemMsg() {
	var typeName string
	if luckybag.TypeDrama == param.luckyBag.Type {
		typeName = "广播剧"
	} else {
		typeName = "实物"
	}
	content := fmt.Sprintf(`你在 %s 发放的%s福袋（福袋 ID：<a href="copy:%d">%d</a>）经审核发现违规，现已被删除，删除原因：%s。请注意福袋使用规范，如有疑问可联系客服咨询。`,
		time.Unix(param.luckyBag.CreateTime, 0).Format(util.TimeFormatYMDHMS),
		typeName,
		param.LuckyBagID,
		param.LuckyBagID,
		html.EscapeString(param.Reason))

	sysMsg := []pushservice.SystemMsg{
		{
			UserID:  param.luckyBag.CreatorID,
			Title:   "喵喵福袋违规删除通知",
			Content: content,
		},
	}
	err := service.PushService.SendSystemMsgWithOptions(sysMsg, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Errorf("删除福袋系统通知发送失败: %v", err)
		// PASS
	}
}

func (param luckyBagRemoveParam) sendRoomMsg() {
	payload := luckyBagRoomMessage{
		Type:   liveim.TypeLuckyBag,
		Event:  liveim.EventLuckyBagRemove,
		RoomID: param.luckyBag.RoomID,
		LuckyBag: &luckyBagInfo{
			LuckyBagID: param.LuckyBagID,
			HasMore:    false,
		},
		Message: "本直播间的喵喵福袋经审核发现违规，现已被删除",
	}
	err := userapi.Broadcast(param.luckyBag.RoomID, payload)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
