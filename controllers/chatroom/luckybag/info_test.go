package luckybag

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestNewLuckyBagInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom, err := room.Find(room.TestExistsRoomID)
	require.NoError(err)
	require.NotNil(testRoom)

	err = luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("room_id = ?", testRoom.RoomID).Delete("").Error
	require.NoError(err)

	luckyBag := luckybag.InitiateRecord{
		UserID:    testRoom.CreatorID,
		RoomID:    testRoom.RoomID,
		CreatorID: testRoom.CreatorID,
		Type:      luckybag.TypeEntity,
	}
	require.NoError(luckyBag.Create())

	newURL := func(roomID, luckyBagID int64) string {
		return fmt.Sprintf("luckybag/info?room_id=%d&lucky_bag_id=%d", roomID, luckyBagID)
	}
	c := handler.NewTestContext(http.MethodGet, newURL(123, 0), false, nil)
	_, err = newLuckyBagInfoParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = handler.NewTestContext(http.MethodGet, newURL(room.TestExistsRoomID, 999), false, nil)
	_, err = newLuckyBagInfoParam(c)
	assert.Equal(actionerrors.ErrNotFound("福袋不存在"), err)

	c = handler.NewTestContext(http.MethodGet, newURL(room.TestExistsRoomID, 1), false, nil)
	_, err = newLuckyBagInfoParam(c)
	assert.Equal(actionerrors.NewErrForbidden("福袋不属于当前房间"), err)

	c = handler.NewTestContext(http.MethodGet, newURL(room.TestExistsRoomID, luckyBag.ID),
		false, nil)
	param, err := newLuckyBagInfoParam(c)
	require.NoError(err)
	assert.NotNil(param.r)
	assert.NotNil(param.luckyBag)
}

func TestLuckyBagInfoParam_creatorResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := luckyBagInfoParam{
		luckyBag: &luckybag.InitiateRecord{
			Status: luckybag.StatusFinish,
			MoreInfo: &luckybag.MoreInfo{
				StartTargetNum: util.NewInt64(0),
				EndTargetNum:   util.NewInt64(10),
			},
		},
	}
	r, _, err := param.creatorResp()
	require.NoError(err)
	resp, ok := r.(luckyBagInfoResp)
	require.True(ok)
	assert.NotNil(resp.LuckyBag.JoinNum)
}

func TestLuckyBagInfoParam_listenerResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var joinRecord luckybag.JoinRecord
	err := luckybag.DB().Find(&joinRecord).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/", false, nil)
	param := luckyBagInfoParam{
		c: c,
		luckyBag: &luckybag.InitiateRecord{
			ID:         joinRecord.LuckyBagID,
			TargetType: luckybag.TargetTypeMedal,
		},
		r: new(room.Room),
	}
	r, _, err := param.listenerResp()
	require.NoError(err)
	resp, ok := r.(*luckyBagInfoResp)
	require.True(ok)
	assert.NotNil(resp.MedalGift)
	require.NotNil(resp.LuckyBag.JoinStatus)
	assert.Zero(*resp.LuckyBag.JoinStatus)

	param.userID = 99
	r, _, err = param.listenerResp()
	require.NoError(err)
	resp, ok = r.(*luckyBagInfoResp)
	require.True(ok)
	assert.NotNil(resp.MedalGift)
	require.NotNil(resp.LuckyBag.JoinStatus)
	assert.Zero(*resp.LuckyBag.JoinStatus)

	param.userID = joinRecord.UserID
	r, _, err = param.listenerResp()
	require.NoError(err)
	resp, ok = r.(*luckyBagInfoResp)
	require.True(ok)
	assert.NotNil(resp.MedalGift)
	require.NotNil(resp.LuckyBag.JoinStatus)
	assert.Equal(1, *resp.LuckyBag.JoinStatus)
}

func TestLuckyBagInfoParam_findIncreaseNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.Find(room.TestExistsRoomID)
	require.NoError(err)
	// 不需要查询
	param := luckyBagInfoParam{
		luckyBag: &luckybag.InitiateRecord{
			Status:     luckybag.StatusFinish,
			TargetType: luckybag.TargetTypeAll,
		},
		r: r,
	}
	assert.Nil(param.findIncreaseNum())
	// more 异常
	param.luckyBag.TargetType = luckybag.TargetTypeFollow
	assert.Nil(param.findIncreaseNum())
	// 从入库数据查询
	param.luckyBag.MoreInfo = &luckybag.MoreInfo{
		StartTargetNum: util.NewInt64(0),
		EndTargetNum:   util.NewInt64(10),
	}
	num := param.findIncreaseNum()
	require.NotNil(num)
	assert.EqualValues(10, *num)
	// 实时关注
	param.luckyBag.Status = luckybag.StatusPending
	assert.NotNil(param.findIncreaseNum())
	// 实时粉丝勋章
	param.luckyBag.TargetType = luckybag.TargetTypeMedal
	assert.NotNil(param.findIncreaseNum())
	// 实时超粉
	param.luckyBag.TargetType = luckybag.TargetTypeSuperFan
	assert.NotNil(param.findIncreaseNum())
}

func TestLuckyBagInfoParam_findLuckyUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := luckyBagInfoParam{
		luckyBag: &luckybag.InitiateRecord{
			ID:     1,
			Status: luckybag.StatusPending,
		},
	}
	luckyusers, err := param.findLuckyUsers()
	require.NoError(err)
	assert.Empty(luckyusers)
	param.luckyBag.Status = luckybag.StatusFinish
	luckyusers, err = param.findLuckyUsers()
	require.NoError(err)
	assert.NotEmpty(luckyusers)
}

func TestActionLuckyBagInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom, err := room.Find(room.TestExistsRoomID)
	require.NoError(err)
	require.NotNil(testRoom)

	err = luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("room_id = ?", testRoom.RoomID).Delete("").Error
	require.NoError(err)

	luckyBag := luckybag.InitiateRecord{
		UserID:     testRoom.CreatorID,
		RoomID:     testRoom.RoomID,
		CreatorID:  testRoom.CreatorID,
		Type:       luckybag.TypeEntity,
		TargetType: luckybag.TargetTypeMedal,
	}
	require.NoError(luckyBag.Create())

	url := fmt.Sprintf("luckybag/info?room_id=%d&lucky_bag_id=%d",
		testRoom.RoomID, luckyBag.ID)
	c := handler.NewTestContext(http.MethodGet, url, true, nil)
	c.User().ID = testRoom.CreatorID
	r, _, err := ActionLuckyBagInfo(c)
	require.NoError(err)
	resp := r.(*luckyBagInfoResp)
	assert.Nil(resp.MedalGift)

	url = fmt.Sprintf("luckybag/info?room_id=%d&lucky_bag_id=%d",
		testRoom.RoomID, luckyBag.ID)
	c = handler.NewTestContext(http.MethodGet, url, true, nil)
	c.User().ID = testRoom.CreatorID + 1
	r, _, err = ActionLuckyBagInfo(c)
	require.NoError(err)
	resp = r.(*luckyBagInfoResp)
	assert.NotNil(resp.MedalGift)
}
