package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionDramaList 获取广播剧福袋列表
/**
 * @api {get} /api/v2/chatroom/luckybag/drama/list 获取广播剧福袋列表
 * @apiDescription 当福袋所属剧集有 IPR ID 时，通过 IPR 来聚合，当福袋所属剧集没有 IPR ID 时，通过剧集来聚合
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "data": [ // 没有数据时返回空数组
 *         {
 *           "ipr_id": 2333, // 剧集所属 IPR ID，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
 *           "ipr_name": "魔道祖师", // 剧集所属 IPR 名，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
 *           "drama_id": 2334, // 剧集 ID，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
 *           "drama_name": "烟火", // 剧集名称，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
 *           "cover_url": "https://static-test.maoercdn.com/cover.png", // 剧集封面
 *           "num": 5, // 当前正在发放此剧集福袋的直播间数量
 *           "rooms": [ // 排名前三的直播间信息
 *             {
 *               "room_id": 100000, // 直播间 ID
 *               "creator_id": 11, // 主播 ID
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png" // 主播头像
 *             },
 *             {
 *               "room_id": 100001,
 *               "creator_id": 12,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             },
 *             {
 *               "room_id": 100002,
 *               "creator_id": 13,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *   }
 */
func ActionDramaList(c *handler.Context) (handler.ActionResponse, string, error) {
	var param utils.DramaLuckyBagListData
	resp, err := param.ListAll(c.UserContext())
	if err != nil {
		return nil, "", err
	}
	return resp, "success", nil
}
