package luckybag

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRoomList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybag/room/list?type=1&room_id=10", true, nil)
	resp, message, err := ActionRoomList(c)
	require.NoError(err)
	assert.Equal("success", message)
	assert.Nil(resp)
}

func TestRoomListParam_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := new(roomListParam)
	err := param.load(handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybag/room/list?type=2", true, nil))
	assert.Equal(actionerrors.ErrParams, err)

	param = new(roomListParam)
	err = param.load(handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybag/room/list?type=1&room_id=10&marker=1&page_size=20", true, nil))
	require.NoError(err)
	assert.Equal(int64(12), param.userID)
	assert.Equal(int64(20), param.pageSize)
	assert.Equal(int64(1), param.marker.PageIndex)
}

func TestRoomListParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)

	// 删除测试数据
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("room_id IN (?)", []int64{20, 30, 40}).Delete("").Error)

	// 测试没有福袋房间列表
	param := &roomListParam{
		Type:   luckybag.TypeDrama,
		RoomID: 99,
		userID: userID,
	}
	resp, message, err := param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.Nil(resp)

	// mock 时间，保证福袋发起时间
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	moreInfo := &luckybag.MoreInfo{
		PrizeIPRName: "名称",
	}
	// 创建福袋发起记录
	record := luckybag.InitiateRecord{
		CreatorID:    userID,
		RoomID:       20,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   1,
		PrizeDramaID: 2,
		Name:         "剧集福袋",
		Num:          1,
		JoinNum:      99,
		MoreInfo:     moreInfo,
	}
	require.NoError(record.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 11, 0, 0, 0, time.Local)
	})
	record2 := luckybag.InitiateRecord{
		CreatorID:    12345,
		RoomID:       30,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   1,
		PrizeDramaID: 2,
		Name:         "剧集福袋",
		Num:          3,
		JoinNum:      98,
		MoreInfo:     moreInfo,
	}
	require.NoError(record2.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 10, 0, 0, 0, time.Local)
	})
	record3 := luckybag.InitiateRecord{
		CreatorID:    10,
		RoomID:       40,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   1,
		PrizeDramaID: 2,
		Name:         "剧集福袋",
		Num:          2,
		JoinNum:      97,
		MoreInfo:     moreInfo,
	}
	require.NoError(record3.Create())

	// 创建的测试数据查询后的排序顺序为 record2,record3,record
	// 测试请求第一页
	param.pageSize = 2
	param.RoomID = record3.RoomID
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.Equal("《名称》广播剧福袋", resp.Title)
	require.Equal(util.NewInt64(record3.PrizeDramaID), resp.PrizeDramaID)
	require.Equal(util.NewInt64(record3.PrizeIPRID), resp.PrizeIPRID)
	require.NotEmpty(resp.Data)
	assert.True(resp.Pagination.HasMore)
	assert.Equal("1", resp.Pagination.Marker)
	assert.EqualValues(3, *resp.Pagination.Count)
	// 测试第一页包含当前房间，将其移到开头
	// 未开奖的记录的 joinNum 存在 redis 中，此处测试未设置，数量为 0. TestBuildJoinNum 已测
	except := []*roomInfo{
		{
			RoomID:          record3.RoomID, // 断言房间号为 40 在列表第一条
			CreatorID:       record3.CreatorID,
			CreatorUsername: "bless",
			CreatorIconURL:  "https://static-test.missevan.com/profile/201704/07/9b3529a08130e74da1dcd8b53feb50c5155007.png",
			RewardType:      luckybag.RewardTypeDrama,
			PrizeName:       "剧集福袋",
			PrizeNum:        record3.Num,
			PrizeIPRID:      util.NewInt64(record3.PrizeIPRID),
			PrizeDramaID:    util.NewInt64(record3.PrizeDramaID),
			Active:          true,
			luckyBagID:      record3.ID,
		},
		{
			RoomID:          record2.RoomID,
			CreatorID:       record2.CreatorID,
			CreatorUsername: "测试首页推荐",
			CreatorIconURL:  "https://static-test.missevan.com/avatars/icon01.png",
			RewardType:      luckybag.RewardTypeDrama,
			PrizeName:       "剧集福袋",
			PrizeNum:        record2.Num,
			PrizeIPRID:      util.NewInt64(record2.PrizeIPRID),
			PrizeDramaID:    util.NewInt64(record2.PrizeDramaID),
			luckyBagID:      record2.ID,
		},
	}
	assert.Equal(except, resp.Data)

	// 请求第二页
	param.pageSize = 2
	param.marker = &luckybag.MarkerListOption{PageIndex: 1}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.False(resp.Pagination.HasMore)
	assert.Equal("", resp.Pagination.Marker)
	assert.Zero(resp.Pagination.Count)

	except = []*roomInfo{
		{
			RoomID:          record.RoomID,
			CreatorID:       userID,
			CreatorUsername: "零月",
			CreatorIconURL:  "https://static-test.missevan.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png",
			RewardType:      luckybag.RewardTypeDrama,
			PrizeName:       "剧集福袋",
			PrizeNum:        record.Num,
			PrizeIPRID:      util.NewInt64(record.PrizeIPRID),
			PrizeDramaID:    util.NewInt64(record.PrizeDramaID),
			luckyBagID:      record.ID,
		},
	}
	assert.Equal(except, resp.Data)

	// 测试无分页
	param = &roomListParam{
		Type:     luckybag.TypeDrama,
		RoomID:   40,
		pageSize: 3,
	}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)

	assert.False(resp.Pagination.HasMore)
	assert.Equal("", resp.Pagination.Marker)
	assert.EqualValues(3, *resp.Pagination.Count)

	// 更新福袋为已结束
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("id = ? AND status = ?", record3.ID, luckybag.StatusPending).Updates(map[string]interface{}{
		"modified_time": goutil.TimeNow().Unix(),
		"status":        luckybag.StatusFinish,
	}).Error)

	// 测试请求第一页
	param.pageSize = 1
	param.RoomID = 40
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.Equal("《名称》广播剧福袋", resp.Title)
	require.NotEmpty(resp.Data)
	assert.True(resp.Pagination.HasMore)
	assert.Equal("1", resp.Pagination.Marker)
	assert.EqualValues(2, *resp.Pagination.Count)
	// 断言已结束的房间不在列表中
	except = []*roomInfo{
		{
			RoomID:          record2.RoomID,
			CreatorID:       12345,
			CreatorUsername: "测试首页推荐",
			CreatorIconURL:  "https://static-test.missevan.com/avatars/icon01.png",
			RewardType:      luckybag.RewardTypeDrama,
			PrizeName:       "剧集福袋",
			PrizeNum:        record2.Num,
			PrizeIPRID:      util.NewInt64(record2.PrizeIPRID),
			PrizeDramaID:    util.NewInt64(record2.PrizeDramaID),
			luckyBagID:      record2.ID,
		},
	}
	assert.Equal(except, resp.Data)

	// 请求第二页
	param.pageSize = 1
	param.marker = &luckybag.MarkerListOption{PageIndex: 1}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.False(resp.Pagination.HasMore)
	assert.Equal("", resp.Pagination.Marker)
	assert.Zero(resp.Pagination.Count)
	except = []*roomInfo{
		{
			RoomID:          record.RoomID,
			CreatorID:       userID,
			CreatorUsername: "零月",
			CreatorIconURL:  "https://static-test.missevan.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png",
			RewardType:      luckybag.RewardTypeDrama,
			PrizeName:       "剧集福袋",
			PrizeNum:        record.Num,
			PrizeIPRID:      util.NewInt64(record.PrizeIPRID),
			PrizeDramaID:    util.NewInt64(record.PrizeDramaID),
			luckyBagID:      record.ID,
		},
	}
	assert.Equal(except, resp.Data)
}

func TestBuildJoinNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res := []*roomInfo{
		{
			RoomID:     1,
			luckyBagID: 1,
		},
		{
			RoomID:     2,
			luckyBagID: 2,
		},
		{
			RoomID:     3,
			luckyBagID: 3,
		},
	}
	pipe := service.Redis.TxPipeline()
	pipe.Del(keys.KeyLuckyBagJoinNum1.Format(res[0].luckyBagID),
		keys.KeyLuckyBagJoinNum1.Format(res[1].luckyBagID),
		keys.KeyLuckyBagJoinNum1.Format(res[2].luckyBagID))
	pipe.Set(keys.KeyLuckyBagJoinNum1.Format(res[0].luckyBagID), 1, time.Minute)
	pipe.Set(keys.KeyLuckyBagJoinNum1.Format(res[2].luckyBagID), 100, time.Minute)
	_, err := pipe.Exec()
	require.NoError(err)

	assert.NotPanics(func() { buildJoinNum(res) })
	assert.EqualValues(1, res[0].JoinNum)
	assert.Zero(res[1].JoinNum)
	assert.EqualValues(100, res[2].JoinNum)
}
