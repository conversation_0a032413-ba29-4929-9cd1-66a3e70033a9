package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	luckybagutil "github.com/MiaoSiLa/live-service/models/livedb/luckybag/util"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type finishParams struct {
	RoomID     int64 `form:"room_id" json:"room_id"`
	LuckyBagID int64 `form:"lucky_bag_id" json:"lucky_bag_id"`
}

type finishResp struct {
	LuckyBag *luckybag.FullInfo `json:"lucky_bag"`
}

// ActionLuckyBagFinish 提前结束福袋
/**
 * @api {post} /api/v2/chatroom/luckybag/finish 提前结束福袋
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} lucky_bag_id 福袋 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "开奖成功！",
 *     "data": {
 *       "lucky_bag": {
 *         "lucky_bag_id": 1,
 *         "status": 0, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *         "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *         "reward_type": 2, // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *         "prize_name": "签名照", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *         "prize_num": 1, // 奖品数量
 *         "prize_price": 100, // 福袋每份价值，只有剧集福袋有价格，单位：钻
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图
 *         "target_type": 0, // 参与对象类型, 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 *         "remain_duration": 1000, // 剩余时间（毫秒）
 *         "keyword": "点点关注抽福袋", // 参与默认口令
 *         "join_num": 10, // 参与人数
 *         "increase_num": 10, // 新增人数
 *         "lucky_users": [ // 中奖用户
 *           {
 *             "user_id": 1223,
 *             "username": "userA"
 *           },
 *           {
 *             "user_id": 1224,
 *             "username": "userB"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 福袋开奖消息:
 *   {
 *     "type": "lucky_bag", // 主播收到该消息后，请求 /api/v2/chatroom/luckybag/info 获取福袋参与人数和新增关注、粉丝勋章或超粉数量等完整信息
 *     "event": "finish", // 客户端收到结束消息后，根据本地维护的用户参与状态决定是否弹窗
 *     "room_id": 123456,
 *     "lucky_bag": {
 *       "lucky_bag_id": 1, // 福袋 ID
 *       "status": 2, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *       "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *       "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *       "prize_name": "广播剧 A", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *       "prize_num": 1, // 奖品数量
 *       "prize_price": 1000, // 福袋每份价值（钻），单位：钻
 *       "prize_icon_url": "https://static-test.maoercdn.com/dramacovers/202208/15/5793676f2056184417.jpg", // 封面图
 *       "remain_duration": 1000, // 公示剩余时间，单位：毫秒
 *       "lucky_users": [ // 客户端需要根据中奖用户判断显示当前用户是否中奖
 *         {
 *           "user_id": 1223,
 *           "username": "userA"
 *         },
 *         {
 *           "user_id": 1224,
 *           "username": "userB"
 *         }
 *       ],
 *       "image_url": "https://static-test.maoercdn.com/luckybag.png" // 福袋图标
 *     }
 *   }
 *
 */
func ActionLuckyBagFinish(c *handler.Context) (handler.ActionResponse, string, error) {
	var param finishParams
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.LuckyBagID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	lb, err := luckybag.FindShowingInitiateRecordByID(param.LuckyBagID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if lb == nil {
		return nil, "", actionerrors.ErrCannotFindResource
	}
	if lb.Status == luckybag.StatusDrawing || lb.Status == luckybag.StatusFinish {
		return nil, "", actionerrors.NewErrLiveForbidden("福袋已开奖，无法操作")
	}
	if lb.Status != luckybag.StatusPending {
		return nil, "", actionerrors.NewErrLiveForbidden("福袋已结束，无法操作")
	}
	if lb.UserID != c.UserID() || lb.RoomID != param.RoomID {
		return nil, "", actionerrors.NewErrLiveForbidden("不可结束当前福袋")
	}

	luckyUsers, err := luckybagutil.DrawLuckyBag(lb)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	conf, err := params.FindLuckyBag()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	info := luckybag.NewFullInfo(lb)
	info.LuckyUsers = luckyUsers
	info.RemainDuration = goutil.SecondOneMinute * 1000 // 结束后一分钟公示期
	info.ImageURL = conf.ImageURL
	if lb.MoreInfo != nil {
		info.IncreaseNum = goutil.NewInt64(luckybag.CalculateIncreaseNum(lb.MoreInfo))
	}
	return finishResp{LuckyBag: info}, "开奖成功！", nil
}
