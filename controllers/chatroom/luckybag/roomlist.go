package luckybag

import (
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomListParam struct {
	Type   int   `form:"type"`
	RoomID int64 `form:"room_id"`

	marker   *luckybag.MarkerListOption
	pageSize int64
	userID   int64
}

type roomInfo struct {
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl"`
	RewardType      int    `json:"reward_type"`
	PrizeName       string `json:"prize_name"`
	PrizeNum        int    `json:"prize_num"`
	PrizeIPRID      *int64 `json:"prize_ipr_id,omitempty"`
	PrizeDramaID    *int64 `json:"prize_drama_id,omitempty"`
	JoinNum         int64  `json:"join_num"`
	Active          bool   `json:"active,omitempty"`

	luckyBagID int64
}

// markerPaginationWithCount 无页数信息进行分页，maker 内容由各业务自行定义
type markerPaginationWithCount struct {
	Count                   *int64 `json:"count,omitempty"`
	goutil.MarkerPagination `json:",inline"`
}

// roomListResp 福袋房间列表
type roomListResp struct {
	Title        string                    `json:"title"`
	PrizeIPRID   *int64                    `json:"prize_ipr_id,omitempty"`
	PrizeDramaID *int64                    `json:"prize_drama_id,omitempty"`
	Data         []*roomInfo               `json:"data"`
	Pagination   markerPaginationWithCount `json:"pagination"`
}

// ActionRoomList 获取福袋房间列表
/**
 * @api {get} /api/v2/chatroom/luckybag/room/list 获取福袋房间列表
 * @apiDescription 仅在有更多福袋的情况下获取，会把传入的房间插入到列表第一个，客户端需要对响应的结果去重 \
 * 当直播间福袋结束后，用户一直待在直播间内，访问更多福袋列表，过滤当前直播间，返回数据
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} type 类型 1：广播剧福袋
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求返回前一次响应的请求中的 marker
 * @apiParam {Number} [page_size=20] 每页数量
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": { // 没有数据时返回 null
 *       "title": "《名称》广播剧福袋",
 *       "prize_ipr_id": 123, // 当前房间的 IPR ID, 为 0 或不存在代表没有 IPR
 *       "prize_drama_id": 456, // 当前房间的 drama ID
 *       "data": [
 *         {
 *           "room_id": 100000, // 直播间 ID
 *           "creator_id": 11, // 主播 ID
 *           "creator_username": "主播昵称", // 主播昵称
 *           "creator_iconurl": "https://static-test.maoercdn.com/icon01.png", // 主播头像
 *           "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *           "prize_name": "魔道祖师第一季", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理添加书名号
 *           "prize_num": 3, // 奖品数量
 *           "prize_ipr_id": 123, // IPR ID, 为 0 或不存在代表没有 IPR
 *           "prize_drama_id": 148,
 *           "join_num": 99, // 参与人数
 *           "active": true // 是否被高亮，不高亮时不返回此字段
 *         },
 *         {
 *           "room_id": 100001,
 *           "creator_id": 12,
 *           "creator_username": "主播昵称",
 *           "creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
 *           "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *           "prize_name": "魔道祖师第一季",
 *           "prize_num": 3,
 *           "prize_ipr_id": 123, // IPR ID, 为 0 或不存在代表没有 IPR
 *           "prize_drama_id": 148,
 *           "join_num": 99
 *         },
 *         {
 *           "room_id": 100002,
 *           "creator_id": 13,
 *           "creator_username": "主播昵称",
 *           "creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
 *           "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *           "prize_name": "魔道祖师第一季",
 *           "prize_num": 3,
 *           "prize_ipr_id": 123, // IPR ID, 为 0 或不存在代表没有 IPR
 *           "prize_drama_id": 148,
 *           "join_num": 99
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否有更多数据
 *         "marker": "1", // 当前的 marker，需要加载下一页时回传
 *         "count": 22 // 数据总量，只在获取第一页时返回总数
 *       }
 *     }
 *   }
 */
func ActionRoomList(c *handler.Context) (handler.ActionResponse, string, error) {
	var param roomListParam
	if err := param.load(c); err != nil {
		return nil, "", err
	}
	return param.resp()
}

// load 参数处理
func (param *roomListParam) load(c *handler.Context) (err error) {
	param.Type, _ = c.GetParamInt("type")
	param.RoomID, _ = c.GetParamInt64("room_id")
	if param.Type != luckybag.TypeDrama || param.RoomID <= 0 {
		return actionerrors.ErrParams
	}

	marker, pageSize, err := c.GetParamMarker()
	if err != nil {
		return actionerrors.ErrParams
	}

	if marker != "" {
		markerIndex, err := strconv.ParseInt(marker, 10, 64)
		if err != nil {
			return actionerrors.ErrParams
		}
		if markerIndex > 0 {
			param.marker = &luckybag.MarkerListOption{PageIndex: markerIndex}
		}
	}
	param.pageSize = pageSize
	param.userID = c.UserID()
	return nil
}

func (param *roomListParam) resp() (*roomListResp, string, error) {
	// 获取当前房间的福袋发起记录
	// param.Type 只支持广播剧福袋
	initiateRecord, err := luckybag.FindLatestInitiateRecordByRoomdID(param.Type, param.RoomID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if initiateRecord == nil {
		// 没有找到数据返回空
		return nil, "success", nil
	}

	initiates, nextMarker, err := luckybag.ListPendingInitiateRecordBy(initiateRecord, param.pageSize, param.marker)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if initiates == nil {
		// 没有找到数据返回空
		return nil, "success", nil
	}

	// 获取福袋发起记录的主播 IDs
	creatorIDs := make([]int64, len(initiates))
	for i, initiate := range initiates {
		creatorIDs[i] = initiate.CreatorID
	}

	// 查询主播信息
	userMap, err := mowangskuser.FindSimpleMap(creatorIDs) // 函数内部已去重
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &roomListResp{
		Data: make([]*roomInfo, 0, len(initiates)+1), // 预分配空间 +1 用于插入置顶房间
	}
	if initiateRecord.Type == luckybag.TypeDrama {
		resp.PrizeDramaID = util.NewInt64(initiateRecord.PrizeDramaID)
		if initiateRecord.PrizeIPRID != 0 {
			resp.PrizeIPRID = util.NewInt64(initiateRecord.PrizeIPRID)
		}
	}

	var name string
	if initiateRecord.PrizeIPRID > 0 {
		if initiateRecord.MoreInfo == nil || initiateRecord.MoreInfo.PrizeIPRName == "" {
			logger.WithField("lucky_bag_id", initiateRecord.ID).Error("福袋 more 异常")
			// 降级使用广播剧的名称
			name = initiateRecord.Name
		} else {
			name = initiateRecord.MoreInfo.PrizeIPRName
		}
	} else {
		name = initiateRecord.Name
	}
	resp.Title = fmt.Sprintf("《%s》广播剧福袋", name)

	var currentRoomInfo *roomInfo
	for _, initiate := range initiates {
		record := &roomInfo{
			RoomID:     initiate.RoomID,
			CreatorID:  initiate.CreatorID,
			RewardType: initiate.RewardType,
			PrizeName:  initiate.Name,
			PrizeNum:   initiate.Num,
			luckyBagID: initiate.ID,
		}
		if initiate.Type == luckybag.TypeDrama {
			record.PrizeDramaID = util.NewInt64(initiate.PrizeDramaID)
			if initiate.PrizeIPRID != 0 {
				record.PrizeIPRID = util.NewInt64(initiate.PrizeIPRID)
			}
		}
		if u := userMap[initiate.CreatorID]; u != nil {
			record.CreatorUsername = u.Username
			record.CreatorIconURL = u.IconURL
		}
		if initiate.RoomID == param.RoomID {
			record.Active = true
			currentRoomInfo = record
			// 当前房间在第一页的时候需要挪到第一位，翻页时需要过滤掉
			continue
		}
		resp.Data = append(resp.Data, record)
	}

	// 第一页且福袋未结束
	if param.marker == nil && luckybag.StatusPending == initiateRecord.Status {
		if currentRoomInfo == nil {
			// 如果第一页不包含当前房间，把最开始查询的未结束的房间插入第一位
			currentRoomInfo = &roomInfo{
				RoomID:     initiateRecord.RoomID,
				CreatorID:  initiateRecord.CreatorID,
				RewardType: initiateRecord.RewardType,
				PrizeName:  initiateRecord.Name,
				PrizeNum:   initiateRecord.Num,
				Active:     true,
				luckyBagID: initiateRecord.ID,
			}
			if initiateRecord.Type == luckybag.TypeDrama {
				currentRoomInfo.PrizeDramaID = util.NewInt64(initiateRecord.PrizeDramaID)
				if initiateRecord.PrizeIPRID != 0 {
					currentRoomInfo.PrizeIPRID = util.NewInt64(initiateRecord.PrizeIPRID)
				}
			}
			if u := userMap[initiateRecord.CreatorID]; u != nil {
				currentRoomInfo.CreatorUsername = u.Username
				currentRoomInfo.CreatorIconURL = u.IconURL
			}
		}
		resp.Data = append(resp.Data, nil)
		copy(resp.Data[1:len(resp.Data)], resp.Data[0:len(resp.Data)-1])
		resp.Data[0] = currentRoomInfo
	}
	// 处理福袋 join num
	buildJoinNum(resp.Data)

	if nextMarker != nil {
		resp.Pagination.HasMore = true
		resp.Pagination.Marker = nextMarker.Encode()
	}
	if param.marker == nil {
		var count int64
		if !resp.Pagination.HasMore {
			// 在第一页已全部查出来
			count = int64(len(resp.Data))
		} else {
			// 数据总量，只在获取第一页时返回总数
			count, err = luckybag.CountPendingInitiateRecordBy(initiateRecord)
			if err != nil {
				logger.Errorf("Failed to count: %v", err)
				// PASS
			}
		}
		resp.Pagination.Count = &count
	}
	return resp, "success", nil
}

func buildJoinNum(resp []*roomInfo) {
	if len(resp) == 0 {
		return
	}
	keyList := make([]string, 0, len(resp))
	for i := range resp {
		keyList = append(keyList, keys.KeyLuckyBagJoinNum1.Format(resp[i].luckyBagID))
	}
	cmdList, err := service.Redis.MGet(keyList...).Result()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	for i, val := range cmdList {
		if val != nil {
			resp[i].JoinNum, _ = strconv.ParseInt(val.(string), 10, 64)
		}
	}
}
