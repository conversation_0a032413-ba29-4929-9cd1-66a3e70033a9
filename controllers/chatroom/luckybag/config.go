package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type luckyBagConfigParam struct {
	roomID int64

	config    *params.LuckyBagConfig
	luckyBags []*luckyBagConfigItem
}

type luckyBagConfigItem struct {
	Type               int                 `json:"type"`                           // 福袋类型 1: 广播剧福袋, 2: 实物福袋
	Name               string              `json:"name"`                           // 福袋类型名称
	MaxPrizeNum        int                 `json:"max_prize_num"`                  // 最大奖品数量
	Keyword            string              `json:"keyword"`                        // 默认参与口令
	InitiatedNum       *int                `json:"initiated_num,omitempty"`        // 用户当日已发福袋数量
	InitiateLimitDaily *int                `json:"initiate_limit_daily,omitempty"` // 当日可发福袋最大数量
	Targets            []luckyBagTarget    `json:"targets"`
	Rewards            []luckyBagReward    `json:"rewards"`
	Countdowns         []luckyBagCountdown `json:"countdowns"`
}

type luckyBagTarget struct {
	Type int    `json:"type"`
	Name string `json:"name"`
}

type luckyBagReward struct {
	Type  int    `json:"type"`
	Name  string `json:"name"`
	Intro string `json:"intro,omitempty"`
}

type luckyBagCountdown struct {
	MinPrice int64 `json:"min_price"` // 最小金额（钻）
	Duration int64 `json:"duration"`  // 毫秒
}

// ActionLuckyBagConfig 获取发起福袋配置页信息
/**
 * @api {get} /api/v2/chatroom/luckybag/config 获取发起福袋配置页信息
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "", // 在福袋黑名单的主播请求返回错误信息
 *     "data": {
 *       "lucky_bags": [ // 未在黑名单的主播下发广播剧福袋，实物福袋只有白名单的用户下发
 *         {
 *           "type": 1,
 *           "name": "广播剧福袋",
 *           "max_prize_num": 10, // 最大奖品数量
 *           "keyword": "点点关注抽福袋", // 参与默认口令
 *           "initiated_num": 1, // 用户当日已发福袋数量
 *           "initiate_limit_daily": 10, // 当日可发福袋最大数量
 *           "targets": [ // 参与对象类型, type 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝, 之后类型可能有新增, 此处仅用于传参, 下发的 type 直接使用即可, 客户端无需限制
 *             {
 *               "type": 0,
 *               "name": "所有人"
 *             },
 *             {
 *               "type": 1,
 *               "name": "关注"
 *             }
 *           ],
 *           "rewards": [ // 奖励类型, type 1: 广播剧, 2: 个人周边, 3: 剧集周边, 之后类型可能有新增, 此处仅用于传参, 下发的 type 直接使用即可, 客户端无需限制
 *             {
 *               "type": 1,
 *               "name": "广播剧"
 *             }
 *           ],
 *           "countdowns": [ // 可选倒计时时长，返回符合条件的可选时长
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 180000 // 毫秒
 *             },
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 300000 // 毫秒
 *             },
 *             {
 *               "min_price": 200, // 最小金额（钻）
 *               "duration": 600000 // 毫秒
 *             },
 *             {
 *               "min_price": 1000, // 最小金额（钻）
 *               "duration": 900000 // 毫秒
 *             }
 *           ]
 *         },
 *         {
 *           "type": 2,
 *           "name": "实物福袋",
 *           "max_prize_num": 10, // 最大奖品数量
 *           "keyword": "点点关注抽福袋", // 参与默认口令
 *           "targets": [ // 参与对象类型, 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝, 之后类型可能有新增, 此处仅用于传参, 下发的 type 直接使用即可, 客户端无需限制
 *             {
 *               "type": 0,
 *               "name": "所有人"
 *             },
 *             {
 *               "type": 1,
 *               "name": "关注"
 *             },
 *             {
 *               "type": 2,
 *               "name": "粉丝勋章"
 *             },
 *             {
 *               "type": 3,
 *               "name": "超级粉丝"
 *             }
 *           ],
 *           "rewards": [ // 奖励类型, 1: 广播剧, 2: 个人周边, 3: 剧集周边, 之后类型可能有新增, 此处仅用于传参, 下发的 type 直接使用即可, 客户端无需限制
 *             {
 *               "type": 2,
 *               "name": "个人周边",
 *               "intro": "对奖励进行备注说明，如：xxx"
 *             },
 *             {
 *               "type": 3,
 *               "name": "剧集周边",
 *               "intro": "对奖励进行备注说明，如：xxx"
 *             }
 *           ],
 *           "countdowns": [ // 可选倒计时时长，实物奖励没有最小金额限制（即 min_price 为 0）
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 180000 // 毫秒
 *             },
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 300000 // 毫秒
 *             },
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 600000 // 毫秒
 *             },
 *             {
 *               "min_price": 0, // 最小金额（钻）
 *               "duration": 900000 // 毫秒
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *   }
 *
 */
func ActionLuckyBagConfig(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newLuckyBagConfig(c)
	if err != nil {
		return nil, "", err
	}
	param.luckyBagDrama()
	param.luckyBagEntity()
	return handler.M{"lucky_bags": param.luckyBags}, "", nil
}

func newLuckyBagConfig(c *handler.Context) (*luckyBagConfigParam, error) {
	param := &luckyBagConfigParam{
		// 目前只有剧集福袋和实物福袋两个类型
		luckyBags: make([]*luckyBagConfigItem, 0, 2),
	}
	param.roomID, _ = c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	r, err := room.Find(param.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != c.UserID() {
		return nil, actionerrors.NewErrForbidden("只有主播才能发起福袋")
	}

	// 判断直播间是否在福袋黑名单中
	exists, err := application.IsRoomLuckyBagInitiateBlocked(param.roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if exists {
		return nil, actionerrors.NewErrForbidden("您当前无法发起福袋玩法")
	}

	param.config, err = params.FindLuckyBag()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return param, nil
}

func (param *luckyBagConfigParam) luckyBagDrama() {
	initiatedNum, err := luckybag.InitiateDailyCount(param.roomID, luckybag.TypeDrama)
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": param.roomID,
		}).Error(err)
		// PASS
	}

	config := param.config.LuckyBagDrama
	item := luckyBagConfigItem{
		Type:               luckybag.TypeDrama,
		Name:               config.Name,
		MaxPrizeNum:        config.MaxPrizeNum,
		Keyword:            config.Keyword,
		InitiatedNum:       goutil.NewInt(initiatedNum),
		InitiateLimitDaily: goutil.NewInt(config.InitiateLimitDaily),
		Targets:            buildTargets(config.Targets),
		Rewards:            buildRewards(config.Rewards),
		Countdowns:         buildCountdowns(config.Countdowns),
	}
	param.luckyBags = append(param.luckyBags, &item)
}

func (param *luckyBagConfigParam) luckyBagEntity() {
	// 判断直播间是否在实物福袋白名单中
	exists, err := application.IsRoomCustomLuckyBagInitiateAllowed(param.roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if !exists {
		return
	}

	config := param.config.LuckyBagEntity
	item := luckyBagConfigItem{
		Type:        luckybag.TypeEntity,
		Name:        config.Name,
		MaxPrizeNum: config.MaxPrizeNum,
		Keyword:     config.Keyword,
		Targets:     buildTargets(config.Targets),
		Rewards:     buildRewards(config.Rewards),
		Countdowns:  buildCountdowns(config.Countdowns),
	}
	param.luckyBags = append(param.luckyBags, &item)
}

func buildTargets(targets []params.LuckyBagTarget) []luckyBagTarget {
	newTargets := make([]luckyBagTarget, 0, len(targets))
	for _, target := range targets {
		newTargets = append(newTargets, luckyBagTarget{
			Type: target.Type,
			Name: target.Name,
		})
	}
	return newTargets
}

func buildRewards(rewards []params.LuckyBagReward) []luckyBagReward {
	newRewards := make([]luckyBagReward, 0, len(rewards))
	for _, reward := range rewards {
		newRewards = append(newRewards, luckyBagReward{
			Type:  reward.Type,
			Name:  reward.Name,
			Intro: reward.Intro,
		})
	}
	return newRewards
}

func buildCountdowns(countdowns []params.LuckyBagCountdown) []luckyBagCountdown {
	newCountdowns := make([]luckyBagCountdown, 0, len(countdowns))
	for _, countdown := range countdowns {
		newCountdowns = append(newCountdowns, luckyBagCountdown{
			MinPrice: countdown.MinPrice,
			Duration: countdown.Duration,
		})
	}
	return newCountdowns
}
