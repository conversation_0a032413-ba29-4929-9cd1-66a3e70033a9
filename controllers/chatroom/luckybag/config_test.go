package luckybag

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLuckyBagConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(application.Element{},
		"element_id = ?", 22489473).Error
	require.NoError(err)

	element := application.Element{
		ApplicationID: 3,
		ElementID:     22489473,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/?room_id=22489473", true, nil)
	c.User().ID = 10
	resp, _, err := ActionLuckyBagConfig(c)
	require.NoError(err)
	luckyBagMap, ok := resp.(handler.M)["lucky_bags"]
	require.True(ok)
	luckyBags := luckyBagMap.([]*luckyBagConfigItem)
	require.Equal(2, len(luckyBags))
	assert.Equal(luckybag.TypeDrama, luckyBags[0].Type)
	assert.Equal(luckybag.TypeEntity, luckyBags[1].Type)
}

func TestNewLuckyBagConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(application.Element{},
		"element_id = ?", 18113499).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/", true, nil)
	_, err = newLuckyBagConfig(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/?room_id=18113499", true, nil)
	param, err := newLuckyBagConfig(c)
	require.NoError(err)
	assert.EqualValues(18113499, param.roomID)
	assert.NotNil(param.config)

	element := application.Element{
		ApplicationID: 1,
		ElementID:     18113499,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/?room_id=18113499", true, nil)
	_, err = newLuckyBagConfig(c)
	assert.EqualError(err, "您当前无法发起福袋玩法")

	c = handler.NewTestContext(http.MethodGet, "/?room_id=18113499", true, nil)
	c.User().ID = 9074509
	_, err = newLuckyBagConfig(c)
	assert.EqualError(err, "只有主播才能发起福袋")
}

func TestLuckyBagConfigParam_luckyBagDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(luckybag.InitiateRecord{},
		"room_id = ?", 9074511).Error
	require.NoError(err)

	now := goutil.TimeNow()
	record := &luckybag.InitiateRecord{
		RoomID:     9074511,
		Type:       luckybag.TypeDrama,
		CreateTime: now.Unix(),
	}
	require.NoError(record.Create())
	defer func() {
		err = service.LiveDB.Delete(luckybag.InitiateRecord{},
			"room_id = ?", 9074511).Error
		require.NoError(err)
	}()

	config, err := params.FindLuckyBag()
	require.NoError(err)

	param := luckyBagConfigParam{
		roomID:    record.RoomID,
		config:    config,
		luckyBags: []*luckyBagConfigItem{},
	}
	param.luckyBagDrama()
	require.Equal(1, len(param.luckyBags))
	assert.EqualValues(1, *param.luckyBags[0].InitiatedNum)
	assert.Equal(luckybag.TypeDrama, param.luckyBags[0].Type)
	assert.Equal(1, len(param.luckyBags[0].Rewards))
	assert.Equal(2, len(param.luckyBags[0].Targets))
	assert.Equal(4, len(param.luckyBags[0].Countdowns))
}

func TestLuckyBagConfigParam_luckyBagEntity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(9074512)
	err := service.LiveDB.Delete(luckybag.InitiateRecord{},
		"room_id = ?", testRoomID).Error
	require.NoError(err)
	err = service.LiveDB.Delete(application.Element{},
		"element_id = ?", testRoomID).Error
	require.NoError(err)

	config, err := params.FindLuckyBag()
	require.NoError(err)
	param := luckyBagConfigParam{
		roomID:    testRoomID,
		config:    config,
		luckyBags: []*luckyBagConfigItem{},
	}
	param.luckyBagEntity()
	assert.Empty(param.luckyBags)

	element := application.Element{
		ApplicationID: 3,
		ElementID:     testRoomID,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)
	param.luckyBagEntity()
	require.Equal(1, len(param.luckyBags))
	assert.Equal(2, len(param.luckyBags[0].Rewards))
	assert.Equal(4, len(param.luckyBags[0].Targets))
	assert.Equal(4, len(param.luckyBags[0].Countdowns))
}

func TestBuildTargets(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config, err := params.FindLuckyBag()
	require.NoError(err)
	targets := buildTargets(config.LuckyBagDrama.Targets)
	expected := []luckyBagTarget{
		{Type: 0, Name: "所有人"},
		{Type: 1, Name: "关注"},
	}
	assert.Equal(expected, targets)
}

func TestBuildRewards(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config, err := params.FindLuckyBag()
	require.NoError(err)
	rewards := buildRewards(config.LuckyBagEntity.Rewards)
	expected := []luckyBagReward{
		{Type: luckybag.RewardTypeEntityPersonal, Name: "个人周边", Intro: "对奖励内容进行备注说明，如“签名照”"},
		{Type: luckybag.RewardTypeEntityDrama, Name: "实物周边", Intro: "对奖励内容进行备注说明，如“签名照”"},
	}
	assert.Equal(expected, rewards)
}

func TestBuildCountdowns(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config, err := params.FindLuckyBag()
	require.NoError(err)
	countdowns := buildCountdowns(config.LuckyBagDrama.Countdowns)
	expected := []luckyBagCountdown{
		{MinPrice: 0, Duration: 180000},
		{MinPrice: 0, Duration: 300000},
		{MinPrice: 200, Duration: 600000},
		{MinPrice: 1000, Duration: 900000},
	}
	assert.Equal(expected, countdowns)
}
