package luckybag

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

func TestActionAdminRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		luckyBagID = int64(12)
		uri        = "/api/v2/admin/luckybag/remove"
	)

	lb, err := luckybag.FindShowingInitiateRecordByID(luckyBagID)
	require.NoError(err)
	require.NotNil(lb)

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 0,
		"reason":       "福袋口令内容含有违规内容",
	})
	_, _, err = ActionAdminRemove(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 没填删除原因
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 1,
		"reason":       "",
	})
	_, _, err = ActionAdminRemove(c)
	assert.Equal(actionerrors.ErrParamsMsg("请输入删除原因"), err)

	// 测试福袋不存在
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 999,
		"reason":       "福袋口令内容含有违规内容",
	})
	_, _, err = ActionAdminRemove(c)
	assert.Equal(actionerrors.ErrNotFound("福袋不存在或已删除"), err)

	// 测试只能删除进行中的福袋
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 13,
		"reason":       "福袋口令内容含有违规内容",
	})
	_, _, err = ActionAdminRemove(c)
	assert.Equal(actionerrors.NewErrForbidden("只能删除进行中的福袋"), err)

	// 测试弹窗内容
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": luckyBagID,
		"reason":       "福袋口令内容含有违规内容",
		"confirm":      0,
	})
	_, _, err = ActionAdminRemove(c)
	msg := `确认要删除福袋吗？<br>福袋 ID: 12<br>主播昵称：test_user<br>奖品名称：删除福袋<br>奖品数量：10<br>口令：口令 1<br><font color="red">删除原因：福袋口令内容含有违规内容</font>`
	assert.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)

	// mock 数据
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		assert.EqualValues(3457181, systemMsgList[0].UserID)
		assert.EqualValues("喵喵福袋违规删除通知", systemMsgList[0].Title)
		assert.EqualValues(`你在 `+time.Unix(lb.CreateTime, 0).Format(util.TimeFormatYMDHMS)+` 发放的广播剧福袋（福袋 ID：<a href="copy:12">12</a>）经审核发现违规，现已被删除，删除原因：福袋口令内容含有违规内容。请注意福袋使用规范，如有疑问可联系客服咨询。`,
			systemMsgList[0].Content)
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		var body struct {
			RoomID  int64               `json:"room_id"`
			Payload luckyBagRoomMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotNil(body.Payload)

		assert.Equal(liveim.TypeLuckyBag, body.Payload.Type)
		assert.Equal(liveim.EventLuckyBagRemove, body.Payload.Event)
		assert.Equal(int64(18113499), body.Payload.RoomID)
		assert.Equal(luckyBagID, body.Payload.LuckyBag.LuckyBagID)
		assert.False(body.Payload.LuckyBag.HasMore)
		assert.Equal("本直播间的喵喵福袋经审核发现违规，现已被删除", body.Payload.Message)
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URILiveRefundGoods, func(input any) (any, error) {
		return userapi.BalanceResp{}, nil
	})
	defer cancel()

	// 测试删除成功
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": luckyBagID,
		"reason":       "福袋口令内容含有违规内容",
		"confirm":      1,
	})
	data, message, err := ActionAdminRemove(c)
	require.NoError(err)
	assert.Equal("删除成功", message)
	assert.Nil(data)

	var count int
	require.NoError(luckybag.DB().Model(&luckybag.InitiateRecord{}).
		Where("id = ? AND status = ?", luckyBagID, luckybag.StatusDeleted).Count(&count).Error)
	assert.Equal(1, count)
}

func TestNewAdminLuckyBagRemoveParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	luckyBagID := int64(12)
	uri := "/api/v2/admin/luckybag/remove"

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 0,
		"reason":       "福袋口令内容含有违规内容",
	})
	_, err := newAdminLuckyBagRemoveParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 没填删除原因
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": 1,
		"reason":       "",
	})
	_, err = newAdminLuckyBagRemoveParam(c)
	assert.Equal(actionerrors.ErrParamsMsg("请输入删除原因"), err)

	// 测试成功
	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"lucky_bag_id": luckyBagID,
		"reason":       "福袋口令内容含有违规内容",
		"confirm":      0,
	})
	param, err := newAdminLuckyBagRemoveParam(c)
	require.NoError(err)
	assert.Equal(luckyBagID, param.LuckyBagID)
	assert.Equal("福袋口令内容含有违规内容", param.Reason)
	assert.Equal(0, param.Confirm)
}

func TestLuckyBagRemoveParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := luckyBagRemoveParam{
		LuckyBagID: 999, // ID 不存在
		Reason:     "福袋口令内容含有违规内容",
	}
	// 测试福袋不存在
	assert.Equal(actionerrors.ErrNotFound("福袋不存在或已删除"), param.check())

	// 测试福袋不存在
	param.LuckyBagID = 12 // TestNewLuckyBagDeleteParam 被删除
	assert.Equal(actionerrors.ErrNotFound("福袋不存在或已删除"), param.check())

	// 测试只能删除进行中的福袋
	param.LuckyBagID = 13
	assert.Equal(actionerrors.NewErrForbidden("只能删除进行中的福袋"), param.check())

	// 测试弹窗内容
	param.LuckyBagID = 14
	msg := `确认要删除福袋吗？<br>福袋 ID: 14<br>主播昵称：test_user<br>奖品名称：删除福袋<br>奖品数量：10<br>口令：口令 1<br><font color="red">删除原因：福袋口令内容含有违规内容</font>`
	assert.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), param.check())

	// 测试不弹窗
	param.Confirm = 1
	require.NoError(param.check())
}

func TestLuckyBagRemoveParam_delete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	luckyBagID := int64(14)

	// mock 数据
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		assert.EqualValues(10, systemMsgList[0].UserID)
		assert.EqualValues("喵喵福袋违规删除通知", systemMsgList[0].Title)
		assert.EqualValues(`你在 2024-06-27 18:52:46 发放的广播剧福袋（福袋 ID：<a href="copy:14">14</a>）经审核发现违规，现已被删除，删除原因：福袋口令内容含有违规内容。请注意福袋使用规范，如有疑问可联系客服咨询。`,
			systemMsgList[0].Content)
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		var body struct {
			RoomID  int64               `json:"room_id"`
			Payload luckyBagRoomMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotNil(body.Payload)

		assert.Equal(liveim.TypeLuckyBag, body.Payload.Type)
		assert.Equal(liveim.EventLuckyBagRemove, body.Payload.Event)
		assert.Equal(int64(18113499), body.Payload.RoomID)
		assert.Equal(int64(14), body.Payload.LuckyBag.LuckyBagID)
		assert.False(body.Payload.LuckyBag.HasMore)
		assert.Equal("本直播间的喵喵福袋经审核发现违规，现已被删除", body.Payload.Message)
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URILiveRefundGoods, func(input any) (any, error) {
		return userapi.BalanceResp{}, nil
	})
	defer cancel()

	param := luckyBagRemoveParam{
		LuckyBagID: luckyBagID,
		Reason:     "福袋口令内容含有违规内容",
		c:          handler.NewTestContext(http.MethodPost, "", true, nil),
		luckyBag: &luckybag.InitiateRecord{
			RoomID:     18113499,
			CreatorID:  10,
			Type:       luckybag.TypeDrama,
			CreateTime: 1719485566,
		},
	}
	err := param.delete()
	require.NoError(err)

	var count int
	require.NoError(luckybag.DB().Model(&luckybag.InitiateRecord{}).
		Where("id = ? AND status = ?", luckyBagID, luckybag.StatusDeleted).Count(&count).Error)
	assert.Equal(1, count)

	param.LuckyBagID = -999
	err = param.delete()
	require.Equal(actionerrors.ErrNotFound("福袋不存在或已删除"), err)
}

func TestLuckyBagRemoveParam_sendSystemMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isCalled := false
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		assert.EqualValues(12, systemMsgList[0].UserID)
		assert.EqualValues("喵喵福袋违规删除通知", systemMsgList[0].Title)
		assert.EqualValues(`你在 2024-06-27 18:52:46 发放的广播剧福袋（福袋 ID：<a href="copy:1">1</a>）经审核发现违规，现已被删除，删除原因：&lt;福袋口令内容含有违规内容&gt;。请注意福袋使用规范，如有疑问可联系客服咨询。`,
			systemMsgList[0].Content)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	param := luckyBagRemoveParam{
		LuckyBagID: 1,
		Reason:     "<福袋口令内容含有违规内容>",
		luckyBag: &luckybag.InitiateRecord{
			CreatorID:  12,
			Type:       luckybag.TypeDrama,
			CreateTime: 1719485566,
		},
	}
	param.sendSystemMsg()
	assert.True(isCalled)
}

func TestLuckyBagRemoveParam_sendRoomMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isCalled := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		var body struct {
			RoomID  int64               `json:"room_id"`
			Payload luckyBagRoomMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotNil(body.Payload)

		assert.Equal(liveim.TypeLuckyBag, body.Payload.Type)
		assert.Equal(liveim.EventLuckyBagRemove, body.Payload.Event)
		assert.Equal(int64(1), body.Payload.RoomID)
		assert.Equal(int64(1), body.Payload.LuckyBag.LuckyBagID)
		assert.False(body.Payload.LuckyBag.HasMore)
		assert.Equal("本直播间的喵喵福袋经审核发现违规，现已被删除", body.Payload.Message)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	param := luckyBagRemoveParam{
		LuckyBagID: 1,
		luckyBag: &luckybag.InitiateRecord{
			RoomID: 1,
		},
	}
	param.sendRoomMsg()
	assert.True(isCalled)
}
