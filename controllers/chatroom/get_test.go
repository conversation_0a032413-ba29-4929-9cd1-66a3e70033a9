package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionMine(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/", true, nil)
	c.User().ID = 999999
	_, err := ActionMine(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	r, err := ActionMine(c)
	require.NoError(err)
	resp := r.(*myChatroomResp)
	assert.NotNil(resp.Room)
	assert.NotNil(resp.Vitality)
	assert.Nil(resp.Ban)
	assert.Nil(resp.Room.Members)
}
