package chatroom

import (
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionUserBlock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testRoomID int64 = 18113499
	var (
		staffUserID  int64 = 3456835 // 超管
		adminUserID  int64 = 1023    // 房管
		normalUserID int64 = 346287
	)
	cleanup := mrpc.SetMock(userapi.URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return "成功加入黑名单", nil
		})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemembers.Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID}, bson.M{"$set": bson.M{"user_id": adminUserID}})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/user/block", true, handler.M{
		"room_id": testRoomID,
		"user_id": adminUserID,
	})
	require.NoError(service.LRURedis.SAdd(blocklist.KeyUserBlock(c.UserID()), adminUserID).Err())
	_, err = ActionUserBlock(c)
	assert.EqualError(err, "此用户已经被您拉黑了")

	c = handler.NewTestContext(http.MethodPost, "/user/block", true, handler.M{
		"room_id": testRoomID,
		"user_id": adminUserID,
	})
	require.NoError(blocklist.Remove(c.UserID(), adminUserID))
	_, err = ActionUserBlock(c)
	assert.EqualError(err, "无法将房管踢出并拉入黑名单，请先取消其房管身份")

	c = handler.NewTestContext(http.MethodPost, "/user/block", true, handler.M{
		"room_id": testRoomID,
		"user_id": staffUserID,
	})
	_, err = ActionUserBlock(c)
	assert.EqualError(err, "无法将超管踢出并拉入黑名单")

	c = handler.NewTestContext(http.MethodPost, "/user/block", true, handler.M{
		"room_id": testRoomID,
		"user_id": normalUserID,
	})
	resp, err := ActionUserBlock(c)
	require.NoError(err)
	assert.Equal("success", resp)

	cleanup = mrpc.SetMock(userapi.URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return nil, errors.New("查无此人")
		})
	defer cleanup()
	c = handler.NewTestContext(http.MethodPost, "/user/block", true, handler.M{
		"room_id": testRoomID,
		"user_id": normalUserID,
	})
	require.NoError(blocklist.Remove(c.UserID(), normalUserID))
	_, err = ActionUserBlock(c)
	assert.EqualError(err, "查无此人")
}

func TestNewUserBlockParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id": 10,
		"room_id": 223344,
	})
	c.User().ID = 223344
	param, err := newUserBlockParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.r)
	assert.NotNil(param.u)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id": 10,
		"room_id": 223344,
	})
	_, err = newUserBlockParam(c)
	assert.EqualError(err, "您无权执行该操作")

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id": 12,
		"room_id": 18113499,
	})
	_, err = newUserBlockParam(c)
	assert.EqualError(err, "不能拉黑自己")
}

func TestBlockUserCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(1)
		testToBlockUserID   = int64(12)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))

	var (
		staffUserID int64 = 3456835 // 超管
		adminUserID int64 = 1023    // 房管
	)
	r, err := room.Find(18113499)
	require.NoError(err)
	require.NotNil(r)

	u, err := liveuser.Find(adminUserID)
	require.NoError(err)
	require.NotNil(u)
	param := &userBlockParam{UserID: adminUserID, r: r, u: u}
	assert.EqualError(param.check(), "无法将房管踢出并拉入黑名单，请先取消其房管身份")

	u, err = liveuser.Find(staffUserID)
	require.NoError(err)
	require.NotNil(u)
	param = &userBlockParam{r: r, u: u}
	assert.EqualError(param.check(), "无法将超管踢出并拉入黑名单")

	param = &userBlockParam{
		r: &room.Room{Helper: room.Helper{CreatorID: testFromBlockUserID}},
		u: &liveuser.User{UID: testToBlockUserID},
	}
	assert.EqualError(param.check(), "此用户已经被您拉黑了")
}

func TestUserBlockParam_block(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &userBlockParam{
		r: new(room.Room),
		c: handler.NewTestContext(http.MethodPost, "", true, nil),
	}

	cleanup := mrpc.SetMock(userapi.URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return "成功加入黑名单", nil
		})
	defer cleanup()
	require.NoError(param.block())

	cleanup = mrpc.SetMock(userapi.URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return nil, errors.New("查无此人")
		})
	defer cleanup()
	assert.EqualError(param.block(), "查无此人")
}

func TestBlockUserAfterBlock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(10101)
	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now.Add(time.Minute)
	})

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = liveconnect.Collection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "user_id": testUserID})
	require.NoError(err)
	connectResult, err := liveconnect.Collection().InsertOne(ctx, &liveconnect.LiveConnect{
		OID: primitive.NewObjectID(),
		Helper: liveconnect.Helper{
			RoomOID:     r.OID,
			RoomID:      r.RoomID,
			UserID:      testUserID,
			Status:      liveconnect.StatusJoined,
			CreatedTime: now.Unix(),
		},
	})
	require.NoError(err)
	_, err = livequestion.Collection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "user_id": testUserID})
	require.NoError(err)
	questionResult, err := livequestion.Collection().InsertOne(ctx, &livequestion.LiveQuestion{
		Helper: livequestion.Helper{
			RoomID:        r.RoomID,
			UserID:        testUserID,
			TransactionID: 111111,
			Status:        livequestion.StatusQueued,
		},
	})
	require.NoError(err)

	param := &userBlockParam{
		UserID: testUserID,
		RoomID: r.RoomID,
		r:      r,
		c:      handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.afterBlock()
	require.NoError(err)

	connect := new(liveconnect.LiveConnect)
	err = liveconnect.Collection().FindOne(ctx, bson.M{"_id": connectResult.InsertedID}).Decode(connect)
	require.NoError(err)
	assert.Equal(liveconnect.StatusCanceled, connect.Status)
	question := new(livequestion.LiveQuestion)
	err = livequestion.Collection().FindOne(ctx, bson.M{"_id": questionResult.InsertedID}).Decode(question)
	require.NoError(err)
	assert.Equal(liveconnect.StatusCanceled, question.Status)
}

func TestCancelQuestion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URICancelAsks, func(input interface{}) (interface{}, error) {
		v, ok := input.(map[string]interface{})
		require.True(ok)
		tids, ok := v["transaction_ids"].([]int64)
		require.True(ok)
		return &userapi.CancelAsksResp{Transactions: make([]userapi.CancelAsksTransaction, len(tids))}, nil
	})
	defer cancel()

	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	result, err := livequestion.Collection().InsertOne(ctx, &livequestion.LiveQuestion{
		Helper: livequestion.Helper{
			RoomID:        r.RoomID,
			UserID:        10101,
			TransactionID: 111111,
			Status:        livequestion.StatusQueued,
		},
	})
	require.NoError(err)

	param := &userBlockParam{
		UserID: 10101,
		RoomID: r.RoomID,
		r:      r,
		c:      handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.cancelQuestion()
	require.NoError(err)

	question := new(livequestion.LiveQuestion)
	err = livequestion.Collection().FindOne(ctx, bson.M{"_id": result.InsertedID}).Decode(question)
	require.NoError(err)
	assert.Equal(liveconnect.StatusCanceled, question.Status)
}
