package chatroom

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestErrExceedPKPeakLimit(t *testing.T) {
	assert := assert.New(t)

	err := errExceedPKPeakLimit(true)
	assert.EqualError(err, "今日 20-22 点 PK 次数已达上限！")

	err = errExceedPKPeakLimit(false)
	assert.EqualError(err, "对方今日 20-22 点 PK 次数已达上限！")
}

func TestActionPKAssistsList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	errURIs := []string{
		"/api/v2/chatroom/pk/assists/list",
		"/api/v2/chatroom/pk/assists/list?room_id=1",
		"/api/v2/chatroom/pk/assists/list?room_id=-1",
		"/api/v2/chatroom/pk/assists/list?pk_id=1111",
	}
	for _, uri := range errURIs {
		c := handler.NewTestContext("GET", uri, false, nil)
		_, err := ActionPKAssistsList(c)
		require.Equal(actionerrors.ErrParams, err)
	}

	c := handler.NewTestContext("GET",
		"/api/v2/chatroom/pk/assists/list?pk_id=62021590f0af33b2fcd342f4&room_id=1000", false, nil)
	resp, err := ActionPKAssistsList(c)
	require.NoError(err)
	assert.NotNil(resp)
	assert.NotNil(resp.(handler.M)["data"])
}

func TestFindAssistsList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pkOID, _ := primitive.ObjectIDFromHex("62021590f0af33b2fcd342f4")
	params := &assistsListParams{
		pkOID:  pkOID,
		roomID: 1000,
	}
	pkKey := roomsrank.PKKey(params.roomID, pkOID)
	require.NoError(service.Redis.Del(pkKey).Err())
	err := params.findAssistsList()
	require.NoError(err)
	assert.NotNil(params.data)

	params.data = nil
	err = service.Redis.ZAdd(pkKey,
		&redis.Z{Score: 111111, Member: 1234},
		&redis.Z{Score: 111, Member: 3457114}).Err()
	require.NoError(err)
	err = params.findAssistsList()
	require.NoError(err)
	require.Len(params.data, 2)
	assert.EqualValues(2, params.data[1].Rank)
}

func TestActionRecordList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/chatroom/pk/record/list?p=%d", -1), true, nil)
	_, err := ActionRecordList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet,
		"/api/v2/chatroom/pk/record/list?room_id=123", true, nil)
	_, err = ActionRecordList(c)
	assert.Equal(actionerrors.ErrForbidden, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	userID := int64(12)
	r, err := room.FindOne(bson.M{"creator_id": userID},
		&room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
	openRoom, err := room.FindOne(bson.M{
		"status.open": 1,
		"creator_id":  bson.M{"$ne": userID},
	}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(openRoom)

	startTime := goutil.TimeNow().Add(-time.Minute * 5).Unix()
	var pk livepk.LivePK
	filter := bson.M{
		"fighters.room_id": r.RoomID,
		"status":           livepk.PKRecordStatusFinished,
	}
	err = livepk.PKCollection().FindOneAndUpdate(ctx, filter, bson.M{
		"$set": bson.M{
			"type":           1,
			"winner_room_id": r.RoomID,
			"fighters": bson.A{
				bson.M{
					"room_id":    openRoom.RoomID,
					"creator_id": openRoom.CreatorID,
				},
				bson.M{
					"room_id":    r.RoomID,
					"creator_id": userID,
				},
			},
			"status":     livepk.PKRecordStatusFinished,
			"start_time": startTime,
		},
	}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&pk)
	require.NoError(err)
	count, err := livepk.PKCollection().CountDocuments(ctx, filter)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/chatroom/pk/record/list?room_id=%d", r.RoomID), true, nil)
	info, err := ActionRecordList(c)
	require.NoError(err)
	data := info.(*livePKSimpleList).Data[0]
	require.NotNil(data)
	assert.Equal(1, data.Type)
	pagination := info.(*livePKSimpleList).Pagination
	require.NotNil(pagination)
	require.NotNil(data.Fighters[0])
	assert.Equal(r.RoomID, data.Fighters[0].RoomID)
	assert.Equal(c.User().ID, data.Fighters[0].CreatorID)
	assert.NotEmpty(data.Fighters[0].CreatorUsername)
	assert.NotEmpty(data.Fighters[0].CreatorIconURL)
	require.NotNil(data.Fighters[1])
	assert.NotEmpty(data.Fighters[1].CreatorUsername)
	assert.NotEmpty(data.Fighters[1].CreatorIconURL)
	require.NotNil(data.Fighters[1].Status)
	assert.Equal(openRoom.Status.Open, data.Fighters[1].Status.Open)
	require.NotNil(data.Fighters[1].Statistics)
	assert.Equal(openRoom.Statistics.Score, data.Fighters[1].Statistics.Score)
	assert.Equal(startTime, data.StartTime)
	assert.Equal(livepk.PKResultWin, data.Result)
	assert.Equal(count, pagination.Count)
	assert.Equal(int64(1), pagination.MaxPage)
}

func TestFindFinishedPKRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := recordListParam{
		p:        1,
		pageSize: 20,
		room: &room.Room{
			Helper: room.Helper{
				RoomID: int64(2233),
			},
		},
		livePKSimpleList: new(livePKSimpleList),
	}

	err := param.findFinishedPKRecord()
	require.NoError(err)
	assert.Empty(param.Data)

	param.room.RoomID = int64(18113499)
	err = param.findFinishedPKRecord()
	require.NoError(err)
	assert.NotEmpty(param.Data)
}

func TestFormatPKRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	r, err := room.FindOne(bson.M{"creator_id": userID},
		&room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
	openRoom, err := room.FindOne(bson.M{
		"status.open": 1,
		"creator_id":  bson.M{"$ne": userID},
	}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(openRoom)

	param := recordListParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: r.RoomID,
			},
		},
		livePKSimpleList: &livePKSimpleList{
			Data: []*pkRecordItem{
				{
					WinnerRoomID: r.RoomID,
					Fighters: [2]*fighter{
						{
							RoomID:    openRoom.RoomID,
							CreatorID: openRoom.CreatorID,
						},
						{
							RoomID:    r.RoomID,
							CreatorID: userID,
						},
					},
				},
				{
					WinnerRoomID: r.RoomID,
					Fighters: [2]*fighter{
						{
							RoomID:    22489473,
							CreatorID: 10,
						},
						{
							RoomID:    r.RoomID,
							CreatorID: userID,
						},
					},
				},
			},
		},
	}

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param.formatPKRecord(c)
	recordList0 := param.Data[0]
	require.NotNil(recordList0)
	assert.Equal(livepk.PKResultWin, recordList0.Result)
	require.NotNil(recordList0.Fighters[0])
	assert.Equal(r.RoomID, recordList0.Fighters[0].RoomID)
	assert.Equal(r.CreatorUsername, recordList0.Fighters[0].CreatorUsername)
	require.NotNil(recordList0.Fighters[1])
	assert.Equal(openRoom.RoomID, recordList0.Fighters[1].RoomID)
	assert.NotEmpty(recordList0.Fighters[1].CreatorUsername)
	assert.NotEmpty(recordList0.Fighters[1].CreatorIconURL)
	require.NotNil(recordList0.Fighters[1].Status)
	assert.Equal(openRoom.Status.Open, recordList0.Fighters[1].Status.Open)
	require.NotNil(recordList0.Fighters[1].Statistics)
	assert.Equal(openRoom.Statistics.Score, recordList0.Fighters[1].Statistics.Score)
}

func TestMuteEvent(t *testing.T) {
	assert := assert.New(t)

	tests := []struct {
		param       *pkMuteParam
		roomID      int64
		targetEvent string
	}{
		{
			param:       &pkMuteParam{RoomID: 1, mute: true},
			roomID:      1,
			targetEvent: liveim.EventPKMute,
		},
		{
			param:       &pkMuteParam{RoomID: 1, mute: false},
			roomID:      1,
			targetEvent: liveim.EventPKUnmute,
		},
		{
			param:       &pkMuteParam{RoomID: 1, mute: true},
			roomID:      2,
			targetEvent: liveim.EventPKForcedMute,
		},
		{
			param:       &pkMuteParam{RoomID: 1, mute: false},
			roomID:      2,
			targetEvent: liveim.EventPKForcedUnmute,
		},
	}
	for _, t := range tests {
		assert.Equal(t.targetEvent, t.param.muteEvent(t.roomID))
	}
}

func TestFindAndSetMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{
		{
			RoomID:    1,
			CreatorID: 2,
			Mute:      0,
		},
		{
			RoomID:    2,
			CreatorID: 1,
			Mute:      0,
		},
	})
	require.NoError(err)
	require.NotNil(pk)
	defer func() {
		_, err := livepk.PKCollection().DeleteOne(ctx, bson.M{"_id": pk.OID})
		assert.NoError(err)
	}()

	param := pkMuteParam{
		RoomID: 1,
		mute:   true,
		pkID:   pk.OID,
	}
	require.NoError(param.findAndSetMute())
	assert.NotNil(param.pk)
	for _, f := range param.pk.Fighters {
		if f.RoomID == param.RoomID {
			assert.Equal(1, f.Mute)
		}
	}

	param.mute = false
	require.NoError(param.findAndSetMute())
	assert.NotNil(param.pk)
	for _, f := range param.pk.Fighters {
		if f.RoomID == param.RoomID {
			assert.Equal(0, f.Mute)
		}
	}

	param = pkMuteParam{
		RoomID: 1,
		mute:   false,
		pkID:   primitive.NewObjectID(),
	}
	err = param.findAndSetMute()
	assert.Equal(actionerrors.ErrNotFound("未查询到 PK 记录"), err)
}

func TestActionPKMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{
		{
			RoomID:    1,
			CreatorID: 2,
			Mute:      0,
		},
		{
			RoomID:    2,
			CreatorID: 1,
			Mute:      0,
		},
	})
	require.NoError(err)
	require.NotNil(pk)
	defer func() {
		_, err := livepk.PKCollection().DeleteOne(ctx, bson.M{"_id": pk.OID})
		assert.NoError(err)
	}()

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/mute", true, pkMuteParam{
		PKID:   pk.OID.Hex(),
		RoomID: 1,
	})
	r, err := ActionPKMute(c)
	require.NoError(err)
	assert.Equal("success", r)
}

func TestActionPKUnmute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{
		{
			RoomID:    1,
			CreatorID: 2,
			Mute:      0,
		},
		{
			RoomID:    2,
			CreatorID: 1,
			Mute:      0,
		},
	})
	require.NoError(err)
	require.NotNil(pk)
	defer func() {
		_, err := livepk.PKCollection().DeleteOne(ctx, bson.M{"_id": pk.OID})
		assert.NoError(err)
	}()

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/unmute", true, pkMuteParam{
		PKID:   pk.OID.Hex(),
		RoomID: 1,
	})
	r, err := ActionPKUnmute(c)
	require.NoError(err)
	assert.Equal("success", r)
}

func TestCheckRoomMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 19, 0, 0, 0, time.Local)
	})
	defer cancel()

	limitKey := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(limitKey).Err())
	r, err := room.FindOne(bson.M{"status.open": 0})
	require.NoError(err)
	require.NotNil(r)
	_, err = liveconnect.Collection().DeleteMany(context.Background(), bson.M{"room_id": r.RoomID})
	require.NoError(err)
	err = livemulticonnect.DB().
		Where("from_room_id = ? OR to_room_id = ?", r.RoomID, r.RoomID).
		Delete(livemulticonnect.Match{}).Error
	require.NoError(err)

	resp := pkMatchStartResp{roomID: r.RoomID}
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	c.User().ID = r.CreatorID
	assert.Equal(actionerrors.ErrClosedRoom, resp.checkRoomMatch(c))

	r, err = room.FindOne(bson.M{"status.open": 1})
	require.NoError(err)
	require.NotNil(r)
	startLockKey := keys.LockRoomPKStartMatch1.Format(r.RoomID)
	err = service.Redis.Del(keys.LockRoomPKEscapePunishment1.Format(r.RoomID), startLockKey).Err()
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "status": livepk.PKPoolStatusWaiting})
	require.NoError(err)
	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": r.RoomID})
	require.NoError(err)
	resp.roomID = r.RoomID
	c.User().ID = r.CreatorID
	require.NoError(resp.checkRoomMatch(c))

	assert.EqualError(resp.checkRoomMatch(c), "操作频繁，请稍后再试")

	_, err = livepk.InsertRandomPool(r.RoomID, r.CreatorID, 0)
	require.NoError(err)
	c.User().ID = r.CreatorID
	require.NoError(service.Redis.Del(startLockKey).Err())
	assert.EqualError(resp.checkRoomMatch(c), "PK 进行中无法重复匹配！", "匹配对手中")

	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "status": livepk.PKPoolStatusWaiting})
	require.NoError(err)
	_, err = livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{
		{
			RoomID:    1,
			CreatorID: 2,
		},
		{
			RoomID:    r.RoomID,
			CreatorID: r.CreatorID,
		},
	})
	require.NoError(err)
	require.NoError(service.Redis.Del(startLockKey).Err())
	assert.EqualError(resp.checkRoomMatch(c), "PK 进行中无法重复匹配！", "pk 进行中")

	// PK 冷静期
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"status": livepk.PKPoolStatusWaiting})
	require.NoError(err)
	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": r.RoomID})
	require.NoError(err)
	require.NoError(service.Redis.Set(keys.LockRoomPKEscapePunishment1.Format(r.RoomID),
		1, livepk.PKRunawayPunishmentDuration).Err())
	require.NoError(service.Redis.Del(startLockKey).Err())
	assert.EqualError(resp.checkRoomMatch(c), "由于多次提前结束 PK，您暂时无法参与 PK 玩法", "PK 冷静期")

	// 测试高峰时段 PK 次数限制 - 次数未到达上限
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 30, 0, 0, time.Local)
	})
	require.NoError(service.Redis.Del(keys.LockRoomPKStartMatch1.Format(resp.roomID)).Err())
	require.NoError(service.Redis.Del(keys.LockRoomPKEscapePunishment1.Format(r.RoomID)).Err())
	err = resp.checkRoomMatch(c)
	require.NoError(err)

	// 测试高峰时段 PK 次数限制 - 次数到达上限
	require.NoError(service.Redis.Del(keys.LockRoomPKStartMatch1.Format(resp.roomID)).Err())
	require.NoError(service.Redis.ZIncrBy(limitKey, 2, strconv.FormatInt(resp.roomID, 10)).Err())
	err = resp.checkRoomMatch(c)
	assert.EqualError(err, "今日 20-22 点 PK 次数已达上限！")
}

func TestActionPKMatchStartAndCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 19, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	r, err := room.FindOne(bson.M{"status.open": 1})
	require.NoError(err)
	require.NotNil(r)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID, "status": livepk.PKPoolStatusWaiting})
	require.NoError(err)
	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": r.RoomID})
	require.NoError(err)
	err = service.Redis.Del(keys.LockRoomPKEscapePunishment1.Format(r.RoomID)).Err()
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "pk/match/start", true, handler.M{"room_id": r.RoomID})
	c.User().ID = r.CreatorID
	require.NoError(service.Redis.Del(keys.LockRoomPKStartMatch1.Format(r.RoomID)).Err())
	_, err = ActionPKMatchStart(c)
	require.NoError(err)

	pool, err := livepk.FindWaitingPKByRoomID(r.RoomID)
	require.NoError(err)
	require.NotNil(pool)

	c = handler.NewTestContext(http.MethodPost, "pk/match/cancel", true, handler.M{"room_id": r.RoomID})
	c.User().ID = r.CreatorID
	_, err = ActionPKMatchCancel(c)
	require.NoError(err)
	pool, err = livepk.FindPKPoolByOID(pool.OID)
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(livepk.PKPoolStatusCancel, pool.Status)
}

func TestActionPKClose(t *testing.T) {
	assert := assert.New(t)

	p := pkCloseParam{
		RoomID: 18113499,
		PKID:   "",
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	_, err := ActionPKClose(c)
	assert.Equal(actionerrors.ErrParams, err)

	p.PKID = "60bdd76afac460c49cdd7605"
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	_, err = ActionPKClose(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)
}

func insertTestPKData(roomID int64, status int) (*livepk.LivePK, error) {
	// 先清理一次数据
	err := clearPKTestPKData(roomID, status)
	if err != nil {
		return nil, err
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pk livepk.LivePK
	err = livepk.PKCollection().FindOneAndUpdate(ctx, bson.M{"fighters.room_id": roomID, "status": status},
		bson.M{"$set": livepk.LivePK{
			Status: status,
			Fighters: [2]*livepk.Fighter{
				{RoomID: 1},
				{RoomID: roomID},
			}}}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&pk)
	if err != nil {
		return nil, err
	}
	return &pk, nil
}

func clearPKTestPKData(roomID int64, status int) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": roomID, "status": status})
	return err
}

func TestPKCloseParamLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk, err := insertTestPKData(testRoom.RoomID, livepk.PKRecordStatusFighting)
	require.NoError(err)
	defer func() {
		assert.NoError(clearPKTestPKData(testRoom.RoomID, livepk.PKRecordStatusFighting))
	}()

	p := pkCloseParam{
		RoomID: testRoom.RoomID,
		PKID:   pk.OID.Hex(),
		Type:   0,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = testRoom.CreatorID
	require.NoError(p.load(c))
	assert.NotNil(p.r)
	require.NotNil(p.pk)
	assert.Equal(testRoom.RoomID, p.pk.Fighters[0].RoomID)

	p = pkCloseParam{
		PKID: pk.OID.Hex(),
		Type: 0,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	c.Equip().OS = goutil.Android
	c.Equip().FromApp = true
	c.Equip().AppVersion = "5.6.7"
	assert.Equal(actionerrors.ErrForbidden, p.load(c))
}

func TestCloseOngoingPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk, err := insertTestPKData(testRoom.RoomID, livepk.PKRecordStatusFighting)
	require.NoError(err)
	defer func() {
		assert.NoError(clearPKTestPKData(testRoom.RoomID, livepk.PKRecordStatusFighting))
	}()

	p := pkCloseParam{
		pkOID: pk.OID,
		pk:    pk,
		r:     new(room.Room),
	}
	p.r.RoomID = testRoom.RoomID
	require.NoError(p.closeOngoingPK())

	pk, err = livepk.FindOne(bson.M{"_id": pk.OID})
	require.NoError(err)
	assert.EqualValues(testRoom.RoomID, pk.WinnerRoomID)
	assert.EqualValues(1, pk.RunawayRoomID)
	assert.Equal(livepk.PKRecordStatusFinished, pk.Status)
}

func TestClosePunishmentPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk, err := insertTestPKData(testRoom.RoomID, livepk.PKRecordStatusPunishment)
	require.NoError(err)
	defer func() {
		assert.NoError(clearPKTestPKData(testRoom.RoomID, livepk.PKRecordStatusPunishment))
	}()

	p := pkCloseParam{
		pkOID: pk.OID,
		pk:    pk,
		r:     new(room.Room),
	}
	p.r.RoomID = testRoom.RoomID
	require.NoError(p.closePunishmentPK())

	pk, err = livepk.FindOne(bson.M{"_id": pk.OID})
	require.NoError(err)
	assert.Zero(pk.WinnerRoomID)
	assert.Zero(pk.RunawayRoomID)
	assert.Equal(livepk.PKRecordStatusFinished, pk.Status)
}

func TestCloseConnectPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk, err := insertTestPKData(testRoom.RoomID, livepk.PKRecordStatusConnect)
	require.NoError(err)
	defer func() {
		assert.NoError(clearPKTestPKData(testRoom.RoomID, livepk.PKRecordStatusConnect))
	}()

	p := pkCloseParam{
		pkOID: pk.OID,
		pk:    pk,
		r:     new(room.Room),
	}
	p.r.RoomID = testRoom.RoomID
	require.NoError(p.closeConnectPK())

	pk, err = livepk.FindOne(bson.M{"_id": pk.OID})
	require.NoError(err)
	assert.Zero(pk.WinnerRoomID)
	assert.Zero(pk.RunawayRoomID)
	assert.Equal(livepk.PKRecordStatusFinished, pk.Status)
}
