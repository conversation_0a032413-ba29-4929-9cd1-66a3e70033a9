package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

func TestLogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	liveAdmin := role.AuthAssignment{
		UserID:   "12",
		ItemName: string(role.LiveAdmin),
	}
	err := service.DB.FirstOrCreate(&liveAdmin, liveAdmin).Error
	require.NoError(err)

	startDateStr := "0000-01-01"
	endDateStr := "2099-01-02"

	startDate, err := time.ParseInLocation("2006-01-02", startDateStr, time.Local)
	require.NoError(err)
	endDate, err := time.ParseInLocation("2006-01-02", endDateStr, time.Local)
	require.NoError(err)

	uri := "/?start_date=" + startDateStr + "&end_date=" + endDateStr
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	c.User().ID = 10
	c.C.Params = append(c.C.Params, gin.Param{Key: "roomID", Value: roomIDStr})

	var param actionLogsParam
	err = param.load(c)
	require.NoError(err)

	assert.Equal(int64(1), param.p)
	assert.Equal(int64(20), param.pageSize)
	assert.Equal(startDate, param.fromTime)
	assert.Equal(endDate.AddDate(0, 0, 1), param.toTime)
	assert.NotEmpty(roomIDStr)

	err = param.process()
	require.NoError(err)

	param.p = 0
	err = param.process()
	require.NoError(err)
	assert.NotNil(param.resp.Data)
	assert.Empty(param.resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/?p=2&pagesize=40", true, nil)
	c.User().ID = 10
	c.C.Params = append(c.C.Params, gin.Param{Key: "roomID", Value: roomIDStr})
	err = param.load(c)
	require.NoError(err)
	assert.Equal(int64(2), param.p)
	assert.Equal(int64(40), param.pageSize)
}
