package chatroom

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/imrpc"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var testForeverBlockUserID int64 = 2020101402

func TestRoomIDRespTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(&roomIDResponse{}, "room", "creator", "creator_card", "recommender", "websocket")
}

func TestRoomIDParam_PreCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(1)
		testToBlockUserID   = int64(12)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))

	_, err := room.Update(roomID, bson.M{"custom_tag_id": 10002})
	require.NoError(err)

	// test ErrCannotFindRoom
	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param := roomIDParam{C: c, Resp: new(roomIDResponse)}
	param.C.C.Request, _ = http.NewRequest("GET", "", nil)
	param.C.C.Params = []gin.Param{{Key: "roomID"}}
	values := [3]string{"aaa", "-123", "123"}
	for i := 0; i < 3; i++ {
		param.C.C.Params[0].Value = values[i]
		assert.Equal(actionerrors.ErrCannotFindRoom, param.PreCheck())
	}

	param.C.C.Params = []gin.Param{{Key: "roomID", Value: bannedRoomIDStr}}
	assert.Equal(actionerrors.ErrBannedRoom, param.PreCheck())
	assert.Empty(param.Resp.Room.Background.PendantImageURL)

	// 受限房间
	limitedRoomStr := strconv.FormatInt(room.TestLimitedRoomID, 10)
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: limitedRoomStr}}
	assert.NoError(param.PreCheck(), "白名单用户不被限制")
	param.C.User().ID = room.TestLimitedRoomCreatorID
	assert.NoError(param.PreCheck(), "主播不被限制")
	param.C.User().ID = 998
	assert.Equal(actionerrors.ErrCannotFindRoom, param.PreCheck())

	// 添加外观
	pImg := "test_pendant_image"
	pendant := appearance.Appearance{
		Type:  appearance.TypeBackgroundPendant,
		Image: pImg,
	}
	now := goutil.TimeNow()
	userPendant := userappearance.NewUserAppearance(testRoomCreatorID, &pendant)
	userPendant.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().InsertOne(ctx, userPendant)
	require.NoError(err)
	defer func() {
		_, err = userappearance.Collection().DeleteOne(ctx, userPendant)
		require.NoError(err)
	}()

	// 正常结果
	r, err := room.Update(roomID, bson.M{"status.open": 1})
	require.NoError(err)
	require.NotNil(r)
	param = roomIDParam{
		C:    handler.NewTestContext(http.MethodGet, "", false, nil),
		Resp: new(roomIDResponse),
	}
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	require.NoError(param.PreCheck())
	assert.Equal(roleGuest, param.Role)
	assert.Nil(param.User)
	assert.Equal(r.Status.OpenTime, param.OpenTime.UnixMilli())
	bg := param.Resp.Room.Background
	require.NotNil(bg)
	assert.Equal(pImg, bg.PendantImage)
	assert.NotEmpty(bg.PendantImageURL)
	assert.EqualValues(10002, param.Resp.Room.CustomTagID)

	param.C = handler.NewTestContext(http.MethodGet, "", true, nil)
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	assert.NoError(param.PreCheck())
	assert.Equal(roleListener, param.Role)
	// tutil.PrintJSON(param.Resp.Creator)
	bg = param.Resp.Room.Background
	require.NotNil(bg)
	assert.Equal(pImg, bg.PendantImage)
	assert.NotEmpty(bg.PendantImageURL)

	param.C = handler.NewTestContext(http.MethodGet, "", true, nil)
	param.C.User().ID = testForeverBlockUserID
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	assert.Equal(actionerrors.NewErrForbidden("您已被封禁，无法进入直播间"), param.PreCheck())

	param.C = handler.NewTestContext(http.MethodGet, "", true, nil)
	param.C.User().ID = testBanForeverUserID
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	assert.Equal(actionerrors.NewErrForbidden("您已被封禁，无法进入直播间"), param.PreCheck())

	// 测试拉黑
	param.C = handler.NewTestContext(http.MethodGet, "", true, nil)
	room, err := room.FindOne(bson.M{"creator_id": testFromBlockUserID})
	require.NoError(err)
	require.NotNil(room)
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: strconv.FormatInt(room.RoomID, 10)}}
	assert.EqualError(param.PreCheck(), "您当前无法进入直播间")
}

func TestRoomIDCheckStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := roomIDParam{Resp: &roomIDResponse{Room: new(room.Room)}}
	// 直接返回
	param.Resp.Room.Status.Broadcasting = true
	param.CheckStatus()
	assert.False(param.Resp.Room.Status.Broadcasting)
	// 测试 config 禁用检查流状态
	config.Conf.Web.CheckChannel = false
	param.Room.OID, _ = primitive.ObjectIDFromHex(roomOIDHex)
	param.Room.Status.Channel.Type = room.TypeLive
	param.Room.Status.Channel.Event = room.EventStart
	param.Room.Channel.Provider = room.ChannelProviderAliyun
	param.Resp.Room.Status.Broadcasting = false
	assert.True(param.Room.IsPushing())
	param.CheckStatus()
	assert.True(param.Resp.Room.Status.Broadcasting)

	_, err := service.LRURedis.Del(keys.KeyRoomStatusCache1.Format(roomID)).Result()
	require.NoError(err)
	// 调用 cdn 返回
	config.Conf.Web.CheckChannel = true
	param.Role = roleOwner
	param.RoomID = roomID
	param.Resp.Room.Status.Broadcasting = true
	beforeStore := goutil.TimeNow().Add(-1 * time.Second)
	param.CheckStatus()
	assert.False(param.Resp.Room.Status.Broadcasting)
	assert.Less(beforeStore.Unix(), param.Resp.Room.Status.Channel.Time/1000)
	isPushing, err := service.LRURedis.Get(keys.KeyRoomStatusCache1.Format(roomID)).Int()
	require.NoError(err)
	assert.Zero(isPushing)

	// 调用缓存返回
	param.Role = roleGuest
	param.Room.Status.Channel.Type = room.TypeLive
	param.Room.Status.Channel.Event = room.EventStart
	assert.True(param.Room.IsPushing())
	param.Resp.Room.Status.Broadcasting = true
	param.CheckStatus()
	assert.False(param.Resp.Room.Status.Broadcasting)
}

func TestRoomIDCheckRoomType(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C:        handler.NewTestContext(http.MethodGet, "", false, nil),
		Resp:     &roomIDResponse{Room: new(room.Room)},
		RoomOpen: true,
	}
	param.Resp.Room.Connect.Provider = room.ProviderAgora
	param.Role = roleOwner
	param.User = new(user.User)
	param.User.ID = 10
	param.Resp.Room.Connect.ID = "test"
	param.CheckRoomType()
	assert.Equal("test", param.Resp.Room.Connect.Name)
}

func TestRoomIDParam_buildRecommendBackground(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()
	service.Cache5Min.Set(keys.KeyRecommend0.Format(),
		liverecommendedelements.RecommendCache{
			12345: {
				liverecommendedelements.ElementBackground: {Attribute: liverecommendedelements.Attribute{URL: "http://background.webp;0.5"}},
			},
		}, 0)

	param := roomIDParam{
		RoomID: 12345,
		Resp:   &roomIDResponse{Room: new(room.Room)},
	}
	param.buildRecommendBackground()
	require.NotNil(param.Resp.Room.Background)
	assert.Equal("http://background.webp", param.Resp.Room.Background.ImageURL)
	assert.Equal(0.5, param.Resp.Room.Background.Opacity)

	param = roomIDParam{
		RoomID: 9999,
		Resp:   &roomIDResponse{Room: new(room.Room)},
	}
	param.buildRecommendBackground()
	assert.Nil(param.Resp.Room.Background)
}

func TestRoomIDParam_buildBackgroundURL(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C: handler.NewTestContext(http.MethodGet, "", true, nil),
		Resp: &roomIDResponse{Room: &room.Room{
			Helper: room.Helper{
				Background: &room.Background{
					ImageURL:        "http://background_image.avif",
					PendantImageURL: "http://pendant_image.avif;http://pendant_image.webp",
				},
			},
		}},
	}
	param.C.Equip().FromApp = true
	param.C.Equip().OS = goutil.IOS
	param.C.Equip().AppVersion = "4.9.0"
	param.buildBackgroundURL()
	assert.Equal("http://background_image.webp", param.Resp.Room.Background.ImageURL)
	assert.Equal("http://pendant_image.webp", param.Resp.Room.Background.PendantImageURL)

	param = roomIDParam{
		C: handler.NewTestContext(http.MethodGet, "", true, nil),
		Resp: &roomIDResponse{Room: &room.Room{
			Helper: room.Helper{
				Background: &room.Background{
					ImageURL:        "http://background_image.avif",
					PendantImageURL: "http://pendant_image.avif;http://pendant_image.webp",
				},
			},
		}},
	}
	param.C.Equip().FromApp = true
	param.C.Equip().OS = goutil.IOS
	param.C.Equip().AppVersion = "4.9.3"
	param.buildBackgroundURL()
	assert.Equal("http://background_image.avif", param.Resp.Room.Background.ImageURL)
	assert.Equal("http://pendant_image.avif;http://pendant_image.webp", param.Resp.Room.Background.PendantImageURL)
}

func TestRoomIDBuildQuestion(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "", false, nil)
	param := roomIDParam{
		Resp:     &roomIDResponse{Room: new(room.Room)},
		User:     new(user.User),
		RoomOpen: true,
		OpenTime: goutil.TimeNow(),
		C:        c,
	}
	param.Room.OID, _ = primitive.ObjectIDFromHex(roomOIDHex)
	assert.NoError(param.BuildQuestion())
	assert.NotNil(param.Resp.Room.Question.Join)
}

func TestRoomIDBuildConnect(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C:        handler.CreateTestContext(false),
		Resp:     &roomIDResponse{Room: new(room.Room)},
		RoomOpen: true,
		OpenTime: goutil.TimeNow(),
	}
	param.Room.Type = room.TypeLive
	param.Resp.Room.Connect.Queue = make([]*liveconnect.LiveConnect, 2)
	assert.NoError(param.BuildConnect())
	assert.Zero(len(param.Resp.Room.Connect.Queue))
	param.Room.OID, _ = primitive.ObjectIDFromHex(roomOIDHex)
	param.Room.Type = room.TypeConnect
	param.Role = roleGuest
	param.Resp.Room.Connect.ID = "aaa"
	assert.NoError(param.BuildConnect())
	assert.Equal("", param.Resp.Room.Connect.ID)
}

func TestRoomIDBuildMembers(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C:        handler.NewTestContext(http.MethodGet, "", false, nil),
		Resp:     &roomIDResponse{Room: new(room.Room)},
		RoomOpen: true,
	}
	param.Room.OID, _ = primitive.ObjectIDFromHex(roomOIDHex)
	assert.NoError(param.BuildMembers())
	assert.Nil(param.Resp.Room.Members)
}

func TestCheckSpecialUserOnline(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C:        handler.NewTestContext(http.MethodGet, "", false, nil),
		Resp:     &roomIDResponse{Room: new(room.Room)},
		User:     new(user.User),
		RoomOpen: true,
	}
	param.Resp.Creator = new(liveuser.User)
	param.RoomID = roomID

	param.Room.CreatorID = 1234
	c := &param.Resp.Room.Connect
	c.Join = []*liveconnect.LiveConnect{new(liveconnect.LiveConnect)}
	c.Join[0].UserID = 5678
	cancel := mrpc.SetMock("im://online/userstatus", func(input interface{}) (output interface{}, err error) {
		return imrpc.OnlineStatusResp{
			UserStatus: []imrpc.UserStatus{
				{UserID: 1234, Online: true},
				{UserID: 5678, Online: true},
			},
		}, nil
	})
	defer cancel()
	param.checkSpecialUserOnline()
	assert.True(param.Resp.Creator.Online)
	assert.True(*c.Join[0].Online)

	param.Resp.Creator.Online = false
	*c.Join[0].Online = false
	cancel = mrpc.SetMock("im://online/userstatus", func(input interface{}) (output interface{}, err error) {
		return imrpc.OnlineStatusResp{
			UserStatus: []imrpc.UserStatus{
				{UserID: 1234, Online: false},
				{UserID: 5678, Online: false},
			},
		}, nil
	})
	defer cancel()
	param.checkSpecialUserOnline()
	assert.False(param.Resp.Creator.Online)
	assert.False(*c.Join[0].Online)

	param.Resp.Creator.Online = false
	*c.Join[0].Online = false
	cancel = mrpc.SetMock("im://online/userstatus", func(input interface{}) (output interface{}, err error) {
		return nil, errors.New("test error")
	})
	defer cancel()
	param.checkSpecialUserOnline()
	assert.True(param.Resp.Creator.Online)
	assert.True(*c.Join[0].Online)
}

func TestRoomIDCheckVipNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param roomIDParam
	assert.NotPanics(func() { param.checkVipNum() })
	param.RoomOpen = true
	param.RoomID = roomID
	param.Resp = &roomIDResponse{Room: new(room.Room)}
	key := vipNumberKey(roomID)
	b, _ := json.Marshal(vipNumCache{VipNum: 10, UpdatedTime: goutil.TimeNow().Unix()})
	require.NoError(service.LRURedis.Set(key, b, time.Hour).Err())
	param.checkVipNum()
	param.C = handler.NewTestContext(http.MethodGet, "/live/123", false, nil)
	assert.Equal(int64(10), param.Resp.Room.Statistics.Vip)

	require.NoError(service.LRURedis.Del(key).Err())
	param.checkVipNum()
	assert.NotEqual(int64(10), param.Resp.Room.Statistics.Vip)
}

func TestRoomIDBuildChannel(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		C:        handler.NewTestContext(http.MethodGet, "", false, nil),
		Resp:     &roomIDResponse{Room: new(room.Room)},
		User:     new(user.User),
		RoomOpen: true,
	}
	param.Role = roleGuest
	param.Resp.Room.Channel.Custom = &room.CustomInfo{
		PushURL:    "rtmp://push",
		FLVPullURL: "https://test_flv",
	}
	param.BuildChannel()
	ch := param.Resp.Room.Channel
	assert.Empty(ch.PushURL)
	assert.Equal(ch.Custom.FLVPullURL, ch.FLVPullURL)
}

func TestBuildWebSocketURL(t *testing.T) {
	assert := assert.New(t)

	param := roomIDParam{
		Resp:   &roomIDResponse{Room: &room.Room{Helper: room.Helper{RoomID: 12345}}},
		RoomID: 12345,
	}
	param.BuildWebSocketURL()
	for i := range param.Resp.WebSocket {
		assert.Contains(param.Resp.WebSocket[i], "?room_id=12345")
	}
}

func TestActionRoomID(t *testing.T) {
	assert := assert.New(t)

	ctx := handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/live/%s", roomIDStr), false, nil)
	ctx.Equip().AppVersion = "4.6.6" // 测试提问兼容性
	ctx.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	key := livenoblerecommend.KeyCurrentRecommend(goutil.TimeNow())
	service.Cache5Min.Set(key, &livenoblerecommend.NobleRecommend{RoomID: roomID, Anonymous: 1}, 0)
	r, err := ActionRoomID(ctx)
	require.NoError(t, err)
	// tutil.PrintJSON(r)
	resp := r.(*roomIDResponse)
	assert.Equal(resp.Room.CreatorID, resp.Creator.UserID())
	assert.NotNil(resp.Recommender)
}

func TestRoomIDParam_findCreatorCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID int64 = 11111
		now              = goutil.TimeNow()
	)

	err := service.DB.Delete(liverecommendedelements.Model{}, "element_id = ?", testRoomID).Error
	require.NoError(err)
	eles := []*liverecommendedelements.LiveRecommendedElements{
		{
			ElementID:   testRoomID,
			ElementType: liverecommendedelements.ElementCreatorCard,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  now.Unix() + 1,
			Sort:        1,
		},
		{
			ElementID:   testRoomID,
			ElementType: liverecommendedelements.ElementCreatorCard,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  now.Unix() + 2,
			Sort:        1,
		},
	}
	err = servicedb.BatchInsert(service.DB, liverecommendedelements.TableName(), eles)
	require.NoError(err)
	service.Cache5Min.Flush()

	param := &roomIDParam{
		RoomID: testRoomID,
		Resp:   new(roomIDResponse),
	}
	param.findCreatorCard()
	assert.NotNil(param.Resp.CreatorCard)
}

func TestRoomIDParam_findRecommender(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	key := livenoblerecommend.KeyCurrentRecommend(now)
	nr := &livenoblerecommend.NobleRecommend{RoomID: roomID, FromUserID: 12} // 现在是非匿名推荐
	service.Cache5Min.Set(key, nr, 0)
	param := roomIDParam{
		Resp: &roomIDResponse{Room: new(room.Room)},
	}
	param.findRecommender()
	assert.Nil(param.Resp.Recommender)
	param.RoomID = nr.RoomID
	param.findRecommender()
	require.NotNil(param.Resp.Recommender)
	assert.Equal(nr.FromUserID, param.Resp.Recommender.UserID())
	assert.Equal(service.Storage.Parse(config.Conf.Params.NobleParams.RecommendWebFrame),
		param.Resp.Recommender.RecommendFrameURL)
	assert.Equal(service.Storage.Parse(config.Conf.Params.NobleParams.RecommendAvatarFrame),
		param.Resp.Recommender.RecommendAvatarFrameURL)
	nr.Anonymous = 1
	param.Resp.Recommender = nil
	param.findRecommender()
	require.NotNil(param.Resp.Recommender)
	assert.Zero(param.Resp.Recommender.UserID())
	assert.Equal("神秘人", param.Resp.Recommender.Username)
}

func TestFindPreviewRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/live/-999?preview=1", false, nil)
	c.C.Params = []gin.Param{{Key: "roomID", Value: "-999"}}
	_, err := findPreviewRoom(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/live/%s?preview=1", roomIDStr), false, nil)
	c.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	resp, err := findPreviewRoom(c)
	require.NoError(err)
	res := resp.(*previewRoomResponse)
	require.NotNil(res.Room)
	assert.Equal(roomID, res.Room.RoomID)
	assert.NotEmpty(res.WebSocket)
}

func TestPreviewRoomResponse_buildCreator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &previewRoomResponse{
		Room: &room.Room{
			Helper: room.Helper{RoomID: roomID, CreatorID: testRoomCreatorID},
		},
		userID: normalUserID,
	}
	require.NoError(attentionuser.UnFollow(normalUserID, testRoomCreatorID))

	require.NoError(resp.buildCreator())
	assert.NotNil(resp.Creator)
	assert.False(resp.Room.Statistics.Attention)

	require.NoError(attentionuser.Follow(normalUserID, testRoomCreatorID))
	require.NoError(resp.buildCreator())
	assert.NotNil(resp.Creator)
	assert.True(resp.Room.Statistics.Attention)
}

func TestPreviewRoomResponse_buildPreviewIntro(t *testing.T) {
	onlineCount := 0
	cancel := mrpc.SetMock("im://online/count", func(any) (any, error) {
		return map[string]any{
			"count": onlineCount,
		}, nil
	})
	defer cancel()

	t.Run("empty", func(t *testing.T) {
		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 114514,
			},
		}
		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.Empty(t, r.PreviewIntro)
	})

	t.Run("online", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 114514,
			},
		}

		onlineCount = 100
		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		require.NotNil(r.PreviewIntro)
		assert.Equal("100 + 人正在听", r.PreviewIntro.Title)
		assert.Equal(service.Storage.Parse(utils.PreviewIntroListenersIcon), r.PreviewIntro.IconURL)

		onlineCount = 512
		require.NotNil(r.PreviewIntro)
		resp.buildPreviewIntro()
		assert.Equal("500 + 人正在听", r.PreviewIntro.Title)
		assert.Equal(service.Storage.Parse(utils.PreviewIntroListenersIcon), r.PreviewIntro.IconURL)

		onlineCount = 3234
		resp.buildPreviewIntro()
		assert.Equal("3 千 + 人正在听", r.PreviewIntro.Title)
		assert.Equal(service.Storage.Parse(utils.PreviewIntroListenersIcon), r.PreviewIntro.IconURL)

		onlineCount = 53234
		resp.buildPreviewIntro()
		assert.Equal("5 万 + 人正在听", r.PreviewIntro.Title)
		assert.Equal(service.Storage.Parse(utils.PreviewIntroListenersIcon), r.PreviewIntro.IconURL)
	})

	t.Run("follower", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 114514,
			},
		}

		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.NotEqual(&room.PreviewIntro{
			Title:   "超 9 万粉宝藏主播",
			IconURL: service.Storage.Parse(utils.PreviewIntroFollowersIcon),
		}, r.PreviewIntro.Title)

		r = &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 3457183,
			},
		}
		resp.Room = r
		resp.buildPreviewIntro()
		require.NotNil(r.PreviewIntro)
		assert.Equal(&room.PreviewIntro{
			Title:   "超 7 万粉宝藏主播",
			IconURL: service.Storage.Parse(utils.PreviewIntroFollowersIcon),
		}, r.PreviewIntro)
	})

	t.Run("pk", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 3457183,
			},
		}

		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.NotEqual(&room.PreviewIntro{
			Title:   "主播正在连麦 PK",
			IconURL: service.Storage.Parse(utils.PreviewIntroPKIcon),
		}, r.PreviewIntro)

		r = &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 3457183,
				Status: room.Status{
					PK: 1,
				},
			},
		}

		resp.Room = r
		resp.buildPreviewIntro()
		require.NotNil(r.PreviewIntro)
		assert.Equal(&room.PreviewIntro{
			Title:   "主播正在连麦 PK",
			IconURL: service.Storage.Parse(utils.PreviewIntroPKIcon),
		}, r.PreviewIntro)
	})

	t.Run("multi connect", func(t *testing.T) {
		assert := assert.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID: 9074509,
			},
		}
		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.NotEqual(&room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroMultiConnectIcon),
			Title:   "主播正在多人连线",
		}, r.PreviewIntro)

		r.Status.MultiConnect = room.MultiConnectStatusOngoing
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroMultiConnectIcon),
			Title:   "主播正在多人连线",
		}, r.PreviewIntro)
	})

	t.Run("rank", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 1,
			},
		}

		zs := make([]*redis.Z, 0, 25)
		for i := 1; i <= 25; i++ {
			zs = append(zs, &redis.Z{
				Score:  float64(26 - i),
				Member: i,
			})
		}

		now := goutil.TimeNow()
		novaKey := usersrank.Key(usersrank.TypeNova, now)
		require.NoError(service.Redis.Del(novaKey).Err())
		err := service.Redis.ZAdd(novaKey, zs...).Err()
		require.NoError(err)

		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播新人榜排名第 1",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		monthKey := usersrank.Key(usersrank.TypeMonth, now)
		require.NoError(service.Redis.Del(monthKey).Err())
		err = service.Redis.ZAdd(monthKey, zs...).Err()
		require.NoError(err)
		defer service.Redis.Del(monthKey)

		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播月榜排名第 1",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		resp.Room.CreatorID = 19
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播月榜排名 TOP20",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		weekKey := usersrank.Key(usersrank.TypeWeek, now)
		require.NoError(service.Redis.Del(weekKey).Err())
		err = service.Redis.ZAdd(weekKey, zs...).Err()
		require.NoError(err)
		defer service.Redis.Del(weekKey)

		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播月榜排名 TOP20",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		resp.Room.CreatorID = 9
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播周榜排名第 9",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		dayKey := usersrank.Key(usersrank.TypeDay, now)
		require.NoError(service.Redis.Del(dayKey).Err())
		err = service.Redis.ZAdd(dayKey, zs...).Err()
		require.NoError(err)
		defer service.Redis.Del(dayKey)

		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播日榜排名第 9",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		lastHourKey := usersrank.Key(usersrank.TypeHour, now.Add(-time.Hour))
		require.NoError(service.Redis.Del(lastHourKey).Err())
		err = service.Redis.ZAdd(lastHourKey, zs...).Err()
		require.NoError(err)
		defer service.Redis.Del(lastHourKey)

		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播日榜排名第 9",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		resp.Room.CreatorID = 2
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播上小时榜排名第 2",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		hourKey := usersrank.Key(usersrank.TypeHour, now)
		require.NoError(service.Redis.Del(hourKey).Err())
		err = service.Redis.ZAdd(hourKey, zs...).Err()
		require.NoError(err)
		defer service.Redis.Del(hourKey)

		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播小时榜排名第 2",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)

		resp.Room.CreatorID = 10
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "直播小时榜排名 TOP10",
			IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
		}, r.PreviewIntro)
	})

	t.Run("red packet", func(t *testing.T) {
		assert := assert.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 114514,
				Status: room.Status{
					RedPacket: 1,
				},
			},
		}

		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "正在抢礼物红包",
			IconURL: service.Storage.Parse(utils.PreviewIntroRedPacketIcon),
		}, r.PreviewIntro)
	})

	t.Run("lucky bag", func(t *testing.T) {
		assert := assert.New(t)

		r := &room.Room{
			Helper: room.Helper{
				RoomID:    18113499,
				CreatorID: 114514,
				Status: room.Status{
					RedPacket: 1,
				},
			},
		}

		resp := &previewRoomResponse{Room: r, c: handler.NewTestContext(http.MethodGet, "", false, nil)}
		resp.buildPreviewIntro()
		assert.Equal(&room.PreviewIntro{
			Title:   "正在免费抽《广播剧 1》福袋",
			IconURL: service.Storage.Parse(utils.PreviewIntroLuckyBagIcon),
		}, r.PreviewIntro)
	})
}

func TestPreviewRoomResponse_buildWebSocket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &previewRoomResponse{
		Room: &room.Room{
			Helper: room.Helper{RoomID: roomID, CreatorID: testRoomCreatorID},
		},
		userID: normalUserID,
	}

	resp.buildWebSocket()
	require.NotEmpty(resp.WebSocket)
	assert.Equal(fmt.Sprintf("wss://fm.example.com:3016/ws?room_id=%d&preview=1", roomID), resp.WebSocket[0])

	resp.Room.Config = &room.Config{
		WebSocketURLs: []string{"wss://fm.example.com:3016/ws", "wss://fm.example.com:3016/ws?room_id=1"},
	}
	resp.buildWebSocket()
	assert.Equal("wss://fm.example.com:3016/ws?preview=1", resp.WebSocket[0])
	assert.Equal("wss://fm.example.com:3016/ws?room_id=1&preview=1", resp.WebSocket[1])
}

func TestPreviewRoomResponse_buildPreviewTags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": normalUserID, "room_id": roomID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Status: livemedal.StatusOwned,
			UserID: normalUserID,
			RoomID: roomID,
			Point:  1,
		},
	})
	require.NoError(err)
	cancelDramaCVs := mrpc.SetMock(userapi.URLGetUserDramaCVs, func(any) (any, error) {
		return handler.M{"cvs": []userapi.DramaCVInfo{
			{
				ID:     1,
				UserID: 233,
			},
			{
				ID:     2,
				UserID: testRoomCreatorID,
			},
		}}, nil
	})
	defer cancelDramaCVs()

	resp := &previewRoomResponse{
		Room: &room.Room{
			CatalogName: "test_catalog",
			CustomTag:   &tag.CustomTag{TagID: 1, TagName: "test_tag"},
			Helper: room.Helper{
				RoomID: roomID, CreatorID: testRoomCreatorID,
				Statistics: room.Statistics{Attention: false},
			},
		},
		c:      handler.NewTestContext(http.MethodGet, "", false, nil),
		userID: normalUserID,
	}
	resp.buildPreviewTags()
	assert.Equal("已点亮粉丝牌", resp.Room.PreviewTag)
	assert.Equal([]string{"已点亮粉丝牌", "我追的剧集声优", "test_catalog", "test_tag"}, resp.previewTags)

	resp.Room.Statistics.Attention = true
	resp.buildPreviewTags()
	assert.Equal([]string{"已点亮粉丝牌", "我关注的", "我追的剧集声优", "test_catalog", "test_tag"}, resp.previewTags)

	// 未登录用户
	resp = &previewRoomResponse{
		Room: &room.Room{
			CatalogName: "test_catalog",
			CustomTag:   &tag.CustomTag{TagID: 1, TagName: "test_tag"},
			Helper:      room.Helper{RoomID: roomID, CreatorID: testRoomCreatorID},
		},
		c: handler.NewTestContext(http.MethodGet, "", false, nil),
	}
	resp.buildPreviewTags()
	assert.Empty(resp.Room.PreviewTag)
	assert.Equal([]string{"test_catalog", "test_tag"}, resp.previewTags)
}

func TestPreviewRoomResponse_buildTrace(t *testing.T) {
	assert := assert.New(t)

	resp := &previewRoomResponse{
		Room: &room.Room{
			PreviewIntro: &room.PreviewIntro{Title: "test_intro"},
		},
	}
	resp.buildTrace()
	assert.Equal("{\"preview_intro_title\":\"test_intro\"}", resp.Trace)

	resp.previewTags = []string{"test_catalog", "test_tag"}
	resp.buildTrace()
	assert.Equal("{\"preview_tags\":[\"test_catalog\",\"test_tag\"],\"preview_intro_title\":\"test_intro\"}", resp.Trace)
}
