package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygiftdresses"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userdiygifts"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestDiyGiftInfoTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(giftDiy{}, "gift_id", "background", "words", "avatars", "dress")
	kc.Check(giftDiyBackground{}, "image_url", "width", "height")
	kc.Check(giftDiyWords{}, "image_url", "preview_position", "status", "text", "text_color")
	kc.Check(giftDiyAvatars{}, "status", "creator", "user")
	kc.Check(giftDiyIcon{}, "iconurl", "preview_position", "choose")
	kc.Check(giftDiyDress{}, "type", "name", "data")
	kc.Check(giftDiyDressElem{}, "dress_id", "icon_url", "image_url", "choose")
}

func TestNewDiyGiftInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "?room_id=-1", true, nil)
	_, err := newDiyGiftInfoParam(c)
	assert.Equal(actionerrors.ErrParams, err, "房间号不正确")

	c = handler.NewTestContext(http.MethodGet, "?room_id=12345", true, nil)
	_, err = newDiyGiftInfoParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("?room_id=%d", openingRoomID), true, nil)
	_, err = newDiyGiftInfoParam(c)
	assert.Equal(actionerrors.ErrParams, err, "礼物 ID 不正确")

	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("?room_id=%d&gift_id=123456", openingRoomID), true, nil)
	_, err = newDiyGiftInfoParam(c)
	assert.Equal(actionerrors.ErrNotFound("礼物不存在"), err)

	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("?room_id=%d&gift_id=301", openingRoomID), true, nil)
	param, err := newDiyGiftInfoParam(c)
	require.NoError(err)
	assert.NotNil(param.room)
	assert.NotNil(param.gift)
}

func TestDiyGiftInfoParamFindDiyGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	giftCol := diygifts.Collection()
	dressCol := diygiftdresses.Collection()
	testGiftID := int64(301)
	_, err := giftCol.DeleteMany(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)
	_, err = dressCol.DeleteMany(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)

	newParam := func() *diyGiftInfoParam {
		param := &diyGiftInfoParam{gift: &gift.Gift{GiftID: 301}}
		param.user = new(user.User)
		param.user.ID = 12
		return param
	}

	param := newParam()
	assert.Equal(actionerrors.ErrNotFound("该礼物无法定制"), param.findDiyGift())

	now := goutil.TimeNow()
	diyGift := diygifts.DiyGift{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		GiftID:       testGiftID,
		DressTypes:   []diygifts.DressType{{Type: 1}},
		Status:       diygifts.StatusOn,
	}
	res, err := giftCol.InsertOne(ctx, diyGift)
	require.NoError(err)
	diyGift.OID = res.InsertedID.(primitive.ObjectID)
	param = newParam()
	assert.Equal(actionerrors.ErrNotFound("无法找到定制信息"), param.findDiyGift())

	dress := diygiftdresses.DiyGiftDress{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		GiftID:       testGiftID,
		Type:         diyGift.DressTypes[0].Type,
		Order:        1,
	}
	res, err = dressCol.InsertOne(ctx, dress)
	require.NoError(err)
	dress.OID = res.InsertedID.(primitive.ObjectID)
	// 没有历史记录的情况
	param = newParam()
	require.NoError(param.findDiyGift())
	assert.NotNil(param.diyGift)
	assert.NotNil(param.dressMap)
	assert.Nil(param.userDiyGift)

	// 有历史记录但是 _diy_id 不正确的情况
	require.NoError(userdiygifts.Collection().FindOneAndUpdate(ctx,
		bson.M{"gift_id": testGiftID, "user_id": param.user.ID},
		bson.M{"$unset": bson.M{"_diy_id": 0}},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)).Err())
	param = newParam()
	require.NoError(param.findDiyGift())
	assert.NotNil(param.diyGift)
	assert.NotNil(param.dressMap)
	assert.Nil(param.userDiyGift)

	// 有历史记录并且 _diy_id 正确的情况
	require.NoError(userdiygifts.Collection().FindOneAndUpdate(ctx,
		bson.M{"gift_id": testGiftID, "user_id": param.user.ID},
		bson.M{"$set": bson.M{"_diy_id": diyGift.OID}},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)).Err())
	param = newParam()
	require.NoError(param.findDiyGift())
	assert.NotNil(param.diyGift)
	assert.NotNil(param.dressMap)
	assert.NotNil(param.userDiyGift)
}

func TestDiyGiftInfoParamResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	kc := tutil.NewKeyChecker(t, tutil.MapString)

	param := diyGiftInfoParam{
		gift: &gift.Gift{GiftID: 123},
		room: &room.Room{CreatorIconURL: "creator_iconurl"},
		user: &user.User{IUser: user.IUser{IconURL: "user_iconurl"}},
		diyGift: &diygifts.DiyGift{
			Background: diygifts.Background{
				Image:  "oss://background.png",
				Width:  100,
				Height: 200,
			},
			DressTypes: []diygifts.DressType{
				{Type: 1, Name: "1"},
				{Type: 2, Name: "2"},
			},
		},
		dressMap: map[int][]*diygiftdresses.DiyGiftDress{
			1: {
				{OID: primitive.NewObjectID(), Icon: "oss://icon1_1.png", Image: "oss://image1_1.png"},
				{OID: primitive.NewObjectID(), Icon: "oss://icon1_2.png", Image: "oss://image1_2.png"},
			},
			2: {
				{OID: primitive.NewObjectID(), Icon: "oss://icon2_1.png", Image: "oss://image2_1.png"},
				{OID: primitive.NewObjectID(), Icon: "oss://icon2_2.png", Image: "oss://image2_2.png"},
			},
		},
	}
	newGiftDiyDressElem := func(d *diygiftdresses.DiyGiftDress, choose bool) giftDiyDressElem {
		return giftDiyDressElem{
			DressID:  d.OID.Hex(),
			IconURL:  storage.ParseSchemeURL(d.Icon),
			ImageURL: storage.ParseSchemeURL(d.Image),
			Choose:   choose,
		}
	}

	respGiftDiy := func() *giftDiy {
		resp := param.resp()
		kc.Check(resp, "gift_diy")
		return resp["gift_diy"].(*giftDiy)
	}

	// 没有赠言，没有头像，没有历史记录
	giftDiy := respGiftDiy()
	assert.Equal(&giftDiyBackground{
		ImageURL: storage.ParseSchemeURL(param.diyGift.Background.Image),
		Width:    param.diyGift.Background.Width,
		Height:   param.diyGift.Background.Height,
	}, giftDiy.Background)
	assert.Nil(giftDiy.Words)
	assert.Nil(giftDiy.Avatars)
	assert.Equal([]giftDiyDress{
		{
			Type: 1,
			Name: "1",
			Data: []giftDiyDressElem{
				newGiftDiyDressElem(param.dressMap[1][0], true),
				newGiftDiyDressElem(param.dressMap[1][1], false),
			},
		},
		{
			Type: 2,
			Name: "2",
			Data: []giftDiyDressElem{
				newGiftDiyDressElem(param.dressMap[2][0], true),
				newGiftDiyDressElem(param.dressMap[2][1], false),
			},
		},
	}, giftDiy.Dress)

	// 有赠言、有头像、没有历史记录
	param.diyGift.Words = &diygifts.Words{
		Image:           "oss://1_2_3_4.png",
		PreviewPosition: []int{5, 6, 7, 8},
		TextColor:       "#000000",
	}
	param.diyGift.Avatars = &diygifts.Avatars{
		CreatorPreviewPosition: []int{9, 0, 1, 2},
		UserPreviewPosition:    []int{3, 4, 5, 6},
	}
	giftDiy = respGiftDiy()
	assert.Equal(&giftDiyWords{
		ImageURL:        storage.ParseSchemeURL(param.diyGift.Words.Image),
		PreviewPosition: param.diyGift.Words.PreviewPosition,
		Status:          giftDiyStatusNoHistory,
		TextColor:       param.diyGift.Words.TextColor,
	}, giftDiy.Words)
	assert.Equal(&giftDiyAvatars{
		Status: giftDiyStatusNoHistory,
		Creator: giftDiyIcon{
			IconURL:         param.room.CreatorIconURL,
			PreviewPosition: param.diyGift.Avatars.CreatorPreviewPosition,
		},
		User: giftDiyIcon{
			IconURL:         param.user.IconURL,
			PreviewPosition: param.diyGift.Avatars.UserPreviewPosition,
		},
	}, giftDiy.Avatars)

	// 有赠言、有头像、有历史记录（头像、赠言未勾选）
	param.userDiyGift = &userdiygifts.UserDiyGift{
		Dress: []userdiygifts.Dress{
			{Type: 1, DressOID: param.dressMap[1][1].OID},
			{Type: 2, DressOID: param.dressMap[1][0].OID}, // 模拟找不到的情况
		},
	}
	giftDiy = respGiftDiy()
	require.NotNil(giftDiy.Words)
	assert.Equal(giftDiyStatusChooseNone, giftDiy.Words.Status)
	assert.Empty(giftDiy.Words.Text)
	require.NotNil(giftDiy.Avatars)
	assert.Equal(giftDiyStatusChooseNone, giftDiy.Avatars.Status)
	assert.False(giftDiy.Avatars.Creator.Choose)
	assert.False(giftDiy.Avatars.User.Choose)
	assert.Equal([]giftDiyDress{
		{
			Type: 1,
			Name: "1",
			Data: []giftDiyDressElem{
				newGiftDiyDressElem(param.dressMap[1][0], false),
				newGiftDiyDressElem(param.dressMap[1][1], true),
			},
		},
		{
			Type: 2,
			Name: "2",
			Data: []giftDiyDressElem{
				newGiftDiyDressElem(param.dressMap[2][0], true),
				newGiftDiyDressElem(param.dressMap[2][1], false),
			},
		},
	}, giftDiy.Dress)

	// 有赠言、有头像、有历史记录（头像、赠言勾选）
	delete(param.dressMap, 2) // 模拟 len(giftDiy.Dress[i].Data) == 0 的情况
	param.userDiyGift.IconConfig.Set(userdiygifts.IconConfigCreator)
	param.userDiyGift.IconConfig.Set(userdiygifts.IconConfigUser)
	param.userDiyGift.Words = "test words"
	giftDiy = respGiftDiy()
	require.NotNil(giftDiy.Words)
	assert.Equal(giftDiyStatusChooseOne, giftDiy.Words.Status)
	assert.Equal(param.userDiyGift.Words, giftDiy.Words.Text)
	require.NotNil(giftDiy.Avatars)
	assert.Equal(giftDiyStatusChooseOne, giftDiy.Avatars.Status)
	assert.True(giftDiy.Avatars.Creator.Choose)
	assert.True(giftDiy.Avatars.User.Choose)
	require.Len(giftDiy.Dress, 2)
	assert.Empty(giftDiy.Dress[1].Data)
}
