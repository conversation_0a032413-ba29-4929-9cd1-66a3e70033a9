package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type setAdminParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`
	UserID int64 `form:"user_id" json:"user_id"`

	user *liveuser.User
	room *room.Room
}

func (param *setAdminParam) check(c *handler.Context, isAdd bool) error {
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.UserID <= 0 {
		return actionerrors.ErrParams
	}
	creator := c.User()
	param.room, err = room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		// 直播间不存在时报错
		return actionerrors.ErrCannotFindRoom
	}
	if creator.ID != param.room.CreatorID {
		return actionerrors.ErrNoAuthority
	}
	if creator.ID == param.UserID {
		if isAdd {
			// 设置房主为房管时时报错
			return actionerrors.ErrParamsMsg("不能将自己设为房管哦~")
		}
		// 房主将自己移出房管时，提示“操作失败”
		return actionerrors.ErrParamsMsg("操作失败")
	}
	// 被主播拉黑，无法设置为房管
	if isAdd {
		blocked, err := blocklist.IsBlocked(creator.ID, param.UserID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return actionerrors.NewErrBlockUser("此用户已经被您拉黑了")
		}
	}
	param.user, err = liveuser.Find(param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		// 被设置用户不存在时报错
		return actionerrors.ErrCannotFindUser
	}
	return nil
}

// ActionAddAdmin 新增房管
// NOTICE: 用户暂无确认是否是房管的地方，不对受限房间做限制
/**
 * @api {post} /api/v2/chatroom/admin/add 新增房管
 * @apiDescription 设置用户为房管将解除该用户在房间内的禁言
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample 响应
 *     {
 *       "code": 0,
 *       "info": {
 *         "admin": {
 *           "user_id": 233,
 *           "username": "加特林",
 *           "iconurl": "https://static-test.missevan.com/avatars/202101/07/test.png"
 *         },
 *         "msg": "添加成功"
 *       }
 *     }
 *
 * @apiSuccessExample im 消息
 *   [
 *     { // 给禁言用户设置房管会额外增加一条解除禁言消息
 *       "type": "member",
 *       "event": "remove_mute",
 *       "room_id": 112422405,
 *       "target": {
 *         "user_id": 9074736,
 *         "username": "maoer_DpT93oPt7Jjo",
 *         "iconurl": "http://static-test.missevan.com/avatars/icon01.png"
 *       },
 *       "user": {
 *         "user_id": 3456835
 *       }
 *     },
 *     {
 *       "type": "member",
 *       "event": "add_admin",
 *       "room_id": 112422405,
 *       "target": {
 *         "user_id": 9074736,
 *         "username": "maoer_DpT93oPt7Jjo",
 *         "iconurl": "http://static-test.missevan.com/avatars/icon01.png"
 *       },
 *       "user": {
 *         "user_id": 3456835
 *       }
 *     }
 *   ]
 */
func ActionAddAdmin(c *handler.Context) (handler.ActionResponse, error) {
	var param setAdminParam
	if err := param.check(c, true); err != nil {
		return nil, err
	}
	isStaff, _, err := liveuser.IsStaff(param.user)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if isStaff {
		// 设置超管为房管时报错
		return nil, actionerrors.ErrForbidden
	}
	// 设置房管数据
	isRemoveMute, err := setRoomAdmin(param.user, param.room.OID, param.room.RoomID, c.User().ID, true)
	if err != nil {
		// 该直播间房管数量超过上限或数据库操作出错时报错
		return nil, err
	}
	// 发送广播通知直播间内用户
	adminInfo := map[string]interface{}{
		"user_id":  param.user.UID,
		"username": param.user.Username,
		"iconurl":  param.user.IconURL,
	}
	payloads := make([]map[string]interface{}, 0, 2)
	if isRemoveMute {
		// 广播解除禁言消息
		payloads = append(payloads, map[string]interface{}{
			"type":    liveim.TypeMember,
			"event":   liveim.EventRemoveMute,
			"room_id": param.room.RoomID,
			"target":  adminInfo,
			"user": map[string]interface{}{
				"user_id": c.User().ID,
			},
		})
	}
	// 广播设置为房管消息
	payloads = append(payloads, map[string]interface{}{
		"type":    liveim.TypeMember,
		"event":   liveim.EventAddAdmin,
		"room_id": param.room.RoomID,
		"target":  adminInfo,
		"user": map[string]interface{}{
			"user_id": c.User().ID,
		},
	})
	err = userapi.Broadcast(param.room.RoomID, payloads)
	if err != nil {
		logger.WithField("room_id", param.room.RoomID).Errorf("RAdd admin broadcast failed: %v", err)
		// PASS: 广播失败时记录日志，不对用户报错
	}
	return handler.M{
		"admin": adminInfo,
		"msg":   "添加成功",
	}, nil
}

// ActionRemoveAdmin 移除房管
/**
 * @api {post} /api/v2/chatroom/admin/remove 移除房管
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample 响应
 *     {
 *       "code": 0,
 *       "info": {
 *         "admin": {
 *           "user_id": 233,
 *           "username": "加特林",
 *           "iconurl": "https://static-test.missevan.com/avatars/202101/07/test.png"
 *         },
 *         "msg": "移除成功"
 *       }
 *     }
 *
 * @apiSuccessExample im 消息
 *   {
 *     "type": "member",
 *     "event": "remove_admin",
 *     "room_id": 112422405,
 *     "target": {
 *       "user_id": 9074736,
 *       "username": "maoer_DpT93oPt7Jjo",
 *       "iconurl": "http://static-test.missevan.com/avatars/icon01.png"
 *     },
 *     "user": {
 *       "user_id": 3456835
 *     }
 *   }
 */
func ActionRemoveAdmin(c *handler.Context) (handler.ActionResponse, error) {
	var param setAdminParam
	if err := param.check(c, false); err != nil {
		return nil, err
	}
	// 设置房管数据
	_, err := setRoomAdmin(param.user, param.room.OID, param.room.RoomID, c.User().ID, false)
	if err != nil {
		// 该直播间房管数量超过上限或数据库操作出错时报错
		return nil, err
	}
	// 发送广播通知直播间内用户
	adminInfo := map[string]interface{}{
		"user_id":  param.user.UID,
		"username": param.user.Username,
		"iconurl":  param.user.IconURL,
	}
	payload := map[string]interface{}{
		"type":    liveim.TypeMember,
		"event":   liveim.EventRemoveAdmin,
		"room_id": param.room.RoomID,
		"target":  adminInfo,
		"user": map[string]interface{}{
			"user_id": c.User().ID,
		},
	}
	err = userapi.Broadcast(param.room.RoomID, payload)
	if err != nil {
		logger.WithField("room_id", param.room.RoomID).Errorf("Remove admin broadcast failed: %v", err)
		// PASS
	}
	return handler.M{
		"admin": adminInfo,
		"msg":   "移除成功",
	}, nil
}

// setRoomAdmin 设置房管
func setRoomAdmin(user *liveuser.User, roomOID primitive.ObjectID, roomID, operatorID int64, set bool) (isRemoveMute bool, err error) {
	adminCount, err := livemembers.CountAdmins(roomID)
	if err != nil {
		return isRemoveMute, actionerrors.NewErrServerInternal(err, nil)
	}
	if set && adminCount >= livemembers.AdminLimit {
		return isRemoveMute, actionerrors.ErrParamsMsg(fmt.Sprintf("最多可设置 %d 位管理员", livemembers.AdminLimit))
	}
	isRemoveMute, err = livemembers.SetRoomAdmin(user, roomOID, roomID, operatorID, set)
	if err != nil {
		return isRemoveMute, actionerrors.NewErrServerInternal(err, nil)
	}
	return isRemoveMute, nil
}
