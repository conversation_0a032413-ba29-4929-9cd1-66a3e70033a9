package gashapon

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGashaponTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(gashaponGoodsResp{}, "goods_list", "energy", "buff", "rule")
}

func TestActionGashaponGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.LRURedis.Del(keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeGashapon)).Err())
	require.NoError(service.LRURedis.Del(keys.KeyParams1.Format(params.KeyGashapon)).Err())
	c := handler.NewTestContext(http.MethodGet, "/gashapon/goods", false, nil)
	_, err := ActionGashaponGoods(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/gashapon/goods?room_id=%d", room.TestNonexistentRoomID), false, nil)
	_, err = ActionGashaponGoods(c)
	require.Equal(actionerrors.ErrCannotFindRoom, err)

	roomID := int64(22489473)
	pool, err := gift.FindPoolGashapon(3, roomID)
	require.NoError(err)
	require.NotNil(pool)
	pool.PoolID = -1
	pool.CurrentBuff = &pool.BuffRates[0]
	require.NoError(service.Redis.Set(keys.KeyGashaponPoolRoom1.Format(roomID),
		tutil.SprintJSON(pool), 10*time.Second).Err())
	_, err = room.Update(roomID, bson.M{"energy": room.GashaponEnergy{Value: 10}})
	require.NoError(err)

	// 设置魔方配置缓存，只用到了最大能量值
	gashaponCfg := params.Gashapon{
		MaxEnergy: 100,
	}
	err = service.LRURedis.Set(keys.KeyParams1.Format(params.KeyGashapon),
		tutil.SprintJSON(gashaponCfg), 10*time.Second).Err()
	require.NoError(err)
	gashaponCfg, err = params.FindGashapon()
	require.NoError(err)
	assert.NotZero(gashaponCfg.MaxEnergy)
	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/gashapon/goods?room_id=%d", roomID), false, nil)
	r, err := ActionGashaponGoods(c)
	require.NoError(err)
	resp := r.(*gashaponGoodsResp)
	require.NotNil(resp)
	assert.Len(resp.GoodsList, 3)
	assert.Equal(room.GashaponEnergy{
		Value:    10,
		MaxValue: gashaponCfg.MaxEnergy,
	}, resp.Energy)
	assert.Nil(resp.Buff, "当前 buff 和商品列表不符")

	// buff 是当前商品的 buff
	lg, err := livegoods.Find(resp.GoodsList[0].ID, livegoods.GoodsTypeGashapon)
	require.NoError(err)
	require.NotNil(lg)
	more, err := lg.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)
	pool.PoolID = more.PoolID
	require.NoError(service.Redis.Set(keys.KeyGashaponPoolRoom1.Format(roomID),
		tutil.SprintJSON(pool), 10*time.Second).Err())
	c = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/gashapon/goods?room_id=%d", roomID), false, nil)
	r, err = ActionGashaponGoods(c)
	require.NoError(err)
	resp = r.(*gashaponGoodsResp)
	require.NotNil(resp)
	assert.NotNil(resp.Buff, "当前 buff 和商品列表相符")
}

func TestActionGashaponPrizeList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/gashapon/prize/list", false, nil)
	r, err := ActionGashaponPrizeList(c)
	require.NoError(err)
	var resp gashaponPrizeListResp
	require.IsType(resp, r)
	resp = r.(gashaponPrizeListResp)
	assert.NotNil(resp.Data)
}
