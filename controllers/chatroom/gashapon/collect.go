package gashapon

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionGashaponCollectProgress 魔方任务收集进度详情
/**
 * @api {get} /api/v2/chatroom/gashapon/collect-progress 魔方任务收集进度详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccessExample {json} 收集阶段:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "status": 1, // 1: 收集阶段, 2: 过渡阶段, 3: 狂欢阶段, 4: 预告阶段
 *       "start_time": 1716441600, // 收集阶段开始时间, 秒级时间戳
 *       "end_time": 1716441600, // 收集阶段结束时间, 秒级时间戳
 *       "remain_duration": 100, // 收集阶段剩余时间, 单位毫秒
 *       "global_task": { // 全站任务，大奖礼物数量
 *         "collection": {
 *           "name": "机甲之心",
 *           "icon_url": "https://static.missevan.com/gift/1.png"
 *         },
 *         "current_num": 1, // 当前收集数量
 *         "prize_name": "机甲之心", // 翻倍礼物名称
 *         "tasks": [
 *           {
 *             "num": 10, // 目标数量
 *             "multiplier": "2.5" // 目标倍率
 *           },
 *           {
 *             "num": 80,
 *             "multiplier": "3"
 *           },
 *           {
 *             "num": 100,
 *             "multiplier": "4"
 *           }
 *         ]
 *       },
 *       "room_task": { // 直播间任务，魔方赠送数量
 *         "collection": {
 *           "name": "织梦奇境",
 *           "icon_url": "https://static.missevan.com/gift/3.png"
 *         },
 *         "current_num": 1, // 当前赠送数量
 *         "prize_name": "机甲之心", // 翻倍礼物名称
 *         "tasks": [
 *           {
 *             "level": 1, // 升级等级
 *             "num": 45, // 目标数量
 *             "multiplier": "2.5" // 目标倍率，根据全站翻倍实时计算出的，若当前没有完成任何全站任务，则不返回
 *           },
 *           {
 *             "level": 2, // 升级等级
 *             "num": 90,
 *             "multiplier": "3"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} 过渡阶段:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "status": 2, // 1: 收集阶段, 2: 过渡阶段, 3: 狂欢阶段, 4: 预告阶段
 *       "start_time": 1716441600, // 过渡阶段开始时间, 秒级时间戳
 *       "end_time": 1716441600, // 过渡阶段结束时间, 秒级时间戳
 *       "remain_duration": 100, // 过渡阶段剩余时间, 单位秒
 *       "collection": {
 *         "name": "机甲之心",
 *         "icon_url": "https://static.missevan.com/gift/2.png"
 *       },
 *       "multiplier": "2.5", // 狂欢阶段最终倍率，该字段为空，表示狂欢开启失败
 *       "global_multiplier": "2.0", // 全站任务升级倍率
 *       "room_level": 1 // 直播间任务升级等级，该字段为空，表示直播间未升级
 *     }
 *   }
 *
 * @apiSuccessExample {json} 狂欢阶段:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "status": 3, // 1: 收集阶段, 2: 过渡阶段, 3: 狂欢阶段, 4: 预告阶段
 *       "start_time": 1716441600, // 狂欢阶段开始时间, 秒级时间戳
 *       "end_time": 1716441600, // 狂欢阶段结束时间, 秒级时间戳
 *       "remain_duration": 100, // 狂欢阶段剩余时间, 单位毫秒
 *       "collection": {
 *         "name": "机甲之心",
 *         "icon_url": "https://static.missevan.com/gift/2.png"
 *       },
 *       "multiplier": "2.5", // 狂欢阶段最终倍率，该字段为空，表示狂欢开启失败
 *       "global_multiplier": "2.0", // 全站任务达成的升级倍率
 *       "room_level": 1 // 直播间任务升级等级，该字段为空，表示直播间未升级
 *     }
 *   }
 *
 * @apiSuccessExample {json} 预告阶段:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "status": 4 // 1: 收集阶段, 2: 过渡阶段, 3: 狂欢阶段, 4: 预告阶段
 *     }
 *   }
 */
func ActionGashaponCollectProgress(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
