package gashapon

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler 注册了扭蛋机相关的接口
func Handler() handler.Handler {
	return handler.Handler{
		Name: "gashapon",
		Actions: map[string]*handler.Action{
			"goods":      handler.NewAction(handler.GET, ActionGashaponGoods, false),
			"gacha":      handler.NewAction(handler.POST, ActionGashaponGacha, false),
			"history":    handler.NewAction(handler.GET, ActionGashaponHistory, true),
			"rank":       handler.NewAction(handler.GET, ActionGashaponRank, false),
			"prize/list": handler.<PERSON>Action(handler.GET, ActionGashaponPrizeList, false),
		},
	}
}
