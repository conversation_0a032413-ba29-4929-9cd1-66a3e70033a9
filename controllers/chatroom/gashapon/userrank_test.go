package gashapon

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestGashaponRankRespBuildGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testPoolID = int64(4)
	)
	_, err := gift.CollectionDrawPool().UpdateOne(
		context.Background(),
		bson.M{
			"pool_id": testPoolID,
		},
		bson.M{
			"$set": bson.M{
				"type":          gift.PoolTypeGashapon,
				"rates":         map[int64]int{},
				"rank_gift_ids": []int64{10070, 10071, 10072},
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	r := gashaponRankResp{}
	require.NoError(r.buildGifts(testPoolID))
	assert.Len(r.poolGifts, 3)
	for _, poolGift := range r.poolGifts {
		assert.NotEmpty(poolGift.GiftID)
		assert.NotEmpty(poolGift.IconURL)
	}
}

func TestGashaponRankRespBuildRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := gashaponRankResp{
		key: "2006-01-02",
	}
	err := r.buildRank()
	require.NoError(err)
	require.NotNil(r.Data)
	assert.Len(r.Data, 3)
	for i, item := range r.Data {
		assert.NotEmpty(item.UserID)
		assert.EqualValues(i+1, item.Rank)
		assert.NotEmpty(item.Score)
		require.Len(item.Achievement, 3)
		for _, achievement := range item.Achievement {
			assert.NotEmpty(achievement.GiftID)
			assert.NotEmpty(achievement.IconURL)
		}
	}
}

func TestGashaponRankRespBuildMyRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := gashaponRankResp{
		key: "2006-01-02",
	}
	err := r.buildGifts(4)
	require.NoError(err)

	c := handler.NewTestContext("GET", "chatroom/gashapon/rank/user?p=1&pagesize=50", false, nil)
	err = r.buildMyRank(c)
	require.NoError(err)
	require.Nil(r.MyRank)

	c = handler.NewTestContext("GET", "chatroom/gashapon/rank/user?p=1&pagesize=50", true, nil)
	err = r.buildMyRank(c)
	require.NoError(err)
	require.NotNil(r.MyRank)

	assert.NotEmpty(r.MyRank.UserID)
	assert.NotEmpty(r.MyRank.Username)
	assert.NotEmpty(r.MyRank.IconURL)
	assert.NotEmpty(r.MyRank.Score)
	require.Len(r.MyRank.Achievement, 3)
	for _, achievement := range r.MyRank.Achievement {
		assert.NotEmpty(achievement.GiftID)
		assert.NotEmpty(achievement.IconURL)
	}
}
