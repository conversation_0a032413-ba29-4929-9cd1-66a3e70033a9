package gashapon

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const maxLimit = 50

type gashaponGift struct {
	GiftID       int64  `json:"gift_id,omitempty"`
	GiftName     string `json:"gift_name,omitempty"`
	GiftIconURL  string `json:"gift_icon_url,omitempty"` // 仅魔方新消息结构下发
	Price        int64  `json:"price,omitempty"`         // 礼物价格, 单位钻石
	Num          int64  `json:"num,omitempty"`
	IconURL      string `json:"icon_url,omitempty"`
	EffectURL    string `json:"effect_url,omitempty"`
	WebEffectURL string `json:"web_effect_url,omitempty"`
}

type gashaponHistoryElem struct {
	List            []*gashaponGift `json:"gifts"`
	CreateTime      int64           `json:"create_time"`
	CreatorID       int64           `json:"creator_id"`
	CreatorUserName string          `json:"creator_username"`
}

type gashaponHistoryResp struct {
	Data []*gashaponHistoryElem `json:"data"`
}

// ActionGashaponHistory 扭蛋抽奖记录
/**
 * @api {get} /api/v2/chatroom/gashapon/history 扭蛋抽奖记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "gifts": [
 *             {
 *               "gift_id": 1,
 *               "num": 1,
 *               "icon_url": "url",
 *             },
 *           ],
 *           "create_time": 1631092186,
 *           "creator_id": 1,
 *           "creator_username": "name"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGashaponHistory(c *handler.Context) (handler.ActionResponse, error) {
	historyList, _, err := livetxnorder.BuyerHistoryListByGoodsType(c.User().ID, livegoods.GoodsTypeGashapon, 1, maxLimit)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(historyList) == 0 {
		return gashaponHistoryResp{Data: []*gashaponHistoryElem{}}, nil
	}

	creatorList := make([]int64, 0, len(historyList))
	giftIDs := make([]int64, 0, 64)
	for _, order := range historyList {
		creatorList = append(creatorList, order.SellerID)
		for _, g := range order.More.Gifts {
			giftIDs = append(giftIDs, g.GiftID)
		}
	}

	users, err := mowangskuser.FindSimpleMap(creatorList)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	giftsMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	list := make([]*gashaponHistoryElem, 0, len(historyList))
	for _, order := range historyList {
		var element gashaponHistoryElem
		element.List = make([]*gashaponGift, 0, len(order.More.Gifts))
		element.CreateTime = order.CreateTime
		for _, g := range order.More.Gifts {
			var gg gashaponGift
			gg.IconURL = giftsMap[g.GiftID].Icon
			gg.Num = int64(g.Num)
			gg.GiftID = g.GiftID
			element.List = append(element.List, &gg)
		}
		if user := users[order.SellerID]; user != nil {
			element.CreatorID = user.ID
			element.CreatorUserName = user.Username
		}
		list = append(list, &element)
	}
	return gashaponHistoryResp{Data: list}, nil
}
