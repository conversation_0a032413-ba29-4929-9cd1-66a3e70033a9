package gashapon

import (
	"encoding/json"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const gashaponPrizeMaxCount = 100

type gashaponGoodsResp struct {
	GoodsList []utils.Goods       `json:"goods_list"`
	Energy    room.GashaponEnergy `json:"energy"`
	Buff      *gift.GashaponBuff  `json:"buff,omitempty"`
	Rule      string              `json:"rule"`
}

// ActionGashaponGoods 获取扭蛋商品信息和能量值
/**
 * @api {get} /api/v2/chatroom/gashapon/goods 获取扭蛋商品信息和能量值
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "goods_list": [
 *         {
 *           "id": 15,
 *           "num": 10,
 *           "price": 100,
 *           "title": "10 连 100 钻",
 *           "icon_url": "http://static.example.com/icon_url.png"
 *         }
 *       ],
 *       "energy": {
 *         "value": 100,
 *         "max_value": 400
 *       },
 *       "buff": {  // 没有 buff 不返回
 *         "gift_id": 1,
 *         "name": "gift_name",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/10004.png",
 *         "buff_duration": 180000, // 总时长
 *         "remain_duration": 1000, // 剩余时间，毫秒
 *         "multiplier": "1.5", // 倍率
 *       },
 *       "rule": "https://link.missevan.com/fm/gashapon-guide",
 *       "collect": {
 *         "status": 1, // 1: 收集阶段, 2: 过渡阶段, 3: 狂欢阶段, 4: 预告阶段
 *         "remain_duration": 100, // 对应阶段剩余时间, 预告阶段无该字段, 单位秒
 *         "multiplier": "1.5", // 倍率，空字符串或不返回表示未获得
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGashaponGoods(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	// 最大能量值配置化后，需要单独获取并赋值
	gashaponCfg, err := params.FindGashapon()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	r.Energy.MaxValue = gashaponCfg.MaxEnergy

	goodsList, err := livegoods.ListLiveGoods(livegoods.GoodsTypeGashapon)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(goodsList) == 0 {
		return &gashaponGoodsResp{
			GoodsList: make([]utils.Goods, 0),
			Energy:    r.Energy,
			Rule:      gashaponCfg.Rule,
		}, nil
	}
	buff, err := gift.FindGashaponBuff(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if buff != nil {
		// 同一批礼物 pool_id 是一致的
		more, err := goodsList[0].UnmarshalMore()
		if err != nil {
			logger.WithField("goods_id", goodsList[0].ID).Error(err)
			// PASS
		} else if more == nil {
			logger.WithField("goods_id", goodsList[0].ID).Error("商品配置异常")
		} else if buff.PoolID != more.PoolID {
			// 当前 buff 和商品列表不符
			buff = nil
		}
	}
	return &gashaponGoodsResp{
		GoodsList: utils.LiveGoodsToGoods(goodsList),
		Energy:    r.Energy,
		Buff:      buff,
		Rule:      gashaponCfg.Rule,
	}, nil
}

type gashaponPrize struct {
	Username    string `json:"username"`
	GashaponNum int    `json:"gashapon_num"`
	GiftName    string `json:"gift_name"`
	GiftNum     int    `json:"gift_num"`
	Grand       bool   `json:"grand"`
}

type gashaponPrizeListResp struct {
	Data []gashaponPrize `json:"data"`
}

// ActionGashaponPrizeList 获取扭蛋商品中奖公示，最多显示 100 条记录
/**
 * @api {get} /api/v2/chatroom/gashapon/prize/list 获取扭蛋商品中奖公示，最多显示 100 条记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "username": "甲",
 *           "gashapon_num": 10, // 连抽数量
 *           "gift_name": "礼物名称",
 *           "gift_num": 10,
 *           "grand": true
 *         },
 *         {
 *           "username": "乙",
 *           "gashapon_num": 1,
 *           "gift_name": "礼物名称",
 *           "gift_num": 10,
 *           "grand": false
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGashaponPrizeList(*handler.Context) (handler.ActionResponse, error) {
	key := keys.KeyGashaponPrizeList0.Format()
	strList, err := service.Redis.LRange(key, 0, gashaponPrizeMaxCount-1).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	gashaponPrizeList := make([]gashaponPrize, 0, len(strList))
	for _, str := range strList {
		prize := gashaponPrize{}
		err := json.Unmarshal([]byte(str), &prize)
		if err != nil {
			logger.Error(err)
			continue
			// PASS
		}
		gashaponPrizeList = append(gashaponPrizeList, prize)
	}
	return gashaponPrizeListResp{
		Data: gashaponPrizeList,
	}, nil
}
