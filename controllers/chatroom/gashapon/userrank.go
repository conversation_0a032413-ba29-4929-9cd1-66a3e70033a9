package gashapon

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegashaponuserrank"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type rankElem struct {
	UserID      int64              `json:"user_id"`
	Username    string             `json:"username"`
	IconURL     string             `json:"iconurl"`
	Rank        int64              `json:"rank"`
	Score       int64              `json:"score"`
	Achievement []*rankAchievement `json:"achievement"`
}

type rankAchievement struct {
	GiftID  int64  `json:"gift_id"`
	IconURL string `json:"icon_url,omitempty"`
	Num     int    `json:"num"`
}

func (r rankAchievement) new(num int) *rankAchievement {
	cr := r
	cr.Num = num
	return &cr
}

type gashaponRankResp struct {
	Data   []*rankElem `json:"data"`
	MyRank *rankElem   `json:"my_rank,omitempty"`

	key       string
	poolGifts []rankAchievement
}

// ActionGashaponRank 扭蛋用户榜
/**
 * @api {get} /api/v2/chatroom/gashapon/rank 扭蛋用户榜
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "user_id": 1631092186,
 *           "username": "甲乙丙",
 *           "iconurl": "url",
 *           "rank": 1,
 *           "score": 100,
 *           "achievement": [
 *             {
 *               "gift_id": 1,
 *               "num": 1,
 *               "icon_url": "url"
 *             },
 *             {
 *               "gift_id": 2,
 *               "num": 1,
 *               "icon_url": "url"
 *             },
 *             {
 *               "gift_id": 3,
 *               "num": 0,
 *               "icon_url": "url"
 *             }
 *           ]
 *         }
 *       ],
 *       "my_rank": {
 *         "user_id": 23,
 *         "username": "test23",
 *         "iconurl": "http://aaa.bbb.ccc/test23.png",
 *         "rank": 0, // 当前用户未上榜的话，这里返回的 rank 为 0
 *         "score": 100,
 *         "achievement": [
 *           {
 *             "gift_id": 1,
 *             "num": 1,
 *             "icon_url": "url"
 *           },
 *           {
 *             "gift_id": 2,
 *             "num": 1,
 *             "icon_url": "url"
 *           },
 *           {
 *             "gift_id": 3,
 *             "num": 0,
 *             "icon_url": "url"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGashaponRank(c *handler.Context) (handler.ActionResponse, error) {
	var resp gashaponRankResp
	resp.key = livegashaponuserrank.Key(goutil.TimeNow())
	err := resp.buildRank()
	if err != nil {
		return nil, err
	}
	err = resp.buildMyRank(c)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (r *gashaponRankResp) buildGifts(poolID int64) error {
	poolGashapon, err := gift.FindPoolGashaponInMongo(poolID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if poolGashapon == nil {
		logger.Errorf("奖池 %d 不存在", poolID)
		return actionerrors.ErrCannotFindResource
	}
	gm, err := gift.FindGiftMapByGiftIDs(poolGashapon.RankGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	r.poolGifts = make([]rankAchievement, 0, 3)
	for _, rankGift := range poolGashapon.RankGiftIDs {
		g := gm[rankGift]
		if g == nil {
			continue
		}
		r.poolGifts = append(r.poolGifts, rankAchievement{
			GiftID:  g.GiftID,
			IconURL: g.Icon,
		})
		// 礼物最多只返回三个
		if len(r.poolGifts) == 3 {
			break
		}
	}
	return nil
}

func (r *gashaponRankResp) buildRank() error {
	list, err := livegashaponuserrank.RankList(
		bson.M{"key": r.key},
		options.Find().SetSort(livegashaponuserrank.SortScoreDesc).SetLimit(50),
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	r.Data = make([]*rankElem, 0, len(list))
	if len(list) == 0 {
		return nil
	}

	err = r.buildGifts(list[0].PoolID)
	if err != nil {
		return err
	}

	userIDs := make([]int64, 0, len(list))
	for i, rank := range list {
		userIDs = append(userIDs, rank.UserID)
		gifts := make([]*rankAchievement, len(r.poolGifts))
		for j, g := range r.poolGifts {
			key := livegashaponuserrank.GiftKey(g.GiftID)
			gifts[j] = g.new(rank.Achievement[key])
		}
		r.Data = append(r.Data, &rankElem{
			UserID:      rank.UserID,
			Rank:        int64(i) + 1,
			Score:       rank.Score,
			Achievement: gifts,
		})
	}

	simpleMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, item := range r.Data {
		simple := simpleMap[item.UserID]
		if simple == nil {
			continue
		}
		item.Username = simple.Username
		item.IconURL = simple.IconURL
	}
	return nil
}

func (r *gashaponRankResp) buildMyRank(c *handler.Context) error {
	u := c.User()
	if u == nil {
		return nil
	}
	for _, item := range r.Data {
		if item.UserID == u.ID {
			r.MyRank = item
			return nil
		}
	}

	myRank, err := livegashaponuserrank.FindRank(
		bson.M{
			"key":     r.key,
			"user_id": u.ID,
		},
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	r.MyRank = &rankElem{
		UserID:      u.ID,
		Username:    u.Username,
		IconURL:     u.IconURL,
		Achievement: make([]*rankAchievement, len(r.poolGifts)),
	}
	if myRank == nil {
		for i, p := range r.poolGifts {
			r.MyRank.Achievement[i] = p.new(0)
		}
		return nil
	}

	r.MyRank.Score = myRank.Score
	for i, p := range r.poolGifts {
		key := livegashaponuserrank.GiftKey(p.GiftID)
		r.MyRank.Achievement[i] = p.new(myRank.Achievement[key])
	}
	return nil
}
