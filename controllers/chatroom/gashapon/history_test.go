package gashapon

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGashaponHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/gashapon/history", true, nil)
	resp, err := ActionGashaponHistory(c)
	require.NoError(err)

	r := resp.(gashaponHistoryResp)
	require.NotEmpty(r.Data)
	require.NotEmpty(r.Data[0].List)
	assert.EqualValues(3, r.Data[0].List[0].Num)
	assert.EqualValues(10070, r.Data[0].List[0].GiftID)
	assert.NotEmpty(r.Data[0].List[0].IconURL)
	assert.EqualValues(10, r.Data[0].CreatorID)
	assert.EqualValues(9, r.Data[0].CreateTime)
	assert.Equal("bless", r.Data[0].CreatorUserName)
}
