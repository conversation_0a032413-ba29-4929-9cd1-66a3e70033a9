package gashapon

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("gashapon", h.Name)
	tutil.NewKeyChecker(t, tutil.Actions).Check(h, "goods", "gacha", "history", "rank", "prize/list")
}
