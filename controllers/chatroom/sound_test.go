package chatroom

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/msound"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSoundRecommendTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(soundRecommendResp{}, "strategy_id", "message", "msg", "room")
	kc.Check(cvInfo{}, "cv_id", "cv_name", "user_id", "main", "character")
	kc.Check(cvUserResp{}, "drama_id", "cv_info", "drama_no_live_recommend")
}

func TestNewSoundRecommendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/", false, nil)
	_, err := newSoundRecommendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "?sound_id=123", false, nil)
	_, err = newSoundRecommendParam(c)
	assert.Equal(actionerrors.ErrNotFound("音频不存在"), err)

	c = handler.NewTestContext(http.MethodGet, "?sound_id=44809", false, nil)
	param, err := newSoundRecommendParam(c)
	require.NoError(err)
	assert.EqualValues(44809, param.soundID)
	assert.NotNil(param.sound)
	assert.NotNil(c)
	assert.NotNil(param.findOpt)
	assert.NotEmpty(param.Key, "判断是否查询了 params")
}

func TestNewSoundRecommendResp(t *testing.T) {
	assert := assert.New(t)

	param := soundRecommendParam{
		c:              handler.NewTestContext(http.MethodGet, "/sound/recommend", false, nil),
		SoundRecommend: params.DefaultSoundRecommend(),
	}
	r := &room.Simple{
		CreatorUsername: "<>",
	}

	// 没有推荐
	resp := param.newSoundRecommendResp(params.SRStrategyNone, nil, nil)
	assert.Equal(0, resp.StrategyID)
	// UP 主推荐
	resp = param.newSoundRecommendResp(params.SRStrategyUP, r, nil)
	assert.Equal(1, resp.StrategyID)
	assert.NotNil(resp.Room)
	assert.Empty(resp.Message)
	assert.Equal(`<font color="#FFFFFF">UP 主 </font>`+
		`<font color="#ED7760">&lt;&gt;</font>`+
		`<font color="#FFFFFF"> 正在直播！</font>`, resp.Msg)
	// CV 推荐
	cv := &cvInfo{
		Character: "<角色名>",
		CVName:    "<CV 名>",
		Main:      mainLeading,
	}
	resp = param.newSoundRecommendResp(params.SRStrategyCV, r, cv)
	assert.Equal(2, resp.StrategyID)
	assert.Equal(
		`<font color="#ED7760">&lt;角色名&gt;</font>`+
			`<font color="#FFFFFF"> 配音 CV </font>`+
			`<font color="#ED7760">&lt;CV 名&gt;</font>`+
			`<font color="#FFFFFF"> 正在直播</font>`, resp.Msg)
	cv.Main = mainExtra
	resp = param.newSoundRecommendResp(params.SRStrategyCV, r, cv)
	assert.Equal(
		`<font color="#FFFFFF">参演 CV </font>`+
			`<font color="#ED7760">&lt;CV 名&gt;</font>`+
			`<font color="#FFFFFF"> 正在直播</font>`, resp.Msg)
	// 直播推荐
	param.Live.MessagePool = []string{"test>"}
	resp = param.newSoundRecommendResp(params.SRStrategyLive, r, nil)
	assert.Equal(3, resp.StrategyID)
	assert.Equal(`<font color="#FFFFFF">test&gt;</font>`, resp.Msg)
	// CV 推荐但是没有 CV 信息，降级成直播推荐
	resp = param.newSoundRecommendResp(params.SRStrategyCV, r, nil)
	assert.Equal(3, resp.StrategyID)
	assert.Equal(`<font color="#FFFFFF">test&gt;</font>`, resp.Msg)

	// 老版本
	e := param.c.Equip()
	e.FromApp = true
	e.AppVersion = "6.0.7"
	e.OS = goutil.Android
	resp = param.newSoundRecommendResp(params.SRStrategyUP, r, nil)
	assert.Equal(1, resp.StrategyID)
	assert.NotNil(resp.Room)
	assert.Empty(resp.Msg)
	assert.Equal(`UP 主 <> 正在直播！`, resp.Message)

	cv.Main = mainSupporting
	resp = param.newSoundRecommendResp(params.SRStrategyCV, r, cv)
	assert.Equal(2, resp.StrategyID)
	assert.Equal(`<角色名> 配音 CV <CV 名> 正在直播`, resp.Message)

	cv.Character = ""
	resp = param.newSoundRecommendResp(params.SRStrategyCV, r, cv)
	assert.Equal(`参演 CV <CV 名> 正在直播`, resp.Message)

	resp = param.newSoundRecommendResp(params.SRStrategyLive, r, nil)
	assert.Equal(3, resp.StrategyID)
	assert.Equal("test>", resp.Message)
}

func TestNoRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := soundRecommendParam{
		sound: &msound.Simple{
			Checked: msound.CheckedApproved + 1,
		},
	}
	assert.True(param.isNoRecommend(), "音频状态不对")
	assert.Equal(&soundRecommendResp{StrategyID: 0}, param.noRecommend())

	param.sound.Checked = msound.CheckedApproved
	assert.False(param.isNoRecommend(), "正常情况")

	param.sound.Refined = msound.RefinedNoLiveRecommend
	require.True(param.sound.IsNoLiveRecommend())
	assert.True(param.isNoRecommend(), "特殊音频")
}

func TestFindUPRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := soundRecommendParam{
		c:     handler.NewTestContext(http.MethodGet, "/sound/recommend", false, nil),
		sound: &msound.Simple{},
	}
	assert.Nil(param.findUPRecommend())

	param.sound.UserID = openingRoom.CreatorID
	resp := param.findUPRecommend()
	require.NotNil(resp)
	assert.EqualValues(1, resp.StrategyID)
	require.NotNil(resp.Room)
	assert.Equal(openingRoom.CreatorID, resp.Room.CreatorID)
}

func TestCheckDramaNoLiveRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URLGODramaCheckDramaRefined, func(input interface{}) (output interface{}, err error) {
		p := input.(map[string]interface{})
		dramaID := p["drama_ids"].([]int64)[0]
		if dramaID == 0 {
			return nil, nil
		}
		return map[string]userapi.CheckDramaRefinedResp{
			strconv.FormatInt(dramaID, 10): {
				CheckDetails: userapi.CheckDetails{
					NoLiveRecommend: true,
				},
			},
		}, nil
	})
	defer cleanup()

	res, err := checkDramaNoLiveRecommend(0)
	require.NoError(err)
	assert.False(res)
	res, err = checkDramaNoLiveRecommend(1001919)
	require.NoError(err)
	assert.True(res)
}

func TestFindCVRecommend(t *testing.T) {
	param := soundRecommendParam{
		soundID: 123,
		c:       handler.NewTestContext(http.MethodGet, "/", false, nil),
	}
	delCache := func() error {
		param.cvUserResp = nil
		return service.LRURedis.Del(keys.KeySoundCVUser1.Format(param.soundID)).Err()
	}
	rpcResp := &cvUserResp{
		DramaID: 1234,
	}
	var rpcErr error
	cancel := mrpc.SetMock("drama://episode/get-cvuser-by-soundid", func(i interface{}) (interface{}, error) {
		assert.Equal(t, handler.M{"sound_id": param.soundID}, i)
		return rpcResp, rpcErr
	})
	defer cancel()
	cancel = mrpc.SetMock(userapi.URLGODramaCheckDramaRefined, func(i interface{}) (interface{}, error) {
		return map[string]userapi.CheckDramaRefinedResp{
			strconv.FormatInt(rpcResp.DramaID, 10): {
				CheckDetails: userapi.CheckDetails{
					NoLiveRecommend: true,
				},
			},
		}, nil
	})
	defer cancel()

	t.Run("TestFindCVUser", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		require.NoError(delCache())
		param.findCVUser()
		require.Equal(param.cvUserResp.DramaID, rpcResp.DramaID)
		assert.True(param.cvUserResp.DramaNoLiveRecommend)

		// 从缓存读取
		rpcResp.DramaID = 456
		param.findCVUser()
		assert.NotEqual(rpcResp.DramaID, param.cvUserResp.DramaID)
	})

	t.Run("TestFindCVRecommend", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		require.NoError(delCache())
		rpcErr = errors.New("rpc test err")
		assert.Nil(param.findCVRecommend(), "模拟 findCVUser 出错的情况")

		require.NoError(delCache())
		rpcErr = nil
		assert.Nil(param.findCVRecommend(), "CV 为空的情况")

		require.NoError(delCache())
		rpcResp.CVInfo = append(rpcResp.CVInfo, cvInfo{
			CVName: "test",
			UserID: openingRoom.CreatorID,
			Main:   mainLeading,
		})
		sr := param.findCVRecommend()
		require.NotNil(sr, "主役推荐")
		assert.Equal(`<font color="#FFFFFF">参演 CV </font>`+
			`<font color="#ED7760">test</font>`+
			`<font color="#FFFFFF"> 正在直播</font>`, sr.Msg)

		require.NoError(delCache())
		rpcResp.CVInfo[0].UserID = 987654321
		rpcResp.CVInfo = append(rpcResp.CVInfo, cvInfo{
			CVName: "test",
			UserID: openingRoom.CreatorID,
			Main:   mainSupporting,
		})
		sr = param.findCVRecommend()
		require.NotNil(sr, "协役推荐")
		assert.Equal(`<font color="#FFFFFF">参演 CV </font>`+
			`<font color="#ED7760">test</font>`+
			`<font color="#FFFFFF"> 正在直播</font>`, sr.Msg)

		require.NoError(delCache())
		rpcResp.CVInfo[1].UserID = 9876543
		rpcResp.CVInfo = append(rpcResp.CVInfo, cvInfo{
			CVName: "test",
			UserID: openingRoom.CreatorID,
			Main:   mainExtra,
		}, cvInfo{
			CVName: "test2",
			UserID: 7890,
			Main:   mainExtra,
		})
		sr = param.findCVRecommend()
		require.NotNil(sr, "龙套推荐")
		assert.Equal(`<font color="#FFFFFF">参演 CV </font>`+
			`<font color="#ED7760">test</font>`+
			`<font color="#FFFFFF"> 正在直播</font>`, sr.Msg)
		require.NotNil(sr.Room)
		assert.Equal(openingRoom.RoomID, sr.Room.RoomID)

		require.NoError(delCache())
		rpcResp.CVInfo = []cvInfo{
			{
				CVName: "test",
				UserID: 0,
				Main:   mainExtra,
			},
		}
		sr = param.findCVRecommend()
		assert.Nil(sr, "cv 没直播的情况")
	})
}

func TestFindLiveRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := soundRecommendParam{
		c: handler.NewTestContext(http.MethodGet, "/", true, nil),
		sound: &msound.Simple{
			ID:        123,
			CatalogID: 18,
		},
		SoundRecommend: params.DefaultSoundRecommend(),
	}
	param.c.User().ID = 999874

	// 测试不推荐
	param.sound.Type = msound.TypeLive
	assert.Nil(param.findLiveRecommend(), "音频是直播推荐")
	param.sound.Type = 0
	param.SoundRecommend.Live.BlocklistSoundCatalog = []int64{param.sound.CatalogID}
	assert.Nil(param.findLiveRecommend(), "音频分区黑名单")
	param.SoundRecommend.Live.BlocklistSoundCatalog = []int64{}
	param.SoundRecommend.Live.BlocklistSound = []int64{param.sound.ID}
	assert.Nil(param.findLiveRecommend(), "音频黑名单")
	param.sound.ID++
	param.cvUserResp = &cvUserResp{
		DramaID:              1,
		DramaNoLiveRecommend: true,
	}
	assert.Nil(param.findLiveRecommend(), "剧集黑名单")
	param.cvUserResp = nil
	require.NoError(service.Redis.Set(
		keys.KeyUsersCloseSoundRecommend1.Format(param.c.UserID()),
		2, 5*time.Second).Err())
	assert.Nil(param.findLiveRecommend(), "用户自己关闭")

	// 没有配置直播间黑名单，直播分区黑名单
	param.c.User().ID++
	sr := param.findLiveRecommend()
	require.NotNil(sr)
	// 分区黑名单
	require.NoError(room.UpdateCatalogID(sr.Room.RoomID, catalog.CatalogIDMusic))
	param.Live.BlocklistRoom = []int64{}
	param.Live.BlocklistLiveCatalog = []int64{catalog.CatalogIDMusic}
	sr1 := param.findLiveRecommend()
	if sr1 != nil {
		assert.NotEqual(sr.Room.RoomID, sr1.Room.RoomID)
	}
	// 房间黑名单
	rs, err := room.List(bson.M{"status.open": room.StatusOpenTrue}, nil)
	require.NoError(err)
	require.NotEmpty(rs)
	param.Live.BlocklistRoom = make([]int64, 0, len(rs))
	for i := range rs {
		param.Live.BlocklistRoom = append(param.Live.BlocklistRoom, rs[i].RoomID)
	}
	param.Live.BlocklistLiveCatalog = []int64{}
	sr1 = param.findLiveRecommend()
	assert.Nil(sr1)

	// 测试有推荐白名单，并且开播返回推荐白名单中的数据
	testDramaAllowIDs := []int64{61618635, 63888614}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = room.Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": testDramaAllowIDs}},
		bson.M{"$set": bson.M{
			"status.open": room.StatusOpenTrue,
		}},
	)
	require.NoError(err)

	testDramaID := int64(100)
	now := util.TimeNow()
	param.cvUserResp = &cvUserResp{
		DramaID: testDramaID,
	}
	param.Live.DramaAllowList = []params.DramaAllowList{
		{
			DramaID:       testDramaID,
			RoomAllowList: testDramaAllowIDs,
			StartTime:     now.Add(-time.Hour).Unix(),
			EndTime:       now.Add(time.Hour).Unix(),
		},
	}
	sr1 = param.findLiveRecommend()
	require.NotNil(sr1)
	assert.True(util.HasElem(testDramaAllowIDs, sr1.Room.RoomID))

	// 测试有推荐白名单，但是音频 ID 在黑名单，正常推荐
	param.SoundRecommend.Live.BlocklistSound = []int64{param.sound.ID}
	sr1 = param.findLiveRecommend()
	assert.NotNil(sr1)
	assert.True(util.HasElem(testDramaAllowIDs, sr1.Room.RoomID))

	// 测试有推荐白名单，但是没有开播的房间，正常推荐
	param.Live.BlocklistRoom = param.Live.BlocklistRoom[1:] // 测试 limit 前 n% 数量为零的情况
	param.sound.ID++
	_, err = room.Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{
			"$in": testDramaAllowIDs},
		},
		bson.M{"$set": bson.M{
			"status.open": room.StatusOpenFalse,
		}},
	)
	require.NoError(err)
	sr1 = param.findLiveRecommend()
	require.NotNil(sr1)
	require.NotNil(sr1.Room)
	assert.False(util.HasElem(testDramaAllowIDs, sr1.Room.RoomID))

	// 测试有推荐白名单，但是没有开播的房间，音频 ID 在黑名单，不返回推荐
	param.SoundRecommend.Live.BlocklistSound = []int64{param.sound.ID}
	sr1 = param.findLiveRecommend()
	assert.Nil(sr1)
}

func TestActionSoundRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var s msound.Simple
	require.NoError(service.DB.First(&s).Error)
	newC := func() *handler.Context {
		return handler.NewTestContext(http.MethodGet, fmt.Sprintf("?sound_id=%d", s.ID), true, nil)
	}
	updateSound := func(v ...interface{}) error {
		return service.DB.Table(s.TableName()).Where("id = ?", s.ID).Update(v...).Error
	}

	c := handler.NewTestContext(http.MethodGet, "?sound_id=a", true, nil)
	_, err := ActionSoundRecommend(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 音频禁止直播推荐
	require.NoError(updateSound(
		map[string]interface{}{
			"refined": gorm.Expr("refined | ?", msound.RefinedNoLiveRecommend),
		}))
	r, err := ActionSoundRecommend(newC())
	require.NoError(err)
	resp := r.(*soundRecommendResp)
	assert.Equal(params.SRStrategyNone, resp.StrategyID)

	require.NoError(updateSound(
		map[string]interface{}{
			"refined": s.Refined,
			"user_id": openingRoom.CreatorID,
		}))
	r, err = ActionSoundRecommend(newC())
	require.NoError(err)
	resp = r.(*soundRecommendResp)
	assert.Equal(params.SRStrategyUP, resp.StrategyID)

	delCache := func() {
		require.NoError(service.LRURedis.Del(keys.KeySoundCVUser1.Format(s.ID)).Err())
	}
	rpcResp := &cvUserResp{DramaID: 7890}
	cancel := mrpc.SetMock("drama://episode/get-cvuser-by-soundid", func(i interface{}) (interface{}, error) {
		return rpcResp, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(userapi.URLGODramaCheckDramaRefined, func(i interface{}) (interface{}, error) {
		return map[string]userapi.CheckDramaRefinedResp{
			strconv.FormatInt(rpcResp.DramaID, 10): {
				CheckDetails: userapi.CheckDetails{
					NoLiveRecommend: rpcResp.DramaNoLiveRecommend,
				},
			},
		}, nil
	})
	defer cancel()

	// CV 推荐
	delCache()
	rpcResp.CVInfo = []cvInfo{{UserID: openingRoom.CreatorID, Main: mainLeading}}
	require.NoError(updateSound("user_id", s.UserID))
	r, err = ActionSoundRecommend(newC())
	require.NoError(err)
	resp = r.(*soundRecommendResp)
	assert.Equal(params.SRStrategyCV, resp.StrategyID)

	// 随机推荐
	delCache()
	rpcResp.CVInfo = []cvInfo{}
	r, err = ActionSoundRecommend(newC())
	require.NoError(err)
	resp = r.(*soundRecommendResp)
	assert.Equal(params.SRStrategyLive, resp.StrategyID)

	// 没有随机推荐
	delCache()
	rpcResp.DramaNoLiveRecommend = true
	r, err = ActionSoundRecommend(newC())
	require.NoError(err)
	resp = r.(*soundRecommendResp)
	assert.Equal(params.SRStrategyNone, resp.StrategyID)
}

func TestActionSoundCloseRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 顺便测试了 isUserCloseSoundRecommend
	c := handler.NewTestContext(http.MethodGet, "/", true, nil)
	c.User().ID = 123456
	userID := c.UserID()
	key := keys.KeyUsersCloseSoundRecommend1.Format(userID)
	require.NoError(service.Redis.Del(key).Err())
	assert.False(isUserCloseSoundRecommend(c))

	// NOTICE: 单元测试未给参数
	c = handler.NewTestContext(http.MethodPost, "/sound/close-recommend", true, nil)
	c.User().ID = userID
	_, err := ActionSoundCloseRecommend(c)
	require.NoError(err)
	assert.True(isUserCloseSoundRecommend(c))

	// 未登录的情况
	c = handler.NewTestContext(http.MethodGet, "/", false, nil)
	userID = 0
	key = keys.KeyUsersCloseSoundRecommend1.Format(userID)
	require.NoError(service.Redis.Set(key, 2, time.Second).Err())
	assert.False(isUserCloseSoundRecommend(c))
	c = handler.NewTestContext(http.MethodPost, "/sound/close-recommend", false, nil)
	_, err = ActionSoundCloseRecommend(c)
	require.NoError(err)
	val, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.NotEqual("1", val)
}
