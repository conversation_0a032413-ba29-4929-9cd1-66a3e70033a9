package luckybox

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSortBoxGifts(t *testing.T) {
	assert := assert.New(t)

	gifts := []*boxGiftItem{
		{
			BoxGiftType: 2,
			GiftID:      2,
		},
		{
			BoxGiftType: 1,
			GiftID:      2,
		},
		{
			BoxGiftType: 3,
			GiftID:      3,
		},
		{
			BoxGiftType: 3,
			GiftID:      2,
		},
	}
	sortBoxGifts(gifts)

	assert.EqualValues(3, gifts[0].BoxGiftType)
	assert.EqualValues(2, gifts[0].GiftID)
	assert.EqualValues(3, gifts[1].BoxGiftType)
	assert.EqualValues(3, gifts[1].GiftID)
	assert.EqualValues(2, gifts[2].BoxGiftType)
	assert.EqualValues(2, gifts[2].GiftID)
	assert.EqualValues(1, gifts[3].BoxGiftType)
	assert.EqualValues(2, gifts[3].GiftID)
}
