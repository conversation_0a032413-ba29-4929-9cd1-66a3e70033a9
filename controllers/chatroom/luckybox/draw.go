package luckybox

import (
	"errors"
	"slices"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox/databus"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const giftDuration = 7 * 24 * time.Hour

type drawParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GoodsID int64 `form:"goods_id" json:"goods_id"`
	Num     int   `form:"num" json:"num"`

	c              *handler.Context
	room           *room.Room
	user           *liveuser.Simple
	bubble         *bubble.Simple
	goods          *livegoods.LiveGoods
	goodsMore      *livegoods.More
	userVip        *vip.UserVip
	broadcastElems []*userapi.BroadcastElem
}

type drawResponse struct {
	ResultMsg string             `json:"result_msg"`
	LuckyBox  *drawLuckyBox      `json:"lucky_box"`
	Balance   userapi.BuyBalance `json:"balance"`
}

type drawLuckyBox struct {
	GoodsID int64          `json:"goods_id"`
	Gifts   []*boxGiftItem `json:"gifts"`
}

// ActionLuckyBoxDraw 购买宝盒
/**
 * @api {post} /api/v2/chatroom/luckybox/draw 购买宝盒
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} goods_id 商品 ID
 * @apiParam {Number} num 购买数量
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "result_msg": "礼物已发送至背包中，有效期 7*24h", // 抽盒结果说明文案
 *       "lucky_box": {
 *         "goods_id": 1,
 *         "gifts": [ // 开出的奖品列表
 *           {
 *             "new": 1, // 是否是第一次获得该礼物, 不下发或 0: 否, 1: 是
 *             "box_gift_type": 1, // 礼物类型, 1: 普通款, 2: 典藏款
 *             "gift_id": 1, // 礼物 ID
 *             "gift_name": "1k 热度卡", // 礼物名
 *             "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png" // 礼物图标
 *           }
 *         ]
 *       },
 *       "balance": {
 *         "balance": 11479968,
 *         "live_noble_balance": 283548,
 *         "live_noble_balance_status": 1
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 抽盒消息:
 *   {
 *     "type": "lucky_box",
 *     "event": "draw",
 *     "room_id": 1, // 当前直播间
 *     "user": {
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *       "titles": [{
 *         "type": "staff",
 *         "name": "超管",
 *         "color": "#F45B41"
 *       }, {
 *         "type": "level",
 *         "level": 9
 *       }, {
 *         "type": "medal",
 *         "name": "独角兽",
 *         "level": 4
 *       }, {
 *         "type": "noble",
 *         "name": "新秀",
 *         "level": 2
 *       }, {
 *         "type": "avatar_frame",
 *         "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *       }, {
 *         "type": "badge",
 *         "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *         "appearance_id": 1
 *       }]
 *     },
 *     "time": 1576744741101,
 *     "current_revenue": 1, // 本场榜中此用户的贡献值
 *     "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *       "type": "message", // 气泡类型，聊天气泡 message
 *       "image_url": "https://static.maoercdn.com/live/bubble/image/001.png",
 *       "frame_url": "https://static.maoercdn.com/live/bubble/frame/001.png",
 *       "text_color": "#F0F0F0"
 *     },
 *     "lucky_box": {
 *       "goods_id": 1,
 *       "name": "宝盒名称",
 *       "contribution": 50, // 花费钻石, 需要根据该字段更新收益记录
 *       "num": 3, // 抽取宝盒数量
 *       "icon_url": "http://static.maoercdn.com/liveactivity/223/box/cover.png",
 *       "gifts": [ // 开出的奖品列表
 *         {
 *           "box_gift_type": 2, // 礼物类型, 1: 普通款, 2: 典藏款
 *           "gift_id": 1, // 礼物 ID
 *           "gift_name": "典藏 1k 热度卡", // 礼物名
 *           "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png", // 礼物图标
 *           "num": 1 // 礼物数量
 *         },
 *         {
 *           "box_gift_type": 1, // 礼物类型, 1: 普通款, 2: 典藏款
 *           "gift_id": 2, // 礼物 ID
 *           "gift_name": "1k 热度卡", // 礼物名
 *           "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png", // 礼物图标
 *           "num": 2 // 礼物数量
 *         }
 *       ]
 *     }
 *   }
 */
func ActionLuckyBoxDraw(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDrawParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.draw()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

func newDrawParam(c *handler.Context) (*drawParam, error) {
	var param *drawParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GoodsID <= 0 || param.Num <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *drawParam) check() (err error) {
	param.room, err = room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if param.room.IsOwner(param.c) {
		return actionerrors.NewErrForbidden("暂不支持在自己直播间抽盒哦~")
	}
	// 被主播拉黑，无法购买宝盒
	blocked, err := blocklist.IsBlocked(param.room.CreatorID, param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
	}
	param.goods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeLuckyBox)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.goods == nil {
		return actionerrors.ErrGoodsNotFound
	}
	param.goodsMore, err = param.goods.UnmarshalMore()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	if param.goodsMore == nil || param.goodsMore.LuckyBox == nil {
		return actionerrors.NewErrServerInternal(errors.New("宝盒配置错误"), logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	if param.Num != 1 && !slices.Contains(param.goodsMore.LuckyBox.DrawNumOptions, param.Num) {
		return actionerrors.NewErrForbidden("购买数量不合法")
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.c.UserID()},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.room.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrUserNotFound
	}
	_, userVip, err := userstatus.UserGeneral(param.user.UserID(), param.c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if userVip != nil && userVip.IsActive() {
		param.userVip = userVip
	}
	param.bubble, err = userappearance.FindMessageBubble(param.user.UserID())
	if err != nil {
		logger.WithField("user_id", param.user.UserID()).Errorf("draw lucky_box find user message bubble error: %v", err)
		// PASS
	}
	return nil
}

func (param *drawParam) lock() (func(), bool) {
	key := keys.LockLuckyBoxDraw2.Format(param.goods.GoodsID(), param.user.UserID())
	for i, retry := 0, 10; i < retry; i++ {
		ok, err := service.Redis.SetNX(key, 1, 2*time.Second).Result()
		if err != nil {
			logger.Error(err)
			return nil, false
		}
		if ok {
			// 加锁成功，返回解锁函数
			return func() {
				err := service.Redis.Del(key).Err()
				if err != nil {
					logger.Error(err)
				}
			}, true
		}
		if i != retry-1 {
			// 最后一次不需要等待
			<-time.NewTimer(100 * time.Millisecond).C // 100 毫秒后再次尝试
		}
	}
	return nil, false
}

func (param *drawParam) draw() (*drawResponse, error) {
	unlock, ok := param.lock()
	if !ok {
		return nil, actionerrors.NewErrForbidden("操作过于频繁，请稍后再试")
	}
	defer unlock()

	pool, err := gift.FindPoolLuckyBox(param.goodsMore.LuckyBox.GiftPoolID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID(), "gift_pool_id": param.goodsMore.LuckyBox.GiftPoolID})
	}
	if pool == nil {
		return nil, actionerrors.NewErrServerInternal(errors.New("未找到宝盒礼物池"), logger.Fields{"goods_id": param.goods.GoodsID(), "gift_pool_id": param.goodsMore.LuckyBox.GiftPoolID})
	}
	giftIDs, err := pool.Draw(param.user.UserID(), param.Num)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	if len(giftIDs) != param.Num {
		return nil, actionerrors.NewErrForbidden("抽盒奖品数量不足")
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(sets.Uniq(giftIDs))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	// 查询用户抽奖记录，用于判断是否是第一次获得该礼物
	records, err := liveluckybox.FindUserRecordsByGiftIDs(param.GoodsID, param.user.UserID(), sets.Uniq(giftIDs))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	var (
		gifts     = make([]*boxGiftItem, 0, len(giftIDs))
		recordMap = goutil.ToMap(records, "GiftID").(map[int64]*liveluckybox.Record)
	)
	for _, giftID := range giftIDs {
		g, ok := giftMap[giftID]
		if !ok {
			logger.WithFields(logger.Fields{
				"room_id":  param.room.RoomID,
				"user_id":  param.user.UserID(),
				"goods_id": param.GoodsID,
				"gift_id":  giftID,
			}).Error("未查询到宝盒奖品")
			// PASS
			continue
		}
		if g.Type != gift.TypeFree {
			logger.WithFields(logger.Fields{
				"goods_id": param.goods.GoodsID(),
				"gift_id":  giftID,
				"type":     g.Type,
			}).Error("宝盒奖池礼物类型错误，非免费礼物")
			// PASS
			continue
		}
		giftItem := &boxGiftItem{
			New:         goutil.NewInt(goutil.BoolToInt(recordMap[g.GiftID] == nil)),
			BoxGiftType: BoxGiftTypeNormal,
			GiftID:      g.GiftID,
			GiftName:    g.Name,
			GiftIconURL: g.Icon,
		}
		if pool.IsSSRID(g.GiftID) {
			giftItem.BoxGiftType = BoxGiftTypeSSR
		}
		gifts = append(gifts, giftItem)
	}

	// 购买付费宝盒并创建订单
	balanceResp, orderID, err := param.buyLuckyBox()
	if err != nil {
		return nil, err
	}
	// 发放礼物
	err = param.addRecordsAndSendGifts(giftIDs, giftMap, orderID)
	if err != nil {
		return nil, err
	}

	// NOTICE: 不加 PK 榜
	goutil.Go(func() {
		// 宝盒收集任务
		databus.DrawLuckyBox(param.user.UserID(), param.room.RoomID, param.goods.GoodsID(), pool)

		param.addRevenue()
		param.addMedalPoint()
		param.addUserRedeemPoint()
		param.addUserContribution()
		param.addMultiConnectScore()
		param.broadcast(gifts)
		param.addActivity()
		param.addLiveShow()
		utils.SendHighnessSpend(param.user.UserID(), int64(param.goods.Price))
		param.addRoomPaidUser()
	})

	return &drawResponse{
		ResultMsg: param.goodsMore.LuckyBox.ResultMsg,
		LuckyBox: &drawLuckyBox{
			GoodsID: param.goods.GoodsID(),
			Gifts:   gifts,
		},
		Balance: userapi.NewBuyBalance(balanceResp, param.userVip != nil && param.userVip.IsActive()),
	}, nil
}

func (param *drawParam) buyLuckyBox() (*userapi.BalanceResp, int64, error) {
	// 创建订单
	// RPC 购买成功后再更新订单状态
	more := &livetxnorder.MoreInfo{
		Num: param.Num,
	}
	if param.room.IsOpen() {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusOpen)
	} else {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusClosed)
	}
	param.goods.SellerID = param.room.CreatorID
	param.goods.TxnOrderTitle = param.goods.Title
	order := livetxnorder.NewOrderWithNum(param.goods, param.Num, param.user.UserID(), 0, more)
	err := livetxnorder.LiveTxnOrder{}.DB().Create(order).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":  param.room.RoomID,
			"user_id":  param.user.UserID(),
			"goods_id": param.GoodsID,
		}).Errorf("创建抽宝盒订单失败：%v", err)
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}

	// 购买宝盒
	balanceResp, err := userapi.BuyLiveGoods(
		param.c.UserContext(),
		userapi.BuyLiveGoodsParam{
			BuyerID:    param.user.UserID(),
			ReceiverID: param.room.CreatorID,
			GoodsType:  userapi.GoodsTypeLuckyBox,
			Goods: []userapi.LiveGoodsElem{
				{
					ID:    param.goods.ID,
					Title: param.goods.Title,
					Price: param.goods.Price,
					Num:   param.Num,
				},
			},
			Noble:     1, // 可用贵族钻石购买
			UserAgent: param.c.UserAgent(),
			EquipID:   param.c.EquipID(),
			BUVID:     param.c.BUVID(),
			IP:        param.c.ClientIP(),
		},
		param.room.Status.OpenLogID,
	)
	if err != nil {
		return nil, 0, err
	}

	// 更新订单
	err = livetxnorder.LiveTxnOrder{}.DB().
		Where("id = ?", order.ID).
		Updates(map[string]interface{}{
			"status":        livetxnorder.StatusSuccess,
			"tid":           balanceResp.TransactionID,
			"modified_time": goutil.TimeNow().Unix(),
		}).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":        param.room.RoomID,
			"user_id":        param.user.UserID(),
			"goods_id":       param.goods.ID,
			"order_id":       order.ID,
			"transaction_id": balanceResp.TransactionID,
		}).Errorf("更新抽宝盒订单失败：%v", err)
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}
	return balanceResp, order.ID, nil
}

// 发放至背包
func (param *drawParam) addRecordsAndSendGifts(giftIDs []int64, giftMap map[int64]*gift.Gift, orderID int64) error {
	var (
		now     = goutil.TimeNow()
		records = make([]*liveluckybox.Record, 0, len(giftIDs))
		adder   = useritems.NewTransactionAdder(param.user.UserID(), orderID, "", len(giftIDs))
	)
	for _, id := range giftIDs {
		g, ok := giftMap[id]
		if !ok {
			logger.WithFields(logger.Fields{
				"room_id":  param.room.RoomID,
				"user_id":  param.user.UserID(),
				"goods_id": param.GoodsID,
				"gift_id":  id,
			}).Error("未查询到宝盒奖品")
			continue
		}
		records = append(records, &liveluckybox.Record{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			UserID:       param.user.UserID(),
			RoomID:       param.room.RoomID,
			GoodsID:      param.GoodsID,
			GiftID:       id,
			OrderID:      orderID,
		})
		adder.Append(g, 1, now.Unix(), now.Add(giftDuration).Unix())
	}
	// NOTICE: 这里不需要事务，因为如果礼物发放至背包失败的话，可以留存记录待后续处理
	err := servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Record{}.TableName(), records)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID(), "user_id": param.user.UserID()})
	}
	err = adder.Add()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID(), "user_id": param.user.UserID()})
	}
	return nil
}

func (param *drawParam) addRevenue() {
	if param.room.IsOwner(param.user) {
		return
	}
	score := int64(param.goods.Price * param.Num)
	if score <= 0 {
		return
	}

	err := roomsrank.AddRevenue(param.room.RoomID, param.user.UserID(), score, param.room.IsOpen())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.room.CreatorID, param.room.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = room.NotifyHourRank(rankChange, param.room)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	err = param.room.ReceiveLuckyBox(score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = liverevenues.AddLuckyBoxRevenue(param.user.UserID(), param.room.OID, param.room.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// addUserRedeemPoint 增加用户常驻兑换商城积分
func (param *drawParam) addUserRedeemPoint() {
	err := shop.AddUserRedeemPoint(param.user.UserID(), int64(param.goods.Price*param.Num))
	if err != nil {
		logger.Error(err)
	}
}

// addUserContribution 增加用户直播间经验值
func (param *drawParam) addUserContribution() {
	pointAdd := int64(param.goods.Price*param.Num) * 10 // 1 钻石 = 10 经验
	if param.userVip != nil && param.userVip.Info != nil {
		pointAdd = param.userVip.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.user.UserID(), param.RoomID, param.room.CreatorUsername, userstatus.FromNormal, param.userVip)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// 添加亲密度
func (param *drawParam) addMedalPoint() {
	if param.room.Medal == nil {
		return
	}
	medalParam := livemedal.AddPointParam{
		RoomOID:   param.room.OID,
		RoomID:    param.room.RoomID,
		CreatorID: param.room.CreatorID,
		UserID:    param.user.UserID(),
		UV:        param.userVip,
		MedalName: param.room.Medal.Name,
		Type:      livemedal.TypeLuckyBoxMedalPoint,
		Source:    livemedal.ChangeSourceBuyLuckyBox,
		PointAdd:  int64(param.Num * param.goods.Price),
		Scene:     livemedalpointlog.SceneTypeLuckyBox,
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.user,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.room.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

// 增加主播连线积分
func (param *drawParam) addMultiConnectScore() {
	if !param.room.IsMultiConnect() {
		return
	}

	elems, err := livemulticonnect.ScoreHelper{
		Room:  param.room,
		Goods: param.goods,
		Num:   int64(param.Num),
	}.AddScore()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *drawParam) addActivity() {
	r := rank.
		NewSyncParam(param.room.RoomID, param.user.UserID(), param.room.CreatorID).
		SetGuildID(param.room.GuildID).
		SetActivityCatalogID(param.room.ActivityCatalogID).
		SetRevenueGoods(rank.AddRevenueTypeLuckyBox, int64(param.Num*param.goods.Price))
	r.AddRankPoint()
	r.SendLiveActivity(param.c.UserContext(), userapi.SyncTypeLuckyBox)
}

func (param *drawParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.RoomID, param.user.UserID(), param.room.CreatorID).
		SetPoint(int64(param.goods.Price)).
		Sync()
}

func (param *drawParam) broadcast(gifts []*boxGiftItem) {
	giftNumMap := make(map[int64]int, len(gifts))
	for _, g := range gifts {
		giftNumMap[g.GiftID]++
	}
	var (
		giftMap  = make(map[int64]struct{}, len(gifts))
		boxGifts = make([]*boxGiftItem, 0, len(giftNumMap))
	)
	for _, g := range gifts {
		_, ok := giftMap[g.GiftID]
		if ok {
			continue
		} else {
			giftMap[g.GiftID] = struct{}{}
		}
		boxGifts = append(boxGifts, &boxGiftItem{
			Num:         goutil.NewInt(giftNumMap[g.GiftID]),
			BoxGiftType: g.BoxGiftType,
			GiftID:      g.GiftID,
			GiftName:    g.GiftName,
			GiftIconURL: g.GiftIconURL,
		})
	}
	sort.SliceStable(boxGifts, func(i, j int) bool {
		if boxGifts[i].BoxGiftType != boxGifts[j].BoxGiftType {
			return boxGifts[i].BoxGiftType > boxGifts[j].BoxGiftType
		}
		if boxGifts[i].Num != boxGifts[j].Num {
			return *boxGifts[i].Num > *boxGifts[j].Num
		}
		return boxGifts[i].GiftID < boxGifts[j].GiftID
	})

	elem := &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: param.RoomID,
		Payload: drawLuckyBoxBroadcast{
			Type:           liveim.TypeLuckyBox,
			Event:          liveim.EventLuckyBoxDraw,
			RoomID:         param.RoomID,
			User:           param.user,
			Time:           goutil.TimeNow().UnixMilli(),
			Bubble:         param.bubble,
			CurrentRevenue: roomsrank.CurrentRevenue(param.room.RoomID, param.user.UserID()),
			LuckyBox: &broadcastLuckyBox{
				GoodsID:      param.GoodsID,
				Name:         param.goods.Title,
				Contribution: int64(param.goods.Price * param.Num),
				Num:          param.Num,
				IconURL:      param.goods.GoodsIconURL(),
				Gifts:        boxGifts,
			},
		},
	}
	param.broadcastElems = append(param.broadcastElems, elem)
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *drawParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.user.UserID(), param.room.Status.OpenTime)
}
