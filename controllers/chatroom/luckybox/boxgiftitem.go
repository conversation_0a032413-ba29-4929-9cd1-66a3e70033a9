package luckybox

import "sort"

type boxGiftItem struct {
	New         *int   `json:"new,omitempty"` // 是否是新获得的礼物，抽盒接口下发
	Num         *int   `json:"num,omitempty"` // 获得抽到的同类型礼物数量，抽盒 WS 下发
	BoxGiftType int    `json:"box_gift_type"`
	GiftID      int64  `json:"gift_id"`
	GiftName    string `json:"gift_name"`
	GiftIconURL string `json:"gift_icon_url"`
	CreateTime  int64  `json:"create_time,omitempty"` // 抽盒时间，抽盒记录接口下发，单位：秒
}

func sortBoxGifts(gifts []*boxGiftItem) {
	sort.SliceStable(gifts, func(i, j int) bool {
		if gifts[i].BoxGiftType != gifts[j].BoxGiftType {
			return gifts[i].BoxGiftType > gifts[j].BoxGiftType
		}
		return gifts[i].GiftID < gifts[j].GiftID
	})
}
