package luckybox

import (
	"errors"
	"sort"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type collectListParam struct {
	RoomID  int64 `form:"room_id"`
	GoodsID int64 `form:"goods_id"`

	c            *handler.Context
	goods        *livegoods.LiveGoods
	giftCountMap map[int64]liveluckybox.OwnedGift
}

type collectListResp struct {
	GoodsID int64               `json:"goods_id"`
	Name    string              `json:"name"`
	Gifts   []*boxGiftItem      `json:"gifts"`
	Prizes  []*collectListPrize `json:"prizes"`
}

type collectListPrize struct {
	Num          int    `json:"num"`
	Title        string `json:"title"`
	PrizeName    string `json:"prize_name"`
	PrizeIconURL string `json:"prize_icon_url"`

	sort int // 仅用于排序
}

// ActionLuckyBoxCollectList 宝盒礼物收集信息
/**
 * @api {get} /api/v2/chatroom/luckybox/collect-list 宝盒礼物收集信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} goods_id 商品 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "lucky_box": {
 *         "goods_id": 1,
 *         "name": "宝盒名", // 宝盒名
 *         "gifts": [ // 收集礼物列表
 *           {
 *             "num": 1, // 抽出该礼物的数量，没有抽出的即为未获得
 *             "box_gift_type": 1, // 礼物类型, 1: 普通款, 2: 典藏款
 *             "gift_id": 1,
 *             "gift_name": "1k 热度卡", // 礼物名
 *             "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png" // 礼物图标
 *           }
 *         ],
 *         "prizes: [ // 收集奖品列表
 *           {
 *             "num": 0, // 获得该奖品的次数，若为 0 则表示未获得过
 *             "title": "集齐 5 套", // 奖品介绍
 *             "prize_name": "奖品名",
 *             "prize_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png" // 奖品图标
 *           },
 *           {
 *             "num": 1,
 *             "title": "获得典藏款",
 *             "prize_name": "奖品名",
 *             "prize_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png" // 奖品图标
 *           }
 *         ]
 *       }
 *     }
 *   }
 */
func ActionLuckyBoxCollectList(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newCollectListParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.collectList()
	if err != nil {
		return nil, "", err
	}
	return handler.M{"lucky_box": resp}, "", nil
}

func newCollectListParam(c *handler.Context) (*collectListParam, error) {
	var param *collectListParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GoodsID <= 0 {
		return nil, actionerrors.ErrParams
	}
	// 仅判断房间是否存在
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, err
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.goods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeLuckyBox)
	if err != nil {
		return nil, err
	}
	if param.goods == nil {
		return nil, actionerrors.ErrGoodsNotFound
	}
	param.giftCountMap, err = liveluckybox.OwnedGiftCount(param.GoodsID, c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.c = c
	return param, nil
}

func (param *collectListParam) collectList() (*collectListResp, error) {
	gs, err := param.gifts()
	if err != nil {
		return nil, err
	}
	ps, err := param.prizes()
	if err != nil {
		return nil, err
	}
	return &collectListResp{
		GoodsID: param.goods.GoodsID(),
		Name:    param.goods.Title,
		Gifts:   gs,
		Prizes:  ps,
	}, nil
}

func (param *collectListParam) gifts() ([]*boxGiftItem, error) {
	more, err := param.goods.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	if more == nil || more.LuckyBox == nil {
		return nil, actionerrors.NewErrServerInternal(errors.New("宝盒配置错误"), logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	pool, err := gift.FindPoolLuckyBox(more.LuckyBox.GiftPoolID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID(), "gift_pool_id": more.LuckyBox.GiftPoolID})
	}
	if pool == nil {
		return nil, actionerrors.NewErrServerInternal(errors.New("未找到宝盒礼物池"), logger.Fields{"goods_id": param.goods.GoodsID(), "gift_pool_id": more.LuckyBox.GiftPoolID})
	}
	giftIDs := make([]int64, 0, len(pool.Rates))
	for giftID := range pool.Rates {
		giftIDs = append(giftIDs, giftID)
	}
	if len(giftIDs) == 0 {
		return nil, nil
	}
	giftIDs = sets.Uniq(giftIDs)
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": param.goods.GoodsID()})
	}
	gifts := make([]*boxGiftItem, 0, len(giftIDs))
	for _, id := range giftIDs {
		g, ok := giftMap[id]
		if !ok {
			logger.WithFields(logger.Fields{
				"goods_id":     param.goods.GoodsID(),
				"gift_pool_id": more.LuckyBox.GiftPoolID,
				"gift_id":      id,
			}).Error("gift not found")
			// PASS
			continue
		}
		giftItem := &boxGiftItem{
			Num:         goutil.NewInt(param.giftCountMap[id].Count),
			BoxGiftType: BoxGiftTypeNormal,
			GiftID:      g.GiftID,
			GiftName:    g.Name,
			GiftIconURL: g.Icon,
		}
		if pool.IsSSRID(g.GiftID) {
			giftItem.BoxGiftType = BoxGiftTypeSSR
		}
		gifts = append(gifts, giftItem)
	}
	sortBoxGifts(gifts)
	return gifts, nil
}

func (param *collectListParam) prizes() ([]*collectListPrize, error) {
	tasks, err := liveluckybox.FindTasks(param.goods.GoodsID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(tasks) == 0 {
		logger.WithField("goods_id", param.goods.GoodsID()).Error("not found tasks")
		// PASS
		return make([]*collectListPrize, 0), nil
	}
	taskIDs := make([]int64, 0, len(tasks))
	for _, task := range tasks {
		taskIDs = append(taskIDs, task.ID)
	}
	userTaskCount, err := liveluckybox.CountUserFinishedTasks(param.c.UserID(), taskIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	userTaskCountMap := goutil.ToMap(userTaskCount, "TaskID").(map[int64]liveluckybox.UserTaskCount)
	prizes := make([]*collectListPrize, 0, len(tasks))
	for _, task := range tasks {
		clp := &collectListPrize{
			Num:          userTaskCountMap[task.ID].Count,
			Title:        task.Name,
			PrizeName:    task.PrizeName,
			PrizeIconURL: task.PrizeIconURL,
			sort:         task.Sort,
		}
		prizes = append(prizes, clp)
	}
	sort.Slice(prizes, func(i, j int) bool {
		// 未完成的排在前面
		if prizes[i].Num == 0 && prizes[j].Num != 0 {
			return true
		}
		if prizes[i].Num != 0 && prizes[j].Num == 0 {
			return false
		}

		return prizes[i].sort < prizes[j].sort
	})
	return prizes, nil
}
