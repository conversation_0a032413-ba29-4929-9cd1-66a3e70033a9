package luckybox

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestActionLuckyBoxRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	shows, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.NotEmpty(shows)
	var (
		testGoodsID int64 = 100000
		testPoolID  int64 = 123
		testUserID  int64 = 100001
		testSSRID         = shows[0].GiftID
	)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybox/record?room_id=223344&goods_id=100000", true, nil)
	_, _, err = ActionLuckyBoxRecord(c)
	assert.Equal(actionerrors.ErrGoodsNotFound, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.CollectionDrawPool().DeleteMany(ctx, bson.M{"pool_id": testPoolID, "type": gift.PoolTypeLuckyBox})
	require.NoError(err)
	_, err = gift.CollectionDrawPool().InsertOne(ctx, gift.PoolLuckyBox{
		PoolID: testPoolID,
		Type:   gift.PoolTypeLuckyBox,
		SSRID:  testSSRID,
	})
	require.NoError(err)
	more := &livegoods.More{
		LuckyBox: &livegoods.LuckyBox{
			GiftPoolID: testPoolID,
		},
	}
	moreBytes, err := json.Marshal(more)
	require.NoError(err)
	err = service.LiveDB.Create(livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeLuckyBox,
		More: string(moreBytes),
	}).Error
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
	require.NoError(err)
	records := make([]*liveluckybox.Record, 0, 100)
	for i := 1; i <= 100; i++ {
		records = append(records, &liveluckybox.Record{
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  shows[0].GiftID,
		})
	}
	records[len(records)-1].GiftID = shows[1].GiftID // 测试最后一条记录的礼物 ID 非典藏款
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Record{}.TableName(), records)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybox/record?room_id=223344&goods_id=100000", true, nil)
	c.User().ID = testUserID
	resp, _, err := ActionLuckyBoxRecord(c)
	require.NoError(err)
	require.NotNil(resp)
	response, ok := resp.(*recordResp)
	require.True(ok)
	require.Len(response.Records, 100)
	assert.Equal(BoxGiftTypeNormal, response.Records[0].BoxGiftType)
	assert.Equal(BoxGiftTypeSSR, response.Records[1].BoxGiftType)
}
