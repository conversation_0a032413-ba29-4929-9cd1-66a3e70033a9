package luckybox

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLuckyBoxList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testPoolID  int64 = 123
		testUserID  int64 = 100001
		testBoxNum  int   = 9
	)
	err := service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)
	key := keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeLuckyBox)
	require.NoError(service.LRURedis.Del(key).Err())

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybox/list?room_id=223344", true, nil)
	resp, _, err := ActionLuckyBoxList(c)
	assert.NoError(err)
	require.NotNil(resp)
	response, ok := resp.(luckyBoxListResp)
	require.True(ok)
	assert.Len(response.LuckyBoxes, 0)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.CollectionDrawPool().DeleteMany(ctx, bson.M{
		"pool_id": testPoolID,
		"type":    gift.PoolTypeLuckyBox,
	})
	require.NoError(err)
	_, err = gift.CollectionDrawPool().InsertOne(ctx, gift.PoolLuckyBox{
		PoolID: testPoolID,
		Type:   gift.PoolTypeLuckyBox,
	})
	require.NoError(err)
	more := livegoods.More{
		LuckyBox: &livegoods.LuckyBox{
			GiftPoolID:     testPoolID,
			Skin:           "oss://live/luckybox/halfwindow/100000/icon.png",
			DrawNumOptions: []int{3, 9},
			Num:            testBoxNum,
		},
	}
	str, err := json.Marshal(more)
	require.NoError(err)
	err = service.LiveDB.Create(livegoods.LiveGoods{
		ID:        testGoodsID,
		Type:      livegoods.GoodsTypeLuckyBox,
		Sort:      1,
		Price:     100,
		More:      string(str),
		StartTime: goutil.TimeNow().Add(-1 * time.Hour).Unix(),
	}).Error
	require.NoError(err)
	require.NoError(service.LRURedis.Del(key).Err())

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/luckybox/record?room_id=223344", true, nil)
	c.User().ID = testUserID
	resp, _, err = ActionLuckyBoxList(c)
	require.NoError(err)
	require.NotNil(resp)
	response, ok = resp.(luckyBoxListResp)
	require.True(ok)
	require.Len(response.LuckyBoxes, 1)
	assert.Equal(testGoodsID, response.LuckyBoxes[0].GoodsID)
	assert.EqualValues(100, response.LuckyBoxes[0].PriceInfo.Price)
	assert.NotEmpty(response.LuckyBoxes[0].SkinURL)
	require.Len(response.LuckyBoxes[0].PriceInfo.PriceOptions, 2)
	assert.EqualValues(3, response.LuckyBoxes[0].PriceInfo.PriceOptions[0].Num)
	assert.EqualValues(9, response.LuckyBoxes[0].PriceInfo.PriceOptions[1].Num)
	assert.EqualValues(testBoxNum, response.LuckyBoxes[0].Num)
}
