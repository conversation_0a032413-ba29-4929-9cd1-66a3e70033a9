package luckybox

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestBroadcast_Send(t *testing.T) {
	var num int
	cancel := mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		num++
		return "success", nil
	})
	defer cancel()

	err := drawLuckyBoxBroadcast{}.Send()
	require.NoError(t, err)
	assert.Equal(t, 1, num)
}
