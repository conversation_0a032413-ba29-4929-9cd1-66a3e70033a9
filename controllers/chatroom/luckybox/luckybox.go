package luckybox

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// HandlerV2 returns the registered handler v2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "luckybox",
		Actions: map[string]*handler.ActionV2{
			"draw":         handler.NewActionV2(handler.POST, ActionLuckyBoxDraw, handler.ActionOption{LoginRequired: true}),
			"list":         handler.NewActionV2(handler.GET, ActionLuckyBoxList, handler.ActionOption{LoginRequired: true}),
			"record":       handler.NewActionV2(handler.GET, ActionLuckyBoxRecord, handler.ActionOption{LoginRequired: true}),
			"collect-list": handler.NewActionV2(handler.GET, ActionLuckyBoxCollectList, handler.ActionOption{LoginRequired: true}),
		},
	}
}
