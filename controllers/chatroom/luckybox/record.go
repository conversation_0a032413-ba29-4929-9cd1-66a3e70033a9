package luckybox

import (
	"errors"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const (
	// BoxGiftTypeNormal 普通款
	BoxGiftTypeNormal = iota + 1
	// BoxGiftTypeSSR 典藏款
	BoxGiftTypeSSR
)

type recordParam struct {
	RoomID  int64 `form:"room_id"`
	GoodsID int64 `form:"goods_id"`
}

type recordResp struct {
	Records []*boxGiftItem `json:"records"`
}

// ActionLuckyBoxRecord 获取宝盒记录
/**
 * @api {get} /api/v2/chatroom/luckybox/record 获取宝盒记录
 * @apiDescription 默认返回最近 100 条记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} goods_id 商品 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "records": [
 *         {
 *           "box_gift_type": 1, // 礼物类型, 1: 普通款, 2: 典藏款
 *           "gift_id": 1,
 *           "gift_name": "1k 热度卡", // 礼物名
 *           "gift_icon_url": "http://static.maoercdn.com/liveactivity/223/box/prize.png", // 礼物图标
 *           "create_time": 1588888888 // 领取时间, 单位：秒
 *         }
 *       ]
 *     }
 *   }
 */
func ActionLuckyBoxRecord(c *handler.Context) (handler.ActionResponse, string, error) {
	var param *recordParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	// 仅判断房间是否存在
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	goods, err := livegoods.Find(param.GoodsID, livegoods.GoodsTypeLuckyBox)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if goods == nil {
		return nil, "", actionerrors.ErrGoodsNotFound
	}
	more, err := goods.UnmarshalMore()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": goods.GoodsID()})
	}
	if more == nil || more.LuckyBox == nil {
		return nil, "", actionerrors.NewErrServerInternal(errors.New("宝盒配置错误"), logger.Fields{"goods_id": goods.GoodsID()})
	}
	pool, err := gift.FindPoolLuckyBox(more.LuckyBox.GiftPoolID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, logger.Fields{"goods_id": goods.GoodsID(), "gift_pool_id": more.LuckyBox.GiftPoolID})
	}
	if pool == nil {
		return nil, "", actionerrors.NewErrServerInternal(errors.New("未找到宝盒礼物池"), logger.Fields{"goods_id": goods.GoodsID(), "gift_pool_id": more.LuckyBox.GiftPoolID})
	}

	records, err := liveluckybox.FindUserRecords(goods.GoodsID(), c.UserID())
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(records) <= 0 {
		return &recordResp{Records: []*boxGiftItem{}}, "", nil
	}
	giftIDs := make([]int64, 0, len(records))
	for _, record := range records {
		giftIDs = append(giftIDs, record.GiftID)
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	drawRecords := make([]*boxGiftItem, 0, len(records))
	for _, record := range records {
		g, ok := giftMap[record.GiftID]
		if !ok {
			logger.WithFields(logger.Fields{"goods_id": goods.GoodsID(), "gift_id": record.GiftID}).Error("未找到礼物信息")
			// PASS
			continue
		}
		giftItem := &boxGiftItem{
			BoxGiftType: BoxGiftTypeNormal,
			GiftID:      record.GiftID,
			GiftName:    g.Name,
			GiftIconURL: g.Icon,
			CreateTime:  record.CreateTime,
		}
		if pool.IsSSRID(record.GiftID) {
			giftItem.BoxGiftType = BoxGiftTypeSSR
		}
		drawRecords = append(drawRecords, giftItem)
	}
	return &recordResp{Records: drawRecords}, "", nil
}
