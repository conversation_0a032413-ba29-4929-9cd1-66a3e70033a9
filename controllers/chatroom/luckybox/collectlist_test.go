package luckybox

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestActionLuckyBoxCollectList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 11111
		testPoolID int64 = 123
	)

	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.GreaterOrEqual(len(gifts), 3)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "type = ?", livegoods.GoodsTypeLuckyBox).Error
	require.NoError(err)
	more := livegoods.More{
		LuckyBox: &livegoods.LuckyBox{
			GiftPoolID: testPoolID,
		},
	}
	moreBytes, err := json.Marshal(more)
	require.NoError(err)
	goods := &livegoods.LiveGoods{
		Type: livegoods.GoodsTypeLuckyBox,
		More: string(moreBytes),
	}
	err = service.LiveDB.Create(goods).Error
	require.NoError(err)

	_, err = gift.CollectionDrawPool().UpdateOne(context.TODO(),
		bson.M{
			"pool_id": testPoolID,
			"type":    gift.PoolTypeLuckyBox,
		},
		bson.M{
			"$set": bson.M{
				"ssr_id": gifts[0].GiftID,
				"rates": map[int64]int{
					gifts[0].GiftID: 10,
					gifts[1].GiftID: 45,
					gifts[2].GiftID: 45,
				},
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.Task{}, "goods_id = ?", goods.GoodsID()).Error
	require.NoError(err)
	tasks := []*liveluckybox.Task{
		{
			ID:      1,
			Sort:    1,
			GoodsID: goods.GoodsID(),
		},
		{
			ID:      2,
			Sort:    2,
			GoodsID: goods.GoodsID(),
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Task{}.TableName(), tasks)
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.TaskRecord{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	records := []*liveluckybox.TaskRecord{
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 2,
			UserID:    testUserID,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.TaskRecord{}.TableName(), records)
	require.NoError(err)

	resp, _, err := ActionLuckyBoxCollectList(handler.NewTestContext(http.MethodGet, fmt.Sprintf("/luckybox/collect-list?room_id=223344&goods_id=%d", goods.GoodsID()), true, nil))
	require.NoError(err)
	assert.NotNil(resp)
}

func TestNewCollectListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(livegoods.LiveGoods{}, "type = ?", livegoods.GoodsTypeLuckyBox).Error
	require.NoError(err)
	goods := &livegoods.LiveGoods{}
	err = service.LiveDB.Create(goods).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("/luckybox/collect-list?room_id=223344&goods_id=%d", goods.GoodsID()), true, nil)
	_, err = newCollectListParam(c)
	assert.EqualError(err, "商品不存在")

	err = service.LiveDB.Model(goods).Update("type", livegoods.GoodsTypeLuckyBox).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/luckybox/collect-list?room_id=223344&goods_id=%d", goods.GoodsID()), true, nil)
	param, err := newCollectListParam(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestCollectListParam_collectList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.GreaterOrEqual(len(gifts), 3)

	var (
		testUserID  int64 = 11111
		testGoodsID int64 = 132321312
		testPoolID  int64 = 123
	)
	_, err = gift.CollectionDrawPool().UpdateOne(context.TODO(),
		bson.M{
			"pool_id": testPoolID,
			"type":    gift.PoolTypeLuckyBox,
		},
		bson.M{
			"$set": bson.M{
				"ssr_id": gifts[0].GiftID,
				"rates": map[int64]int{
					gifts[0].GiftID: 10,
					gifts[1].GiftID: 45,
					gifts[2].GiftID: 45,
				},
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.Task{}, "id IN (1,2)").Error
	require.NoError(err)
	tasks := []*liveluckybox.Task{
		{
			ID:      1,
			Sort:    1,
			GoodsID: testGoodsID,
		},
		{
			ID:      2,
			Sort:    2,
			GoodsID: testGoodsID,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Task{}.TableName(), tasks)
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.TaskRecord{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	records := []*liveluckybox.TaskRecord{
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 2,
			UserID:    testUserID,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.TaskRecord{}.TableName(), records)
	require.NoError(err)

	more := &livegoods.More{
		LuckyBox: &livegoods.LuckyBox{
			GiftPoolID: testPoolID,
		},
	}
	moreBytes, err := json.Marshal(more)
	require.NoError(err)
	param := &collectListParam{
		c: handler.NewTestContext(http.MethodGet, "/luckybox/collect-list", true, nil),
		goods: &livegoods.LiveGoods{
			ID:   testGoodsID,
			More: string(moreBytes),
		},
	}
	list, err := param.collectList()
	require.NoError(err)
	require.NotNil(list)
	assert.Equal(testGoodsID, list.GoodsID)
	assert.Len(list.Gifts, 3)
	assert.Len(list.Prizes, 2)
}

func TestCollectListParam_gifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.GreaterOrEqual(len(gifts), 3)

	var (
		testPoolID int64 = 123
	)
	more := &livegoods.More{
		LuckyBox: &livegoods.LuckyBox{
			GiftPoolID: testPoolID,
		},
	}
	moreBytes, err := json.Marshal(more)
	require.NoError(err)
	param := &collectListParam{
		goods: &livegoods.LiveGoods{
			ID:   1131231,
			More: string(moreBytes),
		},
	}
	_, err = gift.CollectionDrawPool().UpdateOne(context.TODO(),
		bson.M{
			"pool_id": testPoolID,
			"type":    gift.PoolTypeLuckyBox,
		},
		bson.M{
			"$set": bson.M{
				"ssr_id": gifts[0].GiftID,
				"rates": map[int64]int{
					gifts[0].GiftID: 10,
					gifts[1].GiftID: 45,
					gifts[2].GiftID: 45,
				},
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	gs, err := param.gifts()
	require.NoError(err)
	require.Len(gs, 3)
	assert.Equal(gifts[0].GiftID, gs[0].GiftID)
	assert.Equal(BoxGiftTypeSSR, gs[0].BoxGiftType)
	assert.Greater(gs[2].GiftID, gs[1].GiftID)
	assert.Equal(BoxGiftTypeNormal, gs[1].BoxGiftType)
	assert.Equal(BoxGiftTypeNormal, gs[2].BoxGiftType)
}

func TestCollectListParam_prizes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  int64 = 11111
		testGoodsID int64 = 132321312
	)
	err := liveluckybox.DB().Delete(liveluckybox.Task{}, "id IN (1,2)").Error
	require.NoError(err)
	tasks := []*liveluckybox.Task{
		{
			ID:      1,
			Sort:    1,
			GoodsID: testGoodsID,
		},
		{
			ID:      2,
			Sort:    2,
			GoodsID: testGoodsID,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Task{}.TableName(), tasks)
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.TaskRecord{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	records := []*liveluckybox.TaskRecord{
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 1,
			UserID:    testUserID,
		},
		{
			BoxTaskID: 2,
			UserID:    testUserID,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.TaskRecord{}.TableName(), records)
	require.NoError(err)

	param := &collectListParam{
		c: handler.NewTestContext(http.MethodGet, "/luckybox/collect-list", true, nil),
		goods: &livegoods.LiveGoods{
			ID: testGoodsID,
		},
	}
	param.c.User().ID = testUserID
	ps, err := param.prizes()
	require.NoError(err)
	require.Len(ps, 2)
	assert.EqualValues(2, ps[0].Num)
	assert.EqualValues(1, ps[1].Num)
}
