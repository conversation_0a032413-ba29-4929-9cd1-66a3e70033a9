package luckybox

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type luckyBoxListResp struct {
	LuckyBoxes []luckyBoxInfo `json:"lucky_boxes"`
}

type luckyBoxInfo struct {
	GoodsID   int64     `json:"goods_id"`
	Name      string    `json:"name"`
	Num       int       `json:"num"`
	SkinURL   string    `json:"skin_url"`
	PriceInfo priceInfo `json:"price_info"`
}

type priceInfo struct {
	Price        int           `json:"price"`
	PriceOptions []priceOption `json:"price_options"`
}

type priceOption struct {
	Num   int `json:"num"`
	Price int `json:"price"`
}

// ActionLuckyBoxList 获取宝盒列表
/**
 * @api {get} /api/v2/chatroom/luckybox/list 获取宝盒列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "lucky_boxes": [
 *         {
 *           "goods_id": 1,
 *           "name": "宝盒名",
 *           "num": 1, // 宝盒数量
 *           "skin_url": "https://static-test.maoercdn.com/test/skin.zip", // 皮肤资源压缩包地址
 *           "price_info": { // 购买价格信息
 *             "price": 100, // 购买单价，单位：钻石
 *             "price_options": [ // 购买选项，可多选
 *               {
 *                 "num": 3, // 一次购买的数量
 *                 "price": 300, // 价格，单位：钻石
 *               },
 *               {
 *                 "num": 9,
 *                 "price": 900,
 *               }
 *             ]
 *           }
 *         }
 *       ]
 *     }
 *   }
 */
func ActionLuckyBoxList(c *handler.Context) (handler.ActionResponse, string, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	// 仅判断房间是否存在
	r, err := room.Find(roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	boxGoods, err := livegoods.ListLiveGoods(livegoods.GoodsTypeLuckyBox)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(boxGoods) == 0 {
		return luckyBoxListResp{}, "", nil
	}
	// 一期只有一种宝箱，后期可能多种宝箱
	boxInfos := make([]luckyBoxInfo, 0, len(boxGoods))
	for _, v := range boxGoods {
		if !v.IsValidShowTime() {
			continue
		}
		more, err := v.UnmarshalMore()
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if more == nil {
			logger.WithField("goods_id", v.ID).Error("lucky box boxInfo more is nil")
			// PASS
			continue
		}
		if more.LuckyBox == nil {
			logger.WithField("goods_id", v.ID).Error("lucky box boxInfo more.lucky_box is nil")
			// PASS
			continue
		}
		boxInfo := luckyBoxInfo{
			GoodsID: v.ID,
			Name:    v.Title,
			Num:     more.LuckyBox.Num,
			SkinURL: more.LuckyBox.SkinURL(),
			PriceInfo: priceInfo{
				Price:        v.Price,
				PriceOptions: make([]priceOption, 0, len(more.LuckyBox.DrawNumOptions)),
			},
		}
		for _, num := range more.LuckyBox.DrawNumOptions {
			boxInfo.PriceInfo.PriceOptions = append(boxInfo.PriceInfo.PriceOptions, priceOption{
				Num:   num,
				Price: v.Price * num,
			})
		}
		boxInfos = append(boxInfos, boxInfo)
	}
	return luckyBoxListResp{LuckyBoxes: boxInfos}, "", nil
}
