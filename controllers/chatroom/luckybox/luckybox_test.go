package luckybox

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := HandlerV2()
	assert.Equal("luckybox", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "draw", "list", "record", "collect-list")
}
