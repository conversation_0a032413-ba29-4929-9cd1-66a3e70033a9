package chatroom

import (
	"encoding/json"
	"fmt"
	"html"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/msound"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type soundRecommendResp struct {
	StrategyID int          `json:"strategy_id"`
	Message    string       `json:"message,omitempty"` // WORKAROUND: 安卓 6.0.8, iOS 6.0.8 后弃用
	Msg        string       `json:"msg,omitempty"`     // html 格式提示
	Room       *room.Simple `json:"room,omitempty"`
}

type soundRecommendParam struct {
	soundID int64
	c       *handler.Context

	sound *msound.Simple
	params.SoundRecommend
	findOpt *room.FindOptions

	cvUserResp *cvUserResp
}

const (
	mainLeading    = iota + 1 // 主役
	mainSupporting            // 协役
	mainExtra                 // 龙套
)

type cvInfo struct {
	CVID      int64  `json:"cv_id"`
	CVName    string `json:"cv_name"`
	UserID    int64  `json:"user_id"`
	Main      int    `json:"main"`
	Character string `json:"character"`
}

type cvUserResp struct {
	DramaID              int64    `json:"drama_id"`
	CVInfo               []cvInfo `json:"cv_info"`
	DramaNoLiveRecommend bool     `json:"drama_no_live_recommend"` // check-drama-refined 结果
}

func newSoundRecommendParam(c *handler.Context) (*soundRecommendParam, error) {
	soundID, err := c.GetParamInt64("sound_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param := &soundRecommendParam{soundID: soundID}
	param.sound, err = msound.FindSimple(param.soundID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.sound == nil {
		return nil, actionerrors.ErrNotFound("音频不存在")
	}
	param.SoundRecommend, err = params.FindSoundRecommend()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.c = c
	param.findOpt = &room.FindOptions{FindCreator: true}
	return param, nil
}

func (param *soundRecommendParam) newSoundRecommendResp(strategyID int, r *room.Simple, cv *cvInfo) *soundRecommendResp {
	resp := &soundRecommendResp{
		StrategyID: strategyID,
		Room:       r,
	}
	if strategyID == params.SRStrategyNone {
		return resp
	}
	e := param.c.Equip()
	ver := goutil.AppVersions{
		Android: "6.0.8",
		IOS:     "6.0.8",
	}
	if resp.StrategyID == params.SRStrategyCV && cv == nil {
		logger.WithFields(logger.Fields{"sound_id": param.soundID}).Error("CV 推荐没有 CV 信息")
		resp.StrategyID = params.SRStrategyLive
	}
	if e.IsOldApp(ver) {
		// WORKAROUND: 安卓 6.0.8, iOS 6.0.8 之前版本返回无 html 格式的文案
		switch resp.StrategyID {
		case params.SRStrategyUP:
			resp.Message = fmt.Sprintf("UP 主 %s 正在直播！", r.CreatorUsername)
		case params.SRStrategyCV:
			if cv.Character != "" && (cv.Main == mainLeading || cv.Main == mainSupporting) {
				resp.Message = fmt.Sprintf("%s 配音 CV %s 正在直播", cv.Character, cv.CVName)
			} else {
				resp.Message = fmt.Sprintf("参演 CV %s 正在直播", cv.CVName)
			}
		case params.SRStrategyLive:
			resp.Message = param.RandomLiveMessage()
		}
	} else {
		const highlightColor = "#ED7760"
		switch resp.StrategyID {
		case params.SRStrategyUP:
			resp.Msg = fmt.Sprintf(`<font color="#FFFFFF">UP 主 </font><font color="%s">%s</font><font color="#FFFFFF"> 正在直播！</font>`,
				highlightColor, html.EscapeString(r.CreatorUsername))
		case params.SRStrategyCV:
			if cv.Character != "" && (cv.Main == mainLeading || cv.Main == mainSupporting) {
				// 主役、协役文案格式
				resp.Msg = fmt.Sprintf(
					`<font color="%s">%s</font><font color="#FFFFFF"> 配音 CV </font><font color="%s">%s</font><font color="#FFFFFF"> 正在直播</font>`,
					highlightColor, html.EscapeString(cv.Character),
					highlightColor, html.EscapeString(cv.CVName))
			} else {
				// 龙套文案格式
				resp.Msg = fmt.Sprintf(`<font color="#FFFFFF">参演 CV </font><font color="%s">%s</font><font color="#FFFFFF"> 正在直播</font>`,
					highlightColor, html.EscapeString(cv.CVName))
			}
		case params.SRStrategyLive:
			resp.Msg = fmt.Sprintf(`<font color="#FFFFFF">%s</font>`,
				html.EscapeString(param.RandomLiveMessage()))
		}
	}

	return resp
}

func (param *soundRecommendParam) noRecommend() *soundRecommendResp {
	return param.newSoundRecommendResp(params.SRStrategyNone, nil, nil)
}

func (param *soundRecommendParam) isNoRecommend() bool {
	// 状态不是通过或者被标记的音频不进行推荐
	return param.sound.Checked != msound.CheckedApproved || param.sound.IsNoLiveRecommend()
}

func (param *soundRecommendParam) isDramaNoLiveRecommend() bool {
	return param.cvUserResp != nil &&
		param.cvUserResp.DramaID != 0 && param.cvUserResp.DramaNoLiveRecommend
}

func (param *soundRecommendParam) findUPRecommend() *soundRecommendResp {
	r, err := room.FindOneSimple(bson.M{
		"creator_id":  param.sound.UserID,
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}, param.findOpt)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if r == nil {
		return nil
	}
	return param.newSoundRecommendResp(params.SRStrategyUP, r, nil)
}

func checkDramaNoLiveRecommend(dramaID int64) (bool, error) {
	checkResp, err := userapi.CheckDramaRefined([]int64{dramaID}, []string{userapi.DramaTypeNoLiveRecommend})
	if err != nil {
		return false, err
	}
	dramaCheck, ok := checkResp[strconv.FormatInt(dramaID, 10)]
	if !ok {
		return false, nil
	}
	return dramaCheck.CheckDetails.NoLiveRecommend, nil
}

// 查询音频 CV 信息和所属广播剧是否屏蔽直播推荐，在 findCVRecommend 调用
func (param *soundRecommendParam) findCVUser() {
	key := keys.KeySoundCVUser1.Format(param.soundID)
	cache, err := service.LRURedis.Get(key).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		return
	}
	var resp cvUserResp
	if cache != "" {
		err = json.Unmarshal([]byte(cache), &resp)
		if err != nil {
			logger.Error(err)
		} else {
			param.cvUserResp = &resp
		}
		return
	}
	uri := "drama://episode/get-cvuser-by-soundid"
	err = service.MRPC.Do(param.c.UserContext(), uri, handler.M{"sound_id": param.soundID}, &resp)
	if err != nil {
		logger.Error(err)
		return
	}
	param.cvUserResp = &resp
	if resp.DramaID != 0 {
		resp.DramaNoLiveRecommend, err = checkDramaNoLiveRecommend(resp.DramaID)
		if err != nil {
			logger.Error(err)
			return
		}
	}

	b, err := json.Marshal(resp)
	if err != nil {
		logger.Error(err)
		return
	}
	err = service.LRURedis.Set(key, string(b), 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
	}
}

func (param *soundRecommendParam) findCVRecommend() *soundRecommendResp {
	param.findCVUser()
	if param.cvUserResp == nil {
		return nil
	}
	cvs := param.cvUserResp.CVInfo
	if len(cvs) == 0 {
		return nil
	}
	cvInfoMap := make(map[int64]*cvInfo, len(cvs))
	userIDs := [3][]int64{
		make([]int64, 0, len(cvs)), // 主役
		make([]int64, 0, len(cvs)), // 协役
		make([]int64, 0, len(cvs)), // 龙套
	}
	for i := range cvs {
		if cvs[i].UserID == 0 {
			continue
		}
		cvInfoMap[cvs[i].UserID] = &cvs[i]
		switch cvs[i].Main {
		case mainLeading:
			userIDs[0] = append(userIDs[0], cvs[i].UserID)
		case mainSupporting:
			userIDs[1] = append(userIDs[1], cvs[i].UserID)
		case mainExtra:
			fallthrough
		default:
			userIDs[2] = append(userIDs[2], cvs[i].UserID)
		}
	}
	for i := range userIDs {
		if len(userIDs[i]) == 0 {
			continue
		}
		rooms, err := room.ListSimples(
			bson.M{
				"creator_id":  bson.M{"$in": userIDs[i]},
				"status.open": room.StatusOpenTrue,
				"limit":       bson.M{"$exists": false},
			},
			options.Find().SetSort(room.SortByScore).SetLimit(1),
			param.findOpt)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if len(rooms) != 0 {
			return param.newSoundRecommendResp(params.SRStrategyCV, rooms[0],
				cvInfoMap[rooms[0].CreatorID])
		}
	}
	return nil
}

func (param *soundRecommendParam) findLiveRecommend() *soundRecommendResp {
	// 音频是直播回放和用户自己关闭推荐都不返回推荐
	if param.sound.Type == msound.TypeLive || isUserCloseSoundRecommend(param.c) {
		return nil
	}

	type m = bson.M
	roomSimples := func(match m, limit int64) []*room.Simple {
		pipe := []m{
			{"$match": match},
			{"$sort": room.SortByScore},
			{"$limit": limit},
			{"$sample": m{"size": 1}},
		}
		rooms, err := room.AggregateSimples(pipe, param.findOpt)
		if err != nil {
			logger.Error(err)
			return nil
		}
		return rooms
	}

	// 有推荐白名单的情况下，优先从推荐白名单中随机推荐直播间
	// 若配置的推荐白名单中没有正在开播的直播间，则按普通的随机推荐逻辑推荐直播间
	// 没有生效的推荐白名单，按照普通的随机推荐逻辑推荐直播间
	var rooms []*room.Simple
	srl := param.Live
	if param.cvUserResp != nil {
		allowList := srl.FindDramaAllowList(param.cvUserResp.DramaID)
		if len(allowList) > 0 {
			rooms = roomSimples(m{
				"room_id":     m{"$in": allowList},
				"status.open": room.StatusOpenTrue,
				"limit":       m{"$exists": false},
			}, int64(len(allowList)))
		}
		if len(rooms) > 0 {
			return param.newSoundRecommendResp(params.SRStrategyLive, rooms[0], nil)
		}
	}

	if goutil.HasElem(srl.BlocklistSoundCatalog, param.sound.CatalogID) ||
		goutil.HasElem(srl.BlocklistSound, param.sound.ID) ||
		param.isDramaNoLiveRecommend() {
		/*
			- 在音频分类黑名单
			- 在音频黑名单
			- 在剧集黑名单
		*/
		return nil
	}

	// 查询符合条件的房间总数
	filter := m{
		"status.open": room.StatusOpenTrue,
		"limit":       m{"$exists": false},
	}
	if len(srl.BlocklistLiveCatalog) != 0 {
		filter["catalog_id"] = m{"$nin": srl.BlocklistLiveCatalog}
	}
	if len(srl.BlocklistRoom) != 0 {
		filter["room_id"] = m{"$nin": srl.BlocklistRoom}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := room.Collection()
	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if count == 0 {
		return nil
	}
	// 选定分区下的所有直播间按照热度从高到低排名，前 n% 可被推荐，n 可随时调节
	limit := count * int64(srl.ScorePercent) / 100
	if limit == 0 {
		// 防止查不出
		limit = 1
	}
	rooms = roomSimples(filter, limit)
	if len(rooms) == 0 {
		return nil
	}
	return param.newSoundRecommendResp(params.SRStrategyLive, rooms[0], nil)
}

// ActionSoundRecommend 音频播放页的直播推荐
/**
 * @api {get} /api/v2/chatroom/sound/recommend 音频播放页右上角直播推荐
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} sound_id 音频 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room": {
 *         "room_id": 152,
 *         "name": "12345",
 *         "cover_url": "https://static-test.missevan.com/cover.png"
 *         "creator_id": 12345,
 *         "creator_username": "1234",
 *         "creator_iconurl": "http://static.missevan.com/avatars/icon01.png",
 *         "statistics": {
 *           ...
 *         },
 *         "status": {
 *           "open": 1 // 总是开播中
 *         }
 *       },
 *       "strategy_id": 1 // 0: 没有推荐；1: UP 主推荐；2: 声优推荐；3: 直播推荐
 *       "msg": "UP 主<font color=\"#123456\">abc</font>正在直播！" // 新版本文案，总是 html 格式，对应主播描述
 *     }
 *   }
 *
 */
func ActionSoundRecommend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSoundRecommendParam(c)
	if err != nil {
		return nil, err
	}
	if param.isNoRecommend() {
		return param.noRecommend(), nil
	}
	resp := param.findUPRecommend()
	if resp != nil {
		return resp, nil
	}
	resp = param.findCVRecommend()
	if resp != nil {
		return resp, nil
	}
	resp = param.findLiveRecommend()
	if resp != nil {
		return resp, nil
	}
	return param.noRecommend(), nil
}

// ActionSoundCloseRecommend 用户关闭播放页右上角推荐
/**
 * @api {post} /api/v2/chatroom/sound/close-recommend 用户关闭右上角推荐操作
 * @apiDescription 用户关闭右上角的推荐操作，需要在 query 带上参数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam (Query) {Number} sound_id 音频 ID，不传不影响关闭
 * @apiParam (Query) {Number} room_id 房间号，不传不影响关闭
 * @apiParam (Query) {number=1,2,3} strategy_id 推荐策略 ID，不传不影响关闭
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "关闭成功"
 *   }
 *
 */
func ActionSoundCloseRecommend(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: 后续支持未登录用户的关闭
	userID := c.UserID()
	if userID == 0 {
		return "关闭成功", nil
	}
	sr, err := params.FindSoundRecommend()
	if err != nil {
		logger.Error(err)
		// PASS
		return "关闭成功", nil
	}
	key := keys.KeyUsersCloseSoundRecommend1.Format(userID)
	// 关闭之后 7 天内不会进行随机推荐
	err = service.Redis.Set(key, 1,
		time.Duration(sr.Live.OffDuration)*time.Millisecond).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "关闭成功", nil
}

func isUserCloseSoundRecommend(c *handler.Context) bool {
	userID := c.UserID()
	if userID == 0 {
		return false
	}
	key := keys.KeyUsersCloseSoundRecommend1.Format(userID)
	ok, err := service.Redis.Exists(key).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	return ok != 0
}
