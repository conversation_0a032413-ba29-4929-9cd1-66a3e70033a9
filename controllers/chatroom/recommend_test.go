package chatroom

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const bannedRoomCatalogID int64 = 104

func TestRecommendLoad(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("GET", "/api/v2/chatroom/recommend?room_id=-1", true, nil)
	var param recommendParam
	assert.Equal(actionerrors.ErrParams, param.load(c))
	assert.Equal(int64(-1), param.preRoomID)
	c = handler.NewTestContext("GET", "/api/v2/chatroom/recommend?room_id=0&catalog_id=-1", true, nil)
	assert.Equal(actionerrors.ErrParams, param.load(c))
	assert.Zero(param.preRoomID)

	c = handler.NewTestContext("GET", "/api/v2/chatroom/recommend?room_id=1&catalog_id=2", true, nil)
	param = recommendParam{}
	assert.NoError(param.load(c))
	assert.NotNil(param.listener)
	assert.Equal(int64(1), param.preRoomID)
	assert.NotNil(param.opt)
	m := map[int64]struct{}{1: {}}
	for _, id := range room.OpenListExcludeRoomIDs() {
		m[id] = struct{}{}
	}
	assert.Equal(m, param.excludeRoomIDs)
	assert.Equal(int64(2), param.catalogID)
}

func TestRecommendCheckCatalogID(t *testing.T) {
	assert := assert.New(t)

	param := recommendParam{}
	c := handler.NewTestContext("GET", "?catalog_id=1", false, nil)
	param.checkCatalogID(c)
	assert.Equal(int64(1), param.catalogID)
	c = handler.NewTestContext("GET", "?catalog_id=abc", false, nil)
	e := c.Equip()
	e.AppVersion = "4.5.1"
	e.OS = goutil.IOS
	param.checkCatalogID(c)
	assert.Zero(param.catalogID)

	c = handler.NewTestContext("GET", "", false, nil)
	param.checkCatalogID(c)
	assert.Zero(param.catalogID)

	e = c.Equip()
	e.FromApp = true
	e.AppVersion = "4.5.1"
	e.OS = goutil.IOS
	param.checkCatalogID(c)
	assert.Zero(param.catalogID)

	param.preRoomID = bannedRoomID
	param.checkCatalogID(c)
	assert.NotZero(param.catalogID)
}

func TestRecommendFindByCatalog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := recommendParam{
		excludeRoomIDs: map[int64]struct{}{
			bannedRoomID: {},
		},
	}
	assert.NoError(param.findByCatalog())
	assert.Empty(param.Data)
	param.preRoomID = bannedRoomID
	param.catalogID = bannedRoomCatalogID
	assert.NoError(param.findByCatalog())
	require.NotEmpty(param.Data)
	assert.NotZero(param.Data[0].CatalogID)
	assert.Len(param.excludeRoomIDs, len(param.Data)+1)
}

func TestRecommendFindByAttention(t *testing.T) {
	assert := assert.New(t)

	param := recommendParam{excludeRoomIDs: map[int64]struct{}{}}
	assert.NoError(param.findByAttention())
	assert.Empty(param.Data)
	param.listener = new(user.User)
	param.listener.ID = 12
	assert.NoError(param.findByAttention())
}

func TestRecommendFindByHeat(t *testing.T) {
	assert := assert.New(t)

	param := recommendParam{excludeRoomIDs: map[int64]struct{}{}}
	assert.NoError(param.findByHeat())
}

func TestActionRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET",
		fmt.Sprintf("/api/v2/chatroom/recommend?room_id=%d&catalog_id=116", bannedRoomID), true, nil)
	r, err := ActionRecommend(c)
	require.NoError(err)
	resp := r.([]*room.Simple)
	assert.NotZero(len(resp))
	for i := range resp {
		assert.NotEqual(bannedRoomID, resp[i].RoomID)
	}
}
