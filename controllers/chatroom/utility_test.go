package chatroom

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestIsRoomPushing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(room.Room)
	r.Status.Channel.Time = util.TimeToUnixMilli(goutil.TimeNow())
	r.Channel.Provider = room.ChannelProviderAliyun
	_, reliable, err := isRoomPushing(r)
	require.NoError(err)
	assert.False(reliable)
	r.Status.Channel.Time = 0
	_, reliable, err = isRoomPushing(r)
	require.NoError(err)
	assert.True(reliable)
	r.Channel.Provider = "abc"
	push, reliable, err := isRoomPushing(r)
	require.NoError(err)
	assert.True(push && !reliable)
	// TODO: 由于金山没有测试账号，所以暂时不测

	// 自定义 PushURL 有值
	r.Channel.Custom = &room.CustomInfo{PushURL: "test_push_url"}
	r.Status.Channel.Type = room.TypeOpen
	ok, reliable, err := isRoomPushing(r)
	require.NoError(err)
	assert.True(ok)
	assert.False(reliable)
}

func TestAddRankByPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	before := usersrank.Info{
		UserID: testRoom.CreatorID,
	}
	_, err := usersrank.Find(usersrank.TypeHour, now, &before)
	require.NoError(err)

	addRankByPoint(testRoom, 12, 1)
	after := usersrank.Info{
		UserID: testRoom.CreatorID,
	}
	_, err = usersrank.Find(usersrank.TypeHour, now, &after)
	require.NoError(err)
	assert.Equal(before.Revenue+1, after.Revenue)
}
