package chatroom

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livetagcontrollist"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/livevote"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 上神用户 ID
const testHighnessUserID = 9074509

func TestMetaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(metaResp{}, "gifts", "medal", "interaction", "pk", "multi_connect",
		"events", "hour_rank", "popups", "gashapon", "gift_wall", "red_packet", "redeem_shop", "privilege_shop",
		"pia", "multi_combo", "shortcut_gift", "members", "config", "lucky_bag_entry", "lucky_bag", "wish_list", "lucky_box")
	kc.CheckOmitEmpty(metaResp{}, "gifts", "medal",
		"interaction", "pk", "multi_connect", "events", "hour_rank", "popups", "gashapon", "gift_wall", "red_packet", "redeem_shop", "privilege_shop",
		"pia", "multi_combo", "shortcut_gift", "members", "config", "lucky_bag_entry", "lucky_bag", "wish_list", "lucky_box")
	kc.Check(metaGiftsElem{}, "title", "data")
	kc.Check(hourRank{}, "rank", "rank_up")
	kc.Check(gashapon{}, "buff", "default_icon_url", "label_icon_url", "open_url")
	kc.Check(RedPacket{}, "list", "name", "price", "icon_url", "big_icon_url", "icon_active_url", "image_url", "corner_icon_url", "intro", "intro_icon_url", "intro_open_url", "position")
	kc.Check(RedeemShop{}, "name", "web_icon_url", "icon_url", "shop_url")
	kc.Check(luckyBagEntry{}, "name")
	kc.Check(luckyBox{}, "name", "image_url", "label_icon_url")
}

func TestMetaGiftType(t *testing.T) {
	assert := assert.New(t)

	gifts := []gift.Gift{
		{Type: gift.TypeNoble, VipType: gift.VipTypeLiveNoble, NobleLevel: 1},
		{Type: gift.TypeNoble, VipType: gift.VipTypeLiveNoble, NobleLevel: 7},
		{Type: gift.TypeCustom, VipType: gift.VipTypeLiveNoble, NobleLevel: 7, BaseGiftID: 1},
		{Type: gift.TypeCustom},
		{Type: gift.TypeCustom, VipType: gift.VipTypeLiveHighness, NobleLevel: 1},
		{Type: gift.TypeNoble, VipType: gift.VipTypeLiveHighness, NobleLevel: 1},
	}
	wantType := []int{typeOtherGift, typeNoble7BaseGift, typeNoble7CustomGift, typeOtherGift,
		typeHighnessCustomGift, typeHighnessNobleGift}

	for i := range gifts {
		assert.Equal(wantType[i], metaGiftType(gifts[i]))
	}
}

func TestNewMetaGift(t *testing.T) {
	assert := assert.New(t)

	nobleGift := gift.Gift{Type: gift.TypeNoble, VipType: gift.VipTypeLiveNoble, NobleLevel: 7}
	highnessGift := gift.Gift{Type: gift.TypeNoble, VipType: gift.VipTypeLiveHighness, NobleLevel: 1}
	normalGift := gift.Gift{Type: gift.TypeNormal, BaseGiftID: 1}
	customNobleGift := gift.Gift{Type: gift.TypeCustom, VipType: gift.VipTypeLiveNoble, NobleLevel: 7, BaseGiftID: 1}
	customHighnessGift := gift.Gift{Type: gift.TypeCustom, VipType: gift.VipTypeLiveHighness, NobleLevel: 1}

	now := goutil.TimeNow().Unix()
	var (
		notVip     map[int]*vip.UserVip = nil
		activeVip7                      = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now + 100},
		}
		deadline7 = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now - 100},
			2: {Level: 1, ExpireTime: now - 10000000},
		}
		notActiveVip7 = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now - 10000000},
		}
		activeHighness = map[int]*vip.UserVip{
			2: {Level: 1, ExpireTime: now + 100},
		}
		deadlineHighness = map[int]*vip.UserVip{
			2: {Level: 1, ExpireTime: now - 100},
		}
		deadlineHighnessVip7 = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now + 100},
			2: {Level: 1, ExpireTime: now - 100},
		}
		activeVip6 = map[int]*vip.UserVip{
			1: {Level: 6, ExpireTime: now + 100},
		}
		deadlineVip6 = map[int]*vip.UserVip{
			1: {Level: 6, ExpireTime: now - 100},
		}
		notActiveVipHighness = map[int]*vip.UserVip{
			2: {Level: 1, ExpireTime: now - 10000000},
		}
		activeHighnessVip6 = map[int]*vip.UserVip{
			1: {Level: 6, ExpireTime: now},
			2: {Level: 1, ExpireTime: now + 100},
		}
		expireHighnessNoble = map[int]*vip.UserVip{
			1: {Level: 6, ExpireTime: now},
			2: {Level: 1, ExpireTime: now - 10000000},
		}
		activeHighnessNoble = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now - 10000000},
			2: {Level: 1, ExpireTime: now + 100},
		}
		deadlineHighnessNoble = map[int]*vip.UserVip{
			1: {Level: 7, ExpireTime: now - 10000000},
			2: {Level: 1, ExpireTime: now - 100},
		}
		expireVip = map[int]*vip.UserVip{
			1: {Level: 6, ExpireTime: now - 10000000},
			2: {Level: 1, ExpireTime: now - 10000000},
		}

		buyButton = &metaGiftButton{
			Text: "续费贵族",
			Type: giftButtonTypeURL,
			URL:  config.Conf.Params.NobleParams.BuyNobleURL,
		}
		openButton = &metaGiftButton{
			Text: "开通贵族",
			Type: giftButtonTypeURL,
			URL:  config.Conf.Params.NobleParams.BuyNobleURL,
		}
		upgradeButton = &metaGiftButton{
			Text: "升级贵族",
			Type: giftButtonTypeURL,
			URL:  config.Conf.Params.NobleParams.BuyNobleURL,
		}
		customButton = &metaGiftButton{
			Text: "去定制",
			Type: giftButtonTypeURL,
			URL:  config.Conf.Params.NobleParams.CustomNobleGiftURL,
		}
	)

	type args struct {
		g gift.Gift
		v map[int]*vip.UserVip
	}
	tests := []struct {
		name string
		args args
		want *metaGift
	}{
		{"非神话礼物，不返回按钮", args{normalGift, activeVip7}, &metaGift{}},
		{"神话礼物，神话用户，不返回按钮", args{nobleGift, activeVip7}, &metaGift{}},
		{"神话礼物，非神话用户，返回去定制按钮", args{nobleGift, notVip}, &metaGift{Button: customButton}},
		{"神话礼物，保护期神话用户，返回续费按钮", args{nobleGift, deadline7}, &metaGift{Button: buyButton}},
		{"神话礼物，过期神话用户，返回定制按钮", args{nobleGift, notActiveVip7}, &metaGift{Button: customButton}},
		{"神话礼物，低等级贵族用户上神未过期，不返回按钮", args{nobleGift, activeHighnessVip6}, &metaGift{}},
		{"神话礼物，低等级贵族用户，返回去定制", args{nobleGift, activeVip6}, &metaGift{Button: customButton}},
		{"神话礼物，上神用户，不返回按钮", args{nobleGift, activeHighness}, &metaGift{}},
		{"神话礼物，保护期上神用户，不返回按钮", args{nobleGift, deadlineHighness}, &metaGift{}},
		{"神话礼物，过期上神用户，返回定制按钮", args{nobleGift, notActiveVipHighness}, &metaGift{Button: customButton}},
		{"神话礼物，低等级贵族上神过期，返回去定制", args{nobleGift, expireHighnessNoble}, &metaGift{Button: customButton}},
		{"神话礼物，神话过期上神没过期，不返回按钮", args{nobleGift, activeHighnessNoble}, &metaGift{}},
		{"神话礼物，神话过期上神保护期，不返回按钮", args{nobleGift, deadlineHighnessNoble}, &metaGift{}},
		{"定制神话礼物，神话贵族有效上神保护期，不返回按钮", args{customNobleGift, deadlineHighnessVip7}, &metaGift{}},
		{"定制神话礼物，神话、上神贵族过期，不返回礼物", args{customNobleGift, expireVip}, nil},
		{"定制神话礼物，保护期上神用户，返回续费按钮", args{customNobleGift, deadlineHighness}, &metaGift{Button: buyButton}},
		{"定制神话礼物，保护期低等级贵族，不返回礼物", args{customNobleGift, deadlineVip6}, nil},
		{"定制上神礼物，神话、上神贵族过期，不返回礼物", args{customHighnessGift, expireVip}, nil},
		{"定制上神礼物，保护期上神用户，返回续费按钮", args{customHighnessGift, deadlineHighness}, &metaGift{Button: buyButton}},
		{"普通上神礼物，上神用户，不返回按钮", args{highnessGift, activeHighness}, &metaGift{}},
		{"普通上神礼物，上神保护期，返回续费按钮", args{highnessGift, deadlineHighness}, &metaGift{Button: buyButton}},
		{"普通上神礼物，过期上神用户，返回开通贵族", args{highnessGift, notActiveVipHighness}, &metaGift{Button: openButton}},
		{"普通上神礼物，过期上神低等级贵族保护期用户，返回开通贵族", args{highnessGift, expireHighnessNoble}, &metaGift{Button: openButton}},
		{"普通上神礼物，神话用户，返回升级贵族", args{highnessGift, activeVip7}, &metaGift{Button: upgradeButton}},
		{"普通上神礼物，神话、上神贵族过期，返回开通贵族", args{highnessGift, expireVip}, &metaGift{Button: openButton}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metaGift, _ := metaResp{
				uvMap: tt.args.v,
			}.newMetaGift(tt.args.g)
			if tt.want != nil {
				assert.Equal(tt.want.Button, metaGift.Button, tt.name)
			} else {
				assert.Equal(tt.want, metaGift, tt.name)
			}
		})
	}
}

func TestMetaLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var resp metaResp
	c := handler.NewTestContext("GET", "/meta", false, nil)
	assert.Equal(actionerrors.ErrParams, resp.load(c), "room_id")
	c = handler.NewTestContext("GET", fmt.Sprintf("/meta?room_id=%d", room.TestNonexistentRoomID), true, nil)
	assert.Equal(actionerrors.ErrCannotFindRoom, resp.load(c), "找不到房间")
	c = handler.NewTestContext("GET", "/meta?room_id=3192516&&type=-65535", false, nil)
	assert.Equal(actionerrors.ErrParams, resp.load(c), "type 太小")
	c = handler.NewTestContext("GET", fmt.Sprintf("/meta?room_id=3192516&&type=%d", metaTypeLimit), false, nil)
	assert.Equal(actionerrors.ErrParams, resp.load(c), "type 太大")
	c = handler.NewTestContext("GET", "/meta?room_id=3192516", true, nil)
	require.NoError(resp.load(c))
	assert.Equal(metaTypeAll, resp.metaType)
	assert.Equal(int64(3192516), resp.roomID)
	require.NotNil(resp.room)
	assert.NotZero(resp.room.Statistics.Revenue)
	assert.NotNil(resp.equip)
	assert.NotZero(resp.userID)
	c = handler.NewTestContext("GET", "/meta?room_id=3192516&type=1", true, nil)
	require.NoError(resp.load(c))
	assert.Equal(metaTypeGifts, resp.metaType)
}

func TestMetaResp_hasType(t *testing.T) {
	assert := assert.New(t)

	// 新版本
	resp := metaResp{
		metaType: 8,
	}
	ok := resp.hasType(metaTypePK)
	assert.True(ok)

	resp = metaResp{
		metaType: 0,
	}
	ok = resp.hasType(metaTypePK)
	assert.True(ok)

	resp = metaResp{
		metaType: 8,
	}
	ok = resp.hasType(metaTypeGiftWall)
	assert.False(ok)
}

func TestSortGifts(t *testing.T) {
	assert := assert.New(t)

	gifts := []*metaGift{
		{Gift: gift.Gift{Type: gift.TypeMedal, Order: 2}},
		{Gift: gift.Gift{Type: gift.TypeSuperFan, Order: 1}},
	}
	sortGifts(gifts)
	assert.Equal(gift.TypeSuperFan, gifts[0].Type)

	gifts = []*metaGift{
		{Gift: gift.Gift{Type: gift.TypeNormal, Order: 2}},
		{Gift: gift.Gift{Type: gift.TypeDrawSend, Order: 3}},
		{Gift: gift.Gift{Type: gift.TypeMedal, Order: 1}},
	}
	sortGifts(gifts)
	assert.Equal(1, gifts[0].Order)
}

func TestMetaFindGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyOnlineGifts0.Format()
	gifts := []gift.Gift{
		{Type: 1, Order: 2},
		{Type: 1, Icon: "test", Order: 1},
		{Type: 1, Order: 3},
		{Type: 1, NobleLevel: 1},
		{Type: 2, UserID: 123},
		{Type: 2, UserID: 123, VipType: 1, NobleLevel: 7},
		{Type: 2, UserID: testHighnessUserID, VipType: 2, NobleLevel: 1},
		{Type: 2},
		{Type: 2, UserID: 456},
		{Type: 3},
		{Type: 7, RoomID: 456},
		{Type: 7, RoomID: 789},
		{Type: 8},
	}
	service.Cache5s.Set(key, gifts, 0)
	defer service.Cache5s.Flush()
	resp := metaResp{
		userID:   123,
		roomID:   456,
		metaType: 2,
		equip:    &goutil.Equipment{},
		room: &room.Room{
			Helper: room.Helper{
				Status: room.Status{Open: room.StatusOpenTrue},
			},
		},
	}
	c := handler.NewTestContext("GET", "/meta", false, nil)
	require.NoError(resp.findGifts(c))
	assert.Nil(resp.Gifts)

	resp.metaType = metaTypeGifts
	require.NoError(resp.findGifts(c))
	require.Len(resp.Gifts, 3)
	assert.Equal(resp.Gifts[0].Title, "礼物")
	assert.Equal(resp.Gifts[1].Title, "贵族")
	assert.Equal(resp.Gifts[2].Title, "专属")

	resp.equip = &goutil.Equipment{ // old equip
		FromApp:    true,
		OS:         goutil.IOS,
		AppVersion: "4.9.1",
	}
	resp.metaType = metaTypeGifts
	require.NoError(resp.findGifts(c))
	require.Len(resp.Gifts, 2)
	assert.Equal(resp.Gifts[0].Title, "礼物")
	require.Len(resp.Gifts[0].Data, 6)
	// 第一个，个人定制
	assert.Equal(gift.TypeCustom, resp.Gifts[0].Data[0].Type)
	assert.EqualValues(123, resp.Gifts[0].Data[0].UserID)
	assert.Zero(resp.Gifts[0].Data[0].NobleLevel)
	// 第二个，房间定制
	assert.Equal(gift.TypeRoomCustom, resp.Gifts[0].Data[1].Type)
	assert.Equal(int64(456), resp.Gifts[0].Data[1].RoomID)
	// 第三个, 随机礼物
	assert.Equal(gift.TypeDrawSend, resp.Gifts[0].Data[2].Type)
	assert.Equal("test", resp.Gifts[0].Data[3].Icon)
	assert.Equal(resp.Gifts[1].Title, "贵族")
	assert.Len(resp.Gifts[1].Data, 2)

	// 测试上神礼物展示
	resp.userID = testHighnessUserID
	service.Cache5s.Set(key, gifts, 0)
	c = handler.NewTestContext("GET", "/meta", true, nil)
	c.User().ID = testHighnessUserID
	require.NoError(resp.findGifts(c))
	require.NotNil(resp.Gifts[0])
	assert.Len(resp.Gifts[0].Data, 5)
	require.NotNil(resp.Gifts[1])
	assert.Len(resp.Gifts[1].Data, 2)

	c = handler.NewTestContext("GET", "/meta", false, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.7.4"
	require.NoError(resp.findGifts(c))
	require.NotNil(resp.Gifts[0])
	assert.Len(resp.Gifts[0].Data, 5)
	require.NotNil(resp.Gifts[1])
	assert.Len(resp.Gifts[1].Data, 1)

	option, err := interaction.FindVoteOption()
	require.NoError(err)
	assert.Len(option.GiftIDs(), 4)
	gifts = []gift.Gift{{Type: 1}}
	for i := range option.GiftIDs() {
		gifts = append(gifts, gift.Gift{Type: 1, GiftID: option.GiftIDs()[i]})
	}
	service.Cache5s.SetDefault(key, gifts)
	require.NoError(resp.findGifts(c))
	assert.Len(resp.Gifts[0].Data, 5)
}

func TestFindInteractiveGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheLuckyBoxKey := keys.KeyParams1.Format(params.KeyLuckyBox)
	cacheGashaponKey := keys.KeyParams1.Format(params.KeyGashapon)
	cacheRedPacketKey := keys.KeyParams1.Format(params.KeyRedPacket)
	require.NoError(service.LRURedis.Del(cacheLuckyBoxKey, cacheGashaponKey, cacheRedPacketKey, cacheRedPacketKey).Err())

	// 无活动礼物
	resp := new(metaResp)
	resp.room = &room.Room{
		Helper: room.Helper{
			Status: room.Status{Open: room.StatusOpenTrue},
		},
	}
	resp.equip = &goutil.Equipment{FromApp: false}
	resp.Gifts = make([]*metaGiftsElem, 1)
	resp.Gifts[0] = new(metaGiftsElem)
	resp.Gifts[0].Data = make([]*metaGift, 20)
	require.NoError(service.LRURedis.Set(cacheGashaponKey, tutil.SprintJSON(params.Gashapon{
		Key:           params.KeyGashapon,
		WebOldVersion: true,
	}), 10*time.Second).Err())
	err := resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 20)

	// 有单个活动礼物
	box := params.LuckyBox{
		Key:      params.KeyLuckyBox,
		Name:     "宝盒",
		Status:   3,
		Position: 5,
		Intro:    "宝盒说明",
	}
	require.NoError(service.LRURedis.Set(cacheLuckyBoxKey, tutil.SprintJSON(box), 10*time.Second).Err())
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 21)
	assert.EqualValues(resp.Gifts[0].Data[box.Position].Name, box.Name)

	// 有多个活动礼物
	resp.Gifts[0].Data = make([]*metaGift, 10)
	packet := params.RedPacket{
		Key:      params.KeyRedPacket,
		Name:     "红包",
		Position: 8,
		Intro:    "红包说明",
	}
	require.NoError(service.LRURedis.Set(cacheRedPacketKey, tutil.SprintJSON(packet), 10*time.Second).Err())
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 12)
	assert.EqualValues(resp.Gifts[0].Data[box.Position].Name, box.Name)
	assert.EqualValues(resp.Gifts[0].Data[packet.Position].Name, packet.Name)

	// 客户端旧版本
	resp.Gifts[0].Data = make([]*metaGift, 10)
	resp.equip.FromApp = true
	resp.equip.OS = goutil.Android
	resp.equip.AppVersion = "6.2.2"
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 10)

	// 宝盒测试房间 非测试房间无宝盒
	box = params.LuckyBox{
		Key:          params.KeyLuckyBox,
		Name:         "宝盒宝盒",
		Status:       3,
		Position:     5,
		Intro:        "宝盒说明",
		OpenTime:     goutil.TimeNow().AddDate(0, 0, 1).Unix(),
		AllowRoomIDs: []int64{1001, 1002, 1003, 1004},
	}
	require.NoError(service.LRURedis.Set(cacheLuckyBoxKey, tutil.SprintJSON(box), 10*time.Second).Err())
	resp.Gifts[0].Data = make([]*metaGift, 10)
	resp.equip = &goutil.Equipment{FromApp: false}
	resp.roomID = 1000
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 11)

	// 宝盒测试房间 测试房间有宝盒
	resp.Gifts[0].Data = make([]*metaGift, 10)
	resp.roomID = 1002
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 12)
	assert.EqualValues(resp.Gifts[0].Data[box.Position].Name, box.Name)

	// 存在超能魔方
	resp.Gifts[0].Data = make([]*metaGift, 10)
	gashapon := params.Gashapon{
		Key:      params.KeyGashapon,
		Name:     "超能魔方",
		Status:   3,
		Position: 8,
		Intro:    "超能魔方说明",
	}
	require.NoError(service.LRURedis.Set(cacheGashaponKey, tutil.SprintJSON(gashapon), 10*time.Second).Err())
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 13)
	assert.EqualValues(resp.Gifts[0].Data[box.Position].Name, box.Name)
	assert.EqualValues(resp.Gifts[0].Data[gashapon.Position].Name, gashapon.Name)

	resp.room.Helper.Status.Open = room.StatusOpenFalse
	err = resp.findInteractiveGifts()
	require.NoError(err)
	require.Len(resp.Gifts[0].Data, 12)
}

func TestMetaResp_buildUpgradeGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		userID     = int64(123)
		giftID     = int64(456)
		toggleTime = int64(123456789)
	)
	userMeta := usermeta.UserMeta{
		UserID: userID,
		GiftUpgrades: []*usermeta.GiftUpgrade{
			{
				GiftID:     giftID,
				ToggleTime: toggleTime,
				UpgradeNum: 1,
				SendCount:  1,
			},
		},
	}
	err := usermeta.Collection().FindOneAndReplace(context.Background(), bson.M{"user_id": userID}, userMeta).Err()
	require.NoError(err)
	require.NoError(livegiftupgrade.DB().Where("user_id = ? AND base_gift_id = ?", userID, giftID).Delete(&livegiftupgrade.GiftUpgradeRecord{}).Error)
	err = livegiftupgrade.DB().Create(&livegiftupgrade.GiftUpgradeRecord{
		BaseGiftID:    giftID,
		UpgradeGiftID: 1,
		UserID:        userID,
		RoomID:        1,
	}).Error
	require.NoError(err)
	require.NoError(livegiftupgrade.DB().Where("base_gift_id = ?", giftID).Delete(&livegiftupgrade.GiftUpgrade{}).Error)
	upgradeGifts := []*livegiftupgrade.GiftUpgrade{
		{
			Type:          livegiftupgrade.GiftUpgradeTypeBase,
			BaseGiftID:    giftID,
			UpgradeGiftID: giftID,
			More:          []byte(`{"first_upgrade_num": 3,"upgrade_num":10,"label_icon":"http://test.com/icon"}`),
		},
		{
			Type:          livegiftupgrade.GiftUpgradeTypeUpgrade,
			BaseGiftID:    giftID,
			UpgradeGiftID: 1,
			Weight:        100,
		},
		{
			Type:          livegiftupgrade.GiftUpgradeTypeUpgrade,
			BaseGiftID:    giftID,
			UpgradeGiftID: 2,
			Weight:        10,
		},
		{
			Type:          livegiftupgrade.GiftUpgradeTypeFull,
			BaseGiftID:    giftID,
			UpgradeGiftID: 3,
			Weight:        0,
		},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), upgradeGifts)
	require.NoError(err)

	resp := metaResp{
		userID: userID,
	}
	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	elem := []*metaGiftsElem{{Data: []*metaGift{{Gift: gift.Gift{GiftID: giftID, Attr: attr, ToggleTime: toggleTime}}}}}
	giftList := []gift.Gift{
		{GiftID: giftID, Attr: attr, ToggleTime: toggleTime},
		{GiftID: 1},
		{GiftID: 2},
		{GiftID: 3},
	}
	require.NoError(resp.buildUpgradeGifts(elem, giftList))
	require.Len(elem, 1)
	require.Len(elem[0].Data, 1)
	require.NotNil(elem[0].Data[0].Upgrade)
	assert.NotNil(elem[0].Data[0].Upgrade.Discount)
	assert.NotNil(elem[0].Data[0].Upgrade.UpgradeNum)
	require.Len(elem[0].Data[0].Upgrade.Gifts, 4)
}

func TestMetaFindMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		userID:   0,
		roomID:   114,
		metaType: 2,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	// 用户 ID 没有
	assert.NoError(resp.findMedal())
	assert.Nil(resp.Medal)
	resp.userID = 12
	resp.metaType = 1
	// type 不对
	assert.NoError(resp.findMedal())
	assert.Nil(resp.Medal)
	// 未获得 medal
	resp.metaType = metaTypeAll
	var medal livemedal.LiveMedal
	medal.UserID = resp.userID
	medal.RoomID = resp.roomID
	medal.Point = 1
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemedal.Collection()
	_, err := col.UpdateOne(ctx,
		bson.M{"room_id": resp.roomID, "user_id": resp.userID},
		bson.M{
			"$set": bson.M{
				"status": livemedal.StatusPending,
			}},
		options.Update().SetUpsert(true))
	require.NoError(err)
	require.NoError(resp.findMedal())
	assert.Nil(resp.Medal)
	// 正常查询
	_, err = col.UpdateOne(ctx,
		bson.M{"room_id": resp.roomID, "user_id": resp.userID},
		bson.M{
			"$set": bson.M{
				"status": livemedal.StatusShow,
				"point":  medal.Point,
			}})
	require.NoError(err)
	require.NoError(resp.findMedal())
	require.NotNil(resp.Medal)
	assert.Equal(medal.Point, resp.Medal.Point)
	assert.NotZero(resp.Medal.Level)
	roomID := int64(10244)
	userID := int64(77785795)
	_, err = col.UpdateOne(ctx,
		bson.M{"room_id": roomID, "user_id": userID},
		bson.M{
			"$set": bson.M{
				"status": livemedal.StatusShow,
			},
			"$setOnInsert": bson.M{
				"point": 1,
			}},
		options.Update().SetUpsert(true))
	require.NoError(err)
	resp.userID = userID
	resp.roomID = roomID
	require.NoError(resp.findMedal())
	require.NotNil(resp.Medal)
	assert.Greater(resp.Medal.Level, 0)
}

func TestMetaFindInteraction(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(20201110)
	resp := metaResp{
		roomID:   roomID,
		metaType: 4,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livevote.Collection().UpdateOne(ctx, bson.M{"room_id": roomID}, bson.M{"$set": livevote.Vote{
		RoomID:    roomID,
		CreatorID: roomID,
		Content:   []*livevote.VoteContent{{GiftID: 2}},
		EndTime:   goutil.TimeNow().Add(8 * time.Second),
	}}, options.Update().SetUpsert(true))
	require.NoError(err)
	key := keys.LockRoomsInteraction1.Format(roomID)
	require.NoError(service.Redis.Del(key).Err())

	// 无进行中的互动
	require.NoError(resp.findInteraction())
	assert.Nil(resp.Interaction)

	// 当前进行的未非投票活动
	require.NoError(service.Redis.Set(key, "other", 30*time.Second).Err())
	require.NoError(resp.findInteraction())
	assert.Nil(resp.Interaction)

	// 投票互动进行中
	require.NoError(service.Redis.Set(key, interaction.TypeInteractionVote, 30*time.Second).Err())
	resp.metaType = metaTypeInteraction
	require.NoError(resp.findInteraction())
	assert.NotNil(resp.Interaction)
}

func TestMetaFindEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// mock events 缓存
	key := keys.KeyRecommendedEvents1.Format(goutil.TimeNow().Minute() / 5)
	require.NoError(service.Redis.Set(key, "[{}]", 5*time.Minute).Err())

	resp := metaResp{
		room: &room.Room{
			Helper: room.Helper{
				CatalogID: 104,
				TagIDs:    []int64{1, 2},
			},
		},
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	resp.metaType = metaTypePopups // 错误的 type
	assert.NoError(resp.findEvents())
	assert.Empty(resp.Events)

	resp.metaType = metaTypeEvents
	require.NoError(resp.findEvents())
	assert.NotEmpty(resp.Events)

	require.NoError(service.Redis.Set(key, `[{"cover":"","url":"","start_time":null,"show_close":false,"extended_fields":"{\"tag_ids\":[2,3]}"}]`, 5*time.Minute).Err())
	require.NoError(resp.findEvents())
	assert.Len(resp.Events, 1)
}

func TestRoomShowConfigShouldShow(t *testing.T) {
	assert := assert.New(t)

	c := roomShowConfig{
		AllRoomShow: true,
	}
	resp := &metaResp{room: &room.Room{}}
	assert.True(c.shouldShow(resp))

	c = roomShowConfig{
		TagIDs: []int64{1, 2, 3},
	}
	resp.room = &room.Room{
		Helper: room.Helper{
			TagIDs: []int64{2, 3, 4},
		},
	}
	assert.True(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationBlockList
	assert.False(c.shouldShow(resp))

	c = roomShowConfig{
		RoomIDs: []int64{2233},
	}
	resp.room = &room.Room{
		Helper: room.Helper{
			RoomID: 2233,
		},
	}
	assert.True(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationBlockList
	assert.False(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationAllowList
	resp.room = &room.Room{
		Helper: room.Helper{
			CatalogID: 116,
		},
	}
	resp.subCatalogMap = map[int64]int64{116: 104}
	c = roomShowConfig{
		CatalogIDs: []int64{104, 105},
	}
	assert.True(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationBlockList
	assert.False(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationAllowList
	resp.room = &room.Room{
		Helper: room.Helper{
			CatalogID: 117,
		},
	}
	assert.False(c.shouldShow(resp))

	c.ApplicationType = liverecommendedelements.ApplicationBlockList
	assert.True(c.shouldShow(resp))
}

func TestMetaFindHourRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		room: new(room.Room),
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	resp.metaType = metaTypePopups // 错误的 type
	require.NoError(resp.findHourRank())
	assert.Nil(resp.HourRank)

	resp.metaType = metaTypeHourRank
	require.NoError(resp.findHourRank())
	assert.NotNil(resp.HourRank)
}

func TestMetaFindPopups(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	liverecommendedelements.ClearPopupCache()

	c := handler.NewTestContext("GET", "/meta", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "4.7.7"
	resp := metaResp{
		room: new(room.Room),
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	now := goutil.TimeNow()
	startTime := now.Unix()
	m := []liverecommendedelements.Popup{
		{
			Sort: 1,
			Attribute: liverecommendedelements.Attribute{
				URL:       "http://fm.example.com/mini?webview=1;;http://fm.example.com/full_url?webview=1",
				Cover:     "http://fm.example.com/image1.png",
				StartTime: &startTime,
			},
			ExpireTime: now.Add(time.Minute).Unix(),
		},
		{
			Sort: 2,
			Attribute: liverecommendedelements.Attribute{
				URL:       "http://fm.example.com/mini?webview=1;http://fm.example.com/open_url?webview=1;",
				Cover:     "http://fm.example.com/image2.png",
				StartTime: &startTime,
			},
			ExpireTime: now.Add(time.Minute).Unix(),
		},
		{
			Sort: 3,
			Attribute: liverecommendedelements.Attribute{
				URL:       "http://fm.example.com/mini?webview=1;http://fm.example.com/open_url?webview=1;",
				Cover:     "http://fm.example.com/image2.png",
				StartTime: &startTime,
			},
			ElementID:  usersrank.EventIDMarchStarSign,
			ExpireTime: now.Add(time.Minute).Unix(),
		},
	}

	cacheKey := keys.KeyRecommendedPopups1.Format(goutil.TimeNow().Minute() / 5)
	str := tutil.SprintJSON(m)
	require.NoError(service.Redis.Set(cacheKey, str, 5*time.Minute).Err())
	keyEventTime := keys.KeyEventInfo1.Format(usersrank.EventIDMarchStarSign)
	s := &mevent.Simple{
		EndTime:        now.Add(time.Minute).Unix(),
		ExtendedFields: `{"rank_end_time":1,"promoted_score":88888}`,
	}
	service.Cache5Min.Set(keyEventTime, s, 0)
	resp.metaType = metaTypePopups
	err := resp.findPopups()
	require.NoError(err)
	var popups *NewPopups
	require.IsType(popups, resp.Popups)
	popups = resp.Popups.(*NewPopups)
	assert.Len(popups.Data, 3)
	assert.False(resp.Popups.(*NewPopups).Fold)
	assert.Equal(m[0].Cover, popups.Data[0].ImageURL)
	assert.Equal(m[1].Cover, popups.Data[1].ImageURL)

	// 增加小窗图片地址的 URL
	m[1].Attribute.URL = "http://fm.example.com/mini?webview=1;http://fm.example.com/fold_image_url?webview=1;;http://fm.example.com/open_url?webview=1" // 增加小窗图片地址 URL
	str = tutil.SprintJSON(m)
	require.NoError(service.Redis.Set(cacheKey, str, 5*time.Minute).Err())
	resp.Popups = nil
	resp.metaType = metaTypePopups
	err = resp.findPopups()
	require.NoError(err)
	require.NotEmpty(resp.findPopups)
	require.IsType(popups, resp.Popups)
	popups = resp.Popups.(*NewPopups)
	assert.Len(popups.Data, 3)
	assert.False(popups.Fold)
	assert.Equal(m[1].Cover, popups.Data[1].ImageURL)
	assert.Equal("http://fm.example.com/fold_image_url?webview=1", popups.Data[1].FoldImageURL)
	require.NoError(service.Redis.Del(cacheKey).Err())

	// 错误的 URL
	m[0].Attribute.URL = "http://fm.example.com/mini?webview=1;http://fm.example.com/open_url?webview=1"
	m[1].Attribute.URL = "http://fm.example.com/mini?webview=1;http://fm.example.com/open_url?webview=1;"
	str = tutil.SprintJSON(m)
	require.NoError(service.Redis.Set(cacheKey, str, 5*time.Minute).Err())
	resp.Popups = nil
	resp.metaType = metaTypePopups
	err = resp.findPopups()
	require.NoError(err)
	require.NotEmpty(resp.findPopups)
	require.IsType(popups, resp.Popups)
	popups = resp.Popups.(*NewPopups)
	assert.Equal(m[1].Cover, popups.Data[0].ImageURL)
	require.NoError(service.Redis.Del(cacheKey).Err())

	// 大流量主播
	resp.Popups = nil
	resp.room.Config = &room.Config{Popularity: 1}
	err = resp.findPopups()
	require.NoError(err)
	require.IsType(popups, resp.Popups)
	popups = resp.Popups.(*NewPopups)
	assert.True(popups.Fold)

	// 兼容: 安卓 < 5.6.5 或 iOS < 4.7.7 的版本返回旧的数据格式
	resp.Popups = nil
	m[0].Attribute.URL = "http://fm.example.com/mini?webview=1;http://fm.example.com/fold_image_url?webview=1;;http://fm.example.com/open_url?webview=1"
	m[1].Attribute.URL = "http://fm.example.com/mini?webview=1;http://fm.example.com/fold_image_url?webview=1;;http://fm.example.com/open_url?webview=1"
	resp.room.Config.Popularity = 0
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.7.6"
	resp.equip = c.Equip()
	str = tutil.SprintJSON(m)
	require.NoError(service.Redis.Set(cacheKey, str, 5*time.Minute).Err())
	resp.metaType = metaTypePopups
	err = resp.findPopups()
	require.NoError(err)
	var popupItem []*liverecommendedelements.PopupRespItem
	require.IsType(popupItem, resp.Popups)
	popupItem = resp.Popups.([]*liverecommendedelements.PopupRespItem)
	assert.Len(popupItem, 3)
	require.NoError(service.Redis.Del(cacheKey).Err())
}

func TestFindShowPopupsByRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 2, 16, 11, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := keys.KeyRecommendedPopups1.Format(goutil.TimeNow().Minute() / 5)
	require.NoError(service.Redis.Del(key).Err())
	list := []liverecommendedelements.Popup{
		{ElementID: 1, ExtendedFields: "invalid"},
		{ElementID: 2, ExtendedFields: ""},
		{ElementID: 3, ExtendedFields: `{"all_room_show":true}`},
		{ElementID: 4, ExtendedFields: `{"all_room_show":false,"catalog_ids":[104,105],"tag_ids":[1,2],"room_ids":[122340]}`},
	}
	b, err := json.Marshal(list)
	require.NoError(err)
	require.NoError(service.Redis.Set(key, b, 5*time.Minute).Err())

	resp := &metaResp{
		room: &room.Room{
			Helper: room.Helper{RoomID: 122341, CatalogID: 118},
		},
	}
	showPopups, err := resp.findShowPopupsByRoom()
	require.NoError(err)
	require.Len(showPopups, 3)
	assert.Equal(int64(2), showPopups[0].ElementID)
	assert.Equal(int64(3), showPopups[1].ElementID)
	assert.Equal(int64(4), showPopups[2].ElementID)

	resp.room = &room.Room{
		Helper: room.Helper{TagIDs: []int64{2, 3}},
	}
	showPopups, err = resp.findShowPopupsByRoom()
	require.NoError(err)
	require.Len(showPopups, 3)
	assert.Equal(int64(4), showPopups[2].ElementID)

	resp.room = &room.Room{
		Helper: room.Helper{RoomID: 122340},
	}
	showPopups, err = resp.findShowPopupsByRoom()
	require.NoError(err)
	require.Len(showPopups, 3)
	assert.Equal(int64(4), showPopups[2].ElementID)
	resp.room = &room.Room{
		Helper: room.Helper{CatalogID: 118},
	}
	showPopups, err = resp.findShowPopupsByRoom()
	require.NoError(err)
	require.Len(showPopups, 3)
	assert.Equal(int64(4), showPopups[2].ElementID)
	resp.room = &room.Room{
		Helper: room.Helper{
			CatalogID: 166,
			Statistics: room.Statistics{
				Revenue: room.NovaRevenueThreshold + 1,
			},
		},
	}
	showPopups, err = resp.findShowPopupsByRoom()
	require.NoError(err)
	require.Len(showPopups, 2, "二级分区不存在")
	assert.Equal(int64(2), showPopups[0].ElementID)
	assert.Equal(int64(3), showPopups[1].ElementID)
}

func TestActionMeta(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(3192516)
	)

	c := handler.NewTestContext(http.MethodGet, "/meta", false, nil)
	_, err := ActionMeta(c)
	assert.Equal(actionerrors.ErrParams, err)

	_, err = room.UpdateOneRoom(bson.M{"room_id": testRoomID}, bson.M{"config.allow_hide_gift_effect": true})
	require.NoError(err)

	uri := fmt.Sprintf("/meta?room_id=%d", testRoomID)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err := ActionMeta(c)
	require.NoError(err)
	var resp *metaResp
	require.IsType(resp, r)
	resp = r.(*metaResp)
	assert.NotNil(resp.Gifts)
	assert.NotNil(resp.HourRank)
	var newPopups *NewPopups
	assert.IsType(resp.Popups, newPopups)
	newPopups = resp.Popups.(*NewPopups)
	assert.False(newPopups.Fold)
	require.NotNil(resp.Config)
	assert.True(resp.Config.AllowHideGiftEffect)

	c.Equip().OS = goutil.IOS
	c.Equip().FromApp = true
	c.Equip().AppVersion = "4.7.6"
	r, err = ActionMeta(c)
	require.NoError(err)
	require.IsType(resp, r)
	resp = r.(*metaResp)
	var popupItem []*liverecommendedelements.PopupRespItem
	assert.IsType(popupItem, resp.Popups)
}

func TestFindAllWorkingEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tag.ClearAllShowTagsCache()
	var allEvents []liverecommendedelements.Event

	resp := &metaResp{
		roomID: 10240,
		room: &room.Room{
			Helper: room.Helper{
				CatalogID: 105,
			},
		},
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	events, err := resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 0)

	allEvents = append(allEvents, liverecommendedelements.Event{ID: 1})
	events, err = resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 1)
	ef := liverecommendedelements.EventExtendedFields{
		CatalogIDs: []int64{105},
		TagIDs:     []int64{3},
		RoomIDs:    []int64{2236},
	}
	v, err := json.Marshal(ef)
	require.NoError(err)
	allEvents = append(allEvents, liverecommendedelements.Event{ID: 2, ExtendedFields: string(v)})

	resp.roomID = 2234
	resp.room.Helper = room.Helper{}
	events, err = resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 1)
	resp.roomID = 20200923
	resp.room.Helper = room.Helper{
		CatalogID: 120,
	}
	events, err = resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 2)
	resp.room.Helper = room.Helper{
		TagIDs: []int64{3},
		RoomID: 2236,
	}
	events, err = resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 2)
	events, err = resp.findAllWorkingEvents(allEvents)
	require.NoError(err)
	assert.Len(events, 2)
}

func TestFindEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tag.ClearAllShowTagsCache()
	liverecommendedelements.ClearEventCache()

	startTime := goutil.TimeNow().Unix()
	newCover := "https://static-test.missevan.com/live/event/202408/23/f19967e483a3896673ed175932dd2521113111.png"
	oldCover := "https://static-test.missevan.com/live/event/202408/23/f19967e483a3896673ed175932dd2521113222.png"
	extendedFields := liverecommendedelements.EventExtendedFields{
		OldCover: oldCover,
	}
	v, err := json.Marshal(extendedFields)
	require.NoError(err)
	event := liverecommendedelements.Event{
		ID:   2001,
		Sort: 1,
		Attribute: liverecommendedelements.Attribute{
			Name:      "testName",
			Cover:     newCover,
			StartTime: &startTime,
			URL:       "testURL",
			Attr:      goutil.BitMask(1),
		},
		ExpireTime:     startTime + 10000,
		ExtendedFields: string(v),
	}
	err = event.Save(service.DB)
	require.NoError(err)

	// 测试新版本 cover
	resp := metaResp{
		room: &room.Room{
			Helper: room.Helper{},
		},
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "6.2.0",
		},
		metaType: metaTypeEvents,
	}
	require.NoError(resp.findEvents())
	require.Len(resp.Events, 2)
	assert.EqualValues(newCover, resp.Events[1].Cover)
	// 测试旧版本 cover
	resp.equip.AppVersion = "6.1.8"
	require.NoError(resp.findEvents())
	assert.Len(resp.Events, 2)
	assert.EqualValues(oldCover, resp.Events[1].Cover)
}

func TestMetaRespFindGashapon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &metaResp{
		roomID: 10240,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "6.3.5",
		},
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 10240,
				Status: room.Status{Open: room.StatusOpenTrue},
			},
		},
	}
	paramsKey := keys.KeyParams1.Format(params.KeyGashapon)
	key := keys.KeyGashaponPoolRoom1.Format(resp.roomID)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.LRURedis.Del(paramsKey).Err())

	resp.metaType = metaTypePK // 错误的 type
	err := resp.findGashapon()
	require.NoError(err)
	assert.Nil(resp.Gashapon)

	resp.metaType = metaTypeGashapon
	service.LRURedis.Set(paramsKey, `{"status": 3}`, 5*time.Second)
	b, err := json.Marshal(gift.PoolGashapon{
		CurrentBuff: &gift.BuffRate{
			GiftID:     301,
			Multiplier: 1.2,
		},
	})
	require.NoError(err)
	err = service.Redis.Set(key, b, gift.BuffDuration).Err()
	require.NoError(err)
	defer service.Redis.Del(key)

	err = resp.findGashapon()
	require.NoError(err)
	assert.Equal(&gashapon{
		DefaultIconURL: "",
		Buff: &gift.GashaponBuff{
			GiftID:         301,
			Name:           "猫粮",
			IconURL:        "https://static-test.missevan.com/gifts/icons/007.png",
			RemainDuration: 180000,
			BuffDuration:   180000,
			Multiplier:     "1.2",
		},
	}, resp.Gashapon)
}

var (
	pkRoomID18113499 int64 = 18113499
	pkRoomID223344   int64 = 223344
)

func createTestPKData(status int, roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	_, err := livepk.PKCollection().InsertOne(ctx, livepk.LivePK{
		Fighters: [2]*livepk.Fighter{
			{
				RoomID:    roomIDs[0],
				CreatorID: 2,
				Score:     100,
			},
			{
				RoomID:    roomIDs[1],
				CreatorID: 1,
				Score:     99,
			},
		},
		Status:           status,
		StartTime:        goutil.NewTimeUnixMilli(now),
		PunishStartTime:  goutil.NewTimeUnixMilli(now.Add(livepk.RandomPKFightingDuration)),
		ConnectStartTime: goutil.NewTimeUnixMilli(now.Add(livepk.RandomPKFightingDuration + livepk.PKPunishmentDuration)),
		WinnerRoomID:     roomIDs[0],
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
	})
	if err != nil {
		return err
	}
	return nil
}

func clearTestPKData(roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PKCollection().DeleteMany(ctx, bson.M{
		"fighters.room_id": bson.M{"$in": roomIDs},
	})
	if err != nil {
		return err
	}
	return nil
}

func createTestPoolData(roomIDs ...int64) error {
	for _, roomID := range roomIDs {
		if _, err := livepk.InsertRandomPool(roomID, 10, 0); err != nil {
			return err
		}
	}
	return nil
}

func clearTestPoolData(roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PoolCollection().DeleteMany(ctx, bson.M{
		"room_id": bson.M{"$in": roomIDs},
	})
	if err != nil {
		return err
	}
	return nil
}

func TestFindPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testStatus := []int{
		livepk.PKRecordStatusFighting,
		livepk.PKRecordStatusPunishment,
	}
	resp := &metaResp{
		roomID:   pkRoomID18113499,
		metaType: metaTypeAll,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
	}
	var err error
	opt := &room.FindOptions{
		Projection: mongodb.NewProjection(
			"room_id, name, creator_id, creator_username, catalog_id, tag_ids"),
		FindCreator: true,
	}
	resp.room, err = room.Find(resp.roomID, opt)
	require.NoError(err)
	require.NotNil(resp.room)
	resp.userID = resp.room.CreatorID
	require.NoError(clearTestPoolData(pkRoomID18113499))
	for _, status := range testStatus {
		require.NoError(createTestPKData(status, pkRoomID223344, pkRoomID18113499))
		err = resp.findPK()
		require.NoError(err)
		assert.NotNil(resp.PK)
		assert.Equal(livepk.PKTypeRandom, resp.PK.Detail.Type)
		for _, fighter := range resp.PK.Detail.Fighters {
			assert.NotNil(fighter)
		}
		// 当前房间在 Fighters 第一位
		assert.Equal(resp.roomID, resp.PK.Detail.Fighters[0].RoomID)
		if status == livepk.PKRecordStatusPunishment {
			assert.EqualValues(livepk.PKResultLose, *resp.PK.Detail.Result)
		}
		assert.EqualValues(1, *resp.PK.Show)
		assert.NotEmpty(resp.PK.IconURL)
		require.NoError(clearTestPKData(pkRoomID223344, pkRoomID18113499))
	}
	// not find
	resp.roomID = 1111
	err = resp.findPK()
	require.NoError(err)

	// pool 随机 PK
	resp.roomID = pkRoomID18113499
	require.NoError(clearTestPoolData(pkRoomID18113499))
	require.NoError(createTestPoolData(pkRoomID18113499))
	err = resp.findPK()
	require.NoError(err)
	assert.Equal(livepk.PKRecordStatusMatching, resp.PK.Detail.Status)
	assert.Equal(livepk.PKTypeRandom, resp.PK.Detail.Type)

	// pool 指定 PK
	require.NoError(clearTestPoolData(pkRoomID18113499))
	pool, err := livepk.AddInvitationToPool(pkRoomID18113499, 10, 223344, 1, time.Minute.Milliseconds())
	require.NoError(err)
	require.NotNil(pool)
	err = resp.findPK()
	require.NoError(err)
	require.Nil(resp.PK.Detail)
	require.NotNil(resp.PK.Invitation)
	require.NotNil(resp.PK.Invitation.ToRoom)
	require.Nil(resp.PK.Invitation.FromRoom)
	assert.NotEmpty(resp.PK.Invitation.MatchID)
	assert.EqualValues(livepk.PKInvitationDuration.Milliseconds(), resp.PK.Invitation.Duration)
	assert.EqualValues(time.Minute.Milliseconds(), resp.PK.Invitation.FightingDuration)
	assert.EqualValues(0, resp.PK.Invitation.Invited)
	assert.EqualValues(pool.CreateTime, resp.PK.Invitation.CreateTime)
	assert.EqualValues(pool.ToRoomID, resp.PK.Invitation.ToRoom.RoomID)
	assert.NotEmpty(resp.PK.Invitation.ToRoom.CreatorIconURL)

	resp.userID = 111
	err = resp.findPK()
	require.NoError(err)
	assert.NotNil(resp.PK)
	assert.Nil(resp.PK.Show)
	assert.Empty(resp.PK.IconURL)

	// 当前未在等待 PK 或 PK 中的直播间，pk 字段为空
	resp.roomID = 222
	err = resp.findPK()
	require.NoError(err)
	assert.Nil(resp.PK)
}

func TestMetaResp_findGiftWall(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftIDs := []int64{1000, 2000}
	testRoomID := int64(223344)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"start_time": bson.M{"$lte": now.Unix()}, "end_time": bson.M{"$gt": now.Unix()}})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, giftwall.Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)
	_, err = giftwall.RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = giftwall.RecordCollection().InsertOne(ctx, giftwall.ActivatedRecord{
		PeriodOID: res.InsertedID.(primitive.ObjectID),
		RoomID:    testRoomID,
	})
	require.NoError(err)

	resp := &metaResp{
		roomID: testRoomID,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.IOS,
			AppVersion: "4.9.1",
		},
		metaType: metaTypeGiftWall,
	}
	err = resp.findGiftWall()
	require.NoError(err)
	require.NotNil(resp.GiftWall)
	assert.EqualValues(len(testShowGiftIDs), resp.GiftWall.TotalNum)
	assert.EqualValues(1, resp.GiftWall.ActivatedNum)
}

func createTestRedPacketData(roomID int64) error {
	// 删除商品列表缓存
	key := keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeRedPacket)
	err := service.LRURedis.Del(key).Err()
	if err != nil {
		return err
	}

	goodsIDs := []int64{17, 18}
	nowUnix := goutil.TimeNow().Unix()
	redPackets := []interface{}{
		&redpacket.LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       goodsIDs[0],
			UserID:        110,
			RoomID:        roomID,
			CreatorID:     12,
			Status:        redpacket.StatusWaiting,
			WaitDuration:  60000,
			StartGrabTime: nowUnix + 60,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
		&redpacket.LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       goodsIDs[0],
			UserID:        111,
			RoomID:        roomID,
			CreatorID:     12,
			Status:        redpacket.StatusGrabbing,
			StartGrabTime: nowUnix,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
		&redpacket.LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       goodsIDs[1],
			UserID:        111,
			RoomID:        roomID,
			CreatorID:     12,
			Status:        redpacket.StatusWaiting,
			WaitDuration:  120000,
			StartGrabTime: nowUnix + 120,
			CreateTime:    nowUnix + 10,
			ModifiedTime:  nowUnix + 10,
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = redpacket.LiveRedPacketCollection().InsertMany(ctx, redPackets)
	return err
}

func clearTestRedPacketData(roomID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除礼物红包测试数据
	_, err := redpacket.LiveRedPacketCollection().DeleteMany(ctx, bson.M{"room_id": roomID})
	if err != nil {
		return err
	}
	return nil
}

func createTestRedPacketGiftData(giftIDs []int64) error {
	now := goutil.TimeNow()
	gifts := make([]interface{}, 0, len(giftIDs))
	for index, giftID := range giftIDs {
		gifts = append(gifts, gift.Gift{
			OID:       primitive.NewObjectID(),
			GiftID:    giftID,
			Name:      fmt.Sprintf("礼物红包礼物 %d", index),
			NameClean: fmt.Sprintf("礼物红包礼物 %d", index),
			Type:      gift.TypeRebate,
			Order:     index + 1,
			AddedTime: now,
			Price:     100 * int64(index+1),
			Icon:      fmt.Sprintf("oss://test/icon%d.png", index),
		})
	}
	// 生成礼物红包的礼物测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := gift.Collection().InsertMany(ctx, gifts)
	if err != nil {
		return err
	}
	return nil
}

func clearTestRedPacketGiftData(giftIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除礼物红包的礼物测试数据
	_, err := gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs}})
	if err != nil {
		return err
	}
	return nil
}

func TestMetaResp_findRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := params.RedPacket{
		Key:          params.KeyRedPacket,
		Name:         "红包",
		Icon:         "oss://gifts/icons/001.png",         // 礼物红包图
		Image:        "oss://gifts/icons/001.png",         // APP 直播间左上角玩法栏红包小图标
		CornerIcon:   "oss://gifts/icons/corner.png",      // 角标图
		IconActive:   "oss://gifts/icons/active/001.webp", // 礼物被选中时显示的动画
		Intro:        "这是礼物红包",
		IntroOpenURL: "https://fm.uat.missevan.com",
		Position:     4, // 礼物位置（默认普通礼物列表 0 为第一位），显示在普通礼物的该位置
	}
	cacheKey := keys.KeyParams1.Format(param.Key)
	err := service.LRURedis.Set(cacheKey, tutil.SprintJSON(param), 10*time.Second).Err()
	require.NoError(err)

	resp := &metaResp{
		metaType: metaTypeRedPacket,
		roomID:   testRoomID,
		equip: &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.Android,
			AppVersion: "6.2.2",
		},
	}
	err = resp.findRedPacket()
	require.NoError(err)
	assert.Equal("https://static-test.missevan.com/gifts/icons/001.png", resp.RedPacket.IconURL)
	assert.Equal("https://static-test.missevan.com/gifts/icons/001.png", resp.RedPacket.ImageURL)
	assert.Equal("https://static-test.missevan.com/gifts/icons/corner.png", resp.RedPacket.CornerIconURL)
	assert.Equal("https://static-test.missevan.com/gifts/icons/active/001.webp", resp.RedPacket.IconActiveURL)
	assert.Equal(param.Intro, resp.RedPacket.Intro)
	assert.Equal(param.IntroOpenURL, resp.RedPacket.IntroOpenURL)
	assert.Equal(param.Position, resp.RedPacket.Position)

	testRoomID := int64(223346)
	resp = &metaResp{
		roomID:   testRoomID,
		metaType: 1,
		equip: &goutil.Equipment{
			FromApp: false,
		},
	}
	// 测试不下发礼物红包
	err = resp.findRedPacket()
	require.NoError(err)
	require.Nil(resp.RedPacket)

	gifts := []int64{40100, 40101, 40102, 40103}
	// 删除测试数据
	require.NoError(clearTestRedPacketData(testRoomID))
	require.NoError(clearTestRedPacketGiftData(gifts))

	// 测试房间内没有礼物红包
	resp.metaType = metaTypeRedPacket
	err = resp.findRedPacket()
	require.NoError(err)
	require.NotNil(resp.RedPacket)
	require.Empty(resp.RedPacket.List)

	// 生成测试数据
	require.NoError(createTestRedPacketData(testRoomID))
	require.NoError(createTestRedPacketGiftData(gifts))

	// 测试房间下有礼物红包
	resp.roomID = testRoomID
	err = resp.findRedPacket()
	require.NoError(err)
	require.NotNil(resp.RedPacket)
	assert.Len(resp.RedPacket.List, 3)
}

func TestMetaResp_findRedPacketList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(223348)
	resp := &metaResp{
		roomID:    testRoomID,
		RedPacket: &RedPacket{},
	}
	// 删除测试数据
	gifts := []int64{40100, 40101, 40102, 40103}
	require.NoError(clearTestRedPacketData(testRoomID))
	require.NoError(clearTestRedPacketGiftData(gifts))

	// 测试房间内没有礼物红包
	err := resp.findRedPacketList()
	require.NoError(err)
	assert.Empty(resp.RedPacket.List)

	// 生成测试数据
	require.NoError(createTestRedPacketData(testRoomID))
	require.NoError(createTestRedPacketGiftData(gifts))

	// 测试房间内有礼物红包
	err = resp.findRedPacketList()
	require.NoError(err)
	assert.Len(resp.RedPacket.List, 3)
	assert.Equal(livegoods.RedPacketTypeNormal, resp.RedPacket.List[0].Type)
	assert.EqualValues(25, resp.RedPacket.List[0].GiftTotalNum)
	assert.EqualValues(4000, resp.RedPacket.List[0].GiftTotalPrice)
	assert.EqualValues(1000, resp.RedPacket.List[0].Price)
	assert.EqualValues(0, resp.RedPacket.List[0].RemainDuration)
	assert.EqualValues(111, resp.RedPacket.List[0].Sender.ID)
	assert.Len(resp.RedPacket.List[0].Gifts, 2)
	assert.EqualValues(40100, resp.RedPacket.List[0].Gifts[0].GiftID)
	assert.EqualValues(40101, resp.RedPacket.List[0].Gifts[1].GiftID)

	assert.Equal(livegoods.RedPacketTypeNormal, resp.RedPacket.List[1].Type)
	assert.EqualValues(25, resp.RedPacket.List[1].GiftTotalNum)
	assert.EqualValues(4000, resp.RedPacket.List[1].GiftTotalPrice)
	assert.EqualValues(1000, resp.RedPacket.List[1].Price)
	assert.LessOrEqual(resp.RedPacket.List[1].RemainDuration, int64(60000))
	assert.EqualValues(110, resp.RedPacket.List[1].Sender.ID)
	assert.Len(resp.RedPacket.List[1].Gifts, 2)
	assert.EqualValues(40100, resp.RedPacket.List[1].Gifts[0].GiftID)
	assert.EqualValues(40101, resp.RedPacket.List[1].Gifts[1].GiftID)

	assert.Equal(livegoods.RedPacketTypeKeyword, resp.RedPacket.List[2].Type)
	assert.EqualValues(35, resp.RedPacket.List[2].GiftTotalNum)
	assert.EqualValues(12500, resp.RedPacket.List[2].GiftTotalPrice)
	assert.EqualValues(1500, resp.RedPacket.List[2].Price)
	assert.LessOrEqual(resp.RedPacket.List[2].RemainDuration, int64(120000))
	assert.EqualValues(111, resp.RedPacket.List[2].Sender.ID)
	assert.Len(resp.RedPacket.List[2].Gifts, 2)
	assert.EqualValues(40102, resp.RedPacket.List[2].Gifts[0].GiftID)
	assert.EqualValues(40103, resp.RedPacket.List[2].Gifts[1].GiftID)

	// 删除礼物红包对应的礼物数据
	require.NoError(clearTestRedPacketGiftData(gifts))

	// 测试房间内有礼物红包，礼物红包对应的礼物信息不存在时
	err = resp.findRedPacketList()
	require.NoError(err)
	assert.Len(resp.RedPacket.List, 3)
	assert.Empty(resp.RedPacket.List[0].Gifts)
	assert.Equal(livegoods.RedPacketTypeNormal, resp.RedPacket.List[0].Type)
	assert.EqualValues(0, resp.RedPacket.List[0].GiftTotalNum)
	assert.EqualValues(0, resp.RedPacket.List[0].GiftTotalPrice)
	assert.EqualValues(1000, resp.RedPacket.List[0].Price)
	assert.EqualValues(0, resp.RedPacket.List[0].RemainDuration)
	assert.EqualValues(111, resp.RedPacket.List[0].Sender.ID)

	assert.Empty(resp.RedPacket.List[1].Gifts)
	assert.Equal(livegoods.RedPacketTypeNormal, resp.RedPacket.List[1].Type)
	assert.EqualValues(0, resp.RedPacket.List[1].GiftTotalNum)
	assert.EqualValues(0, resp.RedPacket.List[1].GiftTotalPrice)
	assert.EqualValues(1000, resp.RedPacket.List[1].Price)
	assert.LessOrEqual(resp.RedPacket.List[1].RemainDuration, int64(60000))
	assert.EqualValues(110, resp.RedPacket.List[1].Sender.ID)

	assert.Empty(resp.RedPacket.List[2].Gifts)
	assert.Equal(livegoods.RedPacketTypeKeyword, resp.RedPacket.List[2].Type)
	assert.EqualValues(0, resp.RedPacket.List[2].GiftTotalNum)
	assert.EqualValues(0, resp.RedPacket.List[2].GiftTotalPrice)
	assert.EqualValues(1500, resp.RedPacket.List[2].Price)
	assert.LessOrEqual(resp.RedPacket.List[2].RemainDuration, int64(120000))
	assert.EqualValues(111, resp.RedPacket.List[2].Sender.ID)
}

func TestMetaResp_findShops(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	redeemParam := params.RedeemShop{
		Key:     params.KeyRedeemShop,
		Name:    "万事屋常驻兑换商城",
		Icon:    "oss://shop/redeem/icon.png",
		ShopURL: "https://www.missevan.com/some_url_to_shop",
	}
	key := keys.KeyParams1.Format(redeemParam.Key)
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(redeemParam), 10*time.Second).Err())
	defer func() {
		require.NoError(service.LRURedis.Del(key).Err())
	}()

	privilegeParam := params.RedeemShop{
		Key:     params.KeyPrivilegeShop,
		Name:    "星享馆等级权益商城",
		Icon:    "oss://shop/privilege/icon.png",
		ShopURL: "https://www.missevan.com/some_url_to_shop",
	}
	key = keys.KeyParams1.Format(privilegeParam.Key)
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(privilegeParam), 10*time.Second).Err())
	defer func() {
		require.NoError(service.LRURedis.Del(key).Err())
	}()

	resp := &metaResp{
		metaType: metaTypeRedeemShop,
		equip: &goutil.Equipment{
			OS:         goutil.IOS,
			AppVersion: "6.1.3",
		},
		userID: 1919,
		roomID: 114514,
		room:   &room.Room{},
	}
	resp.room.RoomID = resp.roomID
	resp.room.CreatorID = resp.userID
	require.NoError(resp.findShops())
	assert.Nil(resp.PrivilegeShop)

	resp.room.CreatorID = 0
	require.NoError(resp.findShops())
	rs := resp.RedeemShop
	require.NotNil(rs)
	assert.Equal(redeemParam.Name, rs.Name)
	assert.Equal("https://www.missevan.com/some_url_to_shop?from_room_id=114514", rs.ShopURL)
	assert.Equal("https://static-test.missevan.com/shop/redeem/icon.png", rs.IconURL)
	ps := resp.PrivilegeShop
	require.NotNil(ps)
	assert.Equal(privilegeParam.Name, ps.Name)
	assert.Equal("https://static-test.missevan.com/shop/privilege/icon.png", ps.IconURL)
}

func TestMetaResp_findPia(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(223344)
		testUserID = int64(222222)
	)

	err := livetagcontrollist.DB().
		Where("room_id = ? AND tag_id = ?", testRoomID, tag.TagListenDrama).
		Delete(&livetagcontrollist.LiveTagControlList{}).Error
	require.NoError(err)

	resp := &metaResp{
		equip: &goutil.Equipment{
			OS: goutil.Web,
		},
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    testRoomID,
				CreatorID: testUserID,
				TagIDs:    []int64{tag.TagListenDrama},
			},
		},
		userID: testUserID,
	}
	resp.findPia()
	assert.Nil(resp.Pia)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(9999999999, 0)
	})
	defer goutil.SetTimeNow(nil)
	param := params.Pia{
		Key:      params.KeyPia,
		Name:     "pia",
		ShowTime: goutil.TimeNow().Unix() - 1,
	}
	byteParams, err := json.Marshal(param)
	require.NoError(err)
	require.NotNil(byteParams)
	err = service.LRURedis.Set(keys.KeyParams1.Format(params.KeyPia), byteParams, time.Minute).Err()
	require.NoError(err)
	err = livetagcontrollist.DB().Create(&livetagcontrollist.LiveTagControlList{
		TagID:  tag.TagListenDrama,
		RoomID: testRoomID,
		Status: livetagcontrollist.StatusAllowRoomAddTag,
	}).Error
	require.NoError(err)
	resp.findPia()
	assert.NotNil(resp.Pia)
}

func TestMetaResp_findMultiCombo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGiftID = int64(1)
		testRoomID = int64(223344)
		now        = goutil.TimeNow()
	)
	p := params.MultiCombo{
		Key:                    params.KeyMultiCombo,
		ShowShortcutsStartTime: now.Add(-5 * time.Second).Unix(),
		ShowShortcutsEndTime:   now.Add(5 * time.Second).Unix(),
	}
	err := service.LRURedis.Set(keys.KeyParams1.Format(params.KeyMultiCombo), tutil.SprintJSON(p), 5*time.Second).Err()
	require.NoError(err)

	resp := &metaResp{
		userID: 1,
		roomID: testRoomID,
		room: &room.Room{
			Helper: room.Helper{RoomID: testRoomID},
		},
	}
	resp.findMultiCombo()
	require.NotNil(resp.MultiCombo)
	assert.Equal(1, resp.MultiCombo.Status)
	assert.Empty(resp.MultiCombo.Combos)

	key := keys.KeyOnlineGifts0.Format()
	gifts := []gift.Gift{
		{
			GiftID:    testGiftID,
			Price:     1,
			Comboable: gift.ComboableTypeMulti,
			Combos: []gift.Combo{
				{
					TargetPrice:    10,
					RepeatAddPrice: 8,
					ComboEffect:    "http://test1.png",
					WebComboEffect: "http://testweb1.png",
				},
			},
		},
		{GiftID: 3, Comboable: gift.ComboableTypeMulti},
	}
	service.Cache5s.Set(key, gifts, 0)
	comboKey := keys.KeyRoomGiftMultiCombo2.Format(testRoomID, testGiftID)
	require.NoError(service.Redis.HMSet(comboKey, map[string]interface{}{
		"combo_id": 123456, "gift_num": 2, "last_time": now.UnixMilli(),
	}).Err())
	resp.findMultiCombo()
	require.NotNil(resp.MultiCombo)
	assert.Equal(1, resp.MultiCombo.Status)
	require.Equal(1, len(resp.MultiCombo.Combos))
	assert.EqualValues(testGiftID, resp.MultiCombo.Combos[0].Gift.GiftID)
	assert.Equal("123456", resp.MultiCombo.Combos[0].ID)
	assert.Zero(*resp.MultiCombo.Combos[0].AchievedNum)
	assert.EqualValues(10, resp.MultiCombo.Combos[0].TargetNum)
}

func TestMetaResp_findMultiComboParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	p := params.MultiCombo{
		Key:                    params.KeyMultiCombo,
		ShowShortcutsStartTime: now.Add(-5 * time.Second).Unix(),
		ShowShortcutsEndTime:   now.Add(5 * time.Second).Unix(),
	}
	err := service.LRURedis.Set(keys.KeyParams1.Format(params.KeyMultiCombo), tutil.SprintJSON(p), 5*time.Second).Err()
	require.NoError(err)
	var resp metaResp
	require.NoError(resp.findMultiComboParams())
	assert.NotNil(resp.multiComboParams)

	err = service.LRURedis.Del(keys.KeyParams1.Format(params.KeyMultiCombo)).Err()
	require.NoError(err)
	require.NoError(resp.findMultiComboParams())
	assert.NotNil(resp.multiComboParams)
}

func TestMetaResp_findShortcutGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		roomID: 1234,
	}
	require.NoError(livecustom.LiveCustom{}.DB().
		Delete("", "custom_type = ? AND element_id = ?",
			livecustom.TypeShortcutGift, resp.roomID).Error)
	resp.findShortcutGift()
	assert.Nil(resp.ShortcutGift)

	now := goutil.TimeNow()
	err := livecustom.AddShortcutGifts([]int64{resp.roomID}, 1, now.Unix(), now.Add(time.Minute).Unix())
	require.NoError(err)
	resp.findShortcutGift()
	require.NotNil(resp.ShortcutGift)
}

func TestMetaResp_findMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomOID    = primitive.NewObjectID()
		now        = goutil.TimeNow()
		expireTime = now.Add(time.Minute)
	)
	members := []interface{}{
		livemembers.Member{
			Helper: livemembers.Helper{
				RoomOID:     roomOID,
				UserID:      666666,
				Status:      livemembers.StatusAdmin,
				ExpireAt:    &expireTime,
				CreatedTime: now,
			},
		},
		livemembers.Member{
			Helper: livemembers.Helper{
				RoomOID:     roomOID,
				UserID:      55555,
				Status:      livemembers.StatusMute,
				ExpireAt:    &expireTime,
				CreatedTime: now,
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemembers.Collection().InsertMany(ctx, members)
	require.NoError(err)

	resp := metaResp{
		userID:   1,
		room:     &room.Room{OID: roomOID},
		metaType: metaTypeMembers,
	}
	resp.findMembers()
	require.NotNil(resp.Members)
	assert.NotEmpty(resp.Members.Admin)
	assert.NotEmpty(resp.Members.Mute)

	resp = metaResp{
		room:     &room.Room{OID: roomOID},
		metaType: metaTypeAll,
	}
	resp.findMembers()
	require.NotNil(resp.Members)
	assert.NotEmpty(resp.Members.Admin)
	assert.Empty(resp.Members.Mute)

	_, err = livemembers.Collection().DeleteMany(ctx, bson.M{"_room_id": roomOID})
	resp = metaResp{
		userID:   1,
		room:     &room.Room{OID: roomOID},
		metaType: metaTypeAll,
	}
	require.NoError(err)
	resp.findMembers()
	require.NotNil(resp.Members)
	assert.Empty(resp.Members.Admin)
	assert.Empty(resp.Members.Mute)
}

func TestMetaResp_findConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		room: &room.Room{},
	}
	resp.findConfig()
	assert.Nil(resp.Config)

	resp.room.Config = &room.Config{
		AllowHideGiftEffect: true,
	}
	resp.findConfig()
	require.NotNil(resp.Config)
	assert.True(resp.Config.AllowHideGiftEffect)
}

func TestMetaResp_findLuckyBagEntry(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(application.Element{},
		"application_id = ? AND element_id = ?", 1, 9074507).Error
	require.NoError(err)

	resp := metaResp{
		metaType: metaTypeConfig,
		equip:    &goutil.Equipment{},
	}
	resp.findLuckyBagEntry()
	assert.Nil(resp.LuckyBagEntry)

	resp = metaResp{
		metaType: metaTypeLuckyBag,
		userID:   9074508,
		roomID:   9074507,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 9074509,
			},
		},
		equip: &goutil.Equipment{},
	}
	// 测试不是当前直播间主播
	resp.findLuckyBagEntry()
	assert.Nil(resp.LuckyBagEntry)

	// 测试未到开放时间不返回入口
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2019, 3, 8, 15, 47, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	resp.userID = 9074509
	resp.findLuckyBagEntry()
	assert.Nil(resp.LuckyBagEntry)

	// 测试不在黑名单的主播返回入口配置
	goutil.SetTimeNow(nil)
	resp.userID = 9074509
	resp.findLuckyBagEntry()
	require.NotNil(resp.LuckyBagEntry)
	assert.Equal("喵喵福袋", resp.LuckyBagEntry.Name)

	// 测试在黑名单的主播返回空
	element := application.Element{
		ApplicationID: 1,
		ElementID:     9074507,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)
	resp.LuckyBagEntry = nil
	resp.findLuckyBagEntry()
	assert.Nil(resp.LuckyBagEntry)
}

func TestMetaResp_findLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(luckybag.InitiateRecord{},
		"room_id IN (?)", []int64{9074507, 9074508}).Error
	require.NoError(err)

	resp := metaResp{
		metaType: metaTypeConfig,
	}
	resp.findLuckyBag()
	assert.Nil(resp.LuckyBag)

	resp = metaResp{
		metaType: metaTypeLuckyBag,
		userID:   9074508,
		roomID:   9074507,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 9074509,
			},
		},
		equip: &goutil.Equipment{FromApp: true},
	}
	// 测试当前直播间没有福袋
	resp.findLuckyBag()
	assert.Nil(resp.LuckyBag)

	now := goutil.TimeNow()
	r := luckybag.InitiateRecord{
		RoomID:           resp.roomID,
		StartTime:        now.Unix(),
		PrizeIPRID:       1,
		Type:             luckybag.TypeDrama,
		Status:           luckybag.StatusPending,
		ScheduledEndTime: now.Add(time.Minute).Unix(),
	}
	require.NoError(r.Create())
	resp.LuckyBag = nil
	resp.findLuckyBag()
	require.NotNil(resp.LuckyBag)
	assert.EqualValues(r.ID, resp.LuckyBag.LuckyBagID)
	assert.Equal("https://static-test.missevan.com/live/luckybag/entrance.png", resp.LuckyBag.ImageURL)
	assert.Equal("https://static-test.missevan.com/live/luckybag/icon-v2.png", resp.LuckyBag.NewImageURL)
	assert.NotNil(resp.LuckyBag.JoinStatus)
	assert.False(resp.LuckyBag.HasMore)

	// 测试 web 用户侧不下发福袋信息
	resp.LuckyBag = nil
	resp.equip.FromApp = false
	resp.findLuckyBag()
	assert.Nil(resp.LuckyBag)

	// 测试显示更多福袋入口
	r = luckybag.InitiateRecord{
		RoomID:           9074508,
		StartTime:        now.Unix(),
		PrizeIPRID:       1,
		Type:             luckybag.TypeDrama,
		Status:           luckybag.StatusPending,
		ScheduledEndTime: now.Add(time.Minute).Unix(),
	}
	require.NoError(r.Create())
	resp.equip.FromApp = true
	resp.findLuckyBag()
	assert.True(resp.LuckyBag.HasMore)

	// 测试福袋已经结束，不显示福袋图标
	resp.LuckyBag = nil
	err = service.LiveDB.Model(luckybag.InitiateRecord{}).Where("room_id = ?", resp.roomID).
		Updates(map[string]interface{}{"end_time": now.Add(-time.Minute).Unix(), "status": luckybag.StatusFinish}).Error
	require.NoError(err)
	resp.findLuckyBag()
	assert.Nil(resp.LuckyBag)
}

func TestMetaResp_findWishListEntry(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		metaType: metaTypeConfig,
	}
	resp.findWishListEntry()
	assert.Nil(resp.WishListEntry)

	resp = metaResp{
		metaType: metaTypeWishList,
		userID:   9074508,
		roomID:   9074507,
		room:     &room.Room{},
		equip:    &goutil.Equipment{FromApp: true},
	}
	resp.findWishListEntry()
	assert.Nil(resp.WishListEntry)

	resp.room.CreatorID = resp.userID
	resp.findWishListEntry()
	assert.Nil(resp.WishListEntry)

	resp.equip.FromApp = false
	resp.findWishListEntry()
	require.NotNil(resp.WishListEntry)
	assert.Equal("心愿单", resp.WishListEntry.Name)
}

func TestMetaResp_findLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := metaResp{
		metaType: metaTypeConfig,
	}
	resp.findLuckyBox()
	assert.Nil(resp.LuckyBox)

	box := params.LuckyBox{
		Name:     "宝盒",
		OpenTime: goutil.TimeNow().Unix() + 1000,
	}
	cacheKey := keys.KeyParams1.Format(params.KeyLuckyBox)
	require.NoError(service.LRURedis.Set(cacheKey, tutil.SprintJSON(box), 10*time.Second).Err())
	resp.metaType = metaTypeLuckyBox
	resp.findLuckyBox()
	assert.Nil(resp.LuckyBox)

	box.OpenTime = 1
	require.NoError(service.LRURedis.Set(cacheKey, tutil.SprintJSON(box), 10*time.Second).Err())
	resp.findLuckyBox()
	require.NotNil(resp.LuckyBox)
	assert.EqualValues("宝盒", resp.LuckyBox.Name)
}

func TestMetaResp_findMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 其他类型的请求
	resp := metaResp{
		metaType: metaTypeConfig,
	}
	require.NoError(resp.findMultiConnect())
	assert.Nil(resp.MultiConnect)

	r, err := room.Find(roomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)

	// 无进行中主播连线
	resp = metaResp{
		metaType: metaTypeMultiConnect,
		roomID:   roomID,
		userID:   r.CreatorID,
		room:     r,
	}
	require.NoError(resp.findMultiConnect())
	require.NotNil(resp.MultiConnect)
	require.NotNil(resp.MultiConnect.Show)
	assert.Equal(1, *resp.MultiConnect.Show)
	assert.NotNil(resp.MultiConnect.IconURL)
	assert.Nil(resp.MultiConnect.Detail)

	group := &livemulticonnect.Group{
		ID: 100,
	}
	err = livemulticonnect.DB().Delete(group).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(group).Error
	require.NoError(err)
	members := []livemulticonnect.GroupMember{
		{ID: 100, GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: roomID},
		{ID: 101, GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: 18113499},
		{ID: 102, GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: 223344},
	}
	muteRecords := []livemulticonnect.GroupMute{
		{GroupID: group.ID, GroupMemberID: 100, MuteGroupMemberID: 102},
	}
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members))
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMute{}.TableName(), muteRecords))

	// 进行中主播连线，返回 detail 内容
	resp = metaResp{
		metaType: metaTypeMultiConnect,
		roomID:   roomID,
		room:     r,
	}
	require.NoError(resp.findMultiConnect())
	require.NotNil(resp.MultiConnect)
	// 非当前直播间主播不显示主播连线入口
	assert.Nil(resp.MultiConnect.Show)
	assert.Empty(resp.MultiConnect.IconURL)
	assert.NotNil(resp.MultiConnect.Detail)
	assert.Equal([]int64{223344}, resp.MultiConnect.Detail.MuteRoomIDs)
	assert.Equal(roomID, resp.MultiConnect.Detail.Members[0].Room.RoomID)
}

func TestMetaResp_buildSponsorGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(5678)
	giftID1 := int64(1001)
	giftID2 := int64(1002)
	userID := int64(9999)
	revenue1 := int64(15000) // 达到第一档
	revenue2 := int64(25000) // 达到第二档

	// 清理测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.SponsorCollection().DeleteMany(ctx, bson.M{"room_id": roomID})
	require.NoError(err)

	// 准备测试参数配置
	giftWallParams := &params.GiftWall{
		SponsorLevels: []*params.GiftWallSponsorLevel{
			{
				TargetRevenue: 10000,
				GiftsIcon:     "oss://gifts_icon_level1.png",
				Text:          "Level 1 Sponsor",
				TextColor:     "#FF0000",
			},
			{
				TargetRevenue: 20000,
				GiftsIcon:     "oss://gifts_icon_level2.png",
				Text:          "Level 2 Sponsor",
				TextColor:     "#00FF00",
			},
		},
	}
	giftWallParams.AfterLoad()
	paramsBytes, err := json.Marshal(giftWallParams)
	require.NoError(err)
	paramsKey := keys.KeyParams1.Format(params.KeyGiftWall)
	require.NoError(service.LRURedis.Set(paramsKey, paramsBytes, time.Minute).Err())

	// 空礼物列表
	resp := metaResp{
		roomID: roomID,
		userID: userID,
	}
	err = resp.buildSponsorGifts([]*metaGiftsElem{})
	require.NoError(err)

	// 无冠名信息
	elem := []*metaGiftsElem{{
		Data: []*metaGift{{
			Gift: gift.Gift{GiftID: giftID1},
		}},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	assert.Nil(elem[0].Data[0].Sponsor)

	// 准备冠名数据
	now := goutil.TimeNow()
	sponsors := []*giftwall.Sponsor{
		{
			RoomID:    roomID,
			GiftID:    giftID1,
			UserID:    userID,
			StartTime: now.Unix(),
			EndTime:   now.Add(time.Hour).Unix(),
			Revenue:   revenue1,
		},
		{
			RoomID:    roomID,
			GiftID:    giftID2,
			UserID:    userID,
			StartTime: now.Unix(),
			EndTime:   now.Add(time.Hour).Unix(),
			Revenue:   revenue2,
		},
	}
	sponsorBytes, err := json.Marshal(sponsors)
	require.NoError(err)
	sponsorKey := giftwall.KeyRoomSponsors(roomID)
	require.NoError(service.LRURedis.Set(sponsorKey, sponsorBytes, time.Minute).Err())

	// 有冠名信息，但收益未达标
	elem = []*metaGiftsElem{{
		Data: []*metaGift{{
			Gift: gift.Gift{GiftID: 9999}, // 不存在的礼物ID
		}},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	assert.Nil(elem[0].Data[0].Sponsor)

	// 有冠名信息，收益达到第一档
	elem = []*metaGiftsElem{{
		Data: []*metaGift{{
			Gift: gift.Gift{GiftID: giftID1},
		}},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	require.NotNil(elem[0].Data[0].Sponsor)
	assert.Equal(giftWallParams.SponsorLevels[0].GiftsIconURL, elem[0].Data[0].Sponsor.IconURL)
	assert.Equal(giftWallParams.SponsorLevels[0].Text, elem[0].Data[0].Sponsor.Text)
	assert.Equal(giftWallParams.SponsorLevels[0].TextColor, elem[0].Data[0].Sponsor.TextColor)

	// 有冠名信息，收益达到第二档
	elem = []*metaGiftsElem{{
		Data: []*metaGift{{
			Gift: gift.Gift{GiftID: giftID2},
		}},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	require.NotNil(elem[0].Data[0].Sponsor)
	assert.Equal(giftWallParams.SponsorLevels[1].GiftsIconURL, elem[0].Data[0].Sponsor.IconURL)
	assert.Equal(giftWallParams.SponsorLevels[1].Text, elem[0].Data[0].Sponsor.Text)
	assert.Equal(giftWallParams.SponsorLevels[1].TextColor, elem[0].Data[0].Sponsor.TextColor)

	// 多个礼物，部分有冠名
	elem = []*metaGiftsElem{{
		Data: []*metaGift{
			{Gift: gift.Gift{GiftID: giftID1}},
			{Gift: gift.Gift{GiftID: 8888}}, // 无冠名
			{Gift: gift.Gift{GiftID: giftID2}},
		},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	require.NotNil(elem[0].Data[0].Sponsor)
	assert.Equal(giftWallParams.SponsorLevels[0].Text, elem[0].Data[0].Sponsor.Text)
	assert.Nil(elem[0].Data[1].Sponsor)
	require.NotNil(elem[0].Data[2].Sponsor)
	assert.Equal(giftWallParams.SponsorLevels[1].Text, elem[0].Data[2].Sponsor.Text)

	// 收益过低，未达到任何档位
	lowRevenueSponsor := []*giftwall.Sponsor{
		{
			RoomID:    roomID,
			GiftID:    5555,
			UserID:    userID,
			StartTime: now.Unix(),
			EndTime:   now.Add(time.Hour).Unix(),
			Revenue:   5000, // 低于最低档位
		},
	}
	lowRevenueBytes, err := json.Marshal(lowRevenueSponsor)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(sponsorKey, lowRevenueBytes, time.Minute).Err())

	elem = []*metaGiftsElem{{
		Data: []*metaGift{{
			Gift: gift.Gift{GiftID: 5555},
		}},
	}}
	err = resp.buildSponsorGifts(elem)
	require.NoError(err)
	assert.Nil(elem[0].Data[0].Sponsor)
}
