package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSuperFanTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(superFanRankResp{}, "data", "pagination", "my_medal")
	kc.Check(superFanIntroResp{}, "creator_id", "creator_username", "creator_iconurl", "super_fan", "super_fan_count",
		"super_privilege", "goods_list", "rule", "closed_message")

	kc.Check(superFanBuyResp{}, "ok", "user", "medal", "super_fan", "bubble", "balance")
	kc.Check(superFan{}, "num", "icon_url", "contribution", "notify_duration")
	kc.Check(notifyAllPayload{}, "type", "notify_type", "event", "room_id", "user", "super_fan", "notify_bubble", "message")
	kc.Check(notifyRoomPayload{}, "type", "event", "room_id", "user", "bubble", "time", "super_fan", "medal", "current_revenue")
}

func TestNewSuperFanRankParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/superfan/rank?room_id=-123", false, nil)
	_, err := newSuperFanRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext(http.MethodGet, "/superfan/rank?room_id=22489473", true, nil)
	param, err := newSuperFanRankParam(c)
	require.NoError(err)
	assert.NotNil(param.user)
	assert.NotNil(param.Data)
}

func TestSuperFanRankFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testUserID := int64(202104201529)
	s := &livemedal.Simple{
		RoomID:    22489473,
		CreatorID: 10,
		UserID:    testUserID,
		Status:    livemedal.StatusOwned,
		Point:     200,
		Mini:      livemedal.Mini{SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(5 * time.Minute).Unix()}},
	}
	_, err := livemedal.Collection().UpdateOne(ctx, bson.M{"creator_id": 10, "user_id": testUserID},
		bson.M{"$set": s}, options.Update().SetUpsert(true))
	require.NoError(err)

	param := superFanRankParam{roomID: 22489473, p: 1, pageSize: 100}
	assert.NoError(param.findRank())
	require.NotEmpty(param.Data)
	for i := range param.Data {
		assert.Equal(int64(i+1), param.Data[i].Rank)
	}
}

func TestSuperFanRankFindMyMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testUserID := int64(202104201535)
	testRoomID := int64(22489473)
	s := &livemedal.Simple{
		RoomID:    testRoomID,
		CreatorID: 10,
		UserID:    testUserID,
		Status:    livemedal.StatusOwned,
		Point:     100,
		Mini:      livemedal.Mini{SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(5 * time.Minute).Unix()}},
	}
	_, err := livemedal.Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID, "user_id": testUserID},
		bson.M{"$set": s}, options.Update().SetUpsert(true))
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/superfan/rank?room_id=22489473", true, nil)
	param := superFanRankParam{c: c, p: 1}
	param.user = new(user.User)
	param.user.ID = testUserID
	param.roomID = testRoomID
	param.findMyMedal()
	require.NotNil(param.MyMedal)
	assert.NotEmpty(param.MyMedal.Rank)
	assert.NotEmpty(param.MyMedal.RankUp)

	param.user.ID = 99999
	param.MyMedal = nil
	param.findMyMedal()
	require.NotNil(param.MyMedal)
	assert.Equal(livemedal.StatusPending, param.MyMedal.Status)
}

func TestSuperFanCheckRankInvisible(t *testing.T) {
	assert := assert.New(t)

	param := superFanRankParam{user: new(user.User)}
	param.user.ID = 10
	assert.NotPanics(func() { param.checkRankInvisible() })
	param.Data = []*fansRankElem{{
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        10,
		},
	}, {
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        11,
		},
	},
	}
	param.checkRankInvisible()
	d := param.Data
	assert.Equal([]string{"", "神秘人"}, []string{d[0].Username, d[1].Username})
}

func TestActionSuperFanRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/superfan/rank?room_id=22489473", true, nil)
	r, err := ActionSuperFanRank(c)
	require.NoError(err)
	resp := r.(superFanRankResp)
	assert.NotNil(resp.MyMedal)
	assert.NotEmpty(resp.Data)
	assert.NotNil(resp.Pagination)
	tutil.PrintJSON(r)
}

func TestActionSuperFanIntro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	testRoomID := int64(22489473)
	testCreatorID := int64(9074510)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{
		"user_id": testUserID,
		"room_id": testRoomID,
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/superfan/intro", true, nil)
	_, err = ActionSuperFanIntro(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/superfan/intro?room_id=22489473", true, nil)
	c.User().ID = testUserID
	r, err := ActionSuperFanIntro(c)
	require.NoError(err)
	resp := r.(*superFanIntroResp)
	require.NotNil(resp)
	assert.NotEmpty(resp.GoodsList)
	assert.Nil(resp.SuperFan)

	data := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    testRoomID,
			UserID:    testUserID,
			CreatorID: testCreatorID,
			Mini: livemedal.Mini{
				SuperFan: &livemedal.SuperFan{
					ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
				},
			},
		},
	}
	_, err = livemedal.Collection().InsertOne(ctx, data)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/superfan/intro?room_id=22489473", true, nil)
	c.User().ID = 9074509
	r, err = ActionSuperFanIntro(c)
	require.NoError(err)
	resp = r.(*superFanIntroResp)
	require.NotNil(resp.SuperFan)
	assert.NotZero(resp.SuperFan.Days)
}

func TestRoomListSuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID    int64 = 1
		testCreatorID int64 = 2
		testUserID    int64 = 3
		testMore            = `{"super_fan": {"allow_room_ids": [1]}}`
	)
	require.NoError(service.LiveDB.Table(livegoods.TableName()).Delete("", "more = ?", testMore).Error)
	require.NoError(service.LiveDB.Create(&livegoods.LiveGoods{
		Type:  livegoods.GoodsTypeSuperFan,
		Num:   1,
		Price: 10,
		Sort:  1,
		More:  testMore,
	}).Error)

	superFanList1, err := roomListSuperFan(-1111, testCreatorID, testUserID)
	require.NoError(err)
	assert.NotEmpty(superFanList1)

	superFanList2, err := roomListSuperFan(testRoomID, testCreatorID, testUserID)
	require.NoError(err)
	assert.NotEmpty(superFanList2)
	assert.Greater(len(superFanList2), len(superFanList1))
}

func TestActionSuperFanBuy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()

	g := livegoods.LiveGoods{
		ID:    1,
		Num:   25,
		Price: 800,
		Sort:  1,
		Type:  livegoods.GoodsTypeSuperFan,
	}
	require.NoError(service.LiveDB.Save(&g).Error)
	param := superFanBuyParam{
		RoomID:  22489473,
		GoodsID: g.ID,
		Confirm: 1,
	}
	mockAppRPCServer()

	c := handler.NewTestContext(http.MethodPost, "/superfan/buy", true, param)
	r, err := ActionSuperFanBuy(c)
	require.NoError(err)

	resp := r.(*superFanBuyResp)
	require.NotNil(resp.Medal)
	assert.Equal(1, resp.OK)
	assert.NotNil(resp.Medal.SuperFan)

	param.Confirm = 0
	c = handler.NewTestContext(http.MethodPost, "/superfan/buy", true, param)
	_, err = ActionSuperFanBuy(c)
	assert.EqualError(err, `您的超粉有效期已到达上限（24 个月），继续续费<font color="#AC3D3D">无法延长有效期</font>，确定要继续支付吗？`)
}

func TestSuperFanBuyParamCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(10)
		testToBlockUserID   = int64(13)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))

	p := superFanBuyParam{RoomID: 1, Confirm: 1}
	assert.Equal(actionerrors.ErrParams, p.check())

	p = superFanBuyParam{RoomID: room.TestNonexistentRoomID, GoodsID: 1}
	assert.Equal(actionerrors.ErrCannotFindRoom, p.check())
	p.RoomID = room.TestLimitedRoomID
	assert.Equal(actionerrors.NewErrForbidden("本直播间暂不支持本功能"),
		p.check(), "受限房间")

	p = superFanBuyParam{RoomID: 22489473, GoodsID: 1}
	p.c = handler.NewTestContext(http.MethodPost, "/superfan/buy", true, nil)
	p.c.User().ID = 10
	assert.EqualError(p.check(), "不能购买自己直播间超级粉丝")

	p.Confirm = 1
	p.c.User().ID = 12
	assert.NoError(p.check())

	p.c.User().ID = 5
	assert.NoError(p.check())

	p.c.User().ID = 12
	p.GoodsID = 99999
	assert.Equal(actionerrors.ErrGoodsNotFound, p.check())

	// 测试拉黑
	p.c.User().ID = testToBlockUserID
	require.EqualError(p.check(), "您当前无法在本直播间内进行此操作")
}

func TestCheckConfirm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(2021050601)
	testCreatorID := int64(2021050602)

	livetxnorder.LiveTxnOrder{}.DB().Delete("", "buyer_id = ?", testUserID)
	now := util.BeginningOfDay(goutil.TimeNow().Add(31 * 24 * time.Hour)).Unix()
	order := &livetxnorder.LiveTxnOrder{
		ExpireTime: now,
		Status:     livetxnorder.StatusSuccess,
		GoodsID:    1,
		GoodsType:  livegoods.GoodsTypeSuperFan,
		Attr:       1,
		BuyerID:    testUserID,
		SellerID:   testCreatorID,
	}
	require.NoError(livetxnorder.LiveTxnOrder{}.DB().Create(order).Error)

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	c.User().ID = testUserID
	p := superFanBuyParam{
		r: &room.Room{Helper: room.Helper{CreatorUsername: "test"}},
		c: c,
		goods: &livegoods.LiveGoods{
			Num:      1,
			SellerID: testCreatorID,
			Type:     livegoods.GoodsTypeSuperFan,
			Price:    10,
		},
	}
	assert.EqualError(p.checkConfirm(), `确定要消耗 <font color="#AC3D3D">10</font> 钻石续费主播：<font color="#AC3D3D">test</font> 1 个月的超级粉丝吗？`)

	p.goods.Num = 30
	assert.EqualError(p.checkConfirm(), `由于超粉有效期最长为 24 个月，您本次续费 30 个月仅能延长 <font color="#AC3D3D">690</font> 天有效期，确定要继续支付吗？`)

	require.NoError(livetxnorder.LiveTxnOrder{}.DB().Where("buyer_id = ?", testUserID).Update("expire_time",
		util.BeginningOfDay(goutil.TimeNow().Add(721*24*time.Hour)).Unix()).Error)
	assert.EqualError(p.checkConfirm(), `您的超粉有效期已到达上限（24 个月），继续续费<font color="#AC3D3D">无法延长有效期</font>，确定要继续支付吗？`)
}

func TestCheckUserMedalTitle(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lm := &livemedal.LiveMedal{Simple: livemedal.Simple{Status: 2, Mini: livemedal.Mini{
		Name:  "test",
		Level: 12,
	}}}
	param := superFanBuyParam{
		u: &liveuser.Simple{
			Titles: []liveuser.Title{
				{Type: liveuser.TitleTypeLevel, Level: 10},
				{Type: liveuser.TitleTypeMedal, Name: "test", Level: 1},
				{Type: liveuser.TitleTypeUsername, Color: "#ffffff"},
			}},
		lm: lm,
	}
	param.checkUserMedalTitle()
	require.Len(param.u.Titles, 3)
	assert.Equal(liveuser.TitleTypeMedal, param.u.Titles[1].Type)
	assert.Equal(12, param.u.Titles[1].Level)

	param = superFanBuyParam{
		u: &liveuser.Simple{
			Titles: []liveuser.Title{
				{Type: liveuser.TitleTypeLevel, Level: 10},
				{Type: liveuser.TitleTypeUsername, Color: "#ffffff"},
			}},
		lm: lm,
	}
	param.checkUserMedalTitle()
	require.Len(param.u.Titles, 3)
	assert.Equal(liveuser.TitleTypeMedal, param.u.Titles[2].Type)
	assert.Equal(12, param.u.Titles[2].Level)
}

func TestAddCatFoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2023, 7, 2, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return when
	})
	defer goutil.SetTimeNow(nil)
	config.Conf.AB["enable_new_redis_key_duration_time"] = when.Add(-time.Hour).Unix()
	defer delete(config.Conf.AB, "enable_new_redis_key_duration_time")

	var (
		userID = int64(12)

		now = goutil.TimeNow()
		st  = util.BeginningOfDay(now)
	)

	// 清空背包礼物，并领取今日猫粮
	clearAddCatFood(t, userID)
	key := keys.KeyUsersAwardGiftDaily2.Format(st.Format(util.TimeFormatYMDWithNoSpace), userID)
	_, err := service.Redis.Set(key, 1, time.Minute).Result()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"room_id": 22489473, "user_id": 12},
		bson.M{"$unset": bson.M{"super_fan": ""}})
	require.NoError(err)

	goods := &livegoods.LiveGoods{
		ID:    1,
		Num:   25,
		Price: 800,
		Type:  livegoods.GoodsTypeSuperFan,
	}
	require.NoError(service.LiveDB.Save(goods).Error)

	param := superFanBuyParam{
		u:     &liveuser.Simple{UID: 12},
		goods: goods,
	}
	param.addCatFoods()

	userItems, err := useritems.Find(bson.M{
		"user_id":    userID,
		"gift_id":    bson.M{"$in": []int64{useritems.GiftIDCatFood, useritems.GiftIDCatCanFood}},
		"start_time": bson.M{"$gte": st.Unix()},
	}, nil)
	require.NoError(err)
	// 有猫罐头，没猫粮意味着补发成功
	require.Len(userItems, 1)
	assert.Equal(useritems.GiftIDCatCanFood, userItems[0].GiftID)
}

func TestSuperFanBuyParam_tryUnlockSuperFansSticker(t *testing.T) {
	require := require.New(t)

	sticker := params.Sticker{
		SuperFans: params.SuperFansStickerPackage{
			PackageID:        1,
			UnlockStartTime:  1,
			UnlockEndTime:    10000000000000,
			UnlockTriggerNum: 1,
		},
	}
	require.NoError(service.LRURedis.Set(keys.KeyParams1.Format(params.KeySticker), tutil.SprintJSON(sticker), 10*time.Second).Err())

	param := &superFanBuyParam{
		RoomID: 172842330,
	}
	require.NoError(livesticker.DB().
		Delete(livesticker.PackageOwner{}, "room_id = ? AND package_id = ?", param.RoomID, sticker.SuperFans.PackageID).Error)
	param.tryUnlockSuperFansSticker()

	owner := livesticker.PackageOwner{}
	err := livesticker.DB().
		Where("room_id = ? AND package_id = ?", param.RoomID, sticker.SuperFans.PackageID).
		Take(&owner).Error
	require.NoError(err)
}
