package chatroom

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFollowListParamLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := followListParam{
		c: handler.NewTestContext(http.MethodGet, "/follow/list?type=3", true, nil),
	}
	require.NoError(param.load())
	assert.Equal(followListTypeAll, param.listType)
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(goutil.DefaultPageSize), param.pageSize)
	assert.NotNil(param.u)
	assert.NotNil(param.roomOpt)
}

func addRoomTestData(r room.Room) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().InsertOne(ctx, r)
	if err != nil {
		return err
	}
	return nil
}

func addMedalTestData(s *livemedal.Simple) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().InsertOne(ctx, s)
	if err != nil {
		return err
	}
	return nil
}

func clearTestData(roomIDs []int64) error {
	if len(roomIDs) == 0 {
		return nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除直播间测试数据
	_, err := room.Collection().DeleteMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}})
	if err != nil {
		return err
	}
	// 删除亲密度测试数据
	_, err = livemedal.Collection().DeleteMany(ctx,
		bson.M{
			"room_id": bson.M{"$in": roomIDs},
			"user_id": int64(109),
		})
	if err != nil {
		return err
	}
	return nil
}

func TestActionFollowList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("typeAllResp 越界", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/list?page_size=1&p=10", true, nil)
		r, err := ActionFollowList(c)
		require.NoError(err)
		resp := r.(*roomSimpleList)
		assert.Empty(resp.Data)
		assert.NotNil(resp.Data)
	})

	t.Run("typeAllResp 未越界", func(t *testing.T) {
		// 构建测试数据
		// NOTICE: 需确认 MongoDB collection: test_rooms 中存在 room_id: int64(22489473), creator_id: 10 的这条记录
		require.NoError(service.DB.FirstOrCreate(&live.Live{
			ID:     10,
			UserID: 10,
			RoomID: roomID,
			Status: live.StatusOpen,
			Title:  "go test 用",
		}).Error)
		assert.NoError(attentionuser.Follow(12, 10))
		_, err := room.Update(roomID, bson.M{"catalog_id": 118})
		require.NoError(err)

		c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/list", true, nil)
		r, err := ActionFollowList(c)
		require.NoError(err)
		require.IsType(&roomSimpleList{}, r)
		resp := r.(*roomSimpleList)
		require.NotEmpty(resp.Data)
		assert.NotZero(resp.Data[0].Statistics.AttentionCount)
		assert.NotEmpty(resp.Data[0].CatalogName)
		assert.NotEmpty(resp.Data[0].CatalogColor)
		tutil.PrintJSON(resp)
	})

	// typeAllResp 读取未越界的情况
	t.Run("typeOnlyOpenResp", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/list?type=1", true, nil)
		r, err := ActionFollowList(c)
		require.NoError(err)
		resp := r.(*roomSimpleList)
		assert.EqualValues(len(resp.Data), resp.Pagination.Count)
	})
}

func TestActionFollowRoomList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=1&page_size=3", true, nil)
	c.User().ID = int64(109)
	res, err := ActionFollowRoomList(c)
	require.NoError(err)
	assert.NotNil(res)
}

func TestFollowRoomListParam_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无 marker 参数
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=0&page_size=3", true, nil)
	param := followRoomListParam{c: c}
	err := param.load()
	require.NoError(err)
	assert.EqualValues(followListTypeAll, param.listType)
	assert.Nil(param.marker)
	assert.EqualValues(3, param.pageSize)
	assert.NotNil(param.u)
	assert.NotNil(param.roomOpt)
	assert.False(param.roomOpt.FindLuckyBag)

	param.c.Equip().FromApp = true
	err = param.load()
	require.NoError(err)
	assert.True(param.roomOpt.FindLuckyBag)

	// 测试有 marker 参数
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=1&page_size=3&marker=1,2,3", true, nil)
	param = followRoomListParam{c: c}
	err = param.load()
	require.NoError(err)
	assert.EqualValues(followListTypeOpenOnly, param.listType)
	require.NotNil(param.marker)
	assert.EqualValues(1, param.marker.point)
	assert.EqualValues(2, param.marker.score)
	assert.EqualValues(3, param.marker.openTime)
	assert.EqualValues(3, param.pageSize)
	assert.NotNil(param.u)
	assert.NotNil(param.roomOpt)
}

func TestFollowRoomListParam_parseMarker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param followRoomListParam

	// 格式错误
	marker := "1,3"
	err := param.parseMarker(marker)
	require.Error(err)
	assert.EqualError(err, "参数格式错误")

	// 解析错误
	marker = "a,3,3"
	err = param.parseMarker(marker)
	require.Error(err)

	// 有效性检查失败
	marker = "-1,3,3"
	err = param.parseMarker(marker)
	require.Error(err)
	assert.EqualError(err, "参数值不在有效范围内")

	// 解析正常
	marker = "1,2,3"
	err = param.parseMarker(marker)
	require.NoError(err)
	assert.EqualValues(1, param.marker.point)
	assert.EqualValues(2.2, param.marker.score)
	assert.EqualValues(3, param.marker.openTime)
}

func TestFollowRoomListParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	err := clearTestData([]int64{100000005, 100000006, 100000007, 100000008, 100000012, 100000013, 100000014, 100000015, 100000017})
	require.NoError(err)

	// 创建房间号为 100000013 测试直播间
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(time.Unix(0, 0).Add(time.Minute))
	roomID := int64(100000013)
	r := room.Room{
		Helper: room.Helper{
			CreatorID:  13,
			RoomID:     roomID,
			Name:       "测试直播间 13",
			NameClean:  "测试直播间 13",
			Cover:      &cover,
			CatalogID:  106,
			Status:     room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 200},
			Statistics: room.Statistics{Score: 200},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)

	// 创建房间号为 100000014 测试直播间
	openTime = util.TimeToUnixMilli(time.Unix(0, 0).Add(2 * time.Minute))
	roomID = int64(100000014)
	r = room.Room{
		Helper: room.Helper{
			CreatorID:  14,
			RoomID:     roomID,
			Name:       "测试直播间 14",
			NameClean:  "测试直播间 14",
			Cover:      &cover,
			CatalogID:  106,
			Status:     room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 100},
			Statistics: room.Statistics{Score: 100},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)

	// 创建房间号为 100000015 测试直播间
	openTimeRoom15 := util.TimeToUnixMilli(time.Unix(0, 0).Add(3 * time.Minute))
	scoreRoom15 := int64(200)
	roomID = int64(100000015)
	r = room.Room{
		Helper: room.Helper{
			CreatorID:  15,
			RoomID:     roomID,
			Name:       "测试直播间 15",
			NameClean:  "测试直播间 15",
			Cover:      &cover,
			CatalogID:  106,
			Status:     room.Status{Open: room.StatusOpenTrue, OpenTime: openTimeRoom15, Score: float64(scoreRoom15)},
			Statistics: room.Statistics{Score: scoreRoom15},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)

	// 创建房间号为 100000017 测试直播间
	openTime = util.TimeToUnixMilli(goutil.TimeNow())
	roomID = int64(100000017)
	r = room.Room{
		Helper: room.Helper{
			CreatorID:  17,
			RoomID:     roomID,
			Name:       "测试直播间 17",
			NameClean:  "测试直播间 17",
			Cover:      &cover,
			CatalogID:  106,
			Status:     room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 150},
			Statistics: room.Statistics{Score: 150},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)

	userID := int64(109)
	// 创建直播间 100000013 的粉丝勋章
	pointRoom13 := int64(2000)
	s := &livemedal.Simple{RoomID: 100000013, CreatorID: 13, UserID: userID, Point: pointRoom13, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)
	// 创建直播间 100000014 的粉丝勋章
	pointRoom14 := int64(3000)
	s = &livemedal.Simple{RoomID: 100000014, CreatorID: 14, UserID: userID, Point: pointRoom14, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	// 断言第一页数据
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=1&page_size=1", true, nil)
	c.User().ID = userID
	param := followRoomListParam{
		c:        c,
		u:        c.User(),
		listType: followListTypeOpenOnly,
		pageSize: 1,
	}
	// 删除缓存
	key := keys.KeyMedalRoomFollowList2.Format(param.u.ID, param.listType)
	require.NoError(service.LRURedis.Del(key).Err())

	resp, err := param.resp()
	require.NoError(err)
	require.NotEmpty(resp.Data)
	require.EqualValues(1, len(resp.Data))
	assert.EqualValues(1, *resp.FollowStatus)
	assert.EqualValues(100000014, resp.Data[0].RoomID)
	assert.True(resp.Pagination.HasMore)
	require.NotNil(resp.Pagination.Count)
	assert.EqualValues(4, *resp.Pagination.Count)
	// 获取下一页数据需要的 marker 参数
	marker := resp.Pagination.Marker
	// 断言 marker 参数
	assert.Equal(fmt.Sprintf("%d,%d,%d", pointRoom14, 0, 0), marker)

	// 断言第二页数据
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=1&page_size=2&marker="+marker, true, nil)
	c.User().ID = userID
	param = followRoomListParam{
		c:        c,
		u:        c.User(),
		listType: followListTypeOpenOnly,
		marker: &followRoomListMarkerParam{
			point:    pointRoom14,
			score:    0,
			openTime: 0,
		},
		pageSize: 2,
	}
	// 删除缓存
	key = keys.KeyMedalRoomFollowList2.Format(param.u.ID, param.listType)
	require.NoError(service.LRURedis.Del(key).Err())
	resp, err = param.resp()
	require.NoError(err)
	require.NotEmpty(resp.Data)
	require.EqualValues(2, len(resp.Data))
	assert.EqualValues(100000013, resp.Data[0].RoomID)
	assert.EqualValues(100000015, resp.Data[1].RoomID)
	assert.True(resp.Pagination.HasMore)
	assert.Nil(resp.Pagination.Count)
	// 获取下一页数据需要的 marker 参数
	marker = resp.Pagination.Marker
	// 断言 marker 参数
	assert.Equal(fmt.Sprintf("%d,%d,%d", 0, scoreRoom15, openTimeRoom15), marker)

	// 断言第三页数据
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/room-list?type=1&page_size=3&marker="+marker, true, nil)
	c.User().ID = userID
	param = followRoomListParam{
		c:        c,
		u:        c.User(),
		listType: followListTypeOpenOnly,
		marker: &followRoomListMarkerParam{
			point:    0,
			score:    scoreRoom15,
			openTime: openTimeRoom15,
		},
		pageSize: 3,
	}
	// 删除缓存
	key = keys.KeyMedalRoomFollowList2.Format(param.u.ID, param.listType)
	require.NoError(service.LRURedis.Del(key).Err())
	resp, err = param.resp()
	require.NoError(err)
	require.NotEmpty(resp.Data)
	require.EqualValues(1, len(resp.Data))
	assert.EqualValues(100000017, resp.Data[0].RoomID)
	assert.False(resp.Pagination.HasMore)
	assert.Nil(resp.Pagination.Count)
	assert.Equal("", resp.Pagination.Marker)
}

func TestBuildRecommendTags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLastRankKey := usersrank.LastRankKey(usersrank.TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(testLastRankKey).Err())

	lastRankMembers := []*redis.Z{
		{Score: float64(4), Member: 9074501},
		{Score: float64(3), Member: 9074502},
		{Score: float64(2), Member: 9074503},
	}
	require.NoError(service.Redis.ZAdd(testLastRankKey, lastRankMembers...).Err())

	testRooms := []*room.Simple{
		{CreatorID: 9074501, CustomTag: &tag.CustomTag{TagName: "测试标签1"}},
		{CreatorID: 9074504, CustomTag: &tag.CustomTag{TagName: "测试标签2"}},
		{CreatorID: 9074503, CustomTag: &tag.CustomTag{TagName: "测试标签3"}},
		{CreatorID: 9074505},
	}
	buildRecommendTags(testRooms)
	require.NotNil(testRooms[0].RecommendTag)
	assert.Equal(room.RecommendTagTypeLastHourRank, testRooms[0].RecommendTag.Type)
	assert.Equal("https://static-test.missevan.com/live/labelicon/livelist/lasthour01-1.png", testRooms[0].RecommendTag.IconURL)
	require.NotNil(testRooms[1].RecommendTag)
	assert.Equal(room.RecommendTagTypeCustomTag, testRooms[1].RecommendTag.Type)
	assert.Equal("测试标签2", testRooms[1].RecommendTag.Text)
	require.NotNil(testRooms[2].RecommendTag)
	assert.Equal(room.RecommendTagTypeLastHourRank, testRooms[2].RecommendTag.Type)
	assert.Equal("https://static-test.missevan.com/live/labelicon/livelist/lasthour03-1.png", testRooms[2].RecommendTag.IconURL)
	assert.Nil(testRooms[3].RecommendTag)
}

func TestFollowListParam_GetMedalFollowRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	err := clearTestData([]int64{100000005, 100000006, 100000007, 100000008, 100000012, 100000013, 100000014, 100000015, 100000017})
	require.NoError(err)

	userID := int64(109)
	key1 := keys.KeyMedalRoomFollowList2.Format(userID, followListTypeAll)
	key2 := keys.KeyMedalRoomFollowList2.Format(userID, followListTypeOpenOnly)
	err = service.LRURedis.Del(key1, key2).Err()
	require.NoError(err)
	cache1, err := service.LRURedis.Get(key1).Result()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal("", cache1)
	cache2, err := service.LRURedis.Get(key2).Result()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal("", cache2)

	// 创建房间号为 100000013 测试直播间
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(time.Unix(0, 0).Add(time.Minute))
	roomID := int64(100000013)
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 13,
			RoomID:    roomID,
			Name:      "测试直播间 13",
			NameClean: "测试直播间 13",
			Cover:     &cover,
			CatalogID: 106,
			Status:    room.Status{Open: room.StatusOpenFalse, OpenTime: openTime, Score: 200},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)
	// 创建直播间 100000013 的粉丝勋章
	s := &livemedal.Simple{RoomID: 100000013, CreatorID: 13, UserID: userID, Point: 2000, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	// 创建房间号为 100000014 测试直播间
	openTime = util.TimeToUnixMilli(time.Unix(0, 0).Add(2 * time.Minute))
	roomID = int64(100000014)
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 14,
			RoomID:    roomID,
			Name:      "测试直播间 14",
			NameClean: "测试直播间 14",
			Cover:     &cover,
			CatalogID: 106,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 100},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)
	// 创建直播间 100000014 的粉丝勋章
	s = &livemedal.Simple{RoomID: 100000014, CreatorID: 14, UserID: userID, Point: 1999, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	// 创建房间号为 100000015 测试直播间
	openTimeRoom15 := util.TimeToUnixMilli(time.Unix(0, 0).Add(3 * time.Minute))
	scoreRoom15 := float64(150)
	roomID = int64(100000015)
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 15,
			RoomID:    roomID,
			Name:      "测试直播间 15",
			NameClean: "测试直播间 15",
			Cover:     &cover,
			CatalogID: 106,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTimeRoom15, Score: scoreRoom15},
		},
	}
	err = addRoomTestData(r)
	require.NoError(err)
	// 创建直播间 100000015 的粉丝勋章
	s = &livemedal.Simple{RoomID: 100000015, CreatorID: 15, UserID: userID, Point: 2000, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	// 获取所有有亲密度的主播信息
	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/list?type=0&page_size=3&p=1", true, nil)
	c.User().ID = int64(109)
	param := followRoomListParam{
		c:        c,
		u:        c.User(),
		listType: followListTypeAll,
	}
	followUserIDs := []int64{13, 14, 15, 17}

	// 测试 key1 缓存不存在
	res, err := param.getMedalFollowRooms(followUserIDs)
	require.NoError(err)
	assert.EqualValues(100000015, res[0].Room.RoomID)
	assert.EqualValues(100000014, res[1].Room.RoomID)
	assert.EqualValues(100000013, res[2].Room.RoomID)
	// 断言生成了缓存
	cache, err := service.LRURedis.Get(key1).Result()
	require.NoError(err)
	assert.NotEqual("", cache)

	// 测试 key1 缓存存在
	res, err = param.getMedalFollowRooms(followUserIDs)
	require.NoError(err)
	assert.EqualValues(100000015, res[0].Room.RoomID)
	assert.EqualValues(100000014, res[1].Room.RoomID)
	assert.EqualValues(100000013, res[2].Room.RoomID)

	// 获取开播状态的有亲密度的主播信息
	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/follow/list?type=1&page_size=3&p=1", true, nil)
	c.User().ID = int64(109)
	param = followRoomListParam{
		c:        c,
		u:        c.User(),
		listType: followListTypeOpenOnly,
	}

	// 测试 key2 缓存不存在
	res, err = param.getMedalFollowRooms(followUserIDs)
	require.NoError(err)
	assert.EqualValues(100000015, res[0].Room.RoomID)
	assert.EqualValues(100000014, res[1].Room.RoomID)
	// 断言生成了缓存
	cache, err = service.LRURedis.Get(key2).Result()
	require.NoError(err)
	assert.NotEqual("", cache)

	// 测试 key2 缓存存在
	res, err = param.getMedalFollowRooms(followUserIDs)
	require.NoError(err)
	assert.EqualValues(100000015, res[0].Room.RoomID)
	assert.EqualValues(100000014, res[1].Room.RoomID)
}

func TestActionFollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock("go://person/follow", func(input interface{}) (output interface{}, err error) {
		return "关注成功", nil
	})
	defer cleanup()

	cleanup = mrpc.SetMock("go://person/unfollow", func(input interface{}) (output interface{}, err error) {
		return "取消关注成功", nil
	})
	defer cleanup()

	testFromBlockUserID := int64(1)
	cleanup = mrpc.SetMock(userapi.URIGoUserBlockStatus, func(input interface{}) (output interface{}, err error) {
		if input.(map[string][]int64)["user_ids"][0] == testFromBlockUserID {
			return handler.M{"block_status": []bool{true, false}}, nil
		}
		return handler.M{"block_status": []bool{false, false}}, nil
	})
	defer cleanup()

	param := map[string]interface{}{
		"type": "add",
	}
	newC := func() *handler.Context {
		return handler.NewTestContext("POST", "follow", true, param)
	}

	_, err := ActionFollow(newC())
	assert.Equal(actionerrors.ErrParams, err)

	param["room_id"] = 123456789
	_, err = ActionFollow(newC())
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	param["room_id"] = roomID
	param["type"] = "456"
	_, err = ActionFollow(newC())
	assert.Equal(actionerrors.ErrParams, err)

	key, _ := roomFollowedKeyLock(roomID, goutil.TimeNow())
	assert.NoError(service.Redis.Del(key).Err())
	param["type"] = "add"
	param["notify"] = 1
	c := newC()
	c.User().ID = 123
	r, err := ActionFollow(c)
	require.NoError(err)
	assert.Equal(handler.M{"ok": 1, "type": 1}, r)
	notified, err := service.Redis.SIsMember(key, c.UserID()).Result()
	assert.True(err == nil && notified, err)

	// 测试拉黑
	room, err := room.FindOne(bson.M{"creator_id": testFromBlockUserID})
	require.NoError(err)
	require.NotNil(room)
	param["room_id"] = room.RoomID
	_, err = ActionFollow(newC())
	assert.EqualError(err, "由于对方设置，你还不能关注")

	param["type"] = "remove"
	r, err = ActionFollow(newC())
	require.NoError(err)
	assert.Equal(handler.M{"ok": 1, "type": 0}, r)

	c = handler.CreateTestContext(true)
	v := &url.Values{}
	v.Add("room_id", roomIDStr)
	v.Add("type", "remove")
	c.C.Request, _ = http.NewRequest("POST", "attention",
		strings.NewReader(v.Encode()))
	c.C.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	r, err = ActionFollow(c)
	require.NoError(err)
	assert.Equal(handler.M{"ok": 1, "type": 0}, r)
}

func TestBroadcastFollow(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key, _ := roomFollowedKeyLock(roomID, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())
	keys := []string{
		keys.KeyIMPubSub2.Format(2, 0), // 测试数据库上的配置
		keys.KeyIMPubSub2.Format(2, 1),
	}
	watcher := imRedis.Subscribe(keys...)
	defer watcher.Close()
	ch := watcher.Channel()
	time.Sleep(time.Second)

	before := goutil.TimeNow()
	fansID := noble7UserID
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "", nil)
	c.User().ID = fansID
	require.NoError(userstatus.SetInvisible(fansID, false))
	broadcastFollow(roomID, c)
	after := goutil.TimeNow()
	assert.Truef(after.After(before), "before: %d, after: %d", before.UnixNano(), after.UnixNano())
	locks := make([]string, int64(after.Sub(before).Seconds())+1)
	var rec struct {
		Payload struct {
			Type   string          `json:"type"`
			Event  string          `json:"event"`
			RoomID int64           `json:"room_id"`
			User   liveuser.Simple `json:"user"`
			Bubble struct {
				Type       string `json:"type"`
				NobleLevel int    `json:"noble_level"`
			} `json:"bubble"`
		} `json:"payload"`
	}
	f := func(m *redis.Message) (bool, error) {
		_ = json.Unmarshal([]byte(m.Payload), &rec)
		if rec.Payload.Type == "member" && rec.Payload.Event == "followed" &&
			rec.Payload.RoomID == roomID && rec.Payload.User.UID == fansID {
			time.Sleep(2 * time.Second) // 等待 lock 过期
			return true, nil
		}
		return false, nil
	}
	assert.NoError(receiveMessage(ch, f))
	for i := 0; i < len(locks); i++ {
		_, locks[i] = roomFollowedKeyLock(roomID, before.Add(time.Duration(i)*time.Second))
	}
	res, err := service.Redis.Exists(locks...).Result()
	assert.NoError(err, locks)
	assert.Zero(res)
	notified, err := service.Redis.SIsMember(key, fansID).Result()
	require.NoError(err)
	assert.True(notified)
}

func TestRoomFollowedKey(t *testing.T) {
	assert := assert.New(t)
	when := time.Unix(1, 0)
	key, lock := roomFollowedKeyLock(123, when)
	assert.Equal("test_rooms/followed/123", key)
	assert.Equal("lock:rooms/followed/123/01", lock)
	when = time.Unix(324, 123)
	key, lock = roomFollowedKeyLock(7777, when)
	assert.Equal("test_rooms/followed/7777", key)
	assert.Equal("lock:rooms/followed/7777/24", lock)
	when = time.Unix(300, 123)
	_, lock = roomFollowedKeyLock(7777, when)
	assert.Equal("lock:rooms/followed/7777/00", lock)
}
