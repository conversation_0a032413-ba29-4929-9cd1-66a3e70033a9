package chatroom

import (
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const maxSuperFanDays = 24 * 30

// calculateSuperFanNewExpireTime
// https://info.missevan.com/pages/viewpage.action?pageId=28280597
func calculateSuperFanNewExpireTime(lastExpireTimeStamp int64, buyMonthNum int) (newExpireTime time.Time, isExceedMax bool) {
	var lastExpireTime time.Time
	extendDays := buyMonthNum * 30

	if lastExpireTimeStamp == 0 {
		lastExpireTime = util.TimeNow()
		extendDays++ // 开通超粉（最后的过期时间为 0）有效期加 1 天
	} else {
		lastExpireTime = time.Unix(lastExpireTimeStamp, 0)
	}

	newExpireTime = util.BeginningOfDay(lastExpireTime.AddDate(0, 0, extendDays))
	maxExpireTime := util.BeginningOfDay(util.TimeNow().AddDate(0, 0, maxSuperFanDays+1))

	isExceedMax = newExpireTime.After(maxExpireTime)
	if isExceedMax {
		newExpireTime = maxExpireTime
	}
	return
}

type buySuperFanResult struct {
	*userapi.BalanceResp

	NewExpireTime time.Time `json:"-"`
}

func buySuperFan(uc mrpc.UserContext, userID int64, goods livegoods.Goods, r *room.Room,
	b *bubble.Simple, db *gorm.DB) (*buySuperFanResult, error) {
	if db == nil {
		db = livetxnorder.LiveTxnOrder{}.DB()
	}

	nowStamp := util.TimeNow().Unix()
	var lastSuperFanOrder livetxnorder.LiveTxnOrder
	err := db.Select("id, expire_time").
		Where("buyer_id = ? AND seller_id = ?", userID, goods.GoodsSellerID()).
		Where("goods_type = ?", goods.GoodsType()).
		Where("status = ? AND expire_time > ?", livetxnorder.StatusSuccess, nowStamp).
		Scan(&lastSuperFanOrder).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var renew bool
	if goods.GoodsAttr() == livegoods.AttrSuperFanRenew {
		if lastSuperFanOrder.ID == 0 {
			return nil, actionerrors.ErrSuperFanNotRegisteredCannotRenew
		}
		renew = true
	} else if lastSuperFanOrder.ID > 0 {
		return nil, actionerrors.ErrSuperFanCannotRegisterTwice
	}

	// 先下单
	more := &livetxnorder.MoreInfo{
		Bubble: b,
		Num:    goods.GoodsNum(),
	}
	if r.IsOpen() {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusOpen)
	} else {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusClosed)
	}
	superFanOrder := livetxnorder.NewOrder(goods, userID, 0, more)
	err = db.Create(superFanOrder).Error
	if err != nil {
		return nil, err
	}
	// 再完成钻石流转（RPC）
	// TODO: 传递业务方 ID 及来源（此方式 RPC 调用不能放在事务中）
	resp := new(buySuperFanResult)
	resp.BalanceResp, err = userapi.BuySuperFan(userID, goods.GoodsSellerID(),
		goods.GoodsID(), goods.GoodsTotalPrice(),
		goods.GoodsNum(), goods.OrderTitle(), renew, r.Status.OpenLogID, uc)
	if err != nil {
		return nil, err
	}
	if resp.TransactionID == 0 {
		return nil, actionerrors.NewErrServerInternal(errors.New("交易 ID 不存在"), nil)
	}

	err = servicedb.Tx(db, func(tx *gorm.DB) error {
		// 最后更新订单
		resp.NewExpireTime, _ = calculateSuperFanNewExpireTime(lastSuperFanOrder.ExpireTime, goods.GoodsNum())
		_db := tx.Table(livetxnorder.LiveTxnOrder{}.TableName()).
			Where("id = ? AND status = ?", superFanOrder.ID, superFanOrder.Status).
			Update(map[string]interface{}{
				"expire_time":   resp.NewExpireTime.Unix(),
				"tid":           resp.TransactionID,
				"status":        livetxnorder.StatusSuccess,
				"modified_time": nowStamp,
			})
		if _db.Error != nil {
			return actionerrors.NewErrServerInternal(_db.Error, nil)
		}
		if _db.RowsAffected == 0 {
			logger.WithFields(logger.Fields{"user_id": userID, "order_id": superFanOrder.ID}).Error("超粉：更新订单失败")
			return actionerrors.ErrLiveTxnOrderNotExist
		}
		if lastSuperFanOrder.ID > 0 {
			// 旧的订单过期
			_db = tx.Table(livetxnorder.LiveTxnOrder{}.TableName()).
				Where("buyer_id = ? AND seller_id = ?", userID, goods.GoodsSellerID()).
				Where("status = ? AND expire_time > ?", livetxnorder.StatusSuccess, nowStamp).
				Where("id = ?", lastSuperFanOrder.ID).
				Update(map[string]interface{}{
					"expire_time":   nowStamp,
					"modified_time": nowStamp,
				})
			if _db.Error != nil {
				return actionerrors.NewErrServerInternal(_db.Error, nil)
			}
			if _db.RowsAffected == 0 {
				logger.WithFields(logger.Fields{"user_id": userID, "order_id": lastSuperFanOrder.ID}).Error("超粉：更新订单失败")
				return actionerrors.ErrLiveTxnOrderNotExist
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func hasBuyQualification(roomID, creatorID, userID int64, g *livegoods.LiveGoods) (bool, error) {
	if g.More == "" {
		return true, nil
	}
	more, err := g.UnmarshalMore()
	if err != nil {
		return false, err
	}
	if more == nil || more.SuperFan == nil {
		return true, nil
	}
	if more.SuperFan.AllowRoomIDs != nil && !goutil.HasElem(more.SuperFan.AllowRoomIDs, roomID) {
		return false, nil
	}
	switch more.SuperFan.LimitType {
	case livegoods.SuperFanLimitTypeNot:
		return true, nil
	case livegoods.SuperFanLimitTypeFirst:
		// 全站首次开通
		isFirst, err := livetxnorder.IsFirstBuySuperFan(userID)
		if err != nil {
			return false, err
		}
		return isFirst, nil
	case livegoods.SuperFanLimitTypeOpen:
		// 直播间内开通
		isSuperFan, err := livemedal.IsRoomSuperFan(roomID, userID)
		if err != nil {
			return false, err
		}
		if isSuperFan {
			return false, nil
		}
		// 判断是否买过优惠超粉
		isPurchased, err := livetxnorder.IsPurchasedSuperFanInRoom(creatorID, userID, g.ID)
		if err != nil {
			return false, err
		}
		return !isPurchased, nil
	case livegoods.SuperFanLimitTypeRenewal:
		// 直播间内续费（优惠超粉仅支持购买一次）
		isSuperFan, err := livemedal.IsRoomSuperFan(roomID, userID)
		if err != nil {
			return false, err
		}
		if !isSuperFan {
			// 仅超粉可优惠续费超粉
			return false, nil
		}
		// 判断是否买过优惠超粉
		isPurchased, err := livetxnorder.IsPurchasedSuperFanInRoom(creatorID, userID, g.ID)
		if err != nil {
			return false, err
		}
		return !isPurchased, nil
	case livegoods.SuperFanLimitTypeBuy:
		// 直播间内开通或续费（优惠超粉仅支持购买一次）
		// 判断是否买过优惠超粉
		isPurchased, err := livetxnorder.IsPurchasedSuperFanInRoom(creatorID, userID, g.ID)
		if err != nil {
			return false, err
		}
		return !isPurchased, nil
	}
	return false, nil
}
