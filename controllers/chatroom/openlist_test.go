package chatroom

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/recommended"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 测试干预卡列表
const (
	lastHourRoom int64 = 3192516 // 小时榜直播间
	liveNoble    int64 = 4738243 // 神话推荐
	liveOpOne    int64 = 123     // 运营干预卡一
	liveOpTwo    int64 = 1234    // 运营干预卡二
	liveOpThree  int64 = 13579   // 运营干预卡三
	liveOpFour   int64 = 236810  // 运营干预卡四
)

func TestOpenListTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(roomSimpleList{}, "Datas", "pagination")
}

func TestRoomSimpleList_checkResp(t *testing.T) {
	assert := assert.New(t)
	resp := roomSimpleList{
		Data: []*room.Simple{{CreatorID: 12}},
	}
	resp.checkResp()
	assert.NotEmpty(resp.Data[0].CoverURL)
}

func findAndUpdateTestRoom(t *testing.T, roomID int64) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	var rh room.Helper
	firstName := "测试 openList 房间 1"
	rh.Status.Open = 1
	rh.RoomID = roomID
	rh.Name = firstName
	rh.NameClean = firstName
	rh.Type = "live"
	rh.TagIDs = []int64{3}
	rh.CreatorID = 512
	rh.Status.RedPacket = 1
	rh.Status.OpenTime = goutil.TimeNow().Unix()
	rh.CustomTagID = 10003
	// 确保只满足新星的开播时长筛选
	rh.Statistics.TotalDuration = 120*3600*1000 - 1
	rh.Statistics.Revenue = room.NovaRevenueThreshold
	err := collection.FindOneAndUpdate(ctx, bson.M{"room_id": roomID},
		bson.M{"$set": rh}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(t, err)
}
func insertTestRoom(t *testing.T, roomID int64, roomName string) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	// 删除 roomID 相同或者 NameClean 相同的测试数据
	filter := bson.M{
		"$or": []bson.M{
			{"room_id": roomID},
			{"name_clean": "clean-" + roomName},
		},
	}
	_, err := collection.DeleteMany(ctx, filter)
	require.NoError(t, err)
	var rh room.Helper
	rh.Status.Open = 1
	rh.RoomID = roomID
	rh.Name = roomName
	rh.NameClean = "clean-" + roomName
	rh.Type = "live"
	rh.Status.OpenTime = goutil.TimeNow().Unix()
	_, err = collection.InsertOne(ctx, rh)
	require.NoError(t, err)
}

// mock 天马推荐响应数据
func mockTianmaRecommend(c *handler.Context, param *openListParam) (*tianma.RecommendResult, error) {
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)

	pipeline := []bson.M{
		{"$match": filter},
		{"$limit": 50},
		{"$sample": bson.M{"size": 10}},
	}

	// 执行聚合查询
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	var liveRooms []*room.Simple
	if err = cursor.All(ctx, &liveRooms); err != nil {
		return nil, err
	}
	var items []tianma.RecommendItem
	for i, r := range liveRooms {
		var gotoType tianma.Goto
		if i == 3 { // 第四个数据特殊处理
			gotoType = tianma.GotoOpLive
		} else {
			gotoType = tianma.GotoLive
		}
		item := tianma.RecommendItem{
			ID:        r.RoomID,
			Goto:      gotoType,
			Source:    "mock_source",
			AVFeature: "{\"operate_id\":1,\"operate_source\":2}",
			TrackID:   "mock_track_id",
		}
		items = append(items, item)
	}

	return &tianma.RecommendResult{
		UserFeature: "mock_user_feature",
		Data:        items,
	}, nil
}

func TestActionOpenList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 设置小时榜
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	assert.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: 516, Score: 516}).Err())
	require.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	// 子测试 1：catalog_id 参数测试
	t.Run("CatalogIDTest", func(t *testing.T) {
		service.Cache10s.Flush()
		c := handler.NewTestContext(http.MethodGet, "/open/list?catalog_id=104", false, nil)
		r, err := ActionOpenList(c)
		require.NoError(err)
		require.NotNil(r)
		resp := r.(*openListResponse)
		assert.Equal(int64(20), resp.Pagination.PageSize)
		assert.GreaterOrEqual(resp.Pagination.Count, int64(1))
		assert.Equal(resp.Pagination.Count, int64(len(resp.Data)))
		for i := 0; i < len(resp.Data); i++ {
			assert.Contains([]int64{118, 119}, resp.Data[i].CatalogID)
		}
	})

	// 子测试 2：传参 type = 1 按最新排序测试
	t.Run("SortByLatestTest", func(t *testing.T) {
		service.Cache10s.Flush()
		// 插入测试房间数据
		firstRoomID := int64(122340)
		findAndUpdateTestRoom(t, firstRoomID)

		c := handler.NewTestContext(http.MethodGet, "/open/list?type=1", false, nil)
		c.Equip().FromApp = false
		r, err := ActionOpenList(c)
		require.NoError(err)
		resp := r.(*openListResponse)
		require.GreaterOrEqual(len(resp.Data), 2)
		roomIDs := []int64{firstRoomID}
		index := 0
		for i := 0; i < len(resp.Data) && index < len(roomIDs); i++ {
			if resp.Data[i].RoomID == roomIDs[index] {
				require.NotNil(resp.Data[i].CustomTag)
				assert.EqualValues(10003, resp.Data[i].CustomTag.TagID)
				assert.EqualValues("test10003", resp.Data[i].CustomTag.TagName)
				index++
			}
		}
		assert.Equal(1, index)
	})

	// 子测试 3：传参 type = 2 新星测试
	t.Run("NovaTypeTest", func(t *testing.T) {
		// 清空缓存，并设置 AB 配置参数前保存原值
		service.Cache10s.Flush()
		oldNovaUpdate := config.Conf.AB["nova_update_time"]
		config.Conf.AB["nova_update_time"] = 1612152000
		t.Cleanup(func() {
			config.Conf.AB["nova_update_time"] = oldNovaUpdate
		})
		firstRoomID := int64(122340)
		findAndUpdateTestRoom(t, firstRoomID)

		c := handler.NewTestContext(http.MethodGet, "/open/list?type=2", false, nil)
		r, err := ActionOpenList(c)
		require.NoError(err)
		resp := r.(*openListResponse)
		require.GreaterOrEqual(len(resp.Data), 2)
		var respRoomIDs []int64
		for i := range resp.Data {
			respRoomIDs = append(respRoomIDs, resp.Data[i].RoomID)
		}
		assert.Contains(respRoomIDs, firstRoomID)
	})

	// 子测试 4：按 tag_id = 3 过滤测试
	t.Run("TagTest", func(t *testing.T) {
		service.Cache10s.Flush()
		firstRoomID := int64(122340)
		findAndUpdateTestRoom(t, firstRoomID)

		c := handler.NewTestContext(http.MethodGet, "/open/list?tag_id=3", false, nil)
		r, err := ActionOpenList(c)
		require.NoError(err)
		resp := r.(*openListResponse)
		require.GreaterOrEqual(len(resp.Data), 1)
		var respRoomIDs []int64
		for i := range resp.Data {
			respRoomIDs = append(respRoomIDs, resp.Data[i].RoomID)
		}
		assert.Contains(respRoomIDs, firstRoomID)
	})

	// 子测试 5：缓存测试——确保请求结果存入缓存，并且数据序列化正确
	t.Run("TestCache", func(t *testing.T) {
		// 清空缓存环境
		service.Cache10s.Flush()

		// 先发起请求，缓存会被写入
		c := handler.NewTestContext(http.MethodGet, "/open/list?tag_id=3", false, nil)
		r, err := ActionOpenList(c)
		require.NoError(err)
		resp := r.(*openListResponse)
		// 读取缓存数据
		cacheKey := "chatroomOpenList:v2:catalog_id=0&p=1&pagesize=20&tag_id=3&type=0:1"
		cache, ok := service.Cache10s.Get(cacheKey)
		require.True(ok)
		var data cacheData
		require.NoError(json.Unmarshal(cache.([]byte), &data))
		expectedJSON, err := json.Marshal(resp)
		require.NoError(err)
		actualJSON, err := json.Marshal(data.Data)
		require.NoError(err)
		assert.JSONEq(string(expectedJSON), string(actualJSON))
	})

	// 子测试 6：测试不从 Web 查询时 red packet 状态为 0（测试 DB 和缓存两种场景）
	t.Run("TestNoRedPacketStatusFromWeb", func(t *testing.T) {
		// 清空缓存
		service.Cache10s.Flush()
		runTest := func(t *testing.T) {
			c := handler.NewTestContext(http.MethodGet, "/open/list", false, nil)
			r, err := ActionOpenList(c)
			require.NoError(err)
			resp := r.(*openListResponse)
			require.GreaterOrEqual(len(resp.Data), 1)
			for _, r := range resp.Data {
				assert.Equal(0, r.Status.RedPacket)
			}
		}
		t.Run("QueryFromDB", runTest)
		t.Run("QueryFromCache", runTest)
	})

	t.Run("兜底状态下请求第二页", func(t *testing.T) {
		// 恢复正常mock函数但保持兜底状态
		fetchTianmaRecommendFunc = mockTianmaRecommend
		require.NoError(setFallbackState("test_buvid_integrated", 10*time.Minute))

		c2 := handler.NewTestContext(http.MethodGet, "/open/list?p=2", false, nil)
		c2.Equip().FromApp = true
		c2.Equip().BUVID = "test_buvid_integrated"

		resp, err := ActionOpenList(c2)
		require.NoError(err)
		assert.NotNil(resp)
		openResp := resp.(*openListResponse)
		assert.Equal(int64(2), openResp.Pagination.P)
		assert.NotEmpty(openResp.Data)
	})
	// 清理兜底状态
	require.NoError(cleanFallbackState("test_buvid_integrated"))

	t.Run("热门 tab 插卡测试 - 展示推荐 tab 时，不插卡", func(t *testing.T) {
		// 添加运营干预卡
		insertTestRoom(t, liveOpOne, "运营干预卡一")

		c := handler.NewTestContext(http.MethodGet, "/open/list?p=1", true, nil)
		c.Equip().BUVID = "abcde"
		c.Equip().FromApp = true

		// mock 版本
		c.Equip().OS = goutil.IOS
		c.Equip().AppVersion = "6.3.5"

		// mock 实验白名单
		c.User().ID = 123
		config.Conf.Params.LiveFeed.TabABAllowListUserIDs = []int64{c.UserID()}

		// mock 开启个性化推荐
		cancel := mrpc.SetMock(userapi.URLGetUserConfig, func(input interface{}) (output interface{}, err error) {
			enable := int(userapi.ConfigEnable)
			return userapi.GetUserConfigResp{
				Config: &userapi.MUserConfig{
					AppConfig: userapi.AppConfig{
						PersonalizedRecommend: &enable,
					},
				},
			}, nil
		})
		defer cancel()

		r, err := ActionOpenList(c)
		require.NoError(err)

		resp, ok := r.(*openListResponse)
		require.True(ok)

		roomIDs := goutil.SliceMap(resp.Data, func(r *recommended.RoomWithTrace) int64 {
			return r.RoomID
		})
		// 不存在运营干预的房间 ID
		assert.NotContains(roomIDs, liveOpOne)
	})
}

func TestNewOpenListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "/open/list?p=a", false, nil)
	_, err := newOpenListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "/open/list?type=4", false, nil)
	_, err = newOpenListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "/open/list?catalog_id=104&tag_id=1", false, nil)
	_, err = newOpenListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 查询分区失效
	c = handler.NewTestContext("GET", "/open/list?type=1&catalog_id=104", false, nil)
	param, err := newOpenListParam(c)
	require.NoError(err)
	assert.Zero(param.catalogID)

	// 查询新星
	c = handler.NewTestContext("GET", "/open/list?tag_id=1", false, nil)
	param, err = newOpenListParam(c)
	require.NoError(err)
	assert.Equal(openListTypeNova, param.listType)
}

func TestOpenListParam_findSubCatalogIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := openListParam{}
	require.NoError(param.findSubCatalogIDs(), "不查询子分区")
	param.catalogID = 104
	require.NoError(param.findSubCatalogIDs())
	assert.NotEmpty(param.subCatalogIDs)
}

func TestOpenListParam_findList(t *testing.T) {
	assert := assert.New(t)

	param := openListParam{p: 100, pageSize: 20}
	assert.NoError(param.findList())
	assert.NotNil(param.Data)

	param = openListParam{p: 1, pageSize: 20}
	assert.NoError(param.findList())
	assert.Greater(len(param.Data), 1)
	// 有数据的返回 TestActionOpenList 已测
}

func TestOpenListParam_findRecommendRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	param := openListParam{
		roomSimpleList: &roomSimpleList{
			Data: []*room.Simple{{RoomID: 100}},
		},
		p: 1}
	// 有 tag 的情况下不查询推荐房间
	param.tagID = 1
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom)
	assert.Nil(param.nrRoom)
	assert.Empty(param.recommendRooms)

	// 测试上小时榜无人
	param.tagID = 0
	require.NoError(service.Redis.Del(key).Err())
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom)
	// 主播没开播的情况
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: -12, Score: 12}).Err())
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom, 0)
	// 测试小时榜
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: openingRoom.CreatorID, Score: 516}).Err())
	require.NoError(service.Redis.Expire(key, 5*time.Minute).Err())
	param.findRecommendRoom()
	require.NotNil(param.lastHourRoom)
	assert.Equal(2, *param.lastHourRoom.Index)
	assert.Equal(param.iconURLs[1], map[int64]string{
		param.lastHourRoom.RoomID: "https://static-test.missevan.com/live/labelicon/livelist/lasthour01-1.png",
	})
	// 神话和上小时榜是同一个的情况
	now := goutil.TimeNow()
	nobleKey := keys.KeyNobleRecommend1.Format(now.Minute() / 10 * 10)
	service.Cache5Min.Set(nobleKey, &livenoblerecommend.NobleRecommend{RoomID: openingRoom.RoomID}, 0)
	param.lastHourRoom = nil
	param.nrRoom = nil
	param.findRecommendRoom()
	require.NotNil(param.nrRoom)
	assert.Equal(5, *param.nrRoom.Index)
	assert.Equal(param.iconURLs[2], map[int64]string{
		param.nrRoom.RoomID: service.Storage.Parse(config.Conf.Params.NobleParams.RecommendListIcon),
	})
	require.NotNil(param.lastHourRoom)
	assert.Equal(2, *param.lastHourRoom.Index)
	// 只有神话推荐（分区情况）
	param.lastHourRoom = nil
	param.nrRoom = nil
	param.catalogID = openingRoom.CatalogID
	param.subCatalogIDs = []int64{openingRoom.CatalogID}
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom)
	require.NotNil(param.nrRoom)
	assert.Equal(1, *param.nrRoom.Index)
	// 新星置顶
	param.recommendRooms = nil
	service.Cache5Min.Flush()
	rec := liverecommendedelements.Model{
		ID:          33,
		Sort:        3,
		ElementType: liverecommendedelements.ElementSquare,
		ElementID:   lastHourRoom,
		Attribute: liverecommendedelements.Attribute{
			Name: strconv.Itoa(liverecommendedelements.SquareTypeNova),
		},
	}
	require.NoError(liverecommendedelements.TableSquare(nil).Assign(map[string]interface{}{
		"start_time":  now.Unix() - 10,
		"expire_time": now.Unix() + 100,
	}).FirstOrCreate(&rec).Error)
	_, err := room.Update(lastHourRoom, bson.M{
		"statistics.revenue":        0,
		"statistics.total_duration": 0,
	})
	require.NoError(err)
	param.listType = openListTypeNova
	param.findRecommendRoom()
	require.Len(param.recommendRooms, 1)
	assert.Equal(3, *param.recommendRooms[0].Index)
	// 试试有推荐但是房间已经不是新星了
	param.recommendRooms = nil
	_, err = room.Update(lastHourRoom, bson.M{
		"statistics.revenue":        livemedal.RevenueThreshold,
		"statistics.total_duration": 999999999,
	})
	require.NoError(err)
	param.recommendRooms = []*room.Simple{}
	param.findRecommendRoom()
	assert.Empty(param.recommendRooms)
}

func TestOpenListParam_assignRecommend(t *testing.T) {
	assert := assert.New(t)

	var param *openListParam
	resetResp := func(roomID []int64) {
		param = &openListParam{
			roomSimpleList: &roomSimpleList{},
		}
		param.Data = make([]*room.Simple, len(roomID))
		for k, v := range roomID {
			param.Data[k] = &room.Simple{
				RoomID: v,
			}
		}
	}
	assertData := func(data []*room.Simple, expected []int64) {
		roomIDs := make([]int64, len(data))
		for i := range data {
			roomIDs[i] = data[i].RoomID
		}
		assert.Equal(expected, roomIDs)
	}
	t.Run("没有推荐", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4})
	})
	t.Run("神话推荐保底位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5})
		param.nrRoom = &room.Simple{RoomID: 6}
		param.nrRoom.SetIndex(5)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4, 6, 5})
	})
	t.Run("神话推荐不是保底位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6, 7, 8, 9})
		param.recommendRooms = []*room.Simple{{RoomID: 4}, {RoomID: 7}, {RoomID: 5}}
		param.recommendRooms[0].SetIndex(2)
		param.recommendRooms[1].SetIndex(4)
		param.recommendRooms[2].SetIndex(6)
		param.nrRoom = &room.Simple{RoomID: 1}
		param.nrRoom.SetIndex(5)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 4, 2, 7, 3, 5, 6, 8, 9})
	})
	t.Run("上小时榜和推荐直播间", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.lastHourRoom = &room.Simple{RoomID: 6}
		param.lastHourRoom.SetIndex(2)
		param.recommendRooms = []*room.Simple{{RoomID: 5}}
		param.recommendRooms[0].SetIndex(4)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 6, 2, 5, 3, 4})
	})
	t.Run("固定位直播间太过靠后，向前补位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.recommendRooms = []*room.Simple{{RoomID: 5}}
		param.recommendRooms[0].SetIndex(8)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4, 5})
	})
	t.Run("小时榜逻辑和配置冲突", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.lastHourRoom = &room.Simple{RoomID: 5}
		param.lastHourRoom.SetIndex(2)
		param.recommendRooms = []*room.Simple{{RoomID: 1}}
		param.recommendRooms[0].SetIndex(2)
		param.assignRecommend()
		assertData(param.Data, []int64{2, 5, 1, 3, 4})
	})
	t.Run("神话推荐和配置冲突", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6})
		param.recommendRooms = []*room.Simple{{RoomID: 6}}
		param.recommendRooms[0].SetIndex(3)
		param.nrRoom = &room.Simple{RoomID: 5}
		param.nrRoom.SetIndex(3)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 5, 6, 2, 3, 4})
	})
	t.Run("神话推荐和配置冲突完全", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6})
		param.recommendRooms = []*room.Simple{
			{RoomID: 6},
			{RoomID: 7},
			{RoomID: 8},
		}
		param.recommendRooms[0].SetIndex(1)
		param.recommendRooms[1].SetIndex(2)
		param.recommendRooms[2].SetIndex(3)
		param.nrRoom = &room.Simple{RoomID: 5}
		param.nrRoom.SetIndex(3)
		param.assignRecommend()
		assertData(param.Data, []int64{5, 6, 7, 8, 1, 2, 3, 4})
	})
	t.Run("热门列表中只有【上小时榜第一】一个直播间", func(t *testing.T) {
		resetResp([]int64{1})
		param.lastHourRoom = &room.Simple{RoomID: 1}
		param.lastHourRoom.SetIndex(2)
		param.assignRecommend()
		assertData(param.Data, []int64{1})
	})
	t.Run("小时榜，神话推荐，固定位推荐同时存在", func(t *testing.T) {
		// 按照热度排序：直播间 A > 【神话推荐】>【上小时榜第一】>【固定第 3】
		resetResp([]int64{1, 2, 3, 4})
		// 【上小时榜第一】
		param.lastHourRoom = &room.Simple{RoomID: 3}
		param.lastHourRoom.SetIndex(2)
		// 神话推荐
		param.nrRoom = &room.Simple{RoomID: 2}
		param.nrRoom.SetIndex(5)
		// 固定
		param.recommendRooms = []*room.Simple{
			{RoomID: 4},
		}
		param.recommendRooms[0].SetIndex(4)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 3, 2, 4})
	})
	t.Run("优先保证神话推荐和上小时榜第一", func(t *testing.T) {
		// 按照热度排序：直播间 A > 直播间 B > 直播间 C >【固定第 2】>【固定第 3】>【固定第 4】>【上小时榜第一】>【神话推荐】
		resetResp([]int64{1, 2, 3, 4, 5, 6, 7, 8})
		// 【上小时榜第一】
		param.lastHourRoom = &room.Simple{RoomID: 7}
		param.lastHourRoom.SetIndex(2)
		// 神话推荐
		param.nrRoom = &room.Simple{RoomID: 8}
		param.nrRoom.SetIndex(5)
		// 固定
		param.recommendRooms = []*room.Simple{
			{RoomID: 4}, {RoomID: 5}, {RoomID: 6},
		}
		param.recommendRooms[0].SetIndex(2)
		param.recommendRooms[1].SetIndex(3)
		param.recommendRooms[2].SetIndex(4)

		param.assignRecommend()
		assertData(param.Data, []int64{8, 7, 4, 5, 6, 1, 2, 3})
	})
}

func TestOpenListParam_assignIconURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startTime := goutil.TimeNow().Add(-time.Hour).Unix()
	testData := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementLiveIcon,
		ElementID:   1,
		CreateTime:  10,
		ExpireTime:  2999999999,
		Sort:        1,
		Attribute: liverecommendedelements.Attribute{
			URL:       "http://show.png",
			StartTime: &startTime,
		},
	}
	require.NoError(service.DB.Save(&testData).Error)

	param := openListParam{
		roomSimpleList: &roomSimpleList{Data: []*room.Simple{{RoomID: 1}, {RoomID: 2}}},
		iconURLs: [3]map[int64]string{
			nil,
			{2: "https://2.png"},
			{2: "https://1.png"},
		},
	}
	param.assignIconURL()
	assert.Equal(param.iconURLs[0], map[int64]string{
		testData.ElementID: testData.URL,
	})
	assert.Equal(testData.URL, param.Data[0].IconURL)
	assert.Equal("https://1.png", param.Data[1].IconURL)
}

func TestRecommendedSquareType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(liverecommendedelements.SquareTypeCatalog,
		recommendedSquareType(OpenListTypeHot, 10))
	assert.Equal(liverecommendedelements.SquareTypeHot,
		recommendedSquareType(OpenListTypeHot, 0))
	assert.Equal(liverecommendedelements.SquareTypeNova,
		recommendedSquareType(openListTypeNova, 0))
	assert.PanicsWithValue("unsupported listType: 1",
		func() { recommendedSquareType(openListTypeNew, 0) })
}

func TestOpenListParam_handleNonFirstPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理缓存环境
	service.Cache10s.Flush()

	// 添加测试房间数据
	roomID := int64(65432)
	insertTestRoom(t, roomID, "测试非第一页的房间")

	// 创建测试参数
	param := &openListParam{
		p:        2,
		pageSize: 10,
		buvid:    "test_buvid",
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
		},
	}

	c := handler.NewTestContext("GET", "/open/list?p=2", false, nil)
	c.Equip().FromApp = true
	c.Equip().BUVID = "test_buvid"

	t.Run("正常情况下调用算法推荐", func(t *testing.T) {
		// 确保mock函数返回正确的值
		fetchTianmaRecommendFunc = mockTianmaRecommend

		// 清除兜底状态
		require.NoError(cleanFallbackState(param.buvid))

		// 调用测试函数
		err := param.handleNonFirstPage(c)
		require.NoError(err)

		// 验证结果，应该是算法推荐
		assert.True(param.isTianmaRecommend)
		assert.NotEmpty(param.Data)
	})

	t.Run("兜底状态下加载本地数据", func(t *testing.T) {
		// 设置兜底状态
		err := setFallbackState(param.buvid, 10*time.Minute)
		require.NoError(err)

		// 调用测试函数
		err = param.handleNonFirstPage(c)
		require.NoError(err)

		// 验证结果，应该不是算法推荐，并从本地加载
		assert.False(param.isTianmaRecommend)
		assert.NotEmpty(param.Data)

		// 清除兜底状态
		require.NoError(cleanFallbackState(param.buvid))
	})

	t.Run("算法推荐失败时设置兜底并加载本地数据", func(t *testing.T) {
		// 修改mock函数返回错误
		fetchTianmaRecommendFunc = func(c *handler.Context, param *openListParam) (*tianma.RecommendResult, error) {
			return nil, errors.New("mock tianma error")
		}

		// 清除兜底状态
		require.NoError(cleanFallbackState(param.buvid))

		// 调用测试函数
		err := param.handleNonFirstPage(c)
		require.Error(err)
		assert.Contains(err.Error(), "mock tianma error")

		// 验证兜底状态已设置
		inFallback, err := isInFallbackState(param.buvid)
		require.NoError(err)
		assert.True(inFallback)

		// 恢复mock函数
		fetchTianmaRecommendFunc = mockTianmaRecommend

		// 清理兜底状态
		require.NoError(cleanFallbackState(param.buvid))
	})
}

func TestOpenListParam_handleFallback(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理缓存环境
	service.Cache10s.Flush()

	// 添加测试房间数据
	roomID := int64(65432)
	insertTestRoom(t, roomID, "测试兜底逻辑房间")

	// 创建测试参数
	param := &openListParam{
		p:        1,
		pageSize: 10,
		buvid:    "test_buvid_fallback",
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
		},
	}

	c := handler.NewTestContext("GET", "/open/list?p=1", false, nil)
	c.Equip().FromApp = true
	c.Equip().BUVID = "test_buvid_fallback"

	// 清除兜底状态确保测试前的干净环境
	require.NoError(cleanFallbackState(param.buvid))

	// 调用测试函数
	resp, err := param.handleFallback(c)
	require.NoError(err)
	require.NotNil(resp)

	// 验证结果
	assert.NotNil(resp.Data)
	assert.False(param.isTianmaRecommend)

	// 验证兜底状态已设置
	inFallback, err := isInFallbackState(param.buvid)
	require.NoError(err)
	assert.True(inFallback)

	// 清理兜底状态
	require.NoError(cleanFallbackState(param.buvid))
}

func TestIntegratedAppRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 保存原函数并在测试结束后恢复
	originalFunc := fetchTianmaRecommendFunc
	defer func() { fetchTianmaRecommendFunc = originalFunc }()

	// 清理缓存环境
	service.Cache10s.Flush()

	// 添加测试房间
	roomID := int64(76543)
	insertTestRoom(t, roomID, "测试完整APP请求")

	c := handler.NewTestContext("GET", "/open/list?p=1", false, nil)
	c.Equip().FromApp = true
	c.Equip().BUVID = "test_buvid_integrated"

	t.Run("第一页正常请求", func(t *testing.T) {
		fetchTianmaRecommendFunc = mockTianmaRecommend
		require.NoError(cleanFallbackState("test_buvid_integrated"))

		resp, err := ActionOpenList(c)
		require.NoError(err)
		assert.NotNil(resp)
		openResp := resp.(*openListResponse)
		assert.Equal(int64(1), openResp.Pagination.P)
		assert.NotEmpty(openResp.Data)
	})

	t.Run("算法推荐失败时走兜底", func(t *testing.T) {
		fetchTianmaRecommendFunc = func(c *handler.Context, param *openListParam) (*tianma.RecommendResult, error) {
			return nil, errors.New("mock tianma error")
		}
		require.NoError(cleanFallbackState("test_buvid_integrated"))

		resp, err := ActionOpenList(c)
		require.NoError(err)
		assert.NotNil(resp)
		openResp := resp.(*openListResponse)
		assert.Equal(int64(1), openResp.Pagination.P)
		assert.NotEmpty(openResp.Data)

		// 验证兜底状态已设置
		inFallback, err := isInFallbackState("test_buvid_integrated")
		require.NoError(err)
		assert.True(inFallback)
	})

	t.Run("兜底状态下请求第二页", func(t *testing.T) {
		// 恢复正常mock函数但保持兜底状态
		fetchTianmaRecommendFunc = mockTianmaRecommend
		require.NoError(setFallbackState("test_buvid_integrated", 10*time.Minute))

		c2 := handler.NewTestContext("GET", "/open/list?p=2", false, nil)
		c2.Equip().FromApp = true
		c2.Equip().BUVID = "test_buvid_integrated"

		resp, err := ActionOpenList(c2)
		require.NoError(err)
		assert.NotNil(resp)
		openResp := resp.(*openListResponse)
		assert.Equal(int64(2), openResp.Pagination.P)
		assert.NotEmpty(openResp.Data)
	})
	// 清理兜底状态
	require.NoError(cleanFallbackState("test_buvid_integrated"))
}
