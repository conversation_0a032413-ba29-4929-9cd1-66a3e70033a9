package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionPKSettingsGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(22489473)
	url := fmt.Sprintf("/api/v2/chatroom/pk/settings/get?room_id=%d", testRoomID)

	ctx, cancle := service.MongoDB.Context()
	defer cancle()
	_, err := livemeta.Collection().UpdateOne(ctx, bson.M{
		"room_id": testRoomID,
	}, bson.M{
		"$unset": bson.M{
			"pk_settings": "",
		},
	})
	require.NoError(err)
	c := handler.NewTestContext(http.MethodGet, url, true, nil)
	c.User().ID = 10
	resp, err := ActionPKSettingsGet(c)
	require.NoError(err)
	require.NotNil(resp)
	require.IsType(&livemeta.PKSettings{}, resp)
	settings := resp.(*livemeta.PKSettings)
	assert.Zero(settings.DisableInvite)
	assert.Zero(settings.UnfollowedInvite)

	result, err := livemeta.Collection().UpdateOne(ctx, bson.M{
		"room_id": testRoomID,
	}, bson.M{
		"$set": bson.M{
			"pk_settings": &livemeta.PKSettings{DisableInvite: 1, UnfollowedInvite: 1},
		},
	})
	require.NoError(err)
	require.EqualValues(1, result.ModifiedCount)
	c = handler.NewTestContext(http.MethodGet, url, true, nil)
	c.User().ID = 10
	resp, err = ActionPKSettingsGet(c)
	require.NoError(err)
	require.NotNil(resp)
	require.IsType(&livemeta.PKSettings{}, resp)
	settings = resp.(*livemeta.PKSettings)
	assert.Equal(1, settings.DisableInvite)
	assert.Equal(1, settings.UnfollowedInvite)

	c = handler.NewTestContext(http.MethodGet, url, true, nil)
	_, err = ActionPKSettingsGet(c)
	assert.EqualError(err, "您无权执行该操作")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/pk/settings/get", true, nil)
	c.User().ID = 10
	_, err = ActionPKSettingsGet(c)
	assert.EqualError(err, "参数错误")
}

func TestActionPKSettingsSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(22489473)

	ctx, cancle := service.MongoDB.Context()
	defer cancle()
	_, err := livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID}, bson.M{"$unset": bson.M{"pk_settings": ""}})
	require.NoError(err)

	param := map[string]interface{}{
		"room_id": testRoomID,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/settings/set", true, param)
	_, err = ActionPKSettingsSet(c)
	assert.EqualError(err, "参数错误")

	param = map[string]interface{}{
		"room_id":        testRoomID,
		"disable_invite": 1,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/settings/set", true, param)
	_, err = ActionPKSettingsSet(c)
	assert.EqualError(err, "您无权执行该操作")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/settings/set", true, param)
	c.User().ID = 10
	resp, err := ActionPKSettingsSet(c)
	require.NoError(err)
	require.NotNil(resp)
	require.IsType(&livemeta.PKSettings{}, resp)
	settings := resp.(*livemeta.PKSettings)
	assert.Equal(1, settings.DisableInvite)
	assert.Zero(settings.UnfollowedInvite)

	param = map[string]interface{}{
		"room_id":           testRoomID,
		"unfollowed_invite": 1,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/settings/set", true, param)
	c.User().ID = 10
	_, err = ActionPKSettingsSet(c)
	assert.EqualError(err, "已设置不接受 PK 邀请，无法设置接受未关注主播的 PK 邀请")

	_, err = livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"pk_settings.disable_invite": 0}},
	)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/settings/set", true, param)
	c.User().ID = 10
	resp, err = ActionPKSettingsSet(c)
	require.NoError(err)
	require.NotNil(resp)
	require.IsType(&livemeta.PKSettings{}, resp)
	settings = resp.(*livemeta.PKSettings)
	assert.Zero(settings.DisableInvite)
	assert.Equal(1, settings.UnfollowedInvite)
}
