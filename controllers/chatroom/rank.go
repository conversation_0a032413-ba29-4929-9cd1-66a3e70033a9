package chatroom

import (
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	rankTypeTotal = iota // 总榜
)

type rankParam struct {
	C        *handler.Context
	p        int64
	pageSize int64
	roomID   int64
	rankType int

	*rankResp
	user      *user.User
	creatorID int64
}

type rankResp struct {
	Data       []*liverevenues.RankRevenue `json:"Datas"`
	MyRank     *liverevenues.RankRevenue   `json:"myrank,omitempty"`
	Pagination goutil.Pagination           `json:"pagination"`
}

type rankResp2 struct {
	Data        []*roomsrank.Info `json:"Datas"`
	MyRank      *roomsrank.Info   `json:"myrank,omitempty"`
	Refresh     int64             `json:"refresh"`
	RoomRevenue *int64            `json:"room_revenue,omitempty"`
}

// ActionRank api/v2/chatroom/rank/:roomID
/**
 * @api {get} /api/v2/chatroom/rank/:roomID 贡献榜
 * @apiDescription 直播间贡献榜，榜单为第一页时会给出访问者的贡献排行
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} roomID 房间号
 * @apiParam {Number} type 榜单类型，0: 总榜，1: 本场榜，2: 周榜
 * @apiParam {Number} [p=1] 页码，仅总榜使用
 * @apiParam {Number} [pagesize=20] 一页显示数目，仅总榜使用
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "myrank": {
 *         "rank": 1,
 *         "revenue": 1,
 *         "user_id": 12345,
 *         "username": "1234",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *         "rank_invisible": true
 *       },
 *       "Datas": [{
 *         "rank": 1,
 *         "revenue": 10,
 *         "user_id": 12345,
 *         "username": "1234",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *         "rank_invisible": true
 *       },{
 *         "rank": 2,
 *         "revenue": 9,
 *         "user_id": 0,
 *         "username": "神秘人",
 *         "iconurl": "http://static.missevan.com/avatars/invisible.png",
 *         "rank_invisible": true
 *       },{
 *         "rank": 3,
 *         "revenue": 9,
 *         "user_id": 12345,
 *         "username": "1234",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *         "rank_invisible": false
 *       }],
 *       "refresh": 1000,
 *       "room_revenue": 1200 // 本直播间总收益，仅本场榜可能返回，收到此字段后更新显示的房间总收益
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRank(c *handler.Context) (handler.ActionResponse, error) {
	rankType, _ := c.GetParamInt("type")
	// TODO: 后面整合一下
	if rankType == rankTypeTotal {
		return actionTotalRank(c)
	}
	if rankType != roomsrank.RankTypeCurrent && rankType != roomsrank.RankTypeWeek {
		return nil, actionerrors.ErrParams
	}
	roomIDStr := c.C.Param("roomID")
	roomID, err := strconv.ParseInt(roomIDStr, 10, 64)
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}

	opt := &room.FindOptions{DisableAll: true}
	switch rankType {
	case roomsrank.RankTypeCurrent:
		// 本场榜可能需要返回房间总收益
		opt.Projection = bson.M{"creator_id": 1, "config": 1, "statistics.revenue": 1}
	case roomsrank.RankTypeWeek:
		opt.Projection = bson.M{"creator_id": 1}
	}
	r, err := room.Find(roomID, opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	u := c.User()
	var resp rankResp2
	if u != nil {
		resp.MyRank = &roomsrank.Info{ID: u.ID}
	}
	now := goutil.TimeNow()
	resp.Data, err = roomsrank.Find(roomID, rankType, now, resp.MyRank)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 榜外排名置 0
	if resp.MyRank != nil && resp.MyRank.Rank > roomsrank.RankLen(rankType) {
		resp.MyRank.Rank = 0
	}
	switch rankType {
	case roomsrank.RankTypeWeek:
		deadline := usersrank.Deadline(rankType, now)
		// WORKAROUND: 多个 3 秒的误差
		resp.Refresh = int64(deadline.Sub(now).Seconds()) + 3
	case roomsrank.RankTypeCurrent:
		if r.Config != nil && r.Config.RankSyncRevenue {
			resp.RoomRevenue = goutil.NewInt64(r.Statistics.Revenue)
		}
	}
	e := c.Equip()
	hasUserID := rankType == roomsrank.RankTypeCurrent && e != nil && (e.OS == goutil.Android || e.OS == goutil.IOS)
	checkRankInvisible(r.CreatorID, c.UserID(), hasUserID, nil, resp.Data, nil)
	return resp, nil
}

func actionTotalRank(c *handler.Context) (handler.ActionResponse, error) {
	param := rankParam{C: c}
	err := param.load()
	if err != nil {
		return nil, err
	}
	return param.buildTotalRank()
}

func checkRankInvisible(creatorID, userID int64, hasUserID bool,
	data1 []*liverevenues.RankRevenue,
	data2 []*roomsrank.Info,
	data3 []*fansRankElem) {
	if creatorID == userID {
		// 房主不做处理
		return
	}
	iconURL := service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)
	for i := range data1 {
		if data1[i].RankInvisible && data1[i].UserID() != userID {
			// 真爱榜总榜
			data1[i].Username = "神秘人"
			data1[i].Titles = nil
			if !hasUserID {
				data1[i].UID = 0
			}
			data1[i].IconURL = iconURL
			data1[i].Contribution = 0
		}
	}
	for i := range data2 {
		if data2[i].RankInvisible && data2[i].UserID() != userID {
			// 真爱榜本场榜、周榜
			data2[i].Username = "神秘人"
			data2[i].Titles = nil
			if !hasUserID {
				data2[i].UID = 0
			}
			data2[i].IconURL = iconURL
			data2[i].Contribution = 0
		}
	}
	for i := range data3 {
		if data3[i].RankInvisible && data3[i].UserID != userID {
			// 粉丝榜
			data3[i].Username = "神秘人"
			if !hasUserID {
				data3[i].UserID = 0
			}
			data3[i].Titles = nil
			data3[i].IconURL = iconURL
		}
	}
}

func (param *rankParam) load() (err error) {
	param.p, param.pageSize, err = param.C.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	roomIDStr := param.C.C.Param("roomID")
	param.roomID, err = strconv.ParseInt(roomIDStr, 10, 64)
	if err != nil || param.roomID <= 0 {
		return actionerrors.ErrCannotFindRoom
	}
	param.creatorID, err = room.FindCreatorID(param.roomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.creatorID == 0 {
		return actionerrors.ErrCannotFindRoom
	}
	param.rankType = rankTypeTotal // 现在只有总榜
	param.user = param.C.User()
	return nil
}

func (param *rankParam) buildTotalRank() (*rankResp, error) {
	param.rankResp = new(rankResp)
	defer param.removeNilData()
	// 用户自己的，只有第一页的时候会返回
	// NOTICE: 现在只有主播返回正确结果，所以总是没有数据
	if param.user != nil && param.p == 1 {
		// param.MyRank, err = liverevenues.FindRankByUser(param.roomID, param.user.ID)
		param.rankResp.MyRank = &liverevenues.RankRevenue{Simple: &liveuser.Simple{
			UID:      param.user.ID,
			Username: param.user.Username,
			IconURL:  param.user.IconURL,
		}}
	}
	if param.user == nil || param.user.ID != param.creatorID {
		// 总榜只有主播自己返回正确数据
		param.rankResp.Pagination = goutil.MakePagination(0, param.p, param.pageSize)
		param.rankResp.Data = make([]*liverevenues.RankRevenue, 0)
		return param.rankResp, nil
	}
	count, err := liverevenues.Count(param.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	limit := roomsrank.RankLen(roomsrank.RankTypeTotal)
	if count > limit {
		count = limit
	}
	param.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	if param.Pagination.Valid() {
		param.Data, err = liverevenues.FindRankByPage(param.roomID, param.Pagination)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	invisibleUsers := userstatus.RankInvisibleUsers(param.roomID)
	if param.user != nil {
		// 同步用户自己的榜单隐身状态
		for i := range param.Data {
			if param.Data[i].ID == param.user.ID {
				if param.MyRank != nil {
					param.Data[i].RankInvisible = param.MyRank.RankInvisible
				} else {
					_, param.Data[i].RankInvisible = invisibleUsers[param.Data[i].ID]
				}
				break
			}
		}
	}
	checkRankInvisible(param.creatorID, param.C.UserID(), false, param.rankResp.Data, nil, nil)
	return param.rankResp, nil
}

func (param *rankParam) removeNilData() {
	if param.rankResp.Data == nil {
		param.Data = make([]*liverevenues.RankRevenue, 0)
	}
}
