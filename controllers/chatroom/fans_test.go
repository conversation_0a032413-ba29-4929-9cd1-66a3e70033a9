package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFansTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(fansRankResp{}, "data", "my_medal", "rule", "fans_count", "medal_limit", "gift", "medal_gift", "tasks",
		"normal_privilege", "super_privilege")
	kc.Check(fansRankElem{}, "rank", "titles")
	kc.Check(myMedal{}, "rank_up", "rank", "medal_full", "max_level")
	kc.Check(obtainMedalGift{}, "gift_id", "name", "price", "icon_url")
	kc.Check(fansProgressResp{}, "rule", "revenue", "threshold", "medal", "contact")
	kc.Check(fansProgressResp{}.Contact, "qq_group")
}

func TestNewFansRankParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c := handler.CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "/fans/rank?room_id=-123&type=1", nil)
	_, err := newFansRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/fans/rank?room_id=22489473&type=1", nil)
	param, err := newFansRankParam(c)
	require.NoError(err)
	assert.True(param.isTop3)
	assert.NotNil(param.user)
	assert.NotNil(param.Data)
}

func TestFansRankFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := fansRankParam{isTop3: true, roomID: 22489473}
	assert.NoError(param.findRank())
	require.NotEmpty(param.Data)
	for i := range param.Data {
		assert.Equal(int64(i+1), param.Data[i].Rank)
	}

	param = fansRankParam{isTop3: true, roomID: 1234567}
	assert.NoError(param.findRank())
	found := false
	for _, title := range param.Data[0].Titles {
		if title.Type == liveuser.TitleTypeMedal {
			found = true
			break
		}
	}
	assert.True(found)
}

func TestFansRankFindMyMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	s := &livemedal.Simple{RoomID: 22489473, CreatorID: 10, UserID: 12, Status: livemedal.StatusOwned, Point: 200}
	err := collection.FindOneAndUpdate(ctx, bson.M{"room_id": 22489473, "user_id": 12},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && !mongodb.IsNoDocumentsError(err), err)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param := fansRankParam{c: c}
	param.Data = make([]*fansRankElem, 3)
	for i := 0; i < len(param.Data); i++ {
		param.Data[i] = &fansRankElem{Simple: &livemedal.Simple{Point: 1000}}
	}
	param.user = new(user.User)
	param.user.ID = 12
	param.roomID = 22489473
	param.creatorID = 10
	param.findMyMedal()
	require.NotNil(param.MyMedal.LiveMedal)
	assert.Equal(livemedal.UserMedalLevelLimit(), param.MyMedal.MaxLevel)
	assert.GreaterOrEqual(int64(801), param.MyMedal.RankUp)
	param.Data[1].UserID = 12
	param.Data[0].Point = 300
	param.findMyMedal()
	assert.Equal(int64(101), param.MyMedal.RankUp)
	assert.Equal(2, param.MyMedal.Rank)
	param.user.ID = 100
	param.findMyMedal()
	assert.Equal(int64(60), param.MyMedal.RankUp)
	param.user.ID = 10
	param.MyMedal = nil
	param.findMyMedal()
	assert.Nil(param.MyMedal)

	collection = livemedal.Collection()
	s = &livemedal.Simple{RoomID: 1234567, CreatorID: 10, UserID: 3456835, Status: livemedal.StatusShow, Point: 200}
	err = collection.FindOneAndUpdate(ctx, bson.M{"room_id": 1234567, "user_id": 3456835},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && err != mongo.ErrNoDocuments, err)
	param.user.ID = 3456835
	param.roomID = 1234567
	param.findMyMedal()
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level02_0_9_0_54.png", param.MyMedal.FrameURL)
}

func TestFansRankTrimRedundancy(t *testing.T) {
	assert := assert.New(t)
	param := fansRankParam{isTop3: true}
	param.trimRedundancy()
	param.Data = make([]*fansRankElem, 5)
	param.trimRedundancy()
	assert.Len(param.Data, 3)
}

func TestFansRankCheckRankInvisible(t *testing.T) {
	assert := assert.New(t)

	param := fansRankParam{user: new(user.User)}
	param.user.ID = 10
	assert.NotPanics(func() { param.checkRankInvisible() })
	param.Data = []*fansRankElem{{
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        10,
		},
	}, {
		Simple: &livemedal.Simple{
			RankInvisible: true,
			CreatorID:     123,
			UserID:        11,
		},
	},
	}
	param.checkRankInvisible()
	d := param.Data
	assert.Equal([]string{"", "神秘人"}, []string{d[0].Username, d[1].Username})
}

func TestActionFansRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/fans/rank?room_id=22489473&type=1", true, nil)
	r, err := ActionFansRank(c)
	require.NoError(err)
	resp := r.(fansRankResp)
	assert.NotNil(resp.MyMedal)
	assert.NotEmpty(resp.Data)
	tutil.PrintJSON(r)
}

func TestFansRankParam_BuildObtainMedalGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	creatorID := int64(20230217)
	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			CreatorID: creatorID,
			UserID:    creatorID,
			Status:    livemedal.StatusOwned,
		},
	}
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": creatorID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, lm)
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	p := fansRankParam{
		c:         c,
		creatorID: creatorID,
		user: &user.User{
			IUser: user.IUser{
				ID: creatorID,
			},
		},
	}
	key := keys.KeyRoomMedalPointMulti.Format(p.creatorID)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "element_type = ?",
		liverecommendedelements.ElementMultiMedalPoint).Error)
	require.NoError(service.LRURedis.Del(key).Err())
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalGiftID, p.Gift.GiftID)

	now := goutil.TimeNow()
	err = liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{p.creatorID}, now, now.Add(5*time.Second), liverecommendedelements.PointMulti{
		PointMultiAdd: 1,
	})
	require.NoError(err)
	require.NoError(service.LRURedis.Del(key).Err())
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalHalfPriceGiftID, p.Gift.GiftID)

	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": p.c.UserID()})
	require.NoError(err)
	p.buildObtainMedalGift()
	require.NotNil(p.Gift)
	assert.Equal(livemedal.ObtainMedalDiscountGiftID, p.Gift.GiftID)
}

func TestActionFansTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := fansRankParam{
		roomID: 20230222,
		user: &user.User{IUser: user.IUser{
			ID: 2,
		}},
		isTop3: true,
		fansRankResp: fansRankResp{
			MyMedal: &myMedal{
				LiveMedal: &livemedal.LiveMedal{
					Simple: livemedal.Simple{Status: livemedal.StatusOwned},
				},
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveuser.LiveUsersCollection().DeleteOne(ctx, bson.M{"room_id": param.roomID, "user_id": param.user.ID})
	require.NoError(err)
	_, err = livegifts.Collection().DeleteMany(ctx, bson.M{"room_id": param.roomID, "user_id": param.user.ID,
		"gift_id": useritems.GiftIDCatFood})
	require.NoError(err)

	param.buildMedalTasks()
	require.Len(param.Tasks, 6)
	for i := range param.Tasks {
		assert.Zero(param.Tasks[i].Status, param.Tasks[i].Name)
	}

	now := goutil.TimeNow()
	_, err = liveuser.LiveUsersCollection().InsertOne(ctx,
		liveuser.LiveUser{
			UserID:          param.user.ID,
			RoomID:          param.roomID,
			TodayTimeOnline: now,
			TodayAcqOnline:  30 * 1000 * util.SecondOneMinute,
			StatusShare:     1,
			TimeShare:       now,
		})
	require.NoError(err)
	param.buildMedalTasks()
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[1].Status, param.Tasks[1].Name)
	assert.NotZero(param.Tasks[2].Status, param.Tasks[2].Name)
	assert.NotZero(param.Tasks[3].Status, param.Tasks[3].Name)
	assert.Zero(param.Tasks[4].Status, param.Tasks[4].Name)
	assert.Zero(param.Tasks[5].Status, param.Tasks[5].Name)

	param.MyMedal.SuperFan = &livemedal.SuperFan{
		ExpireTime: now.Unix() + 5,
	}
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[5].Status, param.Tasks[5].Name)

	_, err = livegifts.Collection().InsertOne(ctx,
		livegifts.LiveGift{
			UserID:   param.user.ID,
			RoomID:   param.roomID,
			GiftID:   useritems.GiftIDCatFood,
			SentTime: now,
		})
	require.NoError(err)
	param.MyMedal.CreatedTime = now.Add(-10 * time.Second)
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.NotZero(param.Tasks[0].Status, param.Tasks[0].Name)

	param.MyMedal.CreatedTime = now.Add(10 * time.Second)
	param.buildMedalTasks()
	require.NoError(err)
	require.Len(param.Tasks, 6)
	assert.Zero(param.Tasks[0].Status, param.Tasks[0].Name)
}

func TestActionFansProgress(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/fans/progress?room_id=22489473", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.3.6"
	r, err := ActionFansProgress(c)
	require.NoError(err)
	resp := r.(*fansProgressResp)
	assert.NotNil(resp.Medal)
	assert.Equal(int64(30000), resp.Threshold)
	assert.LessOrEqual(resp.Revenue, resp.Threshold)
	assert.Equal(liveQQGroup, resp.Contact.QQGroup)

	c.C.Request, _ = http.NewRequest("GET", "/fans/progress?creator_id=10", nil)
	r, err = ActionFansProgress(c)
	require.NoError(err)
	resp = r.(*fansProgressResp)
	assert.NotNil(resp.Medal)
	assert.Equal(int64(30000), resp.Threshold)
	assert.LessOrEqual(resp.Revenue, resp.Threshold)
	assert.Equal(liveQQGroup, resp.Contact.QQGroup)
}
