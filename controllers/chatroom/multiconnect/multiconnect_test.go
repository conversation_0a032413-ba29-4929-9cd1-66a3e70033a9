package multiconnect

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestHandler(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	handler := Handler()
	assert.Equal(t, "multi-connect", handler.Name)
	kc.Check(handler, "application/request", "application/cancel", "application/accept", "application/refuse",
		"invitation/request", "invitation/cancel", "invitation/accept", "invitation/refuse",
		"kickout", "quit",
		"mute", "unmute",
		"mic-off", "mic-on",
		"settings/get", "settings/set",
		"block-list/get", "block-list/remove", "block-list/add",
		"score/clear",
		"record-list", "recommend-list", "recent-list")
}
