package multiconnect

import (
	"errors"
	"slices"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/connectcheck"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect/databus"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type invitationRequestParam struct {
	RoomID   int64 `form:"room_id" json:"room_id"`
	ToRoomID int64 `form:"to_room_id" json:"to_room_id"`
	GroupID  int64 `form:"group_id" json:"group_id"`

	fromRoom  *room.Room
	toRoom    *room.Room
	group     *livemulticonnect.Group
	match     *livemulticonnect.Match
	memberNum int
}

// ActionMultiConnectInvitationRequest 邀请连线请求
/**
 * @api {post} /api/v2/chatroom/multi-connect/invitation/request 邀请连线请求
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} to_room_id 被邀请的直播间 ID
 * @apiParam {Number} [group_id] 连线组 ID，正在连线时必传
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "multi_connect": {
 *         "group_id": 111, // 连线组 ID，正在连线时返回
 *         "match_id": 1, // 连线匹配 ID
 *         "remain_duration": 10000, // 邀请等待倒计时，单位：毫秒
 *         "create_time": 1584808200, // 单位秒
 *         "to_room": { // 被邀请方直播间信息
 *           "room_id": 654321,
 *           "name": "123456",
 *           "creator_id": 13,
 *           "creator_username": "test13",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *           "statistics": {
 *             "score": 100, // 直播间热度
 *             "attention_count": 100 // 关注数
 *           }
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主麦邀请主播连线请求（仅被邀请主播接受消息）
 *   {
 *     "type": "multi_connect",
 *     "event": "invite_request",
 *     "room_id": 1,
 *     "user_id": 1,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID，正在连线时返回
 *       "match_id": 1, // 连线匹配 ID
 *       "remain_duration": 10000, // 邀请等待倒计时，单位：毫秒
 *       "create_time": 1584808200, // 单位秒
 *       "recent_connected": true, // 是否在最近 7 天连线过
 *       "member_num": 2, // 当前正在连线的人数，若为 0 则为首次连线
 *       "from_room": { // 邀请发起方（主麦）直播间信息
 *         "room_id": 123456,
 *         "name": "123456",
 *         "creator_id": 13,
 *         "creator_username": "test13",
 *         "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *         "status": {
 *           "attention": true // 是否关注主麦
 *         },
 *         "statistics": {
 *           "score": 100, // 直播间热度
 *           "attention_count": 100 // 关注数
 *         }
 *       }
 *     }
 *   }
 */
func ActionMultiConnectInvitationRequest(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newMultiConnectInvitationRequestParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.addConnectMatch()
	if err != nil {
		return nil, "", err
	}
	param.sendTimeoutQueue()
	param.sendMessage()
	return param.resp(), "", nil
}

func newMultiConnectInvitationRequestParam(c *handler.Context) (*invitationRequestParam, error) {
	param := new(invitationRequestParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.ToRoomID <= 0 || param.GroupID < 0 {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID == param.ToRoomID {
		return nil, actionerrors.NewErrForbidden("无法对自己操作")
	}

	param.fromRoom, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true, FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.fromRoom == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if c.UserID() != param.fromRoom.CreatorID {
		return nil, actionerrors.NewErrForbidden("无法替其他主播发起邀请")
	}

	param.toRoom, err = room.Find(param.ToRoomID, &room.FindOptions{FindCreator: true, FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.toRoom == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if !param.toRoom.IsOpen() {
		return nil, actionerrors.NewErrForbidden("主播不在线，无法操作")
	}
	// 发起方没有在连线中发起邀请时，没有 group_id
	if param.GroupID == 0 {
		return param, nil
	}

	// 检查连线组是否存在
	param.group, err = livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return nil, actionerrors.ErrMultiConnectGroupNotFound
	}

	param.memberNum, err = param.group.OngoingMembersCount(livemulticonnect.DB())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group.IsLimit(param.memberNum) {
		return nil, actionerrors.ErrMultiConnectIsLimit
	}

	return param, nil
}

func (param *invitationRequestParam) check() error {
	matchList, err := livemulticonnect.FindPendingMatches([]int64{param.fromRoom.RoomID, param.toRoom.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, match := range matchList {
		if match.FromRoomID == param.fromRoom.RoomID && match.Type == livemulticonnect.MatchTypeApply {
			return actionerrors.NewErrForbidden("已申请加入其他连线，无法操作")
		}
		if match.ToRoomID == param.fromRoom.RoomID && match.Type == livemulticonnect.MatchTypeInvite {
			return actionerrors.NewErrForbidden("有未处理的连线邀请，无法操作")
		}
		if match.ToRoomID == param.fromRoom.RoomID && match.Type == livemulticonnect.MatchTypeApply {
			return actionerrors.NewErrForbidden("有未处理的连线申请，无法操作")
		}
		// 对方在处理其他主播的连线邀请（包括他发给别人和别人发给他的）
		if (match.ToRoomID == param.toRoom.RoomID || match.FromRoomID == param.toRoom.RoomID) &&
			match.Type == livemulticonnect.MatchTypeInvite {
			return actionerrors.NewErrForbidden("对方处理其他邀请中，请稍后再试")
		}
		// 对方已向其他主播申请加入连线
		if match.FromRoomID == param.toRoom.RoomID && match.Type == livemulticonnect.MatchTypeApply {
			return actionerrors.NewErrForbidden("对方处理其他申请中，请稍后再试")
		}
	}

	blockedByRoom, whoBlockedRoom, err := livemulticonnect.BlockedRoomIDs(param.fromRoom.RoomID, param.toRoom.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if slices.Contains(blockedByRoom, param.toRoom.RoomID) {
		return actionerrors.NewErrForbidden("您已将该主播连线拉黑，无法操作")
	}
	if slices.Contains(whoBlockedRoom, param.fromRoom.RoomID) {
		return actionerrors.NewErrForbidden("由于对方设置，无法操作")
	}

	u1BlockU2, u2BlockU1, err := userapi.UserBlockStatus(param.fromRoom.CreatorID, param.toRoom.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if u1BlockU2 {
		return actionerrors.NewErrForbidden("您已拉黑对方，无法操作")
	}
	if u2BlockU1 {
		return actionerrors.NewErrForbidden("由于对方设置，无法操作")
	}

	settings, err := livemeta.FindMultiConnectSettings(param.toRoom.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if settings == nil {
		settings = new(livemeta.MultiConnectSettings)
	}
	if !settings.IsEnableInvite() {
		return actionerrors.NewErrForbidden("对方不允许连线邀请")
	}
	// 对方未关注我 && 对方未开启【接受未关注人的连线邀请】开关
	if !settings.IsEnableUnfollowedInvite() && !param.toRoom.Status.Attention {
		return actionerrors.NewErrForbidden("对方不允许未关注人的连线邀请")
	}

	err = connectcheck.NewMultiConnectComponent(param.fromRoom, param.toRoom).Check()
	if err != nil {
		return err
	}

	ongoingList, err := livemulticonnect.FindOngoingMembers([]int64{param.toRoom.RoomID, param.fromRoom.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(ongoingList) > 0 {
		ongoingMap := util.ToMap(ongoingList, func(member *livemulticonnect.GroupMember) int64 {
			return member.RoomID
		})
		_, toMemberOK := ongoingMap[param.toRoom.RoomID]
		_, fromMemberOK := ongoingMap[param.fromRoom.RoomID]
		if toMemberOK && fromMemberOK {
			return actionerrors.NewErrForbidden("对方正在主播连线中，无法操作")
		}
		if toMemberOK {
			return actionerrors.ErrMultiConnectInviteToApply
		}
	}

	return nil
}

func (param *invitationRequestParam) addConnectMatch() error {
	now := goutil.TimeNow()
	param.match = &livemulticonnect.Match{
		StartTime:  now.UnixMilli(),
		EndTime:    0,
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
		GroupID:    param.GroupID,
		FromRoomID: param.fromRoom.RoomID,
		ToRoomID:   param.toRoom.RoomID,
	}
	err := livemulticonnect.DB().Create(param.match).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *invitationRequestParam) sendTimeoutQueue() {
	databus.DelayMultiConnectMatchTimeout(param.match)
}

func (param *invitationRequestParam) sendMessage() {
	exists, err := livemulticonnect.IsConnected(param.fromRoom.RoomID, param.toRoom.RoomID, time.Hour*24*7)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	isAttention, err := attentionuser.HasFollowed(param.toRoom.CreatorID, param.fromRoom.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	msg := livemulticonnect.RequestBroadcastPayload{
		Type:   liveim.TypeMultiConnect,
		Event:  liveim.EventInviteRequest,
		RoomID: param.toRoom.RoomID,
		UserID: param.toRoom.CreatorID,
		MultiConnect: &livemulticonnect.RequestMultiConnect{
			GroupID:         param.GroupID,
			MatchID:         param.match.ID,
			RemainDuration:  databus.MatchTimeoutDuration.Milliseconds(),
			CreateTime:      param.match.CreateTime,
			RecentConnected: &exists,
			MemberNum:       &param.memberNum,
			FromRoom: &livemulticonnect.MultiConnectRoom{
				RoomID:          param.fromRoom.RoomID,
				Name:            param.fromRoom.Name,
				CreatorID:       param.fromRoom.CreatorID,
				CreatorUsername: param.fromRoom.CreatorUsername,
				CreatorIconURL:  &param.fromRoom.CreatorIconURL,
				Status: &livemulticonnect.MultiConnectRoomStatus{
					Attention: isAttention,
				},
				Statistics: &livemulticonnect.MultiConnectRoomStatistics{
					Score:          param.fromRoom.Statistics.Score,
					AttentionCount: param.fromRoom.Statistics.AttentionCount,
				},
			},
		},
	}
	err = userapi.BroadcastUser(param.toRoom.RoomID, param.toRoom.CreatorID, &msg)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *invitationRequestParam) resp() handler.M {
	return handler.M{
		"multi_connect": &livemulticonnect.RequestMultiConnect{
			GroupID:        param.GroupID,
			MatchID:        param.match.ID,
			RemainDuration: databus.MatchTimeoutDuration.Milliseconds(),
			CreateTime:     param.match.CreateTime,
			ToRoom: &livemulticonnect.MultiConnectRoom{
				RoomID:          param.toRoom.RoomID,
				Name:            param.toRoom.Name,
				CreatorID:       param.toRoom.CreatorID,
				CreatorUsername: param.toRoom.CreatorUsername,
				CreatorIconURL:  &param.toRoom.CreatorIconURL,
				Statistics: &livemulticonnect.MultiConnectRoomStatistics{
					Score:          param.toRoom.Statistics.Score,
					AttentionCount: param.toRoom.Statistics.AttentionCount,
				},
			},
		},
	}
}

type invitationCancelParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`
}

// ActionMultiConnectInvitationCancel 取消连线邀请（主麦）
/**
 * @api {post} /api/v2/chatroom/multi-connect/invitation/cancel 取消连线邀请（主麦）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 主麦直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 邀请双方消息
 *   {
 *     "type": "multi_connect",
 *     "event": "invite_cancel",
 *     "room_id": 1,
 *     "user_id": 1,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 发起方（主麦）直播间
 *         "room_id": 1,
 *         "creator_id": 1,
 *         "creator_username": "test1"
 *       },
 *       "to_room": { // 被邀请方直播间
 *         "room_id": 2,
 *         "creator_id": 1,
 *         "creator_username": "test1"
 *       }
 *     }
 *   }
 */
func ActionMultiConnectInvitationCancel(c *handler.Context) (handler.ActionResponse, string, error) {
	var param *invitationCancelParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.MatchID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	match, err := livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if match == nil {
		return nil, "", actionerrors.ErrMultiConnectMatchNotFound
	}
	if !match.IsPending() {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}
	// 校验是否是发起方
	if match.FromRoomID != param.RoomID {
		return nil, "", actionerrors.ErrForbidden
	}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": []int64{match.FromRoomID, match.ToRoomID}}}, nil)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != 2 {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	roomMap := util.ToMap(rooms, func(room *room.Room) int64 {
		return room.RoomID
	})
	if !roomMap[match.FromRoomID].IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	ok, err := match.Cancel()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}

	err = userapi.BroadcastMany(livemulticonnect.NewUnacceptBroadcastPayloadElems(liveim.EventInviteCancel, match, roomMap))
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil, "success", nil
}

type invitationAcceptParams struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`

	userID     int64
	match      *livemulticonnect.Match
	fromMember *livemulticonnect.GroupMember
	group      *livemulticonnect.Group
}

type invitationAcceptResp struct {
	MultiConnect *livemulticonnect.MultiConnectInfo `json:"multi_connect"`
}

// ActionMultiConnectInvitationAccept 接受连线邀请
/**
 * @api {post} /api/v2/chatroom/multi-connect/invitation/accept 接受连线邀请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "multi_connect": {
 *         "group_id": 111, // 连线组 ID
 *         "status": 1, // 连线状态；1: 连线中；2: 已结束
 *         "join_index": 3, // 加入连线的麦序
 *         "duration": 10000, // 显示本房连线总时长，单位：毫秒
 *         "mute_room_ids": [3,12,223344], // 静音连线直播间 ID 列表
 *         "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *         "members": [ // 连线成员列表，首位总是当前麦位
 *           {
 *             "index": 1, // 连线序号
 *             "role": 1, // 角色；1: 主麦；2: 连线成员
 *             "score": 111, // 礼物积分
 *             "room": { // 房间信息
 *               "room_id": 152,
 *               "name": "123456",
 *               "creator_id": 12345,
 *               "creator_username": "1234",
 *               "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *             }
 *           },
 *           ...
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 所有连线的直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "invite_accept",
 *     "room_id": 10659544,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "status": 1, // 连线状态；1: 连线中；2: 已结束
 *       "join_index": 3, // 加入连线的麦序
 *       "duration": 10000, // 显示本房连线总时长，单位：毫秒
 *       "mute_room_ids": [3,12,223344], // 静音连线直播间 ID 列表
 *       "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *       "members": [ // 连线成员列表，首位总是当前麦位
 *         {
 *           "index": 1, // 连线序号
 *           "role": 1, // 角色；1: 主麦；2: 连线成员
 *           "score": 111, // 礼物积分
 *           "room": { // 房间信息
 *             "room_id": 152,
 *             "name": "123456",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *           }
 *         },
 *         ...
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectInvitationAccept(c *handler.Context) (handler.ActionResponse, string, error) {
	param := invitationAcceptParams{userID: c.UserID()}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.MatchID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.accept()
	if err != nil {
		return nil, "", err
	}
	roomConnectInfo, err := livemulticonnect.AcceptMatchBroadcastMany(liveim.EventInviteAccept, param.group, param.RoomID)
	if err != nil {
		return nil, "", err
	}
	return invitationAcceptResp{MultiConnect: roomConnectInfo}, "", nil
}

func (param *invitationAcceptParams) check() error {
	r, err := room.FindOne(bson.M{"creator_id": param.userID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.RoomID != param.RoomID || !r.IsOpen() {
		return actionerrors.ErrForbidden
	}
	param.match, err = livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.match == nil {
		return actionerrors.ErrMultiConnectMatchNotFound
	}
	if param.match.ToRoomID != param.RoomID || param.match.Type != livemulticonnect.MatchTypeInvite ||
		!param.match.IsPending() {
		return actionerrors.NewErrLiveForbidden("无效的连线匹配")
	}
	fromRoom, err := room.FindOne(bson.M{"room_id": param.match.FromRoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if fromRoom == nil || !fromRoom.IsOpen() {
		return actionerrors.NewErrLiveForbidden("当前邀请方直播间状态异常")
	}
	return nil
}

func (param *invitationAcceptParams) findFromMember() error {
	return servicedb.Tx(livemulticonnect.DB(), func(tx *gorm.DB) error {
		member, err := livemulticonnect.FindOngoingMemberByRoomID(tx, param.RoomID)
		if err != nil {
			return err
		}
		if member != nil {
			return actionerrors.NewErrMultiConnectGroupMemberModifiedMsg("当前已在连线中，无法操作")
		}
		param.fromMember, err = livemulticonnect.FindOngoingMemberByRoomID(tx, param.match.FromRoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	})
}

func (param *invitationAcceptParams) accept() error {
	key := keys.LockMultiConnectGroupCreate1.Format(param.match.FromRoomID)
	mutex := redismutex.New(service.Redis, key, 2*time.Second)
	if !mutex.TryLock() {
		return actionerrors.NewErrForbidden("当前连线组繁忙，请稍后再试")
	}
	defer mutex.Unlock()
	err := param.findFromMember()
	if err != nil {
		return err
	}
	if param.fromMember == nil {
		// 首个同意邀请需要创建 group
		param.group, err = param.match.InitGroup()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return nil
	}
	param.group, err = livemulticonnect.FindOngoingGroup(param.fromMember.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}
	err = param.match.JoinGroup(param.group)
	if err != nil {
		if errors.Is(err, livemulticonnect.ErrConnectIsLimit) {
			return actionerrors.ErrMultiConnectIsLimit
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type invitationRefuseParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`
}

// ActionMultiConnectInvitationRefuse 拒绝邀请连线请求
/**
 * @api {post} /api/v2/chatroom/multi-connect/invitation/refuse 拒绝邀请连线请求
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主动拒绝时，邀请发起方消息
 *   {
 *     "type": "multi_connect",
 *     "event": "invite_refuse",
 *     "room_id": 1,
 *     "user_id": 1,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 发起方（主麦）直播间
 *         "room_id": 1,
 *         "creator_id": 1,
 *         "creator_username": "test1"
 *       },
 *       "to_room": { // 被邀请方直播间
 *         "room_id": 12,
 *         "creator_id": 2,
 *         "creator_username": "test2"
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 自动超时双方消息
 *   {
 *     "type": "multi_connect",
 *     "event": "invite_timeout",
 *     "room_id": 12,
 *     "user_id": 2,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 发起方（主麦）直播间
 *         "room_id": 1,
 *         "creator_id": 1,
 *         "creator_username": "test1"
 *       },
 *       "to_room": { // 被邀请方直播间
 *         "room_id": 12,
 *         "creator_id": 2,
 *         "creator_username": "test2"
 *       }
 *     }
 *   }
 */
func ActionMultiConnectInvitationRefuse(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(invitationRefuseParam)
	err := c.Bind(param)
	if err != nil || param.MatchID <= 0 || param.RoomID <= 0 {
		return nil, "", actionerrors.ErrParams
	}

	match, err := livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if match == nil {
		return nil, "", actionerrors.ErrMultiConnectMatchNotFound
	}
	if match.ToRoomID != param.RoomID {
		return nil, "", actionerrors.NewErrForbidden("只能处理邀请自己的连线请求")
	}
	if !match.IsPending() {
		return nil, "", actionerrors.NewErrForbidden("连线请求已被处理")
	}

	toRoom, err := room.Find(match.ToRoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if toRoom == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if toRoom.CreatorID != c.UserID() {
		return nil, "", actionerrors.ErrParamsMsg("您不是该房间的主播，无法处理邀请")
	}
	fromRoom, err := room.Find(match.FromRoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if fromRoom == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}

	ok, err := match.Refuse()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}

	payload := livemulticonnect.NewUnacceptBroadcastPayload(liveim.EventInviteRefuse, match, map[int64]*room.Room{
		toRoom.RoomID:   toRoom,
		fromRoom.RoomID: fromRoom,
	})
	err = userapi.BroadcastUser(fromRoom.RoomID, fromRoom.CreatorID, payload)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "success", nil
}
