package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewRecommendListParam(t *testing.T) {
	assert := assert.New(t)

	// 用户与房间不匹配
	c := handler.NewTestContext(http.MethodGet, "?room_id=2233", true, nil)
	param, err := newRecommendListParam(c)
	assert.Nil(param)
	assert.Equal(actionerrors.ErrForbidden, err)

	// 用户与房间匹配
	c = handler.NewTestContext(http.MethodGet, "?room_id=18113499", true, nil)
	param, err = newRecommendListParam(c)
	assert.Nil(err)
	assert.NotNil(param)
}

func TestRecommendListParam_findMatchingRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(2233)
	matchRecords := []livemulticonnect.Match{
		{ID: 99, Type: livemulticonnect.MatchTypeApply, Status: livemulticonnect.MatchStatusPending, FromRoomID: roomID, ToRoomID: 100, BridgeRoomID: 99},
		{ID: 101, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending, FromRoomID: roomID, ToRoomID: 101},
		{ID: 102, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending, FromRoomID: roomID, ToRoomID: 102},
		{ID: 103, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending, FromRoomID: roomID, ToRoomID: 103},
	}
	err := livemulticonnect.DB().Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchRecords)
	require.NoError(err)
	p := &recommendListParam{
		room: &room.Room{Helper: room.Helper{RoomID: roomID}},
	}
	roomIDs, err := p.findMatchingRoomIDs()
	require.NoError(err)
	assert.EqualValues([]int64{103, 102, 101, 99}, roomIDs)

	p = &recommendListParam{
		room: &room.Room{Helper: room.Helper{RoomID: 123}},
	}
	roomIDs, err = p.findMatchingRoomIDs()
	require.NoError(err)
	assert.Empty(roomIDs)
}

func TestRecommendListParam_findRandomFollowedRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &recommendListParam{
		userID: 12,
	}
	followedRoomID, err := p.findRandomFollowedRoomID()
	require.NoError(err)
	assert.NotEmpty(followedRoomID)

	p = &recommendListParam{
		userID: 13,
	}
	followedRoomID, err = p.findRandomFollowedRoomID()
	require.NoError(err)
	assert.Empty(followedRoomID)
}

func TestRecommendListParam_findRandomOpeningRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{
		"status.open":          room.StatusOpenTrue,
		"status.multi_connect": bson.M{"$ne": room.MultiConnectStatusOngoing},
	}
	openingCount, err := room.CountByFilter(filter)
	require.NoError(err)

	expectedCount := min(openingCount/2, 20)
	p := &recommendListParam{}
	randomRooms, err := p.findRandomOpeningRooms()
	require.NoError(err)
	assert.Len(randomRooms, int(expectedCount))
}

func TestRecommendListParam_filterRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(18113499)
	roomIDs := make([]int64, 0, 6)
	// 听众连麦中的房间
	lc, err := liveconnect.FindOne(bson.M{"status": liveconnect.StatusJoined, "room_id": bson.M{"$ne": nil}})
	require.NoError(err)
	require.NotNil(lc)
	roomIDs = append(roomIDs, lc.RoomID)
	// PK 流程中的房间
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": 124})
	require.NoError(err)
	pool := &livepk.Pool{
		Type:       livepk.PKTypeInvitation,
		RoomID:     124,
		ToRoomID:   125,
		Status:     livepk.PKPoolStatusWaiting,
		CreateTime: 0,
	}
	_, err = livepk.PoolCollection().InsertOne(ctx, pool)
	require.NoError(err)
	roomIDs = append(roomIDs, pool.RoomID, pool.ToRoomID)
	// 连线拉黑的房间
	blockRecord := &livemulticonnect.Blocklist{
		RoomID:      roomID,
		BlockRoomID: 1,
	}
	err = livemulticonnect.DB().Delete(livemulticonnect.Blocklist{}).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(blockRecord).Error
	require.NoError(err)
	roomIDs = append(roomIDs, blockRecord.BlockRoomID)

	// 账号拉黑的房间，增加拉黑房间在结构体中的映射关系
	param := &recommendListParam{
		room: &room.Room{Helper: room.Helper{RoomID: roomID}},
	}
	param.roomMap = make(map[int64]*room.Simple)
	blockRoom := &room.Simple{RoomID: 2, CreatorID: 1234}
	param.roomMap[blockRoom.RoomID] = blockRoom
	roomIDs = append(roomIDs, blockRoom.RoomID)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatusList,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_user_list": []int64{blockRoom.CreatorID}, "user_block_list": []int64{}}, nil
		})
	defer cleanup()

	roomIDs = append(roomIDs, roomID)
	filteredRoomIDs, err := param.filterRoomIDs(roomIDs)
	require.NoError(err)
	// 验证除了当前房间，其他房间都被过滤
	assert.Equal(len(roomIDs)-1, len(filteredRoomIDs))
	assert.Equal(struct{}{}, filteredRoomIDs[roomID])
}

func TestActionMultiConnectRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(18113499)
	matchRecords := []livemulticonnect.Match{
		{Status: livemulticonnect.NotifyGroupJoinStatusPending, Type: livemulticonnect.MatchTypeApply, FromRoomID: roomID, ToRoomID: 868965282, BridgeRoomID: 868965282},
	}
	connectingMembers := []*livemulticonnect.GroupMember{
		{EndTime: 0, Status: livemulticonnect.MemberStatusOngoing, RoomID: 173076071, GroupID: 1},
		{EndTime: 0, Status: livemulticonnect.MemberStatusOngoing, RoomID: 114693474, GroupID: 1},
	}
	err := livemulticonnect.DB().Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchRecords)
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), connectingMembers)
	require.NoError(err)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatusList,
		func(input interface{}) (output interface{}, err error) {
			// 3013063 对应正在连线中的房间 114693474
			return handler.M{"block_user_list": []int64{3013063}, "user_block_list": []int64{}}, nil
		})
	defer cleanup()

	c := handler.NewTestContext(http.MethodGet, "?room_id=18113499", true, nil)
	resp, _, err := ActionMultiConnectRecommendList(c)
	require.NoError(err)
	r, ok := resp.(*recommendListResp)
	require.True(ok)
	// 1 个匹配中的主播和 10 个推荐主播
	require.Len(r.Data, 1+10)
	// 匹配中房间排在第一个
	assert.Equal(int64(868965282), r.Data[0].Room.RoomID)
	// 关注房间排在第二个
	assert.True(r.Data[1].Room.Status.Attention)
	// 当前房间不在主播连线中，正在主播连线中的主播排在第三个
	assert.Equal(livemulticonnect.NotifyGroupStatusOngoing, r.Data[2].MultiConnect.Status)
	// 第四个过滤掉了有拉黑关系的房间，开始展示随机的未在连线中的房间
	assert.Equal(livemulticonnect.NotifyGroupStatusFinish, r.Data[3].MultiConnect.Status)
}
