package multiconnect

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type muteParam struct {
	RoomID       int64 `form:"room_id" json:"room_id"`
	MuteRoomID   int64 `form:"mute_room_id" json:"mute_room_id"`
	UnmuteRoomID int64 `form:"unmute_room_id" json:"unmute_room_id"`
	GroupID      int64 `form:"group_id" json:"group_id"`

	mute      bool // true: 静音，false: 取消静音
	roomMap   map[int64]*room.Room
	group     *livemulticonnect.Group
	memberMap map[int64]*livemulticonnect.GroupMember
}

func newMuteParam(c *handler.Context, mute bool) (*muteParam, error) {
	param := &muteParam{
		mute: mute,
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.GroupID <= 0 || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	roomIDs := make([]int64, 0, 2)
	roomIDs = append(roomIDs, param.RoomID)
	if mute {
		if param.MuteRoomID <= 0 || param.MuteRoomID == param.RoomID {
			return nil, actionerrors.ErrParams
		}
		roomIDs = append(roomIDs, param.MuteRoomID)
	} else {
		if param.UnmuteRoomID <= 0 || param.UnmuteRoomID == param.RoomID {
			return nil, actionerrors.ErrParams
		}
		roomIDs = append(roomIDs, param.UnmuteRoomID)
	}
	rooms, err := room.List(
		bson.M{"room_id": bson.M{"$in": roomIDs}}, nil, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != 2 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.roomMap = util.ToMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})
	return param, nil
}

func (param *muteParam) check() (err error) {
	param.group, err = livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrNotFound("未查询到正在进行的连线组")
	}
	members, err := param.group.Members(livemulticonnect.DB())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(members) == 0 {
		return actionerrors.ErrNotFound("未查询到连线成员")
	}
	param.memberMap = util.ToMap(members, func(m *livemulticonnect.GroupMember) int64 {
		return m.RoomID
	})
	for roomID := range param.roomMap {
		if _, ok := param.memberMap[roomID]; !ok {
			return actionerrors.ErrNotFound("未查询到连线成员")
		}
	}
	return nil
}

func (param *muteParam) set() (handler.M, error) {
	var (
		event       string
		forcedEvent string
		index       int
		muteRoom    *room.Room
		curRoom     = param.roomMap[param.RoomID]
	)
	if param.mute {
		err := param.memberMap[param.RoomID].Mute(param.memberMap[param.MuteRoomID])
		if err != nil {
			if !servicedb.IsUniqueError(err) {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			// PASS: 忽略重复静音的场景
		}
		event = liveim.EventMute
		forcedEvent = liveim.EventForcedMute
		index = param.memberMap[param.MuteRoomID].Index
		muteRoom = param.roomMap[param.MuteRoomID]
	} else {
		err := param.memberMap[param.RoomID].Unmute(param.memberMap[param.UnmuteRoomID])
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		event = liveim.EventUnmute
		forcedEvent = liveim.EventForcedUnmute
		index = param.memberMap[param.UnmuteRoomID].Index
		muteRoom = param.roomMap[param.UnmuteRoomID]
	}

	muteRoomIDs, err := param.memberMap[param.RoomID].MutedRoomIDs()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	elems := []*userapi.BroadcastElem{
		// 操作静音的连线直播间消息
		{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: curRoom.RoomID,
			Payload: &livemulticonnect.MuteBroadcastPayload{
				Type:   liveim.TypeMultiConnect,
				Event:  event,
				RoomID: curRoom.RoomID,
				MultiConnect: &livemulticonnect.MuteMultiConnect{
					GroupID:     param.group.ID,
					MuteRoomIDs: muteRoomIDs,
					Member: livemulticonnect.MuteMultiConnectMember{
						Index: index,
						Room: &livemulticonnect.MultiConnectRoom{
							RoomID:          muteRoom.RoomID,
							Name:            muteRoom.Name,
							CreatorID:       muteRoom.CreatorID,
							CreatorUsername: muteRoom.CreatorUsername,
						},
					},
				},
			},
		},
		// 被静音的直播间消息
		{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: muteRoom.RoomID,
			Payload: &livemulticonnect.MuteBroadcastPayload{
				Type:   liveim.TypeMultiConnect,
				Event:  forcedEvent,
				RoomID: muteRoom.RoomID,
				MultiConnect: &livemulticonnect.MuteMultiConnect{
					GroupID: param.group.ID,
					Member: livemulticonnect.MuteMultiConnectMember{
						Room: &livemulticonnect.MultiConnectRoom{
							RoomID:          curRoom.RoomID,
							Name:            curRoom.Name,
							CreatorID:       curRoom.CreatorID,
							CreatorUsername: curRoom.CreatorUsername,
						},
					},
				},
			},
		},
	}
	err = userapi.BroadcastMany(elems)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return handler.M{"group_id": param.group.ID, "mute_room_ids": muteRoomIDs}, nil
}

// ActionMultiConnectMute 静音连线的成员
/**
 * @api {post} /api/v2/chatroom/multi-connect/mute 静音连线的成员
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} mute_room_id 被静音的房间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "group_id": 1, // 连线组 ID
 *       "mute_room_ids": [1,2,3] // 静音的连线直播间 ID 列表
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主播将连线成员静音（仅当前直播间下发）
 *   {
 *     "type": "multi_connect",
 *     "event": "mute",
 *     "room_id": 654321,
 *     "multi_connect": {
 *       "group_id": 1, // 连线组 ID
 *       "mute_room_ids": [3,12,123456], // 静音的连线直播间 ID 列表
 *       "member": { // 静音的成员信息
 *         "index": 1, // 连线序号
 *         "room": { // 静音的直播间信息
 *           "room_id": 123456,
 *           "name": "123456",
 *           "creator_id": 123456,
 *           "creator_username": "用户名"
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被静音的直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "forced_mute",
 *     "room_id": 123456,
 *     "multi_connect": {
 *       "group_id": 1, // 连线组 ID
 *       "member": {
 *         "room": { // 操作静音的直播间信息
 *           "room_id": 654321,
 *           "name": "654321",
 *           "creator_id": 654321,
 *           "creator_username": "用户名"
 *         }
 *       }
 *     }
 *   }
 */
func ActionMultiConnectMute(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newMuteParam(c, true)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.set()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

// ActionMultiConnectUnmute 取消静音连线的成员
/**
 * @api {post} /api/v2/chatroom/multi-connect/unmute 取消静音连线的成员
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} unmute_room_id 取消静音的房间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "group_id": 1, // 连线组 ID
 *       "mute_room_ids": [1,2,3] // 静音的连线直播间 ID 列表
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主播取消连线成员的静音（当前直播间下发）
 *   {
 *     "type": "multi_connect",
 *     "event": "unmute",
 *     "room_id": 654321,
 *     "multi_connect": {
 *       "group_id": 1, // 连线组 ID
 *       "mute_room_ids": [3,12], // 静音的连线直播间 ID 列表
 *       "member": { // 取消静音的成员信息
 *         "index": 1, // 连线序号
 *         "room": { // 取消静音的直播间信息
 *           "room_id": 123456,
 *           "name": "123456",
 *           "creator_id": 123456,
 *           "creator_username": "用户名"
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被取消静音的直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "forced_unmute",
 *     "room_id": 123456,
 *     "multi_connect": {
 *       "group_id": 1, // 连线组 ID
 *       "member": {
 *         "room": { // 操作取消静音的直播间信息
 *           "room_id": 654321,
 *           "name": "654321",
 *           "creator_id": 654321,
 *           "creator_username": "用户名"
 *         }
 *       }
 *     }
 *   }
 */
func ActionMultiConnectUnmute(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newMuteParam(c, false)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.set()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}
