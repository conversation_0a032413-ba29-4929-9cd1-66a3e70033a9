package multiconnect

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionMultiConnectSettingsGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.FindOne(bson.M{})
	require.NoError(err)
	require.NotNil(r)
	_, err = livemeta.Collection().UpdateOne(context.Background(),
		bson.M{"_room_id": r.<PERSON>, "room_id": r.<PERSON>},
		bson.M{"$set": bson.M{
			"multi_connect_settings.disable_invite":            0,
			"multi_connect_settings.disable_apply":             0,
			"multi_connect_settings.disable_unfollowed_invite": 1,
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("/get?room_id=%d", r.RoomID), true, nil)
	c.User().ID = r.CreatorID
	resp, _, err := ActionMultiConnectSettingsGet(c)
	require.NoError(err)
	require.NotNil(resp)
	settings, ok := resp.(*settingsResp)
	require.True(ok)
	assert.EqualValues(0, settings.DisableInvite)
	assert.EqualValues(0, settings.DisableApply)
	assert.EqualValues(1, settings.DisableUnfollowedInvite)
}

func TestActionMultiConnectSettingsSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.FindOne(bson.M{})
	require.NoError(err)
	require.NotNil(r)
	_, err = livemeta.Collection().UpdateOne(context.Background(),
		bson.M{"_room_id": r.OID, "room_id": r.RoomID},
		bson.M{"$set": bson.M{
			"multi_connect_settings.disable_invite":            1,
			"multi_connect_settings.disable_apply":             0,
			"multi_connect_settings.disable_unfollowed_invite": 1,
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/set", true, handler.M{
		"room_id":                   r.RoomID,
		"disable_unfollowed_invite": 0,
	})
	c.User().ID = r.CreatorID
	_, _, err = ActionMultiConnectSettingsSet(c)
	assert.Equal(actionerrors.NewErrForbidden("已禁止连线邀请，无法接受未关注人连线邀请"), err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":                   r.RoomID,
		"disable_invite":            0,
		"disable_unfollowed_invite": 1,
		"disable_apply":             1,
	})
	c.User().ID = r.CreatorID
	resp, _, err := ActionMultiConnectSettingsSet(c)
	require.NoError(err)
	require.NotNil(resp)
	settings, ok := resp.(*settingsResp)
	require.True(ok)
	assert.EqualValues(0, settings.DisableInvite)
	assert.EqualValues(1, settings.DisableApply)
	assert.EqualValues(0, settings.DisableUnfollowedInvite)
}
