package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionMultiConnectInvitationAccept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &invitationAcceptParams{
		RoomID:  -11,
		MatchID: 1,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	_, _, err := ActionMultiConnectInvitationAccept(c)
	assert.Equal(actionerrors.ErrParams, err)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"status.open": 1}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)
	m := &livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	g := &livemulticonnect.Group{
		ConnectID: "1",
	}
	require.NoError(livemulticonnect.DB().Create(&g).Error)
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id > 0").Error)
	_, err = g.JoinMember(nil, rooms[0].RoomID)
	require.NoError(err)
	p = &invitationAcceptParams{
		RoomID:  m.ToRoomID,
		MatchID: m.ID,
	}
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input any) (any, error) { return "success", nil })
	defer cancel()

	c = handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = rooms[1].CreatorID
	resp, _, err := ActionMultiConnectInvitationAccept(c)
	require.NoError(err)
	require.NotNil(resp)
	res, ok := resp.(invitationAcceptResp)
	require.True(ok)
	assert.Equal(g.ID, res.MultiConnect.GroupID)
	assert.Equal(2, res.MultiConnect.JoinIndex)
}

func TestInvitationAcceptParams_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"status.open": 1}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)

	p := &invitationAcceptParams{
		RoomID: rooms[1].RoomID,
		userID: rooms[0].CreatorID,
	}
	assert.Equal(actionerrors.ErrForbidden, p.check())

	p = &invitationAcceptParams{
		RoomID:  rooms[1].RoomID,
		MatchID: -1,
		userID:  rooms[1].CreatorID,
	}
	assert.Equal(actionerrors.ErrMultiConnectMatchNotFound, p.check())

	m := livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	p = &invitationAcceptParams{
		RoomID:  rooms[1].RoomID,
		MatchID: m.ID,
		userID:  rooms[1].CreatorID,
	}
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id = ?", m.ToRoomID).Error)
	assert.NoError(p.check())
}

func TestInvitationAcceptParams_accept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bililive.SetMockResult(bililive.ActionCreateChannel, bililive.Channel{ChannelID: 123456})
	m := &livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: 1,
		ToRoomID:   2,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	require.NoError(service.Redis.Del(keys.LockMultiConnectGroupCreate1.Format(m.FromRoomID)).Err())

	p := &invitationAcceptParams{
		RoomID:  m.ToRoomID,
		MatchID: m.ID,
		match:   m,
	}
	require.NoError(p.accept()) // init group
	assert.NotNil(p.group)

	m = &livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: 1,
		ToRoomID:   3,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	p = &invitationAcceptParams{
		RoomID:  m.ToRoomID,
		MatchID: m.ID,
		match:   m,
	}
	require.NoError(p.accept()) // join group
	require.NotNil(p.group)
}

func TestActionMultiConnectInvitationRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.Match{}, "from_room_id = ?", 172842330).Error
	require.NoError(err)

	called := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	body := invitationRefuseParam{
		RoomID: 141317526,
	}
	c := handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, _, err = ActionMultiConnectInvitationRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.MatchID = 12345
	c = handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, _, err = ActionMultiConnectInvitationRefuse(c)
	assert.Equal(actionerrors.ErrMultiConnectMatchNotFound, err)

	match := livemulticonnect.Match{
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: 172842330,
		ToRoomID:   141317526,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)

	body.MatchID = match.ID
	c = handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, _, err = ActionMultiConnectInvitationRefuse(c)
	assert.EqualError(err, "您不是该房间的主播，无法处理邀请")

	body.RoomID = 18113499
	c = handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, _, err = ActionMultiConnectInvitationRefuse(c)
	assert.EqualError(err, "只能处理邀请自己的连线请求")

	match = livemulticonnect.Match{
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: 172842330,
		ToRoomID:   body.RoomID,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)
	body.MatchID = match.ID
	c = handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, msg, err := ActionMultiConnectInvitationRefuse(c)
	require.NoError(err)
	assert.Equal("success", msg)
	assert.True(called)

	c = handler.NewTestContext(http.MethodPost, "/invitation/refuse", true, body)
	_, _, err = ActionMultiConnectInvitationRefuse(c)
	assert.EqualError(err, "连线请求已被处理")
}

func TestActionMultiConnectInvitationCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		count++
		return "success", nil
	})()

	rooms, err := room.List(bson.M{}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)
	match := &livemulticonnect.Match{
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
	}
	err = livemulticonnect.DB().Create(match).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/invitation/cancel", true, handler.M{
		"room_id":  match.FromRoomID,
		"match_id": match.ID,
	})
	c.User().ID = rooms[1].CreatorID // 不是申请者
	_, _, err = ActionMultiConnectInvitationCancel(c)
	assert.Equal(actionerrors.ErrForbidden, err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/invitation/cancel", true, handler.M{
		"room_id":  match.FromRoomID,
		"match_id": match.ID,
	})
	c.User().ID = rooms[0].CreatorID
	_, _, err = ActionMultiConnectInvitationCancel(c)
	require.NoError(err)
	assert.Equal(1, count)
	m, err := livemulticonnect.FindMatch(match.ID)
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(livemulticonnect.MatchStatusCancel, m.Status)
}

func TestActionMultiConnectInvitationRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIGoUserBlockStatus,
		func(input any) (output any, err error) {
			return handler.M{"block_status": []bool{false, false}}, nil
		})
	defer cancel()

	body := invitationRequestParam{
		RoomID:   18113499,
		ToRoomID: 789808782,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": body.ToRoomID}
	_, err := livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"multi_connect_settings.disable_invite":            0,
				"multi_connect_settings.disable_unfollowed_invite": 0,
				"multi_connect_settings.disable_apply":             0,
			},
			"$setOnInsert": bson.M{
				"room_id":  body.ToRoomID,
				"_room_id": primitive.NewObjectID(),
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	resp, _, err := ActionMultiConnectInvitationRequest(c)
	require.NoError(err)
	assert.NotNil(resp)
	assert.True(called)
}

func TestNewMultiConnectInvitationRequestParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "connect_id = ?", 123213).Error
	require.NoError(err)

	body := invitationRequestParam{
		RoomID:  9074509,
		GroupID: 1234,
	}
	c := handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.ToRoomID = 9074501
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	body.RoomID = 20230918
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.EqualError(err, "无法替其他主播发起邀请")

	body.RoomID = 18113499
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	body.ToRoomID = 789808782
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.Equal(actionerrors.ErrMultiConnectGroupNotFound, err)

	g := livemulticonnect.Group{
		ConnectID: "123213",
	}
	require.NoError(livemulticonnect.DB().Create(&g).Error)

	gms := []livemulticonnect.GroupMember{
		{GroupID: g.ID, RoomID: 9074501},
		{GroupID: g.ID, RoomID: 9074502},
		{GroupID: g.ID, RoomID: 9074503},
		{GroupID: g.ID, RoomID: 9074504},
		{GroupID: g.ID, RoomID: 9074505},
		{GroupID: g.ID, RoomID: 9074506},
		{GroupID: g.ID, RoomID: 9074507},
		{GroupID: g.ID, RoomID: 9074508},
		{GroupID: g.ID, RoomID: 9074509},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), gms)
	require.NoError(err)
	body.GroupID = g.ID
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	_, err = newMultiConnectInvitationRequestParam(c)
	assert.EqualError(err, "连线人数已达上限，无法操作")

	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", g.ID).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/invitation/request", true, body)
	param, err := newMultiConnectInvitationRequestParam(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestMultiConnectInvitationRequestParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	blockStatusList := []bool{true, false}
	cancel := mrpc.SetMock(userapi.URIGoUserBlockStatus,
		func(input any) (output any, err error) {
			return handler.M{"block_status": blockStatusList}, nil
		})
	defer cancel()

	testRooms := []int64{9074501, 9074502, 9074503, 9074504, 9074505, 9074506}
	err := livemulticonnect.DB().Delete(&livemulticonnect.Match{},
		"from_room_id IN (?) OR to_room_id IN (?)", testRooms, testRooms).Error
	require.NoError(err)

	err = livemulticonnect.DB().Delete(&livemulticonnect.GroupMember{}, "room_id IN (?)", testRooms).Error
	require.NoError(err)

	now := goutil.TimeNow()
	matchList := []livemulticonnect.Match{
		{FromRoomID: 9074501, Type: livemulticonnect.MatchTypeApply, Status: livemulticonnect.MatchStatusPending},
		{ToRoomID: 9074502, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending},
		{ToRoomID: 9074505, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending},
		{ToRoomID: 9074506, Type: livemulticonnect.MatchTypeApply, Status: livemulticonnect.MatchStatusPending},
		{FromRoomID: 9074507, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchList)
	require.NoError(err)

	param := &invitationRequestParam{
		toRoom: &room.Room{
			Helper: room.Helper{
				RoomID:    9074502,
				CreatorID: 9074502,
				Status: room.Status{
					PK: room.PKStatusOngoing,
				},
			},
		},
		fromRoom: &room.Room{
			Helper: room.Helper{
				RoomID:    9074501,
				CreatorID: 9074501,
			},
		},
	}
	err = param.check()
	assert.EqualError(err, "已申请加入其他连线，无法操作")

	param.fromRoom.RoomID = 9074503
	param.fromRoom.CreatorID = 9074503
	err = param.check()
	assert.EqualError(err, "对方处理其他邀请中，请稍后再试")

	param.fromRoom.RoomID = 9074507
	err = param.check()
	assert.EqualError(err, "对方处理其他邀请中，请稍后再试")

	param.toRoom.RoomID = 9074501
	err = param.check()
	assert.EqualError(err, "对方处理其他申请中，请稍后再试")

	param.toRoom.RoomID = 9074504
	param.toRoom.CreatorID = 9074504
	err = param.check()
	assert.EqualError(err, "您已拉黑对方，无法操作")

	blockStatusList = []bool{false, true}
	err = param.check()
	require.EqualError(err, "由于对方设置，无法操作")

	blockStatusList = []bool{false, false}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": param.toRoom.RoomID}
	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"multi_connect_settings.disable_invite":            1,
				"multi_connect_settings.disable_unfollowed_invite": 1,
			},
			"$setOnInsert": bson.M{
				"room_id":  param.toRoom.RoomID,
				"_room_id": primitive.NewObjectID(),
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "对方不允许连线邀请")

	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{"$set": bson.M{"multi_connect_settings.disable_invite": 0}},
	)
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "对方不允许未关注人的连线邀请")

	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{"$set": bson.M{"multi_connect_settings.disable_unfollowed_invite": 0}},
	)
	require.NoError(err)

	err = livemulticonnect.DB().Model(livemulticonnect.GroupMember{}).
		Where("room_id = ?", param.toRoom.RoomID).
		Updates(map[string]any{
			"end_time": now.UnixMilli(),
		}).Error
	require.NoError(err)
	require.NoError(param.check())

	param.fromRoom.RoomID = 9074505
	err = param.check()
	assert.EqualError(err, "有未处理的连线邀请，无法操作")

	param.fromRoom.RoomID = 9074506
	err = param.check()
	assert.EqualError(err, "有未处理的连线申请，无法操作")
}

func TestMultiConnectInvitationRequestParam_addConnectMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(&livemulticonnect.Match{}, "from_room_id = ?", 9074501).Error
	require.NoError(err)

	param := &invitationRequestParam{
		toRoom: &room.Room{
			Helper: room.Helper{
				RoomID: 9074509,
			},
		},
		fromRoom: &room.Room{
			Helper: room.Helper{
				RoomID: 9074501,
			},
		},
	}
	err = param.addConnectMatch()
	require.NoError(err)

	match, err := livemulticonnect.FindMatch(param.match.ID)
	require.NoError(err)
	assert.NotNil(match)
}
