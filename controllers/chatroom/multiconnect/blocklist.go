package multiconnect

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionMultiConnectBlockList 连线黑名单
/**
 * @api {get} /api/v2/chatroom/multi-connect/block-list 连线黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room_id": 123456,
 *           "creator_id": 123456,
 *           "creator_username": "用户名",
 *           "creator_iconurl": "https://static-test.missevan.com/avatars/icon01.png"
 *         },
 *         ...
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectBlockList(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// ActionMultiConnectBlockListRemove 移除连线黑名单
/**
 * @api {post} /api/v2/chatroom/multi-connect/block-list/remove 移除连线黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} block_room_id 黑名单直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "已移出连线黑名单",
 *     "data": null
 *   }
 */
func ActionMultiConnectBlockListRemove(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// ActionMultiConnectBlockListAdd 添加连线黑名单
/**
 * @api {post} /api/v2/chatroom/multi-connect/block-list/add 添加连线黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} block_room_id 需要拉黑的直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "已加入连线黑名单",
 *     "data": null
 *   }
 */
func ActionMultiConnectBlockListAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
