package multiconnect

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewRecentListParam(t *testing.T) {
	assert := assert.New(t)

	// 用户与房间不匹配
	p := &recentListParam{
		RoomID: 2233,
		userID: 1000,
	}
	c := handler.NewTestContext(http.MethodGet, "?room_id=2233", true, p)
	param, err := newRecentListParam(c)
	assert.Nil(param)
	assert.Equal(actionerrors.ErrForbidden, err)

	// 用户与房间匹配
	p = &recentListParam{
		RoomID: 18113499,
		userID: 12,
	}
	c = handler.NewTestContext(http.MethodGet, "?room_id=18113499", true, p)
	param, err = newRecentListParam(c)
	assert.Nil(err)
	assert.NotNil(param)
}

func TestRecentListParam_getPendingMatches(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(112233)

	matches := []livemulticonnect.Match{
		{Status: livemulticonnect.MatchStatusTimeout, FromRoomID: roomID, ToRoomID: 99},
		{Status: livemulticonnect.MatchStatusPending, Type: livemulticonnect.MatchTypeApply, FromRoomID: roomID, ToRoomID: 100, BridgeRoomID: 99},
		{Status: livemulticonnect.MatchStatusPending, FromRoomID: 101, ToRoomID: roomID},
	}
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.Match{}, "from_room_id = ? OR to_room_id = ?", roomID, roomID).Error)
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matches))

	param := &recentListParam{
		RoomID: roomID,
	}
	err := param.getPendingMatches()
	require.NoError(err)
	assert.Len(param.matchingRoomIDMap, 2)
	assert.NotNil(param.matchingRoomIDMap[99])
	assert.NotNil(param.matchingRoomIDMap[101])
}

func TestRecentListResp_sort(t *testing.T) {
	assert := assert.New(t)

	p := &recentListResp{
		Data: []recentListItemInfo{
			{
				Room: recentListItemRoom{RoomID: 2, Statistics: recentListStatistics{Score: 101, AttentionCount: 101}},
				sort: 3,
			},
			{
				Room: recentListItemRoom{RoomID: 3, Statistics: recentListStatistics{Score: 100, AttentionCount: 101}},
				sort: 3,
			},
			{
				Room: recentListItemRoom{RoomID: 1, Statistics: recentListStatistics{Score: 100, AttentionCount: 102}},
				sort: 3,
			},
			{
				Room: recentListItemRoom{RoomID: 5, Statistics: recentListStatistics{Score: 100, AttentionCount: 101}},
				sort: 2,
			},
			{
				Room: recentListItemRoom{RoomID: 4, Statistics: recentListStatistics{Score: 103, AttentionCount: 101}},
				sort: 2,
			},
			{
				Room: recentListItemRoom{RoomID: 6, Statistics: recentListStatistics{Score: 104, AttentionCount: 101}},
				sort: 1,
			},
			{
				Room: recentListItemRoom{RoomID: 7, Statistics: recentListStatistics{Score: 100, AttentionCount: 101}},
				sort: 1,
			},
		},
	}
	p.sort()
	sortedRoomIDs := make([]int64, 0, len(p.Data))
	// 排序后按 sort，AttentionCount，Score 的顺序排列
	for i := 0; i < len(p.Data); i++ {
		sortedRoomIDs = append(sortedRoomIDs, p.Data[i].Room.RoomID)
	}
	assert.Equal([]int64{1, 2, 3, 4, 5, 6, 7}, sortedRoomIDs)
}

func TestActionMultiConnectRecentList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := goutil.TimeNow()
	roomID := int64(18113499)
	param := &recentListParam{
		RoomID: roomID,
	}
	c := handler.NewTestContext(http.MethodGet, "?room_id=18113499", true, param)
	resp, _, err := ActionMultiConnectRecentList(c)
	assert.Nil(err)
	assert.Empty(resp.(*recentListResp).Data)

	// 构建历史记录
	memberRecords := []livemulticonnect.GroupMember{
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: roomID},
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 4381915},   // 匹配中的房间
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 868965282}, // 正常获取连线过的数据
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 173076071}, // 正常获取连线中的数据
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 114693474}, // pk 中的房间被过滤
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 347142109}, // 正在同一连线组的房间被过滤
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 343348035}, // 账号拉黑的房间被过滤 creator_id：9074699
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 3192516},   // 主播连线拉黑的房间被过滤

		{GroupID: 2, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: roomID},
		{GroupID: 2, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: 347142109},

		{GroupID: 3, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: 100000009},
		{GroupID: 3, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: 173076071},
	}
	matchRecords := []livemulticonnect.Match{
		{Status: livemulticonnect.NotifyGroupJoinStatusPending, FromRoomID: roomID, ToRoomID: 4381915},
	}
	blockRecords := []livemulticonnect.Blocklist{
		{RoomID: roomID, BlockRoomID: 3192516},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livepk.PKCollection().DeleteOne(ctx, bson.M{"fighters.room_id": 114693474})
	require.NoError(err)
	_, err = livepk.PKCollection().InsertOne(ctx, livepk.LivePK{
		OID:    primitive.NewObjectID(),
		Status: livepk.PKRecordStatusConnect,
		Fighters: [2]*livepk.Fighter{
			{
				RoomID: 1,
			},
			{
				RoomID: 114693474,
			},
		},
	})
	require.NoError(err)

	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.Blocklist{}).Error)
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error)
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.Match{}).Error)
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Blocklist{}.TableName(), blockRecords))
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), memberRecords))
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchRecords))

	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatusList,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_user_list": []int64{}, "user_block_list": []int64{9074699}}, nil
		})
	defer cleanup()

	c = handler.NewTestContext(http.MethodGet, "?room_id=18113499", true, param)
	resp, _, err = ActionMultiConnectRecentList(c)
	assert.Nil(err)
	assert.NotEmpty(resp.(*recentListResp).Data)
	assert.Len(resp.(*recentListResp).Data, 3)
}
