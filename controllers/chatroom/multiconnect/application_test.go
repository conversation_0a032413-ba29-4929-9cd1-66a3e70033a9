package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionMultiConnectApplicationRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input any) (output any, err error) {
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cancel()

	called := false
	cancel = mrpc.SetMock(userapi.URIIMBroadcastUser, func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	err := livemulticonnect.DB().Delete(&livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	g := livemulticonnect.Group{
		ID: 1,
	}
	err = livemulticonnect.DB().Create(&g).Error
	require.NoError(err)

	members := []livemulticonnect.GroupMember{
		{GroupID: 1, RoomID: 789808782, Role: livemulticonnect.MemberRoleMember},
		{GroupID: 1, RoomID: 100000010, Role: livemulticonnect.MemberRoleOwner},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": 100000010}
	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"multi_connect_settings.disable_invite":            0,
				"multi_connect_settings.disable_unfollowed_invite": 0,
				"multi_connect_settings.disable_apply":             0,
			},
			"$setOnInsert": bson.M{
				"room_id":  100000010,
				"_room_id": primitive.NewObjectID(),
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)

	body := applicationRequestParam{
		RoomID:   18113499,
		ToRoomID: 789808782,
	}
	c := handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	resp, _, err := ActionMultiConnectApplicationRequest(c)
	require.NoError(err)
	assert.NotEmpty(resp)
	assert.True(called)
}

func TestNewApplicationRequestParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(&livemulticonnect.GroupMember{}, "room_id IN (?)",
		[]int64{18113499, 789808782}).Error
	require.NoError(err)

	err = livemulticonnect.DB().Delete(&livemulticonnect.Group{}).Error
	require.NoError(err)

	g := livemulticonnect.Group{
		ID: 1,
	}
	err = livemulticonnect.DB().Create(&g).Error
	require.NoError(err)

	body := applicationRequestParam{
		RoomID:   9074501,
		ToRoomID: 9074501,
	}
	c := handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.EqualError(err, "无法对自己操作")

	body.ToRoomID = 9074502
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	body.RoomID = 20230918
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.EqualError(err, "无法替其他主播发起申请")

	body.RoomID = 18113499
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	body.ToRoomID = 789808782
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.EqualError(err, "该连线已结束，无法操作")

	members := []livemulticonnect.GroupMember{
		{GroupID: 1, RoomID: 18113499, Role: livemulticonnect.MemberRoleMember},
		{GroupID: 1, RoomID: 789808782, Role: livemulticonnect.MemberRoleMember},
		{GroupID: 1, RoomID: 100000010, Role: livemulticonnect.MemberRoleOwner},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	_, err = newApplicationRequestParam(c)
	assert.EqualError(err, "当前正在主播连线中，无法操作")

	err = livemulticonnect.DB().Delete(&livemulticonnect.GroupMember{}, "room_id = ?", body.RoomID).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/application/request", true, body)
	param, err := newApplicationRequestParam(c)
	require.NoError(err)
	assert.NotEmpty(param)
}

func TestApplicationRequestParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(&livemulticonnect.Match{},
		"from_room_id = ? OR to_room_id = ?", 9074501, 9074503).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(&livemulticonnect.Group{}).Error
	require.NoError(err)

	blockList := []int64{}
	cancel := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input any) (output any, err error) {
			if input.(map[string]any)["type"].(int) == userapi.BlockListTypeBlockedByUser {
				return handler.M{"block_list": []int64{}}, nil
			}
			return handler.M{"block_list": blockList}, nil
		})
	defer cancel()

	param := applicationRequestParam{
		group: &livemulticonnect.Group{
			ID: 1,
		},
		fromRoom: &room.Room{
			Helper: room.Helper{
				RoomID:    9074501,
				CreatorID: 9074501,
			},
		},
		toRoom: &room.Room{
			Helper: room.Helper{
				RoomID:    9074502,
				CreatorID: 9074502,
			},
		},
		ownerRoom: &room.Room{
			Helper: room.Helper{
				RoomID:    9074503,
				CreatorID: 9074503,
			},
		},
	}
	match := livemulticonnect.Match{
		ToRoomID: param.ownerRoom.RoomID,
		Type:     livemulticonnect.MatchTypeApply,
		Status:   livemulticonnect.MatchStatusPending,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)
	err = param.check()
	assert.EqualError(err, "对方处理其他申请中，请稍后再试")

	require.NoError(livemulticonnect.DB().Delete(&livemulticonnect.Match{}, "to_room_id = ?", 9074503).Error)
	match = livemulticonnect.Match{
		FromRoomID: param.fromRoom.RoomID,
		Type:       livemulticonnect.MatchTypeInvite,
		Status:     livemulticonnect.MatchStatusPending,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)
	err = param.check()
	assert.EqualError(err, "已邀请其他主播连线，无法发起连线申请")

	require.NoError(livemulticonnect.DB().Delete(&livemulticonnect.Match{}, "from_room_id = ?", 9074501).Error)
	match = livemulticonnect.Match{
		FromRoomID: param.fromRoom.RoomID,
		Type:       livemulticonnect.MatchTypeApply,
		Status:     livemulticonnect.MatchStatusPending,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)
	err = param.check()
	assert.EqualError(err, "不能同时申请 2 个连线")

	require.NoError(livemulticonnect.DB().Delete(&livemulticonnect.Match{}, "from_room_id = ?", 9074501).Error)
	key := blocklist.KeyUserBlock(param.fromRoom.CreatorID)
	err = service.LRURedis.SAdd(key, param.toRoom.CreatorID, param.ownerRoom.CreatorID).Err()
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "您已拉黑对方，无法操作")

	err = service.LRURedis.SRem(key, param.toRoom.CreatorID).Err()
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "您已将该连线的主麦拉黑，无法操作")

	err = service.LRURedis.SRem(key, param.ownerRoom.CreatorID).Err()
	require.NoError(err)
	blockList = []int64{param.toRoom.CreatorID}
	err = param.check()
	assert.EqualError(err, "由于对方设置，无法操作")

	blockList = []int64{param.ownerRoom.CreatorID}
	err = param.check()
	assert.EqualError(err, "由于对方主麦设置，无法操作")

	blockList = []int64{}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": param.ownerRoom.RoomID}
	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"multi_connect_settings.disable_apply": 1,
			},
			"$setOnInsert": bson.M{
				"room_id":  param.ownerRoom.RoomID,
				"_room_id": primitive.NewObjectID(),
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "对方主麦不允许连线申请")

	_, err = livemeta.Collection().UpdateOne(ctx, filter,
		bson.M{"$set": bson.M{"multi_connect_settings.disable_apply": 0}},
	)
	require.NoError(err)
	members := []livemulticonnect.GroupMember{
		{GroupID: 1, RoomID: 9074501},
		{GroupID: 1, RoomID: 9074502},
		{GroupID: 1, RoomID: 9074503},
		{GroupID: 1, RoomID: 9074504},
		{GroupID: 1, RoomID: 9074505},
		{GroupID: 1, RoomID: 9074506},
		{GroupID: 1, RoomID: 9074507},
		{GroupID: 1, RoomID: 9074508},
		{GroupID: 1, RoomID: 9074509},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "连线人数已达上限，无法操作")

	param.group.ID = 2
	assert.NoError(param.check())
}

func TestApplicationRequestParam_addConnectMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(&livemulticonnect.Match{}, "from_room_id = ?", 9074501).Error
	require.NoError(err)

	param := applicationRequestParam{
		group: &livemulticonnect.Group{
			ID: 1,
		},
		ownerRoom: &room.Room{
			Helper: room.Helper{
				RoomID: 9074509,
			},
		},
		fromRoom: &room.Room{
			Helper: room.Helper{
				RoomID: 9074501,
			},
		},
		toRoom: &room.Room{
			Helper: room.Helper{
				RoomID: 9074509,
			},
		},
	}
	require.NoError(param.addConnectMatch())
	match, err := livemulticonnect.FindMatch(param.match.ID)
	require.NoError(err)
	assert.NotNil(match)
}

func TestActionMultiConnectApplicationCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		count++
		return "success", nil
	})()

	rooms, err := room.List(bson.M{}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)
	match := &livemulticonnect.Match{
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
	}
	err = livemulticonnect.DB().Create(match).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/application/cancel", true, handler.M{
		"room_id":  match.FromRoomID,
		"match_id": match.ID,
	})
	c.User().ID = rooms[1].CreatorID // 不是申请者
	_, _, err = ActionMultiConnectApplicationCancel(c)
	assert.Equal(actionerrors.ErrForbidden, err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/application/cancel", true, handler.M{
		"room_id":  match.FromRoomID,
		"match_id": match.ID,
	})
	c.User().ID = rooms[0].CreatorID
	_, _, err = ActionMultiConnectApplicationCancel(c)
	require.NoError(err)
	assert.Equal(1, count)
	m, err := livemulticonnect.FindMatch(match.ID)
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(livemulticonnect.MatchStatusCancel, m.Status)
}

func TestActionMultiConnectApplicationAccept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &applicationAcceptParams{
		RoomID:  -11,
		MatchID: 1,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	_, _, err := ActionMultiConnectApplicationAccept(c)
	assert.Equal(actionerrors.ErrParams, err)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"status.open": 1}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)
	g := &livemulticonnect.Group{
		ConnectID: "1",
	}
	require.NoError(livemulticonnect.DB().Create(&g).Error)
	m := &livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeApply,
		Status:     livemulticonnect.MatchStatusPending,
		GroupID:    g.ID,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	members := []livemulticonnect.GroupMember{
		{GroupID: g.ID, Role: livemulticonnect.MemberRoleOwner, Status: livemulticonnect.MemberStatusOngoing, RoomID: rooms[1].RoomID},
		{GroupID: g.ID, Role: livemulticonnect.MemberRoleMember, Status: livemulticonnect.MemberStatusOngoing, RoomID: 1},
	}
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members))

	require.NoError(err)
	require.NoError(err)
	p = &applicationAcceptParams{
		RoomID:  m.ToRoomID,
		MatchID: m.ID,
	}
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input any) (any, error) { return "success", nil })
	defer cancel()

	c = handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = rooms[1].CreatorID
	resp, _, err := ActionMultiConnectApplicationAccept(c)
	require.NoError(err)
	require.NotNil(resp)
	res, ok := resp.(applicationAcceptResp)
	require.True(ok)
	assert.Equal(g.ID, res.MultiConnect.GroupID)
	assert.Equal(3, res.MultiConnect.JoinIndex)
}

func TestApplicationAcceptParams_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"status.open": 1}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)

	p := &applicationAcceptParams{
		RoomID: rooms[1].RoomID,
	}
	assert.Equal(actionerrors.ErrForbidden, p.check())

	p = &applicationAcceptParams{
		RoomID:  rooms[1].RoomID,
		MatchID: -1,
		userID:  rooms[1].CreatorID,
	}
	assert.Equal(actionerrors.ErrMultiConnectMatchNotFound, p.check())

	g := &livemulticonnect.Group{
		ConnectID: "1",
	}
	require.NoError(livemulticonnect.DB().Create(&g).Error)
	members := []*livemulticonnect.GroupMember{
		{GroupID: g.ID, Role: livemulticonnect.MemberRoleOwner, Status: livemulticonnect.MemberStatusOngoing, RoomID: rooms[1].RoomID},
		{GroupID: g.ID, Role: livemulticonnect.MemberRoleMember, Status: livemulticonnect.MemberStatusOngoing, RoomID: 2},
	}
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id > 0").Error)
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members))
	m := livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeApply,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: rooms[0].RoomID,
		ToRoomID:   rooms[1].RoomID,
		GroupID:    g.ID,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	p = &applicationAcceptParams{
		RoomID:  rooms[1].RoomID,
		MatchID: m.ID,
		userID:  rooms[1].CreatorID,
		group:   g,
	}
	assert.NoError(p.check())
}

func TestApplicationAcceptParams_accept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &livemulticonnect.Match{
		Type:       livemulticonnect.MatchTypeApply,
		Status:     livemulticonnect.MatchStatusPending,
		FromRoomID: 1,
		ToRoomID:   2,
	}
	require.NoError(livemulticonnect.DB().Create(&m).Error)
	g := &livemulticonnect.Group{
		ConnectID: "1",
	}
	require.NoError(livemulticonnect.DB().Create(&g).Error)
	joinMember, err := g.JoinMember(nil, 2)
	require.NoError(err)
	require.NotNil(joinMember)
	p := &applicationAcceptParams{
		RoomID:  m.ToRoomID,
		MatchID: m.ID,
		match:   m,
		group:   g,
	}
	require.NoError(p.accept())
	require.NotNil(p.group)
	assert.Equal(g.ID, p.group.ID)
}

func TestActionMultiConnectApplicationRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.Match{}).Error
	require.NoError(err)

	called := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	body := applicationRefuseParam{
		RoomID: 141317526,
	}
	c := handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, _, err = ActionMultiConnectApplicationRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.MatchID = 1123
	c = handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, _, err = ActionMultiConnectApplicationRefuse(c)
	assert.Equal(actionerrors.ErrMultiConnectMatchNotFound, err)

	match := livemulticonnect.Match{
		Status:     livemulticonnect.MatchStatusRefuse,
		FromRoomID: 172842330,
		ToRoomID:   141317526,
		GroupID:    1222,
	}
	require.NoError(livemulticonnect.DB().Create(&match).Error)

	body.MatchID = match.ID
	c = handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, _, err = ActionMultiConnectApplicationRefuse(c)
	assert.EqualError(err, "连线请求已被处理")

	err = livemulticonnect.DB().Table(livemulticonnect.Match{}.TableName()).
		Where("id = ?", match.ID).
		Update("status", livemulticonnect.MatchStatusPending).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, _, err = ActionMultiConnectApplicationRefuse(c)
	assert.EqualError(err, "您不是该房间的主播，无法处理该请求")

	body.RoomID = 18113499
	c = handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, _, err = ActionMultiConnectApplicationRefuse(c)
	assert.EqualError(err, "您不是主麦，无法处理该申请")

	err = livemulticonnect.DB().Table(livemulticonnect.Match{}.TableName()).
		Where("id = ?", match.ID).
		Update("to_room_id", 18113499).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/application/refuse", true, body)
	_, msg, err := ActionMultiConnectApplicationRefuse(c)
	assert.NoError(err)
	assert.Equal("success", msg)
	assert.True(called)
	m, err := livemulticonnect.FindMatch(match.ID)
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(livemulticonnect.MatchStatusRefuse, m.Status)
}
