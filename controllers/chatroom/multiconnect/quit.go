package multiconnect

import (
	"errors"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type quitParams struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GroupID int64 `form:"group_id" json:"group_id"`
}

// ActionMultiConnectQuit 退出连线
/**
 * @api {post} /api/v2/chatroom/multi-connect/quit 退出连线
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "退出成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 退出连线成员直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "quit",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "status": 1, // 连线状态；1: 连线中；2: 已结束（连线成员小于 2 人时连线结束）
 *       "quit_member": { // 退出连线的成员信息
 *         "index": 1, // 连线序号
 *         "role": 1, // 角色；1: 主麦；2: 连线成员
 *         "room": { // 房间信息
 *.          "room_id": 223344,
 *           "name": "12345",
 *           "creator_id": 12345,
 *           "creator_username": "1234"
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 退出连线本次连线成员直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "quit",
 *     "room_id": 152,
 *     "multi_connect": {
 *       "quit_member": { // 退出连线的成员信息，如果是主麦退出连线 members 中会产生一个新的主麦
 *         "index": 1, // 连线序号
 *         "role": 1, // 角色；1: 主麦；2: 连线成员
 *         "room": { // 房间信息
 *.          "room_id": 223344,
 *           "name": "12345",
 *           "creator_id": 12345,
 *           "creator_username": "1234"
 *         }
 *       },
 *       "group_id": 111, // 连线组 ID
 *       "status": 1, // 连线状态；1: 连线中；2: 已结束（连线成员小于 2 人时连线结束）
 *       "duration": 10000, // 显示本房连线总时长，单位：毫秒
 *       "mute_room_ids": [3,12], // 静音连线直播间 ID 列表
 *       "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *       "members": [ // 连线成员列表，首位总是当前麦位
 *           "index": 2, // 连线序号
 *           "role": 1, // 角色；1: 主麦；2: 连线成员
 *           "score": 111, // 礼物积分
 *           "room": { // 房间信息
 *             "room_id": 152,
 *             "name": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *           }
 *         },
 *         ...
 *       ]
 *     }
 *   }
 *
 * @apiError (403) {Number} code 500190004
 * @apiError (403) {String} info 连线组成员已变更 // 客户端需要忽略该错误，并重新请求 meta 接口获取最新连线组状态
 *
 */
func ActionMultiConnectQuit(c *handler.Context) (handler.ActionResponse, string, error) {
	var param quitParams
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.GroupID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	quitRoom, err := room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if quitRoom == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if !quitRoom.IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	group, err := livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if group == nil {
		return nil, "", actionerrors.NewErrForbidden("当前不在连线中，无法操作")
	}

	quitMember, err := livemulticonnect.FindOngoingMemberByRoomID(nil, param.RoomID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if quitMember == nil {
		return nil, "", actionerrors.ErrMultiConnectGroupMemberModified
	}
	if quitMember.GroupID != param.GroupID {
		return nil, "", actionerrors.NewErrMultiConnectGroupMemberModifiedMsg("当前不在连线中，无法操作")
	}

	p := livemulticonnect.LeaveMemberParam{
		Group:       group,
		LeaveStatus: livemulticonnect.MemberStatusQuit,
		LeaveRoom:   quitRoom,
	}
	err = p.LeaveMember()
	if err != nil {
		if errors.Is(err, livemulticonnect.ErrGroupMemberModified) {
			return nil, "", actionerrors.ErrMultiConnectGroupMemberModified
		}
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	elems := p.BuildLeaveBroadcastList()
	if len(elems) >= 0 {
		err = userapi.BroadcastMany(elems)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil, "退出成功", nil
}
