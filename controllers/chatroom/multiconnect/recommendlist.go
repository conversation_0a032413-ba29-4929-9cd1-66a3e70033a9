package multiconnect

import (
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	randomConnectingMemberCount = 2  // 随机推荐连线中的主播数
	maxRecommendCount           = 10 // 推荐列表推荐主播数，不包括匹配中的主播
)

type recommendListParam struct {
	uc                mrpc.UserContext
	userID            int64
	room              *room.Room
	roomMap           map[int64]*room.Simple
	roomMemberNumMap  map[int64]*livemulticonnect.GroupMemberNum
	matchingRoomIDMap map[int64]*livemulticonnect.Match
}

func newRecommendListParam(c *handler.Context) (*recommendListParam, error) {
	param := recommendListParam{
		uc:     c.UserContext(),
		userID: c.UserID(),
	}
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.room, err = room.Find(roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.userID != param.room.CreatorID {
		return nil, actionerrors.ErrForbidden
	}
	return &param, nil
}

func (p *recommendListParam) findMatchingRoomIDs() ([]int64, error) {
	matchingRoomIDMap, err := livemulticonnect.FindMatchingRoomIDMap(p.room.RoomID)
	if err != nil {
		return nil, err
	}
	p.matchingRoomIDMap = matchingRoomIDMap
	roomIDs := make([]int64, 0, len(p.matchingRoomIDMap))
	for roomID := range p.matchingRoomIDMap {
		roomIDs = append(roomIDs, roomID)
	}
	// 匹配中的主播需要按匹配时间倒序排列
	sort.Slice(roomIDs, func(i, j int) bool {
		return p.matchingRoomIDMap[roomIDs[i]].ID > p.matchingRoomIDMap[roomIDs[j]].ID
	})
	return roomIDs, nil
}

func (p *recommendListParam) findRandomFollowedRoomID() (int64, error) {
	followedUserIDs, err := attentionuser.AllFollowedCreatorIDs(p.userID)
	if err != nil {
		return 0, err
	}
	if len(followedUserIDs) == 0 {
		return 0, nil
	}

	followedRoomID, err := room.RandomOpenRoomID(followedUserIDs)
	if err != nil {
		return 0, err
	}
	return followedRoomID, nil
}

func (p *recommendListParam) findRandomOpeningRooms() ([]*room.Simple, error) {
	filter := bson.M{
		"status.open":          room.StatusOpenTrue,
		"status.multi_connect": bson.M{"$ne": room.MultiConnectStatusOngoing},
	}
	openingCount, err := room.CountByFilter(filter)
	if err != nil {
		return nil, err
	}
	randomRooms, err := room.ListRandomTopScoreRooms(filter, 0, openingCount/2, 20, &room.FindOptions{FindCreator: true, FindFans: true, ListenerID: p.userID})
	if err != nil {
		return nil, err
	}
	return randomRooms, nil
}

// isMultiConnectOpen 判断多人连线是否开放
func (p *recommendListParam) isMultiConnectOpen() (bool, error) {
	mcCfg, err := params.FindMultiConnect()
	if err != nil {
		return false, actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	return mcCfg.IsFullOpen(now), nil
}

func (p *recommendListParam) findRecommendRoomIDs() ([]int64, error) {
	// roomIDs 按顺序保存推荐列表的房间号，预先分配长度 30（优先推荐的房间长度 10 和随机推荐的房间长度 20）
	roomIDs := make([]int64, 0, 30)

	// 当前匹配中的房间
	matchingRoomIDs, err := p.findMatchingRoomIDs()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs = append(roomIDs, matchingRoomIDs...)

	// 取 1 个关注的主播
	followedRoomID, err := p.findRandomFollowedRoomID()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if followedRoomID > 0 {
		roomIDs = append(roomIDs, followedRoomID)
	}

	// 当前未在连线中时，获取 2 个连线中的主播
	if !p.room.IsMultiConnect() {
		multiConnectingRoomIDs, err := livemulticonnect.FindNonFullGroupMemberRoomIDs(randomConnectingMemberCount)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		roomIDs = append(roomIDs, multiConnectingRoomIDs...)
	}

	// 获取上述房间的信息和所在连线组的人数
	if len(roomIDs) > 0 {
		rooms, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil,
			&room.FindOptions{FindCreator: true, FindFans: true, ListenerID: p.userID})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		p.roomMap = util.ToMap(rooms, func(r *room.Simple) int64 {
			return r.RoomID
		})
		p.roomMemberNumMap, err = livemulticonnect.FindMemberNumByRoomIDs(roomIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	} else {
		p.roomMap = make(map[int64]*room.Simple)
		p.roomMemberNumMap = make(map[int64]*livemulticonnect.GroupMemberNum)
	}

	// 随机取站内热度前 50% 且未在连线中的主播
	randomRooms, err := p.findRandomOpeningRooms()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, r := range randomRooms {
		roomIDs = append(roomIDs, r.RoomID)
		p.roomMap[r.RoomID] = r
	}
	return roomIDs, nil
}

// 获取被过滤的房间号，后续不会展示在推荐列表中
func (p *recommendListParam) filterRoomIDs(roomIDs []int64) (map[int64]struct{}, error) {
	if len(roomIDs) == 0 {
		return nil, nil
	}
	filteredRoomIDMap := make(map[int64]struct{}, len(roomIDs))

	// 过滤正在听众连麦的房间
	connectingRoomIDs, err := liveconnect.ConnectingRoomIDs(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, roomID := range connectingRoomIDs {
		filteredRoomIDMap[roomID] = struct{}{}
	}
	roomIDs = sets.Diff(roomIDs, connectingRoomIDs)
	if len(roomIDs) == 0 {
		return filteredRoomIDMap, nil
	}

	// 过滤正在 PK 流程中的房间
	pkRoomIDs, err := livepk.OngoingPKRoomIDs(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, roomID := range pkRoomIDs {
		filteredRoomIDMap[roomID] = struct{}{}
	}
	roomIDs = sets.Diff(roomIDs, pkRoomIDs)
	if len(roomIDs) == 0 {
		return filteredRoomIDMap, nil
	}

	// 过滤存在连线拉黑关系的房间
	blockedRoomIDs, blockedCurrentRoomIDs, err := livemulticonnect.BlockedRoomIDs(p.room.RoomID, roomIDs...)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, roomID := range blockedRoomIDs {
		filteredRoomIDMap[roomID] = struct{}{}
	}
	for _, roomID := range blockedCurrentRoomIDs {
		filteredRoomIDMap[roomID] = struct{}{}
	}
	roomIDs = sets.Diff(roomIDs, append(blockedRoomIDs, blockedCurrentRoomIDs...))
	if len(roomIDs) == 0 {
		return filteredRoomIDMap, nil
	}

	// 过滤账号拉黑的房间
	creatorIDs := make([]int64, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		if r, ok := p.roomMap[roomID]; ok {
			creatorIDs = append(creatorIDs, r.CreatorID)
		}
	}
	blockUserList, userBlockList, err := userapi.UserBlockStatusList(p.uc, p.userID, creatorIDs)
	if err != nil {
		// 直接返回 mrpc 返回的 error
		return nil, err
	}
	blockUserIDMap := util.ToMap(append(blockUserList, userBlockList...), func(userID int64) int64 {
		return userID
	})
	for _, r := range p.roomMap {
		if _, ok := blockUserIDMap[r.CreatorID]; ok {
			filteredRoomIDMap[r.RoomID] = struct{}{}
		}
	}
	return filteredRoomIDMap, nil
}

func (p *recommendListParam) resp(roomIDs []int64, filteredRoomIDMap map[int64]struct{}) (*recommendListResp, error) {
	resp := &recommendListResp{Data: make([]recommendListItemInfo, 0, 10)}

	// 获取当前房间正在进行中的连线组记录，判断推荐列表中是否有同一连线组的房间
	curMember, err := livemulticonnect.FindOngoingMemberByRoomID(livemulticonnect.DB(), p.room.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取 7 天内连线过的房间
	connectedMembers, err := livemulticonnect.FindRecentConnectedMembers(p.room.RoomID, time.Hour*24*7, true)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	connectedRoomIDMap := util.ToMap(connectedMembers, func(m *livemulticonnect.GroupMember) int64 {
		return m.RoomID
	})

	// recommendCnt 表示列表中推荐主播的数量，匹配中的主播不占用推荐主播数量
	recommendCnt := 0
	for _, roomID := range roomIDs {
		if recommendCnt >= maxRecommendCount {
			break
		}
		if _, ok := filteredRoomIDMap[roomID]; ok {
			continue
		}
		if roomID == p.room.RoomID {
			// 略过随机取到当前房间的情况
			continue
		}
		// 避免匹配中和关注房间和随机获取的房间有重复
		filteredRoomIDMap[roomID] = struct{}{}

		r := p.roomMap[roomID]
		// 房间信息未被获取或房间在未开播状态时，不推荐该房间
		if r == nil || !r.IsOpen() {
			continue
		}
		itemInfo := recommendListItemInfo{
			Room: recommendListItemRoom{
				RoomID:          r.RoomID,
				Name:            r.Name,
				CreatorID:       r.CreatorID,
				CreatorUsername: r.CreatorUsername,
				Status: recommendListStatus{
					Open:      r.Status.Open,
					Attention: r.Status.Attention,
				},
				Statistics: recommendListStatistics{
					Score:          r.Statistics.Score,
					AttentionCount: r.Statistics.AttentionCount,
				},
				CreatorIconURL: r.CreatorIconURL,
			},
			MultiConnect: recommendListItemMultiConnect{},
		}

		if groupMemberNum, ok := p.roomMemberNumMap[roomID]; ok {
			// 过滤满员和同一连线组中的房间
			if groupMemberNum.Count >= livemulticonnect.GroupOngoingMembersCountLimit ||
				(curMember != nil && groupMemberNum.GroupID == curMember.GroupID) {
				continue
			}
			// 连线中的房间，加入类型为申请，下发 member_num
			itemInfo.MultiConnect.JoinType = livemulticonnect.NotifyGroupJoinTypeApply
			itemInfo.MultiConnect.Status = livemulticonnect.NotifyGroupStatusOngoing
			itemInfo.MultiConnect.MemberNum = &groupMemberNum.Count
		} else {
			// 不在连线中的房间，加入类型为邀请
			itemInfo.MultiConnect.JoinType = livemulticonnect.NotifyGroupJoinTypeInvite
			itemInfo.MultiConnect.Status = livemulticonnect.NotifyGroupStatusFinish
		}
		// 是否 7 天内连线过
		_, ok := connectedRoomIDMap[roomID]
		itemInfo.MultiConnect.RecentConnected = ok
		if match, ok := p.matchingRoomIDMap[r.RoomID]; ok {
			// 正在匹配中的房间，下发 match_id，且不占用推荐主播的数量
			itemInfo.MultiConnect.JoinStatus = livemulticonnect.NotifyGroupJoinStatusPending
			itemInfo.MultiConnect.MatchID = &match.ID
		} else {
			itemInfo.MultiConnect.JoinStatus = livemulticonnect.NotifyGroupJoinStatusDefault
			// 更新列表中推荐主播的数量
			recommendCnt++
		}
		resp.Data = append(resp.Data, itemInfo)
	}
	return resp, nil
}

type recommendListResp struct {
	Data []recommendListItemInfo `json:"data"`
}

type recommendListStatus struct {
	Open      int  `json:"open"`
	Attention bool `json:"attention"`
}

type recommendListStatistics struct {
	Score          int64 `json:"score"`
	AttentionCount int64 `json:"attention_count"`
}

type recommendListItemRoom struct {
	RoomID          int64                   `json:"room_id"`
	Name            string                  `json:"name"`
	CreatorID       int64                   `json:"creator_id"`
	CreatorUsername string                  `json:"creator_username"`
	Status          recommendListStatus     `json:"status"`
	Statistics      recommendListStatistics `json:"statistics"`
	CreatorIconURL  string                  `json:"creator_iconurl"`
}

type recommendListItemMultiConnect struct {
	Status          int    `json:"status"` // 连线状态；1: 连线中；2: 已结束
	RecentConnected bool   `json:"recent_connected"`
	MemberNum       *int   `json:"member_num,omitempty"` // 连线人数（仅在连线中下发）
	JoinType        int    `json:"join_type"`            // 加入类型；1: 申请，2: 邀请
	JoinStatus      int    `json:"join_status"`          // 加入状态；0: 未发起申请或邀请，1: 处理中
	MatchID         *int64 `json:"match_id,omitempty"`   // 连线匹配 ID，仅在处理中下发
}

type recommendListItemInfo struct {
	Room         recommendListItemRoom         `json:"room"`
	MultiConnect recommendListItemMultiConnect `json:"multi_connect"`
}

// ActionMultiConnectRecommendList 推荐连线列表
/**
 * @api {get} /api/v2/chatroom/multi-connect/recommend-list 推荐连线列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room": { // 房间信息
 *             "room_id": 252021449,
 *             "name": "猫猫能有什么坏心眼",
 *             "creator_id": 3457177,
 *             "creator_username": "阿橘_",
 *             "status": {
 *               "open": 1,
 *               "attention": true // 是否关注了主播
 *             },
 *             "statistics": {
 *               "score": 1278,
 *               "attention_count": 100 // 关注数
 *             },
 *             "creator_iconurl": "https://static-test.maoercdn.com/avatars/202102/25/1333d068e833dcfe928e344cf59d7345164741.png",
 *           },
 *           "multi_connect": {
 *             "status": 1, // 连线状态；1: 连线中；2: 已结束
 *             "recent_connected": true, // 是否在最近 7 天连线过
 *             "member_num": 2, // 连线人数（仅在处理中下发）
 *             "join_type": 1, // 加入类型；1: 申请，2: 邀请
 *             "join_status": 1, // 加入状态；0: 未发起申请或邀请，1: 处理中
 *             "match_id": 1 // 连线匹配 ID，仅在处理中下发
 *           }
 *         },
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectRecommendList(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newRecommendListParam(c)
	if err != nil {
		return nil, "", err
	}

	// 检查多人连线是否开放
	isMultiConnectOpen, err := param.isMultiConnectOpen()
	if err != nil {
		return nil, "", err
	}
	if !isMultiConnectOpen {
		return &recommendListResp{Data: make([]recommendListItemInfo, 0)}, "", nil
	}

	// 返回有顺序的房间号，通过 filteredRoomIDMap 进行过滤来保证推荐列表的顺序。
	roomIDs, err := param.findRecommendRoomIDs()
	if err != nil {
		return nil, "", err
	}
	filteredRoomIDMap, err := param.filterRoomIDs(roomIDs)
	if err != nil {
		return nil, "", err
	}

	resp, err := param.resp(roomIDs, filteredRoomIDMap)
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}
