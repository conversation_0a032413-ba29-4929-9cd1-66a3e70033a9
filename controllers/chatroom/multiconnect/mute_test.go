package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewMuteParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, err := newMuteParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":        1,
		"unmute_room_id": 2,
		"group_id":       3,
	})
	_, err = newMuteParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":        1,
		"unmute_room_id": 2, // 这里是静音逻辑，所以不需要 unmute_room_id
		"group_id":       3,
	})
	_, err = newMuteParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      114123,
		"mute_room_id": 31232,
		"group_id":     3,
	})
	_, err = newMuteParam(c, true)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	rs, err := room.FindAll(bson.M{"status.open": 1}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      rs[0].RoomID,
		"mute_room_id": rs[1].RoomID,
		"group_id":     3,
	})
	param, err := newMuteParam(c, true)
	require.NoError(err)
	require.NotNil(param)
	assert.Len(param.roomMap, 2)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":        rs[0].RoomID,
		"unmute_room_id": rs[1].RoomID,
		"group_id":       3,
	})
	param, err = newMuteParam(c, false)
	require.NoError(err)
	require.NotNil(param)
	assert.Len(param.roomMap, 2)
}

func TestMuteParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		groupID = int64(100)
	)
	err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "id = ?", groupID).Error
	require.NoError(err)
	param := &muteParam{
		GroupID: groupID,
		mute:    true,
	}
	err = param.check()
	assert.Equal(actionerrors.ErrNotFound("未查询到正在进行的连线组"), err)

	g := &livemulticonnect.Group{
		ID: groupID,
	}
	err = livemulticonnect.DB().Create(g).Error
	require.NoError(err)
	err = param.check()
	assert.Equal(actionerrors.ErrNotFound("未查询到连线成员"), err)

	rs, err := room.List(bson.M{"status.open": 1}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", groupID).Error
	require.NoError(err)
	members := []*livemulticonnect.GroupMember{
		{
			ID:      100,
			GroupID: groupID,
			RoomID:  rs[0].RoomID,
		},
		{
			ID:      200,
			GroupID: groupID,
			RoomID:  rs[1].RoomID,
		},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param = &muteParam{
		GroupID: groupID,
		roomMap: map[int64]*room.Room{
			rs[0].RoomID:         rs[0],
			rs[1].RoomID + 12312: rs[1],
		},
		mute: true,
	}
	err = param.check()
	assert.Equal(actionerrors.ErrNotFound("未查询到连线成员"), err)

	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMute{}, "group_id = ?", groupID).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(&livemulticonnect.GroupMute{
		GroupID:           groupID,
		GroupMemberID:     100,
		MuteGroupMemberID: 200,
	}).Error
	require.NoError(err)
	param = &muteParam{
		RoomID:     rs[0].RoomID,
		MuteRoomID: rs[1].RoomID,
		GroupID:    groupID,
		roomMap: map[int64]*room.Room{
			rs[0].RoomID: rs[0],
			rs[1].RoomID: rs[1],
		},
		mute: true,
	}
	// 该直播间已被静音
	err = param.check()
	require.NoError(err)

	param.UnmuteRoomID = rs[1].RoomID
	param.mute = false
	err = param.check()
	require.NoError(err)

	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMute{}, "group_id = ?", groupID).Error
	require.NoError(err)
	// 该直播间未被静音
	err = param.check()
	require.NoError(err)

	param.MuteRoomID = rs[1].RoomID
	param.mute = true
	err = param.check()
	require.NoError(err)
}

func TestMuteParam_set(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
		return nil, nil
	})()

	var (
		groupID = int64(100)
	)
	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMute{}, "group_id = ?", groupID).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", groupID).Error
	require.NoError(err)
	rs, err := room.List(bson.M{"status.open": 1}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	members := []*livemulticonnect.GroupMember{
		{
			ID:      100,
			GroupID: groupID,
			RoomID:  rs[0].RoomID,
		},
		{
			ID:      200,
			GroupID: groupID,
			RoomID:  rs[1].RoomID,
		},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)

	param := &muteParam{
		GroupID:    groupID,
		RoomID:     rs[0].RoomID,
		MuteRoomID: rs[1].RoomID,
		mute:       true,
		roomMap: map[int64]*room.Room{
			rs[0].RoomID: rs[0],
			rs[1].RoomID: rs[1],
		},
		group: &livemulticonnect.Group{ID: groupID},
		memberMap: map[int64]*livemulticonnect.GroupMember{
			rs[0].RoomID: members[0],
			rs[1].RoomID: members[1],
		},
	}
	resp, err := param.set()
	require.NoError(err)
	assert.Equal(groupID, resp["group_id"])
	assert.Equal([]int64{rs[1].RoomID}, resp["mute_room_ids"])

	param.UnmuteRoomID = rs[1].RoomID
	param.mute = false
	resp, err = param.set()
	require.NoError(err)
	assert.Equal(groupID, resp["group_id"])
	assert.Empty(resp["mute_room_ids"])
}

func TestActionMultiConnectMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
		return nil, nil
	})()

	var (
		groupID = int64(100)
	)
	err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "id = ?", groupID).Error
	require.NoError(err)
	g := &livemulticonnect.Group{
		ID: groupID,
	}
	err = livemulticonnect.DB().Create(g).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", groupID).Error
	require.NoError(err)
	rs, err := room.ListSimples(bson.M{"status.open": 1}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	members := []*livemulticonnect.GroupMember{
		{
			ID:      100,
			GroupID: groupID,
			RoomID:  rs[0].RoomID,
		},
		{
			ID:      200,
			GroupID: groupID,
			RoomID:  rs[1].RoomID,
		},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMute{}, "group_id = ?", groupID).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":      rs[0].RoomID,
		"mute_room_id": rs[1].RoomID,
		"group_id":     groupID,
	})
	_, _, err = ActionMultiConnectMute(c)
	require.NoError(err)

	muteRoomIDs, err := members[0].MutedRoomIDs()
	require.NoError(err)
	assert.Equal([]int64{rs[1].RoomID}, muteRoomIDs)
}

func TestActionMultiConnectUnMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
		return nil, nil
	})()

	var (
		groupID = int64(100)
	)
	err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "id = ?", groupID).Error
	require.NoError(err)
	g := &livemulticonnect.Group{
		ID: groupID,
	}
	err = livemulticonnect.DB().Create(g).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", groupID).Error
	require.NoError(err)
	rs, err := room.ListSimples(bson.M{"status.open": 1}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	members := []*livemulticonnect.GroupMember{
		{
			ID:      100,
			GroupID: groupID,
			RoomID:  rs[0].RoomID,
		},
		{
			ID:      200,
			GroupID: groupID,
			RoomID:  rs[1].RoomID,
		},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMute{}, "group_id = ?", groupID).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(&livemulticonnect.GroupMute{
		GroupID:           groupID,
		GroupMemberID:     100,
		MuteGroupMemberID: 200,
	}).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":        rs[0].RoomID,
		"unmute_room_id": rs[1].RoomID,
		"group_id":       groupID,
	})
	_, _, err = ActionMultiConnectUnmute(c)
	require.NoError(err)

	muteRoomIDs, err := members[0].MutedRoomIDs()
	require.NoError(err)
	assert.Empty(muteRoomIDs)
}
