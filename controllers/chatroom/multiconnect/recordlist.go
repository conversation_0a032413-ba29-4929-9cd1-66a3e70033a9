package multiconnect

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	showRecordCountLimit = 100 // 连线记录最大查询数量
)

type recordParam struct {
	roomID int64
	userID int64
}

type recordListResp struct {
	Data []*recordDetail `json:"data"`
}

// recordDetail 连线记录详情信息
type recordDetail struct {
	Room         *roomInfo   `json:"room"`          // 主麦直播间信息
	MultiConnect *recordInfo `json:"multi_connect"` // 连线记录信息
}

// roomInfo 连麦房间信息
type roomInfo struct {
	RoomID          int64          `json:"room_id"`
	Name            string         `json:"name"`
	CreatorID       int64          `json:"creator_id"`
	CreatorUsername string         `json:"creator_username"`
	CreatorIconURL  string         `json:"creator_iconurl"`
	Status          roomStatusInfo `json:"status"`
}

// roomStatusInfo 直播间状态信息
type roomStatusInfo struct {
	Open int `json:"open"` // 是否正在直播：0 否，1 是
}

// recordInfo 连线记录详情信息
type recordInfo struct {
	Role      int   `json:"role"`       // 角色，1：主麦，2：成员
	StartTime int64 `json:"start_time"` // 连线开始时间，单位：秒
	Duration  int64 `json:"duration"`   // 连线时长，单位：毫秒
}

// ActionMultiConnectRecordList 连线记录
/**
 * @api {get} /api/v2/chatroom/multi-connect/record-list 连线记录
 * @apiDescription 展示最近 100 次连线记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room": { // 房间信息
 *             "room_id": 152,
 *             "name": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *             "status": {
 *               "open": 1
 *             }
 *           },
 *           "multi_connect": {
 *             "role": 1, // 角色；1: 主麦；2: 连线成员
 *             "start_time": 123456789, // 连线开始时间，单位：秒
 *             "duration": 123456 // 连线时长，单位：毫秒
 *           }
 *         },
 *         ...
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectRecordList(c *handler.Context) (handler.ActionResponse, string, error) {
	param := &recordParam{userID: c.UserID()}
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	param.roomID = roomID
	// 查询直播间信息并验证权限
	if err = param.checkRoomInfo(); err != nil {
		return nil, "", err
	}
	// 获取连线记录详情列表
	recordDetailList, err := param.fetchRecordDetailList()
	if err != nil {
		return nil, "", err
	}
	return recordListResp{
		Data: recordDetailList,
	}, "", nil
}

// checkRoomInfo 验证直播间权限
func (p *recordParam) checkRoomInfo() error {
	roomInfo, err := room.Find(p.roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if roomInfo == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if roomInfo.CreatorID != p.userID {
		return actionerrors.ErrForbidden
	}
	return nil
}

// fetchRecordDetailList 获取连线记录详情列表
func (p *recordParam) fetchRecordDetailList() ([]*recordDetail, error) {
	records, err := livemulticonnect.FindMemberRecentRecords(p.roomID, showRecordCountLimit)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(records) == 0 {
		return make([]*recordDetail, 0), nil
	}
	// 获取主麦成员和其直播间的映射关系
	groupOwnerMemberRoomMap, err := getGroupOwnerMemberRoomMap(records)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return buildRecordDetailList(groupOwnerMemberRoomMap, records), nil
}

// getGroupOwnerMemberRoomMap 获取主麦成员和其直播间的映射关系
func getGroupOwnerMemberRoomMap(records []*livemulticonnect.Record) (map[int64]*room.Room, error) {
	// 先根据连线记录中的主麦成员 ID 查询成员信息
	groupOwnerMemberIDs := goutil.SliceMap(records, func(record *livemulticonnect.Record) int64 {
		return record.GroupOwnerMemberID
	})
	groupOwnerMembers, err := livemulticonnect.FindMembers(sets.Uniq(groupOwnerMemberIDs))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 再根据主麦成员信息中的直播间 ID 获取到直播间信息，主要包括头像、直播状态等
	groupOwnerMemberRoomIDs := goutil.SliceMap(groupOwnerMembers, func(groupMember *livemulticonnect.GroupMember) int64 {
		return groupMember.RoomID
	})
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": sets.Uniq(groupOwnerMemberRoomIDs)}}, nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 直播间 ID 和直播间信息的映射关系
	roomMap := util.ToMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})
	// 主麦成员 ID 和其直播间信息的映射关系
	groupOwnerMemberRoomMap := make(map[int64]*room.Room, len(groupOwnerMembers))
	for _, g := range groupOwnerMembers {
		groupOwnerMemberRoomMap[g.ID] = roomMap[g.RoomID]
	}
	return groupOwnerMemberRoomMap, nil
}

// buildRecordDetailList 构建返回给前端的连线记录详情列表数据
func buildRecordDetailList(groupOwnerMemberRoomMap map[int64]*room.Room, records []*livemulticonnect.Record) []*recordDetail {
	recordDetailList := make([]*recordDetail, 0, len(records))
	for _, r := range records {
		roomData, ok := groupOwnerMemberRoomMap[r.GroupOwnerMemberID]
		if !ok {
			// 主麦房间信息不存在，打印 error 日志
			logger.WithField("group_owner_member_id", r.GroupOwnerMemberID).Error("not found room")
			continue
		}
		roomInfoInstance := &roomInfo{
			RoomID:          roomData.RoomID,
			Name:            roomData.Name,
			CreatorID:       roomData.CreatorID,
			CreatorUsername: roomData.CreatorUsername,
			CreatorIconURL:  roomData.CreatorIconURL,
			Status: roomStatusInfo{
				Open: roomData.Status.Open,
			},
		}
		role := livemulticonnect.MemberRoleMember
		if r.GroupOwnerMemberID == r.GroupMemberID {
			role = livemulticonnect.MemberRoleOwner
		}
		recordInfoInstance := &recordInfo{
			Role:      role,
			StartTime: r.StartTime / 1000, // 毫秒转成秒
			Duration:  r.Duration,
		}
		recordDetailInstance := &recordDetail{
			Room:         roomInfoInstance,
			MultiConnect: recordInfoInstance,
		}
		recordDetailList = append(recordDetailList, recordDetailInstance)
	}
	return recordDetailList
}
