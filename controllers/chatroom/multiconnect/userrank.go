package multiconnect

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionMultiConnectUserRank 查询直播间所在连线组的在线用户榜单
/**
 * @api {get} /api/v2/chatroom/multi-connect/user/rank 查询直播间所在连线组的在线用户榜单
 * @apiDescription 获取直播间所在连线组的用户收听排行榜，展示在线用户的前 100 名，按总积分降序和用户等级降序排序，暂只给主播开放。
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "rank": 1,
 *           "user_id": 12345,
 *           "username": "用户名 12345",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "titles": [
 *             {
 *               "type": "noble",
 *               "name": "练习生",
 *               "level": 1
 *             },
 *             {
 *               "type": "highness",
 *               "name": "上神",
 *               "level": 1
 *             },
 *             {
 *               "type": "avatar_frame",
 *               "icon_url": "https://static-test.maoercdn.com/live/avatarframes/40335.webp"
 *             }
 *           ],
 *           "multi_connect": {
 *             "index": 1 // 用户所在直播间的主播麦序。如果用户在多个直播间内，优先返回当前主播的麦序，如果不在当前直播间内，则返回所在直播间最小的麦序
 *           }
 *         },
 *         {
 *           "rank": 2,
 *           "user_id": 23456,
 *           "username": "用户名 23456",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "titles": [
 *             {
 *               "type": "avatar_frame",
 *               "icon_url": "https://static-test.maoercdn.com/live/avatarframes/40335.webp"
 *             },
 *             {
 *               "type": "badge",
 *               "icon_url": "https://static-test.maoercdn.com/live/badges/50281.webp",
 *               "appearance_id": 50281
 *             },
 *             {
 *               "type": "badge",
 *               "icon_url": "https://static-test.maoercdn.com/live/badges/50310.png",
 *               "appearance_id": 50282
 *             }
 *           ],
 *           "multi_connect": {
 *             "index": 1
 *           }
 *         }
 *       ]
 *     }
 *   }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 */
func ActionMultiConnectUserRank(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// ActionMultiConnectUserRankOn 显示主播连线组的在线用户榜单
/**
 * @api {post} /api/v2/chatroom/multi-connect/user/rank/on 显示主播连线组的在线用户榜单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 显示主播连线组在线用户榜单的消息
 *   {
 *     "type": "multi_connect",
 *     "event": "rank_on",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectUserRankOn(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// ActionMultiConnectUserRankOff 隐藏主播连线组的在线用户榜单
/**
 * @api {post} /api/v2/chatroom/multi-connect/user/rank/off 隐藏主播连线组的在线用户榜单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 隐藏主播连线组在线用户榜单的消息
 *   {
 *     "type": "multi_connect",
 *     "event": "rank_off",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectUserRankOff(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
