package multiconnect

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type settingsResp struct {
	DisableInvite           int `json:"disable_invite"`
	DisableUnfollowedInvite int `json:"disable_unfollowed_invite"`
	DisableApply            int `json:"disable_apply"`
}

// ActionMultiConnectSettingsGet 连线设置信息
/**
 * @api {get} /api/v2/chatroom/multi-connect/settings/get 连线设置信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "disable_invite": 0, // 是否允许连线邀请；0：允许；1：不允许
 *       "disable_unfollowed_invite": 0, // 是否接受未关注人的连线邀请；0：接受；1：不接受
 *       "disable_apply": 0 // 是否允许其他主播申请加入连线；0: 允许；1：不允许
 *     }
 *   }
 */
func ActionMultiConnectSettingsGet(c *handler.Context) (handler.ActionResponse, string, error) {
	var param struct {
		RoomID int64 `form:"room_id"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if !r.IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	settings, err := livemeta.FindMultiConnectSettings(r.RoomID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	// 若直播间信息不存在，则返回默认设置
	if settings == nil {
		settings = new(livemeta.MultiConnectSettings)
	}
	return &settingsResp{
		DisableInvite:           goutil.BoolToInt(!settings.IsEnableInvite()),
		DisableUnfollowedInvite: goutil.BoolToInt(!settings.IsEnableUnfollowedInvite()),
		DisableApply:            goutil.BoolToInt(!settings.IsEnableApply()),
	}, "", nil
}

// ActionMultiConnectSettingsSet 连线设置修改
/**
 * @api {post} /api/v2/chatroom/multi-connect/settings/set 连线设置修改
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {number=0,1} [disable_invite] 是否允许连线邀请；0：允许；1：不允许
 * @apiParam {number=0,1} [disable_unfollowed_invite] 是否接受未关注人的连线邀请；0：接受；1：不接受
 * @apiParam {number=0,1} [disable_apply] 是否允许其他主播申请加入连线；0: 允许；1：不允许
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "disable_invite": 0, // 是否允许连线邀请；0：允许；1：不允许
 *       "disable_unfollowed_invite": 0, // 是否接受未关注人的连线邀请；0：接受；1：不接受
 *       "disable_apply": 0 // 是否允许其他主播申请加入连线；0: 允许；1：不允许
 *     }
 *   }
 */
func ActionMultiConnectSettingsSet(c *handler.Context) (handler.ActionResponse, string, error) {
	var param struct {
		RoomID                  int64 `form:"room_id" json:"room_id"`
		DisableInvite           *int  `form:"disable_invite" json:"disable_invite"`
		DisableUnfollowedInvite *int  `form:"disable_unfollowed_invite" json:"disable_unfollowed_invite"`
		DisableApply            *int  `form:"disable_apply" json:"disable_apply"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if !r.IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	settings, err := livemeta.FindMultiConnectSettings(r.RoomID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if settings == nil {
		return nil, "", actionerrors.ErrNotFound("未查询到直播间信息")
	}
	if (param.DisableInvite != nil && *param.DisableInvite == 1) || (param.DisableInvite == nil && !settings.IsEnableInvite()) {
		// 已禁止连线邀请，无法设置接受未关注人连线邀请
		if param.DisableUnfollowedInvite != nil && *param.DisableUnfollowedInvite == 0 {
			return nil, "", actionerrors.NewErrForbidden("已禁止连线邀请，无法接受未关注人连线邀请")
		}
	}

	settings = &livemeta.MultiConnectSettings{
		DisableInvite:           param.DisableInvite,
		DisableUnfollowedInvite: param.DisableUnfollowedInvite,
		DisableApply:            param.DisableApply,
	}
	settings, err = settings.Update(r.RoomID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	return &settingsResp{
		DisableInvite:           goutil.BoolToInt(!settings.IsEnableInvite()),
		DisableUnfollowedInvite: goutil.BoolToInt(!settings.IsEnableUnfollowedInvite()),
		DisableApply:            goutil.BoolToInt(!settings.IsEnableApply()),
	}, "", nil
}
