package multiconnect

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type scoreClearParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GroupID int64 `form:"group_id" json:"group_id"`
}

// ActionMultiConnectScoreClear 清除礼物积分
/**
 * @api {post} /api/v2/chatroom/multi-connect/score/clear 清除礼物积分
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 清除礼物积分连线直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "score_clear",
 *     "room_id": 1,
 *     "multi_connect": {
 *       "group_id": 1, // 连线组 ID
 *     }
 *   }
 */
func ActionMultiConnectScoreClear(c *handler.Context) (handler.ActionResponse, string, error) {
	var param *scoreClearParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	if param.RoomID == 0 || param.GroupID == 0 {
		return nil, "", actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if !r.IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	group, err := livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if group == nil {
		return nil, "", actionerrors.ErrMultiConnectGroupNotFound
	}
	members, err := group.Members(livemulticonnect.DB())
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(members) < 2 {
		return nil, "", actionerrors.NewErrForbidden("当前连线已结束")
	}
	if members[0].RoomID != param.RoomID {
		return nil, "", actionerrors.NewErrForbidden("仅主麦可清除礼物积分")
	}

	// 清除礼物积分
	err = group.ClearScore()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	err = userapi.BroadcastMany(livemulticonnect.NewScoreClearBroadcastPayloadElems(members))
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil, "success", nil
}
