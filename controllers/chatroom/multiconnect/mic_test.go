package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewMicParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  1,
		"group_id": 1,
	})
	param, err := newMicParam(c, true)
	require.NoError(err)
	require.NotNil(param)
	assert.NotZero(param.RoomID)
	assert.NotZero(param.GroupID)
}

func TestMicParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.FindOne(bson.M{"status.open": 1})
	require.NoError(err)
	require.NotNil(r)
	group := &livemulticonnect.Group{
		ID: 131312312,
	}
	err = livemulticonnect.DB().Delete(group).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(group).Error
	require.NoError(err)
	members := []*livemulticonnect.GroupMember{
		{
			GroupID: group.ID,
			RoomID:  r.RoomID,
		},
		{
			GroupID: group.ID,
			RoomID:  r.RoomID + 1,
		},
	}
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", group.ID).Error
	require.NoError(err)
	err = servicedb.BatchInsert(livemulticonnect.DB(), members[0].TableName(), members)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  r.RoomID,
		"group_id": group.ID,
	})
	param, err := newMicParam(c, true)
	require.NoError(err)
	require.NotNil(param)

	err = param.check()
	require.NoError(err)
	assert.NotNil(param.group)
	assert.NotNil(param.members)
	assert.NotNil(param.member)
}

func TestMicParam_setMicAndBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) { return "success", nil })()

	var (
		testGroupID = int64(123211)
	)
	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMicOff{}, "group_id = ?", testGroupID).Error
	require.NoError(err)
	members := []*livemulticonnect.GroupMember{
		{
			ID:      4121,
			GroupID: testGroupID,
			RoomID:  312312,
		},
		{
			ID:      4122,
			GroupID: testGroupID,
			RoomID:  312313,
		},
	}
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", testGroupID).Error
	require.NoError(err)
	err = servicedb.BatchInsert(livemulticonnect.DB(), members[0].TableName(), members)
	require.NoError(err)

	param := &micParam{
		micOff: true,
		group: &livemulticonnect.Group{
			ID: testGroupID,
		},
		members: members,
		member:  members[0],
	}
	resp, err := param.setMicAndBroadcast()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal([]int64{312312}, resp.MicOffRoomIDs)

	param.micOff = false
	resp, err = param.setMicAndBroadcast()
	require.NoError(err)
	require.NotNil(resp)
	assert.Empty(resp.MicOffRoomIDs)
}
