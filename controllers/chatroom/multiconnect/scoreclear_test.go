package multiconnect

import (
	"net/http"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionMultiConnectScoreClear(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock("im://broadcast/many", func(any) (any, error) {
		return nil, nil
	})()

	rs, err := room.List(bson.M{}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rs, 2)
	err = livemulticonnect.DB().Delete(livemulticonnect.Group{}).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)
	group := &livemulticonnect.Group{
		ID: 10099,
	}
	err = livemulticonnect.DB().Create(group).Error
	require.NoError(err)
	members := []*livemulticonnect.GroupMember{
		{
			GroupID: group.ID,
			RoomID:  rs[0].RoomID,
		},
		{
			GroupID: group.ID,
			RoomID:  rs[1].RoomID,
		},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), new(livemulticonnect.GroupMember).TableName(), members)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/score/clear", true, handler.M{
		"room_id":  rs[1].RoomID,
		"group_id": group.ID,
	})
	c.User().ID = rs[1].CreatorID
	_, _, err = ActionMultiConnectScoreClear(c)
	require.Equal(actionerrors.NewErrForbidden("仅主麦可清除礼物积分"), err)

	key := keys.KeyMultiConnectGroupGiftScore1.Format(group.ID)
	err = service.Redis.ZAdd(key, &redis.Z{Score: 1, Member: "1"}).Err()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/multi-connect/score/clear", true, handler.M{
		"room_id":  rs[0].RoomID,
		"group_id": group.ID,
	})
	c.User().ID = rs[0].CreatorID
	_, _, err = ActionMultiConnectScoreClear(c)
	require.NoError(err)
	assert.Zero(service.Redis.Exists(key).Val())
}
