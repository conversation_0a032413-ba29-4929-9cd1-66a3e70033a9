package multiconnect

import (
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	interval14Days     = time.Hour * 24 * 14
	recentMembersLimit = 50
)

type recentListParam struct {
	RoomID int64 `form:"room_id"`

	uc                mrpc.UserContext
	userID            int64
	roomMap           map[int64]*room.Room                       // 最近连线过房间信息的集合
	roomMemberNumMap  map[int64]*livemulticonnect.GroupMemberNum // 最近连线过的直播间当前进行中的连线组的人数
	matchingRoomIDMap map[int64]*livemulticonnect.Match          // 匹配处理中的房间集合
}

func newRecentListParam(c *handler.Context) (*recentListParam, error) {
	var param recentListParam
	if err := c.Bind(&param); err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.uc = c.UserContext()
	param.userID = c.UserID()
	roomID, err := room.FindRoomID(param.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.RoomID != roomID {
		return nil, actionerrors.ErrForbidden
	}
	return &param, nil
}

// recentMembers 获取过滤后的最近的成员列表
func (p *recentListParam) getRecentMembers() ([]*livemulticonnect.GroupMember, error) {
	// 获取最近加入过的组的成员列表
	members, err := livemulticonnect.FindRecentConnectedMembers(p.RoomID, interval14Days, true)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(members) == 0 {
		return nil, nil
	}
	roomIDs := goutil.SliceMap(members, func(m *livemulticonnect.GroupMember) int64 {
		return m.RoomID
	})

	// 查询在直播中的房间信息
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": roomIDs}, "status.open": room.StatusOpenTrue}, nil,
		&room.FindOptions{FindCreator: true, FindFans: true, ListenerID: p.userID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) == 0 {
		return nil, nil
	}
	roomIDs = goutil.SliceMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})

	// 查询连线组当前进行中的人数情况，过滤出连线组已满员的房间
	p.roomMemberNumMap, err = livemulticonnect.FindMemberNumByRoomIDs(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	fullRoomIDs := make([]int64, 0, len(roomIDs))
	for _, m := range members {
		if groupMemberNum, ok := p.roomMemberNumMap[m.RoomID]; ok {
			if groupMemberNum.Count >= livemulticonnect.GroupOngoingMembersCountLimit {
				fullRoomIDs = append(fullRoomIDs, m.RoomID)
			}
		}
	}
	roomIDs = sets.Diff(roomIDs, fullRoomIDs)
	if len(roomIDs) == 0 {
		return nil, nil
	}

	// 过滤正在听众连麦的房间
	connectingRoomIDs, err := liveconnect.ConnectingRoomIDs(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs = sets.Diff(roomIDs, connectingRoomIDs)
	if len(roomIDs) == 0 {
		return nil, nil
	}

	// 过滤正在 PK 流程中的房间
	pkRoomIDs, err := livepk.OngoingPKRoomIDs(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs = sets.Diff(roomIDs, pkRoomIDs)
	if len(roomIDs) == 0 {
		return nil, nil
	}

	// 过滤存在连线拉黑关系的房间
	blockedRoomIDs, blockedCurrentRoomIDs, err := livemulticonnect.BlockedRoomIDs(p.RoomID, roomIDs...)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs = sets.Diff(roomIDs, append(blockedRoomIDs, blockedCurrentRoomIDs...))
	if len(roomIDs) == 0 {
		return nil, nil
	}

	// 获取账号间的拉黑关系，按用户号进行过滤
	roomMap := util.ToMap(rooms, func(m *room.Room) int64 {
		return m.RoomID
	})
	creatorIDs := make([]int64, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		if r, ok := roomMap[roomID]; ok {
			creatorIDs = append(creatorIDs, r.CreatorID)
		}
	}
	blockUserList, userBlockList, err := userapi.UserBlockStatusList(p.uc, p.userID, creatorIDs)
	if err != nil {
		// 直接返回 mrpc 返回的 error
		return nil, err
	}
	blockUserIDMap := make(map[int64]struct{}, len(blockUserList)+len(userBlockList))
	for _, blockUserID := range append(blockUserList, userBlockList...) {
		blockUserIDMap[blockUserID] = struct{}{}
	}

	// 最终过滤后的房间号集合
	p.roomMap = make(map[int64]*room.Room, len(roomIDs))
	for _, roomID := range roomIDs {
		if r, ok := roomMap[roomID]; ok {
			if _, blocked := blockUserIDMap[r.CreatorID]; !blocked {
				p.roomMap[roomID] = r
			}
		}
	}

	// 通过房间信息再次过滤成员列表
	idx := 0
	for _, m := range members {
		if _, ok := p.roomMap[m.RoomID]; ok {
			members[idx] = m
			idx++
		}
	}
	return members[:idx], nil
}

// getMatchingRoomIDs 获取当前房间匹配中的房间
func (p *recentListParam) getPendingMatches() error {
	matchingRoomIDMap, err := livemulticonnect.FindMatchingRoomIDMap(p.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.matchingRoomIDMap = matchingRoomIDMap
	return nil
}

// resp 构建接口返回的内容
func (p *recentListParam) resp() (*recentListResp, error) {
	resp := &recentListResp{Data: make([]recentListItemInfo, 0)}

	// 获取当前房间最近连线过的成员
	members, err := p.getRecentMembers()
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return resp, nil
	}
	resp.Data = make([]recentListItemInfo, 0, len(members))

	// 获取当前处理中的匹配
	err = p.getPendingMatches()
	if err != nil {
		return nil, err
	}

	for _, member := range members {
		r := p.roomMap[member.RoomID]
		itemInfo := recentListItemInfo{
			Room: recentListItemRoom{
				RoomID:          r.RoomID,
				Name:            r.Name,
				CreatorID:       r.CreatorID,
				CreatorUsername: r.CreatorUsername,
				Status: recentListStatus{
					Open:      r.Status.Open,
					Attention: r.Status.Attention,
				},
				Statistics: recentListStatistics{
					Score:          r.Statistics.Score,
					AttentionCount: r.Statistics.AttentionCount,
				},
				CreatorIconURL: r.CreatorIconURL,
			},
			MultiConnect: recentListItemMultiConnect{
				RecentConnected: true,
			},
			sort: member.Sort,
		}

		if groupMemberNum, ok := p.roomMemberNumMap[member.RoomID]; ok {
			// 连线中的房间，加入类型为申请，下发 member_num
			itemInfo.MultiConnect.JoinType = livemulticonnect.NotifyGroupJoinTypeApply
			itemInfo.MultiConnect.Status = livemulticonnect.NotifyGroupStatusOngoing
			itemInfo.MultiConnect.MemberNum = &groupMemberNum.Count
		} else {
			// 不在连线中的房间，加入类型为邀请
			itemInfo.MultiConnect.JoinType = livemulticonnect.NotifyGroupJoinTypeInvite
			itemInfo.MultiConnect.Status = livemulticonnect.NotifyGroupStatusFinish
		}

		// 正在匹配中的房间，下发 match_id
		if match, ok := p.matchingRoomIDMap[r.RoomID]; ok {
			itemInfo.MultiConnect.JoinStatus = livemulticonnect.NotifyGroupJoinStatusPending
			itemInfo.MultiConnect.MatchID = &match.ID
		} else {
			itemInfo.MultiConnect.JoinStatus = livemulticonnect.NotifyGroupJoinStatusDefault
		}
		resp.Data = append(resp.Data, itemInfo)
	}
	resp.sort()
	if len(resp.Data) > recentMembersLimit {
		resp.Data = resp.Data[:recentMembersLimit]
	}
	return resp, nil
}

type recentListResp struct {
	Data []recentListItemInfo `json:"data"`
}

func (r *recentListResp) sort() {
	// 按分组排序后，同一场连线组中的主播按粉丝量和热度进行排序
	sort.Slice(r.Data, func(i, j int) bool {
		if r.Data[i].sort != r.Data[j].sort {
			return r.Data[i].sort > r.Data[j].sort
		}
		if r.Data[i].Room.Statistics.AttentionCount != r.Data[j].Room.Statistics.AttentionCount {
			return r.Data[i].Room.Statistics.AttentionCount > r.Data[j].Room.Statistics.AttentionCount
		}
		if r.Data[i].Room.Statistics.Score != r.Data[j].Room.Statistics.Score {
			return r.Data[i].Room.Statistics.Score > r.Data[j].Room.Statistics.Score
		}
		return r.Data[i].Room.RoomID > r.Data[j].Room.RoomID
	})
}

type recentListStatus struct {
	Open      int  `json:"open"`
	Attention bool `json:"attention"`
}

type recentListStatistics struct {
	Score          int64 `json:"score"`
	AttentionCount int64 `json:"attention_count"`
}

type recentListItemRoom struct {
	RoomID          int64                `json:"room_id"`
	Name            string               `json:"name"`
	CreatorID       int64                `json:"creator_id"`
	CreatorUsername string               `json:"creator_username"`
	Status          recentListStatus     `json:"status"`
	Statistics      recentListStatistics `json:"statistics"`
	CreatorIconURL  string               `json:"creator_iconurl"`
}

type recentListItemMultiConnect struct {
	Status          int    `json:"status"` // 连线状态；1: 连线中；2: 已结束
	RecentConnected bool   `json:"recent_connected"`
	MemberNum       *int   `json:"member_num,omitempty"` // 连线人数（仅在连线中下发）
	JoinType        int    `json:"join_type"`            // 加入类型；1: 申请，2: 邀请
	JoinStatus      int    `json:"join_status"`          // 加入状态；0: 未发起申请或邀请，1: 处理中
	MatchID         *int64 `json:"match_id,omitempty"`   // 连线匹配 ID，仅在处理中下发
}

type recentListItemInfo struct {
	Room         recentListItemRoom         `json:"room"`
	MultiConnect recentListItemMultiConnect `json:"multi_connect"`

	sort int64
}

// ActionMultiConnectRecentList 最近连线列表
/**
 * @api {get} /api/v2/chatroom/multi-connect/recent-list 最近连线列表
 * @apiDescription 最近 14 天跟我连线过的主播，最多展示 50 个
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room": {
 *             "room_id": 252021449,
 *             "name": "猫猫能有什么坏心眼",
 *             "creator_id": 3457177,
 *             "creator_username": "阿橘_",
 *             "status": {
 *               "open": 1,
 *               "attention": true // 是否关注了主播
 *             },
 *             "statistics": {
 *               "score": 1278,
 *               "attention_count": 100 // 关注数
 *             },
 *             "creator_iconurl": "https://static-test.maoercdn.com/avatars/202102/25/1333d068e833dcfe928e344cf59d7345164741.png",
 *           },
 *           "multi_connect": {
 *             "status": 1, // 连线状态；1: 连线中；2: 已结束
 *             "recent_connected": true, // 是否在最近 14 天连线过
 *             "member_num": 2, // 连线人数（仅在连线中下发）
 *             "join_type": 1, // 加入类型；1: 申请，2: 邀请
 *             "join_status": 1, // 加入状态；0: 未发起申请或邀请，1: 处理中
 *             "match_id": 1 // 连线匹配 ID，仅在处理中下发
 *           }
 *         },
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectRecentList(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newRecentListParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.resp()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}
