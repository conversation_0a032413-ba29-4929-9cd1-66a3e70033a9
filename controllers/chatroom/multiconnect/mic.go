package multiconnect

import (
	"slices"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type micParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GroupID int64 `form:"group_id" json:"group_id"`

	micOff  bool
	group   *livemulticonnect.Group
	members []*livemulticonnect.GroupMember
	member  *livemulticonnect.GroupMember
}

type micResp struct {
	GroupID       int64   `json:"group_id"`
	MicOffRoomIDs []int64 `json:"mic_off_room_ids"`
}

func newMicParam(c *handler.Context, micOff bool) (*micParam, error) {
	param := &micParam{micOff: micOff}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GroupID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *micParam) check() error {
	r, err := room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	param.group, err = livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}
	param.members, err = param.group.Members(nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	index := slices.IndexFunc(param.members, func(m *livemulticonnect.GroupMember) bool {
		return m.RoomID == param.RoomID
	})
	if index < 0 {
		return actionerrors.ErrMultiConnectGroupMemberModified
	}
	param.member = param.members[index]
	return nil
}

func (param *micParam) setMicAndBroadcast() (*micResp, error) {
	if param.micOff {
		err := param.member.MicOff()
		if err != nil {
			if !servicedb.IsUniqueError(err) {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			// PASS: 忽略重复闭麦的场景
		}
	} else {
		err := param.member.MicOn()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	roomIDs, err := param.group.MicOffRoomIDs()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	err = userapi.BroadcastMany(livemulticonnect.NewMicOffBroadcastPayload(param.group.ID, param.members, roomIDs))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return &micResp{
		GroupID:       param.group.ID,
		MicOffRoomIDs: roomIDs,
	}, nil
}

// ActionMultiConnectMicOff 主播连线闭麦
/**
 * @api {post} /api/v2/chatroom/multi-connect/mic-off 主播连线闭麦
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "group_id": 1, // 连线组 ID
 *       "mic_off_room_ids": [23, 223344] // 闭麦直播间 ID 列表
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 全部连线成员直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "mic_off",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "mic_off_room_ids": [23, 223344] // 闭麦直播间 ID 列表
 *     }
 *   }
 */
func ActionMultiConnectMicOff(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newMicParam(c, true)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.setMicAndBroadcast()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

// ActionMultiConnectMicOn 主播连线开麦
/**
 * @api {post} /api/v2/chatroom/multi-connect/mic-on 主播连线开麦
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "group_id": 1, // 连线组 ID
 *       "mic_off_room_ids": [23] // 闭麦直播间 ID 列表
 *     }
 *
 * @apiSuccessExample {json} WebSocket 全部连线成员直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "mic_off",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "mic_off_room_ids": [23] // 闭麦直播间 ID 列表
 *     }
 *   }
 */
func ActionMultiConnectMicOn(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newMicParam(c, false)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.setMicAndBroadcast()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}
