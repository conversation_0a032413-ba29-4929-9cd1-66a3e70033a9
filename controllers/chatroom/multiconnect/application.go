package multiconnect

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/connectcheck"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect/databus"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type applicationRequestParam struct {
	RoomID   int64 `form:"room_id" json:"room_id"`
	ToRoomID int64 `form:"to_room_id" json:"to_room_id"`

	memberNum int

	fromRoom  *room.Room
	toRoom    *room.Room
	ownerRoom *room.Room
	group     *livemulticonnect.Group
	match     *livemulticonnect.Match
	uc        mrpc.UserContext
}

// ActionMultiConnectApplicationRequest 申请连线请求
/**
 * @api {post} /api/v2/chatroom/multi-connect/application/request 申请连线请求
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} to_room_id 申请加入连线的直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "multi_connect": {
 *         "match_id": 1,
 *         "remain_duration": 10000, // 申请等待倒计时，单位：毫秒
 *         "create_time": 1584808200, // 单位秒
 *         "to_room": { // 处理人的直播间信息
 *           "room_id": 654321,
 *           "name": "123456",
 *           "creator_id": 13,
 *           "creator_username": "test13",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *           "statistics": {
 *             "score": 100, // 直播间热度
 *             "attention_count": 100 // 关注数
 *           }
 *         }
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主麦消息
 *   {
 *     "type": "multi_connect",
 *     "event": "apply_request",
 *     "room_id": 1,
 *     "user_id": 13,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "remain_duration": 10000, // 申请等待倒计时，单位：毫秒
 *       "create_time": 1584808200, // 单位秒
 *       "recent_connected": true, // 是否在最近 7 天连线过
 *       "member_num": 2, // 当前正在连线的人数
 *       "from_room": { // 申请方发起方直播间信息
 *         "room_id": 123456,
 *         "name": "123456",
 *         "creator_id": 13,
 *         "creator_username": "test13",
 *         "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *         "status": {
 *           "attention": true // 是否关注了主播
 *         },
 *         "statistics": {
 *           "score": 100, // 直播间热度
 *           "attention_count": 100 // 关注数
 *         }
 *       }
 *     }
 *   }
 */
func ActionMultiConnectApplicationRequest(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newApplicationRequestParam(c)
	if err != nil {
		return nil, "", err
	}
	if err := param.check(); err != nil {
		return nil, "", err
	}
	if err := param.addConnectMatch(); err != nil {
		return nil, "", err
	}
	param.sendTimeoutQueue()
	param.sendMessage()
	return param.resp(), "", nil
}

func newApplicationRequestParam(c *handler.Context) (*applicationRequestParam, error) {
	param := new(applicationRequestParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.ToRoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID == param.ToRoomID {
		return nil, actionerrors.NewErrForbidden("无法对自己操作")
	}

	param.fromRoom, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true, FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.fromRoom == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if c.UserID() != param.fromRoom.CreatorID {
		return nil, actionerrors.NewErrForbidden("无法替其他主播发起申请")
	}

	param.toRoom, err = room.Find(param.ToRoomID, &room.FindOptions{FindCreator: true, FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.toRoom == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if !param.toRoom.IsOpen() {
		return nil, actionerrors.NewErrForbidden("主播不在线，无法操作")
	}

	members, err := livemulticonnect.FindOngoingMembers([]int64{param.RoomID, param.ToRoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	memberMap := util.ToMap(members, func(member *livemulticonnect.GroupMember) int64 {
		return member.RoomID
	})
	_, ok := memberMap[param.RoomID]
	if ok {
		return nil, actionerrors.NewErrForbidden("当前正在主播连线中，无法操作")
	}
	toRoomMember, ok := memberMap[param.ToRoomID]
	if !ok {
		return nil, actionerrors.NewErrForbidden("该连线已结束，无法操作")
	}

	param.group, err = livemulticonnect.FindOngoingGroup(toRoomMember.GroupID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return nil, actionerrors.ErrMultiConnectGroupNotFound
	}

	param.uc = c.UserContext()

	// 接受方正好是主麦，不用在查一次主麦信息
	if toRoomMember.Role == livemulticonnect.MemberRoleOwner {
		param.ownerRoom = param.toRoom
		return param, nil
	}
	// 查找当前连线组的主麦
	owner, err := param.group.Owner()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.ownerRoom, err = room.Find(owner.RoomID, &room.FindOptions{FindCreator: true, FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return param, nil
}

func (param *applicationRequestParam) check() error {
	matchList, err := livemulticonnect.FindPendingMatches([]int64{param.fromRoom.RoomID, param.ownerRoom.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, match := range matchList {
		if match.FromRoomID == param.fromRoom.RoomID && match.Type == livemulticonnect.MatchTypeInvite {
			return actionerrors.NewErrForbidden("已邀请其他主播连线，无法发起连线申请")
		}
		if match.FromRoomID == param.fromRoom.RoomID && match.Type == livemulticonnect.MatchTypeApply {
			return actionerrors.NewErrForbidden("不能同时申请 2 个连线")
		}
		if match.ToRoomID == param.ownerRoom.RoomID && match.Type == livemulticonnect.MatchTypeApply {
			return actionerrors.NewErrForbidden("对方处理其他申请中，请稍后再试")
		}
	}

	blockedByRoomIDs, whoBlockedRoomIDs, err := livemulticonnect.BlockedRoomIDs(param.fromRoom.RoomID,
		param.toRoom.RoomID, param.ownerRoom.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, blockedByRoomID := range blockedByRoomIDs {
		if blockedByRoomID == param.toRoom.RoomID {
			return actionerrors.NewErrForbidden("您已将该主播连线拉黑，无法操作")
		}
		if blockedByRoomID == param.ownerRoom.RoomID {
			return actionerrors.NewErrForbidden("您已将该连线的主麦连线拉黑，无法操作")
		}
	}

	for _, whoBlockedRoomID := range whoBlockedRoomIDs {
		if whoBlockedRoomID == param.toRoom.RoomID {
			return actionerrors.NewErrForbidden("由于对方设置，无法操作")
		}
		if whoBlockedRoomID == param.ownerRoom.RoomID {
			return actionerrors.NewErrForbidden("由于对方主麦设置，无法操作")
		}
	}

	blockedUser, err := blocklist.IsBlocked(param.fromRoom.CreatorID, param.toRoom.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blockedUser {
		return actionerrors.NewErrForbidden("您已拉黑对方，无法操作")
	}
	blockedUser, err = blocklist.IsBlocked(param.fromRoom.CreatorID, param.ownerRoom.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blockedUser {
		return actionerrors.NewErrForbidden("您已将该连线的主麦拉黑，无法操作")
	}

	// TODO: 增加缓存
	whoBlockedUserList, err := userapi.UserBlockList(param.uc, param.fromRoom.CreatorID, userapi.BlockListTypeWhoBlockedUser)
	if err != nil {
		return err
	}
	for _, v := range whoBlockedUserList {
		if v == param.toRoom.CreatorID {
			return actionerrors.NewErrForbidden("由于对方设置，无法操作")
		}
		if v == param.ownerRoom.CreatorID {
			return actionerrors.NewErrForbidden("由于对方主麦设置，无法操作")
		}
	}

	settings, err := livemeta.FindMultiConnectSettings(param.ownerRoom.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if settings == nil {
		settings = new(livemeta.MultiConnectSettings)
	}
	if !settings.IsEnableApply() {
		return actionerrors.NewErrForbidden("对方主麦不允许连线申请")
	}
	err = connectcheck.NewMultiConnectComponent(param.fromRoom, param.toRoom).Check()
	if err != nil {
		return err
	}
	param.memberNum, err = param.group.OngoingMembersCount(nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group.IsLimit(param.memberNum) {
		return actionerrors.ErrMultiConnectIsLimit
	}
	return nil
}

func (param *applicationRequestParam) addConnectMatch() error {
	now := goutil.TimeNow()
	param.match = &livemulticonnect.Match{
		StartTime:    now.UnixMilli(),
		EndTime:      0,
		Type:         livemulticonnect.MatchTypeApply,
		Status:       livemulticonnect.MatchStatusPending,
		GroupID:      param.group.ID,
		FromRoomID:   param.fromRoom.RoomID,
		ToRoomID:     param.ownerRoom.RoomID, // 需要主麦同意，这里是主麦房间 ID
		BridgeRoomID: param.toRoom.RoomID,    // 中间主播的房间号，向该主播申请加入连线
	}
	err := livemulticonnect.DB().Create(param.match).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *applicationRequestParam) sendTimeoutQueue() {
	databus.DelayMultiConnectMatchTimeout(param.match)
}

func (param *applicationRequestParam) sendMessage() {
	exists, err := livemulticonnect.IsConnected(param.fromRoom.RoomID, param.ownerRoom.RoomID, time.Hour*24*7)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	isAttention, err := attentionuser.HasFollowed(param.ownerRoom.CreatorID, param.fromRoom.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	msg := livemulticonnect.RequestBroadcastPayload{
		Type:   liveim.TypeMultiConnect,
		Event:  liveim.EventApplyRequest,
		RoomID: param.ownerRoom.RoomID,
		UserID: param.ownerRoom.CreatorID,
		MultiConnect: &livemulticonnect.RequestMultiConnect{
			MatchID:         param.match.ID,
			RemainDuration:  databus.MatchTimeoutDuration.Milliseconds(),
			CreateTime:      param.match.CreateTime,
			RecentConnected: &exists,
			MemberNum:       &param.memberNum,
			FromRoom: &livemulticonnect.MultiConnectRoom{
				RoomID:          param.fromRoom.RoomID,
				Name:            param.fromRoom.Name,
				CreatorID:       param.fromRoom.CreatorID,
				CreatorUsername: param.fromRoom.CreatorUsername,
				CreatorIconURL:  &param.fromRoom.CreatorIconURL,
				Status: &livemulticonnect.MultiConnectRoomStatus{
					Attention: isAttention,
				},
				Statistics: &livemulticonnect.MultiConnectRoomStatistics{
					Score:          param.fromRoom.Statistics.Score,
					AttentionCount: param.fromRoom.Statistics.AttentionCount,
				},
			},
		},
	}
	err = userapi.BroadcastUser(param.ownerRoom.RoomID, param.ownerRoom.CreatorID, &msg)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *applicationRequestParam) resp() handler.M {
	return handler.M{
		"multi_connect": &livemulticonnect.RequestMultiConnect{
			MatchID:        param.match.ID,
			RemainDuration: databus.MatchTimeoutDuration.Milliseconds(),
			CreateTime:     param.match.StartTime,
			ToRoom: &livemulticonnect.MultiConnectRoom{
				RoomID:          param.ownerRoom.RoomID,
				Name:            param.ownerRoom.Name,
				CreatorID:       param.ownerRoom.CreatorID,
				CreatorUsername: param.ownerRoom.CreatorUsername,
				CreatorIconURL:  &param.ownerRoom.CreatorIconURL,
				Statistics: &livemulticonnect.MultiConnectRoomStatistics{
					Score:          param.ownerRoom.Statistics.Score,
					AttentionCount: param.ownerRoom.Statistics.AttentionCount,
				},
			},
		},
	}
}

type applicationCancelParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`
}

// ActionMultiConnectApplicationCancel 取消连线申请
/**
 * @api {post} /api/v2/chatroom/multi-connect/application/cancel 取消连线申请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 申请方和主麦消息
 *   {
 *     "type": "multi_connect",
 *     "event": "apply_cancel",
 *     "room_id": 123456,
 *     "user_id": 13,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 申请方直播间
 *         "room_id": 123456,
 *         "creator_id": 13,
 *         "creator_username": "test"
 *       },
 *       "to_room": { // 申请处理方（主麦）直播间
 *         "room_id": 654321,
 *         "creator_id": 14,
 *         "creator_username": "test"
 *       }
 *     }
 *   }
 */
func ActionMultiConnectApplicationCancel(c *handler.Context) (handler.ActionResponse, string, error) {
	var param *applicationCancelParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.MatchID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	match, err := livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if match == nil {
		return nil, "", actionerrors.ErrMultiConnectMatchNotFound
	}
	if !match.IsPending() {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}
	// 校验是否是发起方
	if match.FromRoomID != param.RoomID {
		return nil, "", actionerrors.ErrForbidden
	}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": []int64{match.FromRoomID, match.ToRoomID}}}, nil)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != 2 {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	roomMap := util.ToMap(rooms, func(room *room.Room) int64 {
		return room.RoomID
	})
	if !roomMap[match.FromRoomID].IsOwner(c) {
		return nil, "", actionerrors.ErrForbidden
	}

	ok, err := match.Cancel()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}

	err = userapi.BroadcastMany(livemulticonnect.NewUnacceptBroadcastPayloadElems(liveim.EventApplyCancel, match, roomMap))
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil, "success", nil
}

type applicationAcceptParams struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`

	userID int64
	match  *livemulticonnect.Match
	group  *livemulticonnect.Group
}

type applicationAcceptResp struct {
	MultiConnect *livemulticonnect.MultiConnectInfo `json:"multi_connect"`
}

// ActionMultiConnectApplicationAccept 接受连线申请
/**
 * @api {post} /api/v2/chatroom/multi-connect/application/accept 接受连线申请（主麦）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 主麦直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "multi_connect": {
 *         "group_id": 111, // 连线组 ID
 *         "status": 1, // 连线状态；1: 连线中；2: 已结束
 *         "join_index": 3, // 加入连线的麦序
 *         "duration": 123456, // 显示本房连线时长，单位：毫秒
 *         "mute_room_ids": [3,12,223344], // 静音连线直播间 ID 列表
 *         "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *         "members": [ // 连线成员列表，首位总是当前麦位
 *           {
 *             "index": 1, // 连线序号
 *             "role": 1, // 角色；1: 主麦；2: 连线成员
 *             "score": 111, // 礼物积分
 *             "room": { // 房间信息
 *               "room_id": 152,
 *               "name": "123456",
 *               "creator_id": 12345,
 *               "creator_username": "1234",
 *               "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *             }
 *           },
 *           ...
 *         ],
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 所有连线的直播间消息
 *   {
 *     "type": "multi_connect",
 *     "event": "apply_accept",
 *     "room_id": 10659544,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "status": 1, // 连线状态；1: 连线中；2: 已结束
 *       "join_index": 3, // 加入连线的麦序
 *       "duration": 10000, // 显示本房连线总时长，单位：毫秒
 *       "mute_room_ids": [3,12,223344], // 静音连线直播间 ID 列表
 *       "mic_off_room_ids": [], // 闭麦直播间 ID 列表
 *       "members": [ // 连线成员列表，首位总是当前麦位
 *         {
 *           "index": 1, // 连线序号
 *           "role": 1, // 角色；1: 主麦；2: 连线成员
 *           "score": 111, // 礼物积分
 *           "room": { // 房间信息
 *             "room_id": 152,
 *             "name": "123456",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *           }
 *         },
 *         ...
 *       ]
 *     }
 *   }
 */
func ActionMultiConnectApplicationAccept(c *handler.Context) (handler.ActionResponse, string, error) {
	param := applicationAcceptParams{userID: c.UserID()}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.MatchID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.accept()
	if err != nil {
		return nil, "", err
	}
	roomConnectInfo, err := livemulticonnect.AcceptMatchBroadcastMany(liveim.EventApplyAccept, param.group, param.match.FromRoomID)
	if err != nil {
		return nil, "", err
	}
	return applicationAcceptResp{MultiConnect: roomConnectInfo}, "", nil
}

func (param *applicationAcceptParams) check() error {
	r, err := room.FindOne(bson.M{"creator_id": param.userID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.RoomID != param.RoomID || !r.IsOpen() {
		return actionerrors.ErrForbidden
	}
	param.match, err = livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.match == nil {
		return actionerrors.ErrMultiConnectMatchNotFound
	}
	if param.match.ToRoomID != param.RoomID || !param.match.IsPending() ||
		param.match.Type != livemulticonnect.MatchTypeApply || param.match.GroupID <= 0 {
		return actionerrors.NewErrLiveForbidden("无效的连线匹配")
	}
	param.group, err = livemulticonnect.FindOngoingGroup(param.match.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}

	fromRoom, err := room.FindOne(bson.M{"room_id": param.match.FromRoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if fromRoom == nil || !fromRoom.IsOpen() {
		return actionerrors.NewErrLiveForbidden("当前申请方直播间状态异常")
	}
	member, err := livemulticonnect.FindOngoingMemberByRoomID(nil, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if member == nil || member.GroupID != param.group.ID || member.Role != livemulticonnect.MemberRoleOwner {
		return actionerrors.ErrForbidden
	}
	fromMember, err := livemulticonnect.FindOngoingMemberByRoomID(nil, param.match.FromRoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if fromMember != nil {
		return actionerrors.NewErrLiveForbidden("对方已在连线中")
	}
	return nil
}

func (param *applicationAcceptParams) accept() error {
	err := param.match.JoinGroup(param.group)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type applicationRefuseParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	MatchID int64 `form:"match_id" json:"match_id"`
}

// ActionMultiConnectApplicationRefuse 拒绝连线申请
/**
 * @api {post} /api/v2/chatroom/multi-connect/application/refuse 拒绝连线申请（主麦）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 主麦直播间 ID
 * @apiParam {Number} match_id 连线匹配 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主动拒绝时，申请方消息
 *   {
 *     "type": "multi_connect",
 *     "event": "apply_refuse",
 *     "room_id": 123456,
 *     "user_id": 13,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 申请方直播间
 *         "room_id": 123456,
 *         "creator_id": 13,
 *         "creator_username": "test"
 *       },
 *       "to_room": { // 申请处理方（主麦）直播间
 *         "room_id": 654321,
 *         "creator_id": 14,
 *         "creator_username": "test"
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 自动超时双方消息
 *   {
 *     "type": "multi_connect",
 *     "event": "apply_timeout",
 *     "room_id": 654321,
 *     "user_id": 13,
 *     "multi_connect": {
 *       "match_id": 1,
 *       "from_room": { // 申请方直播间
 *         "room_id": 123456,
 *         "creator_id": 13,
 *         "creator_username": "test"
 *       },
 *       "to_room": { // 申请处理方（主麦）直播间
 *         "room_id": 654321,
 *         "creator_id": 14,
 *         "creator_username": "test"
 *       }
 *     }
 *   }
 */
func ActionMultiConnectApplicationRefuse(c *handler.Context) (handler.ActionResponse, string, error) {
	param := new(invitationRefuseParam)
	err := c.Bind(param)
	if err != nil || param.MatchID <= 0 || param.RoomID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	match, err := livemulticonnect.FindMatch(param.MatchID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if match == nil {
		return nil, "", actionerrors.ErrMultiConnectMatchNotFound
	}
	if !match.IsPending() {
		return nil, "", actionerrors.NewErrForbidden("连线请求已被处理")
	}
	if match.ToRoomID != param.RoomID {
		return nil, "", actionerrors.NewErrForbidden("您不是主麦，无法处理该申请")
	}

	toRoom, err := room.Find(match.ToRoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if toRoom == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}
	if toRoom.CreatorID != c.UserID() {
		return nil, "", actionerrors.ErrParamsMsg("您不是该房间的主播，无法处理该请求")
	}

	fromRoom, err := room.Find(match.FromRoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if fromRoom == nil {
		return nil, "", actionerrors.ErrCannotFindRoom
	}

	ok, err := match.Refuse()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrMultiConnectMatchStatusModified
	}

	payload := livemulticonnect.NewUnacceptBroadcastPayload(liveim.EventApplyRefuse, match, map[int64]*room.Room{
		toRoom.RoomID:   toRoom,
		fromRoom.RoomID: fromRoom,
	})
	err = userapi.BroadcastUser(fromRoom.RoomID, fromRoom.CreatorID, payload)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "success", nil
}
