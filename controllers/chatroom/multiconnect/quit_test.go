package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionMultiConnectQuit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}, "creator_id": bson.M{"$gt": 1}}, opt.SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)
	group := &livemulticonnect.Group{ConnectID: "1"}
	require.NoError(livemulticonnect.DB().Create(group).Error)
	members := []*livemulticonnect.GroupMember{
		{GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: rooms[0].RoomID, Role: livemulticonnect.MemberRoleOwner},
		{GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: rooms[1].RoomID, Role: livemulticonnect.MemberRoleMember},
		{GroupID: group.ID, Status: livemulticonnect.MemberStatusOngoing, RoomID: rooms[2].RoomID, Role: livemulticonnect.MemberRoleMember},
	}
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members))

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(any any) (any, error) {
		return "success", nil
	})
	defer cancel()
	p := quitParams{
		RoomID:  rooms[0].RoomID,
		GroupID: group.ID,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = rooms[0].CreatorID
	_, res, err := ActionMultiConnectQuit(c)
	require.NoError(err)
	assert.Equal("退出成功", res)

	p = quitParams{
		RoomID:  rooms[0].RoomID,
		GroupID: group.ID,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = rooms[0].CreatorID
	_, _, err = ActionMultiConnectQuit(c)
	assert.Equal(actionerrors.ErrMultiConnectGroupMemberModified, err)

	p = quitParams{
		RoomID:  rooms[1].RoomID,
		GroupID: group.ID,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	c.User().ID = rooms[1].CreatorID
	_, res, err = ActionMultiConnectQuit(c)
	require.NoError(err)
	assert.Equal("退出成功", res)
}
