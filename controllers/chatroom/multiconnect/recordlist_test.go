package multiconnect

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestActionMultiConnectRecordList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("直播间号错误", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/record-list?room_id=0", true, nil)
		_, _, err := ActionMultiConnectRecordList(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("直播间号不存在", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodGet, "/record-list?room_id=100", true, nil)
		_, _, err := ActionMultiConnectRecordList(c)
		assert.Equal(actionerrors.ErrCannotFindRoom, err)
	})

	t.Run("连线记录为空", func(t *testing.T) {
		err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
		require.NoError(err)
		c := handler.NewTestContext(http.MethodGet, "/record-list?room_id=18113499", true, nil)
		resp, msg, err := ActionMultiConnectRecordList(c)
		require.NoError(err)
		listResp, ok := resp.(recordListResp)
		require.True(ok)
		assert.Empty(listResp.Data)
		assert.Empty(msg)
	})

	t.Run("存在连线记录", func(t *testing.T) {
		// 清空再插入连线记录测试数据
		err := livemulticonnect.DB().Delete(livemulticonnect.Record{}).Error
		require.NoError(err)
		records := []*livemulticonnect.Record{
			{CreateTime: 1737429513, ModifiedTime: 1737429513, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737429503237, EndTime: 1737429513237, Duration: 10000},
			{CreateTime: 1737343195, ModifiedTime: 1737343195, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343195000, EndTime: 1737343205000, Duration: 10000},
			{CreateTime: 1737343185, ModifiedTime: 1737343185, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343185000, EndTime: 1737343195000, Duration: 10000},
			{CreateTime: 1737343175, ModifiedTime: 1737343175, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343175000, EndTime: 1737343185000, Duration: 10000},
			{CreateTime: 1737343165, ModifiedTime: 1737343165, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343165000, EndTime: 1737343175000, Duration: 10000},
			{CreateTime: 1737343155, ModifiedTime: 1737343155, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343155000, EndTime: 1737343165000, Duration: 10000},
			{CreateTime: 1737343145, ModifiedTime: 1737343145, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343145000, EndTime: 1737343155000, Duration: 10000},
			{CreateTime: 1737343135, ModifiedTime: 1737343135, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737343135000, EndTime: 1737343145000, Duration: 10000},
		}
		err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Record{}.TableName(), records)
		require.NoError(err)
		// 清空再插入主麦成员测试数据
		err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
		require.NoError(err)
		err = livemulticonnect.DB().Create(livemulticonnect.GroupMember{ID: 10, GroupID: 100, Role: livemulticonnect.MemberRoleOwner, Status: livemulticonnect.MemberStatusOngoing, RoomID: 22489473}).Error
		require.NoError(err)
		c := handler.NewTestContext(http.MethodGet, "/record-list?room_id=18113499", true, nil)
		resp, msg, err := ActionMultiConnectRecordList(c)
		require.NoError(err)
		listResp, ok := resp.(recordListResp)
		require.True(ok)
		require.Len(listResp.Data, 8)
		assert.EqualValues(1737429503, listResp.Data[0].MultiConnect.StartTime)
		assert.EqualValues(10000, listResp.Data[0].MultiConnect.Duration)
		assert.Empty(msg)
	})

	t.Run("存在连线记录且大于一百条", func(t *testing.T) {
		// 清空再插入连线记录测试数据
		require.NoError(livemulticonnect.DB().Delete(livemulticonnect.Record{}).Error)
		records := make([]*livemulticonnect.Record, 0, 110)
		for i := 0; i < 110; i++ {
			records = append(records, &livemulticonnect.Record{
				CreateTime: 1737429513, ModifiedTime: 1737429513, GroupID: 1, GroupMemberID: 12, GroupOwnerMemberID: 10, RoomID: 18113499, StartTime: 1737429503237, EndTime: 1737429513237, Duration: 10000,
			})
		}
		require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Record{}.TableName(), records))
		// 清空再插入主麦成员测试数据
		require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error)
		err := livemulticonnect.DB().Create(&livemulticonnect.GroupMember{
			ID: 10, GroupID: 100, Role: livemulticonnect.MemberRoleOwner, Status: livemulticonnect.MemberStatusOngoing, RoomID: 22489473,
		}).Error
		require.NoError(err)
		c := handler.NewTestContext(http.MethodGet, "/record-list?room_id=18113499", true, nil)
		resp, msg, err := ActionMultiConnectRecordList(c)
		require.NoError(err)
		listResp, ok := resp.(recordListResp)
		require.True(ok)
		assert.Len(listResp.Data, 100)
		assert.Empty(msg)
	})
}
