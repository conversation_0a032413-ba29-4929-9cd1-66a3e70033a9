package chatroom

import (
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	maxMessageLength        int64 = 20 // 直播间历史消息
	maxPreviewMessageLength int64 = 6  // 直播间预览页历史消息
)

type historyMessage struct {
	// 通用
	Type       string               `json:"type"`
	User       *liveuser.Simple     `json:"user"`
	Bubble     *bubble.Simple       `json:"bubble,omitempty"`
	CreateTime goutil.TimeUnixMilli `json:"create_time"`

	// 消息
	MsgID   string `json:"msg_id,omitempty"`
	Message string `json:"message,omitempty"`

	// 礼物和抽奖礼物
	Gift          *gift.Gift `json:"gift,omitempty"`
	GiftNum       int64      `json:"gift_num,omitempty"`
	LuckyGift     *gift.Gift `json:"lucky_gift,omitempty"`
	LuckyGiftNum  int64      `json:"lucky_gift_num,omitempty"`
	MessagePrefix string     `json:"message_prefix,omitempty"`

	// 贵族
	Name  string `json:"name,omitempty"`
	Level int    `json:"level,omitempty"`

	// 超粉
	Num     int    `json:"num,omitempty"`
	Price   int    `json:"price,omitempty"`
	IconURL string `json:"icon_url,omitempty"`

	// 贵族和超粉
	IsRegistered *int `json:"is_registered,omitempty"`

	// 表情
	Sticker *livesticker.StickerInfo `json:"sticker,omitempty"`

	userID      int64
	giftID      int64
	luckyGiftID *int64
}

type historyMessageResp struct {
	History []*historyMessage `json:"history"`

	c *handler.Context

	messages      []*models.Message
	liveGifts     []*livegifts.LiveGift
	nobles        []*vip.MongoUserNobles
	superFans     []*livetxnorder.LiveTxnOrder
	room          *room.Room
	currentTime   time.Time
	startTime     time.Time
	userID        int64
	isPreview     bool
	messageLength int64
	giftWallConf  *params.GiftWall

	stickerMap map[int64]*livesticker.LiveSticker
	packageMap map[int64]*livesticker.Package
	mGifts     map[int64]*gift.Gift
	mUsers     map[int64]*liveuser.Simple
	mSponsors  map[int64]*giftwall.Sponsor
	goodsMap   map[int64]livegoods.LiveGoods
}

func newHistoryMessage(message *models.Message) *historyMessage {
	return &historyMessage{
		Type:       msgTypeMsg,
		Bubble:     message.Bubble,
		CreateTime: goutil.NewTimeUnixMilli(message.CreateTime),
		MsgID:      message.MsgID,
		Message:    message.Message,
		userID:     message.UserID,
	}
}

func newStickerHistoryMessage(message *models.Message, pkg *livesticker.Package, s *livesticker.LiveSticker) *historyMessage {
	return &historyMessage{
		Type:       msgTypeMsg,
		CreateTime: goutil.NewTimeUnixMilli(message.CreateTime),
		MsgID:      message.MsgID,
		Message:    s.Message(),
		userID:     message.UserID,
		Sticker:    s.NewStickerInfo(pkg),
	}
}

func newGiftHistoryMessage(liveGift *livegifts.LiveGift) *historyMessage {
	history := &historyMessage{
		Type:          msgTypeGift,
		Bubble:        liveGift.Bubble,
		CreateTime:    goutil.NewTimeUnixMilli(liveGift.SentTime),
		GiftNum:       liveGift.GiftNum,
		userID:        liveGift.UserID,
		giftID:        liveGift.GiftID,
		MessagePrefix: liveGift.MessagePrefix,
	}
	if liveGift.LuckyGiftNum != nil {
		history.LuckyGiftNum = int64(*liveGift.LuckyGiftNum)
		history.luckyGiftID = liveGift.LuckyGiftID
	}

	return history
}

func newUserNobleHistoryMessage(noble *vip.MongoUserNobles) *historyMessage {
	return &historyMessage{
		Type:         msgTypeNoble,
		Bubble:       noble.Bubble,
		CreateTime:   goutil.NewTimeUnixMilli(noble.CreatedTime),
		Name:         noble.Name,
		Level:        noble.Level,
		IsRegistered: &noble.IsRegistration,
		userID:       noble.UserID,
	}
}

func newSuperFanHistoryMessage(superfan *livetxnorder.LiveTxnOrder, good livegoods.LiveGoods) *historyMessage {
	history := &historyMessage{
		Type:         msgTypeSuperFan,
		IsRegistered: util.NewInt(util.BoolToInt(superfan.Attr == livegoods.AttrSuperFanRegister)),
		Num:          good.GoodsNum(),
		Price:        good.GoodsTotalPrice(),
		IconURL:      good.GoodsIconURL(),
		CreateTime:   goutil.TimeUnixMilli(superfan.CreateTime * 1000),
		userID:       superfan.BuyerID,
	}

	if superfan.More != nil {
		history.Bubble = superfan.More.Bubble
	}

	return history
}

func newHistoryMessageResp(c *handler.Context) (*historyMessageResp, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	room, err := room.Find(roomID,
		&room.FindOptions{
			Projection: bson.M{"status": 1, "creator_id": 1, "room_id": 1},
			DisableAll: true,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	// 后续的查询从开播时间开始查询
	// TODO: 优化查询时间
	resp := historyMessageResp{room: room, userID: c.UserID(), currentTime: goutil.TimeNow()}
	// 如果直播间关播且存在关播时间则查询关播后的历史消息，否则返回开播后的历史消息
	if !util.IntToBool(room.Status.Open) && room.Status.CloseTime != nil {
		resp.startTime = util.UnixMilliToTime(*room.Status.CloseTime)
	} else if room.Status.OpenTime == 0 {
		// 没有开播过的房间 open_time 是 0，不影响历史消息的正常逻辑
		resp.startTime = util.UnixMilliToTime(room.Status.OpenTime)
	} else {
		resp.startTime = util.UnixMilliToTime(room.Status.OpenTime)
	}
	resp.c = c
	preview, _ := c.GetParamInt("preview")
	resp.isPreview = goutil.IntToBool(preview)
	return &resp, nil
}

// 查找消息
func (resp *historyMessageResp) findMessage() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := service.MongoDB.Collection("messages")
	resp.messageLength = maxMessageLength
	if resp.isPreview {
		resp.messageLength = maxPreviewMessageLength
	}
	findOpts := options.Find().SetSort(bson.M{"create_time": -1}).SetLimit(resp.messageLength)
	filter := bson.M{
		"_room_id":    resp.room.OID,
		"create_time": bson.M{"$gt": resp.startTime},
	}
	statusFilter := bson.M{"$in": bson.A{models.MessageStatusNormal, models.MessageStatusLimited}}
	if resp.userID != 0 {
		filter["$or"] = bson.A{
			bson.M{"status": statusFilter},
			bson.M{"user_id": resp.userID},
		}
	} else {
		filter["status"] = statusFilter
	}
	// WORKAROUND: 兼容 Android < 5.8.1 或 iOS < 4.9.8 不显示表情
	isNoSticker := resp.c.Equip().IsOldApp(goutil.AppVersions{IOS: "4.9.8", Android: "5.8.1"})
	if isNoSticker || resp.isPreview {
		filter["sticker"] = bson.M{"$exists": false}
	}

	cur, err := collection.Find(ctx, filter, findOpts)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	defer cur.Close(ctx)

	resp.messages = make([]*models.Message, 0, resp.messageLength)
	err = cur.All(ctx, &resp.messages)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if isNoSticker {
		return nil
	}
	var (
		packageIDs = make([]int64, 0, len(resp.messages))
		stickerIDs = make([]int64, 0, len(resp.messages))
	)
	for _, msg := range resp.messages {
		if msg.Sticker == nil {
			continue
		}
		packageIDs = append(packageIDs, msg.Sticker.PackageID)
		stickerIDs = append(stickerIDs, msg.Sticker.StickerID)
	}
	if len(stickerIDs) == 0 {
		return nil
	}
	// find stickers
	stickers, err := livesticker.FindStickersByIDs(goutil.Uniq(stickerIDs))
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	resp.stickerMap = goutil.ToMap(stickers, "ID").(map[int64]*livesticker.LiveSticker)
	// find packages
	packages, err := livesticker.FindPackagesByIDs(goutil.Uniq(packageIDs))
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	resp.packageMap = goutil.ToMap(packages, "ID").(map[int64]*livesticker.Package)

	return nil
}

// 查找礼物
func (resp *historyMessageResp) findGift() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livegifts.Collection()
	findOpts := options.Find().SetSort(bson.M{"combo_end_time": -1}).SetLimit(maxMessageLength)
	giftFilter := bson.M{
		"_room_id":       resp.room.OID,
		"combo_end_time": bson.M{"$gt": resp.startTime, "$lt": resp.currentTime}, // 历史消息不显示连击中的礼物
	}
	giftCur, err := collection.Find(ctx, giftFilter, findOpts)
	if err != nil {
		logger.Error(err)
		return
	}
	defer giftCur.Close(ctx)

	err = giftCur.All(ctx, &resp.liveGifts)
	if err != nil {
		logger.Error(err)
		return
	}
}

// 查找贵族
func (resp *historyMessageResp) findNoble() {
	var err error
	resp.nobles, err = vip.RoomNobleHistory(resp.room.CreatorID,
		resp.startTime, resp.currentTime, maxMessageLength)
	if err != nil {
		logger.Error(err)
		return
	}
}

// 查找超粉
func (resp *historyMessageResp) findSuperFan() {
	var err error
	resp.superFans, err = livetxnorder.RoomSuperFanHistory(resp.room.CreatorID,
		resp.startTime, resp.currentTime, maxMessageLength)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(resp.superFans) == 0 {
		return
	}

	goods, err := livegoods.AllSuperFan()
	if err != nil {
		logger.Error(err)
		return
	}

	resp.goodsMap = goutil.ToMap(goods, "ID").(map[int64]livegoods.LiveGoods)
}

func (resp *historyMessageResp) findUserInfo(userIDs []int64) {
	userIDs = util.Uniq(userIDs)
	resp.mUsers = make(map[int64]*liveuser.Simple)
	users, err := liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": userIDs}},
		&liveuser.FindOptions{FindTitles: true, RoomID: resp.room.RoomID})
	if err != nil {
		logger.Error(err)
		return
	}

	resp.mUsers = goutil.ToMap(users, "UID").(map[int64]*liveuser.Simple)
}

func (resp *historyMessageResp) findGiftInfo(giftIDs []int64) {
	if len(giftIDs) == 0 {
		return
	}

	giftIDs = util.Uniq(giftIDs)
	resp.mGifts = make(map[int64]*gift.Gift)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := gift.Collection().Find(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs}})
	if err != nil {
		logger.Error(err)
		return
	}
	defer cur.Close(ctx)

	gifts := make([]*gift.Gift, 0)
	err = cur.All(ctx, &gifts)
	if err != nil {
		logger.Error(err)
		return
	}
	for i := range gifts {
		gifts[i].ConstructIconURL()
	}

	resp.mGifts = goutil.ToMap(gifts, "GiftID").(map[int64]*gift.Gift)
}

func (resp *historyMessageResp) findGiftSponsorInfo() {
	var err error
	// 查询礼物冠名信息
	resp.mSponsors, err = giftwall.FindOngoingRoomSponsorsWithCache(resp.room.RoomID, resp.currentTime)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 如果有冠名信息，查询礼物墙配置
	if len(resp.mSponsors) == 0 {
		return
	}
	resp.giftWallConf, err = params.FindGiftWall()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// 查找对应的冠名标识配置
func (resp *historyMessageResp) historySponsorInfo(revenue int64) *gift.Sponsor {
	if resp.giftWallConf == nil {
		return nil
	}
	spLevel := resp.giftWallConf.MatchSponsorLevel(revenue)
	if spLevel == nil {
		return nil
	}
	return &gift.Sponsor{
		IconURL:   spLevel.GiftsIconURL,
		Text:      spLevel.Text,
		TextColor: spLevel.TextColor,
	}
}

// 整合历史记录信息
func (resp *historyMessageResp) processHistoryDetails() {
	for _, history := range resp.History {
		// 整合用户信息
		history.User = resp.mUsers[history.userID]
		// 整合礼物数据
		if history.luckyGiftID != nil {
			history.LuckyGift = resp.mGifts[*history.luckyGiftID]
		}
		if history.giftID != 0 {
			giftInfo := resp.mGifts[history.giftID]
			// 判断用户是否具有该礼物的冠名权
			history.Gift = giftInfo
			if sp, ok := resp.mSponsors[history.giftID]; ok {
				// 判断是否是冠名用户
				if history.userID != sp.UserID {
					continue
				}
				if resp.currentTime.Unix() < sp.StartTime || resp.currentTime.Unix() >= sp.EndTime {
					continue
				}
				// 设置冠名信息
				giftInfo.Sponsor = resp.historySponsorInfo(sp.Revenue)
			}
		}
	}
}

// 构建返回
func (resp *historyMessageResp) buildResp() error {
	length := len(resp.messages) + len(resp.liveGifts) + len(resp.nobles) + len(resp.superFans)
	resp.History = make([]*historyMessage, 0, length)
	for _, msg := range resp.messages {
		if msg.Sticker == nil {
			// 文字消息
			resp.History = append(resp.History, newHistoryMessage(msg))
		} else {
			// 表情消息
			pkg := resp.packageMap[msg.Sticker.PackageID]
			s := resp.stickerMap[msg.Sticker.StickerID]
			if s == nil {
				logger.WithFields(logger.Fields{"sticker_id": msg.Sticker.StickerID}).
					Error("sticker not found")
				continue
			}
			if pkg == nil {
				logger.WithFields(logger.Fields{"package_id": msg.Sticker.PackageID}).
					Error("sticker package not found")
				continue
			}
			resp.History = append(resp.History, newStickerHistoryMessage(msg, pkg, s))
		}
	}
	for _, g := range resp.liveGifts {
		resp.History = append(resp.History, newGiftHistoryMessage(g))
	}
	for _, noble := range resp.nobles {
		resp.History = append(resp.History, newUserNobleHistoryMessage(noble))
	}
	for _, sf := range resp.superFans {
		resp.History = append(resp.History, newSuperFanHistoryMessage(sf, resp.goodsMap[sf.GoodsID]))
	}

	resp.sortHistory()
	// 截取指定数量消息
	// TODO: 该处可能需要优化整体接口查询方式
	if int64(len(resp.History)) > resp.messageLength {
		resp.History = resp.History[int64(len(resp.History))-resp.messageLength:]
	}

	userIDs := make([]int64, 0, length)
	giftIDs := make([]int64, 0, len(resp.liveGifts))
	for i := range resp.History {
		history := resp.History[i]
		userIDs = append(userIDs, history.userID)
		if history.giftID != 0 {
			giftIDs = append(giftIDs, history.giftID)
		}
		if history.luckyGiftID != nil {
			giftIDs = append(giftIDs, *history.luckyGiftID)
		}
	}

	// 查询用户和礼物信息
	resp.findUserInfo(userIDs)
	resp.findGiftInfo(giftIDs)
	resp.findGiftSponsorInfo()
	// 设置历史记录详情
	resp.processHistoryDetails()
	return nil
}

// 对历史消息进行排序
func (resp *historyMessageResp) sortHistory() {
	sort.Slice(resp.History, func(i, j int) bool {
		return resp.History[i].CreateTime < resp.History[j].CreateTime
	})
}

// ActionHistoryMessage 获取直播间历史消息
/**
 * @api {get} /api/v2/chatroom/history/message 获取直播间历史消息
 * @apiDescription 通过直播间 ID 获取历史消息，用户信息和礼物信息绑定到每一条记录，关播直播间会返回从上次关播后开始的最近的历史消息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [preview=0] 是否为直播间预览页，0: 否；1: 是
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "history": [
 *         {
 *           "type": "message",
 *           "user": {
 *             "user_id": 3400560,
 *             "username": "tsubomi0517",
 *             "iconurl": "http://static.missevan.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png",
 *             "titles": []
 *           },
 *           "bubble": {
 *             "type": "noble",
 *             "noble_level": 7
 *           },
 *           "create_time": 1234567888100,
 *           "msg_id": "test-msg-id",
 *           "message": "This is message.",
 *           "price": 0,
 *           "icon_url": ""
 *         },
 *         {
 *           "type": "gift",
 *           "user": {
 *             "user_id": 12,
 *             "username": "零月",
 *             "iconurl": "http://static-test.missevan.com/avatar/test.jpg",
 *             "titles": []
 *           },
 *           "bubble": {
 *             "type": "noble",
 *             "noble_level": 5
 *           },
 *           "create_time": 1234567888200,
 *           "gift": {
 *             "type": 1,
 *             "gift_id": 1,
 *             "name": "药丸",
 *             "icon_url": "https://static-test.missevan.com/gifts/icons/001.png",
 *             "icon_active_url": "https://static-test.missevan.com/gifts/icons/active/001.webp",
 *             "intro": "来，张嘴吃药 | 赠送 167、500 或 1000 个可触发特效，赠送 3500 个可触发飘屏哦~",
 *             "comboable": 1,
 *             "combo_effect_url": "https://static-test.missevan.com/gifts/comboeffects/001.svga",
 *             "web_combo_effect_url": "https://static-test.missevan.com/gifts/comboeffects/001-web.svga",
 *             "price": 6,
 *             "order": 30,
 *             "noble_level": 0,
 *             "medal_level": 3,
 *             "sponsor": { // 冠名信息，没有冠名没有该字段
 *               "icon_url": "https://static-test.missevan.com/gifts/labels/001.png",
 *               "text": "冠名信息",
 *               "text_color": "#FFFFFF"
 *             }
 *           },
 *           "lucky_gift": {}, // 如果 lucky_gift_num 大于一，礼物信息存放到 lucky_gift 字段中
 *           "lucky_gift_num": 1,
 *           "price": 0,
 *           "icon_url": ""
 *         },
 *         {
 *           "type": "noble",
 *           "user": {
 *             "user_id": 12,
 *             "username": "零月",
 *             "iconurl": "http://static-test.missevan.com/avatar/test.jpg",
 *             "titles": []
 *           },
 *           "create_time": 1234567888300,
 *           "name": "live-service/controllers/chatroom/",
 *           "level": 1,
 *           "price": 0,
 *           "icon_url": "",
 *           "is_registered": 1
 *         },
 *         {
 *           "type": "super_fan",
 *           "user": {
 *             "user_id": 12,
 *             "username": "零月",
 *             "iconurl": "http://static-test.missevan.com/avatar/test.jpg",
 *             "titles": []
 *           },
 *           "bubble": {
 *             "type": "noble",
 *             "noble_level": 5
 *           },
 *           "create_time": 1234567889000,
 *           "num": 1,
 *           "price": 1000,
 *           "icon_url": "https://static-test.missevan.com/test.png"
 *         },
 *         {
 *           "type": "message",
 *           "msg_id": "test-msg-id",
 *           "message": "[贴图名称]",
 *           "user": {
 *             "user_id": 12,
 *             "username": "零月",
 *             "iconurl": "http://static-test.missevan.com/avatar/test.jpg",
 *             "titles": []
 *           },
 *           "sticker": { // 该字段用于区分是否是贴图
 *             "package_id": 1,
 *             "package_name": "超粉专属",
 *             "sticker_id": 1,
 *             "name": "贴图名称",
 *             "icon_url": "http://static-test.maoercdn.com/superfans/icon1.png",
 *             "image_url": "http://static-test.maoercdn.com/superfans/image1.webp",
 *             "intro": "超粉专属" // 贴图来源描述
 *           },
 *           "create_time": 1234567888200,
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response: 直播间预览页历史消息
 *   {
 *     "code": 0,
 *     "info": {
 *       "history": [
 *         {
 *           "type": "message",
 *           "user": {
 *             "user_id": 3400560,
 *             "username": "tsubomi0517",
 *             "iconurl": "http://static.missevan.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png",
 *             "titles": []
 *           },
 *           "create_time": 1234567888100, // 发送时间，单位：毫秒
 *           "msg_id": "test-msg-id",
 *           "message": "This is message.",
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (400) {String} info 服务器内部错误
 *
 * @apiError (404) {number} code 500030004, 500030011
 * @apiError (404) {string} info 无法找到该聊天室，直播间尚未开启
 *
 */
func ActionHistoryMessage(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newHistoryMessageResp(c)
	if err != nil {
		return nil, err
	}

	// 直播间开启才查询消息
	if goutil.IntToBool(resp.room.Status.Open) {
		err = resp.findMessage()
		if err != nil {
			return nil, err
		}

		if !resp.isPreview {
			resp.findGift()
			resp.findNoble()
			resp.findSuperFan()
		}
	}

	return resp, resp.buildResp()
}
