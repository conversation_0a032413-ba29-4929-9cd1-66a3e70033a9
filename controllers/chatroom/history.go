package chatroom

import (
	"sort"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	msgTypeNoble    = liveim.TypeNoble
	msgTypeMsg      = liveim.TypeMessage
	msgTypeGift     = liveim.TypeGift
	msgTypeSuperFan = liveim.TypeSuperFan
	msgTypeQuestion = liveim.TypeQuestion
	msgTypeDanmaku  = "danmaku" // NOTICE: liveim 中没有该类型 type
)

type historyRevenueElem struct {
	Type     string               `json:"type"`
	Time     goutil.TimeUnixMilli `json:"time"`
	Operator string               `json:"operator"`
	Num      int64                `json:"num"`
	GiftID   int64                `json:"gift_id,omitempty"` // 礼物时使用该字段
	Name     string               `json:"name,omitempty"`    // 礼物、贵族时使用该字段
	IconURL  string               `json:"icon_url,omitempty"`
	ComboID  *primitive.ObjectID  `json:"combo_id,omitempty"` // 礼物时使用该字段
	Level    int                  `json:"level,omitempty"`    // 贵族时使用该字段
	User     *mowangskuser.Simple `json:"user,omitempty"`
	Price    int64                `json:"price,omitempty"` // 提问时使用该字段

	userID int64
}

type historyRevenueResp struct {
	HasMore bool   `json:"has_more"`
	Marker  string `json:"marker"`

	Data []*historyRevenueElem `json:"data"`
}

type historyRevenueParam struct {
	Marker   string `form:"marker"`
	GiftSize int64  `form:"gift_size"` // Deprecated

	c           *handler.Context
	room        *room.Room
	endTime     goutil.TimeUnixMilli
	nextEndTime goutil.TimeUnixMilli
	nextMarker  string
	hasMore     bool
	gifts       []*livegifts.LiveGift
	nobles      []*vip.MongoUserNobles
	goodsOrders []*livetxnorder.LiveTxnOrder // 包括超粉和付费弹幕
	questions   []*livequestion.LiveQuestion
	enableNew   bool // 是否展示提问和付费弹幕收益
}

// ActionHistoryRevenue 礼物/贵族/超粉/付费弹幕/提问记录/宝盒记录
/**
 * @api {get} /api/v2/chatroom/history/revenue 礼物/贵族/超粉/付费弹幕/提问记录/宝盒记录
 * @apiDescription 本场直播礼物/贵族/超粉/付费弹幕/提问记录/宝盒记录，只有主播可见，时间倒序
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求返回前一次响应的请求中的 marker
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "has_more": true, // 如果还有礼物未返回，则是 true, 反之 false
 *       "marker": "1212121212", // 下一次请求的游标
 *       "data": [{
 *         "type": "gift",
 *         "time": 1584808800000, // 毫秒级时间戳
 *         "operator": "赠送",
 *         "num": 10,
 *         "gift_id": 301,
 *         "name": "猫粮",
 *         "icon_url": "https://static-test.maoercdn.com/live/gifts/icons/128.png",
 *         "combo_id": "123456789", // 礼物连击 id
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }, {
 *         "type": "noble",
 *         "time": 1584808800000,
 *         "operator": "开通",
 *         "num": 1,
 *         "name": "神话",
 *         "icon_url": "http://static-test.maoercdn.com/live/noble/labels/mini/003.png",
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }, {
 *         "type": "super_fan",
 *         "time": 1584808800000,
 *         "operator": "开通",
 *         "num": 1,
 *         "icon_url": "https://static-test.maoercdn.com/live/superfans/icon.png",
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }, {
 *         "type": "danmaku",
 *         "time": 1584808800000,
 *         "operator": "发送",
 *         "num": 1,
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }, {
 *         "type": "question",
 *         "time": 1584808800000,
 *         "operator": "",
 *         "price": 30, // 钻石
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }, {
 *         "type": "lucky_box",
 *         "time": 1584808800000,
 *         "operator": "抽取",
 *         "name": "宝盒名称",
 *         "num": 1,
 *         "icon_url": "https://static-test.maoercdn.com/live/superfans/icon.png",
 *         "user": {
 *           "user_id": 123456,
 *           "username": "用户名",
 *           "iconurl": "用户头像"
 *         }
 *       }]
 *     }
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (500) {string} info 服务器内部错误
 *
 * @apiError (404) {number} code 500030004, 500030011
 * @apiError (404) {string} info 无法找到该聊天室，直播间尚未开启
 *
 */
func ActionHistoryRevenue(c *handler.Context) (handler.ActionResponse, error) {
	var param historyRevenueParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}
	err = param.findGifts()
	if err != nil {
		return nil, err
	}
	// 以礼物为锚点，获取指定时间段内的贵族、超粉、付费弹幕、提问记录，再统一按照时间排序
	param.findNoble()
	param.findGoodsOrders()
	param.findQuestions()
	return param.newResp()
}

func (param *historyRevenueParam) load(c *handler.Context) error {
	err := c.C.BindQuery(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.c = c
	if param.Marker == "" {
		param.endTime = goutil.NewTimeUnixMilli(goutil.TimeNow())
	} else {
		tm, err := strconv.ParseInt(param.Marker, 10, 64)
		if err != nil || tm <= 0 {
			return actionerrors.ErrParams
		}
		param.endTime = goutil.TimeUnixMilli(tm)
	}
	param.room, err = room.FindOne(bson.M{"creator_id": c.UserID()},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	// 首次查询区间为 [open_time, now)，有可能返回 marker 为 open_time 的记录
	if int64(param.endTime) < param.room.Status.OpenTime {
		return actionerrors.ErrParams
	}
	if !util.IntToBool(param.room.Status.Open) {
		return actionerrors.ErrClosedRoom
	}
	if param.GiftSize <= 0 {
		param.GiftSize = 20
	}
	// WORKAROUND: 针对使用旧接口的 web 不下发提问、付费弹幕，防止接口先上线导致的显示异常
	param.enableNew = param.c.Request().URL.Path == "/api/v2/chatroom/history/revenue"
	return nil
}

func (param *historyRevenueParam) findGifts() error {
	s := livegifts.Searcher{
		StartTime:  goutil.TimeUnixMilli(param.room.Status.OpenTime).ToTime(),
		EndTime:    param.endTime.ToTime(),
		RoomID:     param.room.RoomID,
		P:          1,
		PageSize:   param.GiftSize,
		Projection: mongodb.NewProjection("_id, gift_id, gift_num, user_id, sent_time"),
	}
	param.gifts = make([]*livegifts.LiveGift, 0)
	pa, err := s.ListLiveGifts(&param.gifts)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pa.MaxPage > 1 {
		// MaxPage 可能为 0
		param.hasMore = true
	}
	if len(param.gifts) > 0 {
		param.nextEndTime = goutil.NewTimeUnixMilli(param.gifts[len(param.gifts)-1].SentTime)
		param.nextMarker = strconv.FormatInt(int64(param.nextEndTime), 10)
	}
	return nil
}

func (param *historyRevenueParam) findNoble() {
	if len(param.gifts) == 0 {
		return
	}
	var err error
	param.nobles, err = vip.RoomNobleHistory(param.room.CreatorID,
		param.nextEndTime.ToTime(), param.endTime.ToTime(), 0)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *historyRevenueParam) findGoodsOrders() {
	if len(param.gifts) == 0 {
		return
	}
	var err error
	if !param.enableNew {
		param.goodsOrders, err = livetxnorder.RoomSuperFanHistory(param.room.CreatorID,
			param.nextEndTime.ToTime(), param.endTime.ToTime(), 0)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return
	}
	param.goodsOrders, err = livetxnorder.RoomRevenueHistory(param.room.CreatorID,
		param.nextEndTime.ToTime(), param.endTime.ToTime())
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *historyRevenueParam) findQuestions() {
	if !param.enableNew {
		return
	}
	if len(param.gifts) == 0 {
		return
	}
	var err error
	param.questions, err = livequestion.RoomQuestionHistory(param.room.RoomID,
		param.nextEndTime.ToTime(), param.endTime.ToTime())
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *historyRevenueParam) newResp() (*historyRevenueResp, error) {
	resp := historyRevenueResp{HasMore: param.hasMore}
	if resp.HasMore {
		resp.Marker = param.nextMarker
	}
	const (
		send         = "赠送"
		registration = "开通"
		renewal      = "续费"
	)

	giftlist, err := gift.FindAllShowingGifts()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	gifts := goutil.ToMap(giftlist, "GiftID").(map[int64]gift.Gift)
	resp.Data = make([]*historyRevenueElem, 0, len(param.gifts)+len(param.nobles)+len(param.goodsOrders)+len(param.questions))
	userIDs := make([]int64, 0, cap(resp.Data))
	for i := range param.gifts {
		userIDs = append(userIDs, param.gifts[i].UserID)
		gift := gifts[param.gifts[i].GiftID]
		resp.Data = append(resp.Data, &historyRevenueElem{
			Type:     msgTypeGift,
			Time:     goutil.NewTimeUnixMilli(param.gifts[i].SentTime),
			Operator: send,
			Num:      param.gifts[i].GiftNum,
			GiftID:   param.gifts[i].GiftID,
			Name:     gift.Name,
			IconURL:  gift.Icon,
			userID:   param.gifts[i].UserID,
			ComboID:  &param.gifts[i].OID,
		})
	}
	nobleOperator := func(isRegistration int) string {
		if util.IntToBool(isRegistration) {
			return registration
		}
		return renewal
	}
	for i := range param.nobles {
		userIDs = append(userIDs, param.nobles[i].UserID)
		resp.Data = append(resp.Data, &historyRevenueElem{
			Type:     msgTypeNoble,
			Time:     goutil.NewTimeUnixMilli(param.nobles[i].CreatedTime),
			Operator: nobleOperator(param.nobles[i].IsRegistration),
			Name:     param.nobles[i].Name,
			IconURL:  param.nobles[i].IconURL,
			Num:      1,
			Level:    param.nobles[i].Level,
			userID:   param.nobles[i].UserID,
		})
	}

	sfs, err := livegoods.AllSuperFan()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	lbs, err := livegoods.AllLuckyBox()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	sfMap := goutil.ToMap(sfs, "ID").(map[int64]livegoods.LiveGoods)
	lbMap := goutil.ToMap(lbs, "ID").(map[int64]livegoods.LiveGoods)

	superFanOperator := func(attr int) string {
		if attr == livegoods.AttrSuperFanRegister {
			return registration
		}
		return renewal
	}
	for i := range param.goodsOrders {
		userIDs = append(userIDs, param.goodsOrders[i].BuyerID)
		switch param.goodsOrders[i].GoodsType {
		case livegoods.GoodsTypeSuperFan:
			resp.Data = append(resp.Data, &historyRevenueElem{
				Type:     msgTypeSuperFan,
				Time:     goutil.TimeUnixMilli(param.goodsOrders[i].CreateTime * 1000),
				Operator: superFanOperator(param.goodsOrders[i].Attr),
				Num:      int64(sfMap[param.goodsOrders[i].GoodsID].Num),
				IconURL:  sfMap[param.goodsOrders[i].GoodsID].GoodsIconURL(),
				userID:   param.goodsOrders[i].BuyerID,
			})
		case livegoods.GoodsTypeDanmaku:
			resp.Data = append(resp.Data, &historyRevenueElem{
				Type:     msgTypeDanmaku,
				Time:     goutil.TimeUnixMilli(param.goodsOrders[i].CreateTime * 1000),
				Operator: "发送",
				Num:      1,
				userID:   param.goodsOrders[i].BuyerID,
			})
		case livegoods.GoodsTypeLuckyBox:
			var num int64
			if param.goodsOrders[i].More != nil {
				num = int64(param.goodsOrders[i].More.Num)
			}
			resp.Data = append(resp.Data, &historyRevenueElem{
				Type:     liveim.TypeLuckyBox,
				Time:     goutil.TimeUnixMilli(param.goodsOrders[i].CreateTime * 1000),
				Operator: "抽取",
				Name:     lbMap[param.goodsOrders[i].GoodsID].Title,
				Num:      num,
				IconURL:  lbMap[param.goodsOrders[i].GoodsID].GoodsIconURL(),
				userID:   param.goodsOrders[i].BuyerID,
			})
		}
	}

	for i := range param.questions {
		userIDs = append(userIDs, param.questions[i].UserID)
		resp.Data = append(resp.Data, &historyRevenueElem{
			Type:   msgTypeQuestion,
			Time:   goutil.NewTimeUnixMilli(*param.questions[i].AnsweredTime),
			Price:  param.questions[i].Price,
			userID: param.questions[i].UserID,
		})
	}

	if len(resp.Data) == 0 {
		return &resp, nil
	}
	sort.Slice(resp.Data, func(i, j int) bool {
		return resp.Data[i].Time > resp.Data[j].Time
	})
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		logger.Error(err)
		// PASS
		users = make(map[int64]*mowangskuser.Simple)
	}
	defaultUser := &mowangskuser.Simple{
		Username: "神秘人",
		IconURL:  service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL),
	}
	for i := range resp.Data {
		if u := users[resp.Data[i].userID]; u != nil {
			resp.Data[i].User = u
		} else {
			// 没有找到退化成 defaultUser
			resp.Data[i].User = defaultUser
		}
	}
	return &resp, nil
}
