package backpack

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type backpackUseParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	ItemID  int64 `form:"item_id" json:"item_id"`
	ItemNum int64 `form:"item_num" json:"item_num"`

	c                  *handler.Context
	r                  *room.Room
	user               *liveuser.Simple
	backpackItem       *backpackitem.LiveBackpackItem
	nobleTrialCardInfo *vip.Info
}

type trialNobleInfo struct {
	Level   int    `json:"level"`
	Title   string `json:"title"`
	IconURL string `json:"icon_url"`
}

type backpackUseResp struct {
	OK         int              `json:"ok"`
	ItemID     int64            `json:"item_id"`
	Remain     int64            `json:"remain"`
	Tip        string           `json:"tip"`
	User       *liveuser.Simple `json:"user,omitempty"`
	Bubble     *bubble.Simple   `json:"bubble,omitempty"`
	TrialNoble *trialNobleInfo  `json:"trial_noble,omitempty"`
}

// ActionBackpackUse 使用背包中的道具
/**
 * @api {post} /api/v2/chatroom/backpack/use 使用背包中的道具
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} item_id 道具 ID（当前仅用于：贵族体验卡）
 * @apiParam {Number} item_num 使用道具数量
 *
 * @apiSuccessExample {json} 体验卡开通成功响应
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "ok": 1,
 *       "item_id": 1,
 *       "remain": 10, // 剩余数量
 *       "tip": "体验截止至：yyyy-mm-dd hh:mm", // 提示信息
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }]
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static.maoercdn.com/live/bubble/image/001.png",
 *         "frame_url": "https://static.maoercdn.com/live/bubble/frame/001.png",
 *         "text_color": "#F0F0F0"
 *       },
 *       "trial_noble": { // 仅贵族体验卡道具有该字段
 *         "level": 1, // 贵族等级
 *         "title": "大咖",
 *         "icon_url": "https://static-test.maoercdn.com/live/006.png", // 用于贵族体验卡使用成功的弹窗显示
 *       }
 *     }
 *   }
 */
func ActionBackpackUse(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newBackpackUseParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	resp, err := param.use()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newBackpackUseParam(c *handler.Context) (*backpackUseParam, error) {
	var param *backpackUseParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.ItemID <= 0 || param.ItemNum <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *backpackUseParam) check() error {
	var err error
	param.r, err = room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if param.r.CreatorID == param.c.UserID() {
		// 主播在房间里看不到自己的用户背包
		return actionerrors.NewErrForbidden("主播不能使用该道具")
	}
	param.backpackItem, err = backpackitem.FindOne(param.ItemID, backpackitem.TypeNobleTrialCard)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.backpackItem == nil {
		return actionerrors.ErrNotFound("未查询到该道具")
	}
	count, err := useritems.CountItemNum(param.c.UserID(), param.ItemID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.ItemNum > count {
		return actionerrors.ErrNotFound("可用数量不足！")
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.c.UserID()}, &liveuser.FindOptions{
		FindTitles: true,
		FindVips:   true,
		RoomID:     param.RoomID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrCannotFindUser
	}

	switch param.backpackItem.Type {
	case backpackitem.TypeNobleTrialCard:
		err = param.checkNoble()
		if err != nil {
			return err
		}
	default:
		return actionerrors.NewErrForbidden("该道具不支持使用！")
	}
	return nil
}

func (param *backpackUseParam) checkNoble() error {
	var err error
	param.nobleTrialCardInfo, err = vip.FindVipInfoByID(vip.TypeLiveTrialNoble, param.backpackItem.MoreInfo.VipID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.nobleTrialCardInfo == nil {
		logger.WithFields(logger.Fields{
			"item_id": param.backpackItem.ID,
			"vip_id":  param.backpackItem.MoreInfo.VipID,
		}).Error("查询体验贵族信息失败")
		return actionerrors.ErrNotFound("未查询到贵族体验卡的贵族信息")
	}
	for _, vipType := range []int{vip.TypeLiveNoble, vip.TypeLiveTrialNoble} {
		if myNoble, ok := param.user.UserVipMap[vipType]; ok && myNoble.IsActive() {
			// 用户已开通普通贵族，当前贵族等级 > 体验卡等级
			if myNoble.Level > param.nobleTrialCardInfo.Level {
				return actionerrors.NewErrForbidden("无法使用低于当前贵族等级的体验卡！")
			}
		}
	}
	return nil
}

func (param *backpackUseParam) use() (*backpackUseResp, error) {
	key := keys.LockUseBackpackItem1.Format(param.user.UserID())
	ok, err := service.Redis.SetNX(key, 1, 5*time.Second).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("您的操作过于频繁")
	}
	defer service.Redis.Del(key)

	usator := useritems.ItemUsator{
		UserContext:   param.c.UserContext(),
		UserID:        param.user.UserID(),
		CreatorID:     param.r.CreatorID,
		ItemID:        param.ItemID,
		Duration:      param.backpackItem.MoreInfo.Duration,
		Num:           param.ItemNum,
		LiveOpenLogID: param.r.Status.OpenLogID,
		NewTrialNoble: param.nobleTrialCardInfo,
	}
	ok, remain, trialNoble, err := usator.Use()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden
	}

	switch param.backpackItem.Type {
	case backpackitem.TypeNobleTrialCard:
		// 清除用户贵族缓存
		vip.ClearUserVipCache(param.user.UserID())
		// 重新获取用户的最新贵族信息
		param.refreshUserTitlesAndAppearances(trialNoble)
	}

	messageBubble, err := userappearance.FindMessageBubble(param.c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return &backpackUseResp{
		OK:     1,
		ItemID: param.ItemID,
		Remain: remain,
		Tip:    fmt.Sprintf("体验截止至：%s", time.Unix(trialNoble.ExpireTime, 0).Format(util.TimeFormatYMDHHMM)),
		User:   param.user,
		Bubble: messageBubble,
		TrialNoble: &trialNobleInfo{
			Level:   trialNoble.Level,
			Title:   trialNoble.Title,
			IconURL: param.backpackItem.MoreInfo.IconURL,
		},
	}, nil
}

func (param *backpackUseParam) refreshUserTitlesAndAppearances(trialNoble *vip.UserVip) {
	// 使用贵族体验卡后，普通贵族的有效期可能也会发生变化，这里需要重新请求一次 RPC，同时生成缓存
	uvMap, err := vip.UserVipInfos(param.user.UserID(), true, nil)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	param.user.UserVipMap = uvMap
	param.user.VipInfo = vip.NewUserVipInfo(param.user.UserVipMap)
	param.user.MakeTitles()

	// 佩戴指定外观
	uvt := param.user.UserVipMap[vip.TypeLiveTrialNoble]
	if uvt == nil || !uvt.IsActive() {
		logger.WithFields(logger.Fields{
			"user_id":     param.user.UserID(),
			"item_vip_id": param.backpackItem.MoreInfo.VipID,
			"item_id":     param.backpackItem.ID,
		}).Error("体验贵族查询失败")
		// PASS
		return
	}
	if uvt.VipID != trialNoble.VipID || uvt.Level != trialNoble.Level || uvt.ExpireTime != trialNoble.ExpireTime {
		logger.WithFields(logger.Fields{
			"user_id":              param.user.UserID(),
			"vip_id":               uvt.VipID,
			"expected_vip_id":      trialNoble.VipID,
			"expire_time":          uvt.ExpireTime,
			"expected_expire_time": trialNoble.ExpireTime,
		}).Error("获取贵族信息 RPC 与开通体验贵族 RPC 返回结果不一致")
		// PASS
	}
	// NOTICE: 若体验卡等级等于普通贵族等级时，使用普通贵族续期后的过期时间
	uv := param.user.UserVipMap[vip.TypeLiveNoble]
	expireTime := uvt.ExpireTime
	if uv != nil && uv.IsActive() && uvt.Level == uv.Level { // 若普通贵族生效，过期时间一定是大于体验贵族过期时间的
		expireTime = uv.ExpireTime
	}
	isRegistration := uv == nil || !uv.IsActive() || uvt.Level != uv.Level // 若普通贵族生效并且等级相同，则会续期原贵族外观的过期时间
	err = userappearance.AddNobleAppearances(uvt.UserID, expireTime, uvt.Level, isRegistration)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}
