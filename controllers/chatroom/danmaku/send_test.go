package danmaku

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestIsOpen(t *testing.T) {
	assert := assert.New(t)

	t.Cleanup(func() {
		delete(config.Conf.AB, "open_danmaku_time")
	})

	mockOpenTimeUnix := goutil.TimeNow().Unix() + int64(time.Hour.Seconds())
	config.Conf.AB["open_danmaku_time"] = mockOpenTimeUnix
	openTimeUnix, ok := isOpen()
	assert.Equal(mockOpenTimeUnix, openTimeUnix)
	assert.False(ok)

	mockOpenTimeUnix = goutil.TimeNow().Unix() - int64(time.Hour.Seconds())
	config.Conf.AB["open_danmaku_time"] = mockOpenTimeUnix
	openTimeUnix, ok = isOpen()
	assert.Equal(mockOpenTimeUnix, openTimeUnix)
	assert.True(ok)
}

func TestActionDanmakuSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(interface{}) (interface{}, error) {
		return handler.M{"vips": handler.M{}}, nil
	})
	defer cancel()

	var (
		testRoomID   = int64(223344)
		testGoodsID  = int64(556677)
		testBubbleID = int64(1323213)
	)

	cleanup := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIBuyGoods, func(input interface{}) (output interface{}, err error) {
		return new(userapi.BalanceResp), nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cleanup()
	service.Cache10s.SetDefault(keys.KeyBubbles.Format(), testDanmakuBubbles)
	t.Cleanup(func() {
		service.Cache10s.Flush()
		delete(config.Conf.AB, "open_danmaku_time")
	})

	r, err := room.Update(testRoomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)
	require.NotNil(r)
	require.Equal(room.StatusOpenTrue, r.Status.Open)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)
	err = service.LiveDB.Create(&livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeDanmaku,
	}).Error
	require.NoError(err)

	config.Conf.AB["open_danmaku_time"] = time.Date(2666, 1, 1, 12, 0, 0, 0, time.Local).Unix()
	_, err = ActionDanmakuSend(handler.NewTestContext(http.MethodPost, "/send", true, nil))
	assert.EqualError(err, "本功能将于 01.01 12:00 开放")

	config.Conf.AB["open_danmaku_time"] = goutil.TimeNow().Add(-time.Hour).Unix()
	cfg := params.Global{
		Key: params.KeyGlobal,
		Maintain: &params.GlobalMaintain{
			StartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
			EndTime:   goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	key := keys.KeyParams1.Format(cfg.Key)
	hornConfigJSON, err := json.Marshal(cfg)
	require.NoError(err)
	err = service.LRURedis.Set(key, hornConfigJSON, time.Second).Err()
	require.NoError(err)
	_, err = ActionDanmakuSend(nil)
	assert.EqualError(err, fmt.Sprintf("功能维护中，预计 %s 恢复使用", goutil.TimeNow().Add(time.Hour).Format("01-02 15:04")))

	cfg = params.Global{
		Key: params.KeyGlobal,
		Maintain: &params.GlobalMaintain{
			StartTime: goutil.TimeNow().Add(-2 * time.Hour).Unix(),
			EndTime:   goutil.TimeNow().Add(-time.Hour).Unix(),
		},
	}
	hornConfigJSON, err = json.Marshal(cfg)
	require.NoError(err)
	err = service.LRURedis.Set(key, hornConfigJSON, time.Second).Err()
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, handler.M{
		"room_id":     testRoomID,
		"message":     "11",
		"goods_id":    testGoodsID,
		"bubble_id":   testBubbleID,
		"effect_type": 1,
	})
	err = service.Redis.Del(keys.LockDanmakuSendLimit1.Format(c.UserID())).Err()
	require.NoError(err)
	resp, err := ActionDanmakuSend(c)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestNewSendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, handler.M{
		"room_id":     1,
		"message":     "hhhhhhh",
		"goods_id":    1,
		"bubble_id":   1,
		"effect_type": 1,
	})
	param, err := newSendParam(c)
	require.NoError(err)
	assert.NotNil(param)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, handler.M{})
	_, err = newSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestSendParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID   = int64(223344)
		testGoodsID  = int64(556677)
		testBubbleID = int64(1323213)
	)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cleanup()

	r, err := room.Update(testRoomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)
	require.NotNil(r)
	require.Equal(room.StatusOpenTrue, r.Status.Open)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)
	service.Cache10s.SetDefault(keys.KeyBubbles.Format(), testDanmakuBubbles)
	t.Cleanup(func() {
		service.Cache10s.Flush()
	})

	param := &sendParam{
		RoomID:   testRoomID,
		GoodsID:  testGoodsID,
		BubbleID: testBubbleID,
		c:        handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	err = service.Redis.Del(keys.LockDanmakuSendLimit1.Format(param.c.UserID())).Err()
	require.NoError(err)
	err = param.check()
	assert.Equal(err, actionerrors.ErrGoodsNotFound)

	err = service.LiveDB.Create(&livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeDanmaku,
	}).Error
	require.NoError(err)
	param = &sendParam{
		RoomID:   testRoomID,
		GoodsID:  testGoodsID,
		BubbleID: testBubbleID,
		c:        handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	err = service.Redis.Del(keys.LockDanmakuSendLimit1.Format(param.c.UserID())).Err()
	require.NoError(err)
	err = param.check()
	assert.NoError(err)
}

func TestSendParam_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input interface{}) (output interface{}, err error) {
		return "发送成功", nil
	})
	defer cleanup()

	cleanup = mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{
			{
				Pass: false,
			},
		}, nil
	})
	defer cleanup()
	param := &sendParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 123,
			},
		},
		user: &liveuser.Simple{
			UID: 123,
		},
		c: handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	_, err := param.send()
	assert.Equal(actionerrors.ErrDanmakuMessageIllegal, err)

	mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{
			{
				Pass:   true,
				Labels: []string{scan.LabelEvil},
			},
		}, nil
	})
	param = &sendParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 123,
			},
		},
		user: &liveuser.Simple{
			UID: 123,
		},
		c: handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	_, err = param.send()
	assert.Equal(actionerrors.ErrDanmakuMessageSensitive, err)

	mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{}, nil
	})
	cleanup = mrpc.SetMock(userapi.URIBuyGoods, func(input interface{}) (output interface{}, err error) {
		return new(userapi.BalanceResp), nil
	})
	defer cleanup()
	param = &sendParam{
		room: &room.Room{
			OID: primitive.NewObjectID(),
			Helper: room.Helper{
				RoomID: 123,
			},
		},
		user: &liveuser.Simple{
			UID: 123,
		},
		goods: &livegoods.LiveGoods{
			ID: 123,
		},
		danmakuBubble: &bubble.Simple{},
		c:             handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	resp, err := param.send()
	require.NoError(err)
	require.NotNil(resp)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var msg *models.Message
	err = models.MessageCollection().FindOne(ctx, bson.M{"msg_id": resp.MsgID}).Decode(&msg)
	require.NoError(err)
	require.NotNil(msg)
}

func TestSendParam_buyDanmaku(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIBuyGoods, func(input interface{}) (output interface{}, err error) {
		return &userapi.BalanceResp{
			TransactionID: 1,
		}, nil
	})
	defer cleanup()

	param := &sendParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 123,
			},
		},
		user: &liveuser.Simple{
			UID: 123,
		},
		goods: &livegoods.LiveGoods{
			ID: 123,
		},
		c: handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/danmaku/send", true, nil),
	}
	resp, orderID, err := param.buyDanmaku()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(1, resp.TransactionID)
	assert.NotZero(orderID)
}

func TestSendParam_newDanmakuMsg(t *testing.T) {
	assert := assert.New(t)

	param := &sendParam{
		Message: "hhh  hhh",
		user:    &liveuser.Simple{Username: "hhh"},
		danmakuBubble: &bubble.Simple{
			NormalColor:    "#A231DC",
			HighlightColor: "#A231DC",
		},
	}
	msg := param.newDanmakuMsg()
	assert.Equal(`<font color="#A231DC">hhh</font><font color="#A231DC">：hhh&nbsp;&nbsp;hhh</font>`, msg)
}

func TestSendParam_incrRoomMsgCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID int64 = 132789371283
	)

	key := keys.KeyRoomsMeta1.Format(testRoomID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	param := &sendParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: testRoomID,
			},
		},
	}
	param.incrRoomMsgCount()

	count, err := service.Redis.HGet(key, "message_count").Int64()
	require.NoError(err)
	assert.EqualValues(1, count)
}

func TestSendParam_incrUserMsgCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 132789371283
		testRoomID int64 = ************
	)

	key := keys.KeyUserMetaMessageCount2.Format(
		goutil.BeginningOfDay(goutil.TimeNow()).Format(util.TimeFormatYMDWithNoSpace),
		testUserID,
	)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	param := &sendParam{
		user: &liveuser.Simple{UID: testUserID, Username: "hhh"},
		room: &room.Room{
			Helper: room.Helper{
				RoomID: testRoomID,
			},
		},
		goods: &livegoods.LiveGoods{
			Price: 123,
		},
	}
	param.incrUserMsgCount()

	count, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(1, count)
}

func TestSendParam_addRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID    = int64(223344)
		testCreatorID = int64(1)
		testUserID    = int64(2)
		now           = goutil.TimeNow()

		userWeekKey = usersrank.Key(usersrank.TypeWeek, now)

		onceMsgkey   = keys.KeyRoomsRankPointMessageLock2.Format(testUserID, now.Format(util.TimeFormatYMDWithNoSpace))
		onceMsgfield = fmt.Sprintf("messagelock_%d", testRoomID)
	)
	require.NoError(service.Redis.HDel(onceMsgkey, onceMsgfield).Err())
	require.NoError(service.Redis.ZRem(userWeekKey, strconv.FormatInt(testCreatorID, 10)).Err())

	param := &sendParam{
		user: &liveuser.Simple{UID: testUserID, Username: "hhh"},
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    testRoomID,
				CreatorID: testCreatorID,
			},
		},
		goods: &livegoods.LiveGoods{
			Price: 123,
		},
	}
	param.addRevenue()

	source, err := service.Redis.ZScore(userWeekKey, strconv.FormatInt(testCreatorID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(123, source)
	// 确保这里没有累加当日的第一条消息（用于判断普通消息是否加主播榜）
	assert.True(param.room.IsDailyFirstMsg(param.user.UserID()))
}
