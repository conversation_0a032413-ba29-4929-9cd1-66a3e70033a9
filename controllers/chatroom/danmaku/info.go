package danmaku

import (
	"sort"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type danmakuInfoResp struct {
	GoodsID int64                      `json:"goods_id"`
	Price   int                        `json:"price"`
	Tip     string                     `json:"tip"`
	Bubbles []*danmakuBubble           `json:"bubbles"`
	Effects []*livegoods.DanmakuEffect `json:"effects"`
}

type danmakuBubble struct {
	ID      int64  `json:"id"`
	Type    int    `json:"type"`
	IconURL string `json:"icon_url"`
	Lock    int    `json:"lock,omitempty"`

	userLevel  int
	nobleType  int
	nobleLevel int
}

// ActionDanmakuInfo 获取直播间付费弹幕信息
/**
 * @api {get} /api/v2/chatroom/danmaku/info 获取直播间付费弹幕信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "goods_id": 1,
 *       "price": 50, // 单位钻石
 *       "tip": "50 钻石/条（原价 100 钻石/条，活动限定折扣中）", // 在输入框中显示的提示
 *       "bubbles": [
 *         {
 *           "id": 1,
 *           "type": 1, // 1: 常规; 2: 房管; 3: 个人专属; 4: 贵族气泡; 5: 等级气泡; 6: 超粉气泡
 *           "icon_url": "http://static.maoercdn.com/1-icon.png"
 *         },
 *         {
 *           "id": 2,
 *           "type": 2,
 *           "icon_url": "http://static.maoercdn.com/2-icon.png"
 *         },
 *         {
 *           "id": 4,
 *           "type": 6,
 *           "icon_url": "http://static.maoercdn.com/4-icon.png",
 *           "lock": 1 // 1: 待解锁 (type = 6 时，点击出现弹窗跳转超粉购买页，其他 type 仅显示待解锁状态), 0 或没有该字段表示可选
 *         }
 *       ],
 *       "effects": [
 *         {
 *           "type": 1,
 *           "icon_url": "http://static.maoercdn.com/effect-icon.png",
 *         }
 *       ]
 *     }
 *   }
 *
 */
func ActionDanmakuInfo(c *handler.Context) (handler.ActionResponse, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	u, err := liveuser.FindOneSimple(bson.M{"user_id": c.UserID()},
		&liveuser.FindOptions{FindTitles: true, FindVips: true, RoomID: roomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	goods, err := livegoods.FindShowingDanmaku()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if goods == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	more, err := goods.UnmarshalMore()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range more.Danmaku.Effects {
		more.Danmaku.Effects[i].IconURL = storage.ParseSchemeURL(more.Danmaku.Effects[i].IconURL)
	}
	resp := danmakuInfoResp{
		GoodsID: goods.ID,
		Price:   goods.Price,
		Tip:     more.Danmaku.Tip,
		Effects: more.Danmaku.Effects,
		Bubbles: make([]*danmakuBubble, 0, 8), // 一般情况下气泡不会超过 8 个，预分配容量 8 个
	}
	// 取出所有的 bubbles 后判断是否为付费弹幕的气泡，以及用户是否可以使用该气泡
	bubbles, err := bubble.AllBubbles()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range bubbles {
		b := bubbles[i]
		if !isShowDanmakuBubble(u, b, true) {
			continue
		}
		resp.Bubbles = append(resp.Bubbles, &danmakuBubble{
			ID:         b.BubbleID,
			Type:       b.Danmaku.Category,
			IconURL:    b.Danmaku.IconURL,
			Lock:       goutil.BoolToInt(b.Danmaku.Category == bubble.DanmakuCategorySuperFan && !u.IsSuperFan),
			userLevel:  b.Danmaku.UserLevel,
			nobleType:  b.Danmaku.NobleType,
			nobleLevel: b.Danmaku.NobleLevel,
		})
	}
	resp.checkResp()
	return resp, nil
}

// checkResp 检查响应数据
func (resp *danmakuInfoResp) checkResp() {
	sort.Slice(resp.Bubbles, func(i, j int) bool {
		if resp.Bubbles[i].Type != resp.Bubbles[j].Type {
			return resp.Bubbles[i].Type < resp.Bubbles[j].Type
		}
		if resp.Bubbles[i].userLevel != resp.Bubbles[j].userLevel {
			return resp.Bubbles[i].userLevel > resp.Bubbles[j].userLevel
		}
		// 贵族付费弹幕气泡的 nobleType 只有普通贵族（包含体验贵族）和上神
		if resp.Bubbles[i].nobleType != resp.Bubbles[j].nobleType {
			return resp.Bubbles[i].nobleType > resp.Bubbles[j].nobleType
		}
		if resp.Bubbles[i].nobleLevel != resp.Bubbles[j].nobleLevel {
			return resp.Bubbles[i].nobleLevel > resp.Bubbles[j].nobleLevel
		}
		return resp.Bubbles[i].ID > resp.Bubbles[j].ID
	})
}

// isShowDanmakuBubble 判断是否显示弹幕气泡
// showLock 表示是否显示已加锁的气泡（当前仅超粉气泡使用该参数，若为 true 则表示显示已加锁的气泡，若为 false 时是否显示已加锁的气泡由用户是否是超粉决定）
func isShowDanmakuBubble(u *liveuser.Simple, b bubble.Bubble, showLock bool) bool {
	if b.Type != bubble.TypeDanmaku || b.Danmaku == nil {
		return false
	}
	danmakuBubble := b.Danmaku
	// TODO: 支持房管专用气泡
	switch danmakuBubble.Category {
	case bubble.DanmakuCategoryNormal:
		return true
	case bubble.DanmakuCategorySuperFan:
		// NOTICE: 下发列表时需要返回是否解锁，其他时候用于检查是否有权限使用
		return showLock || u.IsSuperFan
	case bubble.DanmakuCategoryPersonal:
		return danmakuBubble.UserID > 0 && danmakuBubble.UserID == u.UserID()
	case bubble.DanmakuCategoryNoble:
		vips := u.UserVipMap
		if danmakuBubble.NobleType == vip.TypeLiveNoble {
			normalNoble := vips[vip.TypeLiveNoble]
			trialNoble := vips[vip.TypeLiveTrialNoble]
			// 仅拥有用户当前等级的贵族气泡
			return (normalNoble != nil && normalNoble.IsActive() && normalNoble.Level == danmakuBubble.NobleLevel) ||
				(trialNoble != nil && trialNoble.IsActive() && trialNoble.Level == danmakuBubble.NobleLevel)
		} else if danmakuBubble.NobleType == vip.TypeLiveHighness {
			return vips[vip.TypeLiveHighness] != nil && vips[vip.TypeLiveHighness].IsActive()
		}
	case bubble.DanmakuCategoryUserLevel:
		return danmakuBubble.UserLevel > 0 && danmakuBubble.UserLevel <= usercommon.Level(u.Contribution)
	}
	return false
}
