package danmaku

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

var testDanmakuBubbles = []bubble.Bubble{
	{
		Type:     bubble.TypeNotify,
		BubbleID: 1,
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 2,
		Danmaku: &bubble.Danmaku{
			Category: bubble.DanmakuCategoryNormal,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 3,
		Danmaku: &bubble.Danmaku{
			UserID:   1,
			Category: bubble.DanmakuCategoryPersonal,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 4,
		Danmaku: &bubble.Danmaku{
			NobleType:  vip.TypeLiveNoble,
			NobleLevel: vip.NobleLevel3,
			Category:   bubble.DanmakuCategoryNoble,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 5,
		Danmaku: &bubble.Danmaku{
			NobleType:  vip.TypeLiveNoble,
			NobleLevel: vip.NobleLevel4,
			Category:   bubble.DanmakuCategoryNoble,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 6,
		Danmaku: &bubble.Danmaku{
			UserLevel: 80,
			Category:  bubble.DanmakuCategoryUserLevel,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 7,
		Danmaku: &bubble.Danmaku{
			UserLevel: 120,
			Category:  bubble.DanmakuCategoryUserLevel,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 8,
		Danmaku: &bubble.Danmaku{
			Category: bubble.DanmakuCategorySuperFan,
		},
	},
	{
		Type:     bubble.TypeDanmaku,
		BubbleID: 1323213,
		Danmaku: &bubble.Danmaku{
			Category: bubble.DanmakuCategoryNormal,
		},
	},
}

func TestActionDanmakuInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := vip.MockVipList()
	defer cancel()

	service.Cache10s.Set(keys.KeyBubbles.Format(), testDanmakuBubbles, 0)
	defer func() {
		service.Cache10s.Flush()
	}()

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	_, err := ActionDanmakuInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/?room_id=18113499", true, nil)
	key := keys.KeyNobleUserVips1.Format(c.User().ID)
	uvJSON, err := json.Marshal(map[int]*vip.UserVip{
		vip.TypeLiveTrialNoble: {UserID: c.UserID(), Type: vip.TypeLiveTrialNoble,
			Level: vip.NobleLevel4, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
	})
	require.NoError(err)
	err = service.Redis.Set(key, uvJSON, 30*time.Second).Err()
	require.NoError(err)
	defer func() {
		service.Redis.Del(key)
	}()
	_, err = liveuser.Update(c.UserID(), bson.M{"contribution": usercommon.LevelStart[130]})
	require.NoError(err)
	resp, err := ActionDanmakuInfo(c)
	require.NoError(err)
	res := resp.(danmakuInfoResp)
	require.NotEmpty(res.Bubbles)
	bubbleIDs := make([]int64, 0, len(res.Bubbles))
	for i := range res.Bubbles {
		bubbleIDs = append(bubbleIDs, res.Bubbles[i].ID)
	}
	assert.Equal([]int64{1323213, 2, 5, 7, 6, 8}, bubbleIDs)
	assert.Equal(1, res.Bubbles[5].Lock)
}

func TestDanmakuInfoResp_checkResp(t *testing.T) {
	assert := assert.New(t)

	resp := &danmakuInfoResp{
		Bubbles: []*danmakuBubble{
			{ID: 1, Type: bubble.DanmakuCategorySuperFan},
			{ID: 2, Type: bubble.DanmakuCategoryNoble, nobleType: 2, nobleLevel: 1},
			{ID: 3, Type: bubble.DanmakuCategoryNoble, nobleType: 1, nobleLevel: 5},
			{ID: 4, Type: bubble.DanmakuCategoryPersonal},
			{ID: 5, Type: bubble.DanmakuCategoryPersonal},
			{ID: 6, Type: bubble.DanmakuCategoryUserLevel, userLevel: 150},
			{ID: 7, Type: bubble.DanmakuCategoryUserLevel, userLevel: 80},
			{ID: 8, Type: bubble.DanmakuCategoryUserLevel, userLevel: 120},
			{ID: 9, Type: bubble.DanmakuCategoryNormal},
		},
	}
	resp.checkResp()
	bubbleIDs := make([]int64, 0, len(resp.Bubbles))
	for i := range resp.Bubbles {
		bubbleIDs = append(bubbleIDs, resp.Bubbles[i].ID)
	}
	assert.Equal([]int64{9, 5, 4, 2, 3, 6, 8, 7, 1}, bubbleIDs)
}

func TestIsShowDanmakuBubble(t *testing.T) {
	assert := assert.New(t)

	showBubbleFunc := func(u *liveuser.Simple) []int64 {
		bubblesIDs := make([]int64, 0, len(testDanmakuBubbles))
		for i := range testDanmakuBubbles {
			if isShowDanmakuBubble(u, testDanmakuBubbles[i], true) {
				bubblesIDs = append(bubblesIDs, testDanmakuBubbles[i].BubbleID)
			}
		}
		return bubblesIDs
	}

	// 用户专属
	assert.ElementsMatch([]int64{2, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999}))
	assert.ElementsMatch([]int64{2, 3, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 1}))
	// 贵族专属
	assert.ElementsMatch([]int64{2, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999, UserVipMap: map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Level: 1, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
		vip.TypeLiveTrialNoble: {Level: vip.NobleLevel4, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()},
	}}))

	assert.ElementsMatch([]int64{2, 5, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999, UserVipMap: map[int]*vip.UserVip{
		vip.TypeLiveNoble: {Level: vip.NobleLevel4, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
	}}))
	// 等级专属
	assert.ElementsMatch([]int64{2, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999, Contribution: usercommon.LevelStart[10]}))
	assert.ElementsMatch([]int64{2, 6, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999, Contribution: usercommon.LevelStart[90]}))
	assert.ElementsMatch([]int64{2, 6, 7, 8, 1323213}, showBubbleFunc(&liveuser.Simple{UID: 999999, Contribution: usercommon.LevelStart[119]}))
}
