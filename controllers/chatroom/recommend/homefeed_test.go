package recommend

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionChatroomOpenHomeFeed(t *testing.T) {
	t.Run("参数校验失败", func(t *testing.T) {
		tests := []struct {
			name string
			ctx  *handler.Context
		}{
			{"空参数", handler.NewRPCTestContext("/rpc/live-service/chatroom/open/home-feed", nil)},
			{"无效用户ID", handler.NewRPCTestContext("/rpc/live-service/chatroom/open/home-feed", handler.M{"user_id": "invalid"})},
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				_, err := ActionHomeFeed(test.ctx)
				assert.Equal(t, actionerrors.ErrParams, err)
			})
		}
	})

	t.Run("房间不存在", func(t *testing.T) {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		testRoomID := int64(9999999)
		_, err := room.Collection().DeleteOne(ctx, bson.M{"room_id": testRoomID})
		require.NoError(t, err)

		c := handler.NewRPCTestContext("/rpc/live-service/chatroom/open/home-feed", handler.M{
			"user_id":  123,
			"room_ids": []int64{testRoomID},
		})

		_, err = ActionHomeFeed(c)
		assert.Equal(t, actionerrors.ErrCannotFindRoom, err)
	})

	t.Run("成功获取直播卡片", func(t *testing.T) {
		cancel := mrpc.SetMock("im://online/count", func(any) (any, error) {
			return map[string]any{
				"count": 0,
			}, nil
		})
		defer cancel()
		cancel = mrpc.SetMock(userapi.URLGetUserDramaCVs, func(any) (any, error) {
			return handler.M{"cvs": nil}, nil
		})
		defer cancel()
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		roomID := int64(10056790)
		testAnnouncement := "测试公告内容"
		_, err := room.Collection().InsertOne(context.Background(), room.Room{
			Helper: room.Helper{
				CreatorID:    165,
				RoomID:       roomID,
				Status:       room.Status{Open: room.StatusOpenTrue},
				Name:         "测试房间" + strconv.FormatInt(roomID, 10),
				NameClean:    "测试房间" + strconv.FormatInt(roomID, 10),
				Announcement: testAnnouncement,
			},
		})
		require.NoError(t, err)
		defer func() {
			_, err := room.Collection().DeleteOne(ctx, bson.M{"room_id": roomID})
			require.NoError(t, err)
		}()

		c := handler.NewRPCTestContext("/rpc/live-service/chatroom/open/home-feed", handler.M{
			"user_id":  123,
			"room_ids": []int64{roomID},
		})

		resp, err := ActionHomeFeed(c)
		require.NoError(t, err)
		result := resp.(*recommendedLiveCardResponse)
		assert.NotNil(t, result)
		assert.Greater(t, len(result.Cards), 0)
		assert.Equal(t, roomID, result.Cards[0].RoomID)
		assert.Equal(t, testAnnouncement, result.Cards[0].Announcement)
	})
}

func TestBuildRecommendTags(t *testing.T) {
	testUserID := int64(123456789)

	t.Run("Success", func(t *testing.T) {
		now := goutil.TimeNow()

		testRooms := []*room.Helper{
			{RoomID: 50001, NameClean: "测试房间 50001", CreatorID: 50001, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 50002, NameClean: "测试房间 50002", CreatorID: 50002, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 50003, NameClean: "测试房间 50003", CreatorID: 50003, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 50004, NameClean: "测试房间 50004", CreatorID: 50004, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 50005, NameClean: "测试房间 50005", CreatorID: 50005, Status: room.Status{Open: room.StatusOpenTrue}},
		}
		createTestRooms(t, testRooms)
		rooms := getTestSimples(t, []int64{50001, 50002, 50003, 50004, 50005})
		testMedals := []*livemedal.Simple{
			{
				RoomID:    testRooms[0].RoomID,
				CreatorID: testRooms[0].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
			},
			{
				RoomID:    testRooms[1].RoomID,
				CreatorID: testRooms[1].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
				Mini:      livemedal.Mini{SuperFan: &livemedal.SuperFan{ExpireTime: now.Add(time.Hour * 24).Unix()}},
			},
		}
		createTestMedals(t, testMedals)
		createTestFollowing(t, testUserID, []int64{testRooms[2].CreatorID})
		setMockGetUserDramaCVs(t, []int64{testRooms[3].CreatorID})
		createTestFreeGiftRecords(t, testUserID, []int64{testRooms[4].RoomID})

		c := handler.NewTestContext(http.MethodGet, "/", true, nil)
		buildRecommendTags(rooms, c.UserContext(), testUserID)
		require.Len(t, rooms, len(testRooms))

		for _, r := range rooms {
			assert.NotNil(t, r.RecommendTag)
			switch r.RoomID {
			case testRooms[0].RoomID:
				assert.Equal(t, "已点亮粉丝牌", r.RecommendTag.Text)
			case testRooms[1].RoomID:
				assert.Equal(t, "已成为超粉", r.RecommendTag.Text)
			case testRooms[2].RoomID:
				assert.Equal(t, "我关注的", r.RecommendTag.Text)
			case testRooms[3].RoomID:
				assert.Equal(t, "我追的剧集声优", r.RecommendTag.Text)
			case testRooms[4].RoomID:
				assert.Equal(t, "最近看过", r.RecommendTag.Text)
			default:
				assert.Failf(t, "unexpected room id", "room id: %d", r.RoomID)
			}
		}
	})
}

func getTestSimples(t *testing.T, roomIDs []int64) []*room.Simple {
	simples, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(bson.M{
			"room_id":    1,
			"creator_id": 1,
		}),
		nil,
	)
	require.NoError(t, err)
	return simples
}
