package recommend

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func listRankRooms(rankType int, mongoOpt *options.FindOptions, opt ...*room.FindOptions) ([]*room.Simple, error) {
	ranks, err := usersrank.FindRank(goutil.TimeNow(), rankType)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return findRankOpenRooms(ranks, mongoOpt, opt...)
}

func listLastRankRooms(rankType int, mongoOpt *options.FindOptions, opt ...*room.FindOptions) ([]*room.Simple, error) {
	lastRanks, err := usersrank.FindLastRank(rankType, goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return findRankOpenRooms(lastRanks, mongoOpt, opt...)
}

func findRankOpenRooms(rankInfos []usersrank.Info, mongoOpt *options.FindOptions, opt ...*room.FindOptions) ([]*room.Simple, error) {
	if len(rankInfos) == 0 {
		return []*room.Simple{}, nil
	}

	creatorIDs := make([]int64, 0, len(rankInfos))
	for _, info := range rankInfos {
		creatorIDs = append(creatorIDs, info.UserID)
	}
	// 过滤掉未开播的主播
	rooms, err := room.ListSimples(bson.M{"status.open": room.StatusOpenTrue, "creator_id": bson.M{"$in": creatorIDs}}, mongoOpt, opt...)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomMap := goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Simple)

	rankRooms := make([]*room.Simple, 0, len(roomMap))
	for _, info := range rankInfos {
		if r, ok := roomMap[info.UserID]; ok {
			rankRooms = append(rankRooms, r)
		}
	}
	return rankRooms, nil
}
