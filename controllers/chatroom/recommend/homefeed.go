package recommend

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/controllers/utils/feed"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type recommendedLiveCardParam struct {
	UserID  int64   `json:"user_id"`
	RoomIDs []int64 `json:"room_ids"`

	uc mrpc.UserContext
}

type recommendedLiveCardResponse struct {
	Cards []*liveCard `json:"cards"`
}

type liveCard struct {
	RoomID          int64              `json:"room_id"`
	Name            string             `json:"name"`
	CreatorID       int64              `json:"creator_id"`
	CreatorUsername string             `json:"creator_username"`
	CreatorIconURL  string             `json:"creator_iconurl"`
	CoverURL        string             `json:"cover_url"`
	CatalogID       int64              `json:"catalog_id"`
	CustomTagID     int64              `json:"custom_tag_id,omitempty"`
	Announcement    string             `json:"announcement"`
	Status          room.Status        `json:"status"`
	Statistics      *room.Statistics   `json:"statistics,omitempty"`
	ExtraInfo       *room.PreviewIntro `json:"extra_info,omitempty"`
	RecommendTag    *room.RecommendTag `json:"recommend_tag,omitempty"`
	CatalogTag      *room.RecommendTag `json:"catalog_tag,omitempty"`
	CustomTag       *room.RecommendTag `json:"custom_tag,omitempty"`
}

// ActionHomeFeed 批量获取 Feed 流猜你喜欢直播卡中需要列出的所有信息
/**
 * @api {post} /rpc/live-service/chatroom/open/home-feed 推荐直播卡
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number[]} room_ids 直播间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "cards": [
 *         {
 *           "room_id": 108324,
 *           "name": "直播间名称",
 *           "creator_id": 123213,
 *           "creator_username": "浅声暖唱の龙年大吉",
 *           "creator_iconurl": "https://static.maoercdn.com/avatars/202410/07/f70f67dfcb93e4bf70a24584df7cff9d095351.png",
 *           "cover_url": "https://static.maoercdn.com/fmcovers/202410/08/435992d3bf5ae1e987eeba63ac00f247.jpg",
 *           "catalog_id": 1,
 *           "custom_tag_id": 2,
 *           "announcement": "直播间公告内容",
 *           "extra_info": { // 展示补充信息，未下发时展示直播间简介，最多展示一行，超过则展示 ...
 *             "icon_url": "https://static.maoercdn.com/live/labelicon/livelist/lasthour01-1.png",
 *             "title": "正在免费抽《广播剧标题》福袋"
 *           },
 *           "recommend_tag": { // 主播关系标签, 如果没有则为空
 *             "type": 6, // 6: 搜索页用户关系标签, 7: 二级分区标签, 4: 个性词条
 *             "text": "最近看过",
 *           },
 *           "catalog_tag": {
 *             "type": 7,
 *             "text": "音乐",
 *           },
 *           "custom_tag": {
 *             "type": 4,
 *             "text": "元气少年",
 *           },
 *           "status": {
 *             "open": 1,
 *             "red_packet": 1, // 当前待抢红包或可抢红包状态, 0: 或不存在表示无待抢红包或者可抢红包
 *             "lucky_bag": 1, // 直播间福袋状态，0: 或不存在表示直播间没有福袋; 1: 直播间存在进行中的福袋
 *             ...
 *           },
 *           "statistics": {
 *             "score": 421314 // 直播间热度
 *             ...
 *           }
 *         }
 *       ]
 *     }
 *   }
 */
func ActionHomeFeed(c *handler.Context) (handler.ActionResponse, error) {
	// 获取参数
	param, err := newRecommendedLiveCardParam(c)
	if err != nil {
		return nil, err
	}

	// 通过参数获取直播卡信息
	resp, err := param.getRecommendedLiveCard()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newRecommendedLiveCardParam(c *handler.Context) (*recommendedLiveCardParam, error) {
	param := &recommendedLiveCardParam{}
	if err := c.BindJSON(param); err != nil {
		return nil, actionerrors.ErrParams
	}

	// 校验传入的 room_ids 是否为空
	if len(param.RoomIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	// 校验传入的 room_ids 是否均存在
	existsAll, err := room.ExistsAll(param.RoomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !existsAll {
		return nil, actionerrors.ErrCannotFindRoom
	}

	param.uc = c.UserContext()
	return param, nil
}

func (p *recommendedLiveCardParam) getRecommendedLiveCard() (*recommendedLiveCardResponse, error) {
	p.RoomIDs = util.Uniq(p.RoomIDs)
	filter := bson.M{"room_id": bson.M{"$in": p.RoomIDs}}
	projection := bson.M{
		"room_id":          1,
		"name":             1,
		"creator_id":       1,
		"creator_username": 1,
		"cover":            1,
		"statistics":       1,
		"status":           1,
		"catalog_name":     1,
		"custom_tag_id":    1,
		"custom_tag":       1,
		"recommend_tag":    1,
		"catalog_id":       1,
		"announcement":     1,
	}
	simples, err := room.ListSimples(filter, options.Find().SetProjection(projection),
		&room.FindOptions{FindCreator: true, FindCustomTag: true, FindCatalogInfo: true, FindLuckyBag: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(simples) != len(p.RoomIDs) {
		m := util.ToMap(simples, func(simple *room.Simple) int64 {
			return simple.RoomID
		})
		list := make([]int64, 0)
		for _, r := range p.RoomIDs {
			if _, ok := m[r]; !ok {
				list = append(list, r)
			}
		}
		return nil, actionerrors.ErrNotFound(fmt.Sprintf("有不存在的直播间 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(list, ",")))
	}

	// 构建直播标签
	if p.UserID > 0 {
		buildRecommendTags(simples, p.uc, p.UserID)
	}

	liveCards := make([]*liveCard, 0, len(simples))
	// 批量构建 PreviewIntro 映射
	previewIntroMap := make(map[int64]*room.PreviewIntro, len(simples))
	for _, v := range simples {
		previewIntroMap[v.RoomID] = utils.BuildPreviewIntro(v, p.uc)
	}
	// 传入直播标签和直播补充信息标签
	for _, v := range simples {
		card := &liveCard{
			RoomID:          v.RoomID,
			Name:            v.Name,
			CreatorID:       v.CreatorID,
			CreatorUsername: v.CreatorUsername,
			CreatorIconURL:  v.CreatorIconURL,
			CoverURL:        v.CoverURL,
			CatalogID:       v.CatalogID,
			CustomTagID:     v.CustomTagID,
			Status:          *v.Status,
			Statistics:      v.Statistics,
			ExtraInfo:       previewIntroMap[v.RoomID], // 从映射中直接获取
			Announcement:    v.Announcement,
		}
		// 主播关系标签，如果没有则不下发
		if v.RecommendTag != nil && v.RecommendTag.Type == room.RecommendTagTypeSearchUserRelated {
			card.RecommendTag = v.RecommendTag
		}
		// 二级分区标签
		if v.CatalogName != "" {
			card.CatalogTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSubCatalog,
				Text: v.CatalogName,
			}
		}
		// 个性词条
		if v.CustomTag != nil {
			card.CustomTag = &room.RecommendTag{
				Type: room.RecommendTagTypeCustomTag,
				Text: v.CustomTag.TagName,
			}
		}

		liveCards = append(liveCards, card)
	}

	resp := &recommendedLiveCardResponse{
		Cards: liveCards,
	}
	return resp, nil
}

// buildRecommendTags 构建直播标签
/*
直播标签：
1. 已成为超粉
2. 已点亮粉丝牌
3. 我关注的
4. 我追的剧集声优
5. 最近看过
6. 直播二级分区 + 个性词条
*/
func buildRecommendTags(simples []*room.Simple, uc mrpc.UserContext, userID int64) {
	medals, err := livemedal.ListUserAll(userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 已成为超粉
	superfanRoomIDs := make([]int64, 0, len(medals))
	superfanRoomIDMap := make(map[int64]struct{}, len(superfanRoomIDs))
	// 已点亮粉丝牌
	medalRoomIDs := make([]int64, 0, len(medals))
	medalRoomIDMap := make(map[int64]struct{}, len(medalRoomIDs))
	for _, v := range medals {
		if livemedal.IsSuperFanActive(v.SuperFan) {
			superfanRoomIDs = append(superfanRoomIDs, v.RoomID)
			superfanRoomIDMap[v.RoomID] = struct{}{}
		} else {
			medalRoomIDs = append(medalRoomIDs, v.RoomID)
			medalRoomIDMap[v.RoomID] = struct{}{}
		}
	}
	// 我关注的
	followedCreatorIDs, err := attentionuser.AllFollowedCreatorIDs(userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	followedCreatorIDMap := make(map[int64]struct{}, len(followedCreatorIDs))
	for _, v := range followedCreatorIDs {
		followedCreatorIDMap[v] = struct{}{}
	}
	// 我追的剧集声优
	dramaCVs, err := userapi.ListUserDramaCVs(uc, userID, userapi.DramaCVSceneSearch)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	creatorIDs := make([]int64, 0, len(dramaCVs))
	for _, v := range dramaCVs {
		creatorIDs = append(creatorIDs, v.UserID)
	}
	dramaCVsCreatorIDMap := make(map[int64]struct{}, len(creatorIDs))
	for _, v := range creatorIDs {
		dramaCVsCreatorIDMap[v] = struct{}{}
	}
	// 最近看过
	interactedRooms, err := feed.ListUser7DaysInteractedRoomRank(userID, nil, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	interactedRoomIDMap := make(map[int64]struct{}, len(interactedRooms))
	for _, v := range interactedRooms {
		interactedRoomIDMap[v.RoomID] = struct{}{}
	}

	// 处理 RecommendTag
	for _, v := range simples {
		if _, ok := superfanRoomIDMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "已成为超粉",
			}
		} else if _, ok := medalRoomIDMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "已点亮粉丝牌",
			}
		} else if _, ok := followedCreatorIDMap[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "我关注的",
			}
		} else if _, ok := dramaCVsCreatorIDMap[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "我追的剧集声优",
			}
		} else if _, ok := interactedRoomIDMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "最近看过",
			}
		} else {
			v.RecommendTag = nil
		}
	}
}
