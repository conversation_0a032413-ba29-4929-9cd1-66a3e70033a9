package recommend

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestSaveRecommendationSourcesCache(t *testing.T) {
	testUserID := int64(10000)

	sources := [][]*room.Simple{
		{
			{
				RoomID: 1001,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeConfig,
					Text: "已成为超粉",
				},
			},
			{
				RoomID: 1002,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeConfig,
					Text: "我的关注",
				},
			},
		},
		{
			{
				RoomID: 2001,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeCV,
					Text: "声优直播",
				},
			},
		},
		{
			{
				RoomID: 3001,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeConfig,
					Text: "优质直播",
				},
			},
		},
	}

	t.Run("Success", func(t *testing.T) {
		saveRecommendationSourcesCache(testUserID, sources)

		key := keys.KeySearchRecommendRooms1.Format(testUserID)
		defer service.LRURedis.Del(key)

		exists, err := service.LRURedis.Exists(key).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists)
	})

	t.Run("EmptySources", func(t *testing.T) {
		saveRecommendationSourcesCache(testUserID, [][]*room.Simple{})

		key := keys.KeySearchRecommendRooms1.Format(testUserID)
		defer service.LRURedis.Del(key)

		exists, err := service.LRURedis.Exists(key).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists)
	})
}

func TestLoadRecommendationSourcesFromCache(t *testing.T) {
	testUserID := int64(10000)

	sources := [][]*room.Simple{
		{
			{
				RoomID: 1001,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeConfig,
					Text: "已成为超粉",
				},
			},
			{
				RoomID: 1002,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeConfig,
					Text: "我的关注",
				},
			},
		},
		{
			{
				RoomID: 2001,
				RecommendTag: &room.RecommendTag{
					Type: room.RecommendTagTypeCV,
					Text: "声优直播",
				},
			},
		},
	}

	t.Run("Success", func(t *testing.T) {
		saveRecommendationSourcesCache(testUserID, sources)

		key := keys.KeySearchRecommendRooms1.Format(testUserID)
		defer service.LRURedis.Del(key)

		listRooms := func(roomIDs []int64) ([]*room.Simple, error) {
			rooms := make([]*room.Simple, 0, len(roomIDs))
			for _, roomID := range roomIDs {
				room := &room.Simple{RoomID: roomID}
				rooms = append(rooms, room)
			}
			return rooms, nil
		}

		loadedSources := loadRecommendationSourcesFromCache(testUserID, listRooms)
		require.NotNil(t, loadedSources)
		require.Len(t, loadedSources, len(sources))

		for i, source := range loadedSources {
			require.Len(t, source, len(sources[i]))
			for j, room := range source {
				assert.Equal(t, sources[i][j].RoomID, room.RoomID)
			}
		}
	})

	t.Run("InvalidCacheData", func(t *testing.T) {
		key := keys.KeySearchRecommendRooms1.Format(testUserID)
		defer service.LRURedis.Del(key)

		err := service.LRURedis.Set(key, "invalid json data", time.Hour).Err()
		require.NoError(t, err)

		loadedSources := loadRecommendationSourcesFromCache(testUserID, nil)
		assert.Nil(t, loadedSources)
	})
}
