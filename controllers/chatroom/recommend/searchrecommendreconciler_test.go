package recommend

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/room"
)

func TestRecommendationReconciler_reconcile(t *testing.T) {
	testRooms, err := room.ListSimples(bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}, options.Find().SetLimit(20))
	require.NoError(t, err)
	require.Equal(t, 20, len(testRooms))

	// Create test data
	sources := [][]*room.Simple{
		// Related rooms
		{
			testRooms[8],
			testRooms[7],
			testRooms[6],
		},
		// CV rooms
		{
			testRooms[5],
			testRooms[4],
			testRooms[3],
		},
		// Quality rooms
		{
			testRooms[2],
			testRooms[1],
			testRooms[0],
		},
	}

	appointments := []appointment{
		{pos: 2, room: testRooms[11]},
		{pos: 1, room: testRooms[10]},
		{pos: 0, room: testRooms[9]},
	}

	substitutes := &recommendationLazySource{
		loader: func() []*room.Simple {
			return testRooms[12:]
		},
	}

	blockedCreatorIDs := map[int64]struct{}{
		testRooms[0].CreatorID: {},
		testRooms[1].CreatorID: {},
	}
	suppressionRoomIDs := map[int64]struct{}{
		testRooms[2].RoomID: {},
		testRooms[3].RoomID: {},
	}

	reconciler := newRecommendationReconciler(
		sources,
		appointments,
		blockedCreatorIDs,
		suppressionRoomIDs,
		substitutes,
	)

	result := reconciler.reconcile(3)

	assert.NotNil(t, result)
	assert.Equal(t, 9, len(result))
	assert.True(t, substitutes.loaded)

	for _, room := range result {
		_, ok := blockedCreatorIDs[room.CreatorID]
		assert.False(t, ok, "Room from blocked creator found")
		_, ok = suppressionRoomIDs[room.RoomID]
		assert.False(t, ok, "Suppressed room found")
	}

	assert.Equal(t, testRooms[9].RoomID, result[0].RoomID)
	assert.Equal(t, testRooms[10].RoomID, result[1].RoomID)
	assert.Equal(t, testRooms[11].RoomID, result[2].RoomID)
	// Since non-appointment rooms are randomly shuffled, we use assert.Contains to verify their presence in the expected slice
	for _, room := range result[3:6] {
		assert.Contains(t, []int64{testRooms[7].RoomID, testRooms[4].RoomID, testRooms[13].RoomID}, room.RoomID)
	}
	for _, room := range result[6:] {
		assert.Contains(t, []int64{testRooms[6].RoomID, testRooms[14].RoomID, testRooms[15].RoomID}, room.RoomID)
	}
}
