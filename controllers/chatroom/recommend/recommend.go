package recommend

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/feed"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	maxGroupCount = 12 // 推荐房间最大分组数
	maxRoomCount  = 8  // 每组最大房间数

	cacheValidDuration = 15 * time.Minute // 缓存有效时长
)

type recommendMarker struct {
	index      int
	createTime int64
}

func parseRecommendMarker(markerStr string, userID int64) (*recommendMarker, error) {
	now := goutil.TimeNow()
	marker := recommendMarker{}
	if markerStr == "" {
		marker.createTime = now.Unix()
		if userID == 0 {
			marker.createTime = (marker.createTime / 60) * 60 // 对于游客，快照时间以分钟为单位
		}
		return &marker, nil
	}

	markers := strings.Split(markerStr, ",")
	if len(markers) != 2 {
		return nil, errors.New("参数格式错误")
	}
	var err error
	marker.index, err = strconv.Atoi(markers[0])
	if err != nil {
		return nil, err
	}
	marker.createTime, err = strconv.ParseInt(markers[1], 10, 64)
	if err != nil {
		return nil, err
	}
	if marker.createTime <= now.Add(-cacheValidDuration).Unix() {
		marker.createTime = now.Unix()
	}
	if marker.index < 0 || marker.index >= maxGroupCount {
		return nil, errors.New("参数值不在有效范围内")
	}
	return &marker, nil
}

func buildRecommendMarker(index int, createTime int64) string {
	return fmt.Sprintf("%d,%d", index, createTime)
}

type recommendCache struct {
	CreatorID    int64  `json:"creator_id"`
	RoomID       int64  `json:"room_id"`
	RecommendTag string `json:"recommend_tag"`
}

type recommend struct {
	noticeRoomID  int64
	resetMarker   *recommendMarker
	marker        *recommendMarker // 总是会创建一个新对象
	recommendType int

	userID                 int64
	noticeRoom             *room.Simple
	rooms                  []*room.Simple
	excludeFollowUserIDs   []int64
	markerPagination       goutil.MarkerPagination
	lastHourTop3Map        map[int64]string   // map[CreatorID]Icon
	tabRecommendMap        map[int64]int      // map[RoomID]Sort
	hotSuppressionRoomsMap map[int64]struct{} // map[RoomID]struct{}
	blockCreatorMap        map[int64]struct{} // map[CreatorID]struct{}

	opt *room.FindOptions
	uc  mrpc.UserContext
}

func newRecommend(c *handler.Context, marker, resetMarker string, noticeRoomID int64, recommendType int) (*recommend, error) {
	r := &recommend{
		noticeRoomID:  noticeRoomID,
		recommendType: recommendType,
		userID:        c.UserID(),
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
			FindLuckyBag:    true,
			Projection:      recommendProjection,
		},
		uc: c.UserContext(),
	}
	var err error
	r.marker, err = parseRecommendMarker(marker, r.userID)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if resetMarker != "" {
		r.resetMarker, err = parseRecommendMarker(resetMarker, r.userID)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
	}

	rankParam, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	icons := rankParam.LastHourRecommendIcons(goutil.TimeNow())
	lastHourTop3CreatorIDs, err := usersrank.ListLastHourTop3CreatorIDs()
	if err != nil {
		return r, actionerrors.NewErrServerInternal(err, nil)
	}
	r.lastHourTop3Map = make(map[int64]string, len(lastHourTop3CreatorIDs))
	for i, creatorID := range lastHourTop3CreatorIDs {
		r.lastHourTop3Map[creatorID] = icons[i]
	}

	r.blockCreatorMap = buildBlockCreatorMap(r.uc, r.userID)
	r.hotSuppressionRoomsMap = hotSuppressionRoomsMap()

	// 第一页要插入运营推荐，推荐列表去重和排序插入使用
	if r.marker.index == 0 {
		recommends, err := liverecommendedelements.ListLiveTabRecommend()
		if err != nil {
			return nil, err
		}
		r.tabRecommendMap = make(map[int64]int, len(recommends))
		if len(recommends) > 0 {
			for _, v := range recommends {
				r.tabRecommendMap[v.ElementID] = v.Sort
			}
		}
	}

	if recommendType == typeExcludeFollowUser {
		r.excludeFollowUserIDs, err = attentionuser.AllFollowedCreatorIDs(r.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	return r, nil
}

func (r *recommend) recommendRoom() error {
	err := r.findNoticeRoom()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	recommendGroups, err := r.loadCache()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	var recommendList []*room.Simple
	var index int
	if len(recommendGroups) > 0 {
		if r.resetMarker != nil {
			// 后续请求的 marker 中的缓存创建时间应继承自 reset_marker 中的创建时间
			r.marker.createTime = r.resetMarker.createTime
			// 客户端传递 reset_marker 参数，推荐组重新随机排序，并从第一页开始
			recommendGroups = r.randomCacheGroups(recommendGroups)
		} else {
			index = r.marker.index
		}

		recommendList, err = r.getPageRecommendList(index, true, recommendGroups, nil)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 缓存列表中直播间关播、新拉黑等情况导致不满 1 组时补位，和已生成的 12 组去重
		recommendList = r.fillSimpleToLength(recommendList, recommendGroups)
	} else {
		// 没有缓存则重新生成
		var simpleGroups [][]*room.Simple
		recommendGroups, simpleGroups = r.buildSnapshot()
		if r.resetMarker != nil {
			index = 0 // 如果是新生成的快照，则从第一页开始
		} else if r.marker != nil {
			index = r.marker.index
		}
		recommendList, err = r.getPageRecommendList(index, false, recommendGroups, simpleGroups)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	recommendList = randomizeRoomList(recommendList)
	// 只有第一页处理运营占位插入推荐房间
	if index == 0 {
		recommendList, err = r.recommendInsert(recommendList)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	if r.noticeRoom != nil {
		// 保证第一个直播间跟底部【直播】tab 展示的头像一致
		recommendRooms := make([]*room.Simple, len(recommendList)+1)
		recommendRooms[0] = r.noticeRoom
		copy(recommendRooms[1:], recommendList)
		r.rooms = recommendRooms
	} else {
		r.rooms = recommendList
	}
	// 更多推荐固定展示为 8 个（1 组），运营插入推荐房间或底部 tab 推荐主播可能超过 8 个（1 组），需要截断
	if r.recommendType == typeExcludeFollowUser && len(recommendList) > maxRoomCount {
		r.rooms = r.rooms[:maxRoomCount]
	}
	r.addRecommendTags(r.rooms, recommendGroups)
	nextIndex := index + 1
	if nextIndex >= maxGroupCount {
		r.markerPagination = goutil.MarkerPagination{
			HasMore: false,
			Marker:  buildRecommendMarker(index, r.marker.createTime), // 客户端下次请求会在 reset_marker 中带上
		}
	} else {
		r.markerPagination = goutil.MarkerPagination{
			HasMore: true,
			Marker:  buildRecommendMarker(nextIndex, r.marker.createTime),
		}
	}
	return nil
}

func (r *recommend) buildSnapshot() (recommendGroups [][]recommendCache, simpleGroups [][]*room.Simple) {
	uniqueCreatorIDs := make(map[int64]struct{}, maxGroupCount*maxRoomCount)
	recommendGroups = make([][]recommendCache, maxGroupCount) // 缓存使用
	simpleGroups = make([][]*room.Simple, maxGroupCount)
	for i := 0; i < maxGroupCount; i++ {
		// 可能存在运营占位 8 个和底部 tab 展示头像主播
		recommendGroups[i] = make([]recommendCache, 0, (maxRoomCount*2)+1)
		simpleGroups[i] = make([]*room.Simple, 0, (maxRoomCount*2)+1)
	}

	// 将取数 2、3、4、5、6 按照要求数量填充到二维数组的每个列表中
	addRecommendGroup := func(items []*room.Simple, countPerRow int, recommendTag func(insertIndex int) string) {
		itemIndex := 0
		for groupIndex := 0; groupIndex < maxGroupCount; groupIndex++ {
			for count := 0; count < countPerRow; itemIndex++ {
				if itemIndex >= len(items) {
					return
				}

				v := items[itemIndex]
				if _, ok := uniqueCreatorIDs[v.CreatorID]; ok {
					continue
				}

				if _, ok := r.blockCreatorMap[v.CreatorID]; ok {
					continue
				}
				if _, ok := r.hotSuppressionRoomsMap[v.RoomID]; ok {
					continue
				}
				if _, ok := r.tabRecommendMap[v.RoomID]; ok {
					continue
				}

				recommendGroups[groupIndex] = append(recommendGroups[groupIndex], recommendCache{
					CreatorID:    v.CreatorID,
					RoomID:       v.RoomID,
					RecommendTag: recommendTag(count),
				})
				simpleGroups[groupIndex] = append(simpleGroups[groupIndex], v)
				uniqueCreatorIDs[v.CreatorID] = struct{}{}
				count++
			}
		}
	}

	// 组内每个列表不满时补位使用
	fillRecommends := func(fetchItems func() []*room.Simple) {
		var missing bool
		// 判断推荐组是否填满，有一个不满就需要补位
		for _, group := range recommendGroups {
			if len(group) < maxRoomCount {
				missing = true
				break
			}
		}
		if !missing {
			return
		}
		itemIndex := 0
		items := fetchItems()
		for i := 0; i < len(recommendGroups); i++ {
			if len(recommendGroups[i]) < maxRoomCount {
				for len(recommendGroups[i]) < maxRoomCount && itemIndex < len(items) {
					v := items[itemIndex]
					itemIndex++
					if _, ok := uniqueCreatorIDs[v.CreatorID]; ok {
						continue
					}

					if _, ok := r.blockCreatorMap[v.CreatorID]; ok {
						continue
					}
					if _, ok := r.hotSuppressionRoomsMap[v.RoomID]; ok {
						continue
					}
					if _, ok := r.tabRecommendMap[v.RoomID]; ok {
						continue
					}
					recommendGroups[i] = append(recommendGroups[i], recommendCache{
						CreatorID: v.CreatorID,
						RoomID:    v.RoomID,
					})
					simpleGroups[i] = append(simpleGroups[i], v)
					uniqueCreatorIDs[v.CreatorID] = struct{}{}
				}
			}
		}
	}

	mongoOpt := options.Find().SetProjection(recommendProjection)
	// 只有登录的用户才查询获取超粉 / 点亮粉丝牌 / 已关注 / 关注声优 / 交互过的主播
	// TODO: 后续部分操作是可以在协程中一起调用，可以减少耗时
	if r.userID > 0 {
		// 获取超粉 / 点亮粉丝牌 / 已关注，每组插入一个
		items1, err := feed.ListMedalAndFollowedRoomsRank(r.userID, mongoOpt, r.opt)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		// NOTICE: 结构体类型不一样，暂时不能用统一方法处理
		groupIndex := 0
		for _, v := range items1 {
			if groupIndex >= maxGroupCount {
				break
			}
			if _, ok := uniqueCreatorIDs[v.Room.CreatorID]; ok {
				continue
			}
			if _, ok := r.blockCreatorMap[v.Room.CreatorID]; ok {
				continue
			}
			if _, ok := r.hotSuppressionRoomsMap[v.Room.RoomID]; ok {
				continue
			}
			if _, ok := r.tabRecommendMap[v.Room.RoomID]; ok {
				continue
			}
			var recommendTag string
			switch v.RankType {
			case feed.RankTypeSuperFan:
				recommendTag = "已成为超粉"
			case feed.RankTypeMedal:
				recommendTag = "已点亮粉丝牌"
			case feed.RankTypeFollow:
				recommendTag = "我的关注"
			}
			recommendGroups[groupIndex] = append(recommendGroups[groupIndex], recommendCache{
				CreatorID:    v.Room.CreatorID,
				RoomID:       v.Room.RoomID,
				RecommendTag: recommendTag,
			})
			simpleGroups[groupIndex] = append(simpleGroups[groupIndex], v.Room)
			uniqueCreatorIDs[v.Room.CreatorID] = struct{}{}
			groupIndex++
		}

		// 已追剧集声优主播，每组插入一个
		items2 := r.findDramaCVs(mongoOpt)
		addRecommendGroup(items2, 1, func(_ int) string {
			return "我追的剧集声优"
		})

		// 未关注但最近 7 天交互过的主播，每组插入两个
		items3, err := feed.ListUser7DaysInteractedRoomRank(r.userID, mongoOpt, r.opt)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		addRecommendGroup(items3, 2, func(_ int) string {
			return "最近看过"
		})
	}

	// 新人榜上榜主播，每组插入两个
	items4, err := listRankRooms(usersrank.TypeNova, mongoOpt, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	addRecommendGroup(items4, 2, func(insertIndex int) string {
		// 只对每组第 1 个直播间展示
		if insertIndex == 0 {
			return "超级新主播"
		}
		return ""
	})

	// 上小时榜 TOP 20，每组插入两个
	items5, err := listLastRankRooms(usersrank.TypeHourLiveTab, mongoOpt, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	addRecommendGroup(items5, 2, func(_ int) string {
		return ""
	})

	// 本小时榜 TOP 20，每组插入一个
	items6, err := listRankRooms(usersrank.TypeHourLiveTab, mongoOpt, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	addRecommendGroup(items6, 1, func(_ int) string {
		return ""
	})

	// 【补位】热度前列主播
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
	}
	// 优先从热度 Top 50 的直播间中随机取出
	fillRecommends(func() []*room.Simple {
		items7, err := room.ListRandomTopScoreRooms(filter, 0, 50, 50, r.opt)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return items7
	})
	// 其次从热度 Top 50-100 的直播间中随机取出
	fillRecommends(func() []*room.Simple {
		items8, err := room.ListRandomTopScoreRooms(filter, 50, 100, 50, r.opt)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return items8
	})
	r.saveCache(recommendGroups)
	return
}

func (r *recommend) getPageRecommendList(index int, fromCache bool, recommendGroups [][]recommendCache, simpleGroups [][]*room.Simple) ([]*room.Simple, error) {
	if r.recommendType == typeExcludeFollowUser {
		recommendCache := r.buildExcludeFollowRooms(recommendGroups, index)
		if fromCache {
			recommendCache = r.excludeRecommendList(recommendCache) // 从缓存排除掉新拉黑的主播
		}
		// TODO: 实时查询时，更多推荐可被优化成不再次查房间信息
		recommendRooms, err := r.buildMoreRecommend(recommendCache)
		if err != nil {
			return nil, err
		}
		return recommendRooms, nil
	}

	if fromCache {
		recommendCache := recommendGroups[index]
		recommendCache = r.excludeRecommendList(recommendCache) // 从缓存排除掉新拉黑的主播
		return r.refreshRoomMessage(recommendCache)
	}
	recommendRooms := simpleGroups[index]
	recommendList := make([]*room.Simple, 0, maxRoomCount)
	for _, v := range recommendRooms {
		if r.noticeRoom != nil && r.noticeRoom.CreatorID == v.CreatorID {
			continue
		}
		recommendList = append(recommendList, v)
	}
	return recommendList, nil
}

// 缓存中某个组存在关播或者新拉黑的直播间，开始补位
func (r *recommend) fillSimpleToLength(simples []*room.Simple, recommendCacheList [][]recommendCache) []*room.Simple {
	limit := maxRoomCount
	if r.noticeRoom != nil {
		limit = maxRoomCount - 1
	}

	if len(simples) >= limit {
		return simples
	}

	blockMap := r.blockCreatorMap
	excludeUserIDs := make([]int64, 0, len(blockMap)+(maxGroupCount*maxRoomCount)+1) // 黑名单 + 最大推荐列表长度 + 底部 tab 推荐主播
	for v := range blockMap {
		excludeUserIDs = append(excludeUserIDs, v)
	}
	for _, v := range recommendCacheList {
		for _, v2 := range v {
			excludeUserIDs = append(excludeUserIDs, v2.CreatorID)
		}
	}
	if r.noticeRoom != nil {
		excludeUserIDs = append(excludeUserIDs, r.noticeRoom.CreatorID)
	}
	if r.recommendType == typeExcludeFollowUser {
		excludeUserIDs = append(excludeUserIDs, r.excludeFollowUserIDs...)
	}

	excludeRoomIDs := make([]int64, 0, len(r.hotSuppressionRoomsMap)+len(r.tabRecommendMap))
	for v := range r.hotSuppressionRoomsMap {
		excludeRoomIDs = append(excludeRoomIDs, v)
	}
	for v := range r.tabRecommendMap {
		excludeRoomIDs = append(excludeRoomIDs, v)
	}

	// 【补位】热度前列主播
	// 优先从热度 Top 50 的直播间中随机取出
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
	}
	if len(excludeUserIDs) > 0 {
		filter["creator_id"] = bson.M{"$nin": sets.Uniq(excludeUserIDs)}
	}
	if len(excludeRoomIDs) > 0 {
		filter["room_id"] = bson.M{"$nin": sets.Uniq(excludeRoomIDs)}
	}
	items7, err := room.ListRandomTopScoreRooms(filter, 0, 50, 50, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	for _, v := range items7 {
		if len(simples) >= limit {
			return simples
		}
		simples = append(simples, v)
	}

	// 其次从热度 Top 50-100 的直播间中随机取出
	items8, err := room.ListRandomTopScoreRooms(filter, 50, 100, 50, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	for _, v := range items8 {
		if len(simples) >= limit {
			return simples
		}
		simples = append(simples, v)
	}
	return simples
}

func (r *recommend) findNoticeRoom() error {
	if r.noticeRoomID <= 0 {
		return nil
	}

	var err error
	r.noticeRoom, err = room.FindSimple(bson.M{
		"room_id":     r.noticeRoomID,
		"status.open": room.StatusOpenTrue,
	}, r.opt)
	if err != nil {
		return err
	}
	return nil
}

func (r *recommend) refreshRoomMessage(recommends []recommendCache) ([]*room.Simple, error) {
	creatorIDs := make([]int64, 0, len(recommends))
	for _, v := range recommends {
		if r.noticeRoom != nil && r.noticeRoom.CreatorID == v.CreatorID {
			continue
		}
		creatorIDs = append(creatorIDs, v.CreatorID)
	}

	mongoOpt := options.Find().SetProjection(recommendProjection)
	openRooms, err := room.ListSimples(bson.M{
		"creator_id":  bson.M{"$in": sets.Uniq(creatorIDs)},
		"status.open": room.StatusOpenTrue,
	}, mongoOpt, r.opt)
	if err != nil {
		return nil, err
	}
	openRoomMap := goutil.ToMap(openRooms, "CreatorID").(map[int64]*room.Simple)
	filterRooms := make([]*room.Simple, 0, len(creatorIDs))
	for _, v := range creatorIDs {
		room, ok := openRoomMap[v]
		if !ok {
			continue
		}
		filterRooms = append(filterRooms, room)
	}

	return filterRooms, nil
}

func (r *recommend) recommendInsert(rooms []*room.Simple) ([]*room.Simple, error) {
	type element struct {
		Sort      int
		ElementID int64
	}
	roomIDs := make([]int64, 0, len(r.tabRecommendMap))
	elements := make([]element, 0, len(r.tabRecommendMap))
	for elementID, sort := range r.tabRecommendMap {
		// 运营推荐跟首位推荐一样时不插入
		if r.noticeRoom != nil && r.noticeRoom.RoomID == elementID {
			continue
		}
		roomIDs = append(roomIDs, elementID)
		elements = append(elements, element{
			Sort:      sort,
			ElementID: elementID,
		})
	}
	mongoOpt := options.Find().SetProjection(recommendProjection)
	openRooms, err := room.ListSimples(bson.M{
		"room_id":     bson.M{"$in": sets.Uniq(roomIDs)},
		"status.open": room.StatusOpenTrue,
	}, mongoOpt, r.opt)
	if err != nil {
		return nil, err
	}
	// 按 sort 顺序对 recommend 进行排序
	sort.Slice(elements, func(i, j int) bool {
		return elements[i].Sort < elements[j].Sort
	})
	roomMap := goutil.ToMap(openRooms, "RoomID").(map[int64]*room.Simple)
	// 遍历运营配置的直播间并插入推荐列表中，推荐列表已过滤运营配置直播间
	for _, item := range elements {
		simple, ok := roomMap[item.ElementID]
		if !ok {
			continue
		}
		index := item.Sort - 1
		if index > len(rooms) {
			rooms = append(rooms, simple)
		} else {
			rooms = append(rooms[:index], append([]*room.Simple{simple}, rooms[index:]...)...)
		}
	}
	return rooms, nil
}

func (r *recommend) addRecommendTags(allRooms []*room.Simple, recommends [][]recommendCache) {
	// 推荐标签缓存，个性词条和小时榜实时获取
	// 推荐标签 > 上小时榜角标 > 运营配置角标 > 个性词条
	// TODO: 运营配置角标三期做
	recommendMap := make(map[int64]recommendCache, len(recommends))
	for _, r := range recommends {
		for _, v := range r {
			if v.RecommendTag != "" { // 补位和上小时榜、本小时榜没有推荐标签
				recommendMap[v.CreatorID] = v
			}
		}
	}
	for _, v := range allRooms {
		if rm, ok := recommendMap[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeRecommend,
				Text: rm.RecommendTag,
			}
		} else if icon, ok := r.lastHourTop3Map[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type:    room.RecommendTagTypeLastHourRank,
				IconURL: service.Storage.Parse(icon),
			}
		} else if v.CustomTag != nil {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeCustomTag,
				Text: v.CustomTag.TagName,
			}
		}
	}
}

func (r *recommend) buildExcludeFollowRooms(groups [][]recommendCache, startIndex int) []recommendCache {
	if startIndex >= len(groups) {
		return []recommendCache{}
	}
	var creatorIDs []recommendCache
	total := len(groups)
	currentIndex := startIndex
	for {
		// 将当前组的所有元素添加到结果中
		creatorIDs = append(creatorIDs, groups[currentIndex]...)
		// 移动到下一组
		currentIndex = (currentIndex + 1) % total
		// 如果回到起始位置，停止循环
		if currentIndex == startIndex {
			break
		}
	}
	// 排除关注列表中的房间
	excludeFollowUserIDs := make(map[int64]struct{}, len(r.excludeFollowUserIDs))
	for _, creatorID := range r.excludeFollowUserIDs {
		excludeFollowUserIDs[creatorID] = struct{}{}
	}

	result := make([]recommendCache, 0, len(creatorIDs))
	for _, v := range creatorIDs {
		if _, ok := excludeFollowUserIDs[v.CreatorID]; !ok {
			result = append(result, v)
		}
	}
	return result
}

func (r *recommend) buildMoreRecommend(recommendCreatorIDs []recommendCache) ([]*room.Simple, error) {
	creatorIDs := make([]int64, 0, len(recommendCreatorIDs))
	for _, v := range recommendCreatorIDs {
		// 首位直播间页面去重
		if r.noticeRoom != nil && r.noticeRoom.CreatorID == v.CreatorID {
			continue
		}
		creatorIDs = append(creatorIDs, v.CreatorID)
	}

	mongoOpt := options.Find().SetProjection(recommendProjection)
	openRooms, err := room.ListSimples(bson.M{
		"creator_id":  bson.M{"$in": sets.Uniq(creatorIDs)},
		"status.open": room.StatusOpenTrue,
	}, mongoOpt, r.opt)
	if err != nil {
		return nil, err
	}
	openRoomMap := goutil.ToMap(openRooms, "CreatorID").(map[int64]*room.Simple)
	// 获取更多推荐，获取 8 个开播房，如果有入口推荐直播间则少一个
	remaining := int64(maxRoomCount)
	if r.noticeRoom != nil {
		remaining = maxRoomCount - 1
	}
	filterRooms := make([]*room.Simple, 0, len(recommendCreatorIDs))
	for _, v := range recommendCreatorIDs {
		if remaining <= 0 {
			break
		}
		room, ok := openRoomMap[v.CreatorID]
		if !ok {
			continue
		}
		filterRooms = append(filterRooms, room)
		remaining--
	}
	return filterRooms, nil
}

func (r *recommend) excludeRecommendList(recommends []recommendCache) []recommendCache {
	newRecommendList := make([]recommendCache, 0, len(recommends))
	for _, v := range recommends {
		if _, ok := r.blockCreatorMap[v.CreatorID]; ok {
			continue
		}
		if _, ok := r.hotSuppressionRoomsMap[v.RoomID]; ok {
			continue
		}
		if _, ok := r.tabRecommendMap[v.RoomID]; ok {
			continue
		}
		newRecommendList = append(newRecommendList, v)
	}
	return newRecommendList
}

func (r *recommend) saveCache(recommendCreatorIDs [][]recommendCache) {
	cacheJSON, err := json.Marshal(recommendCreatorIDs)
	if err != nil {
		logger.Error(err)
		return
	}

	createTime := r.marker.createTime
	cacheKey := keys.KeyRecommendRooms2.Format(r.userID, createTime)
	err = service.LRURedis.Set(cacheKey, cacheJSON, cacheValidDuration+(time.Minute*3)).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (r *recommend) loadCache() ([][]recommendCache, error) {
	createTime := r.marker.createTime
	if r.resetMarker != nil {
		createTime = r.resetMarker.createTime
	}
	cacheKey := keys.KeyRecommendRooms2.Format(r.userID, createTime)
	cache, err := service.LRURedis.Get(cacheKey).Bytes()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return nil, nil
		}
		return nil, err
	}
	var recommendCache [][]recommendCache
	if err = json.Unmarshal(cache, &recommendCache); err != nil {
		return nil, err
	}
	return recommendCache, nil
}

func (r *recommend) randomCacheGroups(groups [][]recommendCache) [][]recommendCache {
	randomIndices := rand.Perm(len(groups))
	shuffled := make([][]recommendCache, len(groups))
	for i, randIndex := range randomIndices {
		shuffled[i] = groups[randIndex]
	}
	// 刷新缓存
	r.saveCache(shuffled)
	return shuffled
}

func randomizeRoomList(rooms []*room.Simple) []*room.Simple {
	randomIndices := rand.Perm(len(rooms))
	// 创建一个新的切片存放随机顺序的元素
	shuffled := make([]*room.Simple, len(rooms))
	// 通过随机下标访问原始数组，并将元素放入新切片
	for i, randIndex := range randomIndices {
		shuffled[i] = rooms[randIndex]
	}
	return shuffled
}

func buildBlockCreatorMap(uc mrpc.UserContext, userID int64) map[int64]struct{} {
	if userID <= 0 {
		return nil
	}

	var blockCreatorMap map[int64]struct{}
	key := keys.KeyUserRecommendBlockList1.Format(userID)
	cacheBlockCreatorListBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	if len(cacheBlockCreatorListBytes) > 0 {
		if err = json.Unmarshal(cacheBlockCreatorListBytes, &blockCreatorMap); err == nil {
			return blockCreatorMap
		}
		logger.Error(err)
		// PASS
	}

	blockedByUserList, err := userapi.UserBlockList(uc, userID, userapi.BlockListTypeBlockedByUser)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	whoBlockedUserList, err := userapi.UserBlockList(uc, userID, userapi.BlockListTypeWhoBlockedUser)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	blockCreatorList, err := room.ListSimples(bson.M{
		"creator_id": bson.M{"$in": sets.Uniq(append(blockedByUserList, whoBlockedUserList...))},
	}, nil, nil)
	if err != nil {
		logger.WithField("user_id", userID).Error(err)
		return nil
	}

	blockCreatorMap = make(map[int64]struct{}, len(blockCreatorList))
	for _, v := range blockCreatorList {
		blockCreatorMap[v.CreatorID] = struct{}{}
	}

	cacheBlockCreatorListBytes, err = json.Marshal(blockCreatorMap)
	if err != nil {
		logger.Error(err)
		// PASS
		return blockCreatorMap
	}
	err = service.LRURedis.Set(key, cacheBlockCreatorListBytes, time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return blockCreatorMap
}

func hotSuppressionRoomsMap() map[int64]struct{} {
	hotSuppressionRoomIDs, err := usersrank.ListHotSuppressionRoomIDs()
	if err != nil {
		logger.Error(err)
		return make(map[int64]struct{})
	}
	blackMap := make(map[int64]struct{}, len(hotSuppressionRoomIDs))
	for _, roomID := range hotSuppressionRoomIDs {
		blackMap[roomID] = struct{}{}
	}
	return blackMap
}

func (r *recommend) findDramaCVs(mongoOpt *options.FindOptions) []*room.Simple {
	cvInfos, err := userapi.ListUserDramaCVs(r.uc, r.userID, userapi.DramaCVSceneLiveRecommend)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}

	creatorIDs := make([]int64, 0, len(cvInfos))
	for _, v := range cvInfos {
		creatorIDs = append(creatorIDs, v.UserID)
	}
	simples, err := room.ListSimples(bson.M{
		"creator_id":  bson.M{"$in": sets.Uniq(creatorIDs)},
		"status.open": room.StatusOpenTrue,
	}, mongoOpt, r.opt)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}

	simpleMap := goutil.ToMap(simples, "CreatorID").(map[int64]*room.Simple)
	result := make([]*room.Simple, 0, len(simples))
	for _, v := range creatorIDs {
		if simple, ok := simpleMap[v]; ok {
			result = append(result, simple)
		}
	}
	return result
}
