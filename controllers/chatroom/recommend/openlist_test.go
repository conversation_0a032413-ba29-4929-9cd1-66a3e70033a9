package recommend

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBuildRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	list, err := buildRecommendList(c, "", 10, nil)
	require.NoError(err)
	assert.NotNil(list)
}

func TestNewRecommendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param := newRecommendParam(c, "", 10, nil)
	require.NotNil(param)
	assert.EqualValues(10, param.pageSize)
	assert.EqualValues(1, param.p)
	assert.NotNil(param.opt)
	assert.False(param.opt.FindLuckyBag)

	c = handler.NewTestContext(http.MethodGet, "", true, nil)
	c.Equip().FromApp = true
	param = newRecommendParam(c, "2", 10, nil)
	require.NotNil(param)
	assert.EqualValues(10, param.pageSize)
	assert.EqualValues(2, param.p)
	assert.NotNil(param.opt)
	assert.True(param.opt.FindLuckyBag)
}

func TestRecommendParam_findList(t *testing.T) {
	assert := assert.New(t)

	param := recommendParam{p: 100, pageSize: 20}
	assert.NoError(param.findList())
	assert.NotNil(param.Data)
	assert.False(param.Pagination.HasMore)
	assert.Empty(param.Pagination.Marker)

	param = recommendParam{p: 1, pageSize: 20}
	assert.NoError(param.findList())
	assert.Greater(len(param.Data), 1)
}

func TestRecommendParam_findRecommendRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	openingRoom, err := room.Find(3192516)
	require.NoError(err)

	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	param := recommendParam{
		roomSimpleList: &roomSimpleList{
			Data: []*room.Simple{{RoomID: 100}},
		},
		p: 1,
	}
	// 测试上小时榜无人
	require.NoError(service.Redis.Del(key).Err())
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom)
	// 主播没开播的情况
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: -12, Score: 12}).Err())
	param.findRecommendRoom()
	assert.Nil(param.lastHourRoom, 0)
	// 测试小时榜
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: openingRoom.CreatorID, Score: 516}).Err())
	require.NoError(service.Redis.Expire(key, 5*time.Minute).Err())
	param.findRecommendRoom()
	require.NotNil(param.lastHourRoom)
	assert.Equal(2, *param.lastHourRoom.Index)

	// 神话和上小时榜是同一个的情况
	now := goutil.TimeNow()
	nobleKey := keys.KeyNobleRecommend1.Format(now.Minute() / 10 * 10)
	service.Cache5Min.Set(nobleKey, &livenoblerecommend.NobleRecommend{RoomID: openingRoom.RoomID}, 0)
	param.lastHourRoom = nil
	param.nrRoom = nil
	param.findRecommendRoom()
	require.NotNil(param.nrRoom)
	assert.Equal(5, *param.nrRoom.Index)
	require.NotNil(param.lastHourRoom)
	assert.Equal(2, *param.lastHourRoom.Index)
}

func TestOpenListParam_assignRecommend(t *testing.T) {
	assert := assert.New(t)

	var param *recommendParam
	resetResp := func(roomID []int64) {
		param = &recommendParam{
			roomSimpleList: &roomSimpleList{},
			pageSize:       20,
		}
		param.Data = make([]*room.Simple, len(roomID))
		for k, v := range roomID {
			param.Data[k] = &room.Simple{
				RoomID: v,
			}
		}
	}
	assertData := func(data []*room.Simple, expected []int64) {
		roomIDs := make([]int64, len(data))
		for i := range data {
			roomIDs[i] = data[i].RoomID
		}
		assert.Equal(expected, roomIDs)
	}
	t.Run("没有推荐", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4})
	})
	t.Run("神话推荐保底位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5})
		param.nrRoom = &room.Simple{RoomID: 6}
		param.nrRoom.SetIndex(5)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4, 6, 5})
	})
	t.Run("神话推荐不是保底位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6, 7, 8, 9})
		param.recommendRooms = []*room.Simple{{RoomID: 4}, {RoomID: 7}, {RoomID: 5}}
		param.recommendRooms[0].SetIndex(2)
		param.recommendRooms[1].SetIndex(4)
		param.recommendRooms[2].SetIndex(6)
		param.nrRoom = &room.Simple{RoomID: 1}
		param.nrRoom.SetIndex(5)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 4, 2, 7, 3, 5, 6, 8, 9})
	})
	t.Run("上小时榜和推荐直播间", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.lastHourRoom = &room.Simple{RoomID: 6}
		param.lastHourRoom.SetIndex(2)
		param.recommendRooms = []*room.Simple{{RoomID: 5}}
		param.recommendRooms[0].SetIndex(4)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 6, 2, 5, 3, 4})
	})
	t.Run("固定位直播间太过靠后，向前补位", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.recommendRooms = []*room.Simple{{RoomID: 5}}
		param.recommendRooms[0].SetIndex(8)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 2, 3, 4, 5})
	})
	t.Run("小时榜逻辑和配置冲突", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4})
		param.lastHourRoom = &room.Simple{RoomID: 5}
		param.lastHourRoom.SetIndex(2)
		param.recommendRooms = []*room.Simple{{RoomID: 1}}
		param.recommendRooms[0].SetIndex(2)
		param.assignRecommend()
		assertData(param.Data, []int64{2, 5, 1, 3, 4})
	})
	t.Run("神话推荐和配置冲突", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6})
		param.recommendRooms = []*room.Simple{{RoomID: 6}}
		param.recommendRooms[0].SetIndex(3)
		param.nrRoom = &room.Simple{RoomID: 5}
		param.nrRoom.SetIndex(3)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 5, 6, 2, 3, 4})
	})
	t.Run("神话推荐和配置冲突完全", func(t *testing.T) {
		resetResp([]int64{1, 2, 3, 4, 5, 6})
		param.recommendRooms = []*room.Simple{
			{RoomID: 6},
			{RoomID: 7},
			{RoomID: 8},
		}
		param.recommendRooms[0].SetIndex(1)
		param.recommendRooms[1].SetIndex(2)
		param.recommendRooms[2].SetIndex(3)
		param.nrRoom = &room.Simple{RoomID: 5}
		param.nrRoom.SetIndex(3)
		param.assignRecommend()
		assertData(param.Data, []int64{5, 6, 7, 8, 1, 2, 3, 4})
	})
	t.Run("热门列表中只有【上小时榜第一】一个直播间", func(t *testing.T) {
		resetResp([]int64{1})
		param.lastHourRoom = &room.Simple{RoomID: 1}
		param.lastHourRoom.SetIndex(2)
		param.assignRecommend()
		assertData(param.Data, []int64{1})
	})
	t.Run("小时榜，神话推荐，固定位推荐同时存在", func(t *testing.T) {
		// 按照热度排序：直播间 A > 【神话推荐】>【上小时榜第一】>【固定第 3】
		resetResp([]int64{1, 2, 3, 4})
		// 【上小时榜第一】
		param.lastHourRoom = &room.Simple{RoomID: 3}
		param.lastHourRoom.SetIndex(2)
		// 神话推荐
		param.nrRoom = &room.Simple{RoomID: 2}
		param.nrRoom.SetIndex(5)
		// 固定
		param.recommendRooms = []*room.Simple{
			{RoomID: 4},
		}
		param.recommendRooms[0].SetIndex(4)
		param.assignRecommend()
		assertData(param.Data, []int64{1, 3, 2, 4})
	})
	t.Run("优先保证神话推荐和上小时榜第一", func(t *testing.T) {
		// 按照热度排序：直播间 A > 直播间 B > 直播间 C >【固定第 2】>【固定第 3】>【固定第 4】>【上小时榜第一】>【神话推荐】
		resetResp([]int64{1, 2, 3, 4, 5, 6, 7, 8})
		// 【上小时榜第一】
		param.lastHourRoom = &room.Simple{RoomID: 7}
		param.lastHourRoom.SetIndex(2)
		// 神话推荐
		param.nrRoom = &room.Simple{RoomID: 8}
		param.nrRoom.SetIndex(5)
		// 固定
		param.recommendRooms = []*room.Simple{
			{RoomID: 4}, {RoomID: 5}, {RoomID: 6},
		}
		param.recommendRooms[0].SetIndex(2)
		param.recommendRooms[1].SetIndex(3)
		param.recommendRooms[2].SetIndex(4)

		param.assignRecommend()
		assertData(param.Data, []int64{8, 7, 4, 5, 6, 1, 2, 3})
	})
}
