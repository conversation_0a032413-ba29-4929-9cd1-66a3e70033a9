package recommend

import (
	"encoding/json"
	"fmt"
	"sort"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/controllers/utils/feed"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/livecreatorrecommend"
	"github.com/MiaoSiLa/live-service/models/livedb/liveuser7daysrewardcreatorrank"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liveextrabanner"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// searchRecommendGroupCount 搜索推荐列表分组数量
const searchRecommendGroupCount = 3

type searchChatroomOpenRecommendListResp struct {
	Rooms    []searchRecommendRoom `json:"rooms"`
	LuckyBag *luckyBag             `json:"lucky_bag,omitempty"`
}

type searchRecommendRoom struct {
	RoomID          int64  `json:"room_id"`
	Name            string `json:"name"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl"`
	CoverURL        string `json:"cover_url"`

	RecommendTag *room.RecommendTag `json:"recommend_tag,omitempty"`
	Status       *room.Status       `json:"status,omitempty"`
}

type luckyBag struct {
	Title   string `json:"title"`    // 广告标题
	OpenURL string `json:"open_url"` // 点击后跳转的链接
	Trace   string `json:"trace"`    // 埋点上报字段
}

type traceInfo struct {
	EnterRoomID int64 `json:"enter_room_id,omitempty"` // 点击该模块将进入的直播间房间号
}

type searchRecommendListParam struct {
	c *handler.Context

	respRooms    []searchRecommendRoom
	respLuckyBag *luckyBag
}

// ActionChatroomOpenSearchRecommendList 搜索页显示直播开播推荐列表
/**
 * @api {get} /api/v2/chatroom/open/search-recommend-list 搜索页显示直播开播推荐列表
 * @apiDescription 列出直播开播推荐列表和福袋广告条，最多返回 9 个直播间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": { // 返回 null 时隐藏搜索页正在直播模块
 *       "rooms": [
 *         {
 *           "room_id": 108324,
 *           "name": "直播间名称",
 *           "creator_id": 123213,
 *           "creator_username": "浅声暖唱の龙年大吉",
 *           "creator_iconurl": "https://static.maoercdn.com/avatars/202410/07/f70f67dfcb93e4bf70a24584df7cff9d095351.png",
 *           "cover_url": "https://static.maoercdn.com/fmcovers/202410/08/435992d3bf5ae1e987eeba63ac00f247.jpg",
 *           "recommend_tag": { // 推荐标签，显示下发的字段，如果都存在，则优先显示 icon_url
 *             "type": 6, // 推荐类型：3: 运营配置标签（黑底红字）, 4: 个性词条（黑底白字）, 5: 声优直播（黑底白字）, 6: 关系标签（黑底红字）
 *             "text": "我的关注",
 *             "icon_url": "https://static.maoercdn.com/live/labelicon/livelist/lasthour01-1.png"
 *           },
 *           "status": {
 *             "open": 1,
 *             "pk": 1, // 1: 直播间在 PK 状态, 0 或不存在: 直播间不在 PK 状态
 *             "red_packet": 1, // 当前待抢红包或可抢红包状态, 0: 或不存在表示无待抢红包或者可抢红包
 *             "lucky_bag": 1, // 直播间福袋状态，0: 或不存在表示直播间没有福袋; 1: 直播间存在进行中的福袋
 *             ...
 *           }
 *         }
 *       ],
 *       "lucky_bag": { // 福袋广告条，没有时不下发
 *         "title": "《魔道祖师》正在免费送",
 *         "open_url": "missevan://live/108324",
 *         "trace": "{\"enter_room_id\":12345}" // 埋点上报字段
 *       }
 *     }
 *   }
 */
func ActionChatroomOpenSearchRecommendList(c *handler.Context) (handler.ActionResponse, string, error) {
	var enableShowSearchLive bool
	config.GetAB("enable_show_search_live", &enableShowSearchLive)
	if !enableShowSearchLive {
		// 返回 null，隐藏搜索页直播模块
		return nil, "", nil
	}

	param, err := newSearchRecommendListParam(c)
	if err != nil {
		return nil, "", err
	}

	err = param.getRooms()
	if err != nil {
		logger.Errorf("获取搜索页推荐直播间信息时出错: %v", err)
		// PASS
		// 记录日志，不直接报错，返回 null，客户端表现为隐藏直播模块
		return nil, "", nil
	}
	if len(param.respRooms) == 0 {
		// 没有直播间信息时，返回 null，隐藏直播模块
		return nil, "", nil
	}

	err = param.getLuckyBag()
	if err != nil {
		logger.Errorf("获取搜索页广播剧福袋信息出错: %v", err)
		// PASS
	}

	return searchChatroomOpenRecommendListResp{
		Rooms:    param.respRooms,
		LuckyBag: param.respLuckyBag,
	}, "", nil
}

func newSearchRecommendListParam(c *handler.Context) (*searchRecommendListParam, error) {
	param := new(searchRecommendListParam)
	param.c = c
	return param, nil
}

// getRooms 获取直播间信息
func (param *searchRecommendListParam) getRooms() error {
	rooms, err := listRecommendationRooms(param.c.UserContext(), param.c.UserID())
	if err != nil {
		return err
	}
	isApp := param.c.Equip().FromApp
	param.respRooms = make([]searchRecommendRoom, 0, len(rooms))
	for _, v := range rooms {
		// Web 端不需要显示福袋状态
		if !isApp {
			v.Status.LuckyBag = 0
		}
		param.respRooms = append(param.respRooms, searchRecommendRoom{
			RoomID:          v.RoomID,
			Name:            v.Name,
			CreatorID:       v.CreatorID,
			CreatorUsername: v.CreatorUsername,
			CreatorIconURL:  v.CreatorIconURL,
			CoverURL:        v.CoverURL,
			Status:          v.Status,
			RecommendTag:    v.RecommendTag,
		})
	}
	return nil
}

// getLuckyBag 获取福袋信息
func (param *searchRecommendListParam) getLuckyBag() error {
	var data utils.DramaLuckyBagListData
	dramaLuckyBagListData, err := data.ListAll(param.c.UserContext(), 3)
	if err != nil {
		return err
	}
	dramaLuckyBagListDataLen := len(dramaLuckyBagListData.Data)
	if dramaLuckyBagListDataLen == 0 {
		return nil
	}

	var title string
	var openURL string
	var trace traceInfo
	if dramaLuckyBagListDataLen == 1 {
		iprOrDramaName := dramaLuckyBagListData.Data[0].IPRName
		if iprOrDramaName == "" {
			// 没有 IPR 名称时使用剧集名称
			iprOrDramaName = dramaLuckyBagListData.Data[0].DramaName
		}
		title = fmt.Sprintf("《%s》正在免费送", iprOrDramaName)
		if len(dramaLuckyBagListData.Data[0].Rooms) == 0 {
			logger.Errorf("搜索页广播剧福袋信息中缺少直播间数据")
			// PASS
			return nil
		}
		roomID := dramaLuckyBagListData.Data[0].Rooms[0].RoomID
		openURL = params.RoomURL(roomID)
		trace.EnterRoomID = roomID
	} else {
		iprOrDramaName1 := dramaLuckyBagListData.Data[0].IPRName
		iprOrDramaName2 := dramaLuckyBagListData.Data[1].IPRName
		if iprOrDramaName1 == "" {
			// 没有 IPR 名称时使用剧集名称
			iprOrDramaName1 = dramaLuckyBagListData.Data[0].DramaName
		}
		if iprOrDramaName2 == "" {
			// 没有 IPR 名称时使用剧集名称
			iprOrDramaName2 = dramaLuckyBagListData.Data[1].DramaName
		}
		if dramaLuckyBagListDataLen == 2 {
			title = fmt.Sprintf("《%s》《%s》正在免费送", iprOrDramaName1, iprOrDramaName2)
		} else {
			title = fmt.Sprintf("《%s》《%s》等剧集正在免费送", iprOrDramaName1, iprOrDramaName2)
		}
		// 有两个或两个以上福袋时跳转到广播剧福袋列表
		openURL = config.Conf.Params.Luckybag.DramalistURL
	}

	traceBytes, _ := json.Marshal(trace)
	traceStr := string(traceBytes)

	param.respLuckyBag = &luckyBag{
		Title:   title,
		OpenURL: openURL,
		Trace:   traceStr,
	}

	return nil
}

func listRecommendationRooms(uc mrpc.UserContext, userID int64) ([]*room.Simple, error) {
	mongoOpt := options.Find().SetProjection(recommendProjection)
	findOpt := &room.FindOptions{
		FindCreator:     true,
		FindCatalogInfo: true,
		FindCustomTag:   true,
		FindLuckyBag:    true,
		Projection:      recommendProjection,
	}

	sources, err := createRecommendationSources(uc, userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}
	relatedRooms := sources[0]
	cvRooms := sources[1]

	reconciler := newRecommendationReconciler(
		sources,
		listAppointments(mongoOpt, findOpt),
		buildBlockCreatorMap(uc, userID),
		hotSuppressionRoomsMap(),
		createRecommendationSubstitutes(findOpt),
	)

	rooms := reconciler.reconcile(searchRecommendGroupCount)
	setRecommendTags(rooms, relatedRooms, cvRooms)
	return rooms, nil
}

func createRecommendationSources(uc mrpc.UserContext, userID int64, mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([][]*room.Simple, error) {
	sources := loadRecommendationSourcesFromCache(userID, func(roomIDs []int64) ([]*room.Simple, error) {
		return room.ListSimples(filterOpenRoomsByRoomIDs(roomIDs), mongoOpt, findOpt)
	})
	if sources != nil {
		return sources, nil
	}

	relatedRooms, err := listRelatedRooms(uc, userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	cvRooms, err := listCVRooms(findOpt)
	if err != nil {
		return nil, err
	}

	qualityRooms, err := listQualityRooms(mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	sources = [][]*room.Simple{relatedRooms, cvRooms, qualityRooms}
	saveRecommendationSourcesCache(userID, sources)
	return sources, nil
}

func createRecommendationSubstitutes(findOpt *room.FindOptions) *recommendationLazySource {
	topN := 50

	firstTopN := &recommendationLazySource{loader: func() []*room.Simple {
		rooms, err := room.ListRandomTopScoreRooms(filterOpenRooms(bson.M{}), 0, int64(topN), int64(topN), findOpt)
		if err != nil {
			logger.Error(err)
			return nil
		}
		return rooms
	}}

	secondTopN := &recommendationLazySource{loader: func() []*room.Simple {
		rooms, err := room.ListRandomTopScoreRooms(filterOpenRooms(bson.M{}), int64(topN), int64(topN), int64(topN), findOpt)
		if err != nil {
			logger.Error(err)
			return nil
		}
		return rooms
	}}

	firstTopN.substitutes = secondTopN
	return firstTopN
}

func listRelatedRooms(uc mrpc.UserContext, userID int64, mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([]*room.Simple, error) {
	if userID == 0 {
		return []*room.Simple{}, nil
	}

	medalAndFollowedRooms, err := listMedalAndFollowedRooms(userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	userDramaRooms, err := listUserDramaRooms(uc, userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	interactedRooms, err := listInteractedRooms(userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	rooms := make([]*room.Simple, 0, len(medalAndFollowedRooms)+len(userDramaRooms)+len(interactedRooms))
	roomIDMap := make(map[int64]struct{}, cap(rooms))

	for _, v := range medalAndFollowedRooms {
		if v.CreatorID == userID {
			continue
		}
		if _, ok := roomIDMap[v.RoomID]; !ok {
			rooms = append(rooms, v)
			roomIDMap[v.RoomID] = struct{}{}
		}
	}

	for _, v := range userDramaRooms {
		if v.CreatorID == userID {
			continue
		}
		if _, ok := roomIDMap[v.RoomID]; !ok {
			rooms = append(rooms, v)
			roomIDMap[v.RoomID] = struct{}{}
		}
	}

	for _, v := range interactedRooms {
		if v.CreatorID == userID {
			continue
		}
		if _, ok := roomIDMap[v.RoomID]; !ok {
			rooms = append(rooms, v)
			roomIDMap[v.RoomID] = struct{}{}
		}
	}

	sortRoomsByRewardRank(rooms, userID)
	return rooms, nil
}

func listMedalAndFollowedRooms(userID int64, mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([]*room.Simple, error) {
	medals, err := livemedal.ListUserAll(userID)
	if err != nil {
		return nil, err
	}

	superfanRoomIDs := make([]int64, 0, len(medals))
	superfanRoomIDMap := make(map[int64]struct{}, len(superfanRoomIDs))
	medalRoomIDs := make([]int64, 0, len(medals))
	medalRoomIDMap := make(map[int64]struct{}, len(medalRoomIDs))
	for _, v := range medals {
		if livemedal.IsSuperFanActive(v.SuperFan) {
			superfanRoomIDs = append(superfanRoomIDs, v.RoomID)
			superfanRoomIDMap[v.RoomID] = struct{}{}
		} else {
			medalRoomIDs = append(medalRoomIDs, v.RoomID)
			medalRoomIDMap[v.RoomID] = struct{}{}
		}
	}

	followedCreatorIDs, err := attentionuser.AllFollowedCreatorIDs(userID)
	if err != nil {
		return nil, err
	}
	followedCreatorIDMap := make(map[int64]struct{}, len(followedCreatorIDs))
	for _, v := range followedCreatorIDs {
		followedCreatorIDMap[v] = struct{}{}
	}

	rooms, err := room.ListSimples(filterOpenRooms(bson.M{
		"$or": []bson.M{
			{"room_id": bson.M{"$in": superfanRoomIDs}},
			{"room_id": bson.M{"$in": medalRoomIDs}},
			{"creator_id": bson.M{"$in": followedCreatorIDs}},
		},
	}), mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	for _, v := range rooms {
		if _, ok := superfanRoomIDMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "已成为超粉",
			}
		} else if _, ok := medalRoomIDMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "已点亮粉丝牌",
			}
		} else if _, ok := followedCreatorIDMap[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeSearchUserRelated,
				Text: "我的关注",
			}
		}
	}

	return rooms, nil
}

func listUserDramaRooms(uc mrpc.UserContext, userID int64, mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([]*room.Simple, error) {
	dramaCVs, err := userapi.ListUserDramaCVs(uc, userID, userapi.DramaCVSceneSearch)
	if err != nil {
		return nil, err
	}

	creatorIDs := make([]int64, 0, len(dramaCVs))
	for _, v := range dramaCVs {
		creatorIDs = append(creatorIDs, v.UserID)
	}

	rooms, err := room.ListSimples(filterOpenRoomsByCreatorIDs(creatorIDs), mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	for _, v := range rooms {
		v.RecommendTag = &room.RecommendTag{
			Type: room.RecommendTagTypeSearchUserRelated,
			Text: "已追剧声优",
		}
	}

	return rooms, nil
}

func listInteractedRooms(userID int64, mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([]*room.Simple, error) {
	interactedRooms, err := feed.ListUser7DaysInteractedRoomRank(userID, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	for _, v := range interactedRooms {
		v.RecommendTag = &room.RecommendTag{
			Type: room.RecommendTagTypeSearchUserRelated,
			Text: "最近看过",
		}
	}

	return interactedRooms, nil
}

func listCVRooms(findOpt *room.FindOptions) ([]*room.Simple, error) {
	topN := 10
	// 实际上在最终的直播间列表中只需要 searchRecommendGroupCount (3) 个 声优直播间
	// 但是在生成直播间列表的过程中，一些声优直播间可能会被过滤掉或者由于之前已经推荐过而被去重
	// 所以在这里我们查询出两倍数量的声优直播间放入推荐池中，以避免过早使用替补规则
	requiredCount := searchRecommendGroupCount * 2

	rooms, err := liveextrabanner.ListRandomRecommendRooms(int64(requiredCount))
	if err != nil {
		return nil, err
	}

	if len(rooms) < requiredCount {
		substitutes, err := livecreatorrecommend.ListRandomTopScoreDramaCVRooms(
			int64(topN),
			int64(requiredCount-len(rooms)),
		)
		if err != nil {
			return nil, err
		}
		rooms = append(rooms, substitutes...)
	}

	if len(rooms) < requiredCount {
		subCatalogIDs, err := catalog.SubCatalogIDs(catalog.CatalogIDPia, true)
		if err != nil {
			return nil, err
		}
		if len(subCatalogIDs) > 0 {
			substitutes, err := room.ListRandomTopScoreRooms(
				filterOpenRoomsByCatalogIDs(subCatalogIDs),
				0,
				int64(topN),
				int64(requiredCount-len(rooms)),
				findOpt,
			)
			if err != nil {
				return nil, err
			}
			rooms = append(rooms, substitutes...)
		}
	}

	for _, v := range rooms {
		v.RecommendTag = &room.RecommendTag{
			Type: room.RecommendTagTypeCV,
			Text: "声优直播",
		}
	}

	return rooms, nil
}

func listQualityRooms(mongoOpt *options.FindOptions, findOpt *room.FindOptions) ([]*room.Simple, error) {
	topN := 10

	rankRooms, err := listRankRooms(usersrank.TypeHourLiveTab, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	lastRankRooms, err := listLastRankRooms(usersrank.TypeHourLiveTab, mongoOpt, findOpt)
	if err != nil {
		return nil, err
	}

	rooms := append(rankRooms, lastRankRooms...)
	sort.SliceStable(rooms, func(i, j int) bool {
		return rooms[i].Status.Score > rooms[j].Status.Score
	})

	if len(rooms) > topN {
		rooms = rooms[:topN]
	}

	shuffleRooms(rooms)
	return rooms, nil
}

func listAppointments(mongoOpt *options.FindOptions, findOpt *room.FindOptions) []appointment {
	elements, err := liverecommendedelements.ListSearchRecommend()
	if err != nil {
		logger.Error(err)
		return nil
	}

	roomIDs := make([]int64, 0, len(elements))
	appointmentMap := make(map[int64]liverecommendedelements.Model, len(elements))
	for _, v := range elements {
		roomID := v.ElementID
		roomIDs = append(roomIDs, roomID)
		appointmentMap[roomID] = v
	}

	rooms, err := room.ListSimples(filterOpenRoomsByRoomIDs(roomIDs), mongoOpt, findOpt)
	if err != nil {
		logger.Error(err)
		return nil
	}

	appointments := make([]appointment, 0, len(rooms))
	for _, v := range rooms {
		a, ok := appointmentMap[v.RoomID]
		if !ok {
			continue
		}
		// 后台可以不配置推荐标签，只配置推荐位置
		if a.Name != "" {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeConfig,
				Text: a.Name,
			}
		}
		appointments = append(appointments, appointment{
			pos:  a.Sort - 1, // transform 1-based index to 0-based index
			room: v,
		})
	}

	return appointments
}

func filterOpenRooms(filter bson.M) bson.M {
	filter["status.open"] = room.StatusOpenTrue
	filter["limit"] = bson.M{"$exists": false}
	return filter
}

func filterOpenRoomsByCreatorIDs(creatorIDs []int64) bson.M {
	return filterOpenRooms(bson.M{
		"creator_id": bson.M{"$in": creatorIDs},
	})
}

func filterOpenRoomsByRoomIDs(roomIDs []int64) bson.M {
	return filterOpenRooms(bson.M{
		"room_id": bson.M{"$in": roomIDs},
	})
}

func filterOpenRoomsByCatalogIDs(catalogIDs []int64) bson.M {
	return filterOpenRooms(bson.M{
		"catalog_id": bson.M{"$in": catalogIDs},
	})
}

func sortRoomsByRewardRank(rooms []*room.Simple, userID int64) {
	rewardRanks, err := liveuser7daysrewardcreatorrank.ListUser7DaysCreatorRank(userID)
	if err != nil {
		return
	}

	rewardRankMap := make(map[int64]int64, len(rewardRanks))
	for _, v := range rewardRanks {
		rewardRankMap[v.CreatorID] = v.Rank
	}

	sort.SliceStable(rooms, func(i, j int) bool {
		return rewardRankMap[rooms[i].CreatorID] < rewardRankMap[rooms[j].CreatorID]
	})
}

func setRecommendTags(rooms []*room.Simple, relatedRooms []*room.Simple, cvRooms []*room.Simple) {
	roomIDs := make([]int64, 0, len(rooms))
	for _, v := range rooms {
		roomIDs = append(roomIDs, v.RoomID)
	}
	tags, err := liverecommendedelements.ListRecommendTag(roomIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	roomIDTagMap := make(map[int64]liverecommendedelements.Model, len(tags))
	for _, tag := range tags {
		roomID := tag.ElementID
		roomIDTagMap[roomID] = tag
	}

	roomIDRelatedRoomMap := make(map[int64]*room.Simple, len(relatedRooms))
	for _, v := range relatedRooms {
		roomIDRelatedRoomMap[v.RoomID] = v
	}

	roomIDCVRoomMap := make(map[int64]*room.Simple, len(cvRooms))
	for _, v := range cvRooms {
		roomIDCVRoomMap[v.RoomID] = v
	}

	for _, v := range rooms {
		if v.RecommendTag != nil && v.RecommendTag.Type == room.RecommendTagTypeConfig {
			continue
		}
		if tag, ok := roomIDTagMap[v.RoomID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeConfig,
				Text: tag.Name,
			}
			continue
		}
		if relatedRoom, ok := roomIDRelatedRoomMap[v.RoomID]; ok {
			v.RecommendTag = relatedRoom.RecommendTag
			continue
		}
		if cvRoom, ok := roomIDCVRoomMap[v.RoomID]; ok {
			v.RecommendTag = cvRoom.RecommendTag
			continue
		}
		if v.RecommendTag == nil && v.CustomTag != nil {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeCustomTag,
				Text: v.CustomTag.TagName,
			}
			continue
		}
	}
}
