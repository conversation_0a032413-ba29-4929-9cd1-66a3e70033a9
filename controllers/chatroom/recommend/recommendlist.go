package recommend

import (
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	typeDefault = iota
	typeExcludeFollowUser
)

type chatroomOpenRecommendListResp struct {
	Data       []recommendRooms        `json:"data"`
	Pagination goutil.MarkerPagination `json:"pagination"`
}

type recommendRooms struct {
	RoomID          int64  `json:"room_id"`
	Name            string `json:"name"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl,omitempty"`
	CoverURL        string `json:"cover_url,omitempty"`

	RecommendTag *room.RecommendTag `json:"recommend_tag,omitempty"`
	Status       *room.Status       `json:"status,omitempty"`
	Statistics   *room.Statistics   `json:"statistics,omitempty"`
}

// ActionChatroomOpenRecommendList 导航栏显示直播开播推荐列表
/**
 * @api {get} /api/v2/chatroom/open/recommend-list 导航栏显示直播开播推荐列表
 * @apiDescription 列出直播开播推荐列表，客户端需要对新加载的和前面重复出现的直播间去重处理，总是在新的一轮列表中重新去重 \
 * 刷新页面时第一次请求使用当前列表的 marker 传入到 reset_marker 中，后续请求使用前一次响应中的 marker 传入到下一次新的列表请求的 marker 参数中 \
 * 如果 has_more 为 false，也需要把最后一页的 marker 传入到下一次新列表请求的 reset_marker 中
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [type=0] 0: 默认规则推荐列表; 1: 剔除我已关注主播的推荐列表（在关注 tab 中更多推荐使用）
 * @apiParam {Number} [notice_room_id] 直播 tab 入口直播动态的房间 ID，只有在推荐页面时需要传入
 * @apiParam {String} [reset_marker] 刷新页面或没有下一页刷新时，第一次请求使用当前列表的 marker 传入
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求提供前一次响应中的 marker
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room_id": 108324,
 *           "name": "直播间名称",
 *           "creator_id": 123213,
 *           "creator_username": "浅声暖唱の龙年大吉",
 *           "creator_iconurl": "https://static.maoercdn.com/avatars/202410/07/f70f67dfcb93e4bf70a24584df7cff9d095351.png",
 *           "cover_url": "https://static.maoercdn.com/fmcovers/202410/08/435992d3bf5ae1e987eeba63ac00f247.jpg",
 *           "recommend_tag": { // 推荐标签，显示下发的字段，如果都存在，则优先显示 icon_url
 *             "type": 1, // 推荐类型：1: 推荐标签, 2: 上小时榜, 3: 运营配置标签, 4: 个性词条
 *             "text": "我的关注",
 *             "icon_url": "https://static.maoercdn.com/live/labelicon/livelist/lasthour01-1.png"
 *           },
 *           "status": {
 *             "open": 1,
 *             "pk": 1, // 1: 直播间在 PK 状态, 0 或不存在: 直播间不在 PK 状态
 *             "red_packet": 1, // 当前待抢红包或可抢红包状态, 0: 或不存在表示无待抢红包或者可抢红包
 *             "lucky_bag": 1, // 直播间福袋状态，0: 或不存在表示直播间没有福袋; 1: 直播间存在进行中的福袋
 *             ...
 *           },
 *           "statistics": {
 *             "accumulation": 123,
 *             "score": 123,
 *             ...
 *           }
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否有更多数据
 *         "marker": "40,0,1695717196620" // 当前的 marker，需要加载下一页时回传，最后一页也会返回
 *       }
 *     }
 *   }
 */
func ActionChatroomOpenRecommendList(c *handler.Context) (handler.ActionResponse, string, error) {
	marker, _, _ := c.GetParamMarker()
	recommendType, _ := c.GetDefaultParamInt("type", 0)
	noticeRoomID, _ := c.GetDefaultParamInt64("notice_room_id", 0)
	resetMarker, _ := c.GetParam("reset_marker")

	recommend, err := newRecommend(c, marker, resetMarker, noticeRoomID, recommendType)
	if err != nil {
		return nil, "", err
	}
	err = recommend.recommendRoom()
	if err != nil {
		return nil, "", err
	}

	isApp := c.Equip().FromApp
	rooms := make([]recommendRooms, 0, len(recommend.rooms))
	for _, v := range recommend.rooms {
		// Web 端不需要显示福袋状态
		if !isApp {
			v.Status.LuckyBag = 0
		}
		rooms = append(rooms, recommendRooms{
			RoomID:          v.RoomID,
			Name:            v.Name,
			CreatorID:       v.CreatorID,
			CreatorUsername: v.CreatorUsername,
			CreatorIconURL:  v.CreatorIconURL,
			CoverURL:        v.CoverURL,
			Status:          v.Status,
			Statistics:      v.Statistics,
			RecommendTag:    v.RecommendTag,
		})
	}
	// 标记访问底部导航栏直播页面的时间，只有第一页并且用户登录时需要标记
	if marker == "" && c.UserID() != 0 {
		err = userapi.MarkPageViewed(c.UserContext(), c.UserID())
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return chatroomOpenRecommendListResp{
		Data:       rooms,
		Pagination: recommend.markerPagination,
	}, "", nil
}
