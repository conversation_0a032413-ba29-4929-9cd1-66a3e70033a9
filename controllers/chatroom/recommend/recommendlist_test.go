package recommend

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionChatroomOpenRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(userapi.URLMainUserMarkPageViewed, func(any) (any, error) {
		ok = true
		return "success", nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodGet, "open/recommend-list", true, nil)
	resp, _, err := ActionChatroomOpenRecommendList(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.True(ok)
	r := resp.(chatroomOpenRecommendListResp)
	require.NotNil(r.Pagination)
	assert.True(r.Pagination.HasMore)
	assert.NotEmpty(r.Pagination.Marker)
	assert.NotEmpty(r.Data)
}
