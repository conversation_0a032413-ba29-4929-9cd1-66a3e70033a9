package recommend

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liveextrabanner"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func setMockGetUserDramaCVs(t *testing.T, creatorIDs []int64) {
	t.Cleanup(mrpc.SetMock(
		userapi.URLGetUserDramaCVs,
		func(input interface{}) (interface{}, error) {
			cvs := make([]userapi.DramaCVInfo, 0, len(creatorIDs))
			for _, creatorID := range creatorIDs {
				cvs = append(cvs, userapi.DramaCVInfo{UserID: creatorID})
			}
			return userapi.ListDramaCVResp{CVs: cvs}, nil
		}),
	)
}

func setMockGetUserBlockList(t *testing.T, blockList []int64) {
	t.Cleanup(mrpc.SetMock(
		userapi.URIGoUserBlocklist,
		func(input interface{}) (interface{}, error) {
			return blockList, nil
		},
	))
}

func createTestRooms(t *testing.T, rooms []*room.Helper) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	roomIDs := make([]int64, len(rooms))
	roomDocs := make([]interface{}, len(rooms))
	for i, r := range rooms {
		roomIDs[i] = r.RoomID
		roomDocs[i] = r
	}
	_, err := room.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(t, err)
	inserted, err := room.Collection().InsertMany(ctx, roomDocs)
	require.NoError(t, err)

	t.Cleanup(func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := room.Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": inserted.InsertedIDs}})
		require.NoError(t, err)
	})

	for _, r := range rooms {
		_, err := live.Save(&live.SaveParams{
			RoomID:    r.RoomID,
			CreatorID: r.CreatorID,
			Title:     r.Name,
			Status:    live.StatusOpen,
		})
		require.NoError(t, err)
	}

	t.Cleanup(func() {
		require.NoError(t, live.Live{}.DB().Delete(&live.Live{}, "room_id IN (?)", roomIDs).Error)
	})
}

func createTestMedals(t *testing.T, testMedals []*livemedal.Simple) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	userIDs := make([]int64, 0, len(testMedals))
	medals := make([]*livemedal.Simple, len(testMedals))
	medalDocs := make([]interface{}, len(testMedals))
	for i, m := range testMedals {
		userIDs = append(userIDs, m.UserID)
		medals[i] = m
		medalDocs[i] = m
	}

	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": userIDs}})
	require.NoError(t, err)
	inserted, err := livemedal.Collection().InsertMany(ctx, medalDocs)
	require.NoError(t, err)

	t.Cleanup(func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": inserted.InsertedIDs}})
		require.NoError(t, err)
	})
}

func createTestFollowing(t *testing.T, userID int64, creatorIDs []int64) {
	for _, creatorID := range creatorIDs {
		require.NoError(t, attentionuser.Follow(userID, creatorID))
	}
	t.Cleanup(func() {
		for _, creatorID := range creatorIDs {
			require.NoError(t, attentionuser.UnFollow(userID, creatorID))
		}
	})
}

func createTestFreeGiftRecords(t *testing.T, userID int64, roomIDs []int64) {
	key := keys.KeyUser7DaysRoomFreeGiftNum1.Format(userID)
	data, err := json.Marshal([]livegifts.RoomWithGiftNum{
		{RoomID: roomIDs[0], GiftNum: 1},
	})
	require.NoError(t, err)
	require.NoError(t, service.LRURedis.Set(key, data, 0).Err())
	t.Cleanup(func() {
		require.NoError(t, service.LRURedis.Del(key).Err())
	})
}

func TestSearchRecommendListTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(searchChatroomOpenRecommendListResp{}, "rooms", "lucky_bag")
	kc.Check(searchRecommendRoom{}, "room_id", "name", "creator_id", "creator_username", "creator_iconurl", "cover_url", "recommend_tag", "status")
	kc.Check(traceInfo{}, "enter_room_id")
	kc.Check(luckyBag{}, "title", "open_url", "trace")

	kc.CheckOmitEmpty(searchChatroomOpenRecommendListResp{}, "lucky_bag")
	kc.CheckOmitEmpty(searchRecommendRoom{}, "recommend_tag", "status")
	kc.CheckOmitEmpty(traceInfo{}, "enter_room_id")
}

func TestActionChatroomOpenSearchRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 将待开奖福袋状态更新为已删除
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("type = ? AND status = ?", luckybag.TypeDrama, luckybag.StatusPending).
		Update("status", luckybag.StatusDeleted).Error)

	// 创建福袋发起记录
	record := luckybag.InitiateRecord{
		CreatorID:    12,
		RoomID:       20,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   1,
		PrizeDramaID: 2,
		Name:         "剧集福袋 1",
		Num:          1,
		JoinNum:      99,
		MoreInfo: &luckybag.MoreInfo{
			PrizeIPRName: "IPR 名称 1",
		},
	}
	require.NoError(record.Create())

	// mock
	cancel := mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{}, nil
	})
	defer cancel()
	setMockGetUserDramaCVs(t, []int64{})
	setMockGetUserBlockList(t, []int64{})

	config.Conf.AB["enable_show_search_live"] = true

	api := "/api/v2/chatroom/open/search-recommend-list"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	resp, _, err := ActionChatroomOpenSearchRecommendList(c)
	require.NoError(err)
	require.NotNil(resp)
	r, ok := resp.(searchChatroomOpenRecommendListResp)
	require.True(ok)
	assert.NotEmpty(r.Rooms)
	require.NotNil(r.LuckyBag)
	assert.Equal("《IPR 名称 1》正在免费送", r.LuckyBag.Title)
	assert.Equal("https://fm.uat.missevan.com/live/20", r.LuckyBag.OpenURL)
	assert.Equal("{\"enter_room_id\":20}", r.LuckyBag.Trace)
}

func TestNewSearchRecommendListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/chatroom/open/search-recommend-list"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)

	param, err := newSearchRecommendListParam(c)
	require.NoError(err)
	assert.Equal(c, param.c)
}

func TestSearchRecommendListParam_getRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	setMockGetUserDramaCVs(t, []int64{})

	api := "/api/v2/chatroom/open/search-recommend-list"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)

	param := &searchRecommendListParam{c: c}
	err := param.getRooms()
	require.NoError(err)
	assert.NotEmpty(param.respRooms)
}

func TestSearchRecommendListParam_getLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 将待开奖福袋状态更新为已删除
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("type = ? AND status = ?", luckybag.TypeDrama, luckybag.StatusPending).
		Update("status", luckybag.StatusDeleted).Error)

	// mock
	cancel := mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{}, nil
	})
	defer cancel()

	api := "/api/v2/chatroom/open/search-recommend-list"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)

	// 测试只有一个福袋的情况
	// 创建福袋发起记录
	record1 := luckybag.InitiateRecord{
		CreatorID:    12,
		RoomID:       20,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   1,
		PrizeDramaID: 2,
		Name:         "剧集福袋 1",
		Num:          1,
		JoinNum:      99,
		MoreInfo: &luckybag.MoreInfo{
			PrizeIPRName: "IPR 名称 1",
		},
	}
	require.NoError(record1.Create())
	param := &searchRecommendListParam{c: c}
	err := param.getLuckyBag()
	require.NoError(err)
	require.NotNil(param.respLuckyBag)
	assert.Equal("《IPR 名称 1》正在免费送", param.respLuckyBag.Title)
	assert.Equal("https://fm.uat.missevan.com/live/20", param.respLuckyBag.OpenURL)
	assert.Equal("{\"enter_room_id\":20}", param.respLuckyBag.Trace)

	// 测试两个福袋的情况
	// 创建福袋发起记录
	record2 := luckybag.InitiateRecord{
		CreatorID:    12,
		RoomID:       30,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   2,
		PrizeDramaID: 3,
		Name:         "剧集福袋 2",
		Num:          1,
		JoinNum:      99,
		MoreInfo: &luckybag.MoreInfo{
			PrizeIPRName: "IPR 名称 2",
		},
	}
	require.NoError(record2.Create())

	param = &searchRecommendListParam{c: c}
	err = param.getLuckyBag()
	require.NoError(err)
	require.NotNil(param.respLuckyBag)
	assert.Equal("《IPR 名称 2》《IPR 名称 1》正在免费送", param.respLuckyBag.Title)
	assert.Equal(config.Conf.Params.Luckybag.DramalistURL, param.respLuckyBag.OpenURL)
	assert.Equal("{}", param.respLuckyBag.Trace)

	// 测试两个以上福袋的情况
	// 创建福袋发起记录
	record3 := luckybag.InitiateRecord{
		CreatorID:    12,
		RoomID:       40,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		Status:       luckybag.StatusPending,
		PrizeIPRID:   0,
		PrizeDramaID: 100,
		Name:         "剧集福袋 3",
		Num:          1,
		JoinNum:      99,
	}
	require.NoError(record3.Create())

	param = &searchRecommendListParam{c: c}
	err = param.getLuckyBag()
	require.NoError(err)
	require.NotNil(param.respLuckyBag)
	// 验证没有 IPR 名称时使用剧集名称
	assert.Equal("《剧集福袋 3》《IPR 名称 2》等剧集正在免费送", param.respLuckyBag.Title)
	assert.Equal(config.Conf.Params.Luckybag.DramalistURL, param.respLuckyBag.OpenURL)
	assert.Equal("{}", param.respLuckyBag.Trace)
}

func clearRecommendationSourcesCache(t *testing.T, userID int64) {
	key := keys.KeySearchRecommendRooms1.Format(userID)
	require.NoError(t, service.LRURedis.Del(key).Err())
}

func TestListRecommendationRooms(t *testing.T) {
	testUserID := int64(10000)

	api := "/api/v2/chatroom/open/search-recommend-list"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	uc := c.UserContext()

	t.Run("DataFromSubstitutes", func(t *testing.T) {
		setMockGetUserDramaCVs(t, []int64{})
		setMockGetUserBlockList(t, []int64{})
		clearRecommendationSourcesCache(t, testUserID)

		rooms, err := listRecommendationRooms(uc, testUserID)
		require.NoError(t, err)
		require.NotEmpty(t, rooms)
		roomMap := make(map[int64]*room.Simple)
		for _, r := range rooms {
			roomMap[r.RoomID] = r
		}

		substitutes := createRecommendationSubstitutes(&room.FindOptions{})
		require.NotNil(t, substitutes)
		for i := 0; i < len(rooms); i++ {
			substitute := substitutes.next(func(room *room.Simple) bool {
				_, ok := roomMap[room.RoomID]
				return ok
			})
			assert.NotNil(t, substitute)
		}
	})

	t.Run("DataFromSources", func(t *testing.T) {
		testRooms := []*room.Helper{
			{RoomID: 10001, NameClean: "测试房间 10001", CreatorID: 10001, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10002, NameClean: "测试房间 10002", CreatorID: 10002, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10003, NameClean: "测试房间 10003", CreatorID: 10003, Status: room.Status{Open: room.StatusOpenTrue}},
		}
		testClosedRooms := []*room.Helper{
			{RoomID: 20001, NameClean: "测试房间 20001", CreatorID: 20001, Status: room.Status{Open: room.StatusOpenFalse}},
		}
		createTestRooms(t, append(testRooms, testClosedRooms...))
		testMedals := []*livemedal.Simple{
			{
				RoomID:    testRooms[0].RoomID,
				CreatorID: testRooms[0].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
			},
			{
				RoomID:    testClosedRooms[0].RoomID,
				CreatorID: testClosedRooms[0].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
			},
		}
		createTestMedals(t, testMedals)
		createTestFollowing(t, testUserID, []int64{testRooms[1].CreatorID})
		setMockGetUserDramaCVs(t, []int64{testRooms[2].CreatorID})
		setMockGetUserBlockList(t, []int64{})
		clearRecommendationSourcesCache(t, testUserID)

		rooms, err := listRecommendationRooms(uc, testUserID)
		require.NoError(t, err)
		require.NotEmpty(t, rooms)
		roomMap := make(map[int64]*room.Simple)
		for _, r := range rooms {
			roomMap[r.RoomID] = r
		}
		for _, r := range testRooms {
			_, ok := roomMap[r.RoomID]
			if r.Status.Open == room.StatusOpenTrue {
				assert.True(t, ok)
			} else {
				assert.False(t, ok)
			}
		}
	})

	t.Run("DataFromCache", func(t *testing.T) {
		setMockGetUserBlockList(t, []int64{})
		testRooms := []*room.Helper{
			{RoomID: 20001, NameClean: "测试房间 10001", CreatorID: 20001, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 20002, NameClean: "测试房间 10002", CreatorID: 20002, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 20003, NameClean: "测试房间 10003", CreatorID: 20003, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 20004, NameClean: "测试房间 10004", CreatorID: 20004, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 20005, NameClean: "测试房间 10005", CreatorID: 20005, Status: room.Status{Open: room.StatusOpenTrue}},
		}
		createTestRooms(t, testRooms)
		saveRecommendationSourcesCache(testUserID, [][]*room.Simple{
			{{RoomID: testRooms[0].RoomID}, {RoomID: testRooms[1].RoomID}},
			{{RoomID: testRooms[2].RoomID}, {RoomID: testRooms[3].RoomID}},
			{{RoomID: testRooms[4].RoomID}},
		})
		defer clearRecommendationSourcesCache(t, testUserID)

		rooms, err := listRecommendationRooms(uc, testUserID)
		require.NoError(t, err)
		require.NotEmpty(t, rooms)
		roomMap := make(map[int64]*room.Simple)
		for _, r := range rooms {
			roomMap[r.RoomID] = r
		}
		for _, r := range testRooms {
			_, ok := roomMap[r.RoomID]
			assert.True(t, ok)
		}
	})
}

func TestCreateRecommendationSources(t *testing.T) {
	testUserID := int64(10000)

	setMockGetUserDramaCVs(t, []int64{})

	t.Run("FromScratch", func(t *testing.T) {
		api := "/api/v2/chatroom/open/search-recommend-list"
		c := handler.NewTestContext(http.MethodGet, api, true, nil)
		result, err := createRecommendationSources(c.UserContext(), testUserID, options.Find(), &room.FindOptions{})
		require.NoError(t, err)
		require.Len(t, result, 3)
	})

	t.Run("FromCache", func(t *testing.T) {
		testRooms := []*room.Helper{
			{RoomID: 10001, NameClean: "测试房间 10001", CreatorID: 10001, Status: room.Status{Score: 10001, Open: room.StatusOpenTrue}},
			{RoomID: 10002, NameClean: "测试房间 10002", CreatorID: 10002, Status: room.Status{Score: 10002, Open: room.StatusOpenTrue}},
			{RoomID: 10003, NameClean: "测试房间 10003", CreatorID: 10003, Status: room.Status{Score: 10003, Open: room.StatusOpenTrue}},
			{RoomID: 10004, NameClean: "测试房间 10004", CreatorID: 10004, Status: room.Status{Score: 10004, Open: room.StatusOpenTrue}},
			{RoomID: 10005, NameClean: "测试房间 10005", CreatorID: 10005, Status: room.Status{Score: 10005, Open: room.StatusOpenTrue}},
			{RoomID: 10006, NameClean: "测试房间 10006", CreatorID: 10006, Status: room.Status{Score: 10006, Open: room.StatusOpenTrue}},
		}
		createTestRooms(t, testRooms)
		sources := [][]*room.Simple{
			{{RoomID: testRooms[0].RoomID}, {RoomID: testRooms[1].RoomID}},
			{{RoomID: testRooms[2].RoomID}, {RoomID: testRooms[3].RoomID}},
			{{RoomID: testRooms[4].RoomID}, {RoomID: testRooms[5].RoomID}},
		}
		saveRecommendationSourcesCache(testUserID, sources)
		defer clearRecommendationSourcesCache(t, testUserID)

		api := "/api/v2/chatroom/open/search-recommend-list"
		c := handler.NewTestContext(http.MethodGet, api, true, nil)
		result, err := createRecommendationSources(c.UserContext(), testUserID, options.Find(), &room.FindOptions{})
		require.NoError(t, err)
		require.Len(t, result, 3)

		for _, source := range result {
			assert.NotEmpty(t, source)
		}
	})
}

func TestListRelatedRooms(t *testing.T) {
	testUserID := int64(10000)
	findOpt := &room.FindOptions{}

	t.Run("Success", func(t *testing.T) {
		now := goutil.TimeNow()

		testRooms := []*room.Helper{
			{RoomID: 10001, NameClean: "测试房间 10001", CreatorID: 10001, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10002, NameClean: "测试房间 10002", CreatorID: 10002, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10003, NameClean: "测试房间 10003", CreatorID: 10003, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10004, NameClean: "测试房间 10004", CreatorID: 10004, Status: room.Status{Open: room.StatusOpenTrue}},
			{RoomID: 10005, NameClean: "测试房间 10005", CreatorID: 10005, Status: room.Status{Open: room.StatusOpenTrue}},
		}
		createTestRooms(t, testRooms)
		testMedals := []*livemedal.Simple{
			{
				RoomID:    testRooms[0].RoomID,
				CreatorID: testRooms[0].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
			},
			{
				RoomID:    testRooms[1].RoomID,
				CreatorID: testRooms[1].CreatorID,
				UserID:    testUserID,
				Status:    livemedal.StatusOwned,
				Mini:      livemedal.Mini{SuperFan: &livemedal.SuperFan{ExpireTime: now.Add(time.Hour * 24).Unix()}},
			},
		}
		createTestMedals(t, testMedals)
		createTestFollowing(t, testUserID, []int64{testRooms[2].CreatorID})
		setMockGetUserDramaCVs(t, []int64{testRooms[3].CreatorID})
		createTestFreeGiftRecords(t, testUserID, []int64{testRooms[4].RoomID})

		api := "/api/v2/chatroom/open/search-recommend-list"
		c := handler.NewTestContext(http.MethodGet, api, true, nil)
		rooms, err := listRelatedRooms(c.UserContext(), testUserID, options.Find(), findOpt)
		require.NoError(t, err)
		require.Len(t, rooms, len(testRooms))

		for _, r := range rooms {
			assert.NotNil(t, r.RecommendTag)
			switch r.RoomID {
			case testRooms[0].RoomID:
				assert.Equal(t, "已点亮粉丝牌", r.RecommendTag.Text)
			case testRooms[1].RoomID:
				assert.Equal(t, "已成为超粉", r.RecommendTag.Text)
			case testRooms[2].RoomID:
				assert.Equal(t, "我的关注", r.RecommendTag.Text)
			case testRooms[3].RoomID:
				assert.Equal(t, "已追剧声优", r.RecommendTag.Text)
			case testRooms[4].RoomID:
				assert.Equal(t, "最近看过", r.RecommendTag.Text)
			default:
				assert.Failf(t, "unexpected room id", "room id: %d", r.RoomID)
			}
		}
	})

	t.Run("NoUserID", func(t *testing.T) {
		api := "/api/v2/chatroom/open/search-recommend-list"
		c := handler.NewTestContext(http.MethodGet, api, true, nil)
		rooms, err := listRelatedRooms(c.UserContext(), 0, options.Find(), findOpt)
		require.NoError(t, err)
		assert.Empty(t, rooms)
	})
}

func TestListCVRooms(t *testing.T) {
	createTestData := func(t *testing.T, recommendRoomsCount, topScoreDramaCVRoomsCount, topScoreRoomsCount int) {
		recommendRooms := make([]*room.Helper, recommendRoomsCount)
		for i := 0; i < recommendRoomsCount; i++ {
			recommendRooms[i] = &room.Helper{
				RoomID:    int64(i + 10000),
				NameClean: fmt.Sprintf("测试房间 %d", i+10000),
				CreatorID: int64(i + 10000),
				Status:    room.Status{Score: float64(i), Open: room.StatusOpenTrue},
			}
		}
		createTestRooms(t, recommendRooms)
		dramaCVRooms := make([]*room.Helper, topScoreDramaCVRoomsCount)
		for i := 0; i < topScoreDramaCVRoomsCount; i++ {
			dramaCVRooms[i] = &room.Helper{
				RoomID:    int64(i + 20000),
				NameClean: fmt.Sprintf("测试房间 %d", i+20000),
				CreatorID: int64(i + 20000),
				Status:    room.Status{Score: float64(i), Open: room.StatusOpenTrue},
				CatalogID: catalog.CatalogIDPia,
			}
		}
		createTestRooms(t, dramaCVRooms)
		otherRooms := make([]*room.Helper, topScoreRoomsCount)
		for i := 0; i < topScoreRoomsCount; i++ {
			otherRooms[i] = &room.Helper{
				RoomID:    int64(i + 30000),
				NameClean: fmt.Sprintf("测试房间 %d", i+30000),
				CreatorID: int64(i + 30000),
				Status:    room.Status{Score: float64(i), Open: room.StatusOpenTrue},
			}
		}
		createTestRooms(t, otherRooms)
		banners := make([]*liveextrabanner.LiveExtraBanner, recommendRoomsCount)
		for i, r := range recommendRooms {
			b := &liveextrabanner.LiveExtraBanner{
				UserID:   r.CreatorID,
				RoomID:   r.RoomID,
				Position: liveextrabanner.PositionExtraBanner1,
			}
			require.NoError(t, liveextrabanner.LiveExtraBanner{}.DB().Create(&b).Error)
			banners[i] = b
		}
		t.Cleanup(func() {
			for _, b := range banners {
				require.NoError(t, liveextrabanner.LiveExtraBanner{}.DB().Delete(&b).Error)
			}
		})
	}

	t.Run("EnoughRecommendRooms", func(t *testing.T) {
		createTestData(t, 6, 1, 1)

		rooms, err := listCVRooms(&room.FindOptions{})
		require.NoError(t, err)
		assert.NotEmpty(t, rooms)

		for _, r := range rooms {
			assert.NotNil(t, r.RecommendTag)
			assert.Equal(t, room.RecommendTagTypeCV, r.RecommendTag.Type)
			assert.Equal(t, "声优直播", r.RecommendTag.Text)
		}
	})

	t.Run("InsufficientRecommendRooms", func(t *testing.T) {
		createTestData(t, 2, 4, 4)

		rooms, err := listCVRooms(&room.FindOptions{})
		require.NoError(t, err)
		assert.NotEmpty(t, rooms)

		for _, r := range rooms {
			assert.NotNil(t, r.RecommendTag)
			assert.Equal(t, room.RecommendTagTypeCV, r.RecommendTag.Type)
			assert.Equal(t, "声优直播", r.RecommendTag.Text)
		}
	})
}

func TestListQualityRooms(t *testing.T) {
	createTestRooms := func(t *testing.T, currentRankRoomsCount, lastRankRoomsCount int) {
		now := goutil.TimeNow()

		currentRankRooms := make([]*room.Helper, currentRankRoomsCount)
		for i := 0; i < currentRankRoomsCount; i++ {
			currentRankRooms[i] = &room.Helper{
				RoomID:    int64(i + 10000),
				NameClean: fmt.Sprintf("测试房间 %d", i+10000),
				CreatorID: int64(i + 10000),
				Status:    room.Status{Score: float64(i), Open: room.StatusOpenTrue},
			}
		}
		createTestRooms(t, currentRankRooms)

		lastRankRooms := make([]*room.Helper, lastRankRoomsCount)
		for i := 0; i < lastRankRoomsCount; i++ {
			lastRankRooms[i] = &room.Helper{
				RoomID:    int64(i + 20000),
				NameClean: fmt.Sprintf("测试房间 %d", i+20000),
				CreatorID: int64(i + 20000),
				Status:    room.Status{Score: float64(i), Open: room.StatusOpenTrue},
			}
		}
		createTestRooms(t, lastRankRooms)

		currentRankKey := usersrank.Key(usersrank.TypeHourLiveTab, now)
		currentRankData := []*redis.Z{}
		for _, r := range currentRankRooms {
			currentRankData = append(currentRankData, &redis.Z{Score: r.Status.Score, Member: fmt.Sprintf("%d", r.RoomID)})
		}
		require.NoError(t, service.Redis.Del(currentRankKey).Err())
		require.NoError(t, service.Redis.ZAdd(currentRankKey, currentRankData...).Err())

		lastRankKey := usersrank.LastRankKey(usersrank.TypeHourLiveTab, now)
		lastRankData := []*redis.Z{}
		for _, r := range lastRankRooms {
			lastRankData = append(lastRankData, &redis.Z{Score: r.Status.Score, Member: fmt.Sprintf("%d", r.RoomID)})
		}
		require.NoError(t, service.Redis.Del(lastRankKey).Err())
		require.NoError(t, service.Redis.ZAdd(lastRankKey, lastRankData...).Err())
	}

	t.Run("LessThanRequiredCountInCurrentHourRank", func(t *testing.T) {
		createTestRooms(t, 6, 6)

		rooms, err := listQualityRooms(options.Find(), &room.FindOptions{})
		require.NoError(t, err)
		assert.Equal(t, 10, len(rooms))
	})

	t.Run("MoreThanRequiredCountInCurrentHourRank", func(t *testing.T) {
		createTestRooms(t, 15, 15)

		rooms, err := listQualityRooms(options.Find(), &room.FindOptions{})
		require.NoError(t, err)
		assert.Equal(t, 10, len(rooms))
	})
}

func TestFilterOpenRooms(t *testing.T) {
	expected := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	filter := filterOpenRooms(bson.M{})
	assert.Equal(t, expected, filter)
}

func TestFilterOpenRoomsByCreatorIDs(t *testing.T) {
	creatorIDs := []int64{123, 456, 789}
	expected := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
		"creator_id":  bson.M{"$in": creatorIDs},
	}
	filter := filterOpenRoomsByCreatorIDs(creatorIDs)
	assert.Equal(t, expected, filter)
}

func TestFilterOpenRoomsByRoomIDs(t *testing.T) {
	roomIDs := []int64{1001, 1002, 1003}
	expected := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
		"room_id":     bson.M{"$in": roomIDs},
	}
	filter := filterOpenRoomsByRoomIDs(roomIDs)
	assert.Equal(t, expected, filter)
}

func TestFilterOpenRoomsByCatalogID(t *testing.T) {
	catalogID := int64(42)
	expected := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
		"catalog_id":  bson.M{"$in": []int64{catalogID}},
	}
	filter := filterOpenRoomsByCatalogIDs([]int64{catalogID})
	assert.Equal(t, expected, filter)
}

func TestSetRecommendTags(t *testing.T) {
	now := goutil.TimeNow()

	startTime := now.Add(-time.Hour * 24).Unix()
	expireTime := now.Add(time.Hour * 24).Unix()
	testTags := []*liverecommendedelements.Model{
		{
			ElementID:   1001,
			ElementType: liverecommendedelements.ElementRecommendTag,
			Attribute: liverecommendedelements.Attribute{
				Name:      "测试标签 A-1001",
				StartTime: &startTime,
			},
			ExpireTime: expireTime,
		},
		{
			ElementID:   1002,
			ElementType: liverecommendedelements.ElementRecommendTag,
			Attribute: liverecommendedelements.Attribute{
				Name:      "测试标签 A-1002",
				StartTime: &startTime,
			},
			ExpireTime: expireTime,
		},
	}
	for _, tag := range testTags {
		require.NoError(t, liverecommendedelements.TableRecommendTag(service.DB).Create(&tag).Error)
	}
	defer func() {
		for _, tag := range testTags {
			require.NoError(t, liverecommendedelements.TableRecommendTag(service.DB).Delete(&tag).Error)
		}
	}()

	testRooms := []*room.Simple{
		{RoomID: 1001, CreatorID: 1001},
		{RoomID: 1002, CreatorID: 1002, RecommendTag: &room.RecommendTag{Type: room.RecommendTagTypeConfig, Text: "测试标签 B-1002"}},
		{RoomID: 1003, CreatorID: 1003, CustomTag: &tag.CustomTag{TagName: "测试标签 B-1003"}},
	}
	setRecommendTags(testRooms, nil, nil)

	for _, r := range testRooms {
		require.NotNil(t, r.RecommendTag)
		switch r.RoomID {
		case testRooms[0].RoomID:
			assert.Equal(t, "测试标签 A-1001", r.RecommendTag.Text)
		case testRooms[1].RoomID:
			assert.Equal(t, "测试标签 B-1002", r.RecommendTag.Text)
		case testRooms[2].RoomID:
			assert.Equal(t, "测试标签 B-1003", r.CustomTag.TagName)
		}
	}
}
