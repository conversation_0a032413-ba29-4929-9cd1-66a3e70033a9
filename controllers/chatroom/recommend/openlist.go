package recommend

import (
	"sort"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var recommendProjection = mongodb.NewProjection("room_id, name, creator_id, creator_username, cover, statistics, status, background, catalog_id, custom_tag_id")

const recommendLastHourIndex = 2 // 上小时榜保底第二

type roomSimpleList struct {
	Data       []*room.Simple
	Pagination goutil.MarkerPagination
}

type recommendParam struct {
	p        int64
	pageSize int64

	// TODO: 二阶段不使用这个结构体
	*roomSimpleList

	excludeFollowUserIDs []int64

	// 上小时榜图标映射，元素是 map[roomID]iconURL
	iconURLs map[int64]string

	// nrRoom 神话推荐的直播间，效果为保底
	nrRoom *room.Simple
	// lastHourRoom 上小时榜的直播间，效果为固定
	lastHourRoom *room.Simple
	// recommendRooms 运营配置的推荐直播间，效果为固定
	recommendRooms []*room.Simple

	opt *room.FindOptions
}

func buildRecommendList(c *handler.Context, marker string, pageSize int64, excludeFollowUserIDs []int64) (*roomSimpleList, error) {
	param := newRecommendParam(c, marker, pageSize, excludeFollowUserIDs)
	err := param.findList()
	if err != nil {
		return nil, err
	}
	param.findRecommendRoom()
	param.assignRecommend()
	return param.roomSimpleList, nil
}

func newRecommendParam(c *handler.Context, marker string, pageSize int64, excludeFollowUserIDs []int64) *recommendParam {
	p := int64(1)
	// TODO: 临时使用，二阶段替换为正式 marker 格式
	if marker != "" {
		var err error
		p, err = strconv.ParseInt(marker, 10, 64)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	opt := &room.FindOptions{
		FindCreator:     true,
		FindCatalogInfo: true,
		FindCustomTag:   true,
	}
	// Web 端不显示福袋图标
	if c.Equip().FromApp {
		opt.FindLuckyBag = true
	}
	return &recommendParam{
		p:                    p,
		pageSize:             pageSize,
		excludeFollowUserIDs: excludeFollowUserIDs,
		opt:                  opt,
	}
}

func (param *recommendParam) findList() error {
	filter := bson.M{
		"room_id":     bson.M{"$nin": room.OpenListExcludeRoomIDs()},
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}

	// 关注用户不为空，排除关注的主播直播间
	if len(param.excludeFollowUserIDs) != 0 {
		filter["creator_id"] = bson.M{"$nin": param.excludeFollowUserIDs}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.roomSimpleList = new(roomSimpleList)
	pagination := goutil.MakePagination(count, param.p, param.pageSize)
	if !pagination.Valid() {
		param.Data = make([]*room.Simple, 0)
		return nil
	}

	// 判断是否有下一页
	if count-(param.pageSize*param.p) > 0 {
		param.Pagination.HasMore = true
		// TODO: 临时使用当前格式
		param.Pagination.Marker = strconv.FormatInt(param.p+1, 10)
	}

	mongoOpt := pagination.SetFindOptions(nil).SetSort(room.SortByScore).SetProjection(recommendProjection)
	param.Data, err = room.ListSimples(filter, mongoOpt, param.opt)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// findRecommendRoom 查询推荐房间
// 上小时榜排名：第二，即使热度更高也会固定第二
// 贵族推荐最低排名：热门列表第五，分区第一
func (param *recommendParam) findRecommendRoom() {
	if param.p != 1 || len(param.Data) == 0 {
		// 不是第一页不查，第一页没数据不查
		return
	}

	rec, err := liverecommendedelements.FindOpenList(liverecommendedelements.SquareTypeHot, 0, false)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// roomIDs 包含运营配置的推荐直播间和神话推荐
	roomIDs := make([]int64, 0, 1+len(rec))
	// mapRoomIDIdx 运营配置的推荐直播间，room_id 到推荐位置的对应关系，推荐位置从 1 开始
	mapRoomIDIdx := make(map[int64]int, len(rec))
	for i := range rec {
		roomIDs = append(roomIDs, rec[i].ElementID)
		mapRoomIDIdx[rec[i].ElementID] = rec[i].Sort
	}

	nr, err := livenoblerecommend.CurrentRecommend()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if nr != nil {
		roomIDs = append(roomIDs, nr.RoomID)
	}

	lastHourTop3, err := usersrank.ListLastHourTop3CreatorIDs()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 因为不仅根据 roomID 查询房间，所以在查询房间后排除不在开播列表显示的房间
	type m bson.M
	filter := m{"status.open": room.StatusOpenTrue}
	switch {
	case len(lastHourTop3) > 0 && len(roomIDs) > 0:
		// 热门
		filter["$or"] = []m{
			{"creator_id": m{"$in": lastHourTop3}},
			{"room_id": m{"$in": util.Uniq(roomIDs)}},
		}
	case len(lastHourTop3) > 0:
		// 热门
		filter["creator_id"] = m{"$in": lastHourTop3}
	case len(roomIDs) > 0:
		// 分区、新星的情况
		filter["room_id"] = m{"$in": roomIDs}
	default:
		return
	}

	// rooms 包括运营手动配置的推荐直播间，神话推荐和上小时榜的数据
	rooms, err := room.ListSimples(filter, options.Find().SetSort(room.SortByOpenScore).SetProjection(recommendProjection), param.opt)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(rooms) == 0 {
		return
	}
	excludeRoomIDs := room.OpenListExcludeRoomIDs()
	param.recommendRooms = make([]*room.Simple, 0, len(mapRoomIDIdx))
	roomMap := make(map[int64]*room.Simple, len(rooms)) // map[CreatorID]*room.Simple
	for i := range rooms {
		// 排除不在开播列表显示的房间
		if goutil.HasElem(excludeRoomIDs, rooms[i].RoomID) {
			continue
		}

		roomMap[rooms[i].CreatorID] = rooms[i]

		// 运营手动配置的推荐直播间
		if index, ok := mapRoomIDIdx[rooms[i].RoomID]; ok {
			r := *rooms[i]
			r.SetIndex(index)
			param.recommendRooms = append(param.recommendRooms, &r)
		}

		// 神话推荐
		if nr != nil && rooms[i].RoomID == nr.RoomID {
			r := *rooms[i]
			r.SetIndex(5)
			param.nrRoom = &r
		}
	}

	rankParam, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	icons := rankParam.LastHourRecommendIcons(goutil.TimeNow())
	// 上小时榜
	for i := range lastHourTop3 {
		if top := roomMap[lastHourTop3[i]]; top != nil {
			r := *top
			r.SetIndex(recommendLastHourIndex) // 上小时榜固定第二位置
			param.lastHourRoom = &r

			// 设置上小时榜图标
			param.iconURLs = map[int64]string{
				r.RoomID: service.Storage.Parse(icons[i]),
			}
			break
		}
	}
}

// assignRecommend 添加推荐房间到列表中
/*
	排序依照的规则：
	1. 神话推荐保底，上小时榜固定，运营配置的推荐直播间固定
	2. 固定和保底的逻辑优先
	3. 运营配置的开播列表优先级弱于神话推荐和上小时榜第一
*/
func (param *recommendParam) assignRecommend() {
	if param.lastHourRoom == nil && param.nrRoom == nil && len(param.recommendRooms) == 0 {
		return
	}
	mapRoomIDIdx := make(map[int64]int, 1+len(param.recommendRooms))
	mapIdxRoomID := make(map[int]int64, 1+len(param.recommendRooms))
	fixedIdxRooms := make([]*room.Simple, 0, 1+len(param.recommendRooms))
	addFixedIdxRoom := func(index int, r *room.Simple) {
		mapRoomIDIdx[r.RoomID] = index
		mapIdxRoomID[index] = r.RoomID
		r.SetIndex(index)
		fixedIdxRooms = append(fixedIdxRooms, r)
	}
	if param.lastHourRoom != nil {
		addFixedIdxRoom(*param.lastHourRoom.Index, param.lastHourRoom)
	}
	// 将固定推荐位房间插入 fixIdxRooms
	for _, r := range param.recommendRooms {
		if mapRoomIDIdx[r.RoomID] != 0 {
			// 该房间已被设置过固定推荐位，跳过
			continue
		}
		idx := *r.Index
		for {
			if mapIdxRoomID[idx] != 0 {
				// 排名被占用，固定位下移一位
				logger.WithField("idx", idx).Error("开播列表推荐位异常，同一推荐位有多个房间")
				idx++
				continue
			}
			if param.nrRoom != nil && param.nrRoom.RoomID == r.RoomID {
				// 如果某房间同时有神话推荐和固定位推荐，以位置更高的为准
				if *r.Index >= *param.nrRoom.Index {
					// 神话推荐位置更高，跳过
					break
				}
				// 固定位更高，将神话推荐删除，后将该房间设置如固定推荐位
				param.nrRoom = nil
			}
			addFixedIdxRoom(idx, r)
			break
		}
	}
	// 根据位置排序
	sort.Slice(fixedIdxRooms, func(i, j int) bool {
		return mapRoomIDIdx[fixedIdxRooms[i].RoomID] < mapRoomIDIdx[fixedIdxRooms[j].RoomID]
	})
	// 检查神话推荐是否被固定推荐位占用，被占用前进一位
	if param.nrRoom != nil {
		idx := *param.nrRoom.Index
		for ; idx > 1; idx-- {
			if mapIdxRoomID[idx] == 0 {
				break
			}
		}
		*param.nrRoom.Index = idx
	}
	// 被占满后，固定推荐位在插入时需要偏移
	newData := make([]*room.Simple, 0, 1+len(param.Data)+len(fixedIdxRooms))
	addNewData := func(r *room.Simple) {
		// 设置小时榜 TOP3 图标，导航栏推荐列表不显示神话推荐、上神推荐等推荐图标
		if iconURL, ok := param.iconURLs[r.RoomID]; ok {
			r.IconURL = iconURL
		}
		newData = append(newData, r)
		mapRoomIDIdx[r.RoomID] = len(newData)
	}
	for {
		if len(param.Data) == 0 && len(fixedIdxRooms) == 0 && param.nrRoom == nil {
			break
		}

		if len(newData) >= int(param.pageSize) {
			break
		}

		curIdx := len(newData) + 1
		if param.nrRoom != nil {
			if mapRoomIDIdx[param.nrRoom.RoomID] != 0 {
				// 更高位置已进入 newData
				param.nrRoom = nil
				continue
			}
			if curIdx >= *param.nrRoom.Index || (len(param.Data) == 0 && len(fixedIdxRooms) == 0) {
				addNewData(param.nrRoom)
				param.nrRoom = nil
				continue
			}
		}
		if len(fixedIdxRooms) != 0 {
			r := fixedIdxRooms[0]
			// fixedIdxRooms 中的房间总是在 mapRoomIDIdx, 不获取房间 idx
			if curIdx >= *r.Index || len(param.Data) == 0 {
				addNewData(r)
				fixedIdxRooms = fixedIdxRooms[1:]
				continue
			}
		}
		if len(param.Data) != 0 {
			r := param.Data[0]
			// 不进入 newData 也从出队
			param.Data = param.Data[1:]
			// 房间位置未被预定或插入
			if mapRoomIDIdx[r.RoomID] == 0 {
				addNewData(r)
			}
		}
	}
	param.Data = newData
}
