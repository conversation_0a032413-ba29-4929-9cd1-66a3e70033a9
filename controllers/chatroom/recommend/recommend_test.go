package recommend

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveuser7daysrewardcreatorrank"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestParseRecommendMarker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	marker, err := parseRecommendMarker("", 0)
	require.NoError(err)
	assert.Equal(0, marker.index)
	assert.NotZero(marker.createTime)

	_, err = parseRecommendMarker("1234567890", 0)
	assert.EqualError(err, "参数格式错误")

	now := goutil.TimeNow().Unix()
	markerStr := buildRecommendMarker(1234567890, now)
	_, err = parseRecommendMarker(markerStr, 0)
	assert.EqualError(err, "参数值不在有效范围内")

	marker, err = parseRecommendMarker("", 0)
	require.NoError(err)
	assert.Equal(0, marker.index)
	expected := (now / 60) * 60
	assert.Equal(expected, marker.createTime)

	marker, err = parseRecommendMarker("", 12345)
	require.NoError(err)
	assert.Equal(0, marker.index)
	assert.Equal(now, marker.createTime)
}

func TestNewRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLastRankKey := usersrank.LastRankKey(usersrank.TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(testLastRankKey).Err())

	require.NoError(liverecommendedelements.TableLiveTabRecommend(service.DB).
		Delete("", "element_type = ?", liverecommendedelements.ElementLiveTabRecommend).Error)

	now := goutil.TimeNow()
	recommend := &liverecommendedelements.LiveRecommendedElements{
		ElementID:   4381915,
		ElementType: liverecommendedelements.ElementLiveTabRecommend,
		StartTime:   goutil.NewInt64(now.Unix()),
		ExpireTime:  now.Add(time.Hour).Unix(),
		Sort:        1,
	}
	require.NoError(service.DB.Create(recommend).Error)

	lastRankMembers := []*redis.Z{
		{Score: float64(4), Member: 9074501},
	}
	require.NoError(service.Redis.ZAdd(testLastRankKey, lastRankMembers...).Err())

	c := handler.NewTestContext(http.MethodGet, "open/recommend-list", true, nil)
	param, err := newRecommend(c, "0,1731914388", "0,1731914388", 22489473, 0)
	require.NoError(err)
	assert.NotNil(param)
	assert.NotEmpty(param.marker)
	assert.NotEmpty(param.resetMarker)
	assert.Equal(1, len(param.lastHourTop3Map))
	assert.Equal(1, len(param.tabRecommendMap))
}

func TestRecommend_recommendRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URLGetUserDramaCVs, func(any) (any, error) {
		resp := userapi.ListDramaCVResp{
			CVs: []userapi.DramaCVInfo{
				{UserID: 3013063},
				{UserID: 9074510},
				{UserID: 1074510},
			},
		}
		return resp, nil
	})
	defer cancel()

	testOpenRooms, err := room.FindAll(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"creator_id":  bson.M{"$gt": 0},
			"limit":       bson.M{"$exists": false},
		},
		options.Find().SetLimit(3),
	)
	require.NoError(err)

	testUserID := int64(9074509)
	service.Cache5Min.Set(
		keys.LocalKeyUser7DaysCreatorRank.Format(testUserID),
		[]*liveuser7daysrewardcreatorrank.LiveUser7DaysRewardCreatorRank{
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[2].CreatorID,
				RoomID:    testOpenRooms[2].RoomID,
				Rank:      1,
			},
			// 第二位和第三位设置相同的打赏量排名，以便测试次级排序
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[1].CreatorID,
				RoomID:    testOpenRooms[1].RoomID,
				Rank:      2,
			},
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[0].CreatorID,
				RoomID:    testOpenRooms[0].RoomID,
				Rank:      2,
			},
		},
		0,
	)

	now := goutil.TimeNow()
	r := recommend{
		userID: testUserID,
		marker: &recommendMarker{
			index:      0,
			createTime: now.Unix(),
		},
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
			FindLuckyBag:    true,
		},
	}
	// 测试没有缓存生成推荐列表
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	cacheKey := keys.KeyRecommendRooms2.Format(r.userID, r.marker.createTime)
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.NotZero(exists)

	// 测试从缓存里获取推荐列表
	r.marker, err = parseRecommendMarker(r.markerPagination.Marker, r.userID)
	require.NoError(err)
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	service.LRURedis.Del(cacheKey)

	// 测试没有缓存，排除关注主播
	r.marker = &recommendMarker{
		index:      0,
		createTime: now.Add(time.Minute).Unix(),
	}
	r.excludeFollowUserIDs = []int64{testOpenRooms[0].CreatorID}
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	cacheKey = keys.KeyRecommendRooms2.Format(r.userID, r.marker.createTime)
	exists, err = service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.NotZero(exists)

	// 测试从缓存里获取推荐列表，排除关注主播
	r.marker, err = parseRecommendMarker(r.markerPagination.Marker, r.userID)
	require.NoError(err)
	r.rooms = []*room.Simple{}
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	service.LRURedis.Del(cacheKey)

	// 测试没有缓存，首位为底部 tab 推荐主播
	r.noticeRoomID = 22489473
	r.marker = &recommendMarker{
		index:      0,
		createTime: now.Add(time.Minute).Unix(),
	}
	r.recommendType = typeExcludeFollowUser
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	assert.Equal(r.noticeRoomID, r.rooms[0].RoomID)
	cacheKey = keys.KeyRecommendRooms2.Format(r.userID, r.marker.createTime)
	exists, err = service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.NotZero(exists)

	// 测试有缓存，首位为底部 tab 推荐主播
	r.rooms = []*room.Simple{}
	r.noticeRoomID = 22489473
	r.marker = &recommendMarker{
		index:      0,
		createTime: now.Add(time.Minute).Unix(),
	}
	require.NoError(r.recommendRoom())
	assert.Equal(8, len(r.rooms))
	assert.Equal(r.noticeRoomID, r.rooms[0].RoomID)
	service.LRURedis.Del(cacheKey)
}

func TestRecommend_buildSnapshot(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URLGetUserDramaCVs, func(any) (any, error) {
		resp := userapi.ListDramaCVResp{
			CVs: []userapi.DramaCVInfo{
				{UserID: 3013063},
				{UserID: 9074510},
				{UserID: 1074510},
			},
		}
		return resp, nil
	})
	defer cancel()

	testOpenRooms, err := room.FindAll(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"creator_id":  bson.M{"$gt": 0},
			"limit":       bson.M{"$exists": false},
		},
		options.Find().SetLimit(3),
	)
	require.NoError(err)

	testUserID := int64(9074509)
	service.Cache5Min.Set(
		keys.LocalKeyUser7DaysCreatorRank.Format(testUserID),
		[]*liveuser7daysrewardcreatorrank.LiveUser7DaysRewardCreatorRank{
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[2].CreatorID,
				RoomID:    testOpenRooms[2].RoomID,
				Rank:      1,
			},
			// 第二位和第三位设置相同的打赏量排名，以便测试次级排序
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[1].CreatorID,
				RoomID:    testOpenRooms[1].RoomID,
				Rank:      2,
			},
			{
				UserID:    testUserID,
				CreatorID: testOpenRooms[0].CreatorID,
				RoomID:    testOpenRooms[0].RoomID,
				Rank:      2,
			},
		},
		0,
	)

	now := goutil.TimeNow()
	r := recommend{
		userID: testUserID,
		marker: &recommendMarker{
			index:      0,
			createTime: now.Unix(),
		},
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
			FindLuckyBag:    true,
		},
	}
	recommendGroups, simpleGroups := r.buildSnapshot()
	require.NoError(err)
	assert.Equal(12, len(recommendGroups))
	assert.Equal(12, len(simpleGroups))
	cacheKey := keys.KeyRecommendRooms2.Format(r.userID, r.marker.createTime)
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.NotZero(exists)
	service.LRURedis.Del(cacheKey)
}

func TestRecommend_getPageRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	recommendCreatorIDs := [][]recommendCache{
		{
			{CreatorID: 10},
			{CreatorID: 3013063},
			{CreatorID: 516},
			{CreatorID: 1023},
			{CreatorID: 370478},
			{CreatorID: 14},
			{CreatorID: 15614},
			{CreatorID: 9074699},
		},
	}

	r := recommend{
		recommendType: typeExcludeFollowUser,
	}
	simples, err := r.getPageRecommendList(0, true, recommendCreatorIDs, nil)
	require.NoError(err)
	assert.Equal(8, len(simples))

	r.recommendType = typeDefault
	simples, err = r.getPageRecommendList(0, true, recommendCreatorIDs, nil)
	require.NoError(err)
	assert.Equal(8, len(simples))

	simpleGroups := [][]*room.Simple{
		{
			{CreatorID: 10},
			{CreatorID: 3013063},
			{CreatorID: 516},
			{CreatorID: 1023},
			{CreatorID: 370478},
			{CreatorID: 14},
			{CreatorID: 15614},
			{CreatorID: 9074699},
		},
	}
	simples, err = r.getPageRecommendList(0, false, recommendCreatorIDs, simpleGroups)
	require.NoError(err)
	assert.Equal(8, len(simples))

	r.noticeRoom = &room.Simple{
		CreatorID: 9074699,
	}
	simples, err = r.getPageRecommendList(0, false, recommendCreatorIDs, simpleGroups)
	require.NoError(err)
	assert.Equal(7, len(simples))
}

func TestRecommend_fillSimpleToLength(t *testing.T) {
	assert := assert.New(t)

	simples := []*room.Simple{
		{CreatorID: 9074507},
		{CreatorID: 9074508},
		{CreatorID: 9074509},
	}
	r := recommend{
		recommendType: typeDefault,
		opt: &room.FindOptions{
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
			FindLuckyBag:    true,
		},
	}
	r.recommendType = typeDefault
	newSimple := r.fillSimpleToLength(simples, nil)
	assert.Equal(8, len(newSimple))

	recommendCache := [][]recommendCache{
		{{CreatorID: 9074508}},
		{{CreatorID: 9074509}},
	}
	r.recommendType = typeExcludeFollowUser
	r.excludeFollowUserIDs = []int64{9074507}
	newSimple = r.fillSimpleToLength(simples, recommendCache)
	assert.Equal(8, len(newSimple))

	r.noticeRoom = &room.Simple{
		CreatorID: 9074511,
	}
	newSimple = r.fillSimpleToLength(simples, recommendCache)
	assert.Equal(7, len(newSimple))
}

func TestRecommend_findNoticeRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := recommend{}
	err := r.findNoticeRoom()
	require.NoError(err)
	assert.Nil(r.noticeRoom)

	r.noticeRoomID = 22489473
	err = r.findNoticeRoom()
	require.NoError(err)
	assert.NotNil(r.noticeRoom)
}

func TestRecommend_refreshRoomMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rc := []recommendCache{
		{CreatorID: 3013063},
		{CreatorID: 370478},
		{CreatorID: 9074699},
	}
	r := recommend{}
	simples, err := r.refreshRoomMessage(rc)
	require.NoError(err)
	assert.Equal(3, len(simples))

	r.noticeRoom = &room.Simple{
		CreatorID: 370478,
	}
	simples, err = r.refreshRoomMessage(rc)
	require.NoError(err)
	assert.Equal(2, len(simples))
}

func TestRecommend_recommendInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rooms := []*room.Simple{
		{CreatorID: 9074501},
		{CreatorID: 9074502},
		{CreatorID: 9074503},
		{CreatorID: 9074504},
	}
	r := recommend{}
	newRooms, err := r.recommendInsert(rooms)
	require.NoError(err)
	assert.Equal(4, len(newRooms))

	r.tabRecommendMap = map[int64]int{
		114693474: 1,
		5202465:   3,
	}
	newRooms, err = r.recommendInsert(rooms)
	require.NoError(err)
	assert.Equal(6, len(newRooms))

	actual := make([]int64, 0, len(newRooms))
	for _, v := range newRooms {
		actual = append(actual, v.CreatorID)
	}

	expected := []int64{3013063, 9074501, 370478, 9074502, 9074503, 9074504}
	assert.Equal(expected, actual)
}

func TestRecommend_addRecommendTags(t *testing.T) {
	assert := assert.New(t)

	allRooms := []*room.Simple{
		{CreatorID: 9074509},
		{CreatorID: 3013063},
		{CreatorID: 9074510},
		{CreatorID: 9074511, CustomTag: &tag.CustomTag{TagName: "听听歌吧"}},
	}

	recommendCache := [][]recommendCache{
		{{CreatorID: 9074510, RecommendTag: "1"}, {CreatorID: 3013063, RecommendTag: ""}},
	}

	r := recommend{
		lastHourTop3Map: map[int64]string{
			9074509: "oss://icon.png",
		},
	}
	r.addRecommendTags(allRooms, recommendCache)
	item := allRooms[0]
	assert.Equal(room.RecommendTagTypeLastHourRank, item.RecommendTag.Type)
	assert.Equal("https://static-test.missevan.com/icon.png", item.RecommendTag.IconURL)
	assert.Nil(allRooms[1].RecommendTag)
	item = allRooms[2]
	assert.Equal(room.RecommendTagTypeRecommend, item.RecommendTag.Type)
	assert.Equal("1", item.RecommendTag.Text)
	item = allRooms[3]
	assert.Equal(room.RecommendTagTypeCustomTag, item.RecommendTag.Type)
	assert.Equal("听听歌吧", item.RecommendTag.Text)
}

func TestRecommend_buildExcludeFollowRooms(t *testing.T) {
	assert := assert.New(t)

	groups := [][]recommendCache{
		{{CreatorID: 1, RecommendTag: "1"}, {CreatorID: 2, RecommendTag: "2"}},
		{{CreatorID: 3, RecommendTag: "3"}, {CreatorID: 4, RecommendTag: "4"}},
	}

	r := recommend{}
	list := r.buildExcludeFollowRooms(groups, 2)
	assert.Empty(list)

	list = r.buildExcludeFollowRooms(groups, 1)
	expected := []recommendCache{
		{CreatorID: 3, RecommendTag: "3"},
		{CreatorID: 4, RecommendTag: "4"},
		{CreatorID: 1, RecommendTag: "1"},
		{CreatorID: 2, RecommendTag: "2"},
	}
	assert.Equal(expected, list)

	r.excludeFollowUserIDs = []int64{1, 2}
	list = r.buildExcludeFollowRooms(groups, 1)
	assert.Equal(expected[:2], list)
}

func TestRecommend_buildMoreRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	recommendCreatorIDs := []recommendCache{
		{CreatorID: 10},
		{CreatorID: 3013063},
		{CreatorID: 516},
		{CreatorID: 1023},
		{CreatorID: 370478},
		{CreatorID: 14},
		{CreatorID: 15614},
		{CreatorID: 9074699},
	}

	r := recommend{}
	simples, err := r.buildMoreRecommend(recommendCreatorIDs)
	require.NoError(err)
	assert.Equal(8, len(simples))

	r.noticeRoom = &room.Simple{
		CreatorID: 9074509,
	}
	simples, err = r.buildMoreRecommend(recommendCreatorIDs)
	require.NoError(err)
	assert.Equal(7, len(simples))
}

func TestRecommend_excludeBlockList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	r := recommend{
		userID: testUserID,
		blockCreatorMap: map[int64]struct{}{
			9074511: {},
		},
		tabRecommendMap: map[int64]int{
			112233445: 3,
		},
	}
	testRecommend := []recommendCache{
		{CreatorID: 9074510, RecommendTag: "1"},
		{CreatorID: 9074511, RecommendTag: "2"},
		{CreatorID: 9074512, RoomID: 112233445, RecommendTag: "3"},
	}
	list := r.excludeRecommendList(testRecommend)
	require.Equal(1, len(list))
	assert.Equal(int64(9074510), list[0].CreatorID)
}

func TestRecommend_saveCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	createTime := goutil.TimeNow().Unix()
	cacheKey := keys.KeyRecommendRooms2.Format(testUserID, createTime)
	err := service.Redis.Del(cacheKey).Err()
	require.NoError(err)

	r := recommend{
		userID: testUserID,
		marker: &recommendMarker{
			createTime: createTime,
		},
		resetMarker: &recommendMarker{
			createTime: createTime,
		},
	}
	testCache := [][]recommendCache{
		{{CreatorID: 1, RecommendTag: "1"}, {CreatorID: 2, RecommendTag: "2"}},
		{{CreatorID: 3, RecommendTag: "3"}, {CreatorID: 4, RecommendTag: "4"}},
	}
	r.saveCache(testCache)
	cache, err := r.loadCache()
	require.NoError(err)
	assert.Equal(testCache, cache)
}

func TestBlockCreatorMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)

	key := keys.KeyUserRecommendBlockList1.Format(testUserID)
	require.NoError(service.LRURedis.Del(key).Err())

	cancel := mrpc.SetMock(userapi.URIGoUserBlocklist, func(body any) (any, error) {
		blockType := body.(map[string]any)["type"]
		switch blockType {
		case userapi.BlockListTypeBlockedByUser:
			return handler.M{"block_list": []int64{3013063, 1023, 370478}}, nil
		case userapi.BlockListTypeWhoBlockedUser:
			return handler.M{"block_list": []int64{15614, 9074699}}, nil
		default:
			return nil, actionerrors.ErrParams
		}
	})
	defer cancel()

	blockList := buildBlockCreatorMap(mrpc.UserContext{}, 0)
	assert.Nil(blockList)

	// 测试没有缓存返回黑名单
	blockList = buildBlockCreatorMap(mrpc.UserContext{}, testUserID)
	assert.Equal(5, len(blockList))

	exists, err := service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.NotZero(exists)

	// 测试从缓存获取黑名单
	blockList = buildBlockCreatorMap(mrpc.UserContext{}, testUserID)
	assert.Equal(5, len(blockList))
}

func TestHotSuppressionRoomsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyRoomsSuppressionHotList0.Format()
	err := service.Redis.ZAdd(key, &redis.Z{
		Score:  -1,
		Member: 122233556,
	}).Err()
	require.NoError(err)

	roomsMap := hotSuppressionRoomsMap()
	assert.Equal(1, len(roomsMap))
}

func TestRecommend_findDramaCVs(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URLGetUserDramaCVs, func(any) (any, error) {
		resp := userapi.ListDramaCVResp{
			CVs: []userapi.DramaCVInfo{
				{UserID: 3013063},
				{UserID: 9074510},
				{UserID: 1074510},
			},
		}
		return resp, nil
	})
	defer cancel()

	r := recommend{
		userID: 9074509,
	}
	cvIDs := r.findDramaCVs(nil)
	assert.Equal(2, len(cvIDs))
}
