package recommend

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// searchRecommendCacheDuration 搜索推荐房间缓存时长
const searchRecommendCacheDuration = time.Minute

type recommendationRoomCache struct {
	RoomID       int64              `json:"room_id"`
	RecommendTag *room.RecommendTag `json:"recommend_tag,omitempty"`
}

func saveRecommendationSourcesCache(userID int64, sources [][]*room.Simple) {
	cacheData := make([][]recommendationRoomCache, 0, len(sources))

	for _, rooms := range sources {
		sourceCache := make([]recommendationRoomCache, 0, len(rooms))
		for _, room := range rooms {
			sourceCache = append(sourceCache, recommendationRoomCache{RoomID: room.RoomID, RecommendTag: room.RecommendTag})
		}
		cacheData = append(cacheData, sourceCache)
	}

	cacheBytes, err := json.Marshal(cacheData)
	if err != nil {
		logger.Error(err)
		return
	}

	key := keys.KeySearchRecommendRooms1.Format(userID)
	err = service.LRURedis.Set(key, string(cacheBytes), searchRecommendCacheDuration).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func loadRecommendationSourcesFromCache(userID int64, listRooms func(roomIDs []int64) ([]*room.Simple, error)) [][]*room.Simple {
	key := keys.KeySearchRecommendRooms1.Format(userID)
	cacheBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		return nil
	}

	var cacheData [][]recommendationRoomCache
	if err := json.Unmarshal(cacheBytes, &cacheData); err != nil {
		logger.Error(err)
		return nil
	}

	roomIDs := make([]int64, 0, len(cacheData)*len(cacheData[0]))
	for _, source := range cacheData {
		for _, room := range source {
			roomIDs = append(roomIDs, room.RoomID)
		}
	}

	rooms, err := listRooms(sets.Uniq(roomIDs))
	if err != nil {
		logger.Error(err)
		return nil
	}
	roomIDRoomMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Simple)

	sources := make([][]*room.Simple, 0, len(cacheData))
	for _, sourceCache := range cacheData {
		source := make([]*room.Simple, 0, len(sourceCache))
		for _, roomCache := range sourceCache {
			if room, ok := roomIDRoomMap[roomCache.RoomID]; ok {
				if roomCache.RecommendTag != nil {
					room.RecommendTag = roomCache.RecommendTag
				}
				source = append(source, room)
				// Remove the room from the map to prevent duplicate,
				// if the room is not in the map, it means it has been used.
				delete(roomIDRoomMap, roomCache.RoomID)
			}
		}
		sources = append(sources, source)
	}

	return sources
}
