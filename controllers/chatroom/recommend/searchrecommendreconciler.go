package recommend

import (
	"math/rand"
	"slices"
	"sort"

	"github.com/MiaoSiLa/live-service/models/room"
)

type recommendationReconciler struct {
	sources            []recommendationSource
	appointments       []appointment
	blockedCreatorIDs  map[int64]struct{}
	suppressionRoomIDs map[int64]struct{}

	// Nil if no substitutes, otherwise the source to be used when the current source is exhausted.
	// Use pointer to share the same substitutes between multiple sources.
	substitutes *recommendationLazySource

	result recommendationResult
}

func newRecommendationReconciler(
	sources [][]*room.Simple,
	appointments []appointment,
	blockedCreatorIDs map[int64]struct{},
	suppressionRoomIDs map[int64]struct{},
	substitutes *recommendationLazySource,
) *recommendationReconciler {
	reconciler := &recommendationReconciler{
		appointments:       appointments,
		blockedCreatorIDs:  blockedCreatorIDs,
		suppressionRoomIDs: suppressionRoomIDs,
		substitutes:        substitutes,
	}

	reconciler.sources = make([]recommendationSource, 0, len(sources))
	for _, source := range sources {
		reconciler.sources = append(reconciler.sources, recommendationSource{
			rooms:       source,
			substitutes: reconciler.substitutes,
		})
	}

	return reconciler
}

func (r *recommendationR<PERSON>onciler) reconcile(groupCount int) []*room.Simple {
	if groupCount <= 0 {
		return nil
	}

	r.result.roomIDs = make(map[int64]struct{}, groupCount*len(r.sources))
	r.result.groups = make([][]*room.Simple, groupCount)

	sort.SliceStable(r.appointments, func(i, j int) bool {
		return r.appointments[i].pos < r.appointments[j].pos
	})

	appointmentRoomIDs := make(map[int64]struct{}, len(r.appointments))
	for _, appointment := range r.appointments {
		appointmentRoomIDs[appointment.room.RoomID] = struct{}{}
	}

	for groupNo := 0; groupNo < groupCount; groupNo++ {
		for sourceNo := range r.sources {
			source := &r.sources[sourceNo]
			room := source.next(func(room *room.Simple) bool {
				if room == nil {
					return false
				}
				if _, ok := appointmentRoomIDs[room.RoomID]; ok {
					return false
				}
				if _, ok := r.blockedCreatorIDs[room.CreatorID]; ok {
					return false
				}
				if _, ok := r.suppressionRoomIDs[room.RoomID]; ok {
					return false
				}
				if _, ok := r.result.roomIDs[room.RoomID]; ok {
					return false
				}
				return true
			})
			if room == nil {
				// Reach the end of the source and substitute, no more room to suggest for this source.
				// It doesn't happen in normal case, because we have enough rooms in the substitute source.
				continue
			}
			r.result.append(groupNo, room)
		}
		shuffleRooms(r.result.groups[groupNo])
	}

	r.insertAppointments()
	return r.joinGroups()
}

func (r *recommendationReconciler) insertAppointments() {
	if len(r.result.groups) <= 0 {
		return
	}

	for _, appointment := range r.appointments {
		// Appointment is prioritized than suppression, so we don't need to check suppression here.
		if _, ok := r.blockedCreatorIDs[appointment.room.CreatorID]; ok {
			continue
		}
		if _, ok := r.result.roomIDs[appointment.room.RoomID]; ok {
			continue
		}
		r.result.insert(0, appointment.pos, appointment.room)
	}

	maxGroupSize := len(r.sources)
	group := r.result.groups[0]
	if len(group) > maxGroupSize {
		deleted := group[maxGroupSize:]
		for _, room := range deleted {
			delete(r.result.roomIDs, room.RoomID)
		}
		group = group[:maxGroupSize]
		r.result.groups[0] = group
	}
}

func (r *recommendationReconciler) joinGroups() []*room.Simple {
	if len(r.result.groups) == 0 {
		return []*room.Simple{}
	}
	rooms := make([]*room.Simple, 0, len(r.result.groups)*len(r.result.groups[0]))
	for _, group := range r.result.groups {
		rooms = append(rooms, group...)
	}
	return rooms
}

type recommendationSource struct {
	cursor int
	rooms  []*room.Simple

	substitutes *recommendationLazySource
}

func (s *recommendationSource) next(accept func(room *room.Simple) bool) *room.Simple {
	for s.cursor < len(s.rooms) {
		room := s.rooms[s.cursor]
		s.cursor++
		if accept(room) {
			return room
		}
	}

	if s.substitutes != nil {
		return s.substitutes.next(accept)
	}

	return nil
}

type recommendationLazySource struct {
	recommendationSource

	loaded bool
	loader func() []*room.Simple
}

func (s *recommendationLazySource) next(accept func(room *room.Simple) bool) *room.Simple {
	if !s.loaded {
		rooms := s.loader()
		s.rooms = rooms
		s.loaded = true
	}

	return s.recommendationSource.next(accept)
}

type appointment struct {
	pos  int // 0-based index
	room *room.Simple
}

type recommendationResult struct {
	roomIDs map[int64]struct{}
	groups  [][]*room.Simple
}

func (r *recommendationResult) append(groupNo int, room *room.Simple) {
	if _, ok := r.roomIDs[room.RoomID]; ok {
		return
	}
	r.groups[groupNo] = append(r.groups[groupNo], room)
	r.roomIDs[room.RoomID] = struct{}{}
}

func (r *recommendationResult) insert(groupNo int, pos int, room *room.Simple) {
	if pos < 0 || groupNo < 0 || groupNo >= len(r.groups) {
		return
	}

	group := r.groups[groupNo]
	if pos >= len(group) {
		r.append(groupNo, room)
		return
	}

	group = slices.Insert(group, pos, room)
	r.groups[groupNo] = group
	r.roomIDs[room.RoomID] = struct{}{}
}

func shuffleRooms(rooms []*room.Simple) {
	if len(rooms) > 1 {
		rand.Shuffle(len(rooms), func(i, j int) {
			rooms[i], rooms[j] = rooms[j], rooms[i]
		})
	}
}
