package recommend

import (
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type feedList struct {
	Data       []feedRoom              `json:"data"`
	Pagination goutil.MarkerPagination `json:"pagination"`
}

type feedRoom struct {
	RoomID          int64            `json:"room_id"`
	Name            string           `json:"name"`
	CreatorID       int64            `json:"creator_id"`
	CreatorUsername string           `json:"creator_username"`
	CreatorIconURL  string           `json:"creator_iconurl,omitempty"`
	CoverURL        string           `json:"cover_url,omitempty"`
	CatalogID       int64            `json:"catalog_id"`
	CatalogName     string           `json:"catalog_name,omitempty"`
	CatalogColor    string           `json:"catalog_color,omitempty"`
	CustomTag       *tag.CustomTag   `json:"custom_tag,omitempty"`
	Background      *room.Background `json:"background,omitempty"`
	// TODO：构建完整预览页标签和额外信息
	PreviewTag   string             `json:"preview_tag,omitempty"`
	PreviewIntro *room.PreviewIntro `json:"preview_intro,omitempty"`
	Trace        string             `json:"trace,omitempty"`
}

// ActionChatroomOpenFeedList 导航栏显示直播开播全屏直播流列表
/**
 * @api {get} /api/v2/chatroom/open/feed-list 导航栏显示直播开播全屏直播流列表
 * @apiDescription 列出直播开播全屏直播流列表，客户端需要对新加载的和前面重复出现的直播间去重处理，总是在新的一轮列表中重新去重 \
 * 刷新页面时第一次请求使用当前列表的 marker 传入到 reset_marker 中，后续请求使用前一次响应中的 marker 传入到下一次新的列表请求的 marker 参数中 \
 * 如果 has_more 为 false，也需要把最后一页的 marker 传入到下一次新列表请求的 reset_marker 中
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [notice_room_id] 直播 tab 入口直播动态的房间 ID，只有在推荐页面时需要传入
 * @apiParam {String} [reset_marker] 刷新页面或没有下一页刷新时，第一次请求使用当前列表的 marker 传入
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求提供前一次响应中的 marker
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "room_id": 122341,
 *           "name": "测试",
 *           "creator_id": 10,
 *           "creator_username": "bless",
 *           "creator_iconurl": "https://static.maoercdn.com/avatars/202410/07/f70f67dfcb93e4bf70a24584df7cff9d095351.png",
 *           "cover_url": "https://static-test.missevan.com/avatars/icon01.png",
 *           "catalog_id": 106,
 *           "catalog_name": "分区名",
 *           "catalog_color": "#ffffff",
 *           "custom_tag": {
 *             "tag_id": 10001,
 *             "tag_name": "腹黑青叔"
 *           },
 *           "background": { // 无 background 字段 或 enable 为 false 时、使用客户端默认图
 *             "enable": true,
 *             "opacity": 0.6785714030265808,
 *             "image_url": "https://static-test.missevan.com/fmbackgrounds/202004/28/80c416e82402f2a178bdabe8bc2496ca.png"
 *           },
 *           "preview_tag": "最近看过", // 预览页标签，未下发时默认展示 catalog_name (背景色 catalog_color) + custom_tag.tag_name
 *           "preview_intro": { // 预览页额外信息，未下发时默认展示 name 直播标题
 *             "icon_url": "https://static.missevan.com/icon/icon01.png",
 *             "title": "直播小时榜排名第 N"
 *           },
 *           "trace": "{\"preview_tags\":[\"最新看过\",\"我的关注\",\"音乐\",\"元气少年\"],\"preview_intro_title\":\"直播小时榜排名第 N\"}" // 埋点数据, 客户端直接透传
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否有更多数据
 *         "marker": "40,0,1695717196620" // 当前的 marker，需要加载下一页时回传，最后一页也会返回
 *       }
 *     }
 *   }
 */
func ActionChatroomOpenFeedList(c *handler.Context) (handler.ActionResponse, string, error) {
	marker, _, _ := c.GetParamMarker()
	noticeRoomID, _ := c.GetDefaultParamInt64("notice_room_id", 0)
	resetMarker, _ := c.GetParam("reset_marker")

	recommend, err := newRecommend(c, marker, resetMarker, noticeRoomID, typeDefault)
	if err != nil {
		return nil, "", err
	}
	err = recommend.recommendRoom()
	if err != nil {
		return nil, "", err
	}

	isApp := c.Equip().FromApp
	rooms := make([]feedRoom, 0, len(recommend.rooms))
	for _, v := range recommend.rooms {
		// Web 端不需要显示福袋状态
		if !isApp {
			v.Status.LuckyBag = 0
		}
		rooms = append(rooms, feedRoom{
			RoomID:          v.RoomID,
			Name:            v.Name,
			CreatorID:       v.CreatorID,
			CreatorUsername: v.CreatorUsername,
			CreatorIconURL:  v.CreatorIconURL,
			CoverURL:        v.CoverURL,
			CatalogID:       v.CatalogID,
			CatalogName:     v.CatalogName,
			CatalogColor:    v.CatalogColor,
			CustomTag:       v.CustomTag,
			Background:      v.Background,
		})
	}
	// 标记访问底部导航栏直播页面的时间，只有第一页并且用户登录时需要标记
	if marker == "" && c.UserID() != 0 {
		err = userapi.MarkPageViewed(c.UserContext(), c.UserID())
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return feedList{
		Data:       rooms,
		Pagination: recommend.markerPagination,
	}, "", nil
}
