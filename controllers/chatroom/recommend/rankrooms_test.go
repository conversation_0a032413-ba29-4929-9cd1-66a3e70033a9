package recommend

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestListRankRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := goutil.TimeNow()
	testData := []*redis.Z{
		{Score: 10, Member: 10},
		{Score: 11, Member: 11},
	}
	key := usersrank.Key(usersrank.TypeHourLiveTab, when)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, testData...).Err())

	rooms, err := listRankRooms(usersrank.TypeHourLiveTab, nil, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.Len(rooms, 1)
	assert.EqualValues(22489473, rooms[0].RoomID)
}

func TestListLastRankRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLastRankKet := usersrank.LastRankKey(usersrank.TypeHourLiveTab, goutil.TimeNow())
	lastRankMembers := []*redis.Z{
		{Score: float64(10), Member: 10},
		{Score: float64(11), Member: 11},
	}
	require.NoError(service.Redis.Del(testLastRankKet).Err())
	require.NoError(service.Redis.ZAdd(testLastRankKet, lastRankMembers...).Err())

	rooms, err := listLastRankRooms(usersrank.TypeHourLiveTab, nil, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.Len(rooms, 1)
	assert.EqualValues(22489473, rooms[0].RoomID)
}
