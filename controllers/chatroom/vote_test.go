package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionInteractionOptions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/interaction/options?type=error", true, nil)
	_, err := ActionInteractionOptions(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/interaction/options?type=vote", true, nil)
	result, err := ActionInteractionOptions(c)
	require.NoError(err)
	r := result.(handler.M)
	assert.NotEmpty(r["duration"])
}

func TestActionInteractionStartVoteCheck(t *testing.T) {
	assert := assert.New(t)

	testTitle := "test title"
	testDuration := int64(15000)
	testContents := []string{"零", "十分", "一百分", "一百一十分"}

	// 参数错误
	params := []startVoteParam{
		{
			Title:    "", // title 为空
			Duration: testDuration,
			Content:  testContents,
		},
		{
			Title:    "测试超长15字符123456 a", // title 超过 15 字符
			Duration: testDuration,
			Content:  testContents,
		},
		{
			Title:    testTitle,
			Duration: 0, // duration 为空
			Content:  testContents,
		},
		{
			Title:    testTitle,
			Duration: -123, // duration 小于 0
			Content:  testContents,
		},
		{
			Title:    testTitle,
			Duration: 44, // duration 不在配置中
			Content:  testContents,
		},
		{
			Title:    testTitle,
			Duration: testDuration,
			Content:  []string{"1"}, // 投票选项少于两个
		},
		{
			Title:    testTitle,
			Duration: testDuration,
			Content:  []string{"1", "2", "3", "4", ""}, // 投票选项超过四个
		},
		{
			Title:    testTitle,
			Duration: testDuration,
			Content:  []string{"1", "超5字符ab"}, // 投票选项描述超五字符
		},
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	for i := range params {
		assert.Equal(actionerrors.ErrParams, params[i].check(c), "参数错误")
	}

	// 直播间不存在
	param := startVoteParam{
		Title:    testTitle,
		Duration: testDuration,
		Content:  testContents,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	c.User().ID = 9999
	assert.Equal(actionerrors.ErrCannotFindRoom, param.check(c), "直播间不存在")

	// 直播间未开通粉丝勋章
	testNoMedalCreatorID := int64(516)
	param = startVoteParam{
		Title:    testTitle,
		Duration: testDuration,
		Content:  testContents,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	c.User().ID = testNoMedalCreatorID
	assert.Equal(actionerrors.ErrNoAuthority, param.check(c), "直播间未开通粉丝勋章")

	// 违禁词
	param = startVoteParam{
		Title:    testTitle,
		Duration: testDuration,
		Content:  []string{"王八蛋", "ok"},
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	assert.EqualError(param.check(c), "您发起的礼物投票中的主题或选项中含有违禁词，请修改后发布哦~")

	param = startVoteParam{
		Title:    testTitle,
		Duration: testDuration,
		Content:  testContents,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	assert.NoError(param.check(c))
	assert.Equal(testContents, param.Content)
}

func TestActionInteractionStartVoteAndClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(18113499)
	param := startVoteParam{
		Title:    "test title",
		Duration: 15000,
		Content:  []string{"生", "三分熟"},
	}
	key := keys.LockRoomsInteraction1.Format(roomID)
	service.Redis.Del(key)

	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionInteractionStartVote(c)
	require.NoError(err)
	v, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.Equal(interaction.TypeInteractionVote, v)

	c = handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err = ActionInteractionCloseVote(c)
	require.NoError(err)
	d, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.GreaterOrEqual(int64(10000), d.Milliseconds())

	c = handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err = ActionInteractionCloseVote(c)
	assert.EqualError(err, "当前没有进行中的礼物投票")
}
