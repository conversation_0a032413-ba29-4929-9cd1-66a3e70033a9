package pia

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestActionPiaStart(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(22489473)
	cancel := mrpc.SetMock("im://broadcast/user", func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	err = room.RemoveTag([]int64{r.RoomID}, tag.TagListenDrama)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/pia/start", true, handler.M{
		"room_id": r.RoomID,
	})
	c.User().ID = r.CreatorID
	resp, err := ActionPiaStart(c)
	require.NoError(err)
	assert.Equal("您已成功开始 pia 戏", resp.(handler.M)["msg"])
	r, err = room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.True(r.ContainsTag(tag.TagListenDrama))
}

func TestActionPiaStop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(22489473)
	cancel := mrpc.SetMock("im://broadcast/user", func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	err = room.AddTag([]int64{r.RoomID}, tag.TagListenDrama)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/pia/stop", true, handler.M{
		"room_id": r.RoomID,
	})
	c.User().ID = r.CreatorID
	resp, err := ActionPiaStop(c)
	require.NoError(err)
	assert.Equal("您已成功停止 pia 戏", resp.(handler.M)["msg"])
	r, err = room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.False(r.ContainsTag(tag.TagListenDrama))
}

func TestNewPiaParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/pia/start", true, handler.M{
		"room_id": 1,
	})
	param, err := newPiaParam(c, true)
	require.NoError(err)
	assert.NotNil(param)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/pia/start", true, handler.M{
		"room_id": -1,
	})
	_, err = newPiaParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestPiaParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &piaParam{
		RoomID:  223344,
		isStart: false,
	}
	ok, err := param.check()
	assert.EqualError(err, "仅允许主播操作 pia 戏")
	assert.False(ok)

	param = &piaParam{
		RoomID:  223344,
		userID:  223344,
		isStart: false,
	}
	ok, err = param.check()
	assert.EqualError(err, "暂无操作权限")
	assert.False(ok)

	now := time.Unix(10, 0)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	param = &piaParam{
		RoomID:  22489473,
		userID:  10,
		isStart: false,
	}
	err = room.AddTag([]int64{param.RoomID}, tag.TagListenDrama)
	require.NoError(err)
	ok, err = param.check()
	require.NoError(err)
	assert.True(ok)

	// 已开始 pia 戏，无 err，但 ok 为 false
	param = &piaParam{
		RoomID:  22489473,
		userID:  10,
		isStart: true,
	}
	ok, err = param.check()
	require.NoError(err)
	assert.False(ok)

	now = time.Unix(20, 0)
	param = &piaParam{
		RoomID:  22489473,
		userID:  10,
		isStart: false,
	}
	err = room.RemoveTag([]int64{param.RoomID}, tag.TagListenDrama)
	require.NoError(err)
	ok, err = param.check()
	require.NoError(err)
	assert.False(ok)
}

func TestPiaParam_start(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(10, 0)
	})
	defer goutil.SetTimeNow(nil)

	param := &piaParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    223344,
				CreatorID: 223344,
			},
		},
		isStart: true,
	}
	err := param.start()
	require.NoError(err)
	r, err := liveroomtagrecord.FindLastRecord(tag.TagListenDrama, param.room.RoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(liveroomtagrecord.OperationAdd, r.Operation)
}

func TestPiaParam_stop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(20, 0)
	})
	defer goutil.SetTimeNow(nil)

	param := &piaParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    223344,
				CreatorID: 223344,
			},
		},
		isStart: false,
	}
	err := param.stop()
	require.NoError(err)
	r, err := liveroomtagrecord.FindLastRecord(tag.TagListenDrama, param.room.RoomID)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(liveroomtagrecord.OperationRemove, r.Operation)
}

func TestPiaParam_resp(t *testing.T) {
	assert := assert.New(t)

	param := &piaParam{
		isStart: true,
	}
	msg := param.resp()["msg"]
	assert.Equal("您已成功开始 pia 戏", msg)

	param = &piaParam{
		isStart: false,
	}
	msg = param.resp()["msg"]
	assert.Equal("您已成功停止 pia 戏", msg)
}
