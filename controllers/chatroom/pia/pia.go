package pia

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/livedb/livetagcontrollist"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type piaParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`

	userID  int64
	isStart bool
	room    *room.Room
}

func newPiaParam(c *handler.Context, isStart bool) (*piaParam, error) {
	var param piaParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.isStart = isStart
	param.userID = c.UserID()
	return &param, nil
}

// check 检查是否可以开始/停止 pia 戏
// 若无 error 并返回 false 时，表示已经开始或停止 pia 戏
func (param *piaParam) check() (bool, error) {
	var err error
	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return false, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return false, actionerrors.ErrCannotFindRoom
	}
	if param.room.CreatorID != param.userID {
		return false, actionerrors.NewErrForbidden("仅允许主播操作 pia 戏")
	}

	allow, err := livetagcontrollist.IsAllowRoomAddTag(tag.TagListenDrama, param.RoomID)
	if err != nil {
		return false, actionerrors.NewErrServerInternal(err, nil)
	}
	if !allow {
		return false, actionerrors.NewErrForbidden("暂无操作权限")
	}

	isListenDrama := param.room.ContainsTag(tag.TagListenDrama)
	if param.isStart {
		if isListenDrama {
			return false, nil
		}
	} else {
		if !isListenDrama {
			return false, nil
		}
	}
	return true, nil
}

func (param *piaParam) start() error {
	err := servicedb.Tx(liveroomtagrecord.DB(), func(tx *gorm.DB) error {
		err := liveroomtagrecord.AddRecords(tx, tag.TagListenDrama, []int64{param.room.RoomID}, param.room.CreatorID,
			liveroomtagrecord.RoleRoomCreator, liveroomtagrecord.OperationAdd)
		if err != nil {
			return err
		}
		err = room.AddTag([]int64{param.room.RoomID}, tag.TagListenDrama)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *piaParam) stop() error {
	err := servicedb.Tx(liveroomtagrecord.DB(), func(tx *gorm.DB) error {
		err := liveroomtagrecord.AddRecords(tx, tag.TagListenDrama, []int64{param.room.RoomID}, param.room.CreatorID,
			liveroomtagrecord.RoleRoomCreator, liveroomtagrecord.OperationRemove)
		if err != nil {
			return err
		}
		err = room.RemoveTag([]int64{param.room.RoomID}, tag.TagListenDrama)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *piaParam) broadcast() {
	err := userapi.BroadcastUser(param.room.RoomID, param.room.CreatorID,
		tag.NewPiaPayload(param.isStart, param.room.RoomID, false))
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *piaParam) resp() handler.M {
	if param.isStart {
		return handler.M{"msg": "您已成功开始 pia 戏"}
	}
	return handler.M{"msg": "您已成功停止 pia 戏"}
}

// ActionPiaStart 开始 pia 戏
/**
 * @api {post} /api/v2/chatroom/pia/start 开始 pia 戏
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "您已成功开始 pia 戏"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 加入听剧标签主播消息
 *   {
 *     "type": "pia",
 *     "event": "start",
 *     "room_id": 10659544
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionPiaStart(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPiaParam(c, true)
	if err != nil {
		return nil, err
	}
	ok, err := param.check()
	if err != nil {
		return nil, err
	}
	// NOTICE: 如果已经开始 pia 戏，直接返回成功
	if !ok {
		return param.resp(), nil
	}
	err = param.start()
	if err != nil {
		return nil, err
	}
	param.broadcast()
	return param.resp(), nil
}

// ActionPiaStop 停止 pia 戏
/**
 * @api {post} /api/v2/chatroom/pia/stop 停止 pia 戏
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "您已成功停止 pia 戏"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 移出听剧标签主播消息
 *   {
 *     "type": "pia",
 *     "event": "stop",
 *     "room_id": 10659544
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionPiaStop(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPiaParam(c, false)
	if err != nil {
		return nil, err
	}
	ok, err := param.check()
	if err != nil {
		return nil, err
	}
	// NOTICE: 如果已经停止 pia 戏，直接返回成功
	if !ok {
		return param.resp(), nil
	}
	err = param.stop()
	if err != nil {
		return nil, err
	}
	param.broadcast()
	return param.resp(), nil
}
