package chatroom

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionChatroomList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "/", true, nil)
	_, err := ActionChatroomList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "/?room_ids=18113499,122340", true, nil)
	r, err := ActionChatroomList(c)
	require.NoError(err)
	assert.Len(r.([]*room.Simple), 2)
}
