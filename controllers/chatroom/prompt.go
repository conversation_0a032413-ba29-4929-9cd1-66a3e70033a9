package chatroom

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	promptActionStartLive        = iota + 1 // 直播广场界面或 tab 中的开始直播
	promptActionOpenLiveWithInfo            // 设置直播间信息的开始直播
	promptActionEnterLiveBackend            // 进入主播直播后台
)

// prompt type
const (
	promptTypeGuildAgreement = "guild_agreement"
	promptTypeLiveAgreement  = "live_agreement"
	promptTypeDisableOpen    = "disable_open"
	promptTypeCommon         = "common_prompt"
)

type promptInfo struct {
	Type string `json:"type"`
	PromptContent

	GuildName string `json:"guild_name,omitempty"`
	UserName  string `json:"username,omitempty"`
}

// PromptContent 弹窗内容
type PromptContent struct {
	Title       string `json:"title"`
	ContentHTML string `json:"content_html"`
}

type promptParams struct {
	CatalogID int `json:"catalog_id" form:"catalog_id"`
	Action    int `json:"action" form:"action"`

	userID   int64
	userName string
	// field       string
	// catalogName string
	resp []*promptInfo

	la *liveaddendum.LiveAddendum
}

// ActionPromptCheck 获取提示弹窗内容
/**
 * @api {get} /api/v2/chatroom/prompt/check 获取提示弹窗内容
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [catalog_id] 直播分区 ID
 * @apiParam {number=1,2,3} action 触发弹窗的操作 1: 进入直播间开播设置页面 2: 点击设置直播间信息的开始直播 3：进入直播管理后台
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response 进入直播间开播设置页面:
 *   {
 *     "code": 0,
 *     "info": [ // 确认过的协议不返回
 *       {
 *         "type": "live_agreement",
 *         "title": "主播入驻协议",
 *         "content_html": "<p><b>主播入驻协议。</b></p>"
 *       },
 *       {
 *         "type": "guild_agreement",
 *         "guild_name": "三体会",
 *         "username": "小喵喵"
 *         "title": "公会入驻协议",
 *         "content_html": "<p><b>公会入驻协议。</b></p>"
 *       },
 *     ]
 *   }
 *
 * @apiSuccessExample {json} Success-Response 点击设置直播间信息的开始直播的友情提示:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "type": "common_prompt", // 值为 disable_open 时, 后面的弹窗不显示, 且不进行开播操作
 *         "title": "友情提示",
 *         "content_html": "<p><b>分区弹窗。</b></p>"
 *       }
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500, 100010002
 * @apiError (500) {String} info 服务器内部错误, 数据库错误
 */
func ActionPromptCheck(c *handler.Context) (handler.ActionResponse, error) {
	p := promptParams{userID: c.UserID(), userName: c.User().Username}
	err := c.Bind(&p)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	p.resp = []*promptInfo{}
	p.la, err = liveaddendum.Find(p.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.la == nil {
		// 找不到认为未同意协议
		p.la = new(liveaddendum.LiveAddendum)
	}
	switch p.Action {
	case promptActionStartLive, promptActionEnterLiveBackend:
		if err := p.getLiveAgreement(); err != nil {
			return nil, err
		}
		if err := p.getGuildAgreement(); err != nil {
			return nil, err
		}
	case promptActionOpenLiveWithInfo:
		p.GetOpenPrompt()
	default:
		return nil, actionerrors.ErrParams
	}

	return p.resp, nil
}

func getDisableOpenPrompt() *promptInfo {
	if config.Conf.Params.DisableOpen.Title == "" && config.Conf.Params.DisableOpen.ContentHTML == "" {
		return nil
	}

	return &promptInfo{
		Type: promptTypeDisableOpen,
		PromptContent: PromptContent{
			Title:       config.Conf.Params.DisableOpen.Title,
			ContentHTML: config.Conf.Params.DisableOpen.ContentHTML,
		},
	}
}

func (p *promptParams) getLiveAgreement() error {
	if config.Conf.Params.LiveAgreement.Title == "" && config.Conf.Params.LiveAgreement.ContentHTML == "" {
		return nil
	}

	info := promptInfo{
		Type: promptTypeLiveAgreement,
		PromptContent: PromptContent{
			Title:       config.Conf.Params.LiveAgreement.Title,
			ContentHTML: config.Conf.Params.LiveAgreement.ContentHTML,
		},
	}
	if p.la.IsAgreed.IsSet(liveaddendum.AgreeLiveAgreement) {
		return nil
	}

	p.resp = append(p.resp, &info)
	return nil
}

func (p *promptParams) getGuildAgreement() error {
	if config.Conf.Params.GuildAgreement.Title == "" && config.Conf.Params.GuildAgreement.ContentHTML == "" {
		return nil
	}

	// 获取主播所属公会信息
	lc, err := guildNameByLiveID(p.userID)
	if err != nil {
		return err
	}
	if lc == nil {
		return nil
	}

	if p.la.IsAgreed.IsSet(liveaddendum.AgreeGuildAgreement) {
		return nil
	}

	var info promptInfo
	info.Title = config.Conf.Params.GuildAgreement.Title
	info.UserName = p.userName
	info.GuildName = lc.GuildName
	info.ContentHTML = config.Conf.Params.GuildAgreement.ContentHTML
	info.Type = promptTypeGuildAgreement

	p.resp = append(p.resp, &info)
	return nil
}

func (p *promptParams) GetOpenPrompt() {
	// 配置项设置禁止开播内容后，只返回禁止开播弹窗
	if disableOpen := getDisableOpenPrompt(); disableOpen != nil {
		p.resp = append(p.resp, disableOpen)
		return
	}
}

type paramsConfirmPrompt struct {
	Agree int    `json:"agree" form:"agree"`
	Type  string `json:"type" form:"type"`
}

// ActionPromptConfirm 确认弹窗内容
/**
 * @api {post} /api/v2/chatroom/prompt/confirm 确认弹窗内容
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {number=1} agree 同意协议, 1：同意
 * @apiParam {string="live_agreement","guild_agreement"} type 确认弹窗类型
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (404) {Number} code 500050001
 * @apiError (404) {String} info 公会不存在
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionPromptConfirm(c *handler.Context) (handler.ActionResponse, error) {
	var params paramsConfirmPrompt
	err := c.Bind(&params)
	if err != nil || params.Agree != liveaddendum.Agreed {
		return nil, actionerrors.ErrParams
	}

	switch params.Type {
	case promptTypeGuildAgreement:
		err := params.confirmGuildAgreement(c.UserID())
		if err != nil {
			return nil, err
		}
		return true, nil
	case promptTypeLiveAgreement:
		err := params.confirmLiveAgreement(c.UserID())
		if err != nil {
			return nil, err
		}
		return true, nil
	default:
		return nil, actionerrors.ErrParams
	}
}

func (p *paramsConfirmPrompt) confirmLiveAgreement(userID int64) error {
	err := liveaddendum.Agree(userID, liveaddendum.AgreeLiveAgreement)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *paramsConfirmPrompt) confirmGuildAgreement(userID int64) error {
	_, err := guildNameByLiveID(userID)
	if err != nil {
		return err
	}
	err = liveaddendum.Agree(userID, liveaddendum.AgreeGuildAgreement)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func guildNameByLiveID(live int64) (*livecontract.LiveContract, error) {
	lc, err := livecontract.FindInContractingByLiveID(live, 0, "guild_name")
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return lc, nil
}
