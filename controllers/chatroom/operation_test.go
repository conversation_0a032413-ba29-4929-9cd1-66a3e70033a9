package chatroom

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/cache/usersession"
	"github.com/MiaoSiLa/live-service/models/livedb/livenewuserrewardrecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/guest"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/redis/rankpoint"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func newPostC(withUser bool, uri string, body interface{}) *handler.Context {
	if body != nil {
		return handler.NewTestContext(http.MethodPost, uri, withUser, body)
	}
	return handler.NewTestContext(http.MethodPost, uri, withUser, uri)
}

func clearAddCatFood(t *testing.T, userID int64) {
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := useritems.Collection()
	bod := util.BeginningOfDay(goutil.TimeNow())

	_, err := col.DeleteMany(ctx, bson.M{
		"user_id":    userID,
		"gift_id":    bson.M{"$in": []int64{useritems.GiftIDCatFood, useritems.GiftIDCatCanFood}},
		"start_time": bson.M{"$gte": bod.Unix()},
	})
	require.NoError(err)
	key := keys.KeyUsersAwardGiftDaily2.Format(bod.Format(util.TimeFormatYMDWithNoSpace), userID)
	require.NoError(service.Redis.Del(key).Err())
}

func TestNewOnlineParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := newOnlineParam(newPostC(false, "/online", map[string]int{"room_id": -123}))
	assert.Equal(actionerrors.ErrParams, err)
	// 第一次
	param, err := newOnlineParam(newPostC(false, "/online",
		map[string]int{"room_id": 1234, "counter": 1}))
	require.NoError(err)
	assert.Equal(int64(1234), param.RoomID)
	// 一般情况
	c := newPostC(false, "/online?room_id=123&counter=2", nil)
	c.Request().AddCookie(&http.Cookie{Name: "equip_id", Value: "test-equip-id"})
	c.Request().RemoteAddr = "127.0.0.1:0"
	c.Request().AddCookie(&http.Cookie{Name: "buvid", Value: "123"})
	c.Equip().FromApp = true
	param, err = newOnlineParam(c)
	require.NoError(err)
	assert.Equal(int64(123), param.RoomID)
	assert.Equal(2, param.Counter)
	assert.NotNil(param.c)
	assert.Equal("127.0.0.1", param.clientIP)
	assert.Equal("test-equip-id", param.equipID)
	buvid := "123"
	assert.Equal(&buvid, param.equip.BUVID)
	assert.NotNil(param.equip.EquipID)
	assert.NotNil(param.equip.Version)
	// 离开
	param, err = newOnlineParam(newPostC(false, "/online?room_id=123&counter=-1", nil))
	require.NoError(err)
	assert.Equal(int64(123), param.RoomID)
	assert.Equal(-1, param.Counter)
	assert.NotNil(param.c)

	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(param, "room_id", "counter", "status")
	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(param, "room_id", "counter", "status")
}

// TestOnlineCheckUser 和 TestActonOnline 共用
var sessionKey = "testsessionKey"

func TestOnlineCheckUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	cacheKey := config.Conf.HTTP.SessionPrefix + sessionKey
	// 测试注册用户
	param := onlineParam{c: handler.NewTestContext("POST", "/online", true, nil)}
	require.NoError(param.checkUser())
	assert.True(param.isUser)
	assert.Equal("test12", param.accID)
	// 测试 guest == nil
	param.c = handler.NewTestContext("POST", "/online", false, nil)
	param.c.C.Request.AddCookie(&http.Cookie{
		Name: config.Conf.HTTP.SessionCookieName, Value: sessionKey,
	})
	assert.Equal(actionerrors.ErrUnloggedUser, param.checkUser())
	// 测试游客
	us := usersession.UserSession{
		Guest: &guest.Guest{
			AccID: "test-acc-id",
		},
	}
	b, _ := json.Marshal(us)
	require.NoError(service.LRURedis.Set(cacheKey, string(b), time.Minute).Err())
	require.NoError(param.checkUser())
	assert.Equal("test-acc-id", param.accID)
}

func TestOnlineCheckRoom(t *testing.T) {
	assert := assert.New(t)
	param := onlineParam{RoomID: 9999999}
	assert.Equal(actionerrors.ErrCannotFindRoom, param.checkRoom())
	param.RoomID = room.TestLimitedRoomID
	assert.Equal(actionerrors.ErrCannotFindRoom, param.checkRoom(), "受限房间")
	param.RoomID = bannedRoomID
	assert.Equal(actionerrors.ErrBannedRoom, param.checkRoom())
	param.RoomID = openingRoomID
	assert.NoError(param.checkRoom())
}

func TestOnlineUpdateOnline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	// NOTICE: param.addOnlineTime 所执行的函数均被各自的单元测试覆盖了，这里只手动看一下覆盖情况

	cleanup := mockMRPC()
	defer cleanup()
	mockAction.Action = func(c *handler.Context) (handler.ActionResponse, error) {
		return nil, nil
	}
	beforeTestMilli := goutil.NewTimeUnixMilli(goutil.TimeNow())
	param := onlineParam{RoomID: 123456, isUser: true, userID: 123, accID: "123", Counter: 1, accessTime: goutil.TimeNow()}
	param.room = new(room.Room)
	param.room.RoomID = param.RoomID
	param.room.CreatorID = 147
	param.c = handler.NewTestContext("POST", "/online", true, nil)
	cacheKey := livelistenlogs.KeyRoomOnline(param.RoomID)
	require.NoError(service.Redis.Del(cacheKey).Err())
	resp, err := param.updateOnline()
	require.NoError(err)
	time.Sleep(100 * time.Millisecond)
	assert.Greater(int64(service.Redis.TTL(cacheKey).Val()), int64(time.Minute*14))
	r, err := service.Redis.HGet(cacheKey, "123").Result()
	require.NoError(err)
	var ot [3]livelistenlogs.OnlineTime // 0：首次，1：缓存，2：离开
	require.NoError(json.Unmarshal([]byte(r), &ot[0]))
	assert.Equal(ot[0].StartTime, ot[0].CurrentTime)
	assert.Equal(ot[0].CurrentTime, resp.LastTime)
	assert.GreaterOrEqual(int64(ot[0].StartTime), int64(beforeTestMilli))
	assert.Equal(param.userID, resp.UserID)
	// 测试 lastOnline.StartTime != 0 的情况
	resp, err = param.updateOnline()
	require.NoError(err)
	r, err = service.Redis.HGet(cacheKey, "123").Result()
	require.NoError(err)
	require.NoError(json.Unmarshal([]byte(r), &ot[1]))
	assert.Equal(ot[0], ot[1]) // 没更新 redis
	assert.Equal(resp.LastTime, ot[1].CurrentTime)
	// 看看游客能不能使用
	param.isUser = false
	// 未修改 AccID 是为了共用之前存进 redis 的值
	resp, err = param.updateOnline()
	require.NoError(err)
	r, err = service.Redis.HGet(cacheKey, "123").Result()
	require.NoError(err)
	require.NoError(json.Unmarshal([]byte(r), &ot[1]))
	assert.Equal(resp.LastTime, ot[1].CurrentTime)
	assert.Equal(ot[0], ot[1])
	// 离开房间
	param.Counter = -1
	resp, err = param.updateOnline()
	require.NoError(err)
	r, err = service.Redis.HGet(cacheKey, "123").Result()
	require.NoError(err)
	assert.Equal(onlineResp{UserID: param.userID, LastTime: ot[1].CurrentTime, NextTime: ot[1].CurrentTime + onlineInRoomTimeInterval}, *resp)
	require.NoError(json.Unmarshal([]byte(r), &ot[2]))
	assert.GreaterOrEqual(int64(ot[2].LeaveTime), int64(beforeTestMilli))
	assert.NotZero(ot[2].CurrentTime)

	param.Status = onlineStatusBackend
	resp, err = param.updateOnline()
	require.NoError(err)
	assert.Equal(onlineResp{UserID: param.userID, LastTime: ot[1].CurrentTime, NextTime: ot[1].CurrentTime + onlineBackendTimeInterval}, *resp)

	param.Status = onlineStatusFinishedLiveTask
	resp, err = param.updateOnline()
	require.NoError(err)
	assert.Equal(onlineResp{UserID: param.userID, LastTime: ot[1].CurrentTime, NextTime: ot[1].CurrentTime + onlineInRoomTimeInterval}, *resp)
}

func TestOnlineAddListenLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := onlineParam{
		userID: 12,
		accID:  "TestOnlineAddListenLog",
		RoomID: 22489473,
		room:   new(room.Room),
		// c:        handler.NewTestContext("POST", "/online", false, nil),
		clientIP: "127.0.0.1",
		equipID:  "test-equip-id",
		equip: livelistenlogs.Equip{
			IP: "127.0.0.1",
		},
		accessTime: goutil.TimeNow(),
	}
	param.room.CatalogID = 102
	ot := livelistenlogs.OnlineTime{
		StartTime:   1,
		CurrentTime: 9 * 1e12,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livelistenlogs.Collection()
	_, err := col.DeleteMany(ctx, bson.M{"accid": param.accID, "room_id": param.RoomID, "start_time": ot.StartTime})
	require.NoError(err)
	beforeSave := goutil.TimeNow().Add(-time.Second)
	assert.NotPanics(func() { param.addListenLog(ot.StartTime) })
	var l livelistenlogs.LiveListenLog
	require.NoError(col.FindOne(ctx,
		bson.M{"user_id": param.userID, "accid": param.accID},
		options.FindOne().SetSort(bson.M{"_id": -1}),
	).Decode(&l))
	assert.Equal(int64(102), l.CatalogID)
	assert.True(beforeSave.Before(l.CreateTime))
	assert.NotZero(l.StartMedalPoint)
	assert.Equal(l.CreateTime, l.ModifiedTime)
	assert.Equal(int64(22489473), l.RoomID)
	assert.Equal([]string{"127.0.0.1"}, l.IPs)
	assert.Equal([]string{"test-equip-id"}, l.EquipIDs)
	assert.Equal(goutil.TimeUnixMilli(1), l.StartTime)
	assert.Equal(goutil.NewTimeUnixMilli(param.accessTime), l.LastTime)
	assert.Equal(int64(goutil.NewTimeUnixMilli(param.accessTime)-1), l.Duration)
	require.Len(l.Equips, 1)
	assert.Equal(param.equip, *l.Equips[0])

	ot.CurrentTime = 100
	param.clientIP = "*********"
	param.equipID = "test-equip-id2"
	param.equip.EquipID = &param.equipID
	param.accessTime = goutil.TimeNow()
	assert.NotPanics(func() { param.addListenLog(ot.StartTime) })
	require.NoError(col.FindOne(ctx,
		bson.M{"_id": l.OID},
		options.FindOne().SetSort(bson.M{"_id": -1}),
	).Decode(&l))
	assert.Equal(goutil.NewTimeUnixMilli(param.accessTime), l.LastTime)
	assert.Equal([]string{"127.0.0.1", "*********"}, l.IPs)
	assert.Equal([]string{"test-equip-id", "test-equip-id2"}, l.EquipIDs)
	assert.NotEqual(l.CreateTime, l.ModifiedTime)
	assert.NotZero(l.LastMedalPoint)
	require.Len(l.Equips, 2)
	assert.Equal(param.equip, *l.Equips[1])

	// 测试 addToSet
	assert.NotPanics(func() { param.addListenLog(ot.StartTime) })
	require.NoError(col.FindOne(ctx,
		bson.M{"_id": l.OID},
		options.FindOne().SetSort(bson.M{"_id": -1}),
	).Decode(&l))
	assert.Equal([]string{"127.0.0.1", "*********"}, l.IPs)
	assert.Equal([]string{"test-equip-id", "test-equip-id2"}, l.EquipIDs)
	assert.Len(l.Equips, 2)
}

func TestOnlineParam_addClosedRoomListenLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	param := onlineParam{
		userID:   12,
		accID:    "TestOnlineAddListenLog",
		RoomID:   22489473,
		room:     new(room.Room),
		clientIP: "127.0.0.1",
		equipID:  "test-equip-id",
		equip: livelistenlogs.Equip{
			IP: "127.0.0.1",
		},
		accessTime: goutil.TimeNow(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livelistenlogs.Collection()
	_, err := col.DeleteMany(ctx, bson.M{
		"accid":       param.accID,
		"room_id":     param.RoomID,
		"open_log_id": bson.M{"$exists": false},
	})
	require.NoError(err)

	param.addClosedRoomListenLog()

	var l livelistenlogs.LiveListenLog
	err = col.FindOne(ctx, bson.M{
		"accid":      param.accID,
		"room_id":    param.RoomID,
		"start_time": goutil.NewTimeUnixMilli(now),
	}).Decode(&l)
	require.NoError(err)
	assert.Nil(l.OpenLogID)
}

// 依赖 TestOnlineCheckUser 添加的 memcache
func TestActonOnline(t *testing.T) {
	assert := assert.New(t)

	cleanup := mockMRPC()
	defer cleanup()
	mockAction.Action = func(c *handler.Context) (handler.ActionResponse, error) {
		return nil, nil
	}
	_, err := ActionOnline(newPostC(false, "/online", nil))
	assert.Equal(actionerrors.ErrParams, err)
	_, err = ActionOnline(newPostC(false, "/online?room_id=123&counter=1", nil))
	assert.Equal(actionerrors.ErrUnloggedUser, err)
	c := newPostC(false, "/online?room_id=123&counter=1", nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name: config.Conf.HTTP.SessionCookieName, Value: sessionKey,
	})
	_, err = ActionOnline(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
	c = newPostC(false, "/online", map[string]int64{"room_id": openingRoomID, "counter": 1})
	c.C.Request.AddCookie(&http.Cookie{
		Name: config.Conf.HTTP.SessionCookieName, Value: sessionKey,
	})
	_, err = ActionOnline(c)
	assert.NoError(err)
}

func TestOnlineParam_addNewUserPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserIDs := []int64{9074508, 9074509, 9074510}
	testBUVID := "XX31182368CF7A3BE1EBF6999B713"

	err := livenewuserrewardrecord.DB().Delete(livenewuserrewardrecord.LiveNewUserRewardRecord{},
		"user_id IN (?)", testUserIDs).Error
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livelistenlogs.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testUserIDs}})
	require.NoError(err)
	_, err = livelistenlogs.Collection().InsertOne(ctx,
		&livelistenlogs.LiveListenLog{
			UserID: testUserIDs[1], Duration: livelistenlogs.NewUserRewardLimitDuration, EquipIDs: []string{testBUVID},
		},
	)
	require.NoError(err)

	recordKeys := make([]string, 0, len(testUserIDs))
	for _, userID := range testUserIDs {
		recordKeys = append(recordKeys, keys.LockLiveNewUserRewardRecord1.Format(userID))
	}
	rewardKey := keys.KeyParams1.Format(params.KeyReward)
	err = service.LRURedis.Del(append(recordKeys, rewardKey)...).Err()
	require.NoError(err)

	param := onlineParam{
		RoomID: 9074508,
		c:      handler.NewTestContext(http.MethodGet, "", true, nil),
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 11223344,
			},
		},
	}
	// 测试没有用户 ID
	assert.Nil(param.addNewUserPrize())

	// 测试非第一次连接
	param.userID = testUserIDs[0]
	param.Counter = 2
	assert.Nil(param.addNewUserPrize())

	// 测试非 App 访问
	param.Counter = 1
	param.c.Equip().FromApp = false
	assert.Nil(param.addNewUserPrize())

	// BUVID 为空
	param.c.Equip().FromApp = true
	param.c.Equip().OS = goutil.IOS
	assert.Nil(param.addNewUserPrize())

	// 测试不是 App 但是有 BUVID 不返回
	param.c.Equip().FromApp = false
	param.c.Equip().BUVID = testBUVID
	assert.Nil(param.addNewUserPrize())

	// 测试新客，版本号不满足
	param.c.Equip().FromApp = true
	param.c.Equip().AppVersion = "6.1.1"
	assert.Nil(param.addNewUserPrize())

	// 测试新客，版本号满足
	param.c.Equip().AppVersion = "6.1.2"
	require.NotNil(param.addNewUserPrize())
	exists, err := service.LRURedis.Exists(recordKeys[0]).Result()
	require.NoError(err)
	assert.NotZero(exists)
	userRecord, err := livenewuserrewardrecord.FindUserRewardRecord(param.userID)
	require.NoError(err)
	require.NotNil(userRecord)
	assert.NotZero(userRecord.RewardID)
	assert.EqualValues(livenewuserrewardrecord.StatusGranted, userRecord.Status)

	// 测试没有配置新客奖励 ID，不返回
	rewardParam := params.Reward{
		Key:                 params.KeyReward,
		LiveNewUserRewardID: 0,
	}
	rewardByte, err := json.Marshal(rewardParam)
	require.NoError(err)
	err = service.LRURedis.Set(rewardKey, string(rewardByte), time.Minute).Err()
	require.NoError(err)
	assert.Nil(param.addNewUserPrize())

	err = service.LRURedis.Del(rewardKey).Err()
	require.NoError(err)

	// 有缓存记录不返回
	assert.Nil(param.addNewUserPrize())

	// 测试无缓存，但是领取过不返回
	err = service.LRURedis.Del(recordKeys[0]).Err()
	require.NoError(err)
	assert.Nil(param.addNewUserPrize())

	// 测试无缓存，收听超过新客户要求
	param.userID = testUserIDs[1]
	assert.Nil(param.addNewUserPrize())
	exists, err = service.LRURedis.Exists(recordKeys[1]).Result()
	require.NoError(err)
	assert.NotZero(exists)
	userRecord, err = livenewuserrewardrecord.FindUserRewardRecord(param.userID)
	require.NoError(err)
	require.NotNil(userRecord)
	assert.NotZero(userRecord.RewardID)
	assert.EqualValues(livenewuserrewardrecord.StatusInvalid, userRecord.Status)

	// 测试无缓存，但是有记录不返回
	err = service.LRURedis.Del(recordKeys[1]).Err()
	require.NoError(err)
	assert.Nil(param.addNewUserPrize())

	// 测试找不到背包礼物，有记录不返回
	rewardParam = params.Reward{
		Key:                 params.KeyReward,
		LiveNewUserRewardID: 16000,
	}
	rewardByte, err = json.Marshal(rewardParam)
	require.NoError(err)
	err = service.LRURedis.Set(rewardKey, string(rewardByte), time.Minute).Err()
	require.NoError(err)
	param.userID = testUserIDs[2]
	assert.Nil(param.addNewUserPrize())
	exists, err = service.LRURedis.Exists(recordKeys[2]).Result()
	require.NoError(err)
	assert.NotZero(exists)
	userRecord, err = livenewuserrewardrecord.FindUserRewardRecord(param.userID)
	require.NoError(err)
	require.NotNil(userRecord)
	assert.Zero(userRecord.RewardID)
	assert.EqualValues(livenewuserrewardrecord.StatusGranted, userRecord.Status)
}

func TestOnlineParam_newUserRewardSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := onlineParam{
		RoomID: 547894,
		userID: 9074509,
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 9074511,
			},
		},
	}
	item, err := param.newUserRewardSend(2000)
	require.NoError(err)
	assert.Nil(item)

	item, err = param.newUserRewardSend(20)
	require.NoError(err)
	require.NotNil(item)
	assert.EqualValues(40047, item.GiftID)

	item, err = param.newUserRewardSend(16000)
	require.NoError(err)
	assert.Nil(item)
}

func TestActionClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URLRoomOnClose, func(any) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()
	c := handler.NewTestContext("POST", "/close", true, "room_id=-10")
	_, err := ActionClose(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", "/close", true, "room_id=123456")
	_, err = ActionClose(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
	oid := primitive.NewObjectID()
	r, err := room.Update(11223344, bson.M{"status.open": room.StatusOpenFalse, "status.open_log_id": oid})
	require.NoError(err)
	require.NotNil(r)
	c = handler.NewTestContext("POST", "/close", true, fmt.Sprintf("room_id=%d", r.RoomID))
	_, err = ActionClose(c)
	assert.Equal(actionerrors.ErrForbidden, err)
	c = handler.NewTestContext("POST", "/close", true, fmt.Sprintf("room_id=%d", r.RoomID))
	c.User().ID = r.CreatorID
	_, err = ActionClose(c)
	assert.Equal(actionerrors.ErrClosedRoom, err)
	_, err = room.Update(11223344, bson.M{"status.open": room.StatusOpenTrue, "status.open_log_id": oid})
	require.NoError(err)
	c = handler.NewTestContext("POST", "/close", true, fmt.Sprintf("room_id=%d", r.RoomID))
	c.User().ID = r.CreatorID
	res, err := ActionClose(c)
	require.NoError(err)
	resp := res.(closeResp)
	assert.NotEmpty(resp)
}

func TestCloseNotifyRecommend(t *testing.T) {
	assert := assert.New(t)

	tRoom := &room.Room{}
	tRoom.Config = &room.Config{
		Popularity: 0,
	}
	// 测试普通主播
	recommend := CloseNotifyRecommend(tRoom, "")
	assert.Nil(recommend)
	// 测试只靠热度推荐
	tRoom.Config.Popularity = 1
	recommend = CloseNotifyRecommend(tRoom, "")
	assert.Len(recommend, recommendLimit)
	// 测试正常推荐
	tRoom.CatalogID = 115
	recommend = CloseNotifyRecommend(tRoom, "")
	assert.Len(recommend, recommendLimit)
}

func TestOnlineAddCatFoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := onlineParam{
		isUser:  true,
		Counter: onlineCounterEnter,
		userID:  13,
		c:       handler.NewTestContext("POST", "/online", true, nil),
	}

	clearAddCatFood(t, param.userID)
	bod := util.BeginningOfDay(goutil.TimeNow())

	// 添加超粉勋章
	pointParam := &livemedal.AddPointParam{
		RoomID:     20001,
		UserID:     param.userID,
		Type:       livemedal.TypeSuperFanMedalPoint,
		ExpireTime: goutil.TimeNow().Unix() + 10000,
		PointAdd:   1000,
	}
	_, err := pointParam.AddPoint()
	require.NoError(err)

	// 领取猫粮
	param.addCatFoods()
	param.addCatFoods()
	filter := bson.M{
		"user_id":    param.userID,
		"gift_id":    bson.M{"$in": []int64{useritems.GiftIDCatFood, useritems.GiftIDCatCanFood}},
		"start_time": bson.M{"$gte": bod.Unix()},
	}
	userItems, err := useritems.Find(filter, nil)
	require.NoError(err)
	require.Len(userItems, 2)
	m, ok := goutil.ToMap(userItems, "GiftID").(map[int64]*useritems.UserItem)
	require.True(ok)
	et := time.Unix(goutil.TimeNow().Unix()+7*util.SecondOneDay, 0)
	et = util.BeginningOfWeek(et)
	assert.Equal(et.Unix(), m[useritems.GiftIDCatFood].EndTime)
	assert.Equal(et.Unix(), m[useritems.GiftIDCatCanFood].EndTime)

	clearAddCatFood(t, param.userID)
	param.Counter = onlineCounterEnterClosedRoom
	// 进入关播直播间加猫粮猫罐头
	param.addCatFoods()
	userItems, err = useritems.Find(filter, nil)
	require.NoError(err)
	assert.NotNil(userItems)
}

func TestOnlineAddViewRankPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	roomID := int64(123)
	creatorID := int64(123)
	before := usersrank.Info{
		UserID: creatorID,
	}
	_, err := usersrank.Find(usersrank.TypeHour, now, &before)
	require.NoError(err)

	key := rankpoint.KeyRoomsViewDaily(roomID)
	pipe := service.Redis.TxPipeline()
	pipe.Del(key)
	pipe.SAdd(key, 1, 2, 3, 4, 5, 6, 7, 8, 9)
	pipe.Expire(key, 36*time.Hour)
	_, err = pipe.Exec()
	require.NoError(err)

	param := onlineParam{
		userID: 12,
		room:   &room.Room{},
	}
	param.room.RoomID = roomID
	param.room.CreatorID = creatorID
	param.addViewRankPoint()

	after := usersrank.Info{
		UserID: creatorID,
	}
	_, err = usersrank.Find(usersrank.TypeHour, now, &after)
	require.NoError(err)
	assert.Equal(before.Revenue+1, after.Revenue)
}

func TestActionShare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	v := url.Values{}
	v.Set("room_id", strconv.FormatInt(roomID, 10))
	v.Set("type", strconv.FormatInt(1, 10))

	c := handler.NewTestContext("POST", "/share", true, v)
	r, err := ActionShare(c)
	require.NoError(err)
	assert.NotNil(r)

	v.Set("room_id", strconv.FormatInt(room.TestLimitedRoomID, 10))
	c = handler.NewTestContext("POST", "/share", true, v)
	r, err = ActionShare(c)
	require.NoError(err)
	assert.NotNil(r)

	// 测试关播的直播间
	v.Set("room_id", strconv.FormatInt(20210609, 10))
	c = handler.NewTestContext("POST", "/share", true, v)
	r, err = ActionShare(c)
	require.NoError(err)
	assert.NotNil(r)
}

func TestAddUserShareCountDaily(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	roomID := int64(22489473)
	require.NoError(service.Redis.HDel(rankpoint.KeyRoomsRankPointDaily(userID),
		rankpoint.FieldShareLock1.Format(roomID)).Err())
	r, err := room.Find(roomID)
	require.NoError(err)
	require.NotNil(r)

	// 没有勋章的情况下
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.FindOne(bson.M{"user_id": 22334455, "room_id": roomID}, nil)
	require.NoError(err)
	require.NoError(addUserShareCountDaily(r, int64(22334455)))
	// 有勋章的情况下
	a := livemedal.AddPointParam{
		RoomOID:   r.OID,
		RoomID:    r.RoomID,
		CreatorID: r.CreatorID,
		MedalName: "test",
		UserID:    userID,
		PointAdd:  100,
	}
	_, err = a.AddPoint()
	require.NoError(err)
	// 今日首次分享
	liveUsersCol := service.MongoDB.Collection("live_users")
	_, err = liveUsersCol.UpdateOne(ctx, bson.M{"user_id": userID, "room_id": roomID}, bson.M{"$set": bson.M{
		"t_status_share": livemedal.ShareStatusPending, "t_time_share": time.Unix(0, 0),
	}})
	require.NoError(err)
	require.NoError(addUserShareCountDaily(r, userID))
	l2, err := livemedal.FindLiveUserShare(userID, r.OID)
	require.NoError(err)
	assert.Equal(livemedal.ShareStatusShared, l2.StatusShare)
	now := goutil.TimeNow()
	assert.True(l2.TimeShare.YearDay() == now.YearDay())
	// 今日第二次分享
	require.NoError(addUserShareCountDaily(r, userID))
	l3, err := livemedal.FindLiveUserShare(userID, r.OID)
	require.NoError(err)
	assert.Equal(l2.TimeShare, l3.TimeShare)
}

func TestOnlineParam_questActivityListenRoomDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URILiveReward, func(input interface{}) (output interface{}, err error) {
		called = true
		return handler.M{"success": true}, nil
	})
	defer cancel()

	key := keys.LocalKeyActivatedQuests1.Format(quests.QuestTypeRoomListenDuration)
	service.Cache10s.Delete(key)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	testOID, _ := primitive.ObjectIDFromHex("6613b4bc29f21e142e80a55c")
	testQuest := &quests.Quest{
		OID:       testOID,
		Type:      quests.QuestTypeRoomListenDuration,
		RoomID:    18113499,
		RewardID:  8,
		Duration:  10,
		StartTime: now.Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
	}

	qCol := quests.CollectionQuests()
	err := qCol.FindOneAndUpdate(ctx, bson.M{"_id": testOID},
		bson.M{"$set": testQuest},
		options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)
	uqCol := quests.CollectionUserQuests()
	_, err = uqCol.DeleteOne(ctx, bson.M{"_quest_id": testOID, "user_id": 9074509})
	require.NoError(err)

	param := onlineParam{
		RoomID:     testQuest.RoomID,
		userID:     9074509,
		accessTime: now.Add(10 * time.Second),
		room: &room.Room{
			Helper: room.Helper{
				CreatorID: 18113499,
			},
		},
	}
	param.questActivityListenRoomDuration(goutil.NewTimeUnixMilli(now), goutil.NewTimeUnixMilli(now))
	err = uqCol.FindOne(ctx, bson.M{
		"_quest_id": testOID,
		"user_id":   param.userID,
	}).Err()
	require.NoError(err)
	assert.True(called)
}
