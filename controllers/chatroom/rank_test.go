package chatroom

import (
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRankRespTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(rankResp{}, "Datas", "myrank", "pagination")
	kc.Check(rankResp2{}, "Datas", "myrank", "refresh", "room_revenue")
}

func TestActionRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/rank/123?type=1", true, nil)
	c.C.Params = append(c.C.Params, gin.Param{Key: "roomID", Value: roomIDStr})
	r, err := ActionRank(c)
	assert.NoError(err)
	require.NotNil(r)
	_, ok := r.(rankResp2)
	assert.True(ok)

	// TODO: 新接口的榜单需要详细测试
	c = handler.NewTestContext(http.MethodGet, "/rank/123?type=2", true, nil)
	c.C.Params = append(c.C.Params, gin.Param{Key: "roomID", Value: roomIDStr})
	r, err = ActionRank(c)
	assert.NoError(err)
	require.NotNil(r)
	resp := r.(rankResp2)
	assert.NotZero(resp.Refresh)
}

func TestCheckRankInvisible(t *testing.T) {
	assert := assert.New(t)

	data1 := []*liverevenues.RankRevenue{{
		RankInvisible: true,
		Simple:        &liveuser.Simple{UID: 10},
	}, {
		RankInvisible: true,
		Simple:        &liveuser.Simple{UID: 11},
	},
	}
	data2 := []*roomsrank.Info{{
		RankInvisible: true,
		Simple:        &liveuser.Simple{UID: 10},
	}, {
		RankInvisible: true,
		Simple:        &liveuser.Simple{UID: 11},
	},
	}
	// data3 在 fans_test.go
	checkRankInvisible(10, 10, false, data1, data2, nil)
	assert.Empty(data1[0].Username + data1[1].Username)
	assert.Empty(data2[0].Username + data2[1].Username)
	checkRankInvisible(1, 10, false, data1, data2, nil)
	assert.Equal([]string{"", "神秘人"}, []string{data1[0].Username, data1[1].Username})
	assert.Equal([]string{"", "神秘人"}, []string{data2[0].Username, data2[1].Username})
}

func TestRankLoad(t *testing.T) {
	assert := assert.New(t)
	param := rankParam{C: handler.CreateTestContext(true)}
	param.C.C.Request, _ = http.NewRequest("GET", "/rank/124?p=1&page_size=1&type=234", nil)
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: "aaa"}}
	// 房间号错误
	assert.Equal(actionerrors.ErrCannotFindRoom, param.load())
	// 房间不存在
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: "123"}}
	assert.Equal(actionerrors.ErrCannotFindRoom, param.load())
	assert.Equal(int64(123), param.roomID)
	// 正常读取
	param.C.C.Params = []gin.Param{{Key: "roomID", Value: roomIDStr}}
	assert.NoError(param.load())
	assert.Equal(int64(1), param.p)
	assert.Equal(int64(1), param.pageSize)
	assert.Zero(param.rankType)
	assert.NotNil(param.user)
}

func TestRankBuildTypeTotalResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(liverevenues.CollectionName)

	param := rankParam{
		user:      new(user.User),
		p:         1,
		pageSize:  goutil.DefaultPageSize,
		roomID:    654321,
		C:         handler.NewTestContext(http.MethodGet, "/654321", true, nil),
		creatorID: 12,
	}
	param.user.ID = param.creatorID
	update := bson.M{"revenue": 10}
	_, err := collection.UpdateOne(ctx, bson.M{"room_id": param.roomID, "user_id": param.user.ID},
		bson.M{"$set": update}, options.Update().SetUpsert(true))
	require.NoError(err)
	cacheKey := keys.KeyChatroomRankRevenue1.Format(param.roomID)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	// 正常查询
	res, err := param.buildTotalRank()
	require.NoError(err)
	require.Len(param.Data, 1)
	assert.Equal(param.user.ID, res.Data[0].UserID())
	assert.Equal(int64(1), param.Data[0].Rank)
	assert.Equal(int64(10), param.Data[0].Revenue)
	// 查询结果为空
	param.p = -1
	res, err = param.buildTotalRank()
	require.NoError(err)
	assert.NotNil(res.Data)
	assert.Empty(res.Data)
	// 不是主播
	param.user.ID = 13
	param.p = 1
	res, err = param.buildTotalRank()
	require.NoError(err)
	assert.Empty(res.Data)
}
