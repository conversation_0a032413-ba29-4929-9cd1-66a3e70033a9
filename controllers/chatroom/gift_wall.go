package chatroom

import (
	"fmt"
	"slices"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/livenotice"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type giftWallInfoResp struct {
	ActivatedNum            int64            `json:"activated_num"`
	TotalNum                int64            `json:"total_num"`
	EndTime                 int64            `json:"end_time"`
	Rule                    string           `json:"rule"`
	Tip                     *giftWallInfoTip `json:"tip,omitempty"`
	ActivatedGifts          []*activatedElem `json:"activated_gifts"`
	InactivatedGifts        []*activatedGift `json:"inactivated_gifts"`
	ActivatedPremiumNum     *int64           `json:"activated_premium_num,omitempty"`
	TotalPremiumNum         *int64           `json:"total_premium_num,omitempty"`
	ActivatedPremiumGifts   []*activatedElem `json:"activated_premium_gifts,omitempty"`
	InactivatedPremiumGifts []*activatedGift `json:"inactivated_premium_gifts,omitempty"`

	roomID        int64
	userID        int64
	r             *room.Room
	normalPeriod  *giftwall.Period
	premiumPeriod *giftwall.Period
}

type giftWallInfoTip struct {
	TipID   int64  `json:"tip_id"`
	Message string `json:"message"`
	OpenURL string `json:"open_url"`
}

type activatedElem struct {
	GiftID       int64              `json:"gift_id"`
	GiftName     string             `json:"gift_name"`
	GiftIconURL  string             `json:"gift_icon_url"`
	ActivatedNum int64              `json:"activated_num"`
	Promotion    int                `json:"promotion,omitempty"`
	Top1         *activatedRankElem `json:"top1"`
}

type activatedGift struct {
	GiftID       int64  `json:"gift_id"`
	GiftName     string `json:"gift_name"`
	GiftPrice    int64  `json:"gift_price"`
	GiftIconURL  string `json:"gift_icon_url"`
	TargetGiftID int64  `json:"target_gift_id"`
}

// ActionGiftWallInfo 礼物墙详情页
/**
 * @api {get} /api/v2/chatroom/giftwall/info 礼物墙详情页
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "activated_num": 1, // 已点亮数量
 *       "total_num": 20, // 本期上墙礼物总数
 *       "end_time": 1660104000, // 本期礼物墙结束时间，秒级时间戳
 *       "rule": "https://link.missevan.com/fm/gift-wall-guide",
 *       "tip": {
 *         "tip_id": 1, // 客户端根据 ID 处理用户公告的关闭
 *         "message": "温馨提示",
 *         "open_url" "http://fm.example.com/open?webview=1"
 *       },
 *       "activated_gifts": [ // 返回全部点亮礼物
 *         {
 *           "gift_id": 1, // 上墙礼物 ID
 *           "gift_name": "凤舞九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "activated_num": 1000, // 点亮次数
 *           "promotion": 1, // 达标需要变色, 0 或不下发该字段不算达标
 *           "top1": {
 *             "user_id": 10,
 *             "username": "送礼者",
 *             "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *             "rank_invisible": true // 榜单隐身中
 *           }
 *         }
 *       ],
 *       "inactivated_gifts": [ // 返回全部未点亮的礼物
 *         {
 *           "gift_id": 2, // 上墙礼物 ID
 *           "gift_name": "凤鸣九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "target_gift_id": 3 // 跳转的定位礼物 ID
 *         }
 *       ],
 *       "activated_premium_num": 1, // 甄选礼物已点亮数量，没有甄选礼物不返回该字段
 *       "total_premium_num": 20 // 本期上墙甄选礼物总数，没有甄选礼物不返回该字段
 *       "activated_premium_gifts": [ // 返回甄选点亮礼物，没有甄选礼物不返回该字段
 *         {
 *           "gift_id": 1, // 上墙礼物 ID
 *           "gift_name": "凤舞九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "activated_num": 1000, // 点亮次数
 *           "promotion": 1, // 达标需要变色, 0 或不下发该字段不算达标
 *           "top1": {
 *             "user_id": 10,
 *             "username": "送礼者",
 *             "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *             "rank_invisible": true // 榜单隐身中
 *           }
 *         }
 *       ],
 *       "inactivated_premium_gifts": [ // 返回甄选未点亮的礼物，没有甄选礼物不返回该字段
 *         {
 *           "gift_id": 2, // 上墙礼物 ID
 *           "gift_name": "凤鸣九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "target_gift_id": 3 // 跳转的定位礼物 ID
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallInfo(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newGiftWallInfoResp(c)
	if err != nil {
		return nil, err
	}
	if err = resp.buildActivatedInfo(); err != nil {
		return nil, err
	}
	resp.buildTip()
	return resp, nil
}

func newGiftWallInfoResp(c *handler.Context) (*giftWallInfoResp, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	now := goutil.TimeNow()

	// 查询普通礼物墙周期
	normalPeriod, err := giftwall.CurrentPeriodInfoByType(now, giftwall.PeriodTypeNormal)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if normalPeriod == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	// 查询甄选礼物墙周期
	premiumPeriod, err := giftwall.CurrentPeriodInfoByType(now, giftwall.PeriodTypePremium)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return &giftWallInfoResp{
		EndTime:       normalPeriod.EndTime,
		Rule:          config.Conf.Params.GiftWall.GuideURL,
		roomID:        roomID,
		userID:        c.UserID(),
		r:             r,
		normalPeriod:  normalPeriod,
		premiumPeriod: premiumPeriod,
	}, nil
}

func (resp *giftWallInfoResp) buildActivatedInfo() error {
	// 构建普通礼物墙信息
	normalInfo, err := resp.processGiftWallPeriod(resp.normalPeriod)
	if err != nil {
		return err
	}
	if normalInfo != nil {
		resp.ActivatedGifts = normalInfo.activatedGifts
		resp.InactivatedGifts = normalInfo.inactivatedGifts
		resp.ActivatedNum = normalInfo.activatedNum
		resp.TotalNum = normalInfo.totalNum
	}

	// 构建甄选礼物墙信息
	premiumInfo, err := resp.processGiftWallPeriod(resp.premiumPeriod)
	if err != nil {
		return err
	}
	if premiumInfo != nil {
		resp.ActivatedPremiumGifts = premiumInfo.activatedGifts
		resp.InactivatedPremiumGifts = premiumInfo.inactivatedGifts
		resp.ActivatedPremiumNum = &premiumInfo.activatedNum
		resp.TotalPremiumNum = &premiumInfo.totalNum
	}

	return nil
}

type giftWallPeriodInfo struct {
	activatedGifts   []*activatedElem
	inactivatedGifts []*activatedGift
	activatedNum     int64
	totalNum         int64
}

// processGiftWallPeriod 处理单个礼物墙周期的数据
func (resp *giftWallInfoResp) processGiftWallPeriod(period *giftwall.Period) (*giftWallPeriodInfo, error) {
	if period == nil {
		return nil, nil
	}
	if len(period.CreatorIDs) > 0 && !slices.Contains(period.CreatorIDs, resp.r.CreatorID) {
		return nil, nil
	}

	activatedGifts, inactivatedGifts, activatedNum, err := resp.buildGiftWallInfo(period)
	if err != nil {
		return nil, err
	}

	return &giftWallPeriodInfo{
		activatedGifts:   activatedGifts,
		inactivatedGifts: inactivatedGifts,
		activatedNum:     activatedNum,
		totalNum:         period.TotalNum(),
	}, nil
}

// buildGiftWallInfo 构建指定周期的礼物墙信息
func (resp *giftWallInfoResp) buildGiftWallInfo(period *giftwall.Period) ([]*activatedElem, []*activatedGift, int64, error) {
	records, err := giftwall.ListRecord(bson.M{"room_id": resp.roomID, "_period_id": period.OID},
		options.Find().SetSort(bson.M{"revenue": -1}))
	if err != nil {
		return nil, nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}

	gsMap, err := gift.FindGiftMapByGiftIDs(period.ShowGiftIDs)
	if err != nil {
		return nil, nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}

	activatedNum := int64(len(records))
	activatedGifts := make([]*activatedElem, 0, len(records))
	activeGiftIDs := make([]int64, 0, len(records))

	for i := range records {
		elem := &activatedElem{
			GiftID:       records[i].ShowGiftID,
			ActivatedNum: records[i].ActivatedNum,
			Promotion:    util.BoolToInt(records[i].Revenue >= giftwall.PromotionRevenueThreshold),
		}
		if g := gsMap[elem.GiftID]; g != nil {
			elem.GiftName = g.Name
			elem.GiftIconURL = g.Icon
		}
		activatedGifts = append(activatedGifts, elem)
		activeGiftIDs = append(activeGiftIDs, elem.GiftID)
	}

	// 构建 Top1 信息
	err = resp.buildTop1Info(period.OID, activatedGifts, activeGiftIDs)
	if err != nil {
		return nil, nil, 0, err
	}

	// 构建未点亮礼物信息
	inactivatedGifts, err := resp.buildInactivatedGifts(period, gsMap, activeGiftIDs)
	if err != nil {
		return nil, nil, 0, err
	}

	return activatedGifts, inactivatedGifts, activatedNum, nil
}

// buildTop1Info 构建已点亮礼物的 Top1 用户信息
func (resp *giftWallInfoResp) buildTop1Info(periodOID primitive.ObjectID, activatedGifts []*activatedElem, activeGiftIDs []int64) error {
	if len(activeGiftIDs) == 0 {
		return nil
	}

	top1Map, err := giftwall.FindUserGiftsRankTop1(periodOID, resp.roomID, activeGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	activeUserIDs := make([]int64, 0, len(activatedGifts))
	for i := range activatedGifts {
		top1, ok := top1Map[activatedGifts[i].GiftID]
		if !ok {
			continue
		}
		activeUserIDs = append(activeUserIDs, top1.UserID)
		activatedGifts[i].Top1 = &activatedRankElem{
			UserID: top1.UserID,
		}
	}

	if len(activeUserIDs) == 0 {
		return nil
	}

	users, err := mowangskuser.FindSimpleMap(activeUserIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	invisibleUsers := userstatus.RankInvisibleUsers(resp.roomID)
	icon := service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)

	for i := range activatedGifts {
		if activatedGifts[i].Top1 == nil {
			continue
		}
		// 房主和用户自己需要看到全部信息
		if _, ok := invisibleUsers[activatedGifts[i].Top1.UserID]; ok {
			activatedGifts[i].Top1.RankInvisible = true
			if (resp.userID != activatedGifts[i].Top1.UserID) &&
				(resp.userID != resp.r.CreatorID) {
				activatedGifts[i].Top1.UserID = 0
				activatedGifts[i].Top1.Username = "神秘人"
				activatedGifts[i].Top1.IconURL = icon
				continue
			}
		}
		if u := users[activatedGifts[i].Top1.UserID]; u != nil {
			activatedGifts[i].Top1.Username = u.Username
			activatedGifts[i].Top1.IconURL = u.IconURL
		}
	}

	return nil
}

// buildInactivatedGifts 构建未点亮礼物信息
func (resp *giftWallInfoResp) buildInactivatedGifts(period *giftwall.Period, gsMap map[int64]*gift.Gift, activeGiftIDs []int64) ([]*activatedGift, error) {
	inactivatedGiftIDs := util.DiffInt64(period.ShowGiftIDs, activeGiftIDs)
	inactivatedGifts := make([]*activatedGift, 0, len(inactivatedGiftIDs))

	if len(inactivatedGiftIDs) == 0 {
		return inactivatedGifts, nil
	}

	gs, err := giftwall.FindGiftsByShowGiftIDs(inactivatedGiftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 即使礼物绑定关系被移除，也要显示未点亮的上墙礼物
	giftWallGiftsMap := goutil.ToMap(gs, "ShowGiftID").(map[int64]*giftwall.Gift)
	for _, showGiftID := range period.ShowGiftIDs {
		// 未点亮的礼物需要按照配置的 show_gift_ids 排序
		if !goutil.HasElem(inactivatedGiftIDs, showGiftID) {
			continue
		}
		g := gsMap[showGiftID]
		if g == nil {
			continue
		}
		elem := &activatedGift{
			GiftID:      g.GiftID,
			GiftName:    g.Name,
			GiftPrice:   g.Price,
			GiftIconURL: g.Icon,
		}
		if gw := giftWallGiftsMap[elem.GiftID]; gw != nil {
			elem.TargetGiftID = gw.TargetGiftID
		}
		inactivatedGifts = append(inactivatedGifts, elem)
	}

	return inactivatedGifts, nil
}

func (resp *giftWallInfoResp) buildTip() {
	ln, err := livenotice.FindGiftWallNotice()
	if err != nil {
		logger.Error(err)
		return
	}
	if ln != nil {
		resp.Tip = &giftWallInfoTip{
			TipID:   ln.ID,
			Message: ln.Content,
			OpenURL: ln.OpenURL,
		}
	}
}

type rewardListItem struct {
	GiftID      int64  `json:"gift_id"`
	GiftName    string `json:"gift_name"`
	GiftIconURL string `json:"gift_icon_url"`
	Threshold   int64  `json:"threshold"`
	Received    int    `json:"received,omitempty"` // 完成任务获得奖励，1 为完成任务
}

type rewardListResp struct {
	Rewards []*rewardListItemNew `json:"rewards"`
}

type rewardListItemNew struct {
	Type           int    `json:"type"`       // 奖励类型
	ElementID      int64  `json:"element_id"` // 奖励 ID（直播间专属礼物 ID、外观 ID）
	ElementName    string `json:"element_name"`
	ElementIconURL string `json:"element_icon_url"`
	Threshold      int64  `json:"threshold"`
	Received       int    `json:"received,omitempty"` // 完成任务获得奖励，1 为完成任务
}

// ActionGiftWallReward 礼物墙点亮进度及奖励
/**
 * @api {get} /api/v2/chatroom/giftwall/reward 礼物墙点亮进度及奖励
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [type=0] 礼物墙类型 0: 普通礼物墙; 1: 甄选礼物墙
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "rewards": [
 *         {
 *           "type": 1, // 1：直播间专属礼物奖励；2：直播间外观奖励；3：直播间专属表情
 *           "element_id": 1, // 当 type = 1 时，element_id 表示直播间专属礼物 ID
 *           "element_name": "直播间专属礼物【神明少女 1】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 5,
 *           "received": 1 // 完成任务获得奖励，下发 1 表示完成任务，不下发表示未完成任务
 *         },
 *         {
 *           "type": 1,
 *           "element_id": 2,
 *           "element_name": "直播间专属礼物【神明少女 2】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 15,
 *           "received": 1
 *         },
 *         {
 *           "type": 2,
 *           "element_id": 3, // 当 type = 2 时，element_id 表示外观 ID
 *           "element_name": "称号【人气冠军】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 30
 *         },
 *         {
 *           "type": 3,
 *           "element_id": 4, // 当 type = 3 时，element_id 表示表情 ID
 *           "element_name": "直播间专属表情",
 *           "element_icon_url": "http://static.maoercdn.com/sticker/icon01.png",
 *           "threshold": 35
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallReward(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	giftWallType, _ := c.GetParamInt("type")
	if !slices.Contains([]int{giftwall.PeriodTypeNormal, giftwall.PeriodTypePremium}, giftWallType) {
		return nil, actionerrors.ErrParams
	}
	period, err := giftwall.CurrentPeriodInfoByType(goutil.TimeNow(), giftWallType)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	emptyResp := rewardListResp{Rewards: make([]*rewardListItemNew, 0)}
	if period == nil {
		return emptyResp, nil
	}
	if len(period.CreatorIDs) > 0 {
		creatorID, err := room.FindCreatorID(roomID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !slices.Contains(period.CreatorIDs, creatorID) {
			return emptyResp, nil
		}
	}

	rewardGiftIDs := make([]int64, 0, len(period.Rewards))
	rewardAppearanceIDs := make([]int64, 0, len(period.Rewards))
	rewardLiveStickerIDs := make([]int64, 0, len(period.Rewards))
	for _, reward := range period.Rewards {
		switch reward.Type {
		case giftwall.RewardTypeCustomGift:
			rewardGiftIDs = append(rewardGiftIDs, reward.ElementID)
		case giftwall.RewardTypeAppearance:
			rewardAppearanceIDs = append(rewardAppearanceIDs, reward.ElementID)
		case giftwall.RewardTypeSticker:
			rewardLiveStickerIDs = append(rewardLiveStickerIDs, reward.ElementID)
		}
	}

	giftMap := make(map[int64]*gift.Gift)
	if len(rewardGiftIDs) > 0 {
		giftMap, err = gift.FindGiftMapByGiftIDs(rewardGiftIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	appearancesMap := make(map[int64]*appearance.Appearance)
	if len(rewardAppearanceIDs) > 0 {
		appearancesMap, err = appearance.FindMapByIDs(rewardAppearanceIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	liveStickerMap := make(map[int64]*livesticker.LiveSticker)
	if len(rewardLiveStickerIDs) > 0 {
		stickers, err := livesticker.FindStickersByIDs(rewardLiveStickerIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		liveStickerMap = util.ToMap(stickers, func(l *livesticker.LiveSticker) int64 {
			return l.ID
		})
	}

	activatedCount, err := giftwall.ActivatedCountByRoomID(period.OID, roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	rewards := make([]*rewardListItemNew, 0, len(period.Rewards))
	for _, reward := range period.Rewards {
		switch reward.Type {
		case giftwall.RewardTypeCustomGift:
			g, ok := giftMap[reward.ElementID]
			if !ok {
				continue
			}
			itemNew := &rewardListItemNew{
				Type:           reward.Type,
				ElementID:      reward.ElementID,
				ElementName:    fmt.Sprintf("直播间专属礼物【%s】", g.Name),
				ElementIconURL: g.Icon,
				Threshold:      reward.Threshold,
				Received:       goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewards = append(rewards, itemNew)
		case giftwall.RewardTypeAppearance:
			a, ok := appearancesMap[reward.ElementID]
			if !ok {
				continue
			}
			itemNew := &rewardListItemNew{
				Type:           reward.Type,
				ElementID:      reward.ElementID,
				ElementName:    fmt.Sprintf("%s【%s】", appearance.TypeName(a.Type), a.Name),
				ElementIconURL: storage.ParseSchemeURL(a.Icon),
				Threshold:      reward.Threshold,
				Received:       goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewards = append(rewards, itemNew)
		case giftwall.RewardTypeSticker:
			p, ok := liveStickerMap[reward.ElementID]
			if !ok {
				continue
			}
			itemNew := &rewardListItemNew{
				Type:           reward.Type,
				ElementID:      reward.ElementID,
				ElementName:    fmt.Sprintf("直播间专属表情【%s】", p.Name),
				ElementIconURL: p.Icon,
				Threshold:      reward.Threshold,
				Received:       goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewards = append(rewards, itemNew)
		}
	}
	return rewardListResp{
		Rewards: rewards,
	}, nil
}

type giftWallRankResp struct {
	ActivatedGift *activatedGift       `json:"activated_gift"`
	ActivatedNum  int64                `json:"activated_num"`
	Data          []*activatedRankElem `json:"data"`
	MyRank        *activatedRankElem   `json:"my_rank,omitempty"`
	Sponsor       *sponsor             `json:"sponsor,omitempty"`

	r            *room.Room
	u            *user.User
	period       *giftwall.Period
	showGift     *gift.Gift
	giftWallType int
}

// activatedRankElem 榜单信息
type activatedRankElem struct {
	Rank     int64  `json:"rank"`
	Score    int64  `json:"score"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`

	RankUp        int64  `json:"rank_up,omitempty"`
	RemainNum     *int64 `json:"remain_num,omitempty"`
	RankInvisible bool   `json:"rank_invisible,omitempty"`
}

// sponsor 冠名相关信息
type sponsor struct {
	LabelIconURL     string `json:"label_icon_url,omitempty"`
	DetailImageURL   string `json:"detail_image_url,omitempty"`
	ShowObtainButton *bool  `json:"show_obtain_button,omitempty"`
	Threshold        int64  `json:"threshold"`
}

// ActionGiftWallRank 礼物墙用户贡献榜
/**
 * @api {get} /api/v2/chatroom/giftwall/rank 礼物墙用户贡献榜
 * @apiDescription 只返回请求时间对应周期的榜单，跨周期导致的上墙礼物不存在会返回参数错误
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} gift_id 上墙礼物 ID
 * @apiParam {Number} [type=0] 礼物墙类型 0: 普通礼物墙; 1: 甄选礼物墙
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "activated_gift": {
 *         "gift_id": 1,
 *         "gift_name": "凤鸣九天",
 *         "gift_price": 100, // 单位: 钻
 *         "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *         "target_gift_id": 3 // 跳转的定位礼物 ID
 *       },
 *       "activated_num": 1, // 被点亮次数
 *       "data": [
 *         {
 *           "rank": 1,
 *           "score": 10,
 *           "user_id": 1234, // 用户 id
 *           "username": "1234",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *         },
 *         {
 *           "rank": 2,
 *           "score": 9,
 *           "user_id": 12345,
 *           "username": "123456",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *         },
 *         {
 *           "rank": 2,
 *           "score": 9,
 *           "user_id": 0,
 *           "username": "神秘人",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }
 *       ],
 *       "my_rank": { // 未登录不返回该字段
 *         "rank": 2,
 *         "score": 9,
 *         "rank_up": 2, // 上升名次或上榜还需要的数量
 *         "remain_num": 10, //  剩余多少数量到榜首
 *         "user_id": 123456,
 *         "username": "123456",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *       },
 *       "sponsor": { // 冠名相关
 *         "label_icon_url": "http://static.maoercdn.com/sponsor_tag_url.png", // 即将冠名角标，没有即将冠名不下发该字段
 *         "detail_image_url": "http://static.maoercdn.com/detail.png", // 冠名详情
 *         "show_obtain_button": true, // 是否显示一键冠名按钮
 *         "threshold": 10000 // 冠名门槛，单位：钻石
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallRank(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newGiftWallRankResp(c)
	if err != nil {
		return nil, err
	}
	if err = resp.buildData(); err != nil {
		return nil, err
	}
	if err = resp.buildMyRank(); err != nil {
		return nil, err
	}
	resp.buildRankInvisible()
	if err = resp.buildActivatedInfo(); err != nil {
		return nil, err
	}
	if err = resp.buildSponsor(); err != nil {
		return nil, err
	}
	return resp, nil
}

func newGiftWallRankResp(c *handler.Context) (*giftWallRankResp, error) {
	roomID, _ := c.GetParamInt64("room_id")
	giftID, _ := c.GetParamInt64("gift_id")
	giftWallType, _ := c.GetParamInt("type")
	if roomID <= 0 || giftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	period, err := giftwall.CurrentPeriodInfoByType(now, giftWallType)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if period == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	if !goutil.HasElem(period.ShowGiftIDs, giftID) {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	g, err := gift.FindByGiftID(giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	return &giftWallRankResp{
		r:            r,
		u:            c.User(),
		period:       period,
		showGift:     g,
		giftWallType: giftWallType,
	}, nil
}

func (resp *giftWallRankResp) buildData() error {
	top5, err := giftwall.FindTop5(resp.period.OID, resp.r.RoomID, resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Data = make([]*activatedRankElem, 0, len(top5))
	userIDs := make([]int64, 0, len(top5))
	for i, v := range top5 {
		info := &activatedRankElem{
			Rank:   int64(i + 1),
			Score:  v.ActivatedNum,
			UserID: v.UserID,
		}
		resp.Data = append(resp.Data, info)
		userIDs = append(userIDs, v.UserID)
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(resp.Data); i++ {
		u := users[resp.Data[i].UserID]
		if u == nil {
			continue
		}
		resp.Data[i].IconURL = u.IconURL
		resp.Data[i].Username = u.Username
	}
	return nil
}

func (resp *giftWallRankResp) buildMyRank() error {
	if resp.u == nil || resp.u.ID == 0 || resp.u.ID == resp.r.CreatorID {
		return nil
	}
	resp.MyRank = &activatedRankElem{
		UserID:        resp.u.ID,
		Username:      resp.u.Username,
		IconURL:       resp.u.IconURL,
		RankUp:        1,
		RankInvisible: userstatus.IsRankInvisible(resp.u.ID, resp.r.RoomID, true),
	}

	var topScore int64
	if len(resp.Data) > 0 {
		topScore = resp.Data[0].Score
	}
	for i := range resp.Data {
		if resp.Data[i].UserID != resp.u.ID {
			continue
		}
		resp.MyRank.Rank = resp.Data[i].Rank
		resp.MyRank.Score = resp.Data[i].Score
		if i == 0 {
			resp.MyRank.RankUp = 0
			resp.MyRank.RemainNum = goutil.NewInt64(0)
		} else {
			resp.MyRank.RankUp = resp.Data[i-1].Score - resp.Data[i].Score + 1
			resp.MyRank.RemainNum = goutil.NewInt64(topScore - resp.Data[i].Score + 1)
		}
		return nil
	}
	// 非前 5 名
	myRank, err := giftwall.FindUserRank(resp.period.OID, resp.r.RoomID, resp.u.ID, resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if myRank == nil {
		resp.MyRank.RemainNum = goutil.NewInt64(topScore + 1)
		if len(resp.Data) >= 5 {
			resp.MyRank.RankUp = resp.Data[4].Score + 1
		}
		return nil
	}

	resp.MyRank.Score = myRank.ActivatedNum
	resp.MyRank.RemainNum = goutil.NewInt64(topScore - myRank.ActivatedNum + 1)
	if len(resp.Data) >= 5 {
		// 榜单满了
		resp.MyRank.RankUp = max(resp.Data[4].Score-myRank.ActivatedNum+1, 1)
	}
	return nil
}

func (resp *giftWallRankResp) buildRankInvisible() {
	invisibleUsers := userstatus.RankInvisibleUsers(resp.r.RoomID)
	icon := service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)
	for i := 0; i < len(resp.Data); i++ {
		if _, ok := invisibleUsers[resp.Data[i].UserID]; ok {
			resp.Data[i].RankInvisible = true
			// 用户自己和主播需要查看用户隐身前的信息
			if resp.u == nil || (resp.Data[i].UserID != resp.u.ID && resp.r.CreatorID != resp.u.ID) {
				resp.Data[i].UserID = 0
				resp.Data[i].Username = "神秘人"
				resp.Data[i].IconURL = icon
			}
		}
	}
}

func (resp *giftWallRankResp) buildActivatedInfo() error {
	g, err := giftwall.FindOneByShowGiftID(resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return actionerrors.ErrCannotFindResource
	}
	resp.ActivatedGift = &activatedGift{
		GiftID:       resp.showGift.GiftID,
		GiftName:     resp.showGift.Name,
		GiftPrice:    resp.showGift.Price,
		GiftIconURL:  resp.showGift.Icon,
		TargetGiftID: g.TargetGiftID,
	}

	record, err := giftwall.FindOneRecord(bson.M{
		"show_gift_id": resp.showGift.GiftID,
		"room_id":      resp.r.RoomID,
		"_period_id":   resp.period.OID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if record == nil {
		return actionerrors.ErrCannotFindResource
	}
	resp.ActivatedNum = record.ActivatedNum
	return nil
}

func (resp *giftWallRankResp) buildSponsor() error {
	giftWallConfig, err := params.FindGiftWall()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	var detailURL string
	if resp.giftWallType == giftwall.PeriodTypePremium {
		detailURL = giftWallConfig.PremiumGiftWall.DetailImageURL
	} else {
		detailURL = giftWallConfig.NormalGiftWall.DetailImageURL
	}

	// 判断是否显示一键冠名按钮：用户已登录，不是随机礼物，并且不是榜首
	var showObtainButton bool
	if resp.u != nil && resp.showGift.Type != gift.TypeDrawReceive {
		// 如果用户不在榜首（或者榜单为空），显示一键冠名按钮
		showObtainButton = len(resp.Data) == 0 || resp.Data[0].UserID != resp.u.ID
	}

	// 添加冠名详情
	resp.Sponsor = &sponsor{
		DetailImageURL:   detailURL,
		ShowObtainButton: goutil.NewBool(showObtainButton),
	}
	var topUserContribution int64
	if len(resp.Data) > 0 && resp.Data[0].Rank == 1 && resp.ActivatedGift != nil {
		topUserContribution = resp.ActivatedGift.GiftPrice * resp.Data[0].Score
	}
	switch resp.giftWallType {
	case giftwall.PeriodTypePremium:
		resp.Sponsor.Threshold = giftWallConfig.PremiumGiftWall.SponsorThreshold
		// 判断是否到达门槛，如果到达，添加即将冠名角标
		if topUserContribution >= giftWallConfig.PremiumGiftWall.SponsorThreshold {
			resp.Sponsor.LabelIconURL = giftWallConfig.LabelIconURL
		}
	case giftwall.PeriodTypeNormal:
		resp.Sponsor.Threshold = giftWallConfig.NormalGiftWall.SponsorThreshold
		// 判断是否到达门槛，如果到达，添加即将冠名角标
		if topUserContribution >= giftWallConfig.NormalGiftWall.SponsorThreshold {
			resp.Sponsor.LabelIconURL = giftWallConfig.LabelIconURL
		}
	}
	return nil
}

type sponsorListResp struct {
	PremiumGifts []sponsorListItem `json:"premium_gifts,omitempty"`
	Gifts        []sponsorListItem `json:"gifts,omitempty"`
	EndTime      int64             `json:"end_time"` // 本周期结束时间，单位：秒
}

type sponsorListItem struct {
	GiftID      int64    `json:"gift_id"`
	GiftName    string   `json:"gift_name"`
	GiftIconURL string   `json:"gift_icon_url"`
	User        userInfo `json:"user"`
}

type userInfo struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
}

// ActionGiftWallSponsorList 上期冠名礼物列表
/**
 * @api {get} /api/v2/chatroom/giftwall/sponsor/list 上期冠名礼物列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *      "premium_gifts": [ // 返回甄选点亮礼物，没有甄选礼物不返回该字段
 *        {
 *          "gift_id": 1, // 上墙礼物 ID
 *          "gift_name": "凤舞九天",
 *          "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *          "user": {
 *            "user_id": 10,
 *            "username": "送礼者",
 *            "iconurl": "http://static.maoercdn.com/avatars/icon01.png"
 *          }
 *        }
 *      ],
 *      "gifts": [ // 返回普通点亮礼物，没有普通点亮礼物不返回该字段
 *        {
 *          "gift_id": 1, // 上墙礼物 ID
 *          "gift_name": "凤舞九天",
 *          "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *          "user": {
 *            "user_id": 10,
 *            "username": "送礼者",
 *            "iconurl": "http://static.maoercdn.com/avatars/icon01.png"
 *          }
 *        }
 *      ],
 *      "end_time": 1660104000 // 本周期结束时间，单位：秒
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallSponsorList(c *handler.Context) (handler.ActionResponse, error) {
	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	// 查询当前周期时间信息，目前普通礼物和甄选礼物周期时间信息是相同的
	currentPeriod, err := giftwall.CurrentPeriodInfoByType(goutil.TimeNow(), giftwall.PeriodTypeNormal)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if currentPeriod == nil {
		return sponsorListResp{}, nil
	}
	creatorID, err := room.FindCreatorID(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 查询上周期礼物墙配置信息
	previousPeriods, err := currentPeriod.PrevPeriods()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var normalPeriodOID primitive.ObjectID
	var premiumPeriodOID primitive.ObjectID
	previousPeriodOIDs := make([]primitive.ObjectID, 0, len(previousPeriods))
	for _, period := range previousPeriods {
		switch period.Type {
		case giftwall.PeriodTypeNormal:
			normalPeriodOID = period.OID
		case giftwall.PeriodTypePremium:
			premiumPeriodOID = period.OID
		default:
			logger.Errorf("错误的礼物墙类型：%d", period.Type)
			continue
		}
		if len(period.CreatorIDs) == 0 || slices.Contains(period.CreatorIDs, creatorID) {
			previousPeriodOIDs = append(previousPeriodOIDs, period.OID)
		}
	}
	resp := sponsorListResp{
		EndTime: currentPeriod.EndTime,
	}
	if len(previousPeriodOIDs) == 0 {
		return resp, nil
	}

	// 查询礼物冠名记录
	records, err := giftwall.ListSponsor(bson.M{"room_id": roomID, "_period_id": bson.M{"$in": previousPeriodOIDs}},
		options.Find().SetSort(bson.M{"revenue": -1}))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	userIDs := make([]int64, 0, len(records))
	giftIDs := make([]int64, 0, len(records))
	for _, record := range records {
		userIDs = append(userIDs, record.UserID)
		giftIDs = append(giftIDs, record.GiftID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, record := range records {
		u, ok := userMap[record.UserID]
		if !ok {
			continue
		}
		g, ok := giftMap[record.GiftID]
		if !ok {
			continue
		}
		item := sponsorListItem{
			GiftID:      g.GiftID,
			GiftName:    g.Name,
			GiftIconURL: g.Icon,
			User: userInfo{
				UserID:   u.ID,
				Username: u.Username,
				IconURL:  u.IconURL,
			},
		}
		switch record.PeriodOID {
		case normalPeriodOID:
			resp.Gifts = append(resp.Gifts, item)
		case premiumPeriodOID:
			resp.PremiumGifts = append(resp.PremiumGifts, item)
		default:
			logger.Errorf("礼物墙类型异常：%d", record.PeriodOID)
			continue
		}
	}
	return resp, nil
}
