package upgrade

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/biz/prize"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type upgradeParam struct {
	RoomID int64 `form:"room_id" json:"room_id"` // 直播间 ID
	GiftID int64 `form:"gift_id" json:"gift_id"` // 礼物 ID

	uc     mrpc.UserContext
	userID int64

	baseGift             *gift.Gift                                   // 基础升级礼物
	baseGiftUpgrade      *livegiftupgrade.GiftUpgrade                 // 基础礼物的升级信息
	fullGiftUpgrade      *livegiftupgrade.GiftUpgrade                 // 终极礼物的升级信息
	userMetaGiftUpgrade  *usermeta.GiftUpgrade                        // user_meta 中的礼物升级数据
	giftUpgradeMap       map[int64]*livegiftupgrade.GiftUpgrade       // 礼物 ID 和礼物升级信息的映射关系
	giftUpgradeRecordMap map[int64]*livegiftupgrade.GiftUpgradeRecord // 礼物 ID 和升级礼物获取记录之间的映射关系
	giftIDMap            map[int64]*gift.Gift                         // 礼物信息的映射关系

	curGiftUpgrade *livegiftupgrade.GiftUpgrade // 本次升级获取礼物的升级信息
	prizeMap       map[int64]*liveprize.Prize   // 本次升级获取的奖品信息
	isNew          bool                         // 本次升级得到的礼物是否首次获得
	isFullCollect  bool                         // 本次升级是否能收集完所有礼物
}

type upgradePrize struct {
	PrizeID      int64  `json:"prize_id"`
	PrizeType    int    `json:"prize_type"`
	PrizeName    string `json:"prize_name"`
	PrizeIconURL string `json:"prize_icon_url"`
}

type upgradedGift struct {
	IsNew bool                     `json:"is_new"`
	Gift  livegiftupgrade.GiftInfo `json:"gift"`
	Prize upgradePrize             `json:"prize"`
}

type upgradeResp struct {
	Result  []upgradedGift              `json:"result"`
	Upgrade livegiftupgrade.UpgradeInfo `json:"upgrade"`
}

// ActionGiftUpgrade 礼物升级
/**
 * @api {post} /api/v2/chatroom/gift/upgrade 礼物升级
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} gift_id 礼物 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "result": [ // 升级结果，若集齐全部礼物会下发两个，第二个为终极礼物相关信息，否则下发一个
 *         { // 抽中的信息
 *           "is_new": true, // false 或不下发：重复获得，true：首次获得
 *           "gift": {
 *             "upgrade_type": 3, // 1：初始礼物，2：终极礼物（集齐获得），3：升级礼物
 *             "type": 11,
 *             "gift_id": 3,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "price": 10, // 礼物价格，单位：钻石
 *             "comboable": 1 // 1 用户连击礼物；2 直播间连击礼物
 *           },
 *           "prize": { // 升级奖品
 *             "prize_id": 1,
 *             "prize_type": 2, // 2：外观
 *             "prize_name": "奖品名称 × 1d",
 *             "prize_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png"
 *           }
 *         },
 *         {
 *           "gift": {
 *             "upgrade_type": 2,
 *             "type": 11,
 *             "gift_id": 2,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "price": 10, // 礼物价格，单位：钻石
 *             "comboable": 1 // 1 用户连击礼物；2 直播间连击礼物
 *           },
 *           "prize": {
 *             "prize_id": 1,
 *             "prize_type": 2, // 2：外观
 *             "prize_name": "永久外观奖励XX头像框",
 *             "prize_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png"
 *           }
 *         }
 *       ],
 *       "upgrade": { // 升级信息
 *         "series_name": "系列名",
 *         "lucky_score": { // 升级幸运值
 *           "current": 0, // 当前幸运值
 *           "target": 100, // 目标幸运值
 *           "per_increment": 10 // 每次升级重复增加的幸运值
 *         },
 *         "discount": { // 折扣信息，没有折扣时不下发
 *           "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png",
 *         },
 *         "upgrade_num": {
 *           "remain_upgrade_num": 1, // 剩余可升级次数
 *           "upgrade_gift_num": 12, // 本次升级所需礼物数量
 *           "gift_num": 10 // 本次升级已累积赠送礼物数量
 *         },
 *         "gifts": [ // 礼物列表
 *           {
 *             "upgrade_type": 1, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 1,
 *             "gift_id": 1,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *           },
 *           {
 *             "upgrade_type": 2, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 11, // 礼物类型，11: 升级基础礼物，12: 升级礼物
 *             "gift_id": 2,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *           },
 *           {
 *             "upgrade_type": 3, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 11, // 礼物类型，11: 升级基础礼物，12: 升级礼物
 *             "gift_id": 3,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1, // 0 或不下发：已解锁，1：未解锁
 *             "rate": "3%", // 获得概率（仅升级类型下发）
 *           }
 *         ]
 *       }
 *     }
 *   }
 */
func ActionGiftUpgrade(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newUpgradeParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.upgrade()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.resp()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

func newUpgradeParam(c *handler.Context) (*upgradeParam, error) {
	param := &upgradeParam{
		uc:     c.UserContext(),
		userID: c.UserID(),
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GiftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	exists, err := room.Exists(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}

	// 获取升级礼物信息，判断类型和上下架情况
	param.baseGift, err = gift.FindByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.baseGift == nil || param.baseGift.Order == gift.OrderHide || !param.baseGift.Attr.IsSet(gift.AttrUpgradeBase) {
		return nil, actionerrors.ErrForbidden
	}
	return param, nil
}

func (param *upgradeParam) upgrade() error {
	// 加锁，获取礼物升级信息，礼物信息和已开通礼物信息
	lock := redismutex.New(
		service.Redis,
		keys.LockGiftUpgrade2.Format(param.userID, param.GiftID),
		2*time.Second,
	)
	if !lock.TryLock() {
		return actionerrors.NewErrForbidden("操作过于频繁，请稍后再试")
	}
	defer lock.Unlock()

	// 获取 user_meta 确定可升级次数 > 0
	var err error
	param.userMetaGiftUpgrade, err = usermeta.FindGiftUpgrade(param.userID, param.GiftID, param.baseGift.ToggleTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.userMetaGiftUpgrade == nil || param.userMetaGiftUpgrade.UpgradeNum <= 0 {
		return actionerrors.NewErrForbidden("暂时无法升级，请刷新直播间后重试")
	}

	// 获取礼物升级信息和升级礼物的拥有情况
	giftUpgradeList, err := livegiftupgrade.FindGiftUpgradeList(param.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.giftUpgradeMap = make(map[int64]*livegiftupgrade.GiftUpgrade, len(giftUpgradeList))
	giftIDs := make([]int64, 0, len(giftUpgradeList))
	for _, gu := range giftUpgradeList {
		if gu.Type == livegiftupgrade.GiftUpgradeTypeBase {
			param.baseGiftUpgrade = gu
			giftIDs = append(giftIDs, gu.BaseGiftID)
			param.giftUpgradeMap[gu.BaseGiftID] = gu
		} else {
			if gu.Type == livegiftupgrade.GiftUpgradeTypeFull {
				param.fullGiftUpgrade = gu
			}
			giftIDs = append(giftIDs, gu.UpgradeGiftID)
			param.giftUpgradeMap[gu.UpgradeGiftID] = gu
		}
	}
	if param.baseGiftUpgrade == nil {
		return actionerrors.ErrNotFound("礼物升级信息不存在")
	}
	param.giftIDMap, err = gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.giftUpgradeRecordMap, err = livegiftupgrade.FindGiftUpgradeRecordsMapByUserID(param.userID, param.GiftID, param.baseGift.ToggleTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 如果已经拥有全部升级礼物，则不需要再进行升级操作。否则获取升级得到的礼物 ID
	if livegiftupgrade.IsAllCollected(param.giftUpgradeMap, param.giftUpgradeRecordMap) {
		return actionerrors.ErrForbidden
	}
	upgradeGiftID, err := livegiftupgrade.GetUpgradeGiftID(param.giftUpgradeMap, param.giftUpgradeRecordMap, param.userMetaGiftUpgrade.LuckyScore, param.baseGiftUpgrade.MoreInfo.LuckyScoreTarget)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.curGiftUpgrade = param.giftUpgradeMap[upgradeGiftID]
	param.isNew = param.giftUpgradeRecordMap[upgradeGiftID] == nil
	if param.isNew {
		// 如果升级得到的是未拥有过的礼物，则有机会获得终极礼物
		param.isFullCollect = true
		// 循环验证是否所有升级礼物都被获取
		for giftID, gu := range param.giftUpgradeMap {
			if gu.Type != livegiftupgrade.GiftUpgradeTypeUpgrade {
				continue
			}
			// 如果存在此前未拥有的升级礼物，且该礼物不是此次升级得到的礼物。则不能得到终极礼物
			if param.giftUpgradeRecordMap[giftID] == nil && giftID != upgradeGiftID {
				param.isFullCollect = false
				break
			}
		}
	}

	// 查询奖品相关信息
	prizeIDs := make([]int64, 0, 2)
	prizeIDs = append(prizeIDs, param.curGiftUpgrade.PrizeID)
	if param.isFullCollect {
		prizeIDs = append(prizeIDs, param.fullGiftUpgrade.PrizeID)
	}
	prizes, err := liveprize.FindPrizes(prizeIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.prizeMap = util.ToMap(prizes, func(prize *liveprize.Prize) int64 {
		return prize.ID
	})

	nowUnix := goutil.TimeNow().Unix()
	// 先写 MySQL，更新 MongoDB user_meta 失败的话可以回滚保持一致性
	err = servicedb.Tx(livegiftupgrade.DB(), func(tx *gorm.DB) error {
		newRecords := []livegiftupgrade.GiftUpgradeRecord{
			{CreateTime: nowUnix, ModifiedTime: nowUnix, BaseGiftID: param.GiftID, UpgradeGiftID: upgradeGiftID, UserID: param.userID, RoomID: param.RoomID},
		}
		if param.isFullCollect {
			newRecords = append(newRecords, livegiftupgrade.GiftUpgradeRecord{CreateTime: nowUnix, ModifiedTime: nowUnix, BaseGiftID: param.GiftID, UpgradeGiftID: param.fullGiftUpgrade.UpgradeGiftID, UserID: param.userID, RoomID: param.RoomID})
		}
		// 更新 giftUpgradeRecordMap，用于更新升级后返回的 gifts 列表 lock 状态
		for i := range newRecords {
			record := newRecords[i]
			param.giftUpgradeRecordMap[record.UpgradeGiftID] = &record
		}
		err = servicedb.BatchInsert(tx, livegiftupgrade.GiftUpgradeRecord{}.TableName(), newRecords)
		if err != nil {
			return err
		}

		afterLuckyScore := param.userMetaGiftUpgrade.LuckyScore + param.baseGiftUpgrade.MoreInfo.LuckyScorePerIncr
		if param.isNew {
			// 未拥有过的礼物，清空幸运值
			afterLuckyScore = 0
		}
		// 减少升级次数并更新幸运值
		param.userMetaGiftUpgrade, err = usermeta.UpdateAfterGiftUpgrade(param.userID, param.GiftID, param.baseGift.ToggleTime, afterLuckyScore)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"user_id": param.userID, "gift_id": param.GiftID})
	}

	userPrizes := make([]prize.Distributor, 0, 2)
	userPrizes = append(userPrizes,
		prize.NewUserDistributor(param.userID, param.prizeMap[param.curGiftUpgrade.PrizeID], prize.WithBiz(liveprize.BizGiftUpgrade, param.curGiftUpgrade.ID)))
	if param.isFullCollect {
		userPrizes = append(userPrizes,
			prize.NewUserDistributor(param.userID, param.prizeMap[param.fullGiftUpgrade.PrizeID], prize.WithBiz(liveprize.BizGiftUpgrade, param.fullGiftUpgrade.ID)))
	}
	_, err = prize.Send(userPrizes)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":   param.userID,
			"gift_id":   param.GiftID,
			"prize_ids": prizeIDs,
		}).Error(err)
		// PASS
	}

	return nil
}

func (param *upgradeParam) buildUpgradeGiftResult(upgradeGiftID int64, prize *liveprize.Prize, isNew bool) upgradedGift {
	g := param.giftIDMap[upgradeGiftID]
	gu := param.giftUpgradeMap[upgradeGiftID]
	return upgradedGift{
		IsNew: isNew,
		Gift: livegiftupgrade.GiftInfo{
			UpgradeType: gu.Type,
			Type:        g.Type,
			GiftID:      g.GiftID,
			Name:        g.Name,
			IconURL:     g.Icon,
			Price:       g.Price,
			Comboable:   g.Comboable,
		},
		Prize: upgradePrize{
			PrizeID:      prize.ID,
			PrizeType:    prize.Type,
			PrizeName:    prize.Name,
			PrizeIconURL: prize.Icon,
		},
	}
}

func (param *upgradeParam) resp() (*upgradeResp, error) {
	resp := &upgradeResp{
		Result: make([]upgradedGift, 0, 2),
	}
	resp.Result = append(resp.Result, param.buildUpgradeGiftResult(param.curGiftUpgrade.UpgradeGiftID, param.prizeMap[param.curGiftUpgrade.PrizeID], param.isNew))
	if param.isFullCollect {
		// 收集完成时，需要下发终极礼物的信息
		resp.Result = append(resp.Result, param.buildUpgradeGiftResult(param.fullGiftUpgrade.UpgradeGiftID, param.prizeMap[param.fullGiftUpgrade.PrizeID], true))
	} else {
		// 未收集完成时，才下发幸运值和升级次数的信息
		resp.Upgrade.LuckyScore = livegiftupgrade.BuildLuckyScore(param.userMetaGiftUpgrade, param.baseGiftUpgrade)
		resp.Upgrade.UpgradeNum = livegiftupgrade.BuildUpgradeNum(param.userMetaGiftUpgrade, param.baseGiftUpgrade) // 升级后返回的升级信息按非首次升级信息返回
	}
	resp.Upgrade.SeriesName = livegiftupgrade.BuildSeriesName(param.baseGiftUpgrade)
	resp.Upgrade.Gifts = livegiftupgrade.BuildGiftInfos(param.giftUpgradeMap, param.giftUpgradeRecordMap, param.giftIDMap)
	return resp, nil
}
