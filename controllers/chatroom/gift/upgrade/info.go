package upgrade

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

type infoParam struct {
	roomID int64
	giftID int64

	uc     mrpc.UserContext
	userID int64

	baseGiftUpgrade      *livegiftupgrade.GiftUpgrade                 // 基础礼物的升级信息
	giftUpgradeMap       map[int64]*livegiftupgrade.GiftUpgrade       // 礼物 ID 和礼物升级信息的映射关系
	giftUpgradeRecordMap map[int64]*livegiftupgrade.GiftUpgradeRecord // 礼物 ID 和升级礼物获取记录之间的映射关系
	giftIDMap            map[int64]*gift.Gift                         // 礼物信息的映射关系
	userMetaGiftUpgrade  *usermeta.GiftUpgrade                        // user_meta 中的礼物升级数据
}

type infoResp struct {
	Upgrade livegiftupgrade.UpgradeInfo `json:"upgrade"`
}

// ActionGiftUpgradeInfo 礼物升级详情
/**
 * @api {get} /api/v2/chatroom/gift/upgrade/info 礼物升级详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} gift_id 礼物 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "upgrade": { // 升级信息
 *         "intro_open_url": "https://open.html", // 规则页跳转链接
 *         "lucky_score": { // 升级幸运值
 *           "current": 0, // 当前幸运值
 *           "target": 100, // 目标幸运值
 *           "per_increment": 10 // 每次升级重复增加的幸运值
 *         },
 *         "discount": { // 折扣信息，没有折扣时不下发
 *           "label_icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png",
 *         },
 *         "upgrade_num": {
 *           "remain_upgrade_num": 1, // 剩余可升级次数
 *           "upgrade_gift_num": 12, // 本次升级所需礼物数量
 *           "gift_num": 10 // 本次升级已累积赠送礼物数量
 *         },
 *         "gifts": [ // 礼物列表
 *           {
 *             "upgrade_type": 1, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 1,
 *             "gift_id": 1,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *           },
 *           {
 *             "upgrade_type": 2, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 11,
 *             "gift_id": 2,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1 // 0 或不下发：已解锁，1：未解锁
 *           },
 *           {
 *             "upgrade_type": 3, // 升级类型，1：初始，2：终极，3：升级
 *             "type": 11,
 *             "gift_id": 3,
 *             "name": "礼物名称",
 *             "icon_url": "http://static-test.maoercdn.com/xxx/xxx.png",
 *             "lock": 1, // 0 或不下发：已解锁，1：未解锁
 *             "rate": "3%", // 获得概率（仅升级类型下发）
 *           }
 *         ]
 *       }
 *     }
 *   }
 */
func ActionGiftUpgradeInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newInfoParam(c)
	if err != nil {
		return nil, "", err
	}
	resp, err := param.resp()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

func newInfoParam(c *handler.Context) (*infoParam, error) {
	param := &infoParam{
		uc:     c.UserContext(),
		userID: c.UserID(),
	}
	var err error
	param.roomID, err = c.GetParamInt64("room_id")
	if err != nil || param.roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	exists, err := room.Exists(param.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.giftID, err = c.GetParamInt64("gift_id")
	if err != nil || param.giftID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 获取礼物升级相关记录，并获取升级礼物对应的礼物信息
	giftUpgradeList, err := livegiftupgrade.FindGiftUpgradeList(param.giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(giftUpgradeList) == 0 {
		return nil, actionerrors.ErrParams
	}
	param.giftUpgradeMap = make(map[int64]*livegiftupgrade.GiftUpgrade, len(giftUpgradeList))
	giftIDs := make([]int64, 0, len(giftUpgradeList))
	for _, gu := range giftUpgradeList {
		if gu.Type == livegiftupgrade.GiftUpgradeTypeBase {
			param.baseGiftUpgrade = gu
			giftIDs = append(giftIDs, gu.BaseGiftID)
			param.giftUpgradeMap[gu.BaseGiftID] = gu
		} else {
			giftIDs = append(giftIDs, gu.UpgradeGiftID)
			param.giftUpgradeMap[gu.UpgradeGiftID] = gu
		}
	}
	if param.baseGiftUpgrade == nil {
		return nil, actionerrors.ErrParams
	}
	param.giftIDMap, err = gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取基础礼物最新一次上架之后，用户的收集情况
	baseGift := param.giftIDMap[param.baseGiftUpgrade.BaseGiftID]
	if baseGift == nil || baseGift.Order == gift.OrderHide || !baseGift.IsUpgradeBaseGift() {
		return nil, actionerrors.ErrForbidden
	}
	param.giftUpgradeRecordMap, err = livegiftupgrade.FindGiftUpgradeRecordsMapByUserID(param.userID, param.giftID, baseGift.ToggleTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	param.userMetaGiftUpgrade, err = usermeta.FindGiftUpgrade(param.userID, param.giftID, baseGift.ToggleTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return param, nil
}

func (p *infoParam) resp() (*infoResp, error) {
	resp := &infoResp{
		Upgrade: livegiftupgrade.UpgradeInfo{
			IntroOpenURL: livegiftupgrade.BuildIntro(p.baseGiftUpgrade),
			Discount:     livegiftupgrade.BuildDiscountIcon(p.userMetaGiftUpgrade, p.baseGiftUpgrade),
			Gifts:        livegiftupgrade.BuildGiftInfos(p.giftUpgradeMap, p.giftUpgradeRecordMap, p.giftIDMap),
		},
	}
	if !livegiftupgrade.IsAllCollected(p.giftUpgradeMap, p.giftUpgradeRecordMap) {
		// 没有收集完时，返回幸运值和升级次数的信息
		resp.Upgrade.LuckyScore = livegiftupgrade.BuildLuckyScore(p.userMetaGiftUpgrade, p.baseGiftUpgrade)
		resp.Upgrade.UpgradeNum = livegiftupgrade.BuildUpgradeNum(p.userMetaGiftUpgrade, p.baseGiftUpgrade)
	}
	return resp, nil
}
