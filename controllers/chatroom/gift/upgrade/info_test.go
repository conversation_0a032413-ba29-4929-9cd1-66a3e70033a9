package upgrade

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGiftUpgradeInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	resp, _, err := ActionGiftUpgradeInfo(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(resp)

	err = livegiftupgrade.DB().Delete(livegiftupgrade.GiftUpgrade{}).Error
	require.NoError(err)
	moreInfo := livegiftupgrade.MoreInfo{
		FirstUpgradeNum:   3,
		UpgradeNum:        10,
		LuckyScoreTarget:  100,
		LuckyScorePerIncr: 10,
	}
	moreByte, err := json.Marshal(moreInfo)
	require.NoError(err)
	guRecords := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111, More: moreByte},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, UpgradeGiftID: 111112},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111113, Weight: 1},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111114, Weight: 2},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRecords)
	require.NoError(err)

	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	gifts := []interface{}{
		gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111, Attr: attr},
		gift.Gift{GiftID: 111112, Name: "升级礼物 111112", NameClean: "升级礼物 111112", Order: 111112},
		gift.Gift{GiftID: 111113, Name: "升级礼物 111113", NameClean: "升级礼物 111113", Order: 111113},
		gift.Gift{GiftID: 111114, Name: "升级礼物 111114", NameClean: "升级礼物 111114", Order: 111114},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 批量插入更新 gifts
	_, err = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{111111, 111112, 111113, 111114}}})
	require.NoError(err)
	_, err = gift.Collection().InsertMany(ctx, gifts)
	require.NoError(err)
	_, err = usermeta.Collection().UpdateOne(ctx,
		bson.M{"user_id": c.UserID()},
		bson.M{"$set": bson.M{"gift_upgrades": []usermeta.GiftUpgrade{}}},
	)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet, "?room_id=223344&gift_id=111111", true, nil)
	resp, _, err = ActionGiftUpgradeInfo(c)
	require.NoError(err)
	r, ok := resp.(*infoResp)
	require.True(ok)
	require.NotNil(r.Upgrade)
	require.NotNil(r.Upgrade.LuckyScore)
	assert.Equal(0, r.Upgrade.LuckyScore.Current)
	assert.Equal(moreInfo.LuckyScorePerIncr, r.Upgrade.LuckyScore.PerIncrement)
	assert.Equal(moreInfo.LuckyScoreTarget, r.Upgrade.LuckyScore.Target)
	require.NotNil(r.Upgrade.UpgradeNum)
	assert.Equal(moreInfo.FirstUpgradeNum, r.Upgrade.UpgradeNum.UpgradeGiftNum)
	assert.Equal(0, r.Upgrade.UpgradeNum.RemainUpgradeNum)
	assert.Equal(0, r.Upgrade.UpgradeNum.GiftNum)
	require.NotNil(r.Upgrade.Gifts)
	assert.Len(r.Upgrade.Gifts, 4)
}

func TestNewInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param, err := newInfoParam(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	c = handler.NewTestContext(http.MethodGet, "?room_id=1", true, nil)
	param, err = newInfoParam(c)
	require.Equal(actionerrors.ErrCannotFindRoom, err)
	assert.Nil(param)

	c = handler.NewTestContext(http.MethodGet, "?room_id=223344", true, nil)
	param, err = newInfoParam(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	c = handler.NewTestContext(http.MethodGet, "?room_id=223344&gift_id=2", true, nil)
	param, err = newInfoParam(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	err = livegiftupgrade.DB().Delete(livegiftupgrade.GiftUpgrade{}).Error
	require.NoError(err)
	guRecords := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 2},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 2, UpgradeGiftID: 3},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 2, UpgradeGiftID: 4},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 2, UpgradeGiftID: 5},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 2, UpgradeGiftID: 6},
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, UpgradeGiftID: 111112},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRecords)
	require.NoError(err)

	// 礼物类型不是升级礼物
	c = handler.NewTestContext(http.MethodGet, "?room_id=223344&gift_id=2", true, nil)
	param, err = newInfoParam(c)
	require.Equal(actionerrors.ErrForbidden, err)
	assert.Nil(param)

	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	g := gift.Gift{
		GiftID:    111111,
		Name:      "升级礼物单测",
		NameClean: "升级礼物单测",
		Order:     111111,
		Attr:      attr,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.Collection().UpdateOne(ctx, bson.M{"gift_id": g.GiftID}, bson.M{"$set": g}, options.Update().SetUpsert(true))
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("?room_id=223344&gift_id=%d", g.GiftID), true, nil)
	param, err = newInfoParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(int64(223344), param.roomID)
	assert.Equal(int64(111111), param.giftID)
}

func TestInfoParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := infoParam{
		giftUpgradeMap: map[int64]*livegiftupgrade.GiftUpgrade{
			1: {Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 1, MoreInfo: &livegiftupgrade.MoreInfo{IntroOpenURL: "http://open.html"}},
			5: {Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 1, UpgradeGiftID: 5},
			2: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 2, Weight: 50},
			3: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 3, Weight: 30},
			4: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 4, Weight: 20},
		},
		giftUpgradeRecordMap: map[int64]*livegiftupgrade.GiftUpgradeRecord{
			2: {BaseGiftID: 1, UpgradeGiftID: 2},
			4: {BaseGiftID: 1, UpgradeGiftID: 4},
		},
		giftIDMap: map[int64]*gift.Gift{
			1: {GiftID: 1, Name: "g1", Icon: "icon1"},
			2: {GiftID: 2, Name: "g2", Icon: "icon2"},
			3: {GiftID: 3, Name: "g3", Icon: "icon3"},
			4: {GiftID: 4, Name: "g4", Icon: "icon4"},
			5: {GiftID: 5, Name: "g5", Icon: "icon5"},
		},
	}
	p.baseGiftUpgrade = p.giftUpgradeMap[1]
	resp, err := p.resp()
	require.NoError(err)
	require.NotNil(resp.Upgrade)
	require.Equal("http://open.html", resp.Upgrade.IntroOpenURL)
	require.Nil(resp.Upgrade.Discount)
	assert.NotNil(resp.Upgrade.UpgradeNum)
	assert.NotNil(resp.Upgrade.LuckyScore)
	assert.Len(resp.Upgrade.Gifts, 5)
	for _, g := range resp.Upgrade.Gifts {
		if g.UpgradeType != livegiftupgrade.GiftUpgradeTypeBase {
			if p.giftUpgradeRecordMap[g.GiftID] == nil {
				assert.Equal(1, g.Lock)
			}
		}
	}

	p.baseGiftUpgrade.MoreInfo = &livegiftupgrade.MoreInfo{FirstUpgradeNum: 3, UpgradeNum: 10, LabelIcon: "icon"}
	p.giftUpgradeRecordMap[3] = &livegiftupgrade.GiftUpgradeRecord{BaseGiftID: 1, UpgradeGiftID: 3}
	p.giftUpgradeRecordMap[5] = &livegiftupgrade.GiftUpgradeRecord{BaseGiftID: 1, UpgradeGiftID: 5}
	p.userMetaGiftUpgrade = &usermeta.GiftUpgrade{
		GiftID: 1, SendCount: 3, // 送礼次数达到首次升级要求时，不下发折扣图标
	}
	resp, err = p.resp()
	require.NoError(err)
	require.NotNil(resp.Upgrade)
	require.Nil(resp.Upgrade.Discount)
	assert.Nil(resp.Upgrade.UpgradeNum)
	assert.Nil(resp.Upgrade.LuckyScore)
	assert.Len(resp.Upgrade.Gifts, 5)
	for _, g := range resp.Upgrade.Gifts {
		if g.UpgradeType != livegiftupgrade.GiftUpgradeTypeBase {
			if p.giftUpgradeRecordMap[g.GiftID] == nil {
				assert.Empty(g.Lock)
			}
		}
	}

	// 重置获取情况，能拿到折扣信息图标
	p.giftUpgradeRecordMap = make(map[int64]*livegiftupgrade.GiftUpgradeRecord)
	p.userMetaGiftUpgrade = nil
	resp, err = p.resp()
	require.NoError(err)
	require.NotNil(resp.Upgrade)
	assert.NotNil(resp.Upgrade.Discount)
}
