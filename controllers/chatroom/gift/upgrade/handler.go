package upgrade

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler .
func Handler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "upgrade",
		Actions: map[string]*handler.ActionV2{
			"":     handler.NewActionV2(handler.POST, ActionGiftUpgrade, handler.ActionOption{LoginRequired: true}),
			"info": handler.NewActionV2(handler.GET, ActionGiftUpgradeInfo, handler.ActionOption{LoginRequired: true}),
		},
	}
}
