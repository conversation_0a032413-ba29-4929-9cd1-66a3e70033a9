package upgrade

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGiftUpgrade(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	gifts := []interface{}{
		gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111, Attr: attr},
		gift.Gift{GiftID: 111112, Name: "升级礼物 111112", NameClean: "升级礼物 111112", Order: 111112},
		gift.Gift{GiftID: 111113, Name: "升级礼物 111113", NameClean: "升级礼物 111113", Order: 111113},
		gift.Gift{GiftID: 111114, Name: "升级礼物 111114", NameClean: "升级礼物 111114", Order: 111114},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 批量插入更新 gifts
	_, err := gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{111111, 111112, 111113, 111114}}})
	require.NoError(err)
	_, err = gift.Collection().InsertMany(ctx, gifts)
	require.NoError(err)

	// 写入礼物升级信息
	err = livegiftupgrade.DB().Delete(&livegiftupgrade.GiftUpgrade{}).Error
	require.NoError(err)
	moreInfo := livegiftupgrade.MoreInfo{
		FirstUpgradeNum:   3,
		UpgradeNum:        10,
		LuckyScoreTarget:  100,
		LuckyScorePerIncr: 10,
	}
	moreByte, err := json.Marshal(moreInfo)
	require.NoError(err)
	guRows := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111, PrizeID: 1, More: moreByte},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, PrizeID: 1, UpgradeGiftID: 111112},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, PrizeID: 2, UpgradeGiftID: 111113, Weight: 100},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, PrizeID: 2, UpgradeGiftID: 111114, Weight: 1},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRows)
	require.NoError(err)

	// 写入礼物获取信息
	guRecords := []livegiftupgrade.GiftUpgradeRecord{
		{BaseGiftID: 111111, UpgradeGiftID: 111113, UserID: userID, RoomID: 0},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgradeRecord{}.TableName(), guRecords)
	require.NoError(err)

	// 写入 usermeta 信息
	userMeta := usermeta.UserMeta{
		UserID: userID,
		GiftUpgrades: []*usermeta.GiftUpgrade{
			{GiftID: 111111, ToggleTime: 0, UpgradeNum: 3, LuckyScore: 100},
		},
	}
	_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": userID}, bson.M{"$set": userMeta}, options.Update().SetUpsert(true))
	require.NoError(err)

	param := &upgradeParam{
		RoomID: 223344,
		GiftID: 111111,
		userID: userID,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	resp, _, err := ActionGiftUpgrade(c)
	require.NoError(err)
	upgradeResp, ok := resp.(*upgradeResp)
	require.True(ok)
	require.NotNil(upgradeResp)
	// 根据测试数据，此次会获取新的升级礼物和终极礼物
	assert.Len(upgradeResp.Result, 2)
	assert.NotNil(upgradeResp.Upgrade)
	assert.Nil(upgradeResp.Upgrade.LuckyScore)
	assert.Nil(upgradeResp.Upgrade.UpgradeNum)
	assert.Len(upgradeResp.Upgrade.Gifts, 4)
	for _, g := range upgradeResp.Upgrade.Gifts {
		assert.Empty(g.Lock)
	}
}

func TestNewUpgradeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &upgradeParam{
		RoomID: 112233,
		GiftID: 0,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, p)
	param, err := newUpgradeParam(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	p = &upgradeParam{
		RoomID: 112233,
		GiftID: 2,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, p)
	param, err = newUpgradeParam(c)
	require.Equal(actionerrors.ErrCannotFindRoom, err)
	assert.Nil(param)

	p = &upgradeParam{
		RoomID: 223344,
		GiftID: 2,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, p)
	param, err = newUpgradeParam(c)
	require.Equal(actionerrors.ErrForbidden, err)
	assert.Nil(param)

	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	baseGift := gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111, Attr: attr}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.Collection().UpdateOne(ctx, bson.M{"gift_id": baseGift.GiftID}, bson.M{"$set": baseGift}, options.Update().SetUpsert(true))
	require.NoError(err)

	p = &upgradeParam{
		RoomID: 223344,
		GiftID: 111111,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, p)
	param, err = newUpgradeParam(c)
	require.Nil(err)
	assert.NotNil(param)
}

func TestUpgradeParam_upgrade(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	gifts := []interface{}{
		gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111, Attr: attr},
		gift.Gift{GiftID: 111112, Name: "升级礼物 111112", NameClean: "升级礼物 111112", Order: 111112},
		gift.Gift{GiftID: 111113, Name: "升级礼物 111113", NameClean: "升级礼物 111113", Order: 111113},
		gift.Gift{GiftID: 111114, Name: "升级礼物 111114", NameClean: "升级礼物 111114", Order: 111114},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 批量插入更新 gifts
	_, err := gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{111111, 111112, 111113, 111114}}})
	require.NoError(err)
	_, err = gift.Collection().InsertMany(ctx, gifts)
	require.NoError(err)

	// 写入礼物升级信息
	moreInfo := livegiftupgrade.MoreInfo{
		FirstUpgradeNum:   3,
		UpgradeNum:        10,
		LuckyScoreTarget:  100,
		LuckyScorePerIncr: 10,
	}
	moreByte, err := json.Marshal(moreInfo)
	require.NoError(err)
	guRows := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111, PrizeID: 1, More: moreByte},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, UpgradeGiftID: 111112, PrizeID: 1},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111113, Weight: 100, PrizeID: 2},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111114, Weight: 0, PrizeID: 2},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRows)
	require.NoError(err)

	// 写入 usermeta 信息
	userID := int64(123123)
	userMeta := usermeta.UserMeta{
		UserID: userID,
		GiftUpgrades: []*usermeta.GiftUpgrade{
			{GiftID: 111111, ToggleTime: 0},
		},
	}
	_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": userID}, bson.M{"$set": userMeta}, options.Update().SetUpsert(true))
	require.NoError(err)

	// 1. 没有升级次数时无法升级
	param := &upgradeParam{
		RoomID: 223344,
		GiftID: 111111,

		baseGift: &gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111},
	}
	err = param.upgrade()
	require.Equal(actionerrors.NewErrForbidden("暂时无法升级，请刷新直播间后重试"), err)

	// 2. 根据权重升级后，更新幸运值和升级次数
	userMeta.GiftUpgrades[0].UpgradeNum = 1
	_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": userID}, bson.M{"$set": userMeta}, options.Update().SetUpsert(true))
	require.NoError(err)
	// 最终会更新到 live_gift_upgrade_record 表中，提前清空数据
	err = livegiftupgrade.DB().Delete(livegiftupgrade.GiftUpgradeRecord{}).Error
	require.NoError(err)
	param = &upgradeParam{
		RoomID:   223344,
		GiftID:   111111,
		userID:   userID,
		baseGift: &gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111},
	}
	err = param.upgrade()
	require.NoError(err)
	require.NotNil(param.userMetaGiftUpgrade)
	assert.Equal(0, param.userMetaGiftUpgrade.LuckyScore)
	assert.Equal(0, param.userMetaGiftUpgrade.UpgradeNum)
	var guRecords []livegiftupgrade.GiftUpgradeRecord
	err = livegiftupgrade.DB().Find(&guRecords, "user_id = ?", param.userID).Error
	require.NoError(err)
	assert.Len(guRecords, 1)

	// 3. 更新权重和幸运值。使用户在幸运值满了的时候能够获取到权重小且未获取过的礼物
	userMeta.GiftUpgrades[0].UpgradeNum = 1
	userMeta.GiftUpgrades[0].LuckyScore = moreInfo.LuckyScoreTarget
	_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": userID}, bson.M{"$set": userMeta}, options.Update().SetUpsert(true))
	require.NoError(err)
	// 将另一个升级礼物的权重提升到 1，幸运值满了的时候能够获取到该礼物
	err = livegiftupgrade.DB().Model(livegiftupgrade.GiftUpgrade{}).Where("upgrade_gift_id = 111114").Update("weight", 1).Error
	require.NoError(err)
	param = &upgradeParam{
		RoomID:   223344,
		GiftID:   111111,
		userID:   userID,
		baseGift: &gift.Gift{GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111},
	}
	err = param.upgrade()
	require.NoError(err)
	// 幸运值和升级次数更新
	assert.Equal(0, param.userMetaGiftUpgrade.LuckyScore)
	assert.Equal(0, param.userMetaGiftUpgrade.UpgradeNum)
	assert.True(param.isNew)
	assert.True(param.isFullCollect)
	err = livegiftupgrade.DB().Find(&guRecords, "user_id = ?", param.userID).Error
	require.NoError(err)
	// 增加了新的升级礼物和终极礼物
	assert.Len(guRecords, 3)
}

func TestUpgradeParam_buildUpgradeGiftResult(t *testing.T) {
	assert := assert.New(t)

	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	param := &upgradeParam{
		giftIDMap: map[int64]*gift.Gift{
			111111: {GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111, Attr: attr},
			111112: {GiftID: 111112, Name: "升级礼物 111112", NameClean: "升级礼物 111112", Order: 111112},
			111113: {GiftID: 111113, Name: "升级礼物 111113", NameClean: "升级礼物 111113", Order: 111113},
			111114: {GiftID: 111114, Name: "升级礼物 111114", NameClean: "升级礼物 111114", Order: 111114},
		},
		giftUpgradeMap: map[int64]*livegiftupgrade.GiftUpgrade{
			111111: {Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111},
			111112: {Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, UpgradeGiftID: 111112},
			111113: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111113, Weight: 100},
			111114: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111114, Weight: 0},
		},
		prizeMap: map[int64]*liveprize.Prize{
			1: {ID: 1},
			2: {ID: 2},
		},
	}

	giftResult := param.buildUpgradeGiftResult(111113, &liveprize.Prize{}, true)
	assert.Equal(true, giftResult.IsNew)
	assert.Equal(livegiftupgrade.GiftUpgradeTypeUpgrade, giftResult.Gift.UpgradeType)
	assert.Equal(int64(111113), giftResult.Gift.GiftID)
}

func TestUpgradeParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	seriesName := "系列名"
	param := &upgradeParam{
		giftIDMap: map[int64]*gift.Gift{
			111111: {GiftID: 111111, Name: "升级礼物 111111", NameClean: "升级礼物 111111", Order: 111111},
			111112: {GiftID: 111112, Name: "升级礼物 111112", NameClean: "升级礼物 111112", Order: 111112},
			111113: {GiftID: 111113, Name: "升级礼物 111113", NameClean: "升级礼物 111113", Order: 111113},
			111114: {GiftID: 111114, Name: "升级礼物 111114", NameClean: "升级礼物 111114", Order: 111114},
		},
		giftUpgradeMap: map[int64]*livegiftupgrade.GiftUpgrade{
			111111: {Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 111111, MoreInfo: &livegiftupgrade.MoreInfo{
				FirstUpgradeNum:   3,
				UpgradeNum:        10,
				LuckyScoreTarget:  100,
				LuckyScorePerIncr: 10,
				SeriesName:        seriesName,
			}},
			111112: {Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 111111, UpgradeGiftID: 111112},
			111113: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111113, Weight: 100},
			111114: {Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 111111, UpgradeGiftID: 111114, Weight: 0},
		},
	}
	param.baseGiftUpgrade = param.giftUpgradeMap[111111]
	param.curGiftUpgrade = param.giftUpgradeMap[111113]
	param.prizeMap = map[int64]*liveprize.Prize{
		param.baseGiftUpgrade.PrizeID: {ID: param.baseGiftUpgrade.PrizeID},
		param.curGiftUpgrade.PrizeID:  {ID: param.curGiftUpgrade.PrizeID},
	}

	// 得到 1 个升级礼物
	resp, err := param.resp()
	require.NoError(err)
	assert.Len(resp.Result, 1)
	assert.Equal(seriesName, resp.Upgrade.SeriesName)
	assert.Equal(param.baseGiftUpgrade.MoreInfo.LuckyScoreTarget, resp.Upgrade.LuckyScore.Target)
	assert.Equal(param.baseGiftUpgrade.MoreInfo.LuckyScorePerIncr, resp.Upgrade.LuckyScore.PerIncrement)
	assert.Equal(param.baseGiftUpgrade.MoreInfo.FirstUpgradeNum, resp.Upgrade.UpgradeNum.UpgradeGiftNum)
	assert.Len(resp.Upgrade.Gifts, 4)

	// 得到升级礼物和最终礼物
	param.isFullCollect = true
	param.fullGiftUpgrade = param.giftUpgradeMap[111112]
	resp, err = param.resp()
	require.NoError(err)
	assert.Equal(seriesName, resp.Upgrade.SeriesName)
	assert.Len(resp.Result, 2)
	require.NotNil(resp.Upgrade)
	assert.Nil(resp.Upgrade.LuckyScore)
	assert.Nil(resp.Upgrade.UpgradeNum)
}
