package chatroom

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestHistoryGiftTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(historyRevenueParam{}, "marker", "gift_size")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(historyRevenueElem{}, "type", "time", "operator", "num", "gift_id",
		"name", "icon_url", "combo_id", "level", "price", "user")
	kc.Check(historyRevenueResp{}, "has_more", "marker", "data")
}

func TestActionHistoryRevenue(t *testing.T) {
	assert := assert.New(t)
	// addTestData()
	// defer deleteTestData()

	c := handler.NewTestContext("GET", "/history/revenue", true, nil)
	_, err := ActionHistoryRevenue(c)
	assert.Equal(actionerrors.ErrClosedRoom, err)

	c = handler.NewTestContext("GET", "/history/revenue", true, nil)
	c.User().ID = openingRoom.CreatorID
	// c.User().ID = testRoom.CreatorID
	_, err = ActionHistoryRevenue(c)
	assert.NoError(err)
}

func TestHistoryRevenueLoad(t *testing.T) {
	assert := assert.New(t)

	// 参数错误
	param := historyRevenueParam{}
	c := handler.NewTestContext("GET", "/history/revenue?marker=abc", true, nil)
	assert.Equal(actionerrors.ErrParams, param.load(c))
	// 不是主播
	param = historyRevenueParam{}
	c = handler.NewTestContext("GET", "/history/revenue", true, nil)
	c.User().ID = 147258369
	assert.Equal(actionerrors.ErrCannotFindRoom, param.load(c))
	// 测试房间未开启
	param = historyRevenueParam{}
	c = handler.NewTestContext("GET", "/history/revenue", true, nil)
	assert.Equal(actionerrors.ErrClosedRoom, param.load(c))
	// 正常情况
	param = historyRevenueParam{}
	c = handler.NewTestContext("GET", fmt.Sprintf("/api/v2/chatroom/history/revenue?marker=%d", goutil.NewTimeUnixMilli(goutil.TimeNow())-200), true, nil)
	c.User().ID = openingRoom.CreatorID
	assert.NoError(param.load(c))
	assert.NotZero(param.Marker)
	assert.NotZero(param.endTime)
	assert.True(param.enableNew)
}

func TestHistoryGiftFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := historyRevenueParam{
		endTime:  goutil.NewTimeUnixMilli(goutil.TimeNow()),
		room:     new(room.Room),
		GiftSize: 100,
	}
	param.room.RoomID = 18113499
	r, err := room.Find(param.room.RoomID)
	require.NoError(err)
	require.NotNil(r)
	lgs := []interface{}{livegifts.LiveGift{
		RoomOID:     r.OID,
		RoomID:      r.RoomID,
		UserID:      1,
		GiftID:      1,
		GiftNum:     1,
		Price:       12,
		UpdatedTime: goutil.TimeNow().Add(-24 * time.Hour),
		SentTime:    goutil.TimeNow().Add(-24 * time.Hour),
	}, livegifts.LiveGift{
		RoomOID:     r.OID,
		RoomID:      r.RoomID,
		UserID:      2,
		GiftID:      1,
		GiftNum:     1,
		Price:       12,
		UpdatedTime: goutil.TimeNow().Add(-24 * time.Hour),
		SentTime:    goutil.TimeNow().Add(-24 * time.Hour),
	}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livegifts.Collection().InsertMany(ctx, lgs)
	require.NoError(err)
	defer func() {
		_, err = livegifts.Collection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
		assert.NoError(err)
	}()
	assert.NoError(param.findGifts())
	assert.False(param.hasMore)
	assert.NotEmpty(param.gifts)
	require.GreaterOrEqual(len(param.gifts), 2)
	param.GiftSize = int64(len(param.gifts) - 1)
	assert.NoError(param.findGifts())
	assert.True(param.hasMore)
	assert.NotZero(param.nextEndTime)

	// 查找贵族
	assert.NotPanics(func() { param.findNoble() })
}

func TestHistoryRevenueParam_findGoodsOrders(t *testing.T) {
	assert := assert.New(t)

	var (
		testUserID = int64(12)
	)
	param := historyRevenueParam{
		nextMarker: "100",
		room:       new(room.Room),
		hasMore:    true,
		endTime:    goutil.NewTimeUnixMilli(goutil.TimeNow().Add(time.Minute)),
		enableNew:  true,
	}
	param.room.CreatorID = testUserID
	param.findGoodsOrders()
	assert.Empty(param.goodsOrders)

	param.gifts = make([]*livegifts.LiveGift, 1)
	param.findGoodsOrders()
	assert.NotEmpty(param.goodsOrders)
}

func TestHistoryRevenueParam_findQuestion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	param := historyRevenueParam{
		nextMarker: "100",
		room:       new(room.Room),
		gifts:      make([]*livegifts.LiveGift, 1),
		endTime:    goutil.NewTimeUnixMilli(now.Add(time.Minute)),
		enableNew:  false,
	}
	param.room.RoomID = testRoomID

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livequestion.Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)

	param.findQuestions()
	assert.Empty(param.questions)

	_, err = livequestion.Collection().InsertOne(ctx, &livequestion.LiveQuestion{
		Helper: livequestion.Helper{
			RoomID:        testRoomID,
			TransactionID: 111111,
			Status:        livequestion.StatusFinished,
			AnsweredTime:  &now,
		},
	})
	require.NoError(err)

	param.findQuestions()
	assert.Empty(param.questions)

	param.enableNew = true
	param.findQuestions()
	assert.NotEmpty(param.questions)
}

func TestHistoryGiftParam_newResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := historyRevenueParam{
		nextMarker: "100",
		room:       new(room.Room),
		hasMore:    true,
	}
	param.room.Status.OpenTime = 1
	now := goutil.TimeNow()
	param.gifts = []*livegifts.LiveGift{{
		GiftID:   301,
		UserID:   12,
		GiftNum:  10,
		SentTime: now.Add(-10 * time.Second),
	}, {
		GiftID:   3,
		UserID:   10,
		GiftNum:  2,
		SentTime: now.Add(-8 * time.Second),
	}}
	param.nobles = []*vip.MongoUserNobles{{
		Name:           "偶像",
		IconURL:        "http://static-test.maoercdn.com/live/noble/labels/mini/003.png",
		Level:          3,
		IsRegistration: 0,
		UserID:         10,
		CreatedTime:    now.Add(-9 * time.Second),
	}, {
		Level:          2,
		IsRegistration: 1,
		UserID:         1234567890, // 不存在的用户
		CreatedTime:    now.Add(-7 * time.Second),
	}}
	param.goodsOrders = []*livetxnorder.LiveTxnOrder{{
		GoodsType:  livegoods.GoodsTypeSuperFan,
		GoodsID:    1,
		SellerID:   10,
		BuyerID:    12,
		Attr:       1,
		CreateTime: now.Add(-11 * time.Second).Unix(),
	}, {
		GoodsType:  livegoods.GoodsTypeSuperFan,
		GoodsID:    1,
		SellerID:   10,
		BuyerID:    12,
		Attr:       2,
		CreateTime: now.Add(-12 * time.Second).Unix(),
	}, {
		GoodsType:  livegoods.GoodsTypeDanmaku,
		GoodsID:    2,
		SellerID:   10,
		BuyerID:    12,
		CreateTime: now.Add(-13 * time.Second).Unix(),
	}, {
		GoodsType:  livegoods.GoodsTypeLuckyBox,
		GoodsID:    3,
		SellerID:   10,
		BuyerID:    12,
		CreateTime: now.Add(-13 * time.Second).Unix(),
		More: &livetxnorder.MoreInfo{
			Num: 2,
		},
	}}
	answeredTime := now.Add(-14 * time.Second)
	param.questions = []*livequestion.LiveQuestion{{
		Helper: livequestion.Helper{
			RoomID:       1,
			Price:        10,
			Status:       livequestion.StatusFinished,
			AnsweredTime: &answeredTime,
		},
	}}
	resp, err := param.newResp()
	require.NoError(err)
	require.NotNil(resp)
	assert.True(resp.HasMore)
	assert.Equal("100", resp.Marker)
	require.Len(resp.Data, 9)
	require.NotNil(resp.Data[0].User)
	assert.Equal("神秘人", resp.Data[0].User.Username)
	var (
		operators   [9]string
		nums        [9]int64
		nobleLevels [9]int
		names       [9]string
	)
	for i := range resp.Data {
		operators[i] = resp.Data[i].Operator
		nums[i] = resp.Data[i].Num
		nobleLevels[i] = resp.Data[i].Level
		names[i] = resp.Data[i].Name
	}
	assert.Equal([9]string{"开通", "赠送", "续费", "赠送", "开通", "续费", "发送", "抽取", ""}, operators)
	assert.Equal([9]int64{1, 2, 1, 10, 1, 1, 1, 2, 0}, nums)
	assert.Equal([9]int{2, 0, 3, 0, 0, 0, 0, 0, 0}, nobleLevels)
	assert.Equal([9]string{"", "肥皂", "偶像", "猫粮", "", "", "", "典藏宝盒礼物", ""}, names)
	tutil.PrintJSON(resp)
}
