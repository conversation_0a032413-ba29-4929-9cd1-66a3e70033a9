package chatroom

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type backpackSendParam struct {
	RoomID     int64 `form:"room_id" json:"room_id"`
	FromRoomID int64 `form:"from_room_id" json:"from_room_id"`
	GiftID     int64 `form:"gift_id" json:"gift_id"`
	GiftNum    int   `form:"gift_num" json:"gift_num"`

	userID int64
	c      *handler.Context
	uc     mrpc.UserContext
	r      *room.Room
	bubble *bubble.Simple
	g      *gift.Gift
	u      *liveuser.Simple
	uv     *vip.UserVip
	lg     *livegifts.LiveGift

	// goroutine 中使用
	userCtx        userapi.UserContext
	broadcastElems []*userapi.BroadcastElem
}

type backpackSendGiftResp struct {
	Ok      int              `json:"ok"`
	GiftID  int64            `json:"gift_id"`
	Remain  *int64           `json:"remain,omitempty"`
	User    *liveuser.Simple `json:"user,omitempty"`
	Bubble  *bubble.Simple   `json:"bubble,omitempty"`
	Message string           `json:"message,omitempty"`
	// Combo   comboInfo        `json:"combo"` // 连击，目前背包没有连击
}

// ActionBackpackSend 送出背包中的礼物
/**
 * @api {post} /api/v2/chatroom/backpack/send 送出背包中的礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 实际收礼直播间 ID
 * @apiParam {Number} [from_room_id=0] 用户送礼所在的直播间 ID，不传默认用户在实际收礼的直播间内送礼
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 礼物数量
 *
 * @apiSuccessExample {json} 送礼成功响应
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1,
 *         "gift_id": 301,
 *         "remain": 10, // 该礼物剩余数量
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *           "titles": [{
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *           }, {
 *             "type": "noble",
 *             "name": "新秀",
 *             "level": 2
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }]
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *           "type": "noble", // 气泡类型，目前支持: 贵族气泡 noble
 *           "noble_level": 2 // 使用对应等级的贵族气泡
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 送礼数量不足响应
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 0,
 *         "remain": 10, // 该礼物剩余数量
 *         "message": "礼物数量不足哦~"
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 送礼成功房间内消息
 *     {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "药丸",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/001.png",
 *         // 如果下发的消息没有 effect_url，需要支持从 meta/data 存储的数据中查询出对应的 effect_url 和 effect_duration
 *         "effect_url": "https://static-test.missevan.com/gifts/effects/001.lottie", // 礼物特效（有特效的礼物才有这个字段），客户端需要支持 lottie、svga 和 webp 格式
 *         "effect_duration": 5000, // 特效时长，如果有就使用。对于无法计算出时长的特效且无法获取出 effect_duration (包括没有在 meta/data) 数据的，使用默认时长 5000ms
 *         "price": 0, // 免费礼物价格
 *         "num": 1
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static.maoercdn.com/live/bubble/image/001.png",
 *         "frame_url": "https://static.maoercdn.com/live/bubble/frame/001.png",
 *         "text_color": "#F0F0F0"
 *       },
 *       "current_revenue": 1 // 本场榜中此用户的贡献值
 *     }
 *
 */
/* 暂时没有的全局消息
 * @apiSuccessExample {json} WebSocket 全局消息
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "gift": {
 *         "gift_id": 11,
 *         "name": "更贵的城堡",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/010.png",
 *         "price": 20000,
 *         "num": 1,
 *         "effect_url": "https://static-test.missevan.com/gifts/effects/010-mobile.svga"
 *       },
 *       "message": "<b>bless</b> 给 <b>绵绵思远道い</b> 送出 <b>1 个更贵的城堡</b>，快来围观吧~"
 *     }
 */
func ActionBackpackSend(c *handler.Context) (handler.ActionResponse, error) {
	var param backpackSendParam
	resp, err := param.load(c)
	if err != nil || resp != nil {
		return resp, err
	}
	return param.send()
}

func (param *backpackSendParam) load(c *handler.Context) (*backpackSendGiftResp, error) {
	err := c.Bind(param)
	if err != nil ||
		param.RoomID <= 0 || param.GiftID <= 0 || param.GiftNum <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	param.uc = c.UserContext()
	param.userID = c.UserID()
	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.r.Limit != nil {
		return nil, actionerrors.NewErrForbidden("本直播间内无法赠送该礼物")
	}
	if param.r.CreatorID == param.userID {
		return nil, actionerrors.ErrParamsMsg("无法给自己的直播间送礼物")
	}
	// NOTICE: 猫耳娘的零钱袋是活动发奖账号，不受黑名单的限制
	if param.userID != userstatus.MaoerWalletUserID {
		// 被主播拉黑无法送礼
		blocked, err := blocklist.IsBlocked(param.r.CreatorID, param.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return nil, actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
		}
	}
	param.g, err = gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.g == nil {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}
	if param.g.IsHotCard() && !param.r.IsOpen() {
		return nil, actionerrors.ErrDisableUseHotCardInCloseRoom
	}
	// 判断是否允许跨直播送礼
	if err = checkAllowCrossSend(param.FromRoomID, param.RoomID); err != nil {
		return nil, err
	}

	switch param.g.Type {
	case gift.TypeFree, gift.TypeRebate:
	default:
		return &backpackSendGiftResp{
			Ok:      0,
			GiftID:  param.GiftID,
			Remain:  util.NewInt64(0),
			Message: "遭遇到了意想不到的错误哦~",
		}, nil
	}
	if len(param.g.AllowedNums) != 0 &&
		!goutil.HasElem(param.g.AllowedNums, -1) &&
		!goutil.HasElem(param.g.AllowedNums, param.GiftNum) {
		return nil, actionerrors.ErrGiftNum
	}

	count, err := useritems.CountGiftNum(param.userID, param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if count < int64(param.GiftNum) {
		return &backpackSendGiftResp{
			Ok:      0,
			GiftID:  param.GiftID,
			Remain:  util.NewInt64(count),
			Message: "礼物数量不足哦~",
		}, nil
	}

	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, &liveuser.FindOptions{
		FindTitles: true,
		RoomID:     param.RoomID,
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	_, param.uv, err = userstatus.UserGeneral(c.UserID(), c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	param.bubble, err = userappearance.FindMessageBubble(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	param.userCtx = userapi.NewUserContext(c)
	return nil, nil
}

func (param *backpackSendParam) send() (*backpackSendGiftResp, error) {
	sender := useritems.GiftSender{
		UserID:    param.userID,
		RoomID:    param.RoomID,
		CreatorID: param.r.CreatorID,
		Gift:      param.g,
		Num:       int64(param.GiftNum),
		OpenLogID: param.r.Status.OpenLogID,
		C:         param.c,
	}
	ok, count, tids, err := sender.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if !ok {
		resp := &backpackSendGiftResp{
			Ok:     0,
			GiftID: param.GiftID,
			Remain: &count,
		}
		if count < int64(param.GiftNum) {
			resp.Message = "礼物数量不足哦~"
		} else {
			// 异常错误
			resp.Message = "遭遇到了意想不到的错误哦~"
		}
		return resp, nil
	}

	param.lg = livegifts.
		NewLiveGifts(param.r.OID, param.RoomID, param.u, param.bubble).
		SetGift(param.g, int64(param.GiftNum)).
		SetRoomOpenStatus(param.r.IsOpen()).
		SetTransactionIDs(tids...)
	_, err = livegifts.UpdateSave(param.lg, nil, primitive.NilObjectID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	goutil.Go(func() { // TODO: 整合优化其他送礼后的相关操作
		param.addRevenueRank()
		param.addPK()
		param.addMedalPoint()
		param.addMultiConnectScore()
		param.activatedGiftWall()
		param.buildIMMessage()
		param.broadcast()

		param.addActivity()
		param.addLiveShow()
		param.addRoomPaidUser()
	})
	return &backpackSendGiftResp{
		Ok:     1,
		GiftID: param.GiftID,
		Remain: &count,
		User:   param.u,
		Bubble: param.bubble,
	}, nil
}

func (param *backpackSendParam) addRevenueRank() {
	price := param.g.Price * int64(param.GiftNum)
	var point int64
	if param.g.AllowPointAddRank() {
		point = param.g.Point * int64(param.GiftNum)
	}
	score := price + point
	if score == 0 {
		return
	}
	err := roomsrank.AddRevenue(param.r.RoomID, param.userID, score, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	if price == 0 {
		err = liverevenues.AddPoint(param.userID, param.r.OID, param.RoomID, point)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return
	}
	err = liverevenues.AddGiftRevenue(param.userID, param.r.OID, param.RoomID, price, point)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = param.r.ReceiveGift(param.GiftNum, param.g.Price)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *backpackSendParam) addPK() {
	score, freeScore := param.g.PKScores(param.GiftNum)
	elems, err := livepk.AddPKScore(param.r, param.userID, score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *backpackSendParam) addMedalPoint() {
	if param.r.Medal == nil {
		return
	}
	medalPoint := param.g.MedalPoint(param.GiftNum)
	if medalPoint == 0 {
		return
	}
	var err error
	var medalUpdatedInfo *livemedal.MedalUpdatedInfo
	switch param.g.Type {
	case gift.TypeFree:
		// 免费礼物
		addParam := &livemedal.AddFreePointParam{
			RoomID:    param.RoomID,
			CreatorID: param.r.CreatorID,
			UserID:    param.userID,
			PointAdd:  medalPoint,
			Scene:     livemedalpointlog.SceneTypeFreeGift,
		}
		medalUpdatedInfo, err = addParam.AddFreePoint()
	case gift.TypeRebate:
		medalParam := livemedal.AddPointParam{
			RoomOID:    param.r.OID,
			RoomID:     param.r.RoomID,
			CreatorID:  param.r.CreatorID,
			FromRoomID: param.FromRoomID,
			UserID:     param.userID,
			UV:         param.uv,
			MedalName:  param.r.Medal.Name,
			Type:       livemedal.TypeGiftAddMedalPoint,
			Source:     livemedal.ChangeSourceGift,
			PointAdd:   medalPoint,
			Scene:      livemedalpointlog.SceneTypePayGift,
		}
		medalUpdatedInfo, err = medalParam.AddPoint()
	}
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.u,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *backpackSendParam) addMultiConnectScore() {
	if !param.r.IsMultiConnect() {
		return
	}
	elems, err := livemulticonnect.ScoreHelper{
		Room: param.r,
		Gift: param.g,
		Num:  int64(param.GiftNum),
	}.AddScore()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *backpackSendParam) activatedGiftWall() {
	revenue := param.g.Price * int64(param.GiftNum)
	notifyElem, err := giftwall.ActiveGift(param.r, param.userID, param.g.GiftID, revenue, param.GiftNum)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

func (param *backpackSendParam) buildIMMessage() {
	price := param.g.Price * int64(param.GiftNum)
	sendNotify := param.g.AlwaysNotify() || price >= gift.ComboNotifyMinPrice

	// 房间送礼消息
	param.broadcastElems = append(param.broadcastElems,
		param.lg.BuildBroadcastMessage(!sendNotify && param.r.FilterGiftMessage()),
	)
	if param.FromRoomID != 0 {
		// 跨房间送礼消息
		param.broadcastElems = append(param.broadcastElems,
			param.lg.BuildCrossRoomBroadcastMessage(param.FromRoomID, param.r),
		)
	}

	// 全站飘屏
	if !sendNotify {
		// 未设置成总是飘屏和价值不够
		return
	}

	nb := gift.NotifyBuilder{
		RoomID:          param.RoomID,
		CreatorUsername: param.r.CreatorUsername,
		User:            param.u,
		Gift:            param.g,
		GiftNum:         param.GiftNum,
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  param.RoomID,
		Payload: nb.Build(),
	})
}

// broadcast 发送 im 消息
func (param backpackSendParam) broadcast() {
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *backpackSendParam) addActivity() {
	if param.g.Type == gift.TypeRebate && param.g.Price > 0 {
		box.SendQuestMessage(param.r.RoomID, param.r.CreatorID, box.QuestTypeGift, param.g.Price*int64(param.GiftNum))
	}

	r := rank.NewSyncParam(param.r.RoomID, param.u.UserID(), param.r.CreatorID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGift(param.g, param.GiftNum)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc)
}

func (param *backpackSendParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.RoomID, param.u.UserID(), param.r.CreatorID).
		SetGift(param.g.GiftID, param.g.Price, param.GiftNum).
		Sync()
}

func (param *backpackSendParam) addRoomPaidUser() {
	// 只统计白给礼物，背包礼物只有白给礼物和免费礼物两种类型
	if param.g.Type != gift.TypeRebate {
		return
	}
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.userID, param.r.Status.OpenTime)
}
