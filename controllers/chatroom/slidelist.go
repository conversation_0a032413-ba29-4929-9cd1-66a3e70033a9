package chatroom

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const slideRoomPipelineLimit = 500 // 生成随机房间号 pipeline 中 limit 的最大数量
const slideRoomsNum = 10

type slideResp struct {
	RoomID          int64            `json:"room_id"`
	Name            string           `json:"name"`
	CreatorID       int64            `json:"creator_id"`
	CreatorUsername string           `json:"creator_username"`
	Status          *room.Status     `json:"status,omitempty"`
	CoverURL        string           `json:"cover_url,omitempty"`
	CreatorIconURL  string           `json:"creator_iconurl,omitempty"`
	Background      *room.Background `json:"background,omitempty"`
}

var slideProjection = mongodb.NewProjection("room_id, name, creator_id, creator_username,  status, cover, background")

// ActionSlideList 获取滑动的房间信息列表
/**
 * @api {get} /api/v2/chatroom/slide/list 获取滑动的房间信息列表
 * @apiDescription 首次进入直播间和切换直播间数组剩余未曝光的直播间数量 <= 5 个时，请求该接口，客户端需要对返回的结果去重。
 *                 去重后没有新房间号时，切换至房间数组的第一个房间，开始循环，剩余未曝光数量 <= 5 个时再次请求。
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [catalog_id] 分区 ID, 从分区进入直播间时需传递该参数
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "room_id": 122341,
 *         "name": "测试",
 *         "creator_id": 10,
 *         "creator_username": "bless",
 *         "creator_iconurl": "https://static-test.missevan.com/avatars/icon01.png",
 *         "status": {
 *           "open": 1,
 *           "open_log_id": "5d7f7b3b7b8b4b0001b3b3b3"
 *           ...
 *         },
 *         "cover_url": "https://static-test.missevan.com/avatars/icon01.png",
 *         "background": { // 无 background 字段 或 enable 为 false 时、使用客户端默认图
 *           "enable": true,
 *           "opacity": 0.6785714030265808,
 *           "image_url": "https://static-test.missevan.com/fmbackgrounds/202004/28/80c416e82402f2a178bdabe8bc2496ca.png"
 *         }
 *       },
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500, 100010002
 * @apiError (500) {String} info 服务器内部错误，数据库错误
 *
 */
func ActionSlideList(c *handler.Context) (handler.ActionResponse, error) {
	catalogID, err := c.GetDefaultParamInt64("catalog_id", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	ninRoomIDs := room.OpenListExcludeRoomIDs()
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}

	if catalogID != 0 {
		catalogIDs, err := catalog.SubCatalogIDs(catalogID, true)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if len(catalogIDs) == 0 {
			return []*slideResp{}, nil
		}
		filter["catalog_id"] = bson.M{"$in": catalogIDs}
	}
	var excludeRoomID int64
	// 排除主播自己的直播间
	if c.UserID() != 0 {
		excludeRoomID, err = room.FindRoomID(c.UserID())
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if excludeRoomID != 0 {
			ninRoomIDs = append(ninRoomIDs, excludeRoomID)
		}
	}
	filter["room_id"] = bson.M{"$nin": ninRoomIDs}
	collection := service.MongoDB.Collection(room.CollectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 获取总数前一部分记录条数
	count = slideFindCount(count)
	if count == 0 {
		return []*slideResp{}, nil
	}
	pipeline := []bson.M{{
		"$match": filter,
	}, {
		"$sort": room.SortByScore,
	}, {
		"$limit": count,
	}, {
		"$sample": bson.M{"size": 20},
	}, {
		"$project": slideProjection,
	},
	}

	rooms, err := room.AggregateSimples(pipeline, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	extraRooms := findRecommendAndTop(c.UserID(), excludeRoomID, catalogID, goutil.TimeNow())
	rooms = append(rooms, extraRooms...)
	if len(rooms) <= 0 {
		return []*slideResp{}, nil
	}
	// 去重
	roomMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Simple)
	var result []*room.Simple
	for _, simple := range roomMap {
		result = append(result, simple)
	}
	result = result[:min(int64(len(result)), slideRoomsNum)]

	resp := make([]*slideResp, len(result))
	for i := 0; i < len(result); i++ {
		resp[i] = &slideResp{
			RoomID:          result[i].RoomID,
			Name:            result[i].Name,
			CreatorID:       result[i].CreatorID,
			CreatorUsername: result[i].CreatorUsername,
			Status:          result[i].Status,
			CoverURL:        result[i].CoverURL,
			CreatorIconURL:  result[i].CreatorIconURL,
			// REVIEW: 考虑下发推荐背景图
			Background: result[i].Background,
		}
	}

	return resp, nil
}

func slideFindCount(totalCount int64) int64 {
	var scoreLimit float64
	config.GetAB("slide_score_limit", &scoreLimit)
	if scoreLimit <= 0 {
		// 默认取前 80%
		scoreLimit = 0.8
	}
	count := int64(float64(totalCount) * scoreLimit)
	if limit := min(slideRoomPipelineLimit, totalCount); count > limit {
		count = limit
	}
	return count
}

func newFilter(recommends, topUsers []int64, catalogID int64) bson.M {
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
	}
	if catalogID != 0 {
		filter["catalog_id"] = catalogID
	}
	reLen, topLen := len(recommends), len(topUsers)
	if reLen > 0 && topLen > 0 {
		filter["$or"] = bson.A{
			bson.M{"creator_id": bson.M{"$in": topUsers}},
			bson.M{"room_id": bson.M{"$in": recommends}},
		}
	} else if reLen > 0 {
		filter["room_id"] = bson.M{"$in": recommends}
	} else if topLen > 0 {
		filter["creator_id"] = bson.M{"$in": topUsers}
	} else {
		filter = nil
	}

	return filter
}

func findRecommendAndTop(userID, excludeUserRoomID, catalogID int64, when time.Time) []*room.Simple {
	// 获取神话推荐和排期推荐
	recommends, err := liverecommendedelements.ListSchedule(when)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	roomIDs := make([]int64, 0, len(recommends))
	for i := 0; i < len(recommends); i++ {
		if recommends[i].ElementID != excludeUserRoomID {
			roomIDs = append(roomIDs, recommends[i].ElementID)
		}
	}
	// 获取前小时榜 top3
	top3, _, err := usersrank.FindHourRank(when, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	topUserIDs := make([]int64, 0, len(top3))
	for i := 0; i < len(top3); i++ {
		if top3[i].UserID != userID {
			topUserIDs = append(topUserIDs, top3[i].UserID)
		}
	}
	filter := newFilter(roomIDs, topUserIDs, catalogID)
	if filter == nil {
		return nil
	}

	rooms, err := room.ListSimples(filter, options.Find().SetProjection(slideProjection), &room.FindOptions{FindCreator: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return rooms
}
