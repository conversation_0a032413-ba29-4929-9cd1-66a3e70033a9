package chatroom

import (
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type actionLogsParam struct {
	p        int64
	pageSize int64

	guildID  int64
	fromTime time.Time
	toTime   time.Time
	roomOID  primitive.ObjectID

	resp *actionLogsResp
}

// load loads and checks parameters
func (param *actionLogsParam) load(c *handler.Context) error {
	user := c.User()
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	startDate, endDate, err := c.GetParamDateRange(goutil.TimeNow().AddDate(0, -1, 0).Format("2006-01-02"),
		goutil.TimeNow().Format("2006-01-02"))
	if err != nil {
		return actionerrors.ErrParams
	}
	param.fromTime = startDate
	param.toTime = endDate.AddDate(0, 0, 1)

	roomID, err := strconv.ParseInt(c.C.Param("roomID"), 10, 64)
	if err != nil {
		return actionerrors.ErrParams
	}

	if roomID <= 0 {
		return actionerrors.ErrParams
	}

	var room struct {
		OID       primitive.ObjectID `bson:"_id"`
		CreatorID int64              `bson:"creator_id"`
	}
	err = utils.GetRoom(roomID, &room, "creator_id")
	if err != nil {
		if !mongodb.IsNoDocumentsError(err) {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return actionerrors.ErrCannotFindRoom
	}
	param.roomOID = room.OID
	if room.CreatorID != user.ID {
		isGuildManager, lc, err := CheckOwnerOrAgentTimeRange(room.CreatorID, c.UserID(), &param.fromTime, param.toTime)
		// 访问用户角色可能为直播管理员或直播运营，
		// 忽略 ErrCreatorNotInYourGuild 错误，单独进行用户角色判断
		if err != nil && err != actionerrors.ErrCreatorNotInYourGuild {
			return err
		}
		if isGuildManager {
			param.guildID = lc.GuildID
		} else {
			isStaff, err := user.IsRole(role.LiveAdmin, role.LiveOperator)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if !isStaff {
				return actionerrors.ErrNoAuthority
			}
		}
	}
	return nil
}

// actionLogsResp is the result of ActionLogs
type actionLogsResp struct {
	Aggs       *livelog.UserLiveAggs `json:"aggs,omitempty"`
	Data       []livelog.Record      `json:"data"`
	Pagination goutil.Pagination     `json:"pagination"`
}

// process processes ActionLogsTask
func (param *actionLogsParam) process() error {
	var (
		resp actionLogsResp
		logs []livelog.Record
	)

	count, err := livelog.CountRoomLogs(param.roomOID, param.fromTime, param.toTime, param.guildID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	pa := goutil.MakePagination(count, param.p, param.pageSize)
	if pa.Valid() {
		logs, err = livelog.GetRoomLogsByPage(param.roomOID, param.fromTime, param.toTime, param.guildID, pa)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		lcs, err := catalog.AllLiveCatalogsWithSubMap(true)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			for i := range logs {
				if lc := lcs[logs[i].CatalogID]; lc != nil {
					logs[i].CatalogName = lc.CatalogName
				}
			}
		}
		// 对于第一页才需要这个数据
		if pa.P == 1 {
			aggs, err := livelog.GetRoomLogsAggs(param.roomOID, param.fromTime, param.toTime, param.guildID)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			resp.Aggs = &aggs
		}
	} else {
		logs = make([]livelog.Record, 0)
	}

	resp.Data = logs
	resp.Pagination = pa

	param.resp = &resp
	return nil
}

// ActionLogs 返回房间的 live logs 和统计信息
/**
 * @api {get} /api/v2/chatroom/logs/:roomID 返回房间的 live logs 和统计信息
 * @apiDescription 主播自己、直播管理员、直播运营、公会会长和主播的经纪人可以查看，
 *   当公会会长和主播的经纪人访问时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 * @apiParam {String} [start_date=一个月前] 筛选的起始时间，例如 "2006-01-02"
 * @apiParam {String} [end_date=今天] 筛选的结束时间，例如 "2006-01-02"
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "aggs": {
 *           "total_duration": 23576, // 总时长
 *           "total_revenue": 0, // 总收益
 *           "total_accumulation": 1, // 总累计人数
 *           "days": 0, // 有效天
 *           "daily_avg_accumulation": 0 // 在筛选的日期范围内，平均每天的累计人数
 *         },
 *         "data": [
 *           {
 *             "_id": "5ab9d5f1bc9b53298ce5a5a9",
 *             "_room_id": "5ab9d5d9bc9b53298ce5a5a5",
 *             "room_id": 22489473,
 *             "creator_id": 10,
 *             "catalog_id": 105,
 *             "catalog_name": "分类名称",
 *             "guild_id": 3,
 *             "start_time": 1522128345942,
 *             "end_time": 1522128369518,
 *             "duration": 23576,
 *             "revenue": 0,
 *             "accumulation": 1, // 累计人数
 *             "question_count": 0,
 *             "message_count": 0,
 *             "avg_score": 1, // 平均热度
 *             "max_score": 1 // 最高热度
 *           }
 *         ],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (400) code 501010000 参数错误
 * @apiError (401) code 500020001 用户未登录或登录已过期
 * @apiError (403) code 200020003 用户没有权限
 * @apiError (404) code 200020004 无法找到该聊天室
 *
 * @apiError (500) code 100010007 数据库错误
 */
func ActionLogs(c *handler.Context) (handler.ActionResponse, error) {
	var param actionLogsParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}
	err = param.process()
	if err != nil {
		return nil, err
	}
	return param.resp, nil
}
