package chatroom

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/widget"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLiveShowWidget(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("get", "?room_id=141317526", false, nil)
	resp, err := actionLiveShowWidget(c)
	assert.NoError(err)
	assert.NotNil(resp)
}

func TestWidgetParamCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := widgetParam{RoomID: 100}
	err := p.checkRoom()
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	p.RoomID = 141317526
	err = p.checkRoom()
	require.NoError(err)
	require.NotNil(p.r)

	key := keys.KeyOngoingLiveShow0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	resp, err := p.checkLiveShow()
	require.NoError(err)
	assert.Equal(&widgetResp{Widget: &widget.Widget{
		Status:          0,
		TS:              goutil.TimeNow().Unix(),
		RefreshDuration: 0,
	}}, resp)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(9000, 0)
	})
	defer goutil.SetTimeNow(nil)
	require.NoError(service.LRURedis.Del(key).Err())

	_, err = p.checkLiveShow()
	require.NoError(err)
	require.NotNil(p.s)
}
