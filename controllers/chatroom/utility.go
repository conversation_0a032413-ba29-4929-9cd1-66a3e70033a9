package chatroom

import (
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// isRoomPushing 检查房间是否实际在推流
// 当前时间与房间推流开启时间间隔足够长时，结果认为是可信的
// 返回值 reliable 为 false 不可信时，调用方不需要更新数据库状态
func isRoomPushing(r *room.Room) (open, reliable bool, err error) {
	if r.Channel.Custom != nil && r.Channel.Custom.PushURL != "" {
		open, reliable = r.Status.Channel.Type == room.TypeOpen, false
		return
	}
	switch r.Channel.Provider {
	/*
		case room.ChannelProviderNetease:
			if r.Channel.CID == nil {
				logger.WithField("room_id", r.RoomID).Error("cid is empty")
				open, reliable = true, false
				break
			}
			open, err = service.NeteaseLive.ChannelOpen(*r.Channel.CID)
			if err == nil &&
				util.TimeToUnixMilli(goutil.TimeNow().Add(-tenSecond)) > r.Status.Channel.Time {
				reliable = true
			}
	*/
	case room.ChannelProviderAliyun:
		var requestID string
		open, requestID, err = service.AliyunLive.ChannelOpen(r.RoomID)
		if err == nil &&
			util.TimeToUnixMilli(goutil.TimeNow().Add(-fiveMinute)) > r.Status.Channel.Time {
			reliable = true
			if !open && r.IsPushing() {
				logger.WithFields(logger.Fields{
					"room_id":    r.RoomID,
					"request_id": requestID,
				}).Warn("Aliyun Live no stream bitrate in open room")
			}
		}
	case room.ChannelProviderKsyun, "":
		var requestID string
		open, requestID, err = service.KsyunLive.ChannelOpen(r.RoomID, r.CreatorID)
		if err != nil && requestID != "" {
			logger.WithFields(logger.Fields{
				"room_id":    r.RoomID,
				"request_id": requestID,
			}).Errorf("Ksyun KLS request error: %v", err)
		}
		if err == nil &&
			util.TimeToUnixMilli(goutil.TimeNow().Add(-tenSecond)) > r.Status.Channel.Time {
			reliable = true
		}
	case room.ChannelProviderBvc:
		var sk string
		open, sk, err = service.BvcLive.ChannelOpen(r.RoomID, r.CreatorID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"room_id": r.RoomID,
			}).Errorf("BVC Live request error: %v", err)
			// PASS
		}
		if err == nil {
			if util.TimeToUnixMilli(goutil.TimeNow().Add(-thirtySecond)) > r.Status.Channel.Time {
				reliable = true
			}
			err = r.SaveChannelBVCSK(sk)
			if err != nil {
				logger.WithField("room_id", r.RoomID).Errorf("save bvc sk error: %v", err)
				// PASS
			}
		}
	default:
		// 未知 provider
		open, reliable = true, false
	}

	return
}

func addRankByPoint(r *room.Room, userID, point int64) {
	err := roomsrank.AddRevenue(r.RoomID, userID, point, goutil.IntToBool(r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(r.CreatorID, r.RoomID, point)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddPoint(userID, r.OID, r.RoomID, point)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
