package liveshow

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/reward/task"
	"github.com/MiaoSiLa/live-service/service"
)

// Star 繁星挑战
type Star struct {
	*LiveShow
}

func (s *Star) feedRank() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	err := liveshow.Collection().FindOneAndUpdate(ctx,
		bson.M{"_id": s.OID},
		s.buildUpdate(),
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&s.LiveShow.LiveShow)
	if err != nil {
		return err
	}

	return nil
}

func (s *Star) collectTask() task.Tasker {
	return task.NewNodeTask(
		// 直播间挂件 ID：60004 有效期：7d
		task.NewScoreTask(5, int64(s.collectGiftNum()), int64(s.addCollectGiftNum), reward.RewardID20),
	)
}

func (s *Star) pointTask() task.Tasker {
	return task.NewNodeTask(
		// 3k 热度卡 × 1 ID：30003 有效期：30d
		task.NewScoreTask(21314, s.Point, s.addPoint, reward.RewardID1),
		// 6k 热度卡 × 1  ID：30001 有效期：30d
		task.NewScoreTask(66666, s.Point, s.addPoint, reward.RewardID2),
		// 10k 热度卡 × 1 ID：30002 有效期：30d
		task.NewScoreTask(99999, s.Point, s.addPoint, reward.RewardID3),
	)
}

func (s *Star) superFanTask() task.Tasker {
	return task.NewNodeTask(
		// 头像框 ID：40109 有效期：3d
		task.NewScoreTask(1, int64(s.collectSuperFanNum()), int64(s.addSuperFanNum), reward.RewardID4),
		// 称号 ID：50047 有效期：3d
		task.NewScoreTask(2, int64(s.collectSuperFanNum()), int64(s.addSuperFanNum), reward.RewardID5),
		// 直播间封面角标 角标地址：live/labelicon/livelist/liveshow-1 有效期：3d
		task.NewScoreTask(3, int64(s.collectSuperFanNum()), int64(s.addSuperFanNum), reward.RewardID6),
	)
}

func (s *Star) task() {
	// 完成全部任务猫耳娘送礼 ID: 40047
	task := task.NewNormalTask(s.collectTask(), s.pointTask(), s.superFanTask()).SetRewards(reward.RewardID8)
	s.reward(task.RewardIDs()...)
}
