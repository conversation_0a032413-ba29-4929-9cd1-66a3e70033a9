package liveshow

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveshowusers"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/reward/task"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Moon 星月挑战
type Moon struct {
	*LiveShow
}

func (m *Moon) feedRank() error {
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		update := m.buildUpdate()
		err := liveshow.Collection().FindOneAndUpdate(ctx,
			bson.M{"_id": m.OID},
			update,
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).Decode(&m.LiveShow.LiveShow)
		if err != nil {
			return err
		}

		// 用户贡献只统计任务期间的贡献，任务完成后不再更新用户贡献值
		task := task.NewNormalTask(m.collectTask(), m.pointTask(), m.superFanTask())
		if task.CheckComplete() && !task.CheckIsFirstComplete() {
			return nil
		}

		// FIXME: 需要调整为只算有效贡献的部分
		err = liveshowusers.Collection().FindOneAndUpdate(ctx,
			bson.M{
				"live_show_id": m.OID,
				"user_id":      m.fromUserID,
			},
			update,
			options.FindOneAndUpdate().SetUpsert(true),
		).Err()
		if err != nil && !mongodb.IsNoDocumentsError(err) {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (m *Moon) collectTask() task.Tasker {
	return task.NewNodeTask(
		// 直播间挂件 ID：60005 有效期：15d
		task.NewScoreTask(10, int64(m.collectGiftNum()), int64(m.addCollectGiftNum), reward.RewardID21),
	)
}

func (m *Moon) pointTask() task.Tasker {
	return task.NewNodeTask(
		// 10k 热度卡 × 1 ID：30002 有效期：30d
		task.NewScoreTask(100000, m.Point, m.addPoint, reward.RewardID3),
		// 10k 热度卡 × 3 ID：30002 有效期：30d
		task.NewScoreTask(360000, m.Point, m.addPoint, reward.RewardID9),
		// 10k 热度卡 × 6 ID：30002 有效期：30d
		task.NewScoreTask(680000, m.Point, m.addPoint, reward.RewardID10),
	)
}

func (m *Moon) superFanTask() task.Tasker {
	return task.NewNodeTask(
		// 头像框 ID：40110 有效期：7d
		task.NewScoreTask(3, int64(m.collectSuperFanNum()), int64(m.addSuperFanNum), reward.RewardID11),
		// 称号 ID：50048 有效期：7d
		task.NewScoreTask(4, int64(m.collectSuperFanNum()), int64(m.addSuperFanNum), reward.RewardID12),
		// 直播间封面角标 角标地址：live/labelicon/livelist/liveshow-2 有效期：7d
		task.NewScoreTask(5, int64(m.collectSuperFanNum()), int64(m.addSuperFanNum), reward.RewardID13),
	)
}

func (m *Moon) task() {
	// 完成全部任务猫耳娘送礼 ID: 40047，房间发飘屏，首席应援发放外观
	task := task.NewNormalTask(m.collectTask(), m.pointTask(), m.superFanTask()).
		SetRewards(reward.RewardID8, reward.RewardID14, reward.RewardID15)
	if !task.CheckIsFirstComplete() {
		m.reward(task.RewardIDs()...)
		return
	}

	// NOTICE: 移动到协程中避免阻塞其他操作
	goutil.Go(func() {
		// NOTICE: 等待从库同步完成，避免主从数据不一致导致奖励发错人的问题
		<-time.After(2 * time.Second)

		topfan, err := liveshowusers.FindTopFan(m.OID)
		if err != nil {
			logger.Error(err)
			return
		}
		if topfan == nil {
			logger.Errorf("live_show: can not find top fan %s", m.OID.Hex())
			return
		}

		m.reward = reward.NewRewardFunc(m.roomID, m.CreatorID, topfan.UserID)
		m.reward(task.RewardIDs()...)
	})
}
