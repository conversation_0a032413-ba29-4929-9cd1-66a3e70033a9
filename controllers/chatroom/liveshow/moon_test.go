package liveshow

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveshowusers"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMoonFeedRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var show liveshows.LiveShow
	err := liveshows.Collection().FindOneAndUpdate(ctx,
		bson.M{"creator_id": 4863},
		bson.M{"$set": bson.M{
			"point": 679999,
			"collect": bson.M{
				collectGiftKey:     10,
				collectSuperFanKey: 10,
				"modified_time":    goutil.TimeNow().Unix(),
			},
		}},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&show)
	require.NoError(err)

	liveshow := NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 11, 10)
	liveshow.LiveShow = show

	moon := Moon{liveshow}
	err = moon.feedRank()
	require.NoError(err)
	assert.Equal(show.Point+liveshow.addPoint, moon.Point)

	oldTopFan, err := liveshowusers.FindTopFan(show.OID)
	require.NoError(err)
	require.NotNil(oldTopFan)

	liveshow = NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 11, 10)
	liveshow.LiveShow = show
	err = moon.feedRank()
	require.NoError(err)
	assert.Equal(show.Point+2*liveshow.addPoint, moon.Point)

	newTopFan, err := liveshowusers.FindTopFan(show.OID)
	require.NoError(err)
	assert.Equal(oldTopFan, newTopFan)
}

func TestMoon_task(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var show liveshows.LiveShow
	err := liveshows.Collection().FindOneAndUpdate(ctx,
		bson.M{"creator_id": 4863},
		bson.M{"$set": bson.M{
			"point": 680000,
			"collect": bson.M{
				collectGiftKey:     10,
				collectSuperFanKey: 10,
				"modified_time":    goutil.TimeNow().Unix(),
			},
		}},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&show)
	require.NoError(err)

	var i int
	cancel = mrpc.SetMock(userapi.URILiveReward, func(interface{}) (interface{}, error) {
		i++
		return nil, nil
	})
	defer cancel()

	key := keys.KeyReward1.Format(21)
	err = service.LRURedis.Set(key, tutil.SprintJSON(reward.Reward{
		Type:       reward.TypeGift,
		ElementID:  1,
		ElementNum: 1,
	}), 100*time.Second).Err()
	require.NoError(err)
	defer service.LRURedis.Del(key)

	liveShow := NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 12, 10)
	liveShow.LiveShow = show
	liveShow.reward = reward.NewRewardFunc(1, 3, 2)
	m := Moon{LiveShow: liveShow}
	m.task()

	goutil.WaitGo()

	assert.EqualValues(2, i)
}
