package liveshow

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	collectGiftID      = 70009
	collectGiftKey     = "gift_70009"
	collectSuperFanKey = "super_fan"
)

// LiveShow sync struct
type LiveShow struct {
	liveshow.LiveShow

	addPoint          int64
	addCollectGiftNum int
	addSuperFanNum    int

	roomID     int64
	fromUserID int64
	toUserID   int64

	reward func(...int64)
}

// NewSyncLiveShow new sync live show
func NewSyncLiveShow(roomID, fromUserID, toUserID int64) *LiveShow {
	return &LiveShow{
		roomID:     roomID,
		fromUserID: fromUserID,
		toUserID:   toUserID,
	}
}

// SetPoint set live show point
func (show *LiveShow) SetPoint(addPoint int64) *LiveShow {
	show.addPoint = addPoint
	return show
}

// SetSuperFan set live show super fans_num
func (show *LiveShow) SetSuperFan(addPoint int64, isFirst bool) *LiveShow {
	show.addPoint = addPoint
	if isFirst {
		show.addSuperFanNum = 1
	}
	return show
}

// SetGift set live show gift
func (show *LiveShow) SetGift(giftID, giftPrice int64, giftNum int) *LiveShow {
	show.addPoint = giftPrice * int64(giftNum)
	if giftID == collectGiftID {
		show.addCollectGiftNum = giftNum
	}
	return show
}

// collectSuperFanNum 个人场当前开通超粉数
func (show *LiveShow) collectSuperFanNum() int {
	return show.Collect[collectSuperFanKey]
}

// collectGiftNum 个人场当前收集礼物数
func (show *LiveShow) collectGiftNum() int {
	return show.Collect[collectGiftKey]
}

// buildUpdate 构建 feedRank 的 update bson
func (show *LiveShow) buildUpdate() bson.M {
	inc := bson.M{"point": show.addPoint}
	if show.addCollectGiftNum != 0 {
		inc["collect."+collectGiftKey] = show.addCollectGiftNum
	}
	if show.addSuperFanNum != 0 {
		inc["collect."+collectSuperFanKey] = show.addSuperFanNum
	}

	return bson.M{"$inc": inc, "$set": bson.M{"modified_time": goutil.TimeNow().Unix()}}
}

// Sync live show
func (show *LiveShow) Sync() {
	m, err := liveshow.MapCreatorIDOngoingShows()
	if err != nil {
		logger.Error(err)
		return
	}

	s, ok := m[show.toUserID]
	if !ok {
		return
	}

	now := goutil.TimeNow().Unix()
	if now < s.StartTime || now >= s.EndTime {
		return
	}

	show.reward = reward.NewRewardFunc(show.roomID, show.toUserID, show.fromUserID)
	shower := show.newLiveShower(s)
	err = shower.feedRank()
	if err != nil {
		logger.Error(err)
		return
	}

	shower.task()
}

type liveShower interface {
	feedRank() error
	task()
}

func (show *LiveShow) newLiveShower(s liveshow.LiveShow) liveShower {
	show.LiveShow = s
	switch show.ShowType {
	case liveshow.ShowTypeStar:
		return &Star{show}
	case liveshow.ShowTypeMoon:
		return &Moon{show}
	}

	panic(fmt.Sprintf(
		"个人场类型错误 id: %s, type: %d",
		show.OID.Hex(), show.ShowType),
	)
}
