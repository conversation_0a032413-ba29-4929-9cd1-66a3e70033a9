package liveshow

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
)

func TestStarFeedRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	show, err := liveshows.FindOne(bson.M{"creator_id": 4863})
	require.NoError(err)
	require.NotNil(show)

	liveshow := NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 11, 10)
	liveshow.LiveShow = *show

	star := Star{liveshow}
	err = star.feedRank()
	require.NoError(err)

	assert.Equal(show.Point+liveshow.addPoint, star.Point)
}
