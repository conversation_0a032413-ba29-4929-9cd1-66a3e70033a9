package liveshow

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestNewSyncLiveShow(t *testing.T) {
	assert := assert.New(t)

	liveshow := NewSyncLiveShow(1, 2, 3)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3}, liveshow)
}

func TestLiveShowSetPoint(t *testing.T) {
	assert := assert.New(t)

	liveshow := NewSyncLiveShow(1, 2, 3).SetPoint(111)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3, addPoint: 111}, liveshow)
}

func TestLiveShowSetSuperFan(t *testing.T) {
	assert := assert.New(t)

	liveshow := NewSyncLiveShow(1, 2, 3).SetSuperFan(111, false)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3, addPoint: 111, addSuperFanNum: 0}, liveshow)

	liveshow = NewSyncLiveShow(1, 2, 3).SetSuperFan(111, true)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3, addPoint: 111, addSuperFanNum: 1}, liveshow)
}

func TestLiveShowSetGift(t *testing.T) {
	assert := assert.New(t)

	liveshow := NewSyncLiveShow(1, 2, 3).SetGift(11, 11, 10)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3, addPoint: 110, addCollectGiftNum: 0}, liveshow)

	liveshow = NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 11, 10)
	assert.Equal(&LiveShow{roomID: 1, fromUserID: 2, toUserID: 3, addPoint: 110, addCollectGiftNum: 10}, liveshow)
}

func TestLiveShowBuildUpdate(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(100, 0)
	})
	defer goutil.SetTimeNow(nil)

	liveshow := NewSyncLiveShow(1, 2, 3).SetGift(collectGiftID, 11, 10)
	assert.Equal(bson.M{
		"$inc": bson.M{
			"collect." + collectGiftKey: 10,
			"point":                     liveshow.addPoint,
		},
		"$set": bson.M{
			"modified_time": int64(100),
		},
	}, liveshow.buildUpdate())

	liveshow = NewSyncLiveShow(1, 2, 3).SetGift(11, 11, 10)
	assert.Equal(bson.M{
		"$inc": bson.M{
			"point": liveshow.addPoint,
		},
		"$set": bson.M{
			"modified_time": int64(100),
		},
	}, liveshow.buildUpdate())

	liveshow = NewSyncLiveShow(1, 2, 3).SetSuperFan(111, true)
	assert.Equal(bson.M{
		"$inc": bson.M{
			"collect." + collectSuperFanKey: 1,
			"point":                         liveshow.addPoint,
		},
		"$set": bson.M{
			"modified_time": int64(100),
		},
	}, liveshow.buildUpdate())
}
