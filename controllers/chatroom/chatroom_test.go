package chatroom

import (
	"errors"
	"log"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const roomIDStr = "22489473"
const roomID = int64(22489473)
const closedRoomID = int64(65150486)
const roomOIDHex = "5ab9d5d9bc9b53298ce5a5a5"
const testRoomCreatorID = int64(10)

const bannedRoomID = int64(369892) // room_test banned
const bannedRoomIDStr = "369892"
const openingRoomID = int64(3192516)

const testUA = "live-service/controllers/chatroom/"

const noble7UserID int64 = 3457114 // 神话测试账号

const (
	adminUserID  = int64(12)
	normalUserID = int64(3400560)
)

var (
	roomObjID, _ = primitive.ObjectIDFromHex(roomOIDHex)
	testMsg      = models.Message{
		RoomOID: roomObjID, RoomID: roomID, UserID: normalUserID, MsgID: "test-msg-id",
		Bubble: &bubble.Simple{Type: "noble", NobleLevel: 7}, Message: "This is message."}
	testGift = livegifts.LiveGift{RoomOID: roomObjID, RoomID: roomID, UserID: adminUserID,
		GiftID: 1, GiftNum: 1, Bubble: &bubble.Simple{Type: "noble", NobleLevel: 5}}
	imRedis *redis.Client
)

var (
	mockedMRPC *mrpc.Client
	orgMRPC    *mrpc.Client

	mockAction = handler.NewAction(handler.POST, nil, false)
)

var (
	testRoom    *room.Room
	openingRoom *room.Room
)

func mockMRPC() func() {
	service.MRPC = mockedMRPC
	return func() {
		service.MRPC = orgMRPC
	}
}

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	testRoom, _ = room.Find(roomID)
	if testRoom == nil {
		log.Fatal("testRoom is nil")
	}
	openingRoom, _ = room.Find(openingRoomID)
	if openingRoom == nil {
		log.Fatal("openingRoom is nil")
	}
	testHistoryMessageRoom, _ = room.Find(testHistoryMessageRoomID)
	if testHistoryMessageRoom == nil {
		log.Fatal("testHistoryMessageRoom is nil")
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().DeleteOne(ctx, bson.M{"room_id": room.TestNonexistentRoomID})
	if err != nil {
		logger.Fatal(err)
	}
	redisConf := serviceredis.Config{
		Addr: "redis.srv.maoer.co:6379",
		DB:   106,
	}
	imRedis, err = serviceredis.NewRedisClient(&redisConf)
	if err != nil {
		logger.Fatal(err)
	}

	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"live/add-listen-history": mockAction,
			"live/send-rebate-gift":   mockAction,
			"live/buy-lucky-gift":     mockAction,
		},
	}
	addr := tutil.RunMockServer(r, 0, &h)
	orgMRPC = service.MRPC
	mrpcConf := make(mrpc.Config, len(config.Conf.Service.MRPC))
	for key, entry := range config.Conf.Service.MRPC {
		mrpcConf[key] = entry
	}
	appConf := mrpcConf["app"]
	appConf.URL = "http://" + addr + "/"
	mrpcConf["app"] = appConf
	mockedMRPC, err = mrpc.NewClient(mrpcConf)
	if err != nil {
		logger.Fatal(err)
	}

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	handlers := Handler().SubHandlers
	names := []string{"chatroom", "live"}

	forActions := [][]string{{
		"mine",
		"meta",
		"logs/:roomID", "rank/:roomID",
		"follow/list", "follow", "follow/room-list",
		"history/message", "history/gift", "history/revenue",
		"fans/rank", "fans/progress",
		"superfan/rank", "superfan/intro", "superfan/buy",
		"list", "open/list", "recommend",
		"sound/recommend", "sound/close-recommend",
		"vitality/logs", "vip/list",
		"online", "close", "share",
		"message/horn",
		"revenue/nobles", "revenue/gifts", "revenue/superfans", "revenue/questions",
		"medal/edit", "medal/status",
		"slide/list",
		"prompt/check", "prompt/confirm",
		"backpack/send", "backpack/use",
		"backpack/creator/list", "backpack/creator/use",
		"interaction/options", "interaction/startvote", "interaction/closevote",
		"gift/send",
		"gift/draw",
		"diygift/send",
		"admin/add", "admin/remove",
		"user/block",
		"question/list", "question/like", "question/append-limit", "question/ask",
		"question/answer",
		"search",
		"diygift/info",
		"liveshow/widget",
		"settings/set", "settings/get",
		"danmaku/info", "danmaku/send",
	}, {
		":roomID",
	}}
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	for i := 0; i < len(handlers); i++ {
		assert.Equal(names[i], handlers[i].Name)
		kc.Check(handlers[i], forActions[i]...)
	}

	require.Len(handlers[0].SubHandlers, 6)
	h := handlers[0].SubHandlers[0]
	assert.Equal("gashapon", h.Name)
	kc.Check(h, "goods", "gacha", "history", "rank", "prize/list")

	h = handlers[0].SubHandlers[1]
	assert.Equal("pk", h.Name)
	kc.Check(h, "match/start", "match/cancel", "close", "record/list", "assists/list", "mute", "unmute",
		"settings/get", "settings/set", "invitation/request", "invitation/cancel", "invitation/accept", "invitation/refuse")

	h = handlers[0].SubHandlers[2]
	assert.Equal("giftwall", h.Name)
	kc.Check(h, "info", "reward", "rank", "sponsor/list")

	h = handlers[0].SubHandlers[3]
	assert.Equal("redpacket", h.Name)
	kc.Check(h, "config", "grabuserlist", "send", "grab")

	h = handlers[0].SubHandlers[4]
	assert.Equal("sticker", h.Name)
	kc.Check(h, "tabs", "list", "send")

	h = handlers[0].SubHandlers[5]
	assert.Equal("pia", h.Name)
	kc.Check(h, "start", "stop")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	handlers := HandlerV2().SubHandlers
	require.Len(handlers, 1)
	assert.Equal("chatroom", handlers[0].Name)

	h := handlers[0].SubHandlers
	require.Len(h, 5)
	assert.Equal("luckybag", h[0].Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h[0], "config", "initiate", "info", "finish", "join", "room/list", "drama/list")
	assert.Equal("open", h[2].Name)
	kc = tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h[2], "recommend-list", "feed-list", "search-recommend-list")
	assert.Equal("multi-connect", h[3].Name)
	assert.Equal("gift", h[4].Name)
}

// receiveMessage 收取广播消息
func receiveMessage(ch <-chan *redis.Message, f func(*redis.Message) (bool, error)) error {
	timer := time.NewTimer(2 * time.Second)
	for i := 0; i < 5; i++ {
		select {
		case <-timer.C:
			return errors.New("timeout")
		case m, ok := <-ch:
			if !ok {
				return errors.New("ch closed")
			}
			logger.Debugf("receive: %s", m.Payload)
			ok, err := f(m)
			if err != nil {
				return err
			}
			if ok {
				return nil
			}
		}
	}
	return errors.New("receive message failed")
}
