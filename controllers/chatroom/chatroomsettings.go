package chatroom

import (
	"encoding/json"
	"strings"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livesetting"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type chatroomSettingsParam struct {
	RoomID int64  `form:"room_id" json:"room_id"`
	Key    string `form:"key" json:"key"`
	Value  string `form:"value" json:"value"`
}

func newChatroomSettingsParam(c *handler.Context, isSet bool) (*chatroomSettingsParam, error) {
	param := new(chatroomSettingsParam)
	if isSet {
		err := c.Bind(&param)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
		param.Key = strings.TrimSpace(param.Key)
		param.Value = strings.TrimSpace(param.Value)
		if param.Value == "" || len(param.Value) > livesetting.MaxChatroomSettingsLen {
			return nil, actionerrors.ErrParams
		}
	} else {
		param.RoomID, _ = c.GetParamInt64("room_id")
		param.Key, _ = c.GetParamString("key")
	}
	if param.RoomID <= 0 || param.Key == "" {
		return nil, actionerrors.ErrParams
	}
	if !livesetting.IsValid(param.Key) {
		return nil, actionerrors.NewErrForbidden("暂不支持该类型的配置信息")
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != c.UserID() {
		return nil, actionerrors.ErrForbidden
	}
	return param, nil
}

// ActionSettingsSet 直播间主播配置信息设置
/**
 * @api {post} /api/v2/chatroom/settings/set 直播间主播配置信息设置
 * @apiDescription 当前仅用于直播消息助手的设置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {string="live_assistant_settings"} key 直播间指定的配置信息
 * @apiParam {String} value 直播间主播配置信息（数据格式 JSON, 最大长度限制为 1024）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 501010003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSettingsSet(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newChatroomSettingsParam(c, true)
	if err != nil {
		return nil, err
	}

	// 对 JSON 字符串进行格式化处理
	var v interface{}
	err = json.Unmarshal([]byte(param.Value), &v)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("JSON 格式错误")
	}
	// 保证存入的是我们处理过的 JSON KEY VALUE 的对象
	normalize, err := json.Marshal(v)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = livesetting.UpdateOne(param.RoomID, param.Key, string(normalize))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

// ActionSettingsGet 直播间主播配置信息获取
/**
 * @api {get} /api/v2/chatroom/settings/get 直播间主播配置信息获取
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {string="live_assistant_settings"} key 直播间指定的配置信息
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "key": "live_assistant_settings",
 *       "value": "{\"message_set\":\"1\"}" // 直播间主播配置信息，如返回为空表示未初始化配置信息，返回格式为 JSON 字符串
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 501010003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSettingsGet(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newChatroomSettingsParam(c, false)
	if err != nil {
		return nil, err
	}

	setting, err := livesetting.FindByRoomID(param.RoomID, param.Key)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if setting == nil {
		return &livesetting.Setting{Key: param.Key}, nil
	}
	return setting, nil
}
