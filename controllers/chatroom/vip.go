package chatroom

import (
	"encoding/json"
	"sort"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const vipListLimit = 50
const maxVipNum = 100 // 贵宾数量超过 99 之后总是显示 99+

type vipListParam struct {
	c *handler.Context

	// TODO: 待简化
	ctx goutil.UserContext

	roomID    int64
	medalName string

	valuableUserIDs []int64

	nobleUsers   map[int64]*vip.UserInfo
	onlineNoble  []int64
	invisibleSet map[int64]struct{}

	allVipIDs []int64

	medalVipUserIDs []int64

	ttl time.Duration
}

type vipListResp struct {
	VipNum int64              `json:"vip_num"`
	Data   []*liveuser.Simple `json:"Datas"` // TODO: 改成 data
	User   *liveuser.UserInfo `json:"user,omitempty"`
}

func vipNumberKey(roomID int64) string {
	return keys.LRUKeyRoomsVipNum1.Format(roomID)
}

// ActionVipList 获取直播间内的贵宾
/**
 * @api {get} /api/v2/chatroom/vip/list 贵宾榜
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "user_id": 12345,
 *           "username": "1234",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "titles": [
 *             {
 *               "type": "medal",
 *               "name": "1",
 *               "frame_url": "http://test.png",  // 用户粉丝徽章
 *               "level": 1,
 *               "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *                 "expire_time": 1576116700 // 秒级时间戳
 *               },
 *             }
 *           ]
 *         }
 *       ],
 *       "user": {
 *         "user_id": 3456835,
 *         "username": "我的力量无人能及",
 *         "iconurl": "https://static-test.missevan.com/avatars/201911/13/9b353f5bc73786d1e77b9fef177b2223190708.jpg",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           },
 *           {
 *             "type": "medal",
 *             "name": "test",
 *             "level": 1,
 *             "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           },
 *           {
 *             "type": "badge",
 *             "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *             "appearance_id": 1
 *           }
 *         ],
 *         "confirm": 3,
 *         "introduction": "咔咔",
 *         "noble": {
 *           "name": "练习生",
 *           "level": 1,
 *           "expire_time": 1917768067,
 *           "status": 1, // 贵族身份状态：0 没有贵族，1 有贵族身份，2 贵族身份处于续费保护期
 *           "tip": "练习生到期：yyyy-mm-dd hh:mm"
 *         },
 *         "highness": { // 没有上神返回 null
 *           "name": "上神",
 *           "level": 1,
 *           "expire_time": 1234567890, // 到期时间
 *           "status": 1 // 上神身份状态：1 有上神身份，2 上神身份处于续费保护期
 *           "spend": 1,  // 续费上神当前累计消费钻石数
 *           "renewal_threshold": 5000000, // 续费上神所需累计消费总钻石数
 *           "tip": "上神到期: yyyy-mm-dd hh:mm" // 状态提示，需要根据当前消费和总需消费拼接第二条轮播展示的续费信息
 *         },
 *         "trial_noble": { // 没有体验贵族返回 null（这里不会影响普通贵族，普通贵族按照原贵族身份下发）
 *           "name": "大咖",
 *           "level": 1,
 *           "expire_time": 1234567890, // 到期时间，单位：秒
 *           "status": 1, // 体验贵族身份状态：1 有体验贵族身份
 *           "tip": "大咖体验到期：yyyy-mm-dd hh:mm" // 状态提示
 *         }
 *       },
 *       "vip_num": 10 // 贵宾数量，返回的最大值为 100（实际可能更多，显示 99+）
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionVipList(c *handler.Context) (handler.ActionResponse, error) {
	param := newVipListParam(c, false)
	err := param.loadFromQuery()
	if err != nil {
		return nil, err
	}
	resp := new(vipListResp)
	param.vipListFromCache(resp)
	user, err := param.findUserInfo()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.User = user
	// 贵宾数量超过 99 之后总是显示 99+
	resp.VipNum = min(resp.VipNum, maxVipNum)
	return resp, nil
}

func (param *vipListParam) loadFromQuery() error {
	param.roomID, _ = param.c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return actionerrors.ErrParams
	}
	return nil
}

func (param *vipListParam) vipListFromCache(resp *vipListResp) (ok bool) {
	directReturn := false
	defer func() {
		if resp.Data == nil {
			resp.Data = make([]*liveuser.Simple, 0)
		}
		f := func(r *vipListResp) {
			param.findValuableUserIDs()
			err := param.findNoblesAndInvisible()
			if err != nil {
				logger.Error(err)
				// PASS
			}
			param.findMedalVips()
			l, err := param.findVips()
			if err != nil {
				logger.Error(err)
				// PASS
			}
			param.cacheResp(l, r)
		}
		if directReturn {
			f(resp)
			return
		}
		if !ok || param.needRenewCache() {
			goutil.Go(func() {
				f(nil)
			})
		}
	}()

	key := keys.KeyChatroomVipList1.Format(param.roomID)
	pipe := service.LRURedis.Pipeline()
	varCmd := pipe.Get(key)
	ttlCmd := pipe.TTL(key)
	_, _ = pipe.Exec()
	val, err := varCmd.Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return
	}
	if serviceredis.IsRedisNil(err) || val == "" {
		directReturn = true
		return
	}
	param.ttl = ttlCmd.Val()

	err = json.Unmarshal([]byte(val), resp)
	if err != nil {
		logger.Error(err)
		return
	}
	ok = true
	return
}

func (param *vipListParam) needRenewCache() bool {
	if param.ttl > 10*time.Second {
		return false
	}
	lock := keys.LRULockChatroomVipList1.Format(param.roomID)
	ok, err := service.LRURedis.SetNX(lock, 1, 5*time.Second).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	return ok
}

// TODO: 请求 userapi.IMRoomList 对服务器造成过大压力，所以需要加缓存
func (param *vipListParam) findValuableUserIDs() {
	apiResp, err := userapi.IMRoomList(userapi.IMRoomListRequest{
		RoomID:        param.roomID,
		ValuableUsers: true,
	}, userapi.NewUserContext(param.ctx))
	if err != nil {
		logger.WithField("room_id", param.roomID).Error(err)
		param.valuableUserIDs = make([]int64, 0)
		return
	}
	param.valuableUserIDs = apiResp.ValuableUserIDs
}

func (param *vipListParam) findNoblesAndInvisible() error {
	userID := param.ctx.UserID()
	found := make(map[int64]*vip.UserInfo)
	if userID != 0 {
		uvs, err := vip.UserVipInfos(userID, false, param.ctx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if uvs != nil {
			found[userID] = vip.NewUserVipInfo(uvs)
		}
	}
	nobleUsers, err := vip.MapUsersInfo(param.valuableUserIDs, found, param.ctx)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.nobleUsers = nobleUsers
	param.onlineNoble, param.invisibleSet = listOnlineAndInvisible(nobleUsers)
	return nil
}

func (param *vipListParam) findMedalVips() {
	var ok bool
	ok, param.medalName = room.HaveMedal(param.roomID)
	if !ok {
		return
	}
	userIDs := make([]int64, 0, len(param.valuableUserIDs))
	for _, userID := range param.valuableUserIDs {
		// 排除掉隐身的和贵族
		_, ok1 := param.invisibleSet[userID]
		_, ok2 := param.nobleUsers[userID]
		if !ok1 && !ok2 {
			userIDs = append(userIDs, userID)
		}
	}
	if len(userIDs) == 0 {
		return
	}
	filter := bson.M{
		"room_id": param.roomID,
		"status":  livemedal.StatusShow,
		"user_id": bson.M{"$in": userIDs},
	}
	opts := options.Find().SetProjection(bson.M{"user_id": 1, "name": 1})
	medals, err := livemedal.FindSimples(filter, opts, false, false)
	if err != nil {
		logger.Error(err)
		return
	}
	param.medalVipUserIDs = make([]int64, len(medals))
	for i := 0; i < len(medals); i++ {
		param.medalVipUserIDs[i] = medals[i].UserID
	}
}

func (param *vipListParam) makeAllVipIDs() int {
	param.allVipIDs = util.Uniq(append(param.onlineNoble, param.medalVipUserIDs...))
	vipNum := len(param.allVipIDs)
	saveVipNumToCache(param.roomID, int64(vipNum))
	return vipNum
}

type vipNumCache struct {
	VipNum      int64 `json:"vip_num"`
	UpdatedTime int64 `json:"updated_time"`
}

func saveVipNumToCache(roomID, vipNum int64) {
	key := vipNumberKey(roomID)
	valuePre := vipNumCache{
		VipNum:      vipNum,
		UpdatedTime: goutil.TimeNow().Unix(),
	}
	value, _ := json.Marshal(valuePre)
	err := service.LRURedis.Set(key, string(value), time.Hour).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func vipNumFromCache(roomID int64) vipNumCache {
	key := vipNumberKey(roomID)
	s, err := service.LRURedis.Get(key).Bytes()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		return vipNumCache{}
	}
	var cache vipNumCache
	err = json.Unmarshal([]byte(s), &cache)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return cache
}

func (param *vipListParam) findVips() ([]*liveuser.Simple, error) {
	if param.makeAllVipIDs() == 0 {
		return make([]*liveuser.Simple, 0), nil
	}

	l, err := liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": param.allVipIDs}},
		&liveuser.FindOptions{
			FindTitles: true,
			RoomID:     param.roomID,
			UvMap:      param.nobleUsers,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 去掉勋章不是本直播间勋章的
	for i := range l {
		if l[i].Medal != nil && l[i].Medal.Name != param.medalName {
			l[i].Medal = nil
			l[i].MakeTitles()
		}
	}
	sortVips(l)
	if len(l) > vipListLimit {
		l = l[:vipListLimit]
	}
	return l, nil
}

func sortVips(l []*liveuser.Simple) {
	sort.Slice(l, func(i, j int) bool {
		vipI := vip.UserHighestVipInfo(l[i].VipInfo)
		vipJ := vip.UserHighestVipInfo(l[j].VipInfo)
		if vipI != nil {
			// vipJ 没有贵族，vipI 一定比 vipJ 大
			if vipJ == nil {
				return true
			}
			tempUserVipI := &vip.UserVip{Type: vipI.Type, Level: vipI.Level}
			if !vip.IsVipEq(tempUserVipI, vipJ.Type, vipJ.Level) {
				return vip.IsVipGte(tempUserVipI, vipJ.Type, vipJ.Level)
			}
		} else if vipJ != nil {
			return false
		}

		// 若贵族等级相同，需要按照勋章等级排序
		if l[i].Medal != nil {
			if l[j].Medal == nil {
				return true
			}
			if l[i].Medal.Level != l[j].Medal.Level {
				return l[i].Medal.Level > l[j].Medal.Level
			}
		} else if l[j].Medal != nil {
			return false
		}
		return l[i].Contribution > l[j].Contribution
	})
}

func (param vipListParam) findUserInfo() (*liveuser.UserInfo, error) {
	userInfo, err := liveuser.FindUserInfo(param.ctx.UserID(), param.roomID)
	if err != nil {
		return nil, err
	}
	if userInfo == nil {
		return nil, nil
	}
	// 普通贵族为 nil 时也要返回普通贵族状态和提示
	userInfo.Noble = vip.NewUserNoble(userInfo.UserVipMap[vip.TypeLiveNoble])
	// 体验贵族仅在体验贵族生效时返回体验贵族相关信息
	if trialNoble := userInfo.UserVipMap[vip.TypeLiveTrialNoble]; trialNoble != nil && trialNoble.IsActive() {
		userInfo.TrialNoble = vip.NewUserNoble(trialNoble)
	}
	// 上神仅在上神生效或在续费保护期内返回上神相关信息
	if highness := userInfo.UserVipMap[vip.TypeLiveHighness]; highness != nil &&
		goutil.TimeNow().Unix() <= vip.RenewDeadline(highness.ExpireTime) {
		userInfo.Highness = vip.NewUserNoble(highness)
	}
	return userInfo, nil
}

func (param *vipListParam) cacheResp(data []*liveuser.Simple, resp *vipListResp) {
	if resp == nil {
		resp = new(vipListResp)
	}
	resp.Data = data
	resp.VipNum = int64(len(param.allVipIDs))

	ttl := 30 * time.Second
	switch {
	case len(param.valuableUserIDs) >= 250:
		ttl = 60 * time.Second
	case len(param.valuableUserIDs) >= 50:
		ttl = 40 * time.Second
	}
	b, err := json.Marshal(resp)
	if err != nil {
		logger.Error(err)
		return
	}
	key := keys.KeyChatroomVipList1.Format(param.roomID)
	err = service.LRURedis.Set(key, string(b), ttl).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

// listOnlineAndInvisible 列出贵族中在线的和隐身的
func listOnlineAndInvisible(nobleUsers map[int64]*vip.UserInfo) (online []int64, invisibleSet map[int64]struct{}) {
	m := liveim.LiveJudgmentUserIDSet()
	invisibleSet = make(map[int64]struct{}, 5+len(m))
	defer func() {
		for userID := range m {
			// 把权限隐身复制进去
			invisibleSet[userID] = struct{}{}
		}
		online = make([]int64, 0, len(nobleUsers))
		for userID := range nobleUsers {
			if _, ok := invisibleSet[userID]; !ok {
				online = append(online, userID)
			}
		}
	}()
	// 查询隐身用户
	userIDs := make([]int64, 0, 5)
	for u, v := range nobleUsers {
		if v != nil && vip.UserHavePrivilege(v, vip.PrivilegeInvisible) {
			userIDs = append(userIDs, u)
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_meta")
	var records []struct {
		UserID int64 `bson:"user_id"`
	}
	cur, err := collection.Find(ctx, bson.M{
		"user_id":   bson.M{"$in": userIDs},
		"invisible": true,
	}, options.Find().SetProjection(bson.M{"user_id": 1}))
	if err != nil {
		logger.Error(err)
		return
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &records)
	if err != nil {
		logger.Error(err)
		return
	}
	for i := 0; i < len(records); i++ {
		invisibleSet[records[i].UserID] = struct{}{}
	}
	return
}

type vipNumberResp struct {
	Data handler.M `json:"data"`
}

// ActionVipNumber 直播间贵宾数目, webrpc mount
/**
 * @api {post} /rpc/chatroom/vip/num 直播间贵宾数目
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number[]} room_ids 房间号列表
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": {
 *         "1234567": 1000 // key 是房间号
 *         "1234568": 0 // 无数据则为 0，如果获取数据时发生错误，查询出错的房间将不会作为结果之一进行返回
 *       }
 *     }
 *   }
 *
 */
func ActionVipNumber(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		RoomID  int64   `json:"room_id"`
		RoomIDs []int64 `json:"room_ids"`
	}
	var resp vipNumberResp
	_ = c.BindJSON(&input)
	if input.RoomID <= 0 && len(input.RoomIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	if input.RoomID > 0 {
		input.RoomIDs = append(input.RoomIDs, input.RoomID)
	}

	resp.Data = make(handler.M, len(input.RoomIDs))
	for _, roomID := range input.RoomIDs {
		param := newVipListParam(c, true)
		param.roomID = roomID
		result := new(vipListResp)
		// FIXME: 在这个接口应该总是保证 vip_num 数量能查出来才对
		ok := param.vipListFromCache(result)
		if ok {
			resp.Data[strconv.FormatInt(roomID, 10)] = result.VipNum
			continue
		}
		resp.Data[strconv.FormatInt(roomID, 10)] = vipNumFromCache(roomID).VipNum
	}

	return resp, nil
}

func newVipListParam(c *handler.Context, isRPC bool) *vipListParam {
	ctx := &goutil.SmartUserContext{
		IP:          c.ClientIP(),
		UserToken:   c.Token(),
		UserEquipID: c.EquipID(),
		UA:          c.UserAgent(),
		Req:         c.Request(),
	}
	param := &vipListParam{c: c}
	if !isRPC {
		ctx.UID = c.UserID()
	}
	param.ctx = ctx
	return param
}
