package chatroom

import (
	"fmt"
	"regexp"
	"strconv"
	"unicode/utf8"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemedalreview"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

// 汉字、英文、数字
var regCharacter = regexp.MustCompile("^[a-z0-9A-Z\u4e00-\u9fa5]+$")

// 四个汉字
var regFourZHCharacter = regexp.MustCompile("^[\u4e00-\u9fa5]{4}$")

// medal edit status
const (
	statusNoQualified   = iota - 1 // 未满足开通勋章条件
	statusAllowNewApply            // 可新开通
	statusEditReviewing            // 审核中
	statusAllowModify              // 可修改（消耗小鱼干）
	statusRefusedToEdit            // 上次拒审可修改
	statusTimeLimit                // 间隔时间未满 30 天
)

type medalEditStatusResp struct {
	Status int    `json:"status"`
	Medal  string `json:"medal"`

	CostPoint int    `json:"cost_point,omitempty"`
	Content   string `json:"content,omitempty"`
}

type editMedalParams struct {
	UserID int64  `form:"user_id" json:"user_id"`
	RoomID int64  `form:"room_id" json:"room_id"`
	Name   string `form:"name" json:"name"`

	c *handler.Context
}

// ActionMedalEdit 主播开通、修改粉丝勋章
/**
 * @api {post} /api/v2/chatroom/medal/edit 主播开通、修改粉丝勋章
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} name 粉丝勋章名称
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该直播间
 *
 * @apiError (403) {Number} code 500020024
 * @apiError (403) {String} info 小鱼干数量不足
 *
 * @apiError (403) {Number} code 500030023
 * @apiError (403) {String} info 勋章重名，请更换名称
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionMedalEdit(c *handler.Context) (handler.ActionResponse, error) {
	p := editMedalParams{c: c, UserID: c.UserID()}
	err := c.Bind(&p)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = p.load()
	if err != nil {
		return nil, err
	}

	err = p.isInEditLimit()
	if err != nil {
		return nil, err
	}

	err = p.saveLiveMedal()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func (p *editMedalParams) load() error {
	r, err := room.FindOne(bson.M{"creator_id": p.UserID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.Limit != nil || r.Statistics.Revenue < livemedal.RevenueThreshold {
		return actionerrors.ErrParamsMsg("该房间暂无法设置勋章")
	}
	p.RoomID = r.RoomID
	return nil
}

// isInEditLimit 修改粉丝勋章限制
func (p *editMedalParams) isInEditLimit() error {
	err := p.checkMedalName()
	if err != nil {
		return err
	}

	// 违禁词检测
	checkRes, err := goclient.CheckText(p.c, p.Name, scan.SceneUserInfo)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !checkRes.Pass {
		return actionerrors.ErrParamsMsg("您申请的勋章名称含有违禁词，请重新输入")
	}

	lm := new(livemedalreview.LiveMedalReview)
	err = service.DB.Table(livemedalreview.TableName()).Where("user_id = ?", p.UserID).Order("modified_time DESC").First(&lm).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if lm.Status == livemedalreview.StatusReviewing {
		return actionerrors.ErrParamsMsg("已有勋章在审核中")
	} else if lm.Status == livemedalreview.StatusPassed {
		inEditTimeLimit := livemedalreview.InEditTimeLimit(lm.ModifiedTime)
		if inEditTimeLimit {
			return actionerrors.ErrParamsMsg("距离上次勋章修改通过未超过 30 天")
		}
	}
	return nil
}

func (p *editMedalParams) saveLiveMedal() error {
	modify, err := livemedalreview.FindLastMedalReview(p.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 仅修改审核已通过的勋章时扣除小鱼干
	if modify != nil && modify.Status == livemedalreview.StatusPassed {
		var point int
		err := service.DB.Table(mowangskuser.TableName()).Select("point").Where("id = ?", p.UserID).Row().Scan(&point)
		if err != nil {
			return err
		}
		if point < config.Conf.Params.MedalParams.CostPoint {
			return actionerrors.ErrNotEnoughPoint
		}

		err = userapi.UpdateUserPoint(p.c.UserContext(), p.UserID,
			-config.Conf.Params.MedalParams.CostPoint, userapi.UserPointTypeLiveMedal,
			p.c.Equip().OS, nil)
		if err != nil {
			if err, ok := err.(*mrpc.ClientError); ok {
				return err
			}
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	m := livemedalreview.LiveMedalReview{UserID: p.UserID, RoomID: p.RoomID, Name: p.Name}
	err = service.DB.Table(livemedalreview.TableName()).Create(&m).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *editMedalParams) checkMedalName() error {
	match := regCharacter.MatchString(p.Name)
	if !match {
		return actionerrors.ErrParamsMsg("您申请的勋章名称含有特殊字符，请重新输入")
	}

	count := utf8.RuneCountInString(p.Name)
	// 2-4 字符且纯汉字不能超过 3 个
	if count < 2 || count > 4 {
		return actionerrors.ErrParamsMsg("请输入 2-4 个字符的非纯数字内容")
	} else if count == 4 {
		match = regFourZHCharacter.MatchString(p.Name)
		if match {
			return actionerrors.ErrParamsMsg("您申请的勋章名称汉字字符超过 3 个，请重新输入")
		}
	}

	if _, err := strconv.ParseInt(p.Name, 10, 64); err == nil {
		return actionerrors.ErrParamsMsg("请输入 2-4 个字符的非纯数字内容")
	}

	// 是否存在同名勋章
	exist, err := room.MedalExists(p.Name, p.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exist {
		return actionerrors.ErrMedalAlreadyExist
	}
	return nil
}

// ActionMedalStatus 获取主播修改粉丝勋章相关状态信息
/**
 * @api {get} /api/v2/chatroom/medal/status 获取修改粉丝勋章相关状态信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1,  // -1：未满足开通条件，0：可新开通，1：审核中，2：可修改（消耗小鱼干），3：拒审可修改，4：不可编辑提交（间隔时间未满 30 天）
 *       "medal": "原子弹",  // 可新开通时 该项为空字符串
 *       "cost_point": 500,  // 消耗小鱼干数量，未满足开通条件或申请不消耗小鱼干时，无该字段
 *       "content": "<p><b>温馨提示。</b></p>", // 未满足开通条件时，无该字段
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalStatus(c *handler.Context) (handler.ActionResponse, error) {
	medalResp := new(medalEditStatusResp)
	r, err := room.FindOne(bson.M{"creator_id": c.UserID()})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil || r.Limit != nil || r.Statistics.Revenue < livemedal.RevenueThreshold {
		medalResp.Status = statusNoQualified
		return medalResp, nil
	}

	medalResp.Content = config.Conf.Params.MedalParams.ContentHTML
	liveMedal, err := livemedalreview.FindLastMedalReview(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if liveMedal == nil {
		medalResp.Status = statusAllowNewApply
		if haveMedal, medalName := room.HaveMedal(r.RoomID); haveMedal {
			medalResp.Status = statusAllowModify
			medalResp.Medal = medalName
		}
		return medalResp, nil
	}

	medalResp.CostPoint = config.Conf.Params.MedalParams.CostPoint
	switch liveMedal.Status {
	case livemedalreview.StatusRefused:
		medalResp.Status = statusRefusedToEdit
		medalResp.CostPoint = 0
	case livemedalreview.StatusReviewing:
		medalResp.Status = statusEditReviewing
	case livemedalreview.StatusPassed:
		medalResp.Status = statusAllowModify
		if inEditTimeLimit := livemedalreview.InEditTimeLimit(liveMedal.ModifiedTime); inEditTimeLimit {
			medalResp.Status = statusTimeLimit
		}
	default:
		panic(fmt.Sprintf("wrong medal status: %d", liveMedal.Status))
	}
	medalResp.Medal = liveMedal.Name
	return medalResp, nil
}
