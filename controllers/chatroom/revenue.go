package chatroom

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	revenueAll = iota
	revenueCurrent

	revenueTypeCount
)

type revenueNobleResp struct {
	Data []*vip.MongoUserNobles `json:"Datas"`
	Page goutil.Pagination      `json:"pagination"`
}

// ActionRevenueNobles 主播收益记录-贵族
/**
 * @api {get} /api/v2/user/revenue/noble 主播收益记录-贵族
 * @apiDescription 在主播房间内其他用户开通贵族的记录，主播自己可见
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页尺寸
 * @apiParam {number=0,1} [type=0] 查询类型，0: 所有，即不限制查询开始时间；1: 本场，即查询开始时间为本场开播时间
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "user_id": 1234,
 *           "username": "金主",
 *           "name": "传奇",
 *           "price": 120000,
 *           "created_time": "2019-12-23T00:00:00.000+08:00"
 *         },
 *         {
 *           "user_id": 12345,
 *           "username": "金主的小号",
 *           "name": "传奇",
 *           "price": 120000,
 *           "created_time": "2019-12-23T00:00:00.000+08:00"
 *         }
 *       ],
 *       "pagination": { // TODO: 换成 marker 分页
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 2,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRevenueNobles(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	resp := new(revenueNobleResp)
	searchType, _ := c.GetParamInt("type")
	switch searchType {
	case revenueAll:
		resp.Data, resp.Page, err = vip.ListNobleBalance(c.UserID(), p, pageSize)
	case revenueCurrent:
		var r *room.Room
		r, err = room.FindOne(bson.M{"creator_id": c.UserID()},
			&room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
		if !util.IntToBool(r.Status.Open) {
			return &revenueNobleResp{
				Data: []*vip.MongoUserNobles{},
				Page: goutil.MakePagination(0, p, pageSize),
			}, nil
		}
		resp.Data, resp.Page, err = vip.ListNobleBalance(c.UserID(), p, pageSize, goutil.TimeUnixMilli(r.Status.OpenTime).ToTime())
	default:
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}

type revenueGiftResp struct {
	Data       []liveGift        `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

type liveGift struct {
	LuckyGiftID int64  `bson:"lucky_gift_id" json:"-"`
	GiftID      int64  `bson:"gift_id" json:"gift_id"`
	GiftName    string `bson:"-" json:"gift_name"`
	GiftIconURL string `bson:"-" json:"gift_icon_url"`
	GiftNum     int64  `bson:"gift_num" json:"gift_num"`
	Price       int64  `bson:"price" json:"price"`

	GoodsID int64 `bson:"goods_id" json:"-"`

	UserID   int64  `bson:"user_id" json:"user_id"`
	Username string `bson:"-" json:"username"`
	IconURL  string `bson:"-" json:"iconurl"`

	SentTime time.Time `bson:"sent_time" json:"sent_time"`
}

func liveGiftProj() map[string]int {
	return mongodb.NewProjection("lucky_gift_id, gift_id, gift_num, price, user_id, sent_time, goods_id")
}

// ActionRevenueGifts 主播收益记录-礼物
/**
 * @api {get} /api/v2/user/revenue/gift 主播收益记录-礼物
 * @apiDescription 在主播收礼记录记录，主播自己可见，包含免费礼物，不是主播返回空，送礼时间倒序
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页尺寸
 * @apiParam {number=0,1} [type=0] 查询类型，0: 所有，即不限制查询开始时间；1: 本场，即查询开始时间为本场开播时间
 * @apiParam {number=0,1,2} [gift_type=0] 礼物查询类型，0: 所有礼物；1: 付费礼物; 2: 免费礼物
 * @apiParam {Number} [end_time] 查询截止时间，不传默认当前时刻，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "gift_id": 101,
 *         "gift_num": 110,
 *         "price": 11000, // 礼物的总收益
 *         "sent_time": "2020-04-12T03:07:25.801Z", // 最新的收礼在前，按照 sent_time 倒序排
 *         "gift_name": "药丸",
 *         "gift_icon_url": "http://static.example.com/101.png",
 *         "user_id": 12,
 *         "username": "用户名",
 *         "iconurl": "http://static.exampl.com/用户头像.jpg",
 *       }, {
 *         "gift_id": 101,
 *         "gift_num": 110,
 *         "price": 0, // 免费礼物的总收益总是为 0
 *         "sent_time": "2020-04-12T02:07:25.801Z",
 *         "gift_name": "药丸",
 *         "gift_icon_url": "http://static.example.com/101.png",
 *         "user_id": 12,
 *         "username": "用户名",
 *         "iconurl": "http://static.exampl.com/用户头像.jpg",
 *       }],
 *       "pagination": { // TODO: 换成 marker 分页
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 2,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRevenueGifts(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	searchType, _ := c.GetParamInt("type")
	if searchType < revenueAll || searchType >= revenueTypeCount {
		return nil, actionerrors.ErrParams
	}
	giftType, _ := c.GetParamInt("gift_type")
	if !livegifts.IsGiftType(giftType) {
		return nil, actionerrors.ErrParams
	}

	r, err := room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		// 因为房间是从登录用户查询的，不是 query，所以未返回房间不存在错误
		return revenueGiftResp{
			Data:       []liveGift{},
			Pagination: goutil.MakePagination(0, p, pageSize)}, nil
	}

	s := livegifts.Searcher{
		P:          p,
		PageSize:   pageSize,
		RoomID:     r.RoomID,
		Projection: liveGiftProj(),
		GiftType:   giftType,
	}

	switch searchType {
	case revenueAll:
		// 隐藏 2020-06-01 00:00:00 之前的数据
		s.StartTime = time.Unix(1590940800, 0)
	case revenueCurrent:
		if !util.IntToBool(r.Status.Open) {
			// 没开播，直接返回
			return revenueGiftResp{
				Data:       []liveGift{},
				Pagination: goutil.MakePagination(0, p, pageSize)}, nil
		}
		s.StartTime = goutil.TimeUnixMilli(r.Status.OpenTime).ToTime()
	}

	var resp revenueGiftResp
	et, _ := c.GetParamInt64("end_time")
	if et > 0 {
		s.EndTime = time.Unix(et, 0)
	}
	resp.Pagination, err = s.ListLiveGifts(&resp.Data)
	if err != nil {
		return nil, err
	}
	resp.findGiftAndUser()
	return resp, nil
}

func (resp *revenueGiftResp) findGiftAndUser() {
	if len(resp.Data) == 0 {
		return
	}
	giftIDs, userIDs, goodsIDs := make([]int64, 0, len(resp.Data)), make([]int64, len(resp.Data)), make([]int64, 0, len(resp.Data))
	for i := range resp.Data {
		giftIDs = append(giftIDs, resp.Data[i].GiftID)
		if resp.Data[i].LuckyGiftID != 0 {
			giftIDs = append(giftIDs, resp.Data[i].LuckyGiftID)
		}
		if resp.Data[i].GoodsID != 0 {
			goodsIDs = append(goodsIDs, resp.Data[i].GoodsID)
		}
		userIDs[i] = resp.Data[i].UserID
	}
	userMap, err := mowangskuser.FindSimpleMap(util.Uniq(userIDs))
	if err != nil {
		userMap = make(map[int64]*mowangskuser.Simple)
		logger.Error(err)
		// PASS
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		giftMap = make(map[int64]*gift.Gift)
		logger.Error(err)
		// PASS
	}
	var goodsMap map[int64]*livegoods.LiveGoods
	if len(goodsIDs) != 0 {
		goods, err := livegoods.FindByIDs(goodsIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		goodsMap = util.ToMap(goods, func(g *livegoods.LiveGoods) int64 {
			return g.ID
		})
	}
	for i := range resp.Data {
		if u := userMap[resp.Data[i].UserID]; u != nil {
			resp.Data[i].Username = u.Username
			resp.Data[i].IconURL = u.IconURL
		}
		if g := giftMap[resp.Data[i].GiftID]; g != nil {
			resp.Data[i].GiftName = g.Name
			if resp.Data[i].LuckyGiftID != 0 {
				luckyGift := giftMap[resp.Data[i].LuckyGiftID]
				if luckyGift != nil {
					resp.Data[i].GiftName = userapi.LuckyGiftTitle(luckyGift.Name, g.Name)
				}
			}
			if goods, ok := goodsMap[resp.Data[i].GoodsID]; ok {
				switch goods.Type {
				case livegoods.GoodsTypeGashapon:
					moreInfo, err := goods.UnmarshalMore()
					if err != nil {
						logger.Error(err)
						continue
					}
					if moreInfo == nil {
						continue
					}
					resp.Data[i].GiftName = userapi.LuckyGiftTitle(moreInfo.GashaponName, g.Name)
				default:
					// PASS
				}
			}
			resp.Data[i].GiftIconURL = g.Icon
		}
	}
}

type revenueSuperFanElem struct {
	Title      string `json:"title"`
	UserID     int64  `json:"user_id"`
	Username   string `json:"username"`
	IconURL    string `json:"iconurl"`
	Price      int    `json:"price"`       // 价值，单位：钻
	CreateTime int64  `json:"create_time"` // 开通时间
}

type revenueSuperFanResp struct {
	Data       []revenueSuperFanElem `json:"data"`
	Pagination goutil.Pagination     `json:"pagination"`
}

// ActionRevenueSuperFans 主播收益记录-超粉
/**
 * @api {get} /api/v2/user/revenue/superfan 主播收益记录-超粉
 * @apiDescription 在主播直播间开通/续费超粉的记录，不是主播返回空 data
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页尺寸
 * @apiParam {number=0,1} [type=0] 查询类型，0: 所有，即不限制查询开始时间；1: 本场，即查询开始时间为本场开播时间
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "title": "开通直播超粉--3 个月",
 *         "user_id": 123, // 开通/续费用户 ID
 *         "username": "用户名",
 *         "iconurl": "http://static.exampl.com/用户头像.jpg",
 *         "price": 11000, // 价值
 *         "create_time": 1234567890 // 开通/续费时间戳，单位：秒
 *       }],
 *       "pagination": { // TODO: 换成 marker 分页
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 2,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 */
func ActionRevenueSuperFans(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	var (
		page   goutil.Pagination
		orders []*livetxnorder.SuperFanTxnOrder
	)
	searchType, _ := c.GetParamInt("type")
	switch searchType {
	case revenueAll:
		orders, page, err = livetxnorder.RoomSuperFanByPage(c.UserID(), p, pageSize)
	case revenueCurrent:
		var r *room.Room
		r, err = room.FindOne(bson.M{"creator_id": c.UserID()},
			&room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
		if !util.IntToBool(r.Status.Open) {
			return &revenueSuperFanResp{
				Data:       []revenueSuperFanElem{},
				Pagination: goutil.MakePagination(0, p, pageSize),
			}, nil
		}
		orders, page, err = livetxnorder.RoomSuperFanByPage(c.UserID(), p, pageSize, goutil.TimeUnixMilli(r.Status.OpenTime).ToTime())
	default:
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(orders) == 0 {
		return &revenueSuperFanResp{
			Data:       make([]revenueSuperFanElem, 0),
			Pagination: page,
		}, nil
	}

	elems := make([]revenueSuperFanElem, len(orders))
	userIDs := make([]int64, len(orders))
	for i := range orders {
		elems[i].Title = orders[i].GoodsTitle()
		elems[i].Price = orders[i].Price
		elems[i].UserID = orders[i].BuyerID
		elems[i].CreateTime = orders[i].CreateTime
		userIDs[i] = orders[i].BuyerID
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range elems {
		if u := userMap[elems[i].UserID]; u != nil {
			elems[i].Username = u.Username
			elems[i].IconURL = u.IconURL
		}
	}
	return &revenueSuperFanResp{
		Data:       elems,
		Pagination: page,
	}, nil
}

type revenueQuestionElem struct {
	UserID       int64      `json:"user_id"`
	Username     string     `json:"username"`
	IconURL      string     `json:"iconurl"`
	Price        int64      `json:"price"`
	CreatedTime  time.Time  `json:"created_time"`
	AnsweredTime *time.Time `json:"answered_time"`
}

type revenueQuestionResp struct {
	Data       []*revenueQuestionElem `json:"data"`
	Pagination goutil.Pagination      `json:"pagination"`
}

// ActionRevenueQuestions 主播收益记录-提问
/*
 * @api {get} /api/v2/chatroom/revenue/questions 主播收益记录-提问
 * @apiDescription 主播回答的提问记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页数量
 * @apiParam {number=0,1} [type=0] 查询类型，0: 所有，即不限制查询开始时间；1: 本场，即查询开始时间为本场开播时间
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "user_id": 3457230,
 *         "username": "六一半夏sixsixsix666",
 *         "iconurl": "https://static-test.missevan.com/avatars/202005/26/8b709126be0b9b1145bf9eb491d0efea145823.jpg",
 *         "price": 10000,
 *         "created_time": "2020-08-07T08:46:03.076Z",
 *         "answered_time": "2020-08-07T08:46:46.526Z",
 *       }],
 *       "pagination": {
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 2,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 */
func ActionRevenueQuestions(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	room, err := room.FindOne(bson.M{"creator_id": c.UserID()},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	searchType, _ := c.GetParamInt("type")
	var (
		questions []*livequestion.LiveQuestion
		page      goutil.Pagination
	)
	switch searchType {
	case revenueAll:
		questions, page, err = livequestion.RoomRevenueByPage(room.RoomID, p, pageSize)
	case revenueCurrent:
		if !util.IntToBool(room.Status.Open) {
			return &revenueQuestionResp{
				Data:       []*revenueQuestionElem{},
				Pagination: goutil.MakePagination(0, p, pageSize),
			}, nil
		}
		questions, page, err = livequestion.RoomRevenueByPage(room.RoomID, p, pageSize, goutil.TimeUnixMilli(room.Status.OpenTime).ToTime())
	default:
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	res := make([]*revenueQuestionElem, len(questions))
	for i := range questions {
		res[i] = &revenueQuestionElem{
			UserID:       questions[i].UserID,
			Username:     questions[i].Username,
			IconURL:      questions[i].IconURL,
			Price:        questions[i].Price,
			CreatedTime:  questions[i].CreatedTime,
			AnsweredTime: questions[i].AnsweredTime,
		}
	}
	return &revenueQuestionResp{
		Data:       res,
		Pagination: page,
	}, nil
}
