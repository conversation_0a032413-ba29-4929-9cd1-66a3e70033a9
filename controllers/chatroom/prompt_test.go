package chatroom

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func insertTestLiveContract(liveID int64) error {
	lc := livecontract.LiveContract{
		GuildID:     99999,
		LiveID:      liveID,
		GuildName:   "test",
		Status:      livecontract.StatusContracting,
		ContractEnd: goutil.TimeNow().Add(2 * time.Minute).Unix(),
	}
	return service.DB.Assign(lc).FirstOrCreate(&lc).Error
}

func TestActionPromptCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12345)
	require.NoError(insertTestLiveContract(userID))

	// 开始直播界面
	c := handler.NewTestContext("GET", "/?action=1", true, nil)
	c.User().ID = userID
	r, err := ActionPromptCheck(c)
	require.NoError(err)
	resp := r.([]*promptInfo)
	require.Len(resp, 2)
	assert.Equal(promptTypeLiveAgreement, resp[0].Type)
	assert.Equal(promptTypeGuildAgreement, resp[1].Type)

	// 主播后台
	c = handler.NewTestContext("GET", "/?action=3", true, nil)
	c.User().ID = userID
	r, err = ActionPromptCheck(c)
	require.NoError(err)
	resp = r.([]*promptInfo)
	assert.Equal(promptTypeLiveAgreement, resp[0].Type)
	assert.Equal(promptTypeGuildAgreement, resp[1].Type)

	// 参数错误
	c = handler.NewTestContext("GET", "/?action=0", true, nil)
	_, err = ActionPromptCheck(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestGetOpenPrompt(t *testing.T) {
	assert := assert.New(t)

	params := promptParams{
		userID: 12,
	}
	config.Conf.Params.DisableOpen.Title = "test"
	params.GetOpenPrompt()
	assert.Len(params.resp, 1)
	assert.Equal(promptTypeDisableOpen, params.resp[0].Type)
}

func TestActionPromptConfirm(t *testing.T) {
	require := require.New(t)

	body := handler.M{
		"agree": 1,
		"type":  "guild_agreement",
	}

	// 测试同意公会协议
	c := handler.NewTestContext("POST", "/", true, body)
	c.User().ID = 987789
	_, err := ActionPromptConfirm(c)
	require.NoError(err)
	// 测试同意主播协议
	body["type"] = "live_agreement"
	c = handler.NewTestContext("POST", "/", true, body)
	c.User().ID = 987789
	_, err = ActionPromptConfirm(c)
	require.NoError(err)

	// 参数错误
	body["agree"] = 0
	c = handler.NewTestContext("POST", "/", true, body)
	_, err = ActionPromptConfirm(c)
	require.Equal(actionerrors.ErrParams, err)
	body = handler.M{
		"agree": 1,
		"type":  "test",
	}
	c = handler.NewTestContext("POST", "/", true, body)
	_, err = ActionPromptConfirm(c)
	require.Equal(actionerrors.ErrParams, err)
}
