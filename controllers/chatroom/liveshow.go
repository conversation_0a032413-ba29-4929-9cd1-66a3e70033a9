package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/sync/singleflight"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine/engineliveshow"
	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveshowusers"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/widget"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// attr type
const (
	attrNotUseSingleFlight = iota + 1
)

var (
	singleFlight singleflight.Group
)

// actionLiveShowWidget 主播个人场小窗
/**
 * @api {get} /api/v2/chatroom/liveshow/widget 主播个人场小窗
 * @apiVersion 0.1.0
 * @apiGroup activity
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [attr] 位 1：不使用缓存
 *
 * @apiSuccessExample
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1, // 个人场状态 0：未开始，1：进行中，2：结束
 *       "ts": 1, // 当前时间戳, 秒级
 *       "show_start_time": 1, // 个人场开始时间，秒级
 *       "show_end_time": 1, // 个人场结束时间，秒级
 *       "refresh_duration": 200000, // 刷新时间，没有该字段时不刷新。毫秒级
 *       "metadata": {} // 待定数据
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func actionLiveShowWidget(c *handler.Context) (handler.ActionResponse, error) {
	attr, err := c.GetDefaultParamInt("attr", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 前端通过 URL 来控制是否使用缓存
	if goutil.BitMask(attr).IsSet(attrNotUseSingleFlight) {
		return actionWidget(c)
	}

	key := c.C.Request.URL.RawQuery

	// 同一个直播间内的用户看到的小窗都是相同的
	// 缓存时间为一个请求的时间
	resp, err, _ := singleFlight.Do(key, func() (interface{}, error) {
		defer singleFlight.Forget(key)
		return actionWidget(c)
	})
	return resp, err
}

type widgetParam struct {
	RoomID int64 `json:"room_id"`

	c *handler.Context
	r *room.Room
	s *liveshow.LiveShow
}

func actionWidget(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	param := widgetParam{
		c:      c,
		RoomID: roomID,
	}

	err := param.checkRoom()
	if err != nil {
		return nil, err
	}

	resp, err := param.checkLiveShow()
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return resp, nil
	}

	return param.buildResp()
}

func (param *widgetParam) checkRoom() (err error) {
	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return err
	}
	if param.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	return nil
}

func (param *widgetParam) checkLiveShow() (*widgetResp, error) {
	mshow, err := liveshow.MapCreatorIDOngoingShows()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	show, ok := mshow[param.r.CreatorID]
	if !ok {
		// 个人场结束后访问接口返回个人场未开始，避免小窗没有下掉访问接口报错
		widget := widget.NewWidget(goutil.TimeNow().Unix())
		widget.BuildNotStartWidget(0)
		widget.RefreshDuration = 0
		return &widgetResp{
			Widget: widget,
		}, nil
	}

	param.s = &show
	return nil, nil
}

type widgetResp struct {
	*widget.Widget

	ShowType      int                    `json:"show_type,omitempty"`
	ShowStartTime int64                  `json:"show_start_time,omitempty"`
	ShowEndTime   int64                  `json:"show_end_time,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

func (param *widgetParam) buildResp() (interface{}, error) {
	show, err := liveshow.FindOne(bson.M{"_id": param.s.OID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if show == nil {
		return nil, fmt.Errorf("can not find live show %s", param.s.OID.Hex())
	}

	var topFan *liveshowusers.LiveShowUser
	topFan, err = liveshowusers.FindTopFan(show.OID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	luaResp, err := engine.RunWidget(param.c,
		engineliveshow.NewWidgetParam(param.s.ShowType, engine.WidgetTypeNormal),
		map[string]interface{}{
			"creator_id": param.r.CreatorID,
			"room":       param.r,
			"show":       show,
			"top_fan":    topFan,
		})
	if err != nil {
		return nil, err
	}

	now := goutil.TimeNow().Unix()
	resp := &widgetResp{
		Widget:        widget.NewWidget(now),
		ShowType:      param.s.ShowType,
		ShowStartTime: param.s.StartTime,
		ShowEndTime:   param.s.EndTime,
		Metadata:      luaResp,
	}

	if param.s.StartTime > now {
		resp.Widget.BuildNotStartWidget(param.s.StartTime)
		return resp, nil
	}

	if param.s.EndTime <= now {
		resp.Widget.BuildEndingWidget()
		return resp, nil
	}

	resp.Widget.BuildOnGoingWidget(param.s.EndTime)
	return resp, nil
}
