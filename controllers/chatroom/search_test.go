package chatroom

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var mockResp = json.RawMessage(`
{
  "status": "OK",
  "request_id": "156655084419726777026851",
  "result": {
    "total": 1,
    "num": 1,
    "viewtotal": 1,
    "items": [
      {
        "room_id": "223355"
      }
    ]
  },
  "ops_request_misc": "%7B%22reque...%7D"
}
`)

func TestActionSearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "/", false, nil)
	resp, err := ActionSearch(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(resp)
	c = handler.NewTestContext("GET", "/?s=", false, nil)
	_, err = ActionSearch(c)
	require.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "/?s=12345", false, nil)
	c.Request().Header.Add("User-Agent", "dayu-live-helper")
	resp, err = ActionSearch(c)
	require.NoError(err)
	returnRooms, ok := resp.(handler.M)["data"].([]roomInfoToReturn)
	require.True(ok)
	require.Len(returnRooms, 0)
	pagination, ok := resp.(handler.M)["pagination"].(goutil.Pagination)
	require.True(ok)
	assert.Equal(int64(0), pagination.Count)
	opsRequestMisc, ok := resp.(handler.M)["ops_request_misc"].(string)
	require.True(ok)
	assert.Equal("", opsRequestMisc)

	cancel := mrpc.SetMock("go://discovery/search", func(input interface{}) (output interface{}, err error) {
		return mockResp, nil
	})
	defer cancel()
	c = handler.NewTestContext("GET", "/?s=abc&input_word=abc", false, nil)
	resp, err = ActionSearch(c)
	require.NoError(err)
	returnRooms, ok = resp.(handler.M)["data"].([]roomInfoToReturn)
	require.True(ok)
	require.Len(returnRooms, 1)
	assert.Equal(int64(223355), returnRooms[0].RoomID)
	assert.Equal("测试失眠星人三期3", returnRooms[0].CreatorUsername)
	pagination, ok = resp.(handler.M)["pagination"].(goutil.Pagination)
	require.True(ok)
	assert.Equal(int64(1), pagination.Count)
	assert.Equal(int64(1), pagination.P)
	assert.Equal(int64(20), pagination.PageSize)
	opsRequestMisc, ok = resp.(handler.M)["ops_request_misc"].(string)
	require.True(ok)
	assert.Equal("%7B%22reque...%7D", opsRequestMisc)
}

func TestSearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := searchParam{
		P: -1,
	}
	require.Nil(p.search())
	cancel := mrpc.SetMock("go://discovery/search", func(input interface{}) (output interface{}, err error) {
		return mockResp, nil
	})
	defer cancel()
	p.P = 1
	p.PageSize = 20
	p.S = "阿"

	r := p.search()
	require.NoError(r)
	require.Len(p.rooms, 1)
	assert.Equal(int64(223355), p.rooms[0].RoomID)
	assert.Equal(int64(1), p.pagination.Count)
	assert.Equal("%7B%22reque...%7D", p.OpsRequestMisc)
}

func TestParseRoomResult(t *testing.T) {
	assert := assert.New(t)

	r := &room.Simple{
		RoomID:       123,
		Name:         "test",
		Announcement: "room",
		Background: &room.Background{
			Enable:  true,
			Opacity: 123,
			Image:   "https://test",
		},
		CreatorID:       12,
		CreatorUsername: "usrename",
		Statistics:      &room.Statistics{Score: 33},
		CoverURL:        "cover_url",
	}
	result := roomInfoToReturn{
		RoomID:          r.RoomID,
		Name:            r.Name,
		Announcement:    r.Announcement,
		CreatorID:       r.CreatorID,
		CreatorUsername: r.CreatorUsername,
		Statistics:      statistics{Score: r.Statistics.Score},
		CoverURL:        r.CoverURL,
	}
	assert.Equal(result, parseRoomResult(r))
}

func TestSearchParam_loadMultiConnectInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := goutil.TimeNow()
	memberRecords := []livemulticonnect.GroupMember{
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 114693474},
		{GroupID: 1, StartTime: timeNow.Add(-time.Hour).UnixMilli(), EndTime: timeNow.Add(-time.Minute * 10).UnixMilli(), RoomID: 4381915},

		{GroupID: 3, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: 100000009},
		{GroupID: 3, StartTime: timeNow.Add(-time.Minute * 10).UnixMilli(), EndTime: 0, RoomID: 173076071},
	}
	matchRecords := []livemulticonnect.Match{
		{ID: 999, Status: livemulticonnect.NotifyGroupJoinStatusPending, FromRoomID: 114693474, ToRoomID: 173076071},
	}
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error)
	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.Match{}).Error)
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), memberRecords))
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchRecords))

	param := searchParam{
		UserID: 3013063, // room_id 114693474
		rooms: []roomInfoToReturn{
			// 关闭的直播间
			{
				RoomID:    114693474,
				CreatorID: 3013063,
			},
			// 最近连线过
			{
				RoomID:    4381915,
				CreatorID: 1,
				Status: room.SimpleRoomStatus{
					Open: 1,
				},
			},
			// 正在处理中
			{
				RoomID:    173076071,
				CreatorID: 3457162,
				Status: room.SimpleRoomStatus{
					Open: 1,
				},
			},
		},
	}
	err := param.loadMultiConnectInfo()
	require.NoError(err)

	roomInfoMap := util.ToMap(param.rooms, func(r roomInfoToReturn) int64 {
		return r.RoomID
	})
	require.Len(roomInfoMap, 3)

	// 搜索到的关闭的直播间
	roomInfo := roomInfoMap[114693474]
	assert.Equal(livemulticonnect.NotifyGroupStatusFinish, roomInfo.MultiConnect.Status)
	assert.Nil(roomInfo.MultiConnect.GroupID)
	assert.False(roomInfo.MultiConnect.RecentConnected)
	assert.Nil(roomInfo.MultiConnect.MemberNum)
	assert.Nil(roomInfo.MultiConnect.MatchID)

	// 搜索到的开播且最近连线的直播间
	roomInfo = roomInfoMap[4381915]
	assert.Equal(1, roomInfo.Status.Open)
	assert.Equal(livemulticonnect.NotifyGroupStatusFinish, roomInfo.MultiConnect.Status)
	assert.Nil(roomInfo.MultiConnect.GroupID)
	assert.True(roomInfo.MultiConnect.RecentConnected)
	assert.Nil(roomInfo.MultiConnect.MemberNum)
	assert.Nil(roomInfo.MultiConnect.MatchID)
	assert.Equal(livemulticonnect.NotifyGroupJoinTypeInvite, roomInfo.MultiConnect.JoinType)
	assert.Equal(livemulticonnect.NotifyGroupJoinStatusDefault, roomInfo.MultiConnect.JoinStatus)

	// 搜索到的直播间正在连线中且发送了申请
	roomInfo = roomInfoMap[173076071]
	assert.Equal(1, roomInfo.Status.Open)
	assert.Equal(livemulticonnect.NotifyGroupStatusOngoing, roomInfo.MultiConnect.Status)
	assert.Equal(int64(3), *roomInfo.MultiConnect.GroupID)
	assert.False(roomInfo.MultiConnect.RecentConnected)
	assert.Equal(2, *roomInfo.MultiConnect.MemberNum)
	assert.Equal(int64(999), *roomInfo.MultiConnect.MatchID)
	assert.Equal(livemulticonnect.NotifyGroupJoinTypeApply, roomInfo.MultiConnect.JoinType)
	assert.Equal(livemulticonnect.NotifyGroupJoinStatusPending, roomInfo.MultiConnect.JoinStatus)
}
