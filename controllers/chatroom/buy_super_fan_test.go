package chatroom

import (
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCalculateSuperFanNewExpireTime(t *testing.T) {
	// 设置当前时间为 2021-05-10 18:59:59，开通 3 个月
	require := require.New(t)
	goutil.SetTimeNow(func() time.Time {
		tm, err := time.ParseInLocation(goutil.TimeFormatHMS, "2021-05-10 18:59:59", time.Local)
		require.NoError(err)
		return tm
	})
	defer goutil.SetTimeNow(nil)
	newExpireTime, isExceedMax := calculateSuperFanNewExpireTime(0, 3)
	require.False(isExceedMax)
	require.Equal("2021-08-09 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))

	// 开通 120 个月
	newExpireTime, isExceedMax = calculateSuperFanNewExpireTime(0, 120)
	require.True(isExceedMax)
	require.Equal("2023-05-01 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))

	// 开通 1 个月
	newExpireTime, isExceedMax = calculateSuperFanNewExpireTime(0, 1)
	require.False(isExceedMax)
	require.Equal("2021-06-10 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))
	// 继续续费 1 月
	newExpireTime, isExceedMax = calculateSuperFanNewExpireTime(newExpireTime.Unix(), 1)
	require.False(isExceedMax)
	require.Equal("2021-07-10 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))
	// 继续续费 1 月
	newExpireTime, isExceedMax = calculateSuperFanNewExpireTime(newExpireTime.Unix(), 1)
	require.False(isExceedMax)
	require.Equal("2021-08-09 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))
	// 继续续费 120 个月
	newExpireTime, isExceedMax = calculateSuperFanNewExpireTime(newExpireTime.Unix(), 120)
	require.True(isExceedMax)
	require.Equal("2023-05-01 00:00:00", newExpireTime.Format(goutil.TimeFormatHMS))
}

func mockAppRPCServer() func() {
	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"/live/buy-super-fan": handler.NewAction(handler.POST, func(c *handler.Context) (response handler.ActionResponse, e error) {
				return handler.M{
					"transaction_id":     100200,
					"balance":            500,
					"live_noble_balance": 2000,
					"price":              1200,
				}, nil
			}, false),
		},
	}
	addr := tutil.RunMockServer(r, 0, &h)

	appRPCEntryOriginal := service.MRPC.Config["app"]
	service.MRPC.Config["app"] = mrpc.ConfigEntry{
		URL: "http://" + addr + "/",
		Key: "testkey",
	}
	return func() {
		service.MRPC.Config["app"] = appRPCEntryOriginal
	}
}

type superFanPriviledge struct {
	id         int64
	attr       int
	orderTitle string
}

func (sfp superFanPriviledge) GoodsID() int64 {
	return sfp.id
}

func (sfp superFanPriviledge) OrderTitle() string {
	return sfp.orderTitle
}

func (sfp superFanPriviledge) GoodsSellerID() int64 {
	return 111222
}

func (sfp superFanPriviledge) GoodsType() int {
	return livegoods.GoodsTypeSuperFan
}

func (sfp superFanPriviledge) GoodsAttr() int {
	return sfp.attr
}

func (sfp superFanPriviledge) GoodsNum() int {
	return 12
}

func (sfp superFanPriviledge) GoodsTotalPrice() int {
	return 1200
}

func TestBuySuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	cleanup := mockAppRPCServer()
	defer cleanup()
	tm := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return tm
	})
	defer goutil.SetTimeNow(nil)

	superFanGoodsRegister := superFanPriviledge{
		id:         4,
		attr:       livegoods.AttrSuperFanRegister,
		orderTitle: "景向",
	}
	superFanGoodsRenew := superFanPriviledge{
		id:         5,
		attr:       livegoods.AttrSuperFanRenew,
		orderTitle: "景向",
	}

	defer func() {
		db := livetxnorder.LiveTxnOrder{}.DB().Delete(nil, "goods_id IN (4, 5) AND price = 1200")
		require.NoError(db.Error)
		assert.Equal(int64(2), db.RowsAffected)
	}()

	c := mrpc.UserContext{}
	_, err := buySuperFan(c, 12, superFanGoodsRenew, openingRoom, nil, nil)
	require.EqualError(err, actionerrors.ErrSuperFanNotRegisteredCannotRenew.Message)

	b := &bubble.Simple{Type: "test", BubbleID: 1}
	resp, err := buySuperFan(c, 12, superFanGoodsRegister, openingRoom, b, nil)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(1200), resp.Price)
	assert.Equal(int64(100200), resp.TransactionID)
	assert.Equal(int64(500), resp.Balance)
	assert.Equal(int64(2000), resp.LiveNobleBalance)
	lastExpireTime := resp.NewExpireTime
	var txOrder livetxnorder.LiveTxnOrder
	require.NoError(livetxnorder.LiveTxnOrder{}.DB().Take(&txOrder, "goods_id = ? AND buyer_id = ?",
		superFanGoodsRegister.id, 12).Error)
	require.NotNil(txOrder.More)
	assert.Equal(util.NewInt(livetxnorder.OpenStatusOpen), txOrder.More.OpenStatus)
	require.NotNil(txOrder.More.Bubble)
	assert.Equal(b.BubbleID, txOrder.More.Bubble.BubbleID)
	assert.Equal(b.Type, txOrder.More.Bubble.Type)

	resp, err = buySuperFan(c, 12, superFanGoodsRenew, testRoom, nil, nil)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(1200), resp.Price)
	assert.Equal(int64(100200), resp.TransactionID)
	assert.Equal(int64(500), resp.Balance)
	assert.Equal(int64(2000), resp.LiveNobleBalance)
	assert.True(resp.NewExpireTime.After(lastExpireTime))

	var orders []livetxnorder.LiveTxnOrder
	require.NoError(
		livetxnorder.LiveTxnOrder{}.DB().
			Where("buyer_id = ? AND seller_id = ? AND status = ?",
				12, superFanGoodsRenew.GoodsSellerID(), livetxnorder.StatusSuccess).
			Order("id ASC").
			Scan(&orders).Error,
	)
	require.Len(orders, 2)
	assert.Equal(tm.Unix(), orders[0].ExpireTime)
	assert.Equal(resp.NewExpireTime.Unix(), orders[1].ExpireTime)
}

func TestHasBuyQualification(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID    int64 = 111
		testRoomID2   int64 = 1112
		testSellerID  int64 = 2221
		testSellerID2 int64 = 2222
		testUserID    int64 = 3331
		testGoodsID   int64 = 444
	)
	err := livetxnorder.LiveTxnOrder{}.DB().Delete("", "buyer_id = ? AND goods_id = ?", testUserID, testGoodsID).Error
	require.NoError(err)

	// 没有配置
	ok, err := hasBuyQualification(testRoomID, testSellerID, testUserID, &livegoods.LiveGoods{})
	require.NoError(err)
	assert.True(ok)
	// 没有配置
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, &livegoods.LiveGoods{More: "{}"})
	require.NoError(err)
	assert.True(ok)
	// 限制直播间
	g := &livegoods.LiveGoods{More: `{"super_fan": {"allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)
	ok, err = hasBuyQualification(-111, testSellerID, testUserID, g)
	require.NoError(err)
	assert.False(ok)

	// 限制全站未开通过
	g = &livegoods.LiveGoods{More: `{"super_fan": {"limit_type": 1, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)
	err = livetxnorder.LiveTxnOrder{}.DB().Create(&livetxnorder.LiveTxnOrder{
		Status:    livetxnorder.StatusSuccess,
		GoodsType: livegoods.GoodsTypeSuperFan,
		SellerID:  testSellerID2,
		BuyerID:   testUserID,
		GoodsID:   testGoodsID,
	}).Error
	require.NoError(err)
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.False(ok)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx,
		bson.M{"room_id": bson.M{"$in": []int64{testRoomID, testRoomID2}}, "user_id": testUserID})
	require.NoError(err)
	require.NoError(service.LiveDB.Table(livetxnorder.TableName()).
		Delete("", "seller_id = ? AND goods_id = ?", testSellerID, testGoodsID).Error)
	// testRoomID 直播间内未开通过
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 2, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)
	// testRoomID2 直播间购买过优惠超粉, 再次开通
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 2, "allow_room_ids": [1112]}}`}
	ok, err = hasBuyQualification(testRoomID2, testSellerID2, testUserID, g)
	require.NoError(err)
	assert.False(ok)
	// testRoomID 直播间内续费
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 3, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.False(ok)
	// testRoomID 直播间内购买
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 4, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)

	require.NoError(addMedalTestData(&livemedal.Simple{
		RoomID:    testRoomID,
		CreatorID: testSellerID,
		UserID:    testUserID,
		Status:    livemedal.StatusOwned,
		Mini: livemedal.Mini{
			SuperFan: &livemedal.SuperFan{
				ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
			},
		},
	}))
	// testRoomID 直播间开通
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 2, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.False(ok)
	// testRoomID 直播间续费
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 3, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)
	// testRoomID 直播间购买
	g = &livegoods.LiveGoods{ID: testGoodsID, More: `{"super_fan": {"limit_type": 4, "allow_room_ids": [111]}}`}
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.True(ok)

	err = livetxnorder.LiveTxnOrder{}.DB().Create(&livetxnorder.LiveTxnOrder{
		Status:    livetxnorder.StatusSuccess,
		GoodsType: livegoods.GoodsTypeSuperFan,
		SellerID:  testSellerID,
		BuyerID:   testUserID,
		GoodsID:   testGoodsID,
	}).Error
	require.NoError(err)
	// testRoomID 直播间购买
	ok, err = hasBuyQualification(testRoomID, testSellerID, testUserID, g)
	require.NoError(err)
	assert.False(ok)
}
