package chatroom

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type myChatroomResp struct {
	*room.Room
	Vitality *int          `json:"vitality"`
	Ban      *livemeta.Ban `json:"ban,omitempty"`
}

// ActionMine 获取自己直播间信息
/**
 * @api {get} /api/v2/chatroom/mine 获取自己直播间信息
 * @apiDescription 主播后台获取自己直播间基本信息，包含房间封禁信息和活力值
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room_id": 152,
 *       "catalog_id": 107,
 *       "name": "12345",
 *       "announcement": "12345",
 *       "creator_id": 12345,
 *       "creator_username": "1234",
 *       "background": {
 *         "enable": false,
 *         "opacity": 1
 *       },
 *       "statistics": {
 *         "accumulation": 123,
 *         "attention_count": 12,
 *         ...
 *       },
 *       "medal": {
 *         "name": "medal_name"
 *       },
 *       "status": {
 *         "open": 1
 *         ...
 *       },
 *       "connect": {
 *         "id": "8e991848182648a8a3aa8629872a0d3f",
 *         "provider": "netease",
 *         "push_type": "pc",
 *         "forbidden": true
 *       },
 *       "question": {
 *         "min_price": 30,
 *         "limit": 100,
 *         "max_limit": 200
 *       },
 *       "vitality": 12,
 *       "ban": { // 未被封禁时，无该字段
 *         "type": 1, // 0: 永封, 1: 按时间封禁
 *         "duration": 60, // 封禁时长 单位：秒
 *         "start_time": **********,
 *         "expire_time": ********** // 永封无该字段
 *       }
 *     }
 *   }
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该聊天室
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionMine(c *handler.Context) (handler.ActionResponse, error) {
	r, err := room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{FindFans: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	vitality, err := liveaddendum.Vitality(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	ban, err := livemeta.FindBanned(r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &myChatroomResp{
		Room:     r,
		Vitality: vitality,
		Ban:      ban,
	}, nil
}
