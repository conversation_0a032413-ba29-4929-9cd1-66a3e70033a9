package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestVitalityLogsParamsLoad(t *testing.T) {
	assert := assert.New(t)

	param := vitalityLogsParams{
		creatorID: 0,
		fromTime:  goutil.TimeNow(),
		toTime:    goutil.TimeNow(),
	}
	startDateStr := "2021-02-01"
	endDateStr := "2021-01-02"

	err := param.load(handler.NewTestContext(http.MethodGet, "vitality/logs?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil))
	assert.Equal(handler.ErrInvalidDateRange, err)

	startDateStr = "2021-02-01"
	endDateStr = "2021-02-02"
	err = param.load(handler.NewTestContext(http.MethodGet, "vitality/logs?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1&creator_id=7878123", true, nil))
	assert.Equal(actionerrors.ErrCreatorNotInYourGuild, err)
}

// TestCheckOwnerOrAgentTimeRange
func TestCheckOwnerOrAgentTimeRange(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试公会会长访问
	testContractStart := time.Date(2021, 05, 01, 01, 01, 0, 0, time.Local)
	testGuildID := int64(222333)
	require.NoError(livecontract.LiveContract{}.DB().Create(livecontract.LiveContract{
		ID:            22233355,
		GuildID:       testGuildID,
		LiveID:        1,
		GuildOwner:    20,
		Status:        livecontract.StatusContracting,
		ContractStart: testContractStart.Unix(),
		ContractEnd:   goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:     "测试 GetGuildID 方法",
	}).Error)
	defer livecontract.LiveContract{}.DB().Delete("", "id = ?", 22233355)

	fromTime := time.Date(2021, 05, 01, 00, 00, 0, 0, time.Local)
	toTime := time.Date(2021, 05, 30, 01, 01, 0, 0, time.Local)
	isGuildManager, lc, err := CheckOwnerOrAgentTimeRange(1, 20, &fromTime, toTime)
	require.NoError(err)
	assert.True(isGuildManager)
	assert.Equal(testGuildID, lc.GuildID)

	/* 因产品提出要公会管理可以查看主播在公会的历史数据，注释查询时间代码
	assert.Equal(testContractStart.Unix(), fromTime.Unix())

	// 测试合约时间大于结束时间
	fromTime = time.Date(2021, 05, 01, 01, 00, 0, 0, time.Local)
	toTime = time.Date(2021, 04, 30, 01, 01, 0, 0, time.Local)
	isGuildManager, _, err = CheckOwnerOrAgentTimeRange(1, 20, &fromTime, toTime)
	assert.True(isGuildManager)
	assert.Equal(actionerrors.ErrParamsMsg("仅能查看入会后的数据"), err)
	*/

	// 将合约会长转给其他人
	err = livecontract.LiveContract{}.DB().Where("id = ?", 22233355).UpdateColumn(livecontract.LiveContract{GuildOwner: 233}).Error
	require.NoError(err)

	// 测试经纪人访问
	require.NoError(guildagent.AgentCreator{}.DB().Create(guildagent.AgentCreator{
		ID:        262726,
		GuildID:   100,
		AgentID:   20,
		CreatorID: 1,
	}).Error)
	defer guildagent.AgentCreator{}.DB().Delete("", "id = ?", 262726)

	fromTime = time.Date(2021, 05, 01, 00, 00, 0, 0, time.Local)
	toTime = time.Date(2021, 05, 30, 01, 01, 0, 0, time.Local)
	isGuildManager, _, err = CheckOwnerOrAgentTimeRange(1, 20, &fromTime, toTime)
	require.NoError(err)
	assert.True(isGuildManager)

	/* 因产品提出要公会管理可以查看主播在公会的历史数据，注释修改查询时间代码
	assert.Equal(testContractStart.Unix(), fromTime.Unix())
	*/

	// 测试普通人访问
	isOwnerOrAgent, _, err := CheckOwnerOrAgentTimeRange(1, 299999, &fromTime, toTime)
	require.NoError(err)
	assert.False(isOwnerOrAgent)
}

func TestActionVitalityLogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "vitality/logs?creator_id=11", true, "")
	// 没权限的查看
	c.User().ID = 999999
	_, err := ActionVitalityLogs(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 有权限的人查看
	c.User().ID = 12
	require.NoError(livecontract.LiveContract{}.DB().Create(livecontract.LiveContract{
		ID:            2223333,
		GuildID:       2223333,
		LiveID:        11,
		GuildOwner:    c.User().ID,
		Status:        livecontract.StatusContracting,
		ContractStart: 23333333,
		ContractEnd:   goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:     "测试 GetGuildID 方法",
	}).Error)
	defer livecontract.LiveContract{}.DB().Delete("", "id = ?", 2223333)
	r, err := ActionVitalityLogs(c)
	require.NoError(err)
	assert.Empty(tutil.KeyExists(tutil.JSON, r, "Datas", "pagination", "vitality"))

	// 自己查看
	c = handler.NewTestContext("GET", "vitality/logs", true, "")
	c.User().ID = 11
	r, err = ActionVitalityLogs(c)
	require.NoError(err)
	assert.Empty(tutil.KeyExists(tutil.JSON, r, "Datas", "pagination", "vitality"))
}
