package chatroom

import (
	"sync"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
)

var (
	lockRecommend sync.Mutex
)

const recommendLimit = 6

type recommendParam struct {
	catalogID      int64
	preRoomID      int64
	listener       *user.User
	opt            *room.FindOptions
	excludeRoomIDs map[int64]struct{}

	roomSimpleList
}

// ActionRecommend 推荐直播
/**
 * @api {get} /api/v2/chatroom/recommend 推荐直播
 * @apiDescription 给出推荐的房间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [room_id] 用户之前收听的直播间
 * @apiParam {Number} catalog_id 用户之前收听的直播间的分区
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "room_id": 152,
 *         "name": "12345",
 *         "cover_url": "http://static.maoercdn.com/avatars/icon01.png",
 *         "announcement": "12345",
 *         "creator_id": 12345,
 *         "creator_username": "1234",
 *         "creator_iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *         "catalog_id": 107,
 *         "catalog_name": "分区名",
 *         "catalog_color": "#ffffff",
 *         "custom_tag": {
 *           "tag_id": 115,
 *           "tag_name": "个性词条"
 *         },
 *         "statistics": {
 *           "accumulation": 123,
 *           "score": 1
 *           ...
 *         },
 *         "status": {
 *           "open": 1,
 *           ...
 *         }
 *       }
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionRecommend(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	err = param.findByCatalog()
	if err != nil {
		return nil, err
	}
	err = param.findByAttention()
	if err != nil {
		return nil, err
	}
	err = param.findByHeat()
	if err != nil {
		return nil, err
	}
	param.checkResp()
	return param.Data, nil
}

func (param *recommendParam) load(c *handler.Context) error {
	param.preRoomID, _ = c.GetParamInt64("room_id")
	if param.preRoomID < 0 {
		return actionerrors.ErrParams
	}
	param.checkCatalogID(c)
	if param.catalogID < 0 {
		return actionerrors.ErrParams
	}
	param.listener = c.User()
	param.opt = &room.FindOptions{
		ClientIP:        c.ClientIP(),
		FindCreator:     true,
		FindCatalogInfo: true,
		FindCustomTag:   true,
	}
	param.fillBaseRoomData()
	return nil
}

func (param *recommendParam) fillBaseRoomData() {
	param.Data = make([]*room.Simple, 0, recommendLimit)

	roomIDs := room.OpenListExcludeRoomIDs()
	param.excludeRoomIDs = make(map[int64]struct{}, 1+len(roomIDs))
	param.excludeRoomIDs[param.preRoomID] = struct{}{}
	for i := range roomIDs {
		param.excludeRoomIDs[roomIDs[i]] = struct{}{}
	}
}

// checkCatalogID 兼容旧版本不传 catalog_id
func (param *recommendParam) checkCatalogID(c *handler.Context) {
	var err error
	param.catalogID, err = c.GetParamInt64("catalog_id")
	if err != handler.ErrEmptyValue {
		return
	}

	// WORKAROUND: 旧版本从 room_id 获取
	// 预计 2020 年 7 月中旬的版本支持
	if e := c.Equip(); !e.IsAppOlderThan("4.5.2", "5.4.2") {
		return
	}
	if param.preRoomID == 0 {
		return
	}
	preRoom, err := room.Find(param.preRoomID, &room.FindOptions{DisableAll: true,
		Projection: bson.M{"catalog_id": 1}})
	if err != nil {
		logger.Error(err)
		return
	}
	if preRoom != nil {
		param.catalogID = preRoom.CatalogID
	}
}

func (param *recommendParam) findByCatalog() error {
	if param.catalogID == 0 {
		return nil
	}
	filter := bson.M{
		"room_id":     bson.M{"$nin": room.OpenListExcludeRoomIDs()},
		"catalog_id":  param.catalogID,
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	l, err := param.findRoomWithCache(param.catalogID, filter,
		options.Find().SetLimit(3).SetSort(room.SortByScore))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(l) && len(param.Data) < 2; i++ {
		if _, ok := param.excludeRoomIDs[l[i].RoomID]; !ok {
			param.Data = append(param.Data, l[i])
			param.excludeRoomIDs[l[i].RoomID] = struct{}{}
		}
	}
	return nil
}

func (param *recommendParam) findByAttention() error {
	if param.listener == nil {
		return nil
	}
	allOpenIDs, err := live.ListAllAttentionRoomIDs(param.listener.ID, true)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs := make([]int64, 0, len(allOpenIDs))
	for _, id := range allOpenIDs {
		if _, ok := param.excludeRoomIDs[id]; !ok {
			roomIDs = append(roomIDs, id)
		}
	}
	if len(roomIDs) == 0 {
		return nil
	}
	mongoOpt := options.Find().SetLimit(2).SetSort(room.SortByScore)
	filter := bson.M{
		"room_id":     bson.M{"$in": roomIDs},
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	simples, err := room.ListSimples(filter, mongoOpt, param.opt)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(simples); i++ {
		param.Data = append(param.Data, simples[i])
		param.excludeRoomIDs[simples[i].RoomID] = struct{}{}
	}
	return nil
}

func (param *recommendParam) findByHeat() error {
	filter := bson.M{
		"room_id":     bson.M{"$nin": room.OpenListExcludeRoomIDs()},
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	l, err := param.findRoomWithCache(0, filter,
		options.Find().SetLimit(7).SetSort(room.SortByScore))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(l) && len(param.Data) < recommendLimit; i++ {
		if _, ok := param.excludeRoomIDs[l[i].RoomID]; !ok {
			param.Data = append(param.Data, l[i])
			param.excludeRoomIDs[l[i].RoomID] = struct{}{}
		}
	}
	return nil
}

// findRoomWithCache 在缓存中查找推荐房间
// catalogID 传 0 代表总热度缓存
// 只缓存正确查找的情况
func (param *recommendParam) findRoomWithCache(catalogID int64, filter interface{}, mongoOpt *options.FindOptions) ([]*room.Simple, error) {
	lockRecommend.Lock()
	defer lockRecommend.Unlock()
	copyHelper := func(src []*room.Simple) []*room.Simple {
		dst := make([]*room.Simple, len(src))
		copy(dst, src)
		return dst
	}
	key := keys.KeyChatroomRecommend1.Format(catalogID)
	v, ok := service.Cache10s.Get(key)
	if ok {
		return copyHelper(v.([]*room.Simple)), nil
	}
	// 从数据库读取
	res, err := room.ListSimples(filter, mongoOpt, param.opt)
	if err != nil {
		return nil, err
	}
	service.Cache10s.Set(key, copyHelper(res), 0)
	return res, nil
}
