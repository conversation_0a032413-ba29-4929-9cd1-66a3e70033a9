package chatroom

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var startTime = int64(1578844800)

func TestFindRecommendAndTop(t *testing.T) {
	assert := assert.New(t)

	when := time.Date(2000, 1, 1, 23, 59, 59, 0, time.Local)
	r := findRecommendAndTop(0, 0, 0, when)
	assert.Empty(r)

	key := usersrank.Key(usersrank.TypeHour, time.Unix(startTime-3600, 0))
	assert.NoError(service.Redis.ZAdd(key, &redis.Z{Score: 10, Member: 512}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())
	r = findRecommendAndTop(0, 0, 0, time.Unix(startTime, 0))
	assert.NotZero(len(r))

	excludeR := findRecommendAndTop(512, 0, 0, time.Unix(startTime, 0))
	assert.Less(len(excludeR), len(r))
}

func TestActionSlideList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config.Conf.AB["slide_score_limit"] = 1.0
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	assert.NoError(service.Redis.ZAdd(key, &redis.Z{Score: 10, Member: 512}).Err())
	c := handler.NewTestContext("GET", "/", true, nil)
	r, err := ActionSlideList(c)
	require.NoError(err)
	extraResp := r.([]*slideResp)
	require.NotEmpty(extraResp)
	assert.NotNil(extraResp[0].Status)

	assert.NoError(service.Redis.Del(key).Err())
	c = handler.NewTestContext("GET", "/?catalog_id=104", true, nil)
	r, err = ActionSlideList(c)
	require.NoError(err)
	resp := r.([]*slideResp)
	assert.LessOrEqual(len(resp), len(extraResp))

	c = handler.NewTestContext("GET", "/?catalog_id=104", true, nil)
	c.User().ID = 516
	r, err = ActionSlideList(c)
	require.NoError(err)
	excludedResp := r.([]*slideResp)
	assert.LessOrEqual(len(excludedResp), len(resp))

	c = handler.NewTestContext("GET", "/?catalog_id=err", true, nil)
	_, err = ActionSlideList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "/?catalog_id=9999", true, nil)
	r, err = ActionSlideList(c)
	require.NoError(err)
	assert.Empty(r.([]*slideResp))
}

func TestNewFilter(t *testing.T) {
	assert := assert.New(t)

	assert.Nil(newFilter(nil, nil, 0))

	assert.Equal(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"catalog_id":  int64(1),
			"room_id":     bson.M{"$in": []int64{1, 2}},
		},
		newFilter([]int64{1, 2}, nil, 1),
	)

	assert.Equal(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"catalog_id":  int64(1),
			"$or": bson.A{
				bson.M{"creator_id": bson.M{"$in": []int64{3, 4}}},
				bson.M{"room_id": bson.M{"$in": []int64{1, 2}}},
			},
		},
		newFilter([]int64{1, 2}, []int64{3, 4}, 1),
	)

	assert.Equal(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"creator_id":  bson.M{"$in": []int64{3, 4}},
		},
		newFilter(nil, []int64{3, 4}, 0),
	)
}

func TestSlideFindCount(t *testing.T) {
	assert := assert.New(t)
	config.Conf.AB["slide_score_limit"] = float64(0.5)
	assert.Equal(int64(50), slideFindCount(100))
	config.Conf.AB["slide_score_limit"] = float64(1.5)
	assert.Equal(int64(100), slideFindCount(100))
	delete(config.Conf.AB, "slide_score_limit")
	assert.Equal(int64(80), slideFindCount(100))
	assert.Equal(int64(500), slideFindCount(1000))
}
