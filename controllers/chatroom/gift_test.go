package chatroom

import (
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckygiftdrop"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestDrawGiftTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(drawGiftParam{}, "room_id", "from_room_id", "gift_id", "gift_num")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(drawGiftParam{}, "room_id", "from_room_id", "gift_id", "gift_num")
}

func TestActionGiftDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URISendLuckyGift, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	c := handler.NewTestContext(http.MethodPost, "/gift/draw", true, nil)
	err := service.Redis.Del(keys.KeyUserForbidSendLuckyGift1.Format(c.UserID())).Err()
	require.NoError(err)
	_, err = ActionGiftDraw(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{
			"gift_id":  80001,
			"gift_num": 1,
			"room_id":  openingRoom.RoomID,
		})
	r, err := ActionGiftDraw(c)
	require.NoError(err)
	resp := r.(*drawGiftResp)
	assert.NotNil(resp.Lucky)
	assert.NotNil(resp.Gift)
}

func TestNewDrawGiftParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLVipList, func(any) (any, error) {
		return handler.M{"Datas": []handler.M{
			{"id": 1, "level": 1, "title": "练习生"},
		}}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					UserID:     3456835,
					VipID:      1,
					Type:       vip.TypeLiveNoble,
					Level:      1,
					ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
				},
			},
		}, nil
	})
	defer cleanup()

	service.Cache5s.Flush()
	g, err := gift.FindShowingGiftByGiftID(80001)
	require.NoError(err)
	require.NotNil(g)
	require.NotEmpty(g.AllowedNums)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.Collection().UpdateOne(ctx,
		bson.M{
			"gift_id": g.GiftID,
		},
		bson.M{
			"$set": bson.M{
				"attr": 0,
			},
		},
	)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": "1"})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParams, err, "bind 失败")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": 0, "gift_id": 1, "gift_num": 1})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParams, err, "room_id 错误")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": 1, "gift_id": 0, "gift_num": 1})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParams, err, "gift_id 错误")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": 1, "gift_id": 1, "gift_num": 0})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParams, err, "gift_num 错误")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": 987654321, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err, "房间不存在")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": room.TestClosedRoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	c.User().ID = testRoom.CreatorID
	require.NoError(service.Redis.Set(keys.KeyParams1.Format(params.KeyGift),
		`{"key":"gift"}`, time.Minute).Err())
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.NewErrForbidden("直播间未开播，无法赠送该礼物"), err)
	params.ClearCache(params.KeyGift)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": room.TestLimitedRoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.NewErrForbidden("本直播间内无法赠送该礼物"), err, "受限房间")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	c.User().ID = openingRoom.CreatorID
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParamsMsg("无法给自己的直播间送礼物"), err)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": 888, "gift_num": g.AllowedNums[0]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err, "礼物不存在")
	g2, err := gift.FindShowingGiftByGiftID(1)
	require.NoError(err)
	require.NotNil(g2)
	require.NotEqual(gift.TypeDrawSend, g2.Type)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g2.GiftID, "gift_num": g.AllowedNums[0]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err, "礼物找到了，但是类型错误")

	testDrawGiftID := int64(1122222)
	key := keys.KeyOnlineGifts0.Format()
	gifts := []gift.Gift{
		{Type: gift.TypeDrawSend, GiftID: testDrawGiftID, UserLevel: 9999999, AllowedNums: []int{1}},
	}
	service.Cache5s.Set(key, gifts, 0)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": testDrawGiftID, "gift_num": 1})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err, "礼物找到了，但是用户等级不够，没有赠送资格")
	service.Cache5s.Flush()

	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": 999})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.ErrParamsMsg("不支持的赠送数量！"), err)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[1]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.NewErrForbidden("无法购买当前礼物"), err, "奖池配置错误")
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[2]})
	_, err = newDrawGiftParam(c)
	assert.Equal(actionerrors.NewErrForbidden("无法购买当前礼物"), err, "未配置奖池")

	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	c.User().ID = 3456835
	now := goutil.TimeNow()
	expTime := now.Add(240 * time.Hour).Unix()
	a := &userappearance.UserAppearance{
		Type:         appearance.TypeMessageBubble,
		Status:       userappearance.StatusWorn,
		UserID:       3456835,
		AppearanceID: 2000,
		Name:         "测试查询头像框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://show.png",
	}
	col := userappearance.Collection()
	_, err = col.UpdateOne(ctx,
		bson.M{"user_id": a.UserID, "appearance_id": a.AppearanceID},
		bson.M{"$set": a}, options.Update().SetUpsert(true))
	require.NoError(err)
	userappearance.ClearCache(a.UserID)
	err = service.Redis.Del(keys.KeyNobleUserVips1.Format(a.UserID)).Err()
	require.NoError(err)
	param, err := newDrawGiftParam(c)
	require.NoError(err)
	assert.Equal(c.User().ID, param.userID)
	assert.NotNil(param.u)
	assert.NotNil(param.bubble)
	assert.NotNil(param.r)
	assert.NotNil(param.giftSend)
	assert.NotNil(param.drawPool)
	assert.NotNil(param.poolGifts)
	assert.NotNil(param.uv)

	// 测试超粉专属随机礼物
	service.Cache5s.Flush()
	_, err = gift.Collection().UpdateOne(ctx,
		bson.M{
			"gift_id": g.GiftID,
		},
		bson.M{
			"$set": bson.M{
				"attr": 1 << (gift.AttrDrawSendSuperFan - 1),
			},
		},
	)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"user_id": c.UserID(), "room_id": openingRoom.RoomID},
		bson.M{"$set": bson.M{"status": livemedal.StatusPending}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = newDrawGiftParam(c)
	assert.EqualError(err, "无法购买当前粉丝礼物")

	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"user_id": c.UserID(), "room_id": openingRoom.RoomID},
		bson.M{"$set": bson.M{"status": livemedal.StatusShow, "super_fan.expire_time": goutil.TimeNow().Add(time.Hour).Unix()}},
	)
	require.NoError(err)
	_, err = newDrawGiftParam(c)
	require.NoError(err)

	testBlockUserID := int64(223344)
	key = blocklist.KeyUserBlock(openingRoom.CreatorID)
	require.NoError(service.LRURedis.SAdd(key, testBlockUserID).Err())
	defer func() {
		_ = blocklist.Clear(openingRoom.CreatorID)
	}()
	c = handler.NewTestContext("POST", "/backpack/send", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	c.User().ID = testBlockUserID
	_, err = newDrawGiftParam(c)
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")

	c = handler.NewTestContext(http.MethodPost, "/gift/draw", true, handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	c.User().ID = 1234545553312
	_, err = service.Redis.Set(keys.KeyUserForbidSendLuckyGift1.Format(c.UserID()), 1, 2*time.Second).Result()
	require.NoError(err)
	_, err = ActionGiftDraw(c)
	assert.EqualError(err, "暂时无法赠送该礼物")
}

func TestDrawGiftSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mockMRPC()
	defer cleanup()

	var param *drawGiftParam
	// 奖池配置错误
	param = &drawGiftParam{
		drawPool: &gift.PoolGift{
			SSRID:     1,
			MissLimit: 1,
		},
		poolGifts: map[int64]gift.Gift{1: {}},
	}
	_, err := param.send()
	assert.Equal(actionerrors.NewErrForbidden("无法购买当前礼物"), err)

	// mrpc 返回错误
	mockAction.Action = func(c *handler.Context) (handler.ActionResponse, error) {
		return nil, actionerrors.ErrParams
	}
	g, err := gift.FindShowingGiftByGiftID(80001)
	require.NoError(err)
	require.NotNil(g)
	c := handler.NewTestContext(http.MethodPost, "/gift/draw", true,
		handler.M{"room_id": openingRoom.RoomID, "gift_id": g.GiftID, "gift_num": g.AllowedNums[0]})
	param, err = newDrawGiftParam(c)
	require.NoError(err)
	_, err = param.send()
	var rpcErr *mrpc.ClientError
	require.IsType(rpcErr, err)
	rpcErr = err.(*mrpc.ClientError)
	assert.Equal(actionerrors.ErrParams.ErrorCode(), rpcErr.Code)
	assert.Equal(actionerrors.ErrParams.ErrorInfo(), rpcErr.Message)

	// 正常情况
	mockAction.Action = func(c *handler.Context) (handler.ActionResponse, error) {
		return struct{}{}, nil
	}
	resp, err := param.send()
	require.NoError(err)
	assert.NotNil(resp.Lucky)
	assert.NotNil(resp.Gift)
	assert.NotNil(resp.User)
}

func TestDrawGiftParam_drop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	giftID, giftNum := int64(80000), int64(1)
	prizeGiftID, prizeGiftNum := int64(90000), int64(100)

	timeNowUnix := goutil.TimeNow().Unix()
	cfg := liveluckygiftdrop.Config{ID: 1, GiftID: giftID, GiftNum: giftNum, PrizeGiftID: prizeGiftID, PrizeGiftTotalNum: prizeGiftNum, StartTime: timeNowUnix, EndTime: timeNowUnix + 100}
	err := liveluckygiftdrop.DB().Delete(liveluckygiftdrop.Config{}).Error
	require.NoError(err)
	err = liveluckygiftdrop.DB().Create(&cfg).Error
	require.NoError(err)
	counterKey := keys.KeyLuckyGiftDropCount1.Format(cfg.ID)
	err = service.Redis.Del(counterKey).Err()
	require.NoError(err)

	// 配置掉落礼物不存在于礼物池
	param := &drawGiftParam{
		poolGifts: map[int64]gift.Gift{90001: {GiftID: 90001, Type: gift.TypeDrawReceive}},
		GiftID:    giftID,
		GiftNum:   int(giftNum),
		RoomID:    12,
		userID:    13,
	}
	dropResult := param.drop()
	assert.Nil(dropResult)
	count, err := service.Redis.Get(counterKey).Int()
	assert.True(serviceredis.IsRedisNil(err))
	assert.Zero(count)

	// 插入有效的 record，确保能命中掉落大奖
	err = liveluckygiftdrop.DB().Delete(liveluckygiftdrop.Record{}).Error
	require.NoError(err)
	record := &liveluckygiftdrop.Record{
		ID:           1,
		ConfigID:     cfg.ID,
		ExpectedTime: timeNowUnix,
	}
	err = liveluckygiftdrop.DB().Create(record).Error
	require.NoError(err)
	param = &drawGiftParam{
		poolGifts: map[int64]gift.Gift{90000: {GiftID: 90000, Type: gift.TypeDrawReceive}},
		GiftID:    giftID,
		GiftNum:   int(giftNum),
	}
	dropResult = param.drop()
	assert.Equal(prizeGiftID, dropResult.PrizeGiftID)
	assert.Equal(record.ID, dropResult.HitRecordID)
	// 确认 redis 和 MySQL 能被正常更新
	count, err = service.Redis.Get(counterKey).Int()
	require.NoError(err)
	assert.Equal(1, count)
	var afterRecords []liveluckygiftdrop.Record
	err = liveluckygiftdrop.DB().Find(&afterRecords).Error
	require.NoError(err)
	// 更新上一条 record 并插入下一条待投放的 record
	require.Len(afterRecords, 2)
	assert.NotZero(afterRecords[0].ActualTime)
	assert.Equal(param.RoomID, afterRecords[0].RoomID)
	assert.Equal(param.userID, afterRecords[0].UserID)
	assert.NotZero(afterRecords[1].ExpectedTime)
	assert.Zero(afterRecords[1].ActualTime)
	assert.Zero(afterRecords[1].RoomID)
	assert.Zero(afterRecords[1].UserID)
}

func TestGiftDraw_tryDisableDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count := 0
	cancel := mrpc.SetMock("im://broadcast", func(interface{}) (interface{}, error) {
		count++
		return nil, nil
	})
	defer cancel()

	param := drawGiftParam{
		giftSend: new(gift.Gift),
		r:        new(room.Room),
		drawPool: new(gift.PoolGift),
		userID:   20201201,
		RoomID:   20201201,
		GiftID:   20201201,
	}
	param.giftSend.GiftID = 20201201
	param.r.CreatorID = 20201201
	param.r.RoomID = 20201201
	now := goutil.TimeNow()
	require.NoError(livecustom.AddRoomCustomGift(param.RoomID, param.GiftID, now, now.Add(time.Minute), livecustom.SourceDefault))

	param.tryDisableDraw(param.GiftID)
	res, err := livecustom.FindAllRoomCustomGiftIDs(param.RoomID)
	require.NoError(err)
	assert.NotEmpty(res)

	param.giftSend.Attr.Set(gift.AttrDisableDrawRoomCustomAfterSSR)
	param.tryDisableDraw(param.GiftID)
	res, err = livecustom.FindAllRoomCustomGiftIDs(param.RoomID)
	require.NoError(err)
	assert.NotEmpty(res)

	param.drawPool.SSRID = 20201201
	param.tryDisableDraw(param.GiftID)
	res, err = livecustom.FindAllRoomCustomGiftIDs(param.RoomID)
	require.NoError(err)
	assert.Empty(res)
	assert.Equal(1, count)
}

func TestGiftDrawAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := drawGiftParam{
		giftReceive: new(gift.Gift),
		r:           new(room.Room),
		userID:      20201201,
	}
	param.r.CreatorID = 20201201
	param.r.RoomID = 20201201
	rankPre, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.r.CreatorID)
	require.NoError(err)
	assert.NotPanics(func() { param.addRevenueRank() })
	rankAfter, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.r.CreatorID)
	require.NoError(err)
	assert.Equal(rankPre, rankAfter)
	param.giftReceive.Price = 100
	assert.NotPanics(func() { param.addRevenueRank() })
	rankAfter, err = usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(), param.r.CreatorID)
	require.NoError(err)
	assert.Equal(rankAfter.Revenue, rankPre.Revenue+100)
}

func TestGiftDrawAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := drawGiftParam{giftReceive: &gift.Gift{Point: 1}}
	assert.NotPanics(func() { param.addPK() })

	param.giftReceive.Price = 10
	param.r = new(room.Room)
	param.addPK()
	assert.Empty(param.broadcastElems)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	fighters := [2]*livepk.Fighter{
		{RoomID: 12345},
		{RoomID: 45678},
	}
	pk, err := livepk.CreatePKInvitationRecord(ctx, 10*time.Second, "", "", fighters)
	require.NoError(err)
	require.NotNil(pk)
	defer func() {
		_, err := livepk.PKCollection().DeleteMany(ctx, bson.M{"_id": pk.OID})
		require.NoError(err)
	}()
	param.r.RoomID = fighters[0].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	assert.Len(param.broadcastElems, 2)
}

func TestGiftDrawAddActivity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIRankRevenue, func(input any) (output any, err error) {
		rankRevenue, ok := input.(*userapi.RankRevenueParams)
		require.True(ok)
		assert.Equal(&userapi.RankRevenueParams{
			UserID:        12,
			CreatorID:     10,
			RoomID:        22489473,
			TransactionID: 1234567890,
			ConfirmTime:   now.Unix(),
			GuildID:       123456,
			Gift: &userapi.RankRevenueGift{
				GiftID:         2,
				GiftName:       "test2",
				GiftType:       0,
				GiftNum:        1,
				GiftPrice:      20,
				GiftPoint:      0,
				GiftAttr:       0,
				LuckyGiftID:    1,
				LuckyGiftName:  "test1",
				LuckyGiftPrice: 10,
				LuckyGiftNum:   0,
			},
		}, rankRevenue)
		return nil, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIInteractionRankRevenue, func(input any) (output any, err error) {
		return nil, nil
	})
	defer cancel()

	assert.NotPanics(func() {
		param := &drawGiftParam{
			r: testRoom,
			u: &liveuser.Simple{UID: 12},
			balance: &userapi.BalanceResp{
				TransactionID: 1234567890,
			},
			giftSend: &gift.Gift{
				GiftID: 1,
				Name:   "test1",
				Price:  10,
			},
			giftReceive: &gift.Gift{
				GiftID: 2,
				Name:   "test2",
				Price:  20,
			},
		}

		param.addActivity()
	})
}

func TestGiftDrawAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.Find(roomID)
	require.NoError(err)
	require.NotNil(r)
	param := drawGiftParam{
		r:           r,
		giftReceive: &gift.Gift{Price: 1},
		userID:      20201201,
	}
	p := livemedal.AddPointParam{
		RoomOID:   r.OID,
		RoomID:    r.RoomID,
		CreatorID: r.CreatorID,
		MedalName: r.Medal.Name,
		UserID:    param.userID,
		Type:      livemedal.TypeQuestionAddMedalPoint,
		PointAdd:  1,
	}
	medalUpdatedInfo, err := p.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	// 重设今日开始亲密度
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"_id": medalUpdatedInfo.After.OID}, bson.M{"$set": bson.M{
		"t_point": medalUpdatedInfo.After.Point,
	}})
	require.NoError(err)

	*param.r = *testRoom
	require.NotNil(param.r.Medal)
	lmPre, err := livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	require.NotNil(lmPre, "数据库不删除应该不会出现问题")

	// 普通
	service.Cache5Min.Flush()
	assert.NotPanics(func() { param.addMedalPoint() })
	lmAfter, err := livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	assert.Equal(lmPre.Point+1, lmAfter.Point)

	// 没有分数可加
	param.giftReceive.Price = 0
	param.giftReceive.Attr = 3
	param.addMedalPoint()
	lmAfter, err = livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	assert.Equal(lmPre.Point+1, lmAfter.Point)

	// 勋章为空, 提前返回
	param.r.Medal = nil
	assert.NotPanics(func() { param.addMedalPoint() })
	lmAfter, err = livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	assert.Equal(lmPre.Point+1, lmAfter.Point)
}

func TestDrawGiftParam_addMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := drawGiftParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		giftReceive: &gift.Gift{
			Price: 10,
		},
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestDrawGiftParam_addUserRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := drawGiftParam{
		userID: 1919,
		giftSend: &gift.Gift{
			GiftID: 8101919,
			Price:  114514,
		},
		GiftNum: 2,
	}

	keyBlock := keys.KeyRedeemPointBlockedGiftIDs0.Format()
	require.NoError(service.Redis.Del(keyBlock).Err())

	var userPoint shop.UserRedeemPoint
	now := goutil.TimeNow()
	require.NoError(userPoint.DB().Delete("", "year = ? AND user_id = ?", now.Year(), param.userID).Error)

	param.addUserRedeemPoint()
	err := userPoint.DB().Where("year = ? AND user_id = ?", now.Year(), param.userID).Take(&userPoint).Error
	require.NoError(err)
	currentSpent := param.giftSend.Price * int64(param.GiftNum)
	assert.EqualValues(currentSpent, userPoint.Point)
	assert.EqualValues(currentSpent, userPoint.AllPoint)

	param.addUserRedeemPoint()
	err = userPoint.DB().Where("year = ? AND user_id = ?", now.Year(), param.userID).Take(&userPoint).Error
	require.NoError(err)
	assert.EqualValues(currentSpent*2, userPoint.Point)
	assert.EqualValues(currentSpent*2, userPoint.AllPoint)

	require.NoError(service.Redis.SAdd(keyBlock, param.giftSend.GiftID).Err())
	param.addUserRedeemPoint()
	err = userPoint.DB().Where("year = ? AND user_id = ?", now.Year(), param.userID).Take(&userPoint).Error
	require.NoError(err)
	assert.EqualValues(currentSpent*2, userPoint.Point)
	assert.EqualValues(currentSpent*2, userPoint.AllPoint)
}

func TestGiftDrawAddUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	}) // 防止任务影响测试
	defer goutil.SetTimeNow(nil)
	param := drawGiftParam{
		userID:      12,
		giftSend:    &gift.Gift{Price: 5},
		giftReceive: &gift.Gift{Price: 10},
		RoomID:      123,
		r: &room.Room{
			Helper: room.Helper{CreatorUsername: "aaa"},
		},
	}
	before, err := liveuser.Find(param.userID)
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(param.giftSend.Price*10, after.Contribution-before.Contribution)
	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(param.giftSend.Price*2*10, after.Contribution-before.Contribution)
}

func TestGiftDrawBuildIMMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := drawGiftParam{
		userID: 12,
		u:      &liveuser.Simple{UID: 12},
		r:      testRoom,
		giftSend: &gift.Gift{
			GiftID: 80001,
			Name:   "lucky",
			Price:  10,
		},
		giftReceive: &gift.Gift{
			Type:   gift.TypeDrawReceive,
			GiftID: 90001,
			Name:   "send",
			Price:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.u, param.bubble).SetGift(param.giftReceive, 1).
		SetLuckyGift(param.giftSend, 1, false)
	require.NotPanics(func() { param.buildIMMessage() })
	assert.Len(param.broadcastElems, 1)
	param.giftReceive.NotifyMessage = "test"
	param.buildIMMessage()
	assert.Len(param.broadcastElems, 3)

	param = drawGiftParam{
		FromRoomID: openingRoomID,
		userID:     12,
		u:          &liveuser.Simple{UID: 12},
		r:          testRoom,
		giftSend: &gift.Gift{
			GiftID: 80001,
			Name:   "lucky",
			Price:  10,
		},
		giftReceive: &gift.Gift{
			Type:   gift.TypeDrawReceive,
			GiftID: 90001,
			Name:   "send",
			Price:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.u, param.bubble).SetGift(param.giftReceive, 1).
		SetLuckyGift(param.giftSend, 1, false)
	require.NotPanics(func() { param.buildIMMessage() })
	require.Len(param.broadcastElems, 2)
	assert.Equal(param.r.RoomID, param.broadcastElems[0].RoomID)
	assert.Zero(param.broadcastElems[0].UserID)
	assert.Equal(param.u.UserID(), param.broadcastElems[1].UserID)
	assert.Equal(param.FromRoomID, param.broadcastElems[1].RoomID)
}

func TestGiftDrawBroadcast(t *testing.T) {
	assert := assert.New(t)

	var notifyCount int
	cancel := mrpc.SetMock("im://broadcast/many", func(i interface{}) (interface{}, error) {
		notifyCount++
		tutil.PrintJSON(i)
		return true, errors.New("unittest error")
	})
	defer cancel()
	r := new(room.Room)
	r.CreatorUsername = "test"
	param := drawGiftParam{
		broadcastElems: []*userapi.BroadcastElem{
			{Type: 1, RoomID: 123, Payload: "test"},
		},
	}
	param.broadcast()
	assert.Equal(notifyCount, 1)
}
