package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRevenueTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	dbKeys := []string{"lucky_gift_id", "gift_id", "gift_num", "price", "goods_id", "user_id", "sent_time"}
	kc.Check(liveGift{}, dbKeys...)

	kc = tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(liveGiftProj(), dbKeys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(revenueNobleResp{}, "Datas", "pagination")
	kc.Check(revenueGiftResp{}, "data", "pagination")
	kc.Check(liveGift{}, "gift_id", "gift_num", "gift_name", "gift_icon_url", "price", "user_id", "username", "iconurl", "sent_time")
	kc.Check(revenueQuestionElem{}, "user_id", "username", "iconurl", "price", "created_time", "answered_time")
	kc.Check(revenueQuestionResp{}, "data", "pagination")
}

func TestActionBalanceNoble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 历史记录
	c := handler.NewTestContext("GET", "/revenue/nobles?p=1", true, nil)
	r, err := ActionRevenueNobles(c)
	require.NoError(err)
	resp := r.(*revenueNobleResp)
	assert.Empty(tutil.KeyExists(tutil.JSON, resp, "Datas", "pagination"))
	assert.Equal(int64(len(resp.Data)), min(resp.Page.Count%20, 20))
	assert.Len(resp.Data, 1)

	// 本次开播
	c = handler.NewTestContext("GET", "/revenue/nobles?p=1&type=1", true, nil)
	r, err = ActionRevenueNobles(c)
	require.NoError(err)
	resp = r.(*revenueNobleResp)
	assert.Empty(resp.Data)
}

func TestActionRevenueGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	addTestData()
	c := handler.NewTestContext("GET", "/revenue/gifts?type=-1", true, nil)
	c.User().ID = 99999
	_, err := ActionRevenueGifts(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", "/revenue/gifts", true, nil)
	c.User().ID = 99999
	r, err := ActionRevenueGifts(c)
	require.NoError(err)
	resp1 := r.(revenueGiftResp)
	assert.Empty(resp1.Data)

	c = handler.NewTestContext("GET", "/revenue/gifts?type=2p=1&end_time=9999999999", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp1 = r.(revenueGiftResp)
	assert.NotEmpty(resp1.Data)
	c = handler.NewTestContext("GET", "/revenue/gifts?type=1&p=1", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp2 := r.(revenueGiftResp)
	assert.NotEqual(len(resp1.Data), len(resp2.Data))

	roomID := testRoom.RoomID
	_, err = room.Update(roomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)
	c = handler.NewTestContext("GET", "/revenue/gifts?p=1&type=1&gift_type=1", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp1 = r.(revenueGiftResp)
	assert.NotEmpty(resp1.Data)

	_, err = room.Update(roomID, bson.M{"status.open": room.StatusOpenFalse})
	require.NoError(err)
	c = handler.NewTestContext("GET", "/revenue/gifts?p=1&type=1&gift_type=2", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp1 = r.(revenueGiftResp)
	assert.Empty(resp1.Data)

	c = handler.NewTestContext("GET", "/revenue/gifts?p=1&end_time=10", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp1 = r.(revenueGiftResp)
	assert.Empty(resp1.Data)

	deleteTestData()
	c = handler.NewTestContext("GET", "/revenue/gifts?type=1&p=1", true, nil)
	c.User().ID = testRoom.CreatorID
	r, err = ActionRevenueGifts(c)
	require.NoError(err)
	resp1 = r.(revenueGiftResp)
	assert.Empty(resp1.Data)
}

func TestRevenueGiftsFindGiftAndUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testGoodsID int64 = 1321312312
	err := service.LiveDB.Delete(livegoods.LiveGoods{ID: testGoodsID}).Error
	require.NoError(err)
	err = service.LiveDB.Create(livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeGashapon,
	}).Error
	require.NoError(err)

	resp := revenueGiftResp{Data: []liveGift{}}
	assert.NotPanics(func() { resp.findGiftAndUser() })
	resp.Data = []liveGift{
		{
			LuckyGiftID: 301,
			GiftID:      1,
			UserID:      12,
		},
		{
			GiftID:  1,
			UserID:  12,
			GoodsID: testGoodsID,
		},
	}
	resp.findGiftAndUser()
	g, err := gift.FindShowingGiftByGiftID(resp.Data[0].GiftID)
	require.NoError(err)
	require.NotNil(g)
	luckyGift, err := gift.FindShowingGiftByGiftID(resp.Data[0].LuckyGiftID)
	require.NoError(err)
	require.NotNil(luckyGift)
	u, err := mowangskuser.FindByUserID(12)
	require.NoError(err)
	require.NotNil(u)
	assert.Equal(fmt.Sprintf("%s（%s）", g.Name, luckyGift.Name), resp.Data[0].GiftName)
	assert.Equal(fmt.Sprintf("%s（%s）", g.Name, "超能魔方"), resp.Data[1].GiftName)
	assert.Equal(u.Username, resp.Data[0].Username)
	assert.Equal(u.ID, resp.Data[0].UserID)
}

func TestActionRevenueSuperFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/revenue/superfans?p=a", true, nil)
	_, err := ActionRevenueSuperFans(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/revenue/superfans", true, nil)
	r, err := ActionRevenueSuperFans(c)
	require.NoError(err)
	resp := r.(*revenueSuperFanResp)
	assert.Empty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/revenue/superfans", true, nil)
	c.User().ID = 10
	r, err = ActionRevenueSuperFans(c)
	require.NoError(err)
	resp = r.(*revenueSuperFanResp)
	require.NotEmpty(resp.Data)
	assert.NotEmpty(resp.Data[0].Username)
	assert.NotEmpty(resp.Data[0].IconURL)
	assert.Equal("开通直播超粉--1 个月", resp.Data[0].Title)

	c = handler.NewTestContext(http.MethodGet, "/revenue/superfans?type=1", true, nil)
	c.User().ID = 10
	r, err = ActionRevenueSuperFans(c)
	require.NoError(err)
	resp = r.(*revenueSuperFanResp)
	require.Empty(resp.Data)
}

func TestActionRevenueQuestions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 添加测试数据
	testUserID := int64(10)
	room, err := room.FindOne(bson.M{"creator_id": testUserID})
	require.NoError(err)
	require.NotNil(room)

	lq := &livequestion.LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(goutil.TimeNow()),
		Helper: livequestion.Helper{
			CreatedTime: goutil.TimeNow().Add(-time.Minute),
			RoomID:      room.RoomID,
			RoomOID:     room.OID,
			UserID:      testUserID,
			Status:      livequestion.StatusFinished,
			Price:       30,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"_id": lq.OID}
	defer func() {
		_, err := livequestion.Collection().DeleteOne(ctx, filter)
		assert.NoError(err)
	}()
	_, err = livequestion.Collection().InsertOne(ctx, lq)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/revenue/questions?p=-1", true, nil)
	_, err = ActionRevenueQuestions(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/revenue/questions?p=1", true, nil)
	c.User().ID = 99999
	_, err = ActionRevenueQuestions(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = handler.NewTestContext(http.MethodGet, "/revenue/questions?p=1", true, nil)
	c.User().ID = testUserID
	r, err := ActionRevenueQuestions(c)
	require.NoError(err)
	resp := r.(*revenueQuestionResp)
	assert.NotEmpty(resp.Data)

	c = handler.NewTestContext(http.MethodGet, "/revenue/questions?p=1&type=1", true, nil)
	c.User().ID = 10
	r, err = ActionRevenueQuestions(c)
	require.NoError(err)
	resp = r.(*revenueQuestionResp)
	assert.Empty(resp.Data)
}
