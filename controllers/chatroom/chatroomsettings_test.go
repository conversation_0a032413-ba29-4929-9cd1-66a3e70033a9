package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livesetting"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	testRoomID = int64(223344)
	testKey1   = "live_assistant_settings"
)

func TestNewChatroomSettingsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// value is ""
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
	})
	_, err := newChatroomSettingsParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	// 配置信息大于最大长度限制
	targetStr := string(make([]byte, livesetting.MaxChatroomSettingsLen+1))
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id": testRoomID,
		"key":     testKey1,
		"value":   targetStr,
	})
	_, err = newChatroomSettingsParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	// 房主非自己
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id": testRoomID,
		"key":     "live_assistant_settings",
		"value":   `{"name":"jesse"}`,
	})
	_, err = newChatroomSettingsParam(c, true)
	assert.Equal(actionerrors.ErrForbidden, err)

	// 正确
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id": testRoomID,
		"key":     "live_assistant_settings",
		"value":   `{"name":"jesse"}`,
	})
	c.User().ID = int64(223344)
	param, err := newChatroomSettingsParam(c, true)
	require.NoError(err)
	assert.NotNil(param)

	// settings get
	c = handler.NewTestContext(http.MethodGet, "/get?room_id=223344&key=live_assistant_settings", true, nil)
	c.User().ID = int64(223344)
	param, err = newChatroomSettingsParam(c, false)
	require.NoError(err)
	assert.NotNil(param)
}

func mock(t *testing.T) {
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livesetting.Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = livesetting.Collection().InsertOne(ctx, livesetting.Setting{
		RoomID: testRoomID,
		Key:    testKey1,
		Value:  `{"name":"哇哈哈"}`,
	})
	require.NoError(err)
}

func TestActionSettingsSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mock(t)

	// 未传递配置信息
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
	})
	c.User().ID = int64(223344)
	_, err := ActionSettingsSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 配置信息大于最大长度限制
	targetStr := string(make([]byte, livesetting.MaxChatroomSettingsLen+1))
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
		"key":     testKey1,
		"value":   targetStr,
	})
	c.User().ID = int64(223344)
	_, err = ActionSettingsSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	// json no valid
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
		"key":     testKey1,
		"value":   "json err",
	})
	c.User().ID = int64(223344)
	_, err = ActionSettingsSet(c)
	assert.EqualError(err, "JSON 格式错误")

	// 非自己的直播间
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
		"key":     testKey1,
		"value":   `{"name":"jesse"}`,
	})
	_, err = ActionSettingsSet(c)
	assert.Equal(actionerrors.ErrForbidden, err)

	// success
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/settings/set", true, handler.M{
		"room_id": testRoomID,
		"key":     testKey1,
		"value":   `{"name":"jesse"}`,
	})
	c.User().ID = int64(223344)
	resp, err := ActionSettingsSet(c)
	require.NoError(err)
	assert.Equal("success", resp)
	setting, err := livesetting.FindByRoomID(testRoomID, testKey1)
	require.NoError(err)
	assert.NotNil(setting)
}

func TestActionSettingsGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mock(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/settings/get?room_id=223344&key=live_assistant_settings", true, nil)
	c.User().ID = int64(223344)
	resp, err := ActionSettingsGet(c)
	require.NoError(err)
	assert.NotNil(resp)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/settings/get?room_id=22489473&key=live_assistant_settings", true, nil)
	c.User().ID = int64(10)
	resp, err = ActionSettingsGet(c)
	require.NoError(err)
	r, ok := resp.(*livesetting.Setting)
	require.True(ok)
	assert.NotEmpty(r.Key)
	assert.Empty(r.Value)
}
