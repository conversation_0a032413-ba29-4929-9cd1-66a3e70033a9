package chatroom

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guildrole"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const fifty = 50

type vitalityLogsParams struct {
	creatorID int64
	guildID   int64

	fromTime time.Time
	toTime   time.Time

	p        int64
	pageSize int64
}

type vitalityLogsResp struct {
	Data       []*liveaddendum.Log `json:"Datas"`
	Pagination goutil.Pagination   `json:"pagination"`
	Vitality   *int                `json:"vitality,omitempty"`
}

func (params *vitalityLogsParams) load(c *handler.Context) error {
	// WORKAROUND: 兼容老版本，默认返回历史所有数据，下次发版时，可以改为返回最近 7 天
	startDate, endDate, err := c.GetParamDateRange(time.Unix(0, 0).Format(goutil.TimeFormatYMD),
		goutil.TimeNow().Format(goutil.TimeFormatYMD))
	if err != nil {
		return err
	}
	params.fromTime = startDate
	params.toTime = endDate.AddDate(0, 0, 1)

	params.p, params.pageSize, err = c.GetParamPage(&handler.PageOption{DefaultPageSize: fifty})
	if err != nil {
		return actionerrors.ErrParams
	}

	params.creatorID, err = c.GetDefaultParamInt64("creator_id", c.UserID())
	if err != nil {
		return actionerrors.ErrParams
	}

	if params.creatorID != c.UserID() {
		isGuildManager, lc, err := CheckOwnerOrAgentTimeRange(params.creatorID, c.UserID(), &params.fromTime, params.toTime)
		if err != nil {
			return err
		}
		if !isGuildManager {
			return actionerrors.ErrNoAuthority
		}
		params.guildID = lc.GuildID
	}
	return nil
}

func (params *vitalityLogsParams) buildResp() (handler.ActionResponse, error) {
	var err error
	var resp vitalityLogsResp
	resp.Data, resp.Pagination, err = liveaddendum.ListLogs(params.creatorID, params.guildID, params.fromTime, params.toTime, params.p, params.pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if params.p == 1 {
		resp.Vitality, err = liveaddendum.Vitality(params.creatorID)
		if err != nil {
			logger.Error(err)
			// PASS
			resp.Vitality = new(int)
		}
	}
	return &resp, nil
}

// ActionVitalityLogs 惩罚日志
/**
 * @api {get} /api/v2/chatroom/vitality/logs 主播元气值日志
 * @apiDescription 主播自己、公会会长和主播的经纪人可以查看，
 *   当公会会长和主播的经纪人访问时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [creator_id] 主播 ID，默认为当前登录的用户 ID
 * @apiParam {String} [start_date] 筛选的起始时间，默认为 7 天前，格式 "2006-01-02"
 * @apiParam {String} [end_date] 筛选的结束时间，默认为今天，格式 "2006-01-02"
 * @apiParam {Number} [p] 页数
 * @apiParam {Number} [pagesize] 每页数目
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "vitality": 8, // 当前元气值, p == 1 时返回
 *       "Datas": [
 *         {
 *           "creator_id": 12,
 *           "operator": -1, // 操作类型 1: 恢复元气值; -1: 扣除元气值; -2: 切断直播; -3: 封禁直播间
 *           "vitality_change": 2,
 *           "reason": "扣除原因",
 *           "create_time": 147234568
 *         },
 *         ...
 *         {
 *           "creator_id": 12,
 *           "operator": -3,
 *           "vitality_change": 0,
 *           "reason": "扣除原因",
 *           "intro": "10 天", // 封禁时长
 *           "create_time": 141234568
 *         }
 *       ],
 *       "pagination": {
 *         "count": 10,
 *         "p": 1,
 *         "pagesize": 20,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionVitalityLogs(c *handler.Context) (handler.ActionResponse, error) {
	var params vitalityLogsParams
	err := params.load(c)
	if err != nil {
		return nil, err
	}
	return params.buildResp()
}

// CheckOwnerOrAgentTimeRange 访问用户是被查看用户的会长或经纪人时，如果查询开始时间早于主播入会时间，将查询开始时间改为主播入会时间，返回合约信息
// isOwnerOrAgent 是否是会长或者是主播经纪人，lc 合约信息
func CheckOwnerOrAgentTimeRange(creatorID, userID int64, fromTime *time.Time, toTime time.Time) (isOwnerOrAgent bool, lc *livecontract.LiveContract, err error) {
	/* 因产品提出要公会管理可以查看主播在公会的历史数据，注释查询时间验证
	if fromTime == nil {
		return false, nil, actionerrors.ErrParams
	}
	*/
	role, lc, err := guildrole.FindAssociatedContractWithAgent(creatorID, userID)
	if err != nil {
		return false, nil, err
	}
	if lc == nil {
		return false, nil, actionerrors.ErrCreatorNotInYourGuild
	}
	/*
		背景：线上会大量出现主播和当前公会重复解约签约
		接口【主播报表详情、返回房间的 live logs 和统计信息、主播元气值日志】的原有逻辑：公会管理只能查看主播当前合约期内的数据，无法查看主播在该公会历史上的数据，无法满足运营需求
		需求：公会管理可以查看主播在该公会的历史数据
		解决方案：
			1. 不修改传入的查询开始时间
			2. 查询时将数据和合约表进行关联

		// 会长和经纪人只能查看用户主播入会后的数据
		if fromTime.Unix() < lc.ContractStart {
			*fromTime = time.Unix(lc.ContractStart, 0)
			if util.BeginningOfDay(toTime).AddDate(0, 0, 1).Before(*fromTime) {
				return true, lc, actionerrors.ErrParamsMsg("仅能查看入会后的数据")
			}
		}
	*/
	return role.IsGuildManager(), lc, nil
}
