package guild

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)
	m.Run()
}

var (
	testUserID     = int64(12)
	testRoomID     = int64(18113499)
	testGuildID    = int64(3)
	testUploadTime = int64(12345678)
)

func insertTestData() (func() error, error) {
	lr := livereview.LiveReview{
		UserID:     testUserID,
		RoomID:     testRoomID,
		GuildID:    testGuildID,
		Type:       livereview.TypeScheduleRecommend,
		Status:     livereview.StatusReviewing,
		UploadTime: testUploadTime,
		ReviewInfo: livereview.ReviewInfo{
			ImageURL: "oss://test.png",
		},
	}
	err := service.DB.Save(&lr).Error
	if err != nil {
		return nil, err
	}

	ls := guildscheduleapply.LiveGuildScheduleApply{
		GuildID:   testGuildID,
		CreatorID: testUserID,
		RoomID:    testRoomID,
		Status:    guildscheduleapply.StatusReviewing,
		StartTime: 86400,
		Duration:  86400,
	}
	err = guildscheduleapply.LiveGuildScheduleApply{}.DB().Save(&ls).Error
	if err != nil {
		return nil, err
	}
	return clearTestData, nil
}

func clearTestData() error {
	err := service.DB.Table(livereview.TableName()).Delete("", "upload_time = ?", testUploadTime).Error
	if err != nil {
		return err
	}
	return guildscheduleapply.LiveGuildScheduleApply{}.DB().Delete("", "start_time = 86400 AND duration = 86400").Error
}

func TestActionScheduleReviewList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clear, err := insertTestData()
	require.NoError(err)
	defer func() {
		require.NoError(clear())
	}()
	c := handler.NewTestContext(http.MethodGet, "/", true, nil)
	resp, err := ActionScheduleReviewList(c)
	require.NoError(err)
	res := resp.(*scheduleListResp)
	assert.NotEmpty(res.Data)

	c = handler.NewTestContext(http.MethodGet, "/?creator_id=10", true, nil)
	resp, err = ActionScheduleReviewList(c)
	require.NoError(err)
	res = resp.(*scheduleListResp)
	assert.Empty(res.Data)

	c = handler.NewTestContext(http.MethodGet, "/?guild_id=1", true, nil)
	resp, err = ActionScheduleReviewList(c)
	require.NoError(err)
	res = resp.(*scheduleListResp)
	assert.Empty(res.Data)

	c = handler.NewTestContext(http.MethodGet, "/?creator_username=xxx", true, nil)
	resp, err = ActionScheduleReviewList(c)
	require.NoError(err)
	res = resp.(*scheduleListResp)
	assert.Empty(res.Data)

	c = handler.NewTestContext(http.MethodGet, "/?guild_id=3&guild_name=公&creator_id=12&creator_username=零月", true, nil)
	resp, err = ActionScheduleReviewList(c)
	require.NoError(err)
	res = resp.(*scheduleListResp)
	assert.NotEmpty(res.Data)
}

func TestActionScheduleReviewPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": nil,
	})
	_, err := ActionScheduleReviewPass(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": []scheduleReviewElem{{
			CreatorID:  1,
			UploadTime: 0,
		}, {
			CreatorID:  2,
			UploadTime: 0,
		}, {
			CreatorID:  3,
			UploadTime: 0,
		}},
	})
	resp, err := ActionScheduleReviewPass(c)
	require.NoError(err)
	assert.Equal("主播 [1 2 3] 审核失败", resp)

	clear, err := insertTestData()
	require.NoError(err)
	defer func() {
		require.NoError(clear())
	}()
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": []scheduleReviewElem{{
			CreatorID:  testUserID,
			UploadTime: testUploadTime,
		}},
	})
	resp, err = ActionScheduleReviewPass(c)
	require.NoError(err)
	assert.Equal("审核成功", resp)
	apply, err := guildscheduleapply.FindApply(testUserID, testGuildID)
	require.NoError(err)
	require.NotNil(apply)
	assert.Equal(guildscheduleapply.StatusPassed, apply.Status)
	assert.NotEmpty(apply.Cover)
	assert.Empty(apply.Reason)
	r, err := room.FindOne(bson.M{"room_id": testRoomID}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
}

func TestActionScheduleReviewRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": []scheduleReviewElem{{
			CreatorID:  1,
			UploadTime: 0,
		}},
		"reason": "      ",
	})
	_, err := ActionScheduleReviewRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data":   nil,
		"reason": "含有敏感信息",
	})
	_, err = ActionScheduleReviewRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": []scheduleReviewElem{{
			CreatorID:  1,
			UploadTime: 0,
		}, {
			CreatorID:  2,
			UploadTime: 0,
		}, {
			CreatorID:  3,
			UploadTime: 0,
		}},
		"reason": "含有敏感信息",
	})
	resp, err := ActionScheduleReviewRefuse(c)
	require.NoError(err)
	assert.Equal("主播 [1 2 3] 审核失败", resp)

	clear, err := insertTestData()
	require.NoError(err)
	defer func() {
		require.NoError(clear())
	}()
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{
		"data": []scheduleReviewElem{{
			CreatorID:  testUserID,
			UploadTime: testUploadTime,
		}},
		"reason": "含有敏感信息",
	})
	resp, err = ActionScheduleReviewRefuse(c)
	require.NoError(err)
	assert.Equal("审核成功", resp)
	apply, err := guildscheduleapply.FindApply(testUserID, testGuildID)
	require.NoError(err)
	require.NotNil(apply)
	assert.Equal(guildscheduleapply.StatusRefused, apply.Status)
	assert.Empty(apply.Cover)
	assert.Equal("含有敏感信息", apply.Reason)
}

func TestAdminLogContent(t *testing.T) {
	assert := assert.New(t)

	param := livereview.LiveReview{
		ID:      1,
		UserID:  1,
		RoomID:  1,
		GuildID: 1,
		Type:    livereview.TypeScheduleRecommend,
		Status:  livereview.StatusPassed,
		ReviewInfo: livereview.ReviewInfo{
			ImageURL: "oss://test.png",
		},
	}

	catalog, intro, id := adminLogContent(&param)
	assert.Equal(userapi.CatalogReviewGuildRecommendImage, catalog)
	assert.Equal("审核通过首页推荐位推荐图，公会 ID: 1, 直播间 ID: 1, 主播 ID: 1, 首页推荐位推荐图: https://static-test.missevan.com/test.png", intro)
	assert.Equal(param.ID, id)
}

func TestRefuseSysMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := livereview.LiveReview{
		ID:      1,
		UserID:  1,
		RoomID:  1,
		GuildID: testGuildID,
		Type:    livereview.TypeScheduleRecommend,
		Status:  livereview.StatusPassed,
		ReviewInfo: livereview.ReviewInfo{
			ImageURL: "oss://test.png",
		},
	}

	userMap := make(map[int64]*mowangskuser.Simple)
	userMap[param.UserID] = &mowangskuser.Simple{
		ID:       param.UserID,
		Username: "test",
	}
	title, content, err := refuseSysMessage(&param, userMap, "含有敏感词语")
	require.NoError(err)
	assert.Equal("主播 test 的 首页推荐位推荐图 未通过审核", title)
	assert.Equal("亲爱的公会管理员，非常抱歉，主播 test 的 首页推荐位推荐图 未通过审核。拒绝原因：含有敏感词语。", content)
}
