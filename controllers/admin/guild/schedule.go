package guild

import (
	"fmt"
	"html"
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type searchScheduleListParam struct {
	CreatorID       int64
	CreatorUsername string
	GuildID         int64
	GuildName       string
	P               int64
	PageSize        int64
}

type scheduleListElem struct {
	ReviewID        int64  `json:"review_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	GuildID         int64  `json:"guild_id"`
	GuildName       string `json:"guild_name"`
	ImageURL        string `json:"image_url"`
	UploadTime      int64  `json:"upload_time"`
}

type scheduleListResp struct {
	Data       []*scheduleListElem `json:"data"`
	Pagination goutil.Pagination   `json:"pagination"`
}

type searchReview struct {
	livereview.WithUserInfo
	GuildName string `gorm:"column:guild_name" json:"guild_name"`
}

// ActionScheduleReviewList 超管审核直播间信息列表
/**
 * @api {get} /api/v2/admin/guild/schedule/list 超管审核直播间信息列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播昵称
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名称
 *
 * @apiSuccessExample:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "review_id": 1,
 *           "creator_id": 12,
 *           "creator_username": "用户名",
 *           "guild_id": 3,
 *           "guild_name": "公会名称",
 *           "image_url": "http://static.missevan.com/example.png",
 *           "upload_time": 1234567890
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionScheduleReviewList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := load(c)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	resp, err := param.findReviewList()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}

func load(c *handler.Context) (*searchScheduleListParam, error) {
	var searchParam searchScheduleListParam
	searchParam.CreatorID, _ = c.GetParamInt64("creator_id")
	searchParam.CreatorUsername, _ = c.GetParam("creator_username")
	searchParam.GuildID, _ = c.GetParamInt64("guild_id")
	searchParam.GuildName, _ = c.GetParam("guild_name")
	if searchParam.CreatorID < 0 || searchParam.GuildID < 0 {
		return nil, actionerrors.ErrParams
	}
	var err error
	searchParam.P, searchParam.PageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	return &searchParam, nil
}

func (p searchScheduleListParam) findReviewList() (*scheduleListResp, error) {
	var count int64
	db := service.DB.Select("r.*, u.username, g.name AS guild_name").
		Table(livereview.TableName()+" AS r").
		Joins("LEFT JOIN "+guild.TableName()+" AS g ON g.id = r.guild_id").
		Joins("LEFT JOIN "+mowangskuser.TableName()+" AS u ON u.id = r.user_id").
		Where("r.status = ? AND r.type = ?", livereview.StatusReviewing, livereview.TypeScheduleRecommend)
	if p.CreatorID != 0 {
		db = db.Where("r.user_id = ?", p.CreatorID)
	}
	if p.CreatorUsername != "" {
		db = db.Where("u.username LIKE ?", servicedb.ToLikeStr(p.CreatorUsername))
	}
	if p.GuildID != 0 {
		db = db.Where("g.id = ?", p.GuildID)
	}
	if p.GuildName != "" {
		db = db.Where("g.name LIKE ?", servicedb.ToLikeStr(p.GuildName))
	}
	db = db.Order("r.upload_time DESC")
	err := db.Count(&count).Error
	if err != nil {
		return nil, err
	}
	pa := goutil.MakePagination(count, p.P, p.PageSize)
	resp := &scheduleListResp{
		Data:       make([]*scheduleListElem, 0, pa.PageSize),
		Pagination: pa,
	}
	if !pa.Valid() {
		return resp, nil
	}

	var res []searchReview
	err = pa.ApplyTo(db).Find(&res).Error
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		res[i].SchemeToURL()
		resp.Data = append(resp.Data, &scheduleListElem{
			ReviewID:        res[i].ID,
			CreatorID:       res[i].UserID,
			CreatorUsername: res[i].Username,
			GuildID:         res[i].GuildID,
			GuildName:       res[i].GuildName,
			ImageURL:        res[i].ImageURL,
			UploadTime:      res[i].UploadTime,
		})
	}
	return resp, nil
}

type scheduleReviewElem struct {
	CreatorID  int64 `form:"creator_id" json:"creator_id"`
	UploadTime int64 `form:"upload_time" json:"upload_time"`
}

type schedulePassParam struct {
	Data []*scheduleReviewElem `form:"data" json:"data"`
}

// ActionScheduleReviewPass 超管通过直播间首页推荐信息审核
/**
 * @api {post} /api/v2/admin/guild/recommend/schedule/pass 超管通过直播间首页推荐信息审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Object[]} data
 * @apiParam {Number} data.creator_id 主播 ID
 * @apiParam {Number} data.upload_time 上传时间，秒级时间戳
 * @apiParamExample {json} Request-Example:
 *   {
 *     "data": [{
 *       "creator_id": 12,
 *       "upload_time": 1234567890
 *     }, {
 *       "creator_id": 123,
 *       "upload_time": 1234567890
 *     }]
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "审核成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionScheduleReviewPass(c *handler.Context) (handler.ActionResponse, error) {
	var params schedulePassParam
	err := c.Bind(&params)
	if err != nil || len(params.Data) == 0 {
		return nil, actionerrors.ErrParams
	}

	adminLogs := goclient.NewAdminLogBox(c)
	failedUserIDs := make([]int64, 0, len(params.Data))
	for _, param := range params.Data {
		f := logger.Fields{"creator_id": param.CreatorID, "upload_time": param.UploadTime}
		after, err := livereview.FinishReviewing(param.CreatorID, param.UploadTime,
			livereview.TypeScheduleRecommend, livereview.StatusPassed, nil)
		if err != nil {
			logger.WithFields(f).Error(err)
			failedUserIDs = append(failedUserIDs, param.CreatorID)
			continue
		}
		if after != nil {
			image, _ := service.Storage.Format(after.ImageURL)
			db := guildscheduleapply.LiveGuildScheduleApply{}.DB().
				Where("creator_id = ? AND guild_id = ? AND delete_time = 0",
					after.UserID, after.GuildID).
				Updates(map[string]interface{}{
					// 审核通过不会修改暂停推荐 StatusPaused 的状态
					"status": gorm.Expr(servicedb.IFExpr("status = ?", "?", "?"),
						guildscheduleapply.StatusPaused, guildscheduleapply.StatusPaused, guildscheduleapply.StatusPassed),
					"cover":         image,
					"reason":        "",
					"modified_time": goutil.TimeNow().Unix(),
				})
			if db.Error != nil {
				logger.WithFields(f).Error(db.Error)
				failedUserIDs = append(failedUserIDs, param.CreatorID)
				continue
			}
			if db.RowsAffected == 0 {
				failedUserIDs = append(failedUserIDs, param.CreatorID)
				continue
			}
			catalog, intro, id := adminLogContent(after)
			adminLogs.Add(catalog, intro, goclient.AdminLogOptions{
				ChannelID: &id,
			})
		} else {
			failedUserIDs = append(failedUserIDs, param.CreatorID)
		}
	}
	// 管理员操作日志
	if err = adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	if len(failedUserIDs) != 0 {
		return fmt.Sprintf("主播 %v 审核失败", failedUserIDs), nil
	}
	return "审核成功", nil
}

type scheduleRefuseParam struct {
	Data   []*scheduleReviewElem `form:"data" json:"data"`
	Reason string                `form:"reason" json:"reason"`
}

// ActionScheduleReviewRefuse 超管拒绝直播间首页推荐信息审核
/**
 * @api {post} /api/v2/admin/guild/recommend/schedule/refuse 超管拒绝直播间首页推荐信息审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Object[]} data
 * @apiParam {Number} data.creator_id 用户 ID
 * @apiParam {Number} data.upload_time 上传时间，秒级时间戳
 * @apiParam {string} reason 拒绝原因
 * @apiParamExample {json} Request-Example:
 *   {
 *     "data": [{
 *       "creator_id": 12,
 *       "upload_time": 1234567890
 *     }, {
 *       "creator_id": 123,
 *       "upload_time": 1234567890
 *     }],
 *     "reason": "1234567890"
 *   }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "审核成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionScheduleReviewRefuse(c *handler.Context) (handler.ActionResponse, error) {
	var param scheduleRefuseParam
	err := c.Bind(&param)
	param.Reason = strings.TrimSpace(param.Reason)
	if err != nil || len(param.Data) == 0 || param.Reason == "" {
		return nil, actionerrors.ErrParams
	}
	userIDs := make([]int64, len(param.Data))
	for i := range param.Data {
		userIDs[i] = param.Data[i].CreatorID
	}
	simples, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	adminLogs := goclient.NewAdminLogBox(c)
	sysMsgs := make([]pushservice.SystemMsg, 0, len(param.Data))
	failedUserIDs := make([]int64, 0, len(param.Data))
	for i := range param.Data {
		f := logger.Fields{"user_id": param.Data[i].CreatorID, "upload_time": param.Data[i].UploadTime}
		after, err := livereview.FinishReviewing(param.Data[i].CreatorID, param.Data[i].UploadTime,
			livereview.TypeScheduleRecommend, livereview.StatusRefused, nil)
		if err != nil {
			logger.WithFields(f).Error(err)
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		if after == nil {
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		apply, err := guildscheduleapply.FindApply(after.UserID, after.GuildID)
		if err != nil {
			logger.WithFields(f).Error(err)
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		if apply == nil {
			logger.WithFields(f).Warn("Can't find guild schedule apply.")
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		var updateStatus int
		switch apply.Status {
		case guildscheduleapply.StatusPaused:
			updateStatus = guildscheduleapply.StatusPaused
		case guildscheduleapply.StatusReviewing:
			updateStatus = guildscheduleapply.StatusRefused
		case guildscheduleapply.StatusPassed:
			// 已过审的状态不变
			updateStatus = guildscheduleapply.StatusPassed
		default:
			logger.WithFields(f).Warnf("guild schedule apply status: %d error.", apply.Status)
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		db := guildscheduleapply.LiveGuildScheduleApply{}.DB().
			Where("creator_id = ? AND guild_id = ? AND status = ? AND delete_time = 0",
				after.UserID, after.GuildID, apply.Status).
			Updates(map[string]interface{}{
				"status":        updateStatus,
				"reason":        param.Reason,
				"modified_time": goutil.TimeNow().Unix(),
			})
		if db.Error != nil {
			logger.WithFields(f).Error(db.Error)
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		if db.RowsAffected == 0 {
			failedUserIDs = append(failedUserIDs, param.Data[i].CreatorID)
			continue
		}
		catalog, intro, id := adminLogContent(after)
		adminLogs.Add(catalog, intro, goclient.AdminLogOptions{
			ChannelID: &id,
		})
		title, content, err := refuseSysMessage(after, simples, param.Reason)
		if err != nil {
			logger.WithFields(f).Error(err)
			continue
		}
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{UserID: apply.OperatorID, Title: title, Content: content})
	}
	goutil.Go(func() {
		// 系统通知
		if err = service.PushService.SendSystemMsg(sysMsgs); err != nil {
			logger.Error(err)
			// PASS
		}
		// 管理员操作日志
		if err = adminLogs.Send(); err != nil {
			logger.Error(err)
			// PASS
		}
	})
	if len(failedUserIDs) != 0 {
		return fmt.Sprintf("主播 %v 审核失败", failedUserIDs), nil
	}
	return "审核成功", nil
}

func adminLogContent(after *livereview.LiveReview) (int, string, int64) {
	ks, kt, ki := after.LogKeywords()
	intro := fmt.Sprintf("审核%s%s，公会 ID: %d, 直播间 ID: %d, 主播 ID: %d, %s: %s", ks, kt, after.RoomID,
		after.GuildID, after.UserID, kt, ki)
	return userapi.CatalogReviewGuildRecommendImage, intro, after.ID
}

func refuseSysMessage(after *livereview.LiveReview, userMap map[int64]*mowangskuser.Simple, reason string) (title string, content string, err error) {
	u := userMap[after.UserID]
	if u == nil {
		return "", "", actionerrors.ErrCannotFindResource
	}
	typeStr := livereview.TypeStr(after.Type)
	title = fmt.Sprintf("主播 %s 的 %s 未通过审核", u.Username, typeStr)
	content = fmt.Sprintf("亲爱的公会管理员，非常抱歉，主播 %s 的 %s 未通过审核。拒绝原因：%s。", html.EscapeString(u.Username), typeStr, html.EscapeString(reason))
	return
}

// ActionScheduleReviewAbsenceList 列出推荐位请假换人列表，根据操作时间倒序返回
/**
 * @api {get} /api/v2/admin/guild/recommend/schedule/absence/list 列出推荐位请假换人列表，根据操作时间倒序返回
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/guild
 *
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {String} [creator_username] 主播昵称
 * @apiParam {Number} [room_id] 房间 ID
 * @apiParam {Number} [type] 操作类型 0: 全部; 1: 请假; 2: 换人;
 * @apiParam {String} [operator_username] 操作人
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {String} [guild_name] 公会名称
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 2,
 *           "creator_id": 123,
 *           "creator_username": "test",
 *           "room_id": 123,
 *           "guild_id": 456,
 *           "guild_name": "公会名称",
 *           "start_time": 1584806400,
 *           "operator_username": "操作人",
 *           "create_time": 1584806400 // 操作时间
 *         },
 *         {
 *           "id": 1,
 *           "creator_id": 123,
 *           "creator_username": "test2",
 *           "room_id": 1234,
 *           "guild_id": 456,
 *           "guild_name": "公会名称",
 *           "start_time": 1584806400,
 *           "operator_username": "操作人",
 *           "create_time": 1584806400
 *         }
 *       ],
 *       "pagination": {
 *         "count": 2,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 */
func ActionScheduleReviewAbsenceList(c *handler.Context) (handler.ActionResponse, error) {
	return nil, nil
}
