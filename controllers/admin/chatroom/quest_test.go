package chatroom

import (
	"encoding/csv"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionQuestImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := [][]string{
		{"房间 ID"}, {"18113899"}, {"24113499"},
	}
	testFilePath := "../../../testdata/quest.csv"
	err := createTestCSV(testFilePath, testRoomIDs)
	require.NoError(err)
	defer cleanTestFile(testFilePath)

	roomIDs := []int64{18113899, 24113499}
	col := quests.CollectionQuests()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"room_id": bson.M{"$in": roomIDs},
	}
	_, err = col.DeleteMany(ctx, filter)
	require.NoError(err)

	called := false
	cancel = mrpc.SetMock("go://util/addadminlog", func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	now := goutil.TimeNow()
	body := questImportParam{
		CSVURL:    "https://fm.example.com/testdata/quest.csv",
		Type:      questTypeRoomView,
		Intro:     "test",
		StartTime: now.Add(time.Minute).Unix(),
		EndTime:   now.Add(time.Hour).Unix(),
		Daily:     true,
		RewardID:  19,
		Duration:  180,
		Confirm:   1,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, body)
	_, message, err := ActionQuestImport(c)
	require.NoError(err)
	assert.Equal("导入成功", message)
	assert.True(called)

	count, err := col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(len(roomIDs), count)
}

func TestNewQuestImportParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := [][]string{
		{"房间 ID"}, {"18113899"}, {"24113499"},
	}
	testFilePath := "../../../testdata/quest.csv"
	err := createTestCSV(testFilePath, testRoomIDs)
	require.NoError(err)
	defer cleanTestFile(testFilePath)

	body := questImportParam{}
	c := handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.CSVURL = "https://fm.example.com/testdata/quest.csv"
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.Type = questTypeRoomView
	body.Intro = "test"
	now := goutil.TimeNow()
	body.StartTime = now.Add(-time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.StartTime = now.Add(time.Minute).Unix()
	body.EndTime = now.Add(-time.Hour).Unix()
	body.RewardID = 666666
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.EqualError(err, "开始 / 结束时间有误，请检查后重试")

	body.EndTime = now.Add(time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.EqualError(err, "奖励 ID 不存在，请检查后重试")

	body.RewardID = 1
	body.Type = questTypeRoomListenDuration
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.EqualError(err, "直播间连续收听时长有误，请检查后重试")

	body.Duration = 180
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.EqualError(err, "只支持背包礼物奖励")

	body.RewardID = 16
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	assert.EqualError(err, "礼物 30022 不存在或已下架")

	body.RewardID = 19
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newQuestImportParam(c)
	msg := "确认新增 2 条配置吗？<br>礼物名称: 梦想之声<br>奖励数量: 10<br>有效期: 86400 秒"
	assert.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	param, err := newQuestImportParam(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestQuestImportParam_readRoomIDByCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := [][]string{
		{"房间 ID"}, {"18113899"}, {"24113499"},
	}
	testFilePath := "../../../testdata/quest.csv"
	err := createTestCSV(testFilePath, testRoomIDs)
	require.NoError(err)
	defer cleanTestFile(testFilePath)

	p := questImportParam{
		CSVURL: "https://fm.example.com/testdata/quest.csv",
	}
	roomIDs, err := p.readRoomIDByCSV()
	require.NoError(err)
	assert.Equal(2, len(roomIDs))
}

func TestCheckRoomIDs(t *testing.T) {
	assert := assert.New(t)

	csvRoomIDs := []int64{9074510, 9074509, 9074509}
	err := checkRoomIDs(csvRoomIDs)
	assert.EqualError(err, "存在重复的房间 ID，请检查后重试")

	csvRoomIDs = []int64{9074510, 9074509}
	err = checkRoomIDs(csvRoomIDs)
	assert.EqualError(err, "房间 9074510,9074509 不存在, 请检查后重试")

	csvRoomIDs = []int64{9074510, 9074509, 9074508, 9074517, 9074516, 9074515}
	err = checkRoomIDs(csvRoomIDs)
	assert.EqualError(err, "房间 9074510,9074509,9074508,9074517,9074516,... 不存在, 请检查后重试")

	csvRoomIDs = []int64{4381915}
	err = checkRoomIDs(csvRoomIDs)
	assert.NoError(err)
}

func TestTruncateElementsToString(t *testing.T) {
	assert := assert.New(t)

	str := truncateElementsToString([]int64{1, 2, 3, 4, 5})
	assert.Equal("1,2,3,4,5", str)

	str = truncateElementsToString([]int{1, 2, 3, 4, 5, 6, 7})
	assert.Equal("1,2,3,4,5,...", str)
}

func TestQuestImportParam_importQuest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomIDs := []int64{9074510, 9074509}
	col := quests.CollectionQuests()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"room_id": bson.M{"$in": roomIDs},
	}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	now := goutil.TimeNow().Unix()
	param := questImportParam{
		Type:      questTypeRoomListenDuration,
		StartTime: now,
		EndTime:   now,
		Intro:     "test",
		RewardID:  123,
		Duration:  180,
		Daily:     true,
		roomIDs:   roomIDs,
	}
	require.NoError(param.importQuest())
	count, err := col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(len(roomIDs), count)
}

func createTestCSV(filePath string, data [][]string) error {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			return err
		}
	}

	// 创建 CSV 文件
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入 CSV 数据
	writer := csv.NewWriter(file)
	defer writer.Flush()
	if err := writer.WriteAll(data); err != nil {
		return err
	}
	return nil
}

func cleanTestFile(filePath string) {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			logger.Error(err)
		}
	}
}
