package chatroom

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// ActionCatalogSet 更改分区
/**
 * @api {post} /api/v2/admin/chatroom/catalog/set 更改分区
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} catalog_id 分区id
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_id": 12,
 *     "catalog_id": 111,
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "修改成功"
 *   }
 *
 */
func ActionCatalogSet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID    int64 `form:"room_id" json:"room_id"`
		CatalogID int64 `form:"catalog_id" json:"catalog_id"`
	}

	err := c.Bind(&param)
	if err != nil || param.RoomID == 0 || param.CatalogID == 0 {
		return nil, actionerrors.ErrParams
	}

	subCatalogs, err := catalog.LiveSubCatalogs(false)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	isSub := util.Includes(len(subCatalogs), func(i int) bool {
		return subCatalogs[i].ID == param.CatalogID
	})

	if !isSub {
		return nil, actionerrors.ErrParamsMsg("找不到要修改的分区")
	}

	res, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if res == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if res.CatalogID == param.CatalogID {
		return nil, actionerrors.ErrParamsMsg("请选择与当前不同的目标分区")
	}

	if err = room.UpdateCatalogID(param.RoomID, param.CatalogID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	intro := fmt.Sprintf("直播间修改分区, 直播间 ID: %d, 分区 ID: %d", param.RoomID, param.CatalogID)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogLiveSetCatalog)

	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return "修改成功", nil
}
