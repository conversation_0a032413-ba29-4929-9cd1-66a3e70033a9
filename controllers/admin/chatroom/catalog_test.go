package chatroom

import (
	"net/url"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionCatalogSet(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	roomID := int64(61618635)
	require.NoError(room.UpdateCatalogID(roomID, 118))

	c := handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true,
		map[string]int64{
			"room_id":    roomID,
			"catalog_id": 0,
		})
	_, err := ActionCatalogSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true,
		map[string]int64{
			"room_id":    18113,
			"catalog_id": 118,
		})
	_, err = ActionCatalogSet(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true,
		map[string]int64{
			"room_id":    roomID,
			"catalog_id": 118,
		})
	_, err = ActionCatalogSet(c)
	assert.EqualError(err, "请选择与当前不同的目标分区")

	c = handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true,
		map[string]int64{
			"room_id":    roomID,
			"catalog_id": 98,
		})
	_, err = ActionCatalogSet(c)
	assert.EqualError(err, "找不到要修改的分区")

	// 测试表单方式是否能正常处理
	form := url.Values{}
	form.Set("room_id", strconv.FormatInt(roomID, 10))
	form.Set("catalog_id", "98")
	c = handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true, form)
	_, err = ActionCatalogSet(c)
	assert.EqualError(err, "找不到要修改的分区")

	c = handler.NewTestContext("POST", "/api/v2/admin/chatroom/catalog/set", true,
		map[string]int64{
			"room_id":    roomID,
			"catalog_id": 120,
		})
	resp, err := ActionCatalogSet(c)
	require.NoError(err)
	assert.Equal("修改成功", resp)
}
