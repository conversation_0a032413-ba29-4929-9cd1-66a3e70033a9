package chatroom

import (
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var clearRankTypes = []int{usersrank.TypeWeek, usersrank.TypeMonth, usersrank.TypeHour,
	usersrank.TypeDay, usersrank.TypeNova,
	usersrank.TypeGashaponWeek,
}

type hotSuppressionUpdateParam struct {
	RoomID  int64  `form:"room_id" json:"room_id"`
	Action  string `form:"action" json:"action"`
	EndTime int64  `form:"end_time" json:"end_time"`
}

// ActionHotSuppressionUpdate 更新直播间热度限制列表
/**
 * @api {post} /api/v2/admin/chatroom/hotsuppression/update 更新直播间热度限制列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {string="add","del"} action 操作类型，add: 添加直播间，del: 移除直播间
 * @apiParam {Number} [end_time=0] 热度限制过期时间，秒级时间戳，0: 本次直播有效，-1: 永久有效
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionHotSuppressionUpdate(c *handler.Context) (handler.ActionResponse, error) {
	var param hotSuppressionUpdateParam
	err := c.Bind(&param)
	if err != nil || param.RoomID == 0 || param.EndTime < -1 {
		return nil, actionerrors.ErrParams
	}
	if param.EndTime > 0 && param.EndTime < goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("限制过期不能小于当前时间")
	}

	exists, err := room.Exists(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}

	adminLogs := goclient.NewAdminLogBox(c)

	switch param.Action {
	case "add":
		creatorID, err := room.FindCreatorID(param.RoomID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if creatorID == 0 {
			return nil, actionerrors.ErrParamsMsg("找不到直播间对应的主播")
		}

		pipe := service.Redis.TxPipeline()
		pipe.ZAdd(keys.KeyRoomsSuppressionHotList0.Format(), &redis.Z{
			Member: param.RoomID,
			Score:  float64(param.EndTime),
		})

		// 清掉主播在榜单上的积分，即删除榜单上的该直播间对应的值
		now := goutil.TimeNow()
		for _, t := range clearRankTypes {
			key := usersrank.Key(t, now)
			// 删除榜单上的该直播间对应的值
			pipe.ZRem(key, strconv.FormatInt(creatorID, 10))
		}
		_, err = pipe.Exec()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		endTimeIntro := "本次直播有效"
		if param.EndTime > 0 {
			endTimeIntro = fmt.Sprintf("限制过期时间 %s", time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHMS))
		} else if param.EndTime == -1 {
			endTimeIntro = "永久有效"
		}
		intro := fmt.Sprintf("直播间热度限制列表更新，添加直播间 %d %s", param.RoomID, endTimeIntro)
		adminLogs.Add(userapi.CatalogUpdateHotSuppression, intro)
	case "del":
		cmd := service.Redis.ZRem(keys.KeyRoomsSuppressionHotList0.Format(), param.RoomID)
		if cmd.Err() != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if cmd.Val() == 0 {
			return nil, actionerrors.ErrParamsMsg("限制列表中无该直播间")
		}
		intro := fmt.Sprintf("直播间热度限制列表更新，移除直播间 %d", param.RoomID)
		adminLogs.Add(userapi.CatalogUpdateHotSuppression, intro)
	default:
		return nil, actionerrors.ErrParams
	}

	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return "success", nil
}
