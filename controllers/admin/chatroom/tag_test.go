package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCheckTagKey(t *testing.T) {
	kcFORM := tutil.NewKeyChecker(t, tutil.FORM)
	kcJSON := tutil.NewKeyChecker(t, tutil.JSON)

	keys := []string{"tag_id", "tag_name", "icon_url", "dark_icon_url", "web_icon_url"}
	kcFORM.Check(tagParams{}, keys...)
	kcJSON.Check(tagParams{}, keys...)

	keys = []string{"tag_id", "status", "confirm"}
	kcFORM.Check(tagChangeStatusParams{}, keys...)
	kcJSON.Check(tagChangeStatusParams{}, keys...)
}

func TestActionSetTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// check params
	var param tagParams
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err := ActionTagSet(c)
	require.Equal(actionerrors.ErrParams, err)
	param.ID = 1
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionTagSet(c)
	require.Equal(actionerrors.ErrParams, err)

	param = tagParams{
		TagName:     "name",
		IconURL:     "https://fm.example.com/testdata/test.jpg",
		DarkIconURL: "https://fm.example.com/testdata/test.jpg",
		WebIconURL:  "https://fm.example.com/testdata/test.jpg",
	}
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer goutil.SetTimeNow(nil)
	newNow := goutil.TimeNow()
	require.NoError(service.LRURedis.Set(keys.KeyLiveMetaTabs0.Format(), "[]", time.Second).Err())
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	r, err := ActionTagSet(c)
	require.NoError(err)
	require.Equal("添加标签成功", r)
	var nTag tag.Tag
	err = service.LiveDB.Where("tag_name = ?", param.TagName).First(&nTag).Error
	require.NoError(err)
	assert.GreaterOrEqual(nTag.CreateTime, newNow.Unix())
	assert.GreaterOrEqual(nTag.ModifiedTime, newNow.Unix())
	// 存到数据库中为 "oss://live/tags/icon/${tag_id}.jpg" 因有 afterFind 的钩子 所以结果为 "https://static-test.missevan.com/live/tags/icon/${tag_id}.jpg"
	assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/tags/icon/%03d_19700101080000.jpg", nTag.ID), nTag.IconURL)
	assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/tags/icon/%03d_19700101080000-dark.jpg", nTag.ID), nTag.DarkIconURL)
	assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/tags/icon/%03d_19700101080000-web.jpg", nTag.ID), nTag.WebIconURL)
	param = tagParams{
		ID:         nTag.ID,
		TagName:    "nTag",
		WebIconURL: "https://fm.example.com/testdata/test.png",
	}
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	editNow := goutil.TimeNow()
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	r, err = ActionTagSet(c)
	require.NoError(err)
	require.Equal("修改标签成功", r)
	assert.Equal(redis.Nil, service.LRURedis.Get(keys.KeyLiveMetaTabs0.Format()).Err())
	var eTag tag.Tag
	err = service.LiveDB.First(&eTag, param.ID).Error
	require.NoError(err)
	assert.Equal(param.TagName, eTag.TagName)
	assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/tags/icon/%03d_19700101080001-web.png", eTag.ID), eTag.WebIconURL)
	assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/tags/icon/%03d_19700101080000.jpg", eTag.ID), eTag.IconURL)
	assert.GreaterOrEqual(eTag.ModifiedTime, editNow.Unix())
}

func TestCheckTagName(t *testing.T) {
	assert := assert.New(t)

	type args struct {
		tagName string
		ID      int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{name: "标签名为空", args: args{tagName: "    "}, wantErr: actionerrors.ErrParamsMsg("标签名不可以为空")},
		{name: "标签名长度超过 5 位", args: args{tagName: "abcdef"}, wantErr: actionerrors.ErrParamsMsg("标签名最多输入 5 个字")},
		{name: "中文标签名长度", args: args{tagName: "这是个标签"}, wantErr: nil},
		{name: "中英文标签名长度为 5 位", args: args{tagName: "is a 标签"}, wantErr: nil},
		{name: "中英文标签名长度超过 5 位", args: args{tagName: "标签lalala"}, wantErr: actionerrors.ErrParamsMsg("标签名最多输入 5 个字")},
		{name: "中文标签名长度超过 5 位", args: args{tagName: "这是个标签~"}, wantErr: actionerrors.ErrParamsMsg("标签名最多输入 5 个字")},
		{name: "标签名重复", args: args{tagName: "test1"}, wantErr: actionerrors.ErrParamsMsg("有重复的标签名")},
		{name: "标签名忽略空格", args: args{tagName: "ab  cde"}, wantErr: nil},
		{name: "正常情况", args: args{tagName: "test1", ID: 1}, wantErr: nil},
	}
	for _, tt := range tests {
		err := checkTagName(tt.args.tagName, tt.args.ID)
		assert.Equal(tt.wantErr, err, tt.name)
	}
}

func TestTagParams_addTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := tagParams{
		TagName: "testMaxID",
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	require.NoError(param.addTag())

	tagID, err := tag.FindNextTagID(0, tag.TypeLiveTag)
	require.NoError(err)
	liveTag, err := tag.FindTagByID(tagID-1, tag.TypeLiveTag)
	require.NoError(err)
	assert.Equal(param.TagName, liveTag.TagName)
	assert.Equal(tag.TypeLiveTag, liveTag.Type)

	require.NoError(service.LiveDB.Delete(&tag.Tag{}, "id = ?", tagID-1).Error)
}

func TestParamToUrl(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(fmt.Sprintf("oss://live/tags/icon/001_%s.png", goutil.TimeNow().Format(util.TimeFormatYMDHHMMSSWithNoSpace)), paramToURL(1, ".png"))
}

func TestTagParamsUploadFile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	param := tagParams{
		IconURL:     "https://fm.example.com/testdata/test.jpg",
		DarkIconURL: "https://fm.example.com/testdata/test.png",
	}
	tag := tag.Tag{
		ID: 1,
	}
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer goutil.SetTimeNow(nil)
	err := param.uploadFile(&tag)
	require.NoError(err)
	assert.Equal(fmt.Sprintf("oss://live/tags/icon/%03d_19700101080000.jpg", tag.ID), tag.IconURL)
	assert.Equal(fmt.Sprintf("oss://live/tags/icon/%03d_19700101080000-dark.png", tag.ID), tag.DarkIconURL)
	assert.Equal("", tag.WebIconURL)
}

func TestActionTagEditStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := tagChangeStatusParams{
		TagID:  0,
		Status: util.NewInt(1),
	}
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err := ActionTagEditStatus(c)
	require.EqualError(err, "标签不存在")

	param.TagID = 3
	rooms, err := room.ListSimples(bson.M{"tag_ids": param.TagID}, nil, nil)
	require.NoError(err)
	total := len(rooms)
	var open int64
	for i := range rooms {
		if rooms[i].Status.Open == room.StatusOpenTrue {
			open++
		}
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionTagEditStatus(c)
	require.EqualError(err, fmt.Sprintf("是否上线“test3”标签？<br><br>总房间数：%d<br>目前开播数：%d", total, open))
	param.Confirm = 1

	now := goutil.TimeNow().Unix()
	tags, err := tag.FindTags(false, tag.TypeLiveTag)
	require.NoError(err)
	length := len(tags)
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err := ActionTagEditStatus(c)
	require.NoError(err)
	assert.Equal("上线成功", res)
	_, err = service.LRURedis.Get(keys.KeyLiveMetaTabs0.Format()).Result()
	assert.Equal(redis.Nil, err)
	tags, err = tag.FindTags(false, tag.TypeLiveTag)
	require.NoError(err)
	require.Equal(length+1, len(tags))

	var tag1 tag.Tag
	err = service.LiveDB.Table(tag.TableName()).First(&tag1, param.TagID).Error
	require.NoError(err)
	assert.Equal(tag.StatusShow, tag1.Status)
	assert.GreaterOrEqual(tag1.ModifiedTime, now)

	param.TagID = 3
	*param.Status = 0
	now = goutil.TimeNow().Unix()
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err = ActionTagEditStatus(c)
	require.NoError(err)
	assert.Equal("下线成功", res)
	assert.Equal(redis.Nil, service.LRURedis.Get(keys.KeyLiveMetaTabs0.Format()).Err())
	tags, err = tag.FindTags(false, tag.TypeLiveTag)
	require.NoError(err)
	require.Equal(length, len(tags))

	var tag2 tag.Tag
	require.NoError(err, service.LiveDB.Table(tag.TableName()).First(&tag2, param.TagID).Error)
	assert.Equal(tag.StatusHide, tag2.Status)
	assert.GreaterOrEqual(tag2.ModifiedTime, now)
}

func TestActionTagSortOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// err ID
	param := tagSortOrder{
		ID:        0,
		SortOrder: 0,
		Type:      "",
	}
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err := ActionTagSortOrder(c)
	require.Equal(actionerrors.ErrParams, err)

	// err type
	param = tagSortOrder{
		ID:        3,
		SortOrder: 0,
		Type:      "",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionTagSortOrder(c)
	require.Equal(actionerrors.ErrParams, err)

	// err catalog ID
	param = tagSortOrder{
		ID:        3,
		SortOrder: 0,
		Type:      "catalog",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionTagSortOrder(c)
	require.EqualError(err, "分区不存在")

	// err tag ID
	param = tagSortOrder{
		ID:        10,
		SortOrder: 0,
		Type:      "tag",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionTagSortOrder(c)
	require.EqualError(err, "标签不存在")

	param = tagSortOrder{
		ID:        104,
		SortOrder: 104,
		Type:      "catalog",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err := ActionTagSortOrder(c)
	require.NoError(err)
	assert.Equal("调整标签排序成功", res)

	var catalog catalog.LiveCatalog
	err = service.DB.Table(catalog.TableName()).Where("id = ?", 104).First(&catalog).Error
	require.NoError(err)
	assert.Equal(104, catalog.SortOrder)

	param = tagSortOrder{
		ID:        3,
		SortOrder: 30,
		Type:      "tag",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err = ActionTagSortOrder(c)
	require.NoError(err)
	assert.Equal("调整标签排序成功", res)
	_, err = service.LRURedis.Get(keys.KeyLiveMetaTabs0.Format()).Result()
	assert.Equal(redis.Nil, err)
	_, err = service.LRURedis.Get(keys.KeyLiveCatalogs1.Format(util.BoolToInt(false))).Result()
	assert.Equal(redis.Nil, err)

	tag, err := tag.FindTagByID(param.ID, tag.TypeLiveTag)
	require.NoError(err)
	require.NotNil(tag)
	assert.Equal(30, tag.SortOrder)
}
