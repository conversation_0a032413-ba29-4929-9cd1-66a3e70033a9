package chatroom

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestQuestionTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(questionCancelParam{}, "data", "reason")
	kc.Check(questionCancelElement{}, "room_id", "question_id")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(questionCancelParam{}, "data", "reason")
	kc.Check(questionCancelElement{}, "room_id", "question_id")
}

func TestActionQuestionCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, nil)
	_, err := ActionQuestionCancel(c)
	assert.Equal(actionerrors.ErrParams, err, "缺乏需要的参数")

	var param questionCancelParam
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	assert.Equal(actionerrors.ErrParams, err, "room_id 参数值不合法")

	param.Data = []questionCancelElement{{RoomID: existRoomID}}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	assert.Equal(actionerrors.ErrParams, err, "question_id 参数值不合法")

	now := goutil.TimeNow()
	param.Data[0].QuestionID = primitive.NewObjectIDFromTimestamp(now).Hex()
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	assert.EqualError(err, "提问不存在")

	// 插入测试数据
	r, err := findRoom(22489473)
	require.NoError(err)
	require.NotNil(r)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lq := &livequestion.LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(now),
		Helper: livequestion.Helper{
			CreatedTime: now,
			RoomID:      existRoomID,
			RoomOID:     r.OID,
			UserID:      1200,
			Status:      livequestion.StatusQueued,
			Price:       30,
		},
	}

	lqTransactionID := new(livequestion.LiveQuestion)
	*lqTransactionID = *lq
	lqTransactionID.OID = primitive.NewObjectIDFromTimestamp(now.Add(time.Minute))
	lqTransactionID.TransactionID = 123456

	lqFinished := new(livequestion.LiveQuestion)
	*lqFinished = *lq
	lqFinished.OID = primitive.NewObjectIDFromTimestamp(now.Add(time.Minute))
	lqFinished.Status = livequestion.StatusFinished
	defer func() {
		_, err = livequestion.Collection().DeleteMany(ctx,
			bson.M{"user_id": lq.UserID},
		)
		assert.NoError(err)
	}()
	_, err = livequestion.Collection().InsertMany(ctx, []interface{}{lq, lqTransactionID, lqFinished})
	require.NoError(err)

	// mock pushservice
	pushserviceURI := pushservice.Scheme + "://api/systemmsg"
	cancelMock := mrpc.SetMock(pushserviceURI,
		func(i interface{}) (interface{}, error) {
			logger.Debugf("systemmsgs: %s", tutil.SprintJSON(i))
			return "success", nil
		})
	defer cancelMock()

	var check bool
	cleanup := mrpc.SetMock(userapi.URICancelAsks, func(input interface{}) (interface{}, error) {
		v, ok := input.(map[string]interface{})
		require.True(ok)
		tids, ok := v["transaction_ids"].([]int64)
		require.True(ok)
		check = true
		return &userapi.CancelAsksResp{Transactions: make([]userapi.CancelAsksTransaction, len(tids))}, nil
	})
	defer cleanup()

	param.Data[0].QuestionID = lq.OID.Hex()
	param.Reason = "测试删除提问，没有 TransactionID"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	require.NoError(err)
	assert.False(check)

	param.Data[0].QuestionID = lqTransactionID.OID.Hex()
	param.Reason = "测试删除提问，有 TransactionID"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	require.NoError(err)
	assert.True(check)

	param.Data[0].QuestionID = lqFinished.OID.Hex()
	param.Reason = "测试删除已完成提问"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/question/cancel", true, param)
	_, err = ActionQuestionCancel(c)
	require.NoError(err)
}
