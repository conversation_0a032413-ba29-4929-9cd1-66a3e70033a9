package chatroom

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/util"
)

const liveShowPopupSort = 3 // 小窗固定排在第三位

type newLiveShowParams struct {
	CreatorID     int64 `json:"creator_id" form:"creator_id"`
	ShowStartTime int64 `json:"show_start_time" form:"show_start_time"`
	ShowType      int   `json:"show_type" form:"show_type"`
	Confirm       int   `json:"confirm" form:"confirm"`
}

// ActionCreateLiveShow 添加主播个人场
/**
 * @api {post} /api/v2/admin/chatroom/liveshow/create 添加主播个人场
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {Number} show_start_time 开始时间, 单位: 秒
 * @apiParam {number=1,2} show_type 个人场类型
 * @apiParam {number=1} [confirm] 二次确认弹窗
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionCreateLiveShow(c *handler.Context) (handler.ActionResponse, error) {
	var param newLiveShowParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if param.ShowType != liveshow.ShowTypeStar && param.ShowType != liveshow.ShowTypeMoon {
		return nil, actionerrors.ErrParams
	}

	roomID, err := room.FindRoomID(param.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}

	if err := param.checkTime(); err != nil {
		return nil, err
	}

	if param.Confirm != 1 {
		return nil, actionerrors.ErrConfirmRequired(fmt.Sprintf("确认添加此主播（房间 ID：%d）个人场？", roomID), 1)
	}

	liveShowConfig, err := params.FindLiveShow()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	startTime := time.Unix(param.ShowStartTime, 0)
	show := liveshow.NewLiveShow(
		param.CreatorID,
		param.ShowType,
		startTime.Unix(),
		startTime.Add(4*time.Hour).Unix(), // 个人场持续时长 4h
	)

	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		err := show.Create(ctx)
		if err != nil {
			return err
		}

		// TODO: 二期迁移到审核接口
		_, err = show.UpdateStatus(ctx, liveshow.ShowStatusCheckApproved)
		if err != nil {
			return err
		}

		e, err := json.Marshal(liverecommendedelements.PopupShowConfig{
			AllRoomShow: false,
			RoomIDs:     []int64{roomID},
			LiveShowID:  show.OID.Hex(),
		})
		if err != nil {
			return err
		}

		popupItem := liverecommendedelements.PopupRespItem{
			MiniURL:      liveShowConfig.MiniURL,
			FoldImageURL: liveShowConfig.FoldImageURL,
			FullURL:      liveShowConfig.FullURL,
		}
		popup := liverecommendedelements.Popup{
			Sort: liveShowPopupSort, // 主播个人场小窗排在固定位置
			Attribute: liverecommendedelements.Attribute{
				Cover: liveShowConfig.Cover,
				URL:   popupItem.PopupURL(),
				// 小窗提前 30m 上线
				StartTime: util.NewInt64(startTime.Add(-30 * time.Minute).Unix()),
			},
			// 小窗个人场结束 10m 后下线
			ExpireTime:     startTime.Add(4*time.Hour + 10*time.Minute).Unix(),
			ExtendedFields: string(e),
		}
		_, err = popup.Save(service.DB)
		if err != nil {
			return err
		}

		err = livecustom.AddRoomCustomGift(roomID, liveshow.CollectGiftID, startTime, startTime.Add(4*time.Hour), livecustom.SourceLiveShow)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	msg := pushservice.SystemMsg{
		UserID: param.CreatorID,
		Title:  "恭喜您成功报名【摘星记】活动！",
		Content: fmt.Sprintf(
			"主播您好！您报名的【摘星记】%s活动现已通过审核，活动将在您指定的时间内准时开始，和小耳朵们一起努力摘星星吧！\n"+
				"如有疑问，可联系客服哟~", liveshow.LiveShowName(param.ShowType)),
	}
	err = service.PushService.SendSystemMsg([]pushservice.SystemMsg{msg})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	intro := fmt.Sprintf("添加参加主播个人场主播，开始时间：%s、主播 M号：%d",
		time.Unix(param.ShowStartTime, 0).Format(util.TimeFormatHMS), param.CreatorID)
	adminLogs := goclient.NewAdminLogBox(c)
	adminLogs.Add(userapi.CatalogManageLiveShow, intro)

	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return "success", nil
}

func (param newLiveShowParams) checkTime() error {
	startTime := time.Unix(param.ShowStartTime, 0)
	if !util.TimeNow().Before(startTime.Add(-time.Hour)) {
		return actionerrors.NewErrForbidden("该主播挑战已过期")
	}

	if startTime.Unix()-util.TimeNow().Unix() > 30*util.SecondOneDay {
		return actionerrors.ErrParamsMsg("请选择 30 天内的挑战开始时间")
	}

	if h, m, s := startTime.Clock(); h > 20 || (h == 20 && (m > 0 || s > 0)) {
		return actionerrors.ErrParamsMsg("请选择 20:00 前的挑战开始时间")
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := liveshow.Collection().CountDocuments(ctx, bson.M{
		"status":     liveshow.ShowStatusCheckApproved,
		"creator_id": param.CreatorID,
		// 个人场结束后有 40m 间隔（10m 小窗 + 30m 间隔时间）
		"start_time": bson.M{"$lt": startTime.Add(4*time.Hour + 40*time.Minute).Unix()},
		// 上一场结束 40m 间隔 + 本场小窗提前 30m 开始预热
		"end_time": bson.M{"$gt": startTime.Add(-40*time.Minute - 30*time.Minute).Unix()},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if count > 0 {
		return actionerrors.ErrParamsMsg("个人场时间冲突")
	}

	return nil
}

type cancelLiveShowParams struct {
	LiveShowID string `json:"live_show_id" form:"live_show_id"`
	Confirm    int    `json:"confirm" form:"confirm"`
}

// ActionCancelLiveShow 取消主播个人场
/**
 * @api {post} /api/v2/admin/chatroom/liveshow/cancel 取消主播个人场
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} live_show_id 主播个人场 ID
 * @apiParam {number=1} [confirm] 二次确认弹窗
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionCancelLiveShow(c *handler.Context) (handler.ActionResponse, error) {
	var param cancelLiveShowParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	oid, err := primitive.ObjectIDFromHex(param.LiveShowID)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	show, err := liveshow.FindOne(bson.M{"_id": oid})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if show == nil {
		return nil, actionerrors.ErrNotFound("找不到要取消的个人场")
	}

	if show.Status != liveshow.ShowStatusCheckApproved {
		return nil, actionerrors.NewErrForbidden("审核状态不是已通过，无法取消")
	}

	now := util.TimeNow()
	if show.StartTime < now.Add(time.Hour).Unix() {
		return nil, actionerrors.NewErrForbidden("个人场已开始，无法取消")
	}

	roomID, err := room.FindRoomID(show.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}

	if param.Confirm != 1 {
		user, err := mowangskuser.FindByUserID(show.CreatorID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if user == nil {
			return nil, actionerrors.ErrUserNotFound
		}
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确认取消%s（房间 ID：%d）个人场%s资格？",
				user.Username, roomID, liveshow.LiveShowName(show.ShowType)), 1)
	}

	e, err := json.Marshal(liverecommendedelements.PopupShowConfig{
		AllRoomShow: false,
		RoomIDs:     []int64{roomID},
		LiveShowID:  show.OID.Hex(),
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		_, err = show.UpdateStatus(ctx, liveshow.ShowStatusCheckCanceled)
		if err != nil {
			return err
		}

		// TODO: 用 JSON 指令，判断 live_show_id 相同
		err = service.DB.Table(liverecommendedelements.TableName()).
			Where("element_type = ? AND sort = ? AND start_time = ? AND extended_fields = ?",
				liverecommendedelements.ElementPopup,
				liveShowPopupSort, show.StartTime-30*util.SecondOneMinute, string(e),
			).
			// 软删除
			Update("sort", 0).Error
		if err != nil {
			return err
		}

		err = livecustom.RemoveLiveShowGift(roomID, liveshow.CollectGiftID, show.StartTime)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	msg := pushservice.SystemMsg{
		UserID: show.CreatorID,
		Title:  "抱歉，您报名的【摘星记】活动已取消",
		Content: fmt.Sprintf(
			"主播您好！非常抱歉您报名的【摘星记】%s活动已取消，期待与您的下一次摘星之约！\n"+
				"如有疑问，可联系客服哟~", liveshow.LiveShowName(show.ShowType)),
	}
	err = service.PushService.SendSystemMsg([]pushservice.SystemMsg{msg})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	intro := fmt.Sprintf("取消主播个人场，开始时间：%s、主播 M号：%d",
		time.Unix(show.StartTime, 0).Format(util.TimeFormatHMS), show.CreatorID)
	adminLogs := goclient.NewAdminLogBox(c)
	adminLogs.Add(userapi.CatalogManageLiveShow, intro)

	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return "success", nil
}
