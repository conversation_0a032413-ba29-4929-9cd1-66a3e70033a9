package chatroom

import (
	"errors"
	"fmt"
	"html"
	"io"
	"slices"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustombatchaddlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	sqlgift "github.com/MiaoSiLa/live-service/models/mysql/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/useroa"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type giftHideParams struct {
	GiftID  int64  `form:"gift_id" json:"gift_id"`
	Confirm int    `form:"confirm" json:"confirm"`
	Reason  string `form:"reason" json:"reason"`
}

// ActionGiftHide 下架礼物
/**
 * @api {post} /api/v2/admin/chatroom/gift/hide 下架礼物
 * @apiDescription 下架礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} gift_id 要下架的礼物 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiParam {String} reason 下架原因 reason
 *
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "下架成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "礼物 ID：2<br>礼物名称：寿司<br>售价（钻）：10<br>下架原因：需求"
 *     }
 *   }
 */
func ActionGiftHide(c *handler.Context) (handler.ActionResponse, error) {
	var param giftHideParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	g, err := gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 %d 不存在或已下架", param.GiftID))
	}

	if param.Confirm == 0 {
		message := fmt.Sprintf("礼物 ID：%d<br>礼物名称：%s<br>售价（钻）：%d<br>"+
			"下架原因：%s", g.GiftID, html.EscapeString(g.Name), g.Price, html.EscapeString(param.Reason))
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := gift.Collection()
	_, err = col.UpdateOne(ctx, bson.M{"gift_id": param.GiftID}, bson.M{
		"$set": bson.M{
			"order":       gift.OrderHide,
			"toggle_time": goutil.TimeNow().Unix(),
		},
		"$unset": bson.M{"setorder": ""},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("下架礼物，礼物 ID： %d，礼物名称：%s，售价（钻）：%d，下架原因：%s", g.GiftID, g.Name, g.Price, param.Reason)

	box.Add(userapi.CatalogManageGift, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "下架成功", nil
}

type paramGiftReorder struct {
	GiftIDs string `form:"gift_ids" json:"gift_ids"`
	Type    int    `form:"type" json:"type"`

	giftIDs []int64
}

// ActionGiftReorder 调整礼物顺序
/**
 * @apiDeprecated 使用 /api/v2/admin/chatroom/gift/setorder 替代
 * @api {post} /api/v2/admin/chatroom/gift/reorder 调整礼物顺序
 * @apiDescription 调整礼物排序，当前仅支持对已上线礼物进行排序
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} gift_ids 要排序的礼物 IDs, 半角逗号分割
 * @apiParam {Number} type 要排序的礼物栏类型, 0: 普通礼物栏, 1: 贵族礼物栏, 2: 粉丝礼物栏
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "排序成功"
 *   }
 */
func ActionGiftReorder(c *handler.Context) (handler.ActionResponse, error) {
	var param paramGiftReorder
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if err = param.check(); err != nil {
		return nil, err
	}
	if err = param.reorder(); err != nil {
		return nil, err
	}
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("直播间礼物排序, 礼物 IDs: %s", param.GiftIDs)
	box.Add(userapi.CatalogManageGift, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "排序成功", nil
}

func (param *paramGiftReorder) check() (err error) {
	param.giftIDs, err = goutil.SplitToInt64Array(param.GiftIDs, ",")
	if err != nil || param.Type < gift.TabGiftNormal || param.Type > gift.TabGiftMedal {
		return actionerrors.ErrParams
	}
	giftIDs := util.Uniq(param.giftIDs)
	if len(param.giftIDs) != len(giftIDs) {
		return actionerrors.ErrParamsMsg("礼物 ID 重复，请检查重新输入")
	}
	// 当前仅支持对已上线礼物进行排序
	shows, err := gift.FindAllShowingGifts()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 只对需要展示的礼物进行排序，忽略 extra
	gifts, _ := gift.GroupGifts(shows, gift.GroupGiftsOptions{
		User:   nil,
		RoomID: nil,
	})
	checkGifts := make([]int64, 0, len(gifts))
	for i := range gifts[param.Type] {
		checkGifts = append(checkGifts, gifts[param.Type][i].GiftID)
	}
	diff := util.DiffInt64(checkGifts, param.giftIDs)
	if len(diff) != 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 IDs: %s 不存在或被遗漏，请检查后重新输入", goutil.JoinInt64Array(diff, ",")))
	}
	return
}

func (param *paramGiftReorder) reorder() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := gift.Collection()
	updates := make([]mongo.WriteModel, len(param.giftIDs))
	for i := range param.giftIDs {
		m := mongo.NewUpdateOneModel()
		m.SetFilter(bson.M{"gift_id": param.giftIDs[i]})
		m.SetUpdate(bson.M{"$set": bson.M{"order": (i + 1) * 10}})
		updates[i] = m
	}
	_, err := col.BulkWrite(ctx, updates)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type giftSetorderParams struct {
	Type          int64  `form:"type" json:"type"`
	OrderIDs      string `form:"order_ids" json:"order_ids"`
	HideIDs       string `form:"hide_ids" json:"hide_ids"`
	EffectiveTime int64  `form:"effective_time" json:"effective_time"`

	orderIDs []int64
	hideIDs  []int64
}

// ActionGiftSetOrder 设定礼物在指定时间的指定顺序
/**
 * @api {post} /api/v2/admin/chatroom/gift/setorder 设定礼物在指定时间的指定顺序
 * @apiDescription 通过时间、排序参数，设定某个时刻下，礼物的顺序和状态
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} type 要排序的礼物栏类型, 0: 普通礼物栏, 1: 贵族礼物栏, 2: 粉丝礼物栏
 * @apiParam {String} order_ids 要排序的礼物 IDs, 半角逗号分割
 * @apiParam {String} hide_ids 要下架的礼物 IDs, 半角逗号分割
 * @apiParam {Number} effective_time 生效时间, 秒级时间戳, 如: 1578844800
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设定成功"
 *   }
 */
func ActionGiftSetOrder(c *handler.Context) (handler.ActionResponse, error) {
	var param giftSetorderParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.EffectiveTime == 0 || (param.OrderIDs == "" && param.HideIDs == "") {
		return nil, actionerrors.ErrParams
	}
	err = param.checkSetOrderParam()
	if err != nil {
		return nil, err
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := gift.Collection()
	updates := make([]mongo.WriteModel, 0)
	effectiveTime := time.Unix(param.EffectiveTime, 0)
	if len(param.orderIDs) > 0 {
		for i := range param.orderIDs {
			m := mongo.NewUpdateOneModel()
			m.SetFilter(bson.M{"gift_id": param.orderIDs[i]})
			m.SetUpdate(bson.M{"$set": bson.M{"setorder": bson.M{
				"effective_time": effectiveTime,
				"order":          (i + 1) * 10,
			}}})
			updates = append(updates, m)
		}
	}

	if len(param.hideIDs) > 0 {
		for i := range param.hideIDs {
			m := mongo.NewUpdateOneModel()
			m.SetFilter(bson.M{"gift_id": param.hideIDs[i]})
			m.SetUpdate(bson.M{"$set": bson.M{"setorder": bson.M{
				"effective_time": effectiveTime,
				"order":          gift.OrderHide,
			}}})
			updates = append(updates, m)
		}
	}
	_, err = col.BulkWrite(ctx, updates)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(c)
	// 设置礼物定时排序的情况
	if len(param.orderIDs) > 0 {
		box.Add(userapi.CatalogManageGift, fmt.Sprintf("添加礼物定时排序，礼物 IDs：%s，生效时间：%s",
			goutil.JoinInt64Array(param.orderIDs, ","),
			time.Unix(param.EffectiveTime, 0).Format(util.TimeFormatYMDHMS)))
	}
	// 设置礼物定时下架的情况
	if len(param.hideIDs) > 0 {
		box.Add(userapi.CatalogManageGift, fmt.Sprintf("添加礼物定时下线，礼物 IDs：%s，生效时间：%s",
			goutil.JoinInt64Array(param.hideIDs, ","),
			time.Unix(param.EffectiveTime, 0).Format(util.TimeFormatYMDHMS)))
	}
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return "设定成功", nil
}

func (param *giftSetorderParams) checkSetOrderParam() (err error) {
	// 排除 OrderIDs 输入重复的情况
	if param.OrderIDs != "" {
		param.orderIDs, err = goutil.SplitToInt64Array(param.OrderIDs, ",")
		if err != nil {
			return actionerrors.ErrParams
		}
		orderIDs := util.Uniq(param.orderIDs)
		if len(param.orderIDs) != len(orderIDs) {
			return actionerrors.ErrParamsMsg("排序的礼物 ID 重复，请检查重新输入")
		}
	}

	// 排除 HideIDs 输入重复的情况
	if param.HideIDs != "" {
		param.hideIDs, err = goutil.SplitToInt64Array(param.HideIDs, ",")
		if err != nil {
			return actionerrors.ErrParams
		}
		hideIDs := util.Uniq(param.hideIDs)
		if len(param.hideIDs) != len(hideIDs) {
			return actionerrors.ErrParamsMsg("下架的礼物 ID 重复，请检查重新输入")
		}
	}

	// 筛选 orderIDs 和 hideIDs 同时输入的情况
	if len(param.orderIDs) > 0 && len(param.hideIDs) > 0 {
		// 获得交集中的 IDs
		intersection := util.IntersectionInt64(param.orderIDs, param.hideIDs)
		if len(intersection) != 0 {
			return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 IDs：%s 同时出现在排序的礼物和下架的礼物中，请检查后重新输入", goutil.JoinInt64Array(intersection, ",")))
		}
	}

	if len(param.orderIDs) > 0 {
		orderGiftMap, err := gift.FindGiftMapByGiftIDs(param.orderIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		luckyGiftIDs := make([]int64, 0, len(param.orderIDs))
		for i := range param.orderIDs {
			g := orderGiftMap[param.orderIDs[i]]
			if g == nil {
				return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %d 不存在", param.orderIDs[i]))
			}
			if g.Type == gift.TypeDrawSend {
				luckyGiftIDs = append(luckyGiftIDs, g.GiftID)
			}
		}
		if len(luckyGiftIDs) > 0 {
			if err = checkLuckyGiftPool(luckyGiftIDs, orderGiftMap); err != nil {
				return err
			}
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 查询生效时刻在线上的所有礼物
	filter := bson.M{
		"type":       bson.M{"$gt": gift.TypeHide},
		"order":      bson.M{"$gt": gift.OrderHide},
		"added_time": bson.M{"$lte": time.Unix(param.EffectiveTime, 0)},
	}
	cur, err := gift.Collection().Find(ctx, filter, options.Find())
	if err != nil {
		return err
	}
	defer cur.Close(ctx)
	var allOnlineGifts []gift.Gift
	err = cur.All(ctx, &allOnlineGifts)
	if err != nil {
		return err
	}

	// 需要对所有的礼物进行排序
	gifts, _ := gift.GroupGifts(allOnlineGifts, gift.GroupGiftsOptions{
		User:   nil,
		RoomID: nil,
	})

	checkGifts := make([]int64, 0, len(gifts))
	for i := range gifts[param.Type] {
		checkGifts = append(checkGifts, gifts[param.Type][i].GiftID)
	}

	// 检查不存在或者被遗漏的礼物
	diff := util.DiffInt64(checkGifts, append(param.orderIDs, param.hideIDs...))
	// 如果没有需要检查的礼物 ID，则直接返回
	if len(diff) == 0 {
		return
	}

	// 检查 diff 的礼物是否是下架礼物
	filter = bson.M{
		"gift_id":    bson.M{"$in": diff},
		"order":      gift.OrderHide,
		"added_time": bson.M{"$lte": time.Unix(param.EffectiveTime, 0)},
	}
	cur, err = gift.Collection().Find(ctx, filter, options.Find())
	if err != nil {
		return err
	}
	defer cur.Close(ctx)
	var orderHideGifts []gift.Gift
	err = cur.All(ctx, &orderHideGifts)
	if err != nil {
		return err
	}

	orderHideCheckGifs := make([]int64, 0, len(orderHideGifts))
	for i := range orderHideGifts {
		orderHideCheckGifs = append(orderHideCheckGifs, orderHideGifts[i].GiftID)
	}

	// 如果 diff 中还是存在非下架礼物，则返回错误
	diff = util.DiffInt64(diff, orderHideCheckGifs)
	if len(diff) != 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 IDs: %s 不存在或被遗漏，请检查后重新输入", goutil.JoinInt64Array(diff, ",")))
	}

	return
}

type addGiftCustomParam struct {
	UserID int64 `form:"user_id" json:"user_id"`
	GiftID int64 `form:"gift_id" json:"gift_id"`
	Day    int   `form:"day" json:"day"`
}

// ActionGiftCustomAdd 发放用户定制礼物
/**
 * @api {post} /api/v2/admin/chatroom/gift/custom/add 发放用户定制礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} day 天数
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionGiftCustomAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newAddGiftCustomParam(c)
	if err != nil {
		return nil, err
	}
	// TODO: 增加对礼物是否正确的判断
	err = param.addGiftCustom()
	if err != nil {
		return nil, err
	}
	param.sendAdminLog(c)
	return "success", nil
}

func newAddGiftCustomParam(c *handler.Context) (*addGiftCustomParam, error) {
	var param *addGiftCustomParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.GiftID <= 0 || param.Day <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param addGiftCustomParam) addGiftCustom() error {
	err := livecustom.AddUserCustomGift(param.UserID, param.GiftID,
		time.Duration(param.Day)*24*time.Hour, true)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param addGiftCustomParam) sendAdminLog(c *handler.Context) {
	intro := fmt.Sprintf("发放用户定制礼物，用户 ID: %d, 礼物 ID: %d, 天数: %d", param.UserID, param.GiftID, param.Day)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageGift)
	// 管理员操作日志
	err := adminLogs.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type addGiftRoomCustomParam struct {
	RoomIDs string `form:"room_ids" json:"room_ids"`
	GiftID  int64  `form:"gift_id" json:"gift_id"`
	Day     int    `form:"day" json:"day"`

	roomIDs []int64
}

// ActionGiftRoomCustomAdd 发放直播间定制礼物
/**
 * @api {post} /api/v2/admin/chatroom/gift/room/custom/add 发放直播间定制礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} room_ids 直播间 ID e.g. "1,2,3" // 半角逗号分割
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} day 天数
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 */
func ActionGiftRoomCustomAdd(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: 接口逻辑比较复杂，会和个人场发放礼物冲突，后续有需要再调整
	// TODO: 支持发放到 missevan_live.live_custom 表
	skip := true
	if skip {
		return nil, actionerrors.ErrParamsMsg("暂时不支持发放直播间定制礼物")
	}

	param, err := newAddGiftRoomCustomParam(c)
	if err != nil {
		return nil, err
	}
	err = param.addGiftRoomCustom()
	if err != nil {
		return nil, err
	}
	param.sendAdminLog(c)
	return "设置成功", nil
}

func newAddGiftRoomCustomParam(c *handler.Context) (*addGiftRoomCustomParam, error) {
	var p *addGiftRoomCustomParam
	err := c.Bind(&p)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if p.RoomIDs == "" || p.GiftID <= 0 || p.Day <= 0 {
		return nil, actionerrors.ErrParams
	}
	roomIDs, err := goutil.SplitToInt64Array(p.RoomIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	p.roomIDs = util.Uniq(roomIDs)
	ok, err := room.ExistsAll(p.roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrParamsMsg("部分直播间不存在，请检查后重新输入")
	}
	g, err := gift.FindShowingGiftByGiftID(p.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	if g.Type != gift.TypeRoomCustom || g.RoomID != 0 {
		return nil, actionerrors.ErrParamsMsg("不支持当前礼物")
	}
	return p, nil
}

func (p addGiftRoomCustomParam) addGiftRoomCustom() error {
	/*
		roomStrIDs := make([]string, 0, len(p.roomIDs))
		for i := range p.roomIDs {
			roomStrIDs = append(roomStrIDs, strconv.FormatInt(p.roomIDs[i], 10))
		}
		prizeKey := keys.KeyGiftsRoomCustom1.Format(p.GiftID)
		rec, err := service.Redis.HMGet(prizeKey, roomStrIDs...).Result()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		updateMap := make(map[string]interface{}, len(roomStrIDs))
		for i := range rec {
			var customs []gift.TimeRange
			switch v := rec[i].(type) {
			case string:
				err := json.Unmarshal([]byte(v), &customs)
				if err != nil {
					logger.WithField("room_id", roomStrIDs[i]).Error(err)
					continue
				}
			}

			// TODO: 调整逻辑
			now := goutil.TimeNow()
			customs = append(customs, gift.TimeRange{
				StartTime: now.Unix(),
				EndTime:   goutil.BeginningOfDay(now).AddDate(0, 0, p.Day+1).Unix()},
			)
			bytes, err := json.Marshal(customs)
			if err != nil {
				logger.WithField("room_id", roomStrIDs[i]).Error(err)
				continue
			}
			updateMap[roomStrIDs[i]] = bytes
		}
		if err = service.Redis.HMSet(prizeKey, updateMap).Err(); err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	*/
	return nil
}

func (p addGiftRoomCustomParam) sendAdminLog(c *handler.Context) {
	intro := fmt.Sprintf("发放直播间定制礼物，直播间 IDs: %s, 礼物 ID: %d, 天数: %d", p.RoomIDs, p.GiftID, p.Day)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageGift)
	// 管理员操作日志
	err := adminLogs.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

const maxElementIDsBatchAdd = 1000

type exclusiveBatchAddParam struct {
	ExclusiveType int              `form:"exclusive_type" json:"exclusive_type"`       // 专属类型，1：用户专属礼物，2：直播间专属礼物
	GiftID        int64            `form:"gift_id" json:"gift_id"`                     // 礼物 ID
	CSVURL        upload.SourceURL `form:"csv_url" json:"csv_url"`                     // CSV 文件地址
	EffectiveTime int64            `form:"effective_time" json:"effective_time"`       // 生效时间，秒级时间戳
	ExpireTime    int64            `form:"expire_time" json:"expire_time"`             // 过期时间，秒级时间戳
	Confirm       int              `form:"confirm,omitempty" json:"confirm,omitempty"` // 确认次数，首次请求传 0

	customType int     // 定制类型
	elementIDs []int64 // 用户 ID 或者直播间 ID 的列表，从 CSV 文件中解析
	batchID    int64   // 批量分配 ID
}

// ActionExclusiveBatchAdd 专属礼物配置批量添加
/**
 * @api {post} /api/v2/admin/chatroom/gift/exclusive/batch-add 批量分配专属礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {number=1,2} exclusive_type 专属类型，1：用户专属礼物，2：直播间专属礼物
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {String} csv_url CSV 文件地址
 * @apiParam {Number} effective_time 生效时间，秒级时间戳
 * @apiParam {Number} expire_time 过期时间，秒级时间戳
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确认将礼物 10086 分配给 9075606 等 10 个用户？"
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "分配成功",
 *     "data": null
 *   }
 */
func ActionExclusiveBatchAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newExclusiveBatchAddParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.parseCSV()
	if err != nil {
		return nil, "", err
	}
	if len(param.elementIDs) > maxElementIDsBatchAdd {
		return nil, "", actionerrors.ErrParamsMsg(fmt.Sprintf("最多上传 %d 条数据", maxElementIDsBatchAdd))
	}
	switch param.ExclusiveType {
	case gift.GiftExclusiveUser:
		ok, err := mowangskuser.ExistsAll(param.elementIDs)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return nil, "", actionerrors.ErrParamsMsg("CSV 文件中存在无效用户 ID")
		}
	case gift.GiftExclusiveRoom:
		ok, err := room.ExistsAll(param.elementIDs)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return nil, "", actionerrors.ErrParamsMsg("CSV 文件中存在无效直播间 ID")
		}
	}
	if param.Confirm == 0 {
		var exclusiveTypeMsg string
		switch param.ExclusiveType {
		case gift.GiftExclusiveUser:
			exclusiveTypeMsg = "用户"
		case gift.GiftExclusiveRoom:
			exclusiveTypeMsg = "直播间"
		}
		var msg string
		if len(param.elementIDs) > 1 {
			msg = fmt.Sprintf("确认将礼物 %d 分配给 %d 等 %d 个%s？", param.GiftID, param.elementIDs[0], len(param.elementIDs), exclusiveTypeMsg)
		} else {
			msg = fmt.Sprintf("确认将礼物 %d 分配给%s %d？", param.GiftID, exclusiveTypeMsg, param.elementIDs[0])
		}
		return nil, "", actionerrors.ErrConfirmRequired(msg, 1)
	}
	err = param.batchAdd()
	if err != nil {
		return nil, "", err
	}
	param.sendAdminLog(c)
	return nil, "分配成功", nil
}

func newExclusiveBatchAddParam(c *handler.Context) (*exclusiveBatchAddParam, error) {
	var param *exclusiveBatchAddParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.CSVURL == "" || param.GiftID <= 0 || param.EffectiveTime <= 0 || param.ExpireTime <= 0 {
		return nil, actionerrors.ErrParams
	}
	if param.ExpireTime <= param.EffectiveTime {
		return nil, actionerrors.ErrParamsMsg("过期时间不能早于生效时间！")
	}
	g, err := gift.FindByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg("礼物不存在")
	}
	switch param.ExclusiveType {
	case gift.GiftExclusiveUser:
		if g.Type != gift.TypeDrawSend && g.Type != gift.TypeCustom {
			return nil, actionerrors.ErrParamsMsg("礼物类型不支持用户专属")
		}
		param.customType = livecustom.TypeUserCustomGift
	case gift.GiftExclusiveRoom:
		if g.Type != gift.TypeDrawSend && g.Type != gift.TypeRoomCustom {
			return nil, actionerrors.ErrParamsMsg("礼物类型不支持直播间专属")
		}
		param.customType = livecustom.TypeRoomCustomGift
	default:
		return nil, actionerrors.ErrParamsMsg("不支持的专属类型")
	}
	return param, nil
}

func (p *exclusiveBatchAddParam) parseCSV() error {
	res, err := service.Upload.ToResource(p.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 文件读取失败，请检查文件后再试")
	}
	defer file.Close()
	p.elementIDs, err = csv.ReadFirstColumnInt64(file)
	if err != nil {
		return actionerrors.ErrParamsMsg(err.Error())
	}
	return nil
}

func (p *exclusiveBatchAddParam) batchAdd() error {
	savedCSVURL, err := service.Upload.Upload(p.CSVURL, storage.SchemeURLPrefixLiveLog)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	var batchID int64
	err = servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		liveCustomBatchAddLog := livecustombatchaddlog.LiveCustomBatchAddLog{
			CustomType:    p.customType,
			CustomID:      p.GiftID,
			CSVURL:        savedCSVURL,
			EffectiveTime: p.EffectiveTime,
			ExpireTime:    p.ExpireTime,
		}
		if err := tx.Create(&liveCustomBatchAddLog).Error; err != nil {
			return err
		}
		now := goutil.TimeNow().Unix()
		batchID = liveCustomBatchAddLog.ID
		liveCustoms := make([]livecustom.LiveCustom, 0, len(p.elementIDs))
		for _, elementID := range p.elementIDs {
			liveCustom := livecustom.LiveCustom{
				CreateTime:   now,
				ModifiedTime: now,
				CustomType:   p.customType,
				CustomID:     p.GiftID,
				ElementID:    elementID,
				// 保留生效时间重叠的配置，不需要进行合并，以便删除时不影响其它生效时间重叠的配置
				StartTime: p.EffectiveTime,
				EndTime:   p.ExpireTime,
				BatchID:   liveCustomBatchAddLog.ID,
			}
			liveCustoms = append(liveCustoms, liveCustom)
		}
		if err := servicedb.BatchInsert(tx, livecustom.LiveCustom{}.TableName(), liveCustoms); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.batchID = batchID
	return nil
}

func (p *exclusiveBatchAddParam) sendAdminLog(c *handler.Context) {
	var intro string
	effectiveTime := time.Unix(p.EffectiveTime, 0).Format(util.TimeFormatYMDHMS)
	expireTime := time.Unix(p.ExpireTime, 0).Format(util.TimeFormatYMDHMS)
	switch p.ExclusiveType {
	case gift.GiftExclusiveRoom:
		intro = fmt.Sprintf(
			"【新增分配】直播间专属礼物；批量分配 ID %d；礼物 ID %d；直播间数量 %d；生效时间 %s；失效时间 %s",
			p.batchID, p.GiftID, len(p.elementIDs), effectiveTime, expireTime,
		)
	case gift.GiftExclusiveUser:
		intro = fmt.Sprintf(
			"【新增分配】用户专属礼物；批量分配 ID %d；礼物 ID %d；用户数量 %d；生效时间 %s；失效时间 %s",
			p.batchID, p.GiftID, len(p.elementIDs), effectiveTime, expireTime,
		)
	}
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageGift)
	err := adminLogs.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type exclusiveBatchDeleteParam struct {
	BatchID int64 `form:"batch_id" json:"batch_id"`                   // 批量分配 ID
	Confirm int   `form:"confirm,omitempty" json:"confirm,omitempty"` // 确认次数，首次请求传 0
}

// ActionExclusiveBatchDelete 专属礼物配置批量删除
/**
 * @api {post} /api/v2/admin/chatroom/gift/exclusive/batch-delete 删除专属礼物批量分配
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} batch_id 批量分配 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确认删除专属礼物批量分配？<br>礼物 ID：10086<br>专属类型：直播间专属<br>生效时间：2025-01-01 00:00:00<br>失效时间：2025-01-10 00:00:00"
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
func ActionExclusiveBatchDelete(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newExclusiveBatchDeleteParam(c)
	if err != nil {
		return nil, "", err
	}
	batchAddLog, err := param.findBatchAddLog()
	if err != nil {
		return nil, "", err
	}
	var customTypeMsg string
	switch batchAddLog.CustomType {
	case livecustom.TypeRoomCustomGift:
		customTypeMsg = "直播间专属"
	case livecustom.TypeUserCustomGift:
		customTypeMsg = "用户专属"
	default:
		return nil, "", actionerrors.ErrParamsMsg("不支持的专属类型")
	}
	liveCustomCount, err := param.findLiveCustomCount()
	if err != nil {
		return nil, "", err
	}
	if liveCustomCount == 0 {
		return nil, "", actionerrors.ErrParamsMsg("没有找到关联的专属礼物分配记录")
	}
	if param.Confirm == 0 {
		effectiveTimeStr := time.Unix(batchAddLog.EffectiveTime, 0).Format(util.TimeFormatYMDHMS)
		expireTimeStr := time.Unix(batchAddLog.ExpireTime, 0).Format(util.TimeFormatYMDHMS)
		msg := fmt.Sprintf(
			"确认删除专属礼物批量分配？<br>礼物 ID：%d<br>专属类型：%s<br>生效时间：%s<br>失效时间：%s",
			batchAddLog.CustomID, customTypeMsg, effectiveTimeStr, expireTimeStr,
		)
		return nil, "", actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	err = param.batchDelete()
	if err != nil {
		return nil, "", err
	}
	param.sendAdminLog(c, batchAddLog)
	return nil, "删除成功", nil
}

func newExclusiveBatchDeleteParam(c *handler.Context) (*exclusiveBatchDeleteParam, error) {
	var param exclusiveBatchDeleteParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.BatchID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

func (p *exclusiveBatchDeleteParam) findBatchAddLog() (*livecustombatchaddlog.LiveCustomBatchAddLog, error) {
	var batchAddLog livecustombatchaddlog.LiveCustomBatchAddLog
	err := batchAddLog.DB().Where("id = ? AND delete_time = 0", p.BatchID).Take(&batchAddLog).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrParamsMsg("专属礼物批量分配记录不存在")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &batchAddLog, nil
}

func (p *exclusiveBatchDeleteParam) findLiveCustomCount() (int64, error) {
	var count int64
	err := livecustom.LiveCustom{}.DB().
		Where("batch_id = ? AND delete_time = 0", p.BatchID).
		Count(&count).Error
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	return count, nil
}

func (p *exclusiveBatchDeleteParam) batchDelete() error {
	errUpdateFailed := errors.New("乐观锁更新失败")
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		now := goutil.TimeNow().Unix()
		db := tx.Table(livecustom.LiveCustom{}.TableName()).
			Where("batch_id = ? AND delete_time = 0", p.BatchID).
			Updates(map[string]interface{}{
				"delete_time":   now,
				"modified_time": now,
			})
		if err := db.Error; err != nil {
			return err
		}
		db = tx.Table(livecustombatchaddlog.LiveCustomBatchAddLog{}.TableName()).
			Where("id = ? AND delete_time = 0", p.BatchID).
			Updates(map[string]interface{}{
				"delete_time":   now,
				"modified_time": now,
			})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return errUpdateFailed
		}
		return nil
	})
	if err != nil {
		if errors.Is(err, errUpdateFailed) {
			return actionerrors.ErrParamsMsg("没有找到关联的专属礼物分配记录")
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *exclusiveBatchDeleteParam) sendAdminLog(c *handler.Context, batchAddLog *livecustombatchaddlog.LiveCustomBatchAddLog) {
	var customTypeMsg string
	switch batchAddLog.CustomType {
	case livecustom.TypeRoomCustomGift:
		customTypeMsg = "直播间专属"
	case livecustom.TypeUserCustomGift:
		customTypeMsg = "用户专属"
	}
	effectiveTimeStr := time.Unix(batchAddLog.EffectiveTime, 0).Format(util.TimeFormatYMDHMS)
	expireTimeStr := time.Unix(batchAddLog.ExpireTime, 0).Format(util.TimeFormatYMDHMS)
	intro := fmt.Sprintf(
		"【删除分配】%s礼物；批量分配 ID %d；礼物 ID %d；生效时间 %s；失效时间 %s",
		customTypeMsg, batchAddLog.ID, batchAddLog.CustomID, effectiveTimeStr, expireTimeStr,
	)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageGift)
	err := adminLogs.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type giftSetInfo struct {
	Msg string `json:"msg"`
}

type importGiftInfoParam struct {
	CSVURL upload.SourceURL `form:"csv_url" json:"csv_url"`

	resp         giftSetInfo
	sqlData      []sqlgift.Gift
	mongoData    []*gift.CSVGift
	sqlGiftIDs   []int64
	mongoGiftIDs []int64
}

// ActionGiftImport 导入礼物信息
/**
 * @api {post} /api/v2/admin/chatroom/gift/import 导入礼物信息
 * @apiDescription 导入礼物信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} csv_url csv 地址
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "导入成功 2 个礼物"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGiftImport(c *handler.Context) (handler.ActionResponse, error) {
	var param importGiftInfoParam
	err := c.Bind(&param)
	if err != nil || param.CSVURL == "" {
		return nil, actionerrors.ErrParams
	}
	err = param.parseCSV()
	if err != nil {
		return nil, err
	}
	err = param.insertGiftData()
	if err != nil {
		return nil, err
	}
	param.resp.Msg = fmt.Sprintf("导入成功 %d 个礼物", len(param.mongoData))
	return param.resp, nil
}

func (p *importGiftInfoParam) parseCSV() error {
	res, err := service.Upload.ToResource(p.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	csvData, err := io.ReadAll(file)
	file.Close()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	var imports []*gift.CSVGift
	if err := gocsv.UnmarshalBytes(csvData, &imports); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(imports) == 0 {
		return actionerrors.ErrParamsMsg("CSV 不可为空")
	}

	for i := range imports {
		imports[i].TrimSpaces()
		if err = checkGiftParam(imports[i]); err != nil {
			return err
		}
		p.buildSQLGift(imports[i])
		if err = p.buildMongoGift(imports[i]); err != nil {
			return err
		}
	}
	return nil
}

func checkGiftParam(g *gift.CSVGift) error {
	if g.GiftID < 0 || g.Type < 0 || g.Exclusive < 0 || g.Price < 0 || g.Point < 0 ||
		g.NobleLevel < 0 || g.MedalLevel < 0 || g.UserLevel < 0 || g.UserID < 0 || g.RoomID < 0 || g.ToggleTime < 0 {
		return actionerrors.ErrParamsMsg("礼物信息错误, 请检查后重新上传")
	}
	if g.Exclusive != 0 {
		if g.Exclusive != gift.GiftExclusiveUser && g.Exclusive != gift.GiftExclusiveRoom {
			return actionerrors.ErrParamsMsg("专属类型错误，请检查后重新上传")
		}
		if g.Type != gift.TypeDrawSend {
			return actionerrors.ErrParamsMsg("仅支持随机礼物设置专属类型，请检查后重新上传")
		}
	}
	if g.BaseGiftID != 0 && !(g.Type == gift.TypeCustom && g.NobleLevel > 0) {
		return actionerrors.ErrParamsMsg("贵族礼物的模板礼物、礼物类型或贵族等级限制错误，请检查后重新上传")
	}
	if g.UserLevel > len(usercommon.LevelStart) {
		return actionerrors.ErrParamsMsg("用户等级限制错误，请检查后重新上传")
	}
	if g.VipType != 0 && (g.VipType != vip.TypeLiveHighness && g.VipType != vip.TypeLiveNoble) {
		return actionerrors.ErrParamsMsg("贵族礼物的类别错误，请检查后重新上传")
	}
	return nil
}

func (p *importGiftInfoParam) buildMongoGift(g *gift.CSVGift) error {
	p.mongoGiftIDs = append(p.mongoGiftIDs, g.GiftID)
	err := g.BuildMongoGifts()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.mongoData = append(p.mongoData, g)
	return nil
}

func (p *importGiftInfoParam) buildSQLGift(g *gift.CSVGift) {
	if g.Type == gift.TypeFree {
		return
	}

	sg := sqlgift.Gift{
		ID:    g.GiftID,
		Name:  g.Name,
		Price: g.Price,
		Type:  sqlgift.TypeLiveCommon,
	}
	if g.Type == gift.TypeCustom && g.NobleLevel == 7 {
		sg.Name = "【神话定制】" + sg.Name
	}
	if g.Type == gift.TypeRebate {
		sg.Type = sqlgift.TypeLiveRebate
	}
	p.sqlGiftIDs = append(p.sqlGiftIDs, g.GiftID)
	p.sqlData = append(p.sqlData, sg)
}

func (p *importGiftInfoParam) insertGiftData() error {
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if len(p.sqlGiftIDs) > 0 {
			err := tx.Table(sqlgift.TableName()).Delete("", "id IN (?)", p.sqlGiftIDs).Error
			if err != nil {
				return err
			}
			if err = servicedb.BatchInsert(tx, sqlgift.TableName(), p.sqlData); err != nil {
				return err
			}
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		updates := make([]mongo.WriteModel, 0, len(p.mongoData)+1)
		updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{"gift_id": bson.M{"$in": p.mongoGiftIDs}}))
		for i := range p.mongoData {
			m := mongo.NewInsertOneModel()
			m.SetDocument(p.mongoData[i])
			updates = append(updates, m)
		}
		_, err := gift.Collection().BulkWrite(ctx, updates)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type giftSetOnlineParams struct {
	GiftIDs       string `form:"gift_ids" json:"gift_ids"`
	EffectiveTime int64  `form:"effective_time" json:"effective_time"`
	Confirm       int    `form:"confirm" json:"confirm"`

	c       *handler.Context
	giftIDs []int64
	giftMap map[int64]*gift.Gift
}

// ActionGiftSetOnline 设定礼物上架
/**
 * @api {post} /api/v2/admin/chatroom/gift/set-online 设定礼物上架
 * @apiDescription 上架礼物，order 默认为礼物 ID
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} gift_ids 要排序的礼物 IDs, 半角逗号分割
 * @apiParam {Number} [effective_time=0] 生效时间, 0 表示立即生效, 单位秒级时间戳, 如: 1578844800
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认上架礼物以下礼物嘛<br> 1 礼物"
 *     }
 *   }
 */
func ActionGiftSetOnline(c *handler.Context) (handler.ActionResponse, string, error) {
	param := giftSetOnlineParams{c: c}
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	if err = param.load(); err != nil {
		return nil, "", err
	}
	if err = param.check(); err != nil {
		return nil, "", err
	}
	if err = param.setOnline(); err != nil {
		return nil, "", err
	}
	param.sendLog()
	param.sendBotMessage()
	return nil, "上架成功", nil
}

func (p *giftSetOnlineParams) load() error {
	if p.GiftIDs == "" {
		return actionerrors.ErrParams
	}
	if p.EffectiveTime != 0 && p.EffectiveTime < goutil.TimeNow().Unix() {
		return actionerrors.ErrParamsMsg("生效时间不能早于当前时间")
	}
	var err error
	p.giftIDs, err = goutil.SplitToInt64Array(p.GiftIDs, ",")
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.giftIDs) == 0 {
		return actionerrors.ErrParamsMsg("礼物 ID 不能为空")
	}
	uniqGiftIDs := sets.Uniq(p.giftIDs)
	if len(uniqGiftIDs) != len(p.giftIDs) {
		return actionerrors.ErrParamsMsg("礼物 ID 重复，请检查后重新输入")
	}
	p.giftMap, err = gift.FindGiftMapByGiftIDs(p.giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.giftMap) != len(uniqGiftIDs) {
		return actionerrors.ErrParamsMsg("部分礼物 ID 不存在，请检查后重新输入")
	}
	return nil
}

func (p *giftSetOnlineParams) check() error {
	showGiftIDs := make([]int64, 0, len(p.giftMap))
	setOrderGiftIDs := make([]int64, 0, len(p.giftMap))
	unsupportGiftIDs := make([]int64, 0, len(p.giftMap))
	luckyGiftIDs := make([]int64, 0, len(p.giftMap))
	for _, g := range p.giftMap {
		if g.Type == gift.TypeDrawReceive {
			unsupportGiftIDs = append(unsupportGiftIDs, g.GiftID)
			continue
		} else if g.Type == gift.TypeDrawSend {
			luckyGiftIDs = append(luckyGiftIDs, g.GiftID)
			continue
		}
		if g.Order != gift.OrderHide {
			showGiftIDs = append(showGiftIDs, g.GiftID)
			continue
		}
		if g.SetOrder != nil {
			setOrderGiftIDs = append(setOrderGiftIDs, g.GiftID)
		}
	}
	if len(unsupportGiftIDs) > 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("部分礼物类型不支持上架操作, 礼物 ID: %s, 请检查后重新输入",
			goutil.JoinInt64Array(unsupportGiftIDs, ",")))
	}
	if len(showGiftIDs) > 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %s 已经上架, 请检查后重新输入",
			goutil.JoinInt64Array(showGiftIDs, ",")))
	}
	if err := checkLuckyGiftPool(luckyGiftIDs, p.giftMap); err != nil {
		return err
	}

	if p.Confirm == 0 {
		content := "确认上架以下礼物吗"
		temp := "<br>%d: %s"
		for i := range p.giftIDs {
			if g := p.giftMap[p.giftIDs[i]]; g != nil {
				content += fmt.Sprintf(temp, g.GiftID, g.Name)
			}
		}
		if len(setOrderGiftIDs) > 0 {
			content += fmt.Sprintf("<br>当前礼物 %s 已经设置了定时排序，提交后排序可能冲突，请确认是否继续上架",
				goutil.JoinInt64Array(setOrderGiftIDs, ","))
		}
		return actionerrors.ErrConfirmRequired(content, 1, true)
	}
	return nil
}

func checkLuckyGiftPool(luckyGiftIDs []int64, giftMap map[int64]*gift.Gift) error {
	if len(luckyGiftIDs) == 0 {
		return nil
	}
	pools, err := gift.ListPoolGift(luckyGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(pools) == 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("随机礼物奖池未配置, 礼物 ID: %s, 请检查后重新输入",
			goutil.JoinInt64Array(luckyGiftIDs, ",")))
	}
	for i := range luckyGiftIDs {
		g := giftMap[luckyGiftIDs[i]]
		if g == nil {
			return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %d 不存在, 请检查后重新输入", luckyGiftIDs[i]))
		}
		if len(g.AllowedNums) == 0 {
			return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %d 未配置奖池档位, 请检查后重新输入", g.GiftID))
		}
		for _, allowNum := range g.AllowedNums {
			foundIndex := slices.IndexFunc(pools, func(pool *gift.PoolGift) bool {
				return pool.GiftID == g.GiftID && pool.GiftNum == allowNum
			})
			if foundIndex < 0 {
				return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %d, 数量: %d 档位的奖池配置不存在, 请检查后重新输入",
					g.GiftID, allowNum))
			}
			if ok, _ := pools[foundIndex].Valid(); !ok {
				return actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 ID: %d, 数量: %d 档位的奖池配置错误, 请检查后重新输入",
					g.GiftID, allowNum))
			}
		}
	}
	return nil
}

func (p *giftSetOnlineParams) setOnline() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	updates := make([]mongo.WriteModel, 0, len(p.giftIDs))
	for _, giftID := range p.giftIDs {
		m := mongo.NewUpdateOneModel()
		m.SetFilter(bson.M{"gift_id": giftID})
		order := giftID // 默认排序为礼物 ID
		if p.EffectiveTime == 0 {
			m.SetUpdate(bson.M{
				"$set": bson.M{
					"order":       order,
					"toggle_time": goutil.TimeNow().Unix(),
				},
				"$unset": bson.M{"setorder": ""},
			})
		} else {
			m.SetUpdate(bson.M{
				"$set": bson.M{"setorder": bson.M{
					"effective_time": time.Unix(p.EffectiveTime, 0),
					"order":          order,
				}},
			})
		}
		updates = append(updates, m)
	}
	_, err := gift.Collection().BulkWrite(ctx, updates)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *giftSetOnlineParams) sendLog() {
	box := goclient.NewAdminLogBox(p.c)
	intro := fmt.Sprintf("上架礼物，礼物 ID：%s", goutil.JoinInt64Array(p.giftIDs, ","))

	box.Add(userapi.CatalogManageGift, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *giftSetOnlineParams) sendBotMessage() {
	message := "礼物上架提醒\n"
	userOA, err := useroa.FindByUserID(p.c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if userOA == nil {
		logger.Errorf("用户 %d 未绑定 OA 账号", p.c.UserID())
	} else {
		message += fmt.Sprintf("操作人：%s", userOA.OAName)
	}
	if p.EffectiveTime == 0 {
		message += "\n生效时间：立即生效"
	} else {
		message += fmt.Sprintf("\n生效时间：%s", time.Unix(p.EffectiveTime, 0).Format(util.TimeFormatYMDHMS))
	}
	for i := range p.giftIDs {
		if g := p.giftMap[p.giftIDs[i]]; g != nil {
			message += fmt.Sprintf("\n> <font color=\"warning\">%d</font> <font color=\"comment\">%s</font>", g.GiftID, g.Name)
		}
	}
	message += "\n"
	for _, mentionUser := range config.Conf.Params.Notification.SetGiftOnlineMentionUsers {
		message += fmt.Sprintf("<@%s>", mentionUser)
	}
	err = service.PushService.SendBot(pushservice.BotMessage{
		Message: message,
		Channel: "gift_notify",
	})
	if err != nil {
		logger.Error(err)
	}
}
