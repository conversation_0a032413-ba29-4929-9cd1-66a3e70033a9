package chatroom

import (
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type scoreSetParams struct {
	RoomIDs   string `form:"room_ids" json:"room_ids"`
	Score     int64  `form:"score" json:"score"`
	StartTime int64  `form:"start_time" json:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time"`
	Confirm   int    `form:"confirm" json:"confirm"`

	roomIDs              []int64
	roomStrIDs           []string
	htmlCreatorUsernames []string
}

// maxExtraScore 奖励热度的最大值
const maxExtraScore = 100000

// ActionExtraScoreSet 配置房间奖励热度
/**
 * @api {post} /api/v2/admin/chatroom/score/set 配置房间奖励热度
 * @apiDescription 配置房间奖励热度
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} room_ids 直播间 ID, 批量设置, 使用半角逗号分割
 * @apiParam {Number} score 热度，为整数，最大值为 100000。如果传 0 表示清空热度
 * @apiParam {Number} start_time 开始时间, 如: 1610467200，设置热度时不可传 0，清空热度时不需要传开始时间
 * @apiParam {Number} end_time 结束时间, 如: 1610467300，设置热度时不可传 0，清空热度时不需要传结束时间
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiSuccessExample 配置成功:
 *   {
 *     "code": 0,
 *     "info": "配置成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "提示信息"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 *
 */
func ActionExtraScoreSet(c *handler.Context) (handler.ActionResponse, error) {
	var param scoreSetParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.roomIDs, err = util.SplitToInt64Array(param.RoomIDs, ",")
	if err != nil || len(param.roomIDs) <= 0 || len(param.roomIDs) > 100 {
		return nil, actionerrors.ErrParams
	}
	if param.Score < 0 {
		return nil, actionerrors.ErrParamsMsg("请输入正确的热度值")
	}
	if param.Score > maxExtraScore {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("请输入小于等于 %d 的热度值", maxExtraScore))
	}
	rooms, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": param.roomIDs}, "limit": bson.M{"$exists": false}},
		nil, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.roomIDs) != len(rooms) || len(rooms) == 0 {
		return nil, actionerrors.ErrParamsMsg("有直播间不存在，请检查后重新输入")
	}
	for i := range rooms {
		param.roomStrIDs = append(param.roomStrIDs, strconv.FormatInt(rooms[i].RoomID, 10))
		param.htmlCreatorUsernames = append(param.htmlCreatorUsernames, html.EscapeString(rooms[i].CreatorUsername))
	}
	if param.Score != 0 {
		if param.StartTime <= 0 || param.EndTime <= 0 {
			return nil, actionerrors.ErrParams
		}
		if param.StartTime <= goutil.TimeNow().Unix() {
			return nil, actionerrors.ErrParamsMsg("开始时间需晚于当前时间，请选择正确的时间")
		}
		if param.StartTime >= param.EndTime {
			return nil, actionerrors.ErrParamsMsg("结束时间需晚于开始时间，请选择正确的时间")
		}
	}

	if param.Confirm == 0 {
		heatConfirm := `<h2 align="center">设置热度确认</h2><p>房间号：%s<br>主播昵称：%s<br>热度值：%d`
		message := fmt.Sprintf(heatConfirm, strings.Join(param.roomStrIDs, ", "),
			strings.Join(param.htmlCreatorUsernames, ", "), param.Score)
		if param.Score != 0 {
			startTime := time.Unix(param.StartTime, 0)
			endTime := time.Unix(param.EndTime, 0)
			timeInfo := `<br>开始时间：%s<br>结束时间：%s<br>持续时间：%d min</p>`
			message += fmt.Sprintf(timeInfo, startTime.Format(goutil.TimeFormatHMS),
				endTime.Format(goutil.TimeFormatHMS), int64(endTime.Sub(startTime).Minutes()))
		} else {
			message += `</p>`
		}
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	}
	if err := param.setExtraScore(c); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "配置成功", nil
}

func (param scoreSetParams) setExtraScore(c *handler.Context) error {
	// 发送管理员操作日志
	var intro string
	if param.Score == 0 {
		intro = fmt.Sprintf("清空直播间热度，房间号：%s", strings.Join(param.roomStrIDs, ", "))
		if err := livemeta.ClearExtraScore(param.roomIDs); err != nil {
			return err
		}
	} else {
		if err := livemeta.SetExtraScore(param.roomIDs, float64(param.Score), time.Unix(param.StartTime, 0),
			time.Unix(param.EndTime, 0)); err != nil {
			return err
		}

		intro = fmt.Sprintf("配置直播间热度，房间号：%s，奖励热度: %d，开始时间：%s，结束时间：%s",
			strings.Join(param.roomStrIDs, ", "), param.Score,
			time.Unix(param.StartTime, 0).Format(goutil.TimeFormatHMS), time.Unix(param.EndTime, 0).Format(goutil.TimeFormatHMS))
	}
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogSetExtraScore, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
