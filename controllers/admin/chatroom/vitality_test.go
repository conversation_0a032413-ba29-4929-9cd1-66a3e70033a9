package chatroom

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionVitalityReduce(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	newC := func(param map[string]interface{}) *handler.Context {
		return handler.NewTestContext("POST", "/vitality/reduce", true, tutil.ToRequestBody(param))
	}
	param := make(map[string]interface{})
	_, err := ActionVitalityReduce(newC(param))
	assert.Equal(actionerrors.ErrParams, err)
	param["reason"] = "go test"
	param["value"] = 1
	param["creator_id"] = 999999999
	_, err = ActionVitalityReduce(newC(param))
	assert.Equal(actionerrors.ErrCannotFindUser, err)

	la := liveaddendum.LiveAddendum{
		Vitality:     12,
		CreateTime:   1234567890,
		ModifiedTime: 1234567890,
	}
	la.SetUserID(123)
	require.NoError(service.DB.Save(&la).Error)
	defer func() {
		service.DB.Table(messageassign.TableName()).Delete("",
			"recuid = 123 AND title = ?", "元气值扣除通知")
	}()
	param["creator_id"] = 123
	r, err := ActionVitalityReduce(newC(param))
	require.NoError(err)
	assert.Equal("扣分成功", r)
}

func TestActionVitalityIncrease(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	newC := func(param map[string]interface{}) *handler.Context {
		return handler.NewTestContext("POST", "/vitality/increase", true, tutil.ToRequestBody(param))
	}
	param := make(map[string]interface{})
	_, err := ActionVitalityIncrease(newC(param))
	assert.Equal(actionerrors.ErrParams, err)
	param["value"] = 1
	param["creator_id"] = 999999999
	_, err = ActionVitalityIncrease(newC(param))
	assert.Equal(actionerrors.ErrCannotFindUser, err)

	la := liveaddendum.LiveAddendum{
		Vitality:     5,
		CreateTime:   1234567890,
		ModifiedTime: 1234567890,
	}
	la.SetUserID(345)
	require.NoError(service.DB.Save(&la).Error)
	defer func() {
		service.DB.Table(messageassign.TableName()).Delete("",
			"recuid = 345 AND title = ?", "元气值返还通知")
	}()
	param["creator_id"] = 345
	r, err := ActionVitalityIncrease(newC(param))
	require.NoError(err)
	assert.Equal("返还成功", r)
}
