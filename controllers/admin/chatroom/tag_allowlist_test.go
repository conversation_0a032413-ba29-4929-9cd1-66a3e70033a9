package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/livetagcontrollist"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewTagAllowListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"tag_id":   24,
		"user_ids": "",
	})
	_, err := newTagAllowListParam(c, true)
	assert.EqualError(err, "M号不得为空")

	c = handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"tag_id":   24,
		"user_ids": ",,,",
	})
	_, err = newTagAllowListParam(c, true)
	assert.EqualError(err, "参数错误")

	c = handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"tag_id":   24,
		"user_ids": "10,31323,42342,3121313",
	})
	_, err = newTagAllowListParam(c, true)
	assert.EqualError(err, "存在无效的M号：31323，42342，3121313")

	c = handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"confirm":  0,
		"tag_id":   24,
		"user_ids": "10",
	})
	_, err = newTagAllowListParam(c, true)
	assert.EqualError(err, "用户都已在白名单中")

	c = handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"confirm":  0,
		"tag_id":   24,
		"user_ids": "892,186192636,10",
	})
	_, err = newTagAllowListParam(c, true)
	assert.EqualError(err, "确定要将 1 位用户加入 pia 戏功能白名单么？")

	c = handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"confirm":  1,
		"tag_id":   24,
		"user_ids": "11223344,186192636",
	})
	param, err := newTagAllowListParam(c, true)
	require.NoError(err)
	require.NotNil(param)
	require.Equal(2, len(param.rooms))

	c = handler.NewTestContext(http.MethodPost, "/remove", true, handler.M{
		"confirm":  0,
		"tag_id":   24,
		"user_ids": "11223344,10,186192636",
	})
	_, err = newTagAllowListParam(c, false)
	assert.EqualError(err, "确定要将 2 位用户移出 pia 戏功能白名单么？")

	c = handler.NewTestContext(http.MethodPost, "/remove", true, handler.M{
		"confirm":  0,
		"tag_id":   24,
		"user_ids": "11223344",
	})
	_, err = newTagAllowListParam(c, false)
	assert.EqualError(err, "用户都不在白名单中")

	c = handler.NewTestContext(http.MethodPost, "/remove", true, handler.M{
		"confirm":  0,
		"tag_id":   24,
		"user_ids": "4",
	})
	_, err = newTagAllowListParam(c, false)
	assert.EqualError(err, "存在非主播身份的M号：4")
}

func TestActionTagAllowListAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomIDs = []int64{11223344, 186192636}
		testUserIDs = []int64{11223344, 186192636}
	)

	c := handler.NewTestContext(http.MethodPost, "/add", true, handler.M{
		"confirm":  1,
		"tag_id":   24,
		"user_ids": goutil.JoinInt64Array(testUserIDs, ","),
	})
	resp, err := ActionTagAllowListAdd(c)
	require.NoError(err)
	assert.Equal("添加成功！", resp.(handler.M)["msg"])

	list, err := livetagcontrollist.ControlList(tag.TagListenDrama, livetagcontrollist.StatusAllowRoomAddTag, testRoomIDs)
	require.NoError(err)
	require.Equal(2, len(list))
}

func TestActionTagAllowListRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomIDs = []int64{11223344, 186192636}
		testUserIDs = []int64{11223344, 186192636}
	)

	rooms, err := room.FindAll(bson.M{"creator_id": bson.M{"$in": testUserIDs}})
	require.NoError(err)
	require.Equal(2, len(rooms))

	c := handler.NewTestContext(http.MethodPost, "/remove", true, handler.M{
		"confirm":  1,
		"tag_id":   tag.TagListenDrama,
		"user_ids": goutil.JoinInt64Array(testUserIDs, ","),
	})
	resp, err := ActionTagAllowListRemove(c)
	require.NoError(err)
	assert.Equal("移出成功！", resp.(handler.M)["msg"])

	list, err := livetagcontrollist.ControlList(tag.TagListenDrama, livetagcontrollist.StatusAllowRoomAddTag, testRoomIDs)
	require.NoError(err)
	require.Empty(list)
}
