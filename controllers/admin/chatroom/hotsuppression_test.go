package chatroom

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testRoomIDExist    = int64(18113499)
	testRoomIDNotExist = int64(-1)
	testCreatorID      = int64(12)
)

func TestActionUpdateSuppressionHotList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/admin/chatroom/hotsuppression/update"

	testCases := []struct {
		roomID  int64
		action  string
		endTime int64
		wantRes handler.ActionResponse
		wantErr string
	}{
		{roomID: 0, action: "add", endTime: 0, wantRes: nil, wantErr: actionerrors.ErrParams.Error()},
		{roomID: testRoomIDNotExist, action: "add", endTime: 0, wantRes: nil, wantErr: actionerrors.ErrCannotFindRoom.Error()},
		{roomID: testRoomIDExist, action: "add", endTime: -2, wantRes: nil, wantErr: actionerrors.ErrParams.Error()},
		{roomID: testRoomIDExist, action: "add", endTime: goutil.TimeNow().Unix() - 100, wantRes: nil, wantErr: "限制过期不能小于当前时间"},
		{roomID: testRoomIDExist, action: "add", endTime: goutil.TimeNow().Unix() + 100, wantRes: "success", wantErr: ""},
		{roomID: testRoomIDExist, action: "add", endTime: 0, wantRes: "success", wantErr: ""},
		{roomID: testRoomIDExist, action: "del", wantRes: "success", wantErr: ""},
		{roomID: testRoomIDExist, action: "del", wantRes: nil, wantErr: "限制列表中无该直播间"},
	}
	for _, tc := range testCases {
		param := hotSuppressionUpdateParam{RoomID: tc.roomID, Action: tc.action, EndTime: tc.endTime}
		c := handler.NewTestContext(http.MethodPost, api, true, &param)
		res, err := ActionHotSuppressionUpdate(c)
		assert.Equal(tc.wantRes, res)
		if tc.wantErr != "" {
			assert.EqualError(err, tc.wantErr)
		}
	}

	t.Run("测试添加热度限制直播间时，清空直播间的常驻榜单积分", func(t *testing.T) {
		param := hotSuppressionUpdateParam{RoomID: testRoomIDExist, Action: "add", EndTime: 0}
		for _, t := range clearRankTypes {
			key := usersrank.Key(t, goutil.TimeNow())
			err := service.Redis.ZAdd(key, &redis.Z{Score: float64(goutil.TimeNow().Unix()), Member: strconv.FormatInt(testCreatorID, 10)}).Err()
			require.NoError(err)
		}

		c := handler.NewTestContext(http.MethodPost, api, true, &param)
		res, err := ActionHotSuppressionUpdate(c)
		require.NoError(err)
		assert.Equal("success", res)
		for _, t := range clearRankTypes {
			key := usersrank.Key(t, goutil.TimeNow())
			_, err = service.Redis.ZScore(key, strconv.FormatInt(testCreatorID, 10)).Result()
			assert.True(serviceredis.IsRedisNil(err))
		}
	})
}
