package chatroom

import (
	"encoding/json"
	"fmt"
	"html"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionHornMuteAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 9074509}
	_, err := livemembers.FindOneAndDelete(filter)
	require.NoError(err)

	body := hornMuteAddParam{
		UserID:       9074509,
		Duration:     1,
		DurationType: durationTypeDay,
		Reason:       "test",
		Confirm:      1,
	}
	c := handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, msg, err := ActionHornMuteAdd(c)
	require.NoError(err)
	assert.Equal("封禁成功", msg)
}

func TestNewHornMuteAddParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := hornMuteAddParam{}
	c := handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err := newHornMuteAddParam(c)
	assert.EqualError(err, "请输入用户 ID!")

	body.UserID = 9074511
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err = newHornMuteAddParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.DurationType = durationTypeDay
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err = newHornMuteAddParam(c)
	assert.EqualError(err, "封禁时长需大于 0")

	body.Duration = 1
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err = newHornMuteAddParam(c)
	assert.EqualError(err, "请填写封禁理由!")

	body.Reason = "test"
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err = newHornMuteAddParam(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)

	body.UserID = 9074509
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	_, err = newHornMuteAddParam(c)
	expected := actionerrors.ErrConfirmRequired("<p>确定要对该用户：封禁全站喇叭吗？</p>用户 ID: 9074509<br>用户昵称: <br>用户等级: 1", 1, true)
	assert.Equal(expected, err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/hornmute/add", true, body)
	param, err := newHornMuteAddParam(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestHornMuteAddParam_add(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 9074509}
	_, err := livemembers.FindOneAndDelete(filter)
	require.NoError(err)

	param := &hornMuteAddParam{
		Duration:     1,
		DurationType: durationTypeDay,
		operatorID:   9074511,
		user: &liveuser.User{
			UID:      9074509,
			Username: "test",
			IconURL:  "oss://test.jpg",
		},
	}
	require.NoError(param.add())

	mute, err := livemembers.FindHornMute(param.user.UID)
	require.NoError(err)
	require.NotNil(mute)
	now := goutil.TimeNow()
	assert.Equal(now.AddDate(0, 0, 1).Round(time.Minute), *mute.ExpireAt)

	param.DurationType = durationTypeHour
	require.NoError(param.add())
	mute, err = livemembers.FindHornMute(param.user.UID)
	require.NoError(err)
	require.NotNil(mute)
	assert.Equal(now.Add(time.Hour).Round(time.Minute), *mute.ExpireAt)

	param.DurationType = durationTypeMinute
	require.NoError(param.add())
	mute, err = livemembers.FindHornMute(param.user.UID)
	require.NoError(err)
	require.NotNil(mute)
	assert.Equal(now.Add(time.Minute).Round(time.Minute), *mute.ExpireAt)
}

func TestActionHornMuteRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 9074509}
	_, err := livemembers.FindOneAndDelete(filter)
	require.NoError(err)

	now := goutil.TimeNow()
	expireAt := now.Add(time.Minute)
	m := &livemembers.Member{
		Helper: livemembers.Helper{
			RoomID:      0,
			UserID:      9074509,
			Username:    "test",
			IconURL:     "oss://test.jpg",
			CreatedTime: now,
			ExpireAt:    &expireAt,
			Status:      livemembers.StatusHornMute,
			UpdatedTime: now,
			OperatorID:  9074511,
		},
	}
	err = m.SetMute()
	require.NoError(err)

	body := hornMuteRemove{
		UserID:  9074509,
		Confirm: 1,
	}
	c := handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	_, msg, err := ActionHornMuteRemove(c)
	require.NoError(err)
	assert.Equal("解封成功", msg)
}

func TestHornMuteAddParam_sendMuteSystemMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &hornMuteAddParam{
		Reason:        "test",
		expireTimeStr: "2025-01-07 23:43:00",
	}

	called := false
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]any)
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		assert.Equal("全站喇叭封禁惩罚通知", systemMsgList[0].Title)
		content := fmt.Sprintf("由于%s，您的全站喇叭功能已被封禁至 %s。在此期间，无法使用全站喇叭，请您遵守规范，谨慎发言！",
			html.EscapeString(param.Reason), param.expireTimeStr)
		assert.Equal(content, systemMsgList[0].Content)
		called = true
		return "success", nil
	})
	defer cancel()

	param.sendMuteSystemMessage()
	assert.True(called)
}

func TestHornMuteAddParam_sendMuteLog(t *testing.T) {
	assert := assert.New(t)

	var actual string
	cancel := mrpc.SetMock("go://util/addadminlog", func(input any) (any, error) {
		actual = (*input.(*[]adminlogger.AdminLog))[0].Intro
		return "success", nil
	})
	defer cancel()

	param := &hornMuteAddParam{
		UserID:        9074509,
		Reason:        "test",
		Duration:      1,
		DurationType:  durationTypeDay,
		expireTimeStr: "2025-01-07 23:43:00",
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	param.sendMuteLog(c)
	expected := fmt.Sprintf("【封禁全站喇叭】用户 ID: %d; 理由: %s; 封禁时长: %d 天; 解封时间: %s",
		param.UserID, param.Reason, param.Duration, param.expireTimeStr,
	)
	assert.Equal(expected, actual)

	param.DurationType = durationTypeHour
	c = handler.NewTestContext(http.MethodPost, "/", true, nil)
	param.sendMuteLog(c)
	expected = fmt.Sprintf("【封禁全站喇叭】用户 ID: %d; 理由: %s; 封禁时长: %d 小时; 解封时间: %s",
		param.UserID, param.Reason, param.Duration, param.expireTimeStr,
	)
	assert.Equal(expected, actual)

	param.DurationType = durationTypeMinute
	c = handler.NewTestContext(http.MethodPost, "/", true, nil)
	param.sendMuteLog(c)
	expected = fmt.Sprintf("【封禁全站喇叭】用户 ID: %d; 理由: %s; 封禁时长: %d 分钟; 解封时间: %s",
		param.UserID, param.Reason, param.Duration, param.expireTimeStr,
	)
	assert.Equal(expected, actual)
}

func TestNewHornMuteRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 9074509}
	_, err := livemembers.FindOneAndDelete(filter)
	require.NoError(err)

	body := hornMuteRemove{}
	c := handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	_, err = newHornMuteRemove(c)
	assert.EqualError(err, "请输入用户 ID!")

	body.UserID = 9074511
	c = handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	_, err = newHornMuteRemove(c)
	assert.Equal(actionerrors.ErrUserNotFound, err)

	body.UserID = 9074509
	c = handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	_, err = newHornMuteRemove(c)
	assert.EqualError(err, "该用户当前未被封禁！")

	now := goutil.TimeNow()
	expireAt := now.Add(time.Minute)
	m := &livemembers.Member{
		Helper: livemembers.Helper{
			RoomID:      0,
			UserID:      9074509,
			Username:    "test",
			IconURL:     "oss://test.jpg",
			CreatedTime: now,
			ExpireAt:    &expireAt,
			Status:      livemembers.StatusHornMute,
			UpdatedTime: now,
			OperatorID:  9074511,
		},
	}
	err = m.SetMute()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	_, err = newHornMuteRemove(c)
	msg := fmt.Sprintf("<p>确定要对该用户：解封全站喇叭吗？</p>用户 ID: 9074509<br>用户昵称: <br>用户等级: 1<br>封禁到期时间: %s", expireAt.Format(util.TimeFormatYMDHMS))
	expected := actionerrors.ErrConfirmRequired(msg, 1, true)
	assert.Equal(expected, err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/hornmute/remove", true, body)
	param, err := newHornMuteRemove(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestHornMuteRemove_sendMuteSystemMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]any)
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		assert.Equal("全站喇叭封禁解除", systemMsgList[0].Title)
		assert.Equal("现已恢复您的全站喇叭功能，请您遵守规范，谨慎发言！", systemMsgList[0].Content)
		called = true
		return "success", nil
	})
	defer cancel()

	param := &hornMuteRemove{
		UserID: 12345,
	}
	param.sendMuteSystemMessage()
	assert.True(called)
}

func TestHornMuteRemove_sendMuteLog(t *testing.T) {
	assert := assert.New(t)

	var actual string
	cancel := mrpc.SetMock("go://util/addadminlog", func(input any) (any, error) {
		actual = (*input.(*[]adminlogger.AdminLog))[0].Intro
		return "success", nil
	})
	defer cancel()

	now := goutil.TimeNow()
	param := hornMuteRemove{
		UserID:        9074509,
		expireTimeStr: now.Format(util.TimeFormatYMDHMS),
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	param.sendMuteLog(c)
	expected := fmt.Sprintf("【解封全站喇叭】用户 ID: %d; 原解封时间: %s",
		param.UserID, param.expireTimeStr)
	assert.Equal(expected, actual)
}

func TestHornMuteConfirmParam_buildMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := vip.MockVipList()
	defer cancel()

	key := keys.KeyNobleUserVips1.Format(9074509)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	now := goutil.TimeNow().Add(time.Minute).Unix()
	uv := map[int]*vip.UserVip{
		vip.TypeLiveHighness: {
			UserID:     9074509,
			Type:       vip.TypeLiveHighness,
			Level:      1,
			ExpireTime: now,
		},
	}
	b, err := json.Marshal(uv)
	require.NoError(err)
	err = service.Redis.Set(key, string(b), time.Second).Err()
	require.NoError(err)

	param := hornMuteConfirmParam{
		muteType: muteTypeAdd,
		user: &liveuser.User{
			UID:          9074509,
			Username:     "username",
			Contribution: 100,
		},
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	param.buildMessage(c)
	msg := "<p>确定要对该用户：封禁全站喇叭吗？</p>用户 ID: 9074509<br>用户昵称: username<br>用户等级: 2<br>贵族等级: 上神"
	assert.Equal(msg, param.buildMessage(c))

	param.muteType = muteTypeRemove
	param.expireTimeStr = "2025-01-09 17:10:00"
	msg = "<p>确定要对该用户：解封全站喇叭吗？</p>用户 ID: 9074509<br>用户昵称: username<br>用户等级: 2<br>贵族等级: 上神<br>封禁到期时间: 2025-01-09 17:10:00"
	assert.Equal(msg, param.buildMessage(c))
}
