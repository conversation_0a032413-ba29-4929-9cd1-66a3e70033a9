package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const existRoomID int64 = 22489473

func TestActionChannelProviderSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID, "provider": "notexists"})
	_, err := ActionChannelProviderSet(c)
	assert.EqualError(err, "不支持的推流提供商")

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID, "provider": room.ChannelProviderAliyun})
	_, err = ActionChannelProviderSet(c)
	require.NoError(err)

	// 直播间不存在
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": 9999999, "provider": room.ChannelProviderAliyun})
	_, err = ActionChannelProviderSet(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
}
