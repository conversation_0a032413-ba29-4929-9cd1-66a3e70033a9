package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestFinishReviewTagKey(t *testing.T) {
	assert := assert.New(t)
	param := finishReviewParam{Data: make(finishReviewData, 1)}
	assert.Empty(tutil.KeyExists(tutil.JSON, param, "Datas", "reason"))
	assert.Empty(tutil.KeyExists(tutil.JSON, param.Data[0], "user_id", "type", "upload_time"))
	resp := make(finishReviewResp, 1)
	assert.Empty(tutil.KeyExists(tutil.JSON, resp[0], "user_id", "status"))
}

func TestActionReviewPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 没有 Data
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/review/pass", true, nil)
	_, err := ActionReviewPass(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 没有 type
	param := &finishReviewParam{
		Data: finishReviewData{
			{UserID: 10, UploadTime: 1234567890},
			{UserID: 11, UploadTime: 1234567890},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/review/pass", true, param)
	_, err = ActionReviewPass(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 正常返回
	imageType := livereview.TypeCover
	param = &finishReviewParam{
		Data: finishReviewData{
			{UserID: 10, UploadTime: 1234567890, Type: &imageType},
			{UserID: 11, UploadTime: 1234567890, Type: &imageType},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/review/pass", true, param)
	r, err := ActionReviewPass(c)
	require.NoError(err)
	resp := r.(finishReviewResp)
	assert.Len(resp, 2)
	for i := 0; i < len(resp); i++ {
		assert.Equal(int64(i+10), resp[i].UserID)
		assert.Equal("outdated", resp[i].Status)
	}

	// 直播间名称已存在
	room, err := room.FindOne(bson.M{"room_id": 223344})
	require.NoError(err)
	require.NotNil(room)
	require.NoError(service.DB.Where("user_id = 12345 AND type = 2 AND upload_time = 1234567890 AND status = 0").
		FirstOrCreate(&livereview.LiveReview{
			UserID:     12345,
			Type:       2,
			RoomID:     12,
			Status:     0,
			UploadTime: 1234567890,
			ReviewInfo: livereview.ReviewInfo{Name: room.Name},
		}).Error)
	typeName := livereview.TypeName
	param = &finishReviewParam{
		Data: finishReviewData{
			{UserID: 12345, UploadTime: 1234567890, Type: &typeName},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/review/pass", true, param)
	_, err = ActionReviewPass(c)
	assert.EqualError(err, "直播间名称已存在")
}

func TestActionReviewRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.NoError(service.DB.Delete(livereview.LiveReview{}, "user_id = ?", 11).Error)

	messageCount := 0
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		messageCount = len(systemMsgList)

		return "success", nil
	})
	defer cancel()

	// 没有 Data, reason
	var param finishReviewParam
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/image/refuse", false, param)
	_, err := ActionReviewRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 没有 type
	param.Reason = "test reason"
	param.Data = make(finishReviewData, 2)
	for i := 0; i < 2; i++ {
		param.Data[i].UserID = int64(i + 10)
		param.Data[i].UploadTime = 1234567890
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/image/refuse", false, param)
	_, err = ActionReviewRefuse(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 正常返回
	imageType := livereview.TypeCover
	testLiveReview := livereview.LiveReview{
		UserID:     param.Data[1].UserID,
		Type:       imageType,
		Status:     livereview.StatusReviewing,
		UploadTime: param.Data[1].UploadTime,
	}
	require.NoError(service.DB.Create(&testLiveReview).Error)
	param.Data[0].Type = &imageType
	param.Data[1].Type = &imageType
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/image/refuse", false, param)
	r, err := ActionReviewRefuse(c)
	require.NoError(err)
	resp := r.(finishReviewResp)
	assert.Len(resp, 2)

	expectedResp := finishReviewResp{
		{UserID: param.Data[0].UserID, Status: statusOutdated},
		{UserID: param.Data[1].UserID, Status: statusSuccess},
	}
	assert.Equal(expectedResp, resp)
	assert.EqualValues(1, messageCount)
}

func TestRefuseSysMessage(t *testing.T) {
	assert := assert.New(t)

	recuid, title, content := refuseSysMessage(&livereview.LiveReview{
		UserID: 10,
		Type:   livereview.TypeBackground,
	}, "test")
	assert.Equal(int64(10), recuid)
	assert.Equal("您的直播背景图未通过审核", title)
	assert.Equal("亲爱的主播，非常抱歉，您的直播背景图未通过审核。拒绝原因：test。", content)
}

func TestAdminLog(t *testing.T) {
	assert := assert.New(t)

	after := livereview.LiveReview{ID: 123}
	after.Status = livereview.StatusPassed
	after.Type = livereview.TypeName
	after.Name = "test"
	after.RoomID = 147
	after.UserID = 258
	catalog, channelID, intro := adminLogContent(&after)
	assert.Equal(int64(123), channelID)
	assert.Equal(catalog, 86)
	assert.Equal("审核通过直播间名称，直播间 ID: 147, 主播 ID: 258, 直播间名称: test", intro)

	after.Status = livereview.StatusRefused
	catalog, _, intro = adminLogContent(&after)
	assert.Equal(catalog, 87)
	assert.Equal("审核拒绝直播间名称，直播间 ID: 147, 主播 ID: 258, 直播间名称: test", intro)
}
