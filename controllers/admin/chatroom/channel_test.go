package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionChannelSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("im://broadcast", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err := ActionChannelSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": 999999})
	_, err = ActionChannelSet(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	_, err = room.Update(existRoomID, bson.M{"status.channel.type": room.TypeOpen})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID})
	_, err = ActionChannelSet(c)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID})
	_, err = ActionChannelSet(c)
	assert.Equal(actionerrors.ErrRoomIsPushing, err)
}
