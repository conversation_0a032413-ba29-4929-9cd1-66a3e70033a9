package chatroom

import (
	"fmt"
	"net/http"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const (
	statusSuccess  = "success"
	statusOutdated = "outdated"
)

type finishReviewParam struct {
	Data   finishReviewData `json:"Datas"`
	Reason string           `json:"reason"`
}

type finishReviewData []struct {
	UserID     int64 `json:"user_id"`
	Type       *int  `json:"type"`
	UploadTime int64 `json:"upload_time"`
}

type finishReviewResp []struct {
	UserID int64  `json:"user_id"`
	Status string `json:"status"`
}

// ActionReviewPass 超管通过直播间信息审核
/**
 * @api {post} /api/v2/admin/chatroom/review/pass 超管通过直播间信息审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} type 状态，0：封面图，1：背景图，2: 房间名称
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} upload_time 上传时间
 * @apiParamExample {json} Request-Example:
 *   {
 *     "Datas": [{
 *       "type": 0,
 *       "user_id": 12,
 *       "upload_time": 1234567890
 *     }, {
 *       "type": 0,
 *       "user_id": 123,
 *       "upload_time": 1234567890
 *     }]
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [{
 *       "user_id": 12,
 *       "status": "success",
 *     }, {
 *       "user_id": 123,
 *       "status": "outdated",
 *     }]
 *   }
 *
 * @apiSuccessExample {json} WebSocket 超管通过直播间信息审核消息
 *   {
 *     "type": "room",
 *     "event": "update",
 *     "room_id": 65261414,
 *     "room": {
 *       "room_id": 65261414,
 *       "catalog_id": 115,
 *       "custom_tag_id": 10001,
 *       "name": "直播间名字",
 *       "announcement": "公告",
 *       "creator_id": 65261514,
 *       "type": "connect",
 *       "notice": "通知",
 *       "background": {
 *         "enable": true,
 *         "image_url": "http://static.maoercdn.com/background/icon01.png",
 *         "pendant_image_url": "http://static.maoercdn.com/background/icon01.png",
 *         "opacity": 1.0,
 *       },
 *       "cover_url": "https://static.maoercdn.com/profile/01.png"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionReviewPass(c *handler.Context) (handler.ActionResponse, error) {
	var param finishReviewParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Data) == 0 {
		return nil, actionerrors.ErrParams
	}

	adminLogs := userapi.NewAdminLogBox(c)
	resp := make(finishReviewResp, len(param.Data))
	for i := 0; i < len(param.Data); i++ {
		index := &param.Data[i]
		resp[i].UserID = index.UserID
		if index.Type == nil || *index.Type < livereview.TypeCover || *index.Type > livereview.TypeName {
			return nil, actionerrors.ErrParams
		}
		after, err := room.UpdateReview(index.UserID, index.UploadTime, *index.Type)
		if err != nil {
			if err == room.ErrRoomNameExists {
				return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "直播间名称已存在")
			}
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if after != nil {
			resp[i].Status = statusSuccess
			adminLogs.AddWithChannelID(adminLogContent(after))
		} else {
			resp[i].Status = statusOutdated
		}
	}
	// 管理员操作日志
	if err = adminLogs.Send(); err != nil {
		logger.Error(err)
		// pass
	}
	return resp, nil
}

// ActionReviewRefuse 超管拒绝直播间信息审核
/**
 * @api {post} /api/v2/admin/chatroom/review/refuse 超管拒绝直播间信息审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} type 状态，0：封面图，1：背景图，2: 房间名称
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} upload_time 上传时间
 * @apiParam {string} reason 拒绝原因
 * @apiParamExample {json} Request-Example:
 *   {
 *     "reason": "必填，上限 30 字",
 *     "Datas": [{
 *       "type": 0,
 *       "user_id": 12,
 *       "upload_time": 1234567890
 *     }, {
 *       "type": 0,
 *       "user_id": 123,
 *       "upload_time": 1234567890
 *     }]
 *   }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [{
 *       "user_id": 12,
 *       "status": "success",
 *     }, {
 *       "user_id": 123,
 *       "status": "outdated",
 *     }]
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionReviewRefuse(c *handler.Context) (handler.ActionResponse, error) {
	var param finishReviewParam
	err := c.BindJSON(&param)
	if err != nil || len(param.Data) == 0 || param.Reason == "" {
		return nil, actionerrors.ErrParams
	}

	adminLogs := userapi.NewAdminLogBox(c)
	box := userapi.NewSystemMsgBox()
	resp := make(finishReviewResp, len(param.Data))
	for i := 0; i < len(param.Data); i++ {
		index := &param.Data[i]
		resp[i].UserID = index.UserID
		if index.Type == nil || *index.Type < livereview.TypeCover || *index.Type > livereview.TypeName {
			return nil, actionerrors.ErrParams
		}
		after, err := livereview.FinishReviewing(index.UserID, index.UploadTime,
			*index.Type, livereview.StatusRefused, nil)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if after != nil {
			resp[i].Status = statusSuccess
			box.AddMessage(refuseSysMessage(after, param.Reason))
			adminLogs.AddWithChannelID(adminLogContent(after))
		} else {
			resp[i].Status = statusOutdated
		}
	}
	// TODO: 可以用 goroutine 加速
	if len(box.Messages) > 0 {
		if err = box.Send(); err != nil {
			logger.Error(err)
			// PASS
		}
	}

	// 管理员操作日志
	if err = adminLogs.Send(); err != nil {
		logger.Error(err)
		// pass
	}
	return resp, nil
}

func refuseSysMessage(after *livereview.LiveReview, reason string) (recuid int64, title string, content string) {
	recuid = after.UserID
	typeStr := livereview.TypeStr(after.Type)
	title = fmt.Sprintf("您的%s未通过审核", typeStr)
	content = fmt.Sprintf("亲爱的主播，非常抱歉，您的%s未通过审核。拒绝原因：%s。", typeStr, reason)
	return
}

func adminLogContent(after *livereview.LiveReview) (int, int64, string) {
	ks, kt, ki := after.LogKeywords()
	intro := fmt.Sprintf("审核%s%s，直播间 ID: %d, 主播 ID: %d, %s: %s", ks, kt, after.RoomID, after.UserID, kt, ki)
	// 操作日志
	var catalog int
	switch after.Status {
	case livereview.StatusPassed:
		catalog = userapi.CatalogPassRoomInfo
	case livereview.StatusRefused:
		catalog = userapi.CatalogRefuseRoomInfo
	}
	return catalog, after.ID, intro
}
