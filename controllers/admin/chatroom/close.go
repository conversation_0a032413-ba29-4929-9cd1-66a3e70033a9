package chatroom

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionClose 切断直播
/**
 * @api {post} /api/v2/admin/chatroom/close 切断直播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} reason 切断直播原因
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_id": 12,
 *     "reason": "原因",
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "切断成功"
 *   }
 *
 */
func ActionClose(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64  `json:"room_id"`
		Reason string `json:"reason"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || len(param.Reason) == 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	cs := room.NewCloseStatistics(r, room.OperatorStaff, c.UserID(), "管理员停止了直播！")
	recommend := chatroom.CloseNotifyRecommend(r, c.ClientIP())
	// 部分听众可能收不到主播关播消息（比如网络问题），管理员可以再次发送
	room.NewCloseNotifier(cs, recommend, c).Notify()
	// 清空提问列表和连麦列表，以防未处理的提问钻石未退回
	r.ClearList(c)
	if r.Status.Open == room.StatusOpenFalse {
		return "房间未开启，仅清空提问和连麦列表", nil
	}
	err = r.Close(cs, c.UserContext())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	goutil.Go(func() {
		err = livepk.AfterCloseRoom(r.RoomID)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
		err = livemulticonnect.AfterCloseRoom(r)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
		// 主播惩罚日志
		_, err = liveaddendum.Punish(r.CreatorID, liveaddendum.OperatorCut, 0, param.Reason)
		if err != nil {
			logger.WithFields(logger.Fields{"creator_id": r.CreatorID, "reason": param.Reason}).
				Errorf("log cut pushing error: %v", err)
			// PASS
		}
	})

	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	box.AddAdminLog(liveaddendum.AdminLogInfo(liveaddendum.OperatorCut, r.CreatorID))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return "切断成功", nil
}
