package chatroom

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 封禁时 S 级主播判断标准: 三百万钻石收益 or 30 * 24h 内一百万钻石收益
const (
	banConfirmRevenue        = 3000000
	banConfirmMonthlyRevenue = 1000000
)

type operationBan struct {
	c *handler.Context

	RoomID      int64
	DurationStr string
	StartTime   string
	Operation   int
	Reason      string
}

type banParams struct {
	RoomID   int64  `form:"room_id" json:"room_id"`
	Duration int64  `form:"duration" json:"duration"`
	Confirm  int    `form:"confirm" json:"confirm"`
	Type     int    `form:"type" json:"type"`
	Reason   string `form:"reason" json:"reason"`

	r           *room.Room
	durationStr string // 如：30 天，10 分钟
	startTime   int64  // 秒级时间戳
}

// ActionBan 封禁直播间
/**
 * @api {post} /api/v2/admin/chatroom/ban 封禁直播间
 * @apiDescription 封禁直播间，返回消息 code 为 100010020 时需弹窗确认, 并携带返回的 confirm 再次请求
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} [duration] 封禁时长, 单位:秒, 暂只支持按分钟或天封禁
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiParam {number=0,1} [type=0] 禁言时长类型 0: 永久封禁, 1: 按时间封禁
 * @apiParam {String} reason 封禁原因
 *
 * @apiSuccessExample 封禁成功:
 *   {
 *     "code": 0,
 *     "info": "封禁成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "主播 小蜜蜂（MID：12345）为 S 级主播，请先联系运营同学商讨封禁惩罚力度，再进行封禁操作。\
 *               若确定该主播需要进行封禁处罚，请点击“确定”完成封禁"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 *
 */
func ActionBan(c *handler.Context) (handler.ActionResponse, error) {
	var params banParams
	err := params.banLoad(c)
	if err != nil {
		return nil, err
	}

	err = params.checkConfirm()
	if err != nil {
		return nil, err
	}

	if params.r.Status.Open != room.StatusOpenFalse {
		// 切断直播
		cs := room.NewCloseStatistics(params.r, room.OperatorStaff, c.UserID(), "管理员停止了直播！")
		recommend := chatroom.CloseNotifyRecommend(params.r, c.ClientIP())
		room.NewCloseNotifier(cs, recommend, c).Notify()
		params.r.ClearList(c)
		err = params.r.Close(cs, c.UserContext())
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		goutil.Go(func() {
			err = livepk.AfterCloseRoom(params.r.RoomID)
			if err != nil {
				logger.WithField("room_id", params.r.RoomID).Error(err)
				// PASS
			}
			err = livemulticonnect.AfterCloseRoom(params.r)
			if err != nil {
				logger.WithField("room_id", params.r.RoomID).Error(err)
				// PASS
			}
		})
	}
	if err := livemeta.BanRoom(params.RoomID, params.Duration, params.startTime, params.Type); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 主播惩罚日志
	params.addBanPunishLog()
	// 记录管理员操作日志
	op := operationBan{
		c:           c,
		RoomID:      params.RoomID,
		DurationStr: params.durationStr,
		Operation:   userapi.CatalogBanRoom,
		Reason:      params.Reason,
		StartTime:   time.Unix(params.startTime, 0).Format(util.TimeFormatYMDHMS),
	}
	op.sendBanLog()
	return "封禁成功", nil
}

func (p *banParams) banLoad(c *handler.Context) (err error) {
	err = c.Bind(&p)
	if err != nil {
		return actionerrors.ErrParams
	}
	if p.RoomID <= 0 || p.Confirm < 0 {
		return actionerrors.ErrParams
	}
	if p.Type != livemeta.TypeBanForever && p.Duration <= 0 {
		return actionerrors.ErrParamsMsg("请输入封禁时长")
	}
	if p.Reason == "" {
		return actionerrors.ErrParamsMsg("请输入封禁理由")
	}
	if err = p.newDurationStr(); err != nil {
		return err
	}
	p.r, err = room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if p.r.IsBan() {
		return actionerrors.ErrParamsMsg("该直播间已在封禁中")
	}
	p.startTime = goutil.TimeNow().Unix()
	return
}

func (p *banParams) checkConfirm() error {
	if p.Confirm != 0 {
		return nil
	}
	message := fmt.Sprintf("主播 %s（MID：%d）为 S 级主播，请先联系运营同学商讨封禁惩罚力度，再进行封禁操作。"+
		"若确定该主播需要进行封禁处罚，请点击“确定”完成封禁", p.r.CreatorUsername, p.r.CreatorID)
	if p.r.Statistics.Revenue >= banConfirmRevenue {
		return actionerrors.ErrConfirmRequired(message, 1)
	}

	confirmMonthlyTotalRevenue, err := livelog.RoomTotalRevenue(p.r.OID, goutil.TimeNow().AddDate(0, 0, -30), goutil.TimeNow())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if confirmMonthlyTotalRevenue >= banConfirmMonthlyRevenue {
		return actionerrors.ErrConfirmRequired(message, 1)
	}

	return nil
}

func (p *banParams) newDurationStr() (err error) {
	switch p.Type {
	case livemeta.TypeBanForever:
		p.durationStr = "永久封禁"
	case livemeta.TypeBanDuration:
		if p.Duration%util.SecondOneDay == 0 {
			p.durationStr = fmt.Sprintf("%d 天", p.Duration/livemeta.OneDay)
		} else if p.Duration%util.SecondOneMinute == 0 {
			p.durationStr = fmt.Sprintf("%d 分钟", p.Duration/livemeta.OneMinute)
		} else {
			return actionerrors.ErrParamsMsg("请输入正确的封禁时长")
		}
	default:
		return actionerrors.ErrParams
	}
	return
}

func (p *banParams) addBanPunishLog() {
	_, err := liveaddendum.BanPunish(p.r.CreatorID, p.Reason, p.durationStr)
	if err != nil {
		logger.WithFields(logger.Fields{"creator_id": p.r.CreatorID, "reason": p.Reason}).
			Errorf("log cut pushing error: %v", err)
		// PASS
	}
}

func (op *operationBan) sendBanLog() {
	var intro string
	switch op.Operation {
	case userapi.CatalogBanRoom:
		intro = fmt.Sprintf("封禁直播间, 直播间 ID: %d, 原因: %s, 封禁开始时间: %s, 封禁时长: %s",
			op.RoomID, op.Reason, op.StartTime, op.DurationStr)
	case userapi.CatalogUpdateBanRoom:
		intro = fmt.Sprintf("修改直播间封禁时长, 直播间 ID: %d, 封禁开始时间: %s, 封禁时长: %s",
			op.RoomID, op.StartTime, op.DurationStr)
	case userapi.CatalogUnBanRoom:
		intro = fmt.Sprintf("解封直播间, 直播间 ID: %d", op.RoomID)
	default:
		panic(fmt.Sprintf("invalid operator: %d", op.Operation))
	}
	adminLogs := userapi.NewAdminLogBox(op.c)
	adminLogs.AddAdminLog(intro, op.Operation)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionUpdateBan 修改直播间封禁时长
/**
 * @api {post} /api/v2/admin/chatroom/updateban 修改直播间封禁时长
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} [duration] 封禁时长, 单位:秒, 暂只支持按分钟或天封禁
 * @apiParam {number=0,1} type 禁言时长类型 0: 永久封禁, 1: 按时间封禁
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionUpdateBan(c *handler.Context) (handler.ActionResponse, error) {
	var params banParams
	if err := params.updateBanLoad(c); err != nil {
		return nil, err
	}
	if err := livemeta.BanRoom(params.RoomID, params.Duration, params.startTime, params.Type); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 记录管理员操作日志
	op := operationBan{
		c:           c,
		DurationStr: params.durationStr,
		Operation:   userapi.CatalogUpdateBanRoom,
		RoomID:      params.RoomID,
		StartTime:   time.Unix(params.startTime, 0).Format(util.TimeFormatYMDHMS),
	}
	op.sendBanLog()
	return "设置成功", nil
}

func (p *banParams) updateBanLoad(c *handler.Context) (err error) {
	err = c.Bind(&p)
	if err != nil || p.RoomID <= 0 {
		return actionerrors.ErrParams
	}
	if p.Type != livemeta.TypeBanForever && p.Duration <= 0 {
		return actionerrors.ErrParamsMsg("请输入封禁时长")
	}
	if err = p.newDurationStr(); err != nil {
		return err
	}

	p.r, err = room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	ban, err := livemeta.FindBanned(p.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if ban == nil {
		return actionerrors.ErrParamsMsg("当前直播间未被封禁")
	}
	if p.Type > livemeta.TypeBanForever {
		if ban.StartTime+p.Duration < goutil.TimeNow().Unix() {
			return actionerrors.ErrParamsMsg("解封时间不可早于当前时间")
		}
	}
	p.startTime = ban.StartTime
	return
}

// ActionUnban 直播间解封
/**
 * @api {post} /api/v2/admin/chatroom/unban 直播间解封
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "解封成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionUnban(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	err := c.Bind(&params)
	if err != nil || params.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(params.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	ban, err := livemeta.FindBanned(params.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if ban == nil {
		return nil, actionerrors.ErrParamsMsg("当前直播间未被封禁")
	}

	if err = livemeta.Unban(params.RoomID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// REVIEW: 清理缓存
	// 发送用户系统通知
	sendBanSystemMessage(r.CreatorID)
	// 记录管理员操作日志
	op := operationBan{
		c:         c,
		Operation: userapi.CatalogUnBanRoom,
		RoomID:    r.RoomID,
	}
	op.sendBanLog()
	return "解封成功", nil
}

func sendBanSystemMessage(creatorID int64) {
	title := "直播间解封通知"
	content := "亲爱的主播，您的直播间现已解封。请遵守《猫耳FM主播直播规范》进行直播，维护绿色健康直播氛围~"
	err := messageassign.SystemMessageAssign(creatorID, title, content)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
