package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionExtraScoreSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs: "112233",
		Score:   -1,
		Confirm: 1,
	})
	_, err := ActionExtraScoreSet(c)
	assert.Equal(actionerrors.ErrParamsMsg("请输入正确的热度值"), err)

	c = handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs: "112233",
		Score:   maxExtraScore + 1,
		Confirm: 1,
	})
	_, err = ActionExtraScoreSet(c)
	assert.Equal(actionerrors.ErrParamsMsg(fmt.Sprintf("请输入小于等于 %d 的热度值", maxExtraScore)), err)

	now := goutil.TimeNow()
	c = handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs:   "112233",
		StartTime: now.Add(3 * time.Minute).Unix(),
		EndTime:   now.Add(4 * time.Minute).Unix(),
		Score:     24,
		Confirm:   1,
	})
	_, err = ActionExtraScoreSet(c)
	assert.Equal(actionerrors.ErrParamsMsg("有直播间不存在，请检查后重新输入"), err)

	c = handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs:   "4381915",
		StartTime: -1,
		EndTime:   now.Add(4 * time.Minute).Unix(),
		Score:     24,
		Confirm:   1,
	})
	_, err = ActionExtraScoreSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs:   "4381915",
		StartTime: now.Add(-3 * time.Minute).Unix(),
		EndTime:   now.Add(4 * time.Minute).Unix(),
		Score:     24,
		Confirm:   1,
	})
	_, err = ActionExtraScoreSet(c)
	assert.EqualError(err, "开始时间需晚于当前时间，请选择正确的时间")

	c = handler.NewTestContext(http.MethodPost, "/", true, scoreSetParams{
		RoomIDs:   "4381915",
		StartTime: now.Add(7 * time.Minute).Unix(),
		EndTime:   now.Add(4 * time.Minute).Unix(),
		Score:     24,
		Confirm:   1,
	})
	_, err = ActionExtraScoreSet(c)
	assert.EqualError(err, "结束时间需晚于开始时间，请选择正确的时间")

	// 设置直播间热度，需要弹窗提示
	rooms, err := room.ListSimples(bson.M{"status.open": 1, "limit": bson.M{"$exists": false}},
		options.Find().SetLimit(2).SetSort(bson.M{"room_id": -1}), &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.Len(rooms, 2)
	param := &scoreSetParams{
		RoomIDs:   fmt.Sprintf("%d,%d", rooms[0].RoomID, rooms[1].RoomID),
		StartTime: now.Add(1 * time.Minute).Unix(),
		EndTime:   now.Add(4 * time.Minute).Unix(),
		Score:     24,
		Confirm:   0,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionExtraScoreSet(c)
	require.Error(err)
	startTime := time.Unix(param.StartTime, 0)
	endTime := time.Unix(param.EndTime, 0)
	message := fmt.Sprintf(
		`<h2 align="center">设置热度确认</h2><p>房间号：%d, %d<br>主播昵称：%s, %s<br>热度值：%d<br>开始时间：%s<br>结束时间：%s<br>持续时间：%d min</p>`,
		rooms[0].RoomID, rooms[1].RoomID, rooms[0].CreatorUsername, rooms[1].CreatorUsername,
		param.Score, startTime.Format(goutil.TimeFormatHMS), endTime.Format(goutil.TimeFormatHMS), int64(endTime.Sub(startTime).Minutes()))
	assert.EqualError(err, message)

	// 不需要弹窗提示
	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	msg, err := ActionExtraScoreSet(c)
	require.NoError(err)
	assert.Equal("配置成功", msg)

	// 直播间热度清零
	// 需要弹窗提示
	param.Confirm = 0
	param.Score = 0
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionExtraScoreSet(c)
	require.NotNil(err)
	message = `<h2 align="center">设置热度确认</h2><p>房间号：%d, %d<br>主播昵称：%s, %s<br>热度值：%d</p>`
	message = fmt.Sprintf(message, rooms[0].RoomID, rooms[1].RoomID, rooms[0].CreatorUsername,
		rooms[1].CreatorUsername, param.Score)
	assert.EqualError(err, message)

	// 不需要弹窗提示
	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	msg, err = ActionExtraScoreSet(c)
	require.NoError(err)
	assert.Equal("配置成功", msg)

	// 受限房间
	param.RoomIDs = fmt.Sprintf("%d,%d", room.TestLimitedRoomID, rooms[0].RoomID)
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionExtraScoreSet(c)
	assert.EqualError(err, "有直播间不存在，请检查后重新输入")
}
