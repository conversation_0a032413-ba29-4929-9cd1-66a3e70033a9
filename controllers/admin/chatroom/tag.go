package chatroom

import (
	"fmt"
	"html"
	"strings"
	"unicode/utf8"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	rooms "github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type tagParams struct {
	ID          int64            `form:"tag_id" json:"tag_id"`
	TagName     string           `form:"tag_name" json:"tag_name"`
	IconURL     upload.SourceURL `form:"icon_url" json:"icon_url"`
	DarkIconURL upload.SourceURL `form:"dark_icon_url" json:"dark_icon_url"`
	WebIconURL  upload.SourceURL `form:"web_icon_url" json:"web_icon_url"`

	c *handler.Context
}

type fileType struct {
	upload.SourceURL
	target string
}

// ActionTagSet 添加或修改标签
/**
 * @api {post} /api/v2/admin/chatroom/tag/set 添加或修改标签
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} [tag_id] 标签 ID 不传为添加，传为修改
 * @apiParam {String} [tag_name] tag 名称
 * @apiParam {String} [icon_url] App 白天图标
 * @apiParam {String} [dark_icon_url] App 夜晚图标
 * @apiParam {String} [web_icon_url] Web 图标
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "添加标签成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionTagSet(c *handler.Context) (handler.ActionResponse, error) {
	var params tagParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	params.c = c
	paramlist := []string{params.TagName, string(params.IconURL), string(params.DarkIconURL), string(params.WebIconURL)}
	switch {
	case params.ID == 0:
		for _, v := range paramlist {
			if v == "" {
				return nil, actionerrors.ErrParams
			}
		}

		// check TagName
		err := checkTagName(params.TagName, params.ID)
		if err != nil {
			return nil, err
		}

		err = params.addTag()
		if err != nil {
			return nil, err
		}
		return "添加标签成功", nil
	case params.ID > 0:
		var ok bool
		for _, v := range paramlist {
			if v != "" {
				ok = true
				break
			}
		}
		if !ok {
			return nil, actionerrors.ErrParams
		}

		if params.TagName != "" {
			err := checkTagName(params.TagName, params.ID)
			if err != nil {
				return nil, err
			}
		}

		err = params.editTag()
		if err != nil {
			return nil, err
		}
		utils.DelTabsCache()
		return "修改标签成功", nil
	}
	return nil, actionerrors.ErrParams
}

func (params *tagParams) addTag() error {
	nextID, err := tag.FindNextTagID(0, tag.TypeLiveTag)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	tag := tag.Tag{
		ID:        nextID,
		TagName:   params.TagName,
		Type:      tag.TypeLiveTag,
		Status:    tag.StatusHide,
		SortOrder: 0,
	}
	err = servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		err := tx.Create(&tag).Error
		if err != nil {
			return err
		}
		err = params.uploadFile(&tag)
		if err != nil {
			return err
		}
		err = tx.Model(&tag).Updates(map[string]string{
			"icon":      tag.IconURL,
			"dark_icon": tag.DarkIconURL,
			"web_icon":  tag.WebIconURL,
		}).Error
		return err
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	logbox := goclient.NewAdminLogBox(params.c)
	intro := fmt.Sprintf("新增标签 %s，标签 ID: %d", tag.TagName, tag.ID)
	logbox.Add(userapi.CatalogManageTag, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func (params *tagParams) editTag() error {
	checkTag, err := tag.FindTagByID(params.ID, tag.TypeLiveTag)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if checkTag == nil {
		return actionerrors.ErrParamsMsg("标签不存在")
	}

	tag := tag.Tag{
		ID:      params.ID,
		TagName: params.TagName,
	}
	err = params.uploadFile(&tag)
	if err != nil {
		return err
	}
	err = service.LiveDB.Model(&tag).Updates(&tag).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	logbox := goclient.NewAdminLogBox(params.c)
	changeList := make([]string, 0, 3)
	if tag.IconURL != "" {
		changeList = append(changeList, "白天图标")
	}
	if tag.DarkIconURL != "" {
		changeList = append(changeList, "夜间图标")
	}
	if tag.WebIconURL != "" {
		changeList = append(changeList, "Web 图标")
	}
	intro := fmt.Sprintf("修改标签：%s (%d)", tag.TagName, tag.ID)
	if params.TagName != "" && params.TagName != checkTag.TagName {
		intro += fmt.Sprintf("，标签名修改为 %s", params.TagName)
	}
	if len(changeList) > 0 {
		intro += "，修改了图标 " + strings.Join(changeList, "、")
	}
	logbox.Add(userapi.CatalogManageTag, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func (params *tagParams) uploadFile(tag *tag.Tag) error {
	list := make([]fileType, 0, 3)
	if params.IconURL != "" {
		var target = paramToURL(tag.ID, params.IconURL.Ext())
		list = append(list, fileType{SourceURL: params.IconURL, target: target})
		tag.IconURL = target
	}
	if params.DarkIconURL != "" {
		var target = paramToURL(tag.ID, "-dark"+params.DarkIconURL.Ext())
		list = append(list, fileType{SourceURL: params.DarkIconURL, target: target})
		tag.DarkIconURL = target
	}
	if params.WebIconURL != "" {
		var target = paramToURL(tag.ID, "-web"+params.WebIconURL.Ext())
		list = append(list, fileType{SourceURL: params.WebIconURL, target: target})
		tag.WebIconURL = target
	}
	for _, f := range list {
		err := storage.UploadToTarget(f.SourceURL, f.target)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

func checkTagName(tagName string, id int64) error {
	// tagName 可以有空格，计算长度的时候忽略空格
	str := strings.ReplaceAll(tagName, " ", "")
	length := utf8.RuneCountInString(str)
	if length == 0 {
		return actionerrors.ErrParamsMsg("标签名不可以为空")
	}
	if length > 5 {
		return actionerrors.ErrParamsMsg("标签名最多输入 5 个字")
	}

	db := service.LiveDB.Table(tag.TableName()).Where("type = ? AND tag_name = ? AND id <> ?", tag.TypeLiveTag, tagName, id)
	exists, err := servicedb.Exists(db)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return actionerrors.ErrParamsMsg("有重复的标签名")
	}

	return nil
}

// paramToURL 生成 storage 路径，suffix 含有类别和后缀，例如 -web.png
func paramToURL(tagID int64, suffix string) string {
	return fmt.Sprintf("%s://%s%03d_%s%s",
		config.DefaultCDNScheme,
		storage.PathPrefixTabIcon,
		tagID,
		goutil.TimeNow().Format(util.TimeFormatYMDHHMMSSWithNoSpace),
		suffix,
	)
}

type tagChangeStatusParams struct {
	TagID   int64 `form:"tag_id" json:"tag_id"`
	Status  *int  `form:"status" json:"status"`
	Confirm int   `form:"confirm" json:"confirm"`
}

// ActionTagEditStatus 更改 tag 状态
/**
 * @api {post} /api/v2/admin/chatroom/tag/editstatus 更改 tag 状态
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} tag_id 标签 ID
 * @apiParam {number=0,1} status tag 状态 0 下线 1 上线
 * @apiParam {number=0,1} confirm 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "上线成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 是否上线“test3”标签？<br><br>总房间数：1<br>目前开播数：1
 */
func ActionTagEditStatus(c *handler.Context) (handler.ActionResponse, error) {
	var param tagChangeStatusParams
	err := c.Bind(&param)
	if err != nil || param.Status == nil {
		return nil, actionerrors.ErrParams
	}
	var typeStr string
	switch *param.Status {
	case tag.StatusShow:
		typeStr = "上线"
	case tag.StatusHide:
		typeStr = "下线"
	default:
		return nil, actionerrors.ErrParams
	}
	t, err := tag.FindTagByID(param.TagID, tag.TypeLiveTag)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if t == nil {
		return nil, actionerrors.ErrParamsMsg("标签不存在")
	}
	switch param.Confirm {
	case 0:
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		match := bson.M{"$match": bson.M{"tag_ids": param.TagID}}
		group := bson.M{"$group": bson.M{
			"_id":   "$status.open",
			"count": bson.M{"$sum": 1},
		}}
		cur, err := rooms.Collection().Aggregate(ctx, bson.A{match, group})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		defer cur.Close(ctx)
		type statusCount struct {
			Status int `bson:"_id"`
			Count  int `bson:"count"`
		}
		var r []statusCount
		err = cur.All(ctx, &r)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		roomCount := 0
		openCount := 0
		for _, v := range r {
			if v.Status == rooms.StatusOpenTrue {
				roomCount += v.Count
				openCount += v.Count
			}
			if v.Status == rooms.StatusOpenFalse {
				roomCount += v.Count
			}
		}
		message := fmt.Sprintf("是否%s“%s”标签？<br><br>总房间数：%d<br>目前开播数：%d", typeStr, html.EscapeString(t.TagName), roomCount, openCount)
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	case 1:
		err = service.LiveDB.Model(&t).Update("status", *param.Status).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		tag.ClearAllShowTagsCache()
		logbox := goclient.NewAdminLogBox(c)
		intro := fmt.Sprintf("%s标签：%s，标签 ID: %d", typeStr, t.TagName, t.ID)
		logbox.Add(userapi.CatalogManageTag, intro)
		err = logbox.Send()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		utils.DelTabsCache()
		return typeStr + "成功", nil
	default:
		return nil, actionerrors.ErrParams
	}
}

type tagSortOrder struct {
	ID        int64  `form:"id" json:"id"`
	SortOrder int    `form:"sort_order" json:"sort_order"`
	Type      string `form:"type" json:"type"`
}

// ActionTagSortOrder 更改标签排序
/**
 * @api {post} /api/v2/admin/chatroom/tag/sortorder 更改标签排序
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id 标签 ID
 * @apiParam {Number} sort_order 排序
 * @apiParam {string="catalog","tag"} type 标签种类
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "调整标签排序成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 */
func ActionTagSortOrder(c *handler.Context) (handler.ActionResponse, error) {
	var param tagSortOrder
	err := c.Bind(&param)
	if err != nil || param.ID == 0 {
		return nil, actionerrors.ErrParams
	}
	switch param.Type {
	case "catalog":
		if !catalog.LiveCatalogExists(param.ID) {
			return nil, actionerrors.ErrParamsMsg("分区不存在")
		}
		err = service.DB.Table(catalog.TableName()).Where("id = ?", param.ID).Update("sort_order", param.SortOrder).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	case "tag":
		t, err := tag.FindTagByID(param.ID, tag.TypeLiveTag)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if t == nil {
			return nil, actionerrors.ErrParamsMsg("标签不存在")
		}
		err = service.LiveDB.Model(t).Update("sort_order", param.SortOrder).Error
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	default:
		return nil, actionerrors.ErrParams
	}
	logbox := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("更新 %s 排序 ID: %d，排序: %d", param.Type, param.ID, param.SortOrder)
	logbox.Add(userapi.CatalogManageTag, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	utils.DelTabsCache()
	catalog.DelLiveCatalogCache(false)
	return "调整标签排序成功", nil
}
