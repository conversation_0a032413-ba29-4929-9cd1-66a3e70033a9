package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionPlaybackPrioritySet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{
		"priority": 1,
	}

	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionPlaybackPrioritySet(c)
	assert.Equal(actionerrors.ErrParams, err)

	param["room_id"] = existRoomID
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionPlaybackPrioritySet(c)
	require.NoError(err)
	assert.Equal(1, livemeta.FindPlaybackPriority(existRoomID))

	param["priority"] = 0
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionPlaybackPrioritySet(c)
	require.NoError(err)
	assert.Zero(livemeta.FindPlaybackPriority(existRoomID))
}
