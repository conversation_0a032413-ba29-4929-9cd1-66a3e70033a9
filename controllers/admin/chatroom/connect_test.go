package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionConnectProviderSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	_, err := ActionConnectProviderSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": 999999})
	_, err = ActionConnectProviderSet(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	key := keys.KeyRoomsEnableAgora0.Format()
	require.NoError(service.Redis.Del(key).Err())

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID, "provider": "agora"})
	_, err = ActionConnectProviderSet(c)
	require.NoError(err)
	exists, err := service.Redis.SIsMember(key, existRoomID).Result()
	require.NoError(err)
	assert.True(exists)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_id": existRoomID, "agora_enable": 0})
	_, err = ActionConnectProviderSet(c)
	require.NoError(err)
	exists, err = service.Redis.SIsMember(key, existRoomID).Result()
	require.NoError(err)
	assert.False(exists)
}
