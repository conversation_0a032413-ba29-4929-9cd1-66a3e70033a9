package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionChannelProviderSet 指定直播间设置推流提供商
/**
 * @api {post} /api/v2/admin/chatroom/channel/provider/set 指定直播间设置推流提供商
 * @apiDescription 指定直播间设置推流提供商，设置完毕主播重新推流即可生效
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {string="aliyun","ksyun","bvc"} provider 推流提供商
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 */
func ActionChannelProviderSet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID   int64  `form:"room_id" json:"room_id"`
		Provider string `form:"provider" json:"provider"`
	}

	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	if param.Provider != room.ChannelProviderAliyun && param.Provider != room.ChannelProviderKsyun &&
		param.Provider != room.ChannelProviderBvc {
		return nil, actionerrors.ErrParamsMsg("不支持的推流提供商")
	}
	exists, err := room.Exists(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}

	update := bson.M{"channel.setprovider": param.Provider, "updated_time": goutil.TimeNow()}
	_, err = room.Update(param.RoomID, update)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	room.ClearRoomCache(param.RoomID)
	intro := fmt.Sprintf("设置直播推流提供商, 直播间 ID: %d, 提供商: %s", param.RoomID, param.Provider)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogLiveChannelProviderSet)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}
