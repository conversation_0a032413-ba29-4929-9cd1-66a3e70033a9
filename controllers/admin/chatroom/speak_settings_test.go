package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestSpeakMangeParamTags(t *testing.T) {
	tags := []string{"room_id", "confirm", "all", "live_level", "medal_level", "super_fan"}

	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(speakManageParam{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(speakManageParam{}, tags...)
}

func TestNewSpeakSetParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/speak/set", true, handler.M{
		"room_id": "a",
	})
	_, err := newSpeakSetParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/speak/set", true, handler.M{
		"room_id": -1,
	})
	_, err = newSpeakSetParam(c)
	assert.EqualError(err, "请输入有效的房间号")

	c = handler.NewTestContext(http.MethodPost, "/speak/set", true, handler.M{
		"room_id": room.TestLimitedRoomID,
	})
	_, err = newSpeakSetParam(c)
	assert.EqualError(err, "发言权限设置无效")

	r, err := room.FindOne(bson.M{"medal": nil})
	require.NoError(err)
	require.NotNil(r)
	c = handler.NewTestContext(http.MethodPost, "/speak/set", true, handler.M{
		"room_id":     r.RoomID,
		"medal_level": 1,
	})
	_, err = newSpeakSetParam(c)
	assert.EqualError(err, "该房间未开通粉丝勋章，无法设置粉丝等级限制")

	r, err = room.FindOne(bson.M{"medal": bson.M{"$exists": true}})
	require.NoError(err)
	require.NotNil(r)
	c = handler.NewTestContext(http.MethodPost, "/speak/set", true, handler.M{
		"room_id":     r.RoomID,
		"medal_level": 1,
		"super_fan":   1,
	})
	param, err := newSpeakSetParam(c)
	require.NoError(err)
	require.NotNil(param.ss)
	assert.Equal(&livemeta.MedalInfo{Level: 1, SuperFan: true}, param.ss.Medal)
}

func TestNewSpeakDelParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/speak/del", true, handler.M{
		"room_id": "a",
	})
	_, err := newSpeakDelParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/speak/del", true, handler.M{
		"room_id": -1,
	})
	_, err = newSpeakDelParam(c)
	assert.EqualError(err, "请输入有效的房间号")

	require.NoError(livemeta.SetSpeakSettings(room.TestLimitedRoomID, nil))
	c = handler.NewTestContext(http.MethodPost, "/speak/del", true, handler.M{
		"room_id": room.TestLimitedRoomID,
	})
	_, err = newSpeakDelParam(c)
	assert.EqualError(err, "该直播间未设置发言权限，无法取消")

	ss := &livemeta.SpeakSettings{Enable: true, All: true}
	require.NoError(livemeta.SetSpeakSettings(room.TestLimitedRoomID, ss))
	c = handler.NewTestContext(http.MethodPost, "/speak/del", true, handler.M{
		"room_id": room.TestLimitedRoomID,
	})
	param, err := newSpeakDelParam(c)
	require.NoError(err)
	assert.Equal(ss, param.ss)
}

func TestSpeakManageParam_CheckConfirm(t *testing.T) {
	assert := assert.New(t)

	param := speakManageParam{
		Confirm: 1,
	}
	assert.NoError(param.CheckConfirm())
	param = speakManageParam{
		Confirm: 0,
		room:    new(room.Room),
		ss:      &livemeta.SpeakSettings{Enable: true, All: true},
	}
	param.room.RoomID = 123
	param.room.CreatorUsername = "<>"
	assert.EqualError(param.CheckConfirm(), "房间号：123<br>"+
		"主播：&lt;&gt;<br>"+
		"全部禁言：开")

	param.ss.All = false
	assert.EqualError(param.CheckConfirm(), "房间号：123<br>"+
		"主播：&lt;&gt;<br>"+
		"全部禁言：关<br>"+
		"直播等级限制：无<br>"+
		"粉丝等级限制：无")

	param.ss.LiveLevel = 1
	param.ss.Medal = &livemeta.MedalInfo{Level: 1, SuperFan: true}
	assert.EqualError(param.CheckConfirm(), "房间号：123<br>"+
		"主播：&lt;&gt;<br>"+
		"全部禁言：关<br>"+
		"直播等级限制：1 级及以上<br>"+
		"粉丝等级限制：1 级及以上且为超粉")
}

func TestSpeakManageParam_SetAndDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.Find(room.TestLimitedRoomID)
	require.NoError(err)
	require.NotNil(r)
	param := speakManageParam{
		room: r,
		ss:   &livemeta.SpeakSettings{Enable: true, All: true},
	}

	require.NoError(param.Set())
	ss, err := livemeta.FindSpeakSettings(room.TestLimitedRoomID)
	require.NoError(err)
	assert.Equal(param.ss, ss)

	require.NoError(param.Del())
	ss, err = livemeta.FindSpeakSettings(room.TestLimitedRoomID)
	require.NoError(err)
	assert.Nil(ss)
}

func TestTestSpeakManageParam_Log(t *testing.T) {
	assert := assert.New(t)

	param := speakManageParam{
		room: &room.Room{Helper: room.Helper{RoomID: 123}},
		ss:   &livemeta.SpeakSettings{Enable: true, All: true},
		c:    handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	assert.Equal("设置发言权限：123，【全部禁言】开", param.Log("设置发言权限"))

	param.ss.All = false
	assert.Equal("设置发言权限：123，"+
		"【全部禁言】关，"+
		"【直播等级限制】无，"+
		"【粉丝等级限制】无", param.Log("设置发言权限"))

	param.ss.LiveLevel = 1
	param.ss.Medal = &livemeta.MedalInfo{Level: 1, SuperFan: true}
	assert.Equal("设置发言权限：123，"+
		"【全部禁言】关，"+
		"【直播等级限制】1 级及以上，"+
		"【粉丝等级限制】1 级及以上且为超粉", param.Log("设置发言权限"))
}

func TestActionSpeakSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{"room_id": "1"})
	_, err := ActionSpeakSet(c)
	assert.Equal(actionerrors.ErrParams, err)

	r, err := room.Find(room.TestLimitedRoomID)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{
			"room_id": room.TestLimitedRoomID,
			"all":     1,
		})
	_, err = ActionSpeakSet(c)
	assert.EqualError(err, fmt.Sprintf("房间号：%d<br>主播：%s<br>",
		r.RoomID, r.CreatorUsername)+"全部禁言：开")

	c = handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{
			"room_id": room.TestLimitedRoomID,
			"all":     1,
			"confirm": 1,
		})
	resp, err := ActionSpeakSet(c)
	require.NoError(err)
	assert.Equal("设置成功", resp)
}

func TestActionSpeakDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{"room_id": "1"})
	_, err := ActionSpeakDel(c)
	assert.Equal(actionerrors.ErrParams, err)

	require.NoError(livemeta.SetSpeakSettings(room.TestLimitedRoomID,
		&livemeta.SpeakSettings{Enable: true, All: true}))
	r, err := room.Find(room.TestLimitedRoomID)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{"room_id": room.TestLimitedRoomID})
	_, err = ActionSpeakDel(c)
	assert.EqualError(err, fmt.Sprintf("房间号：%d<br>主播：%s<br>",
		r.RoomID, r.CreatorUsername)+"全部禁言：开")

	c = handler.NewTestContext(http.MethodPost, "/speak/set", true,
		handler.M{
			"room_id": room.TestLimitedRoomID,
			"confirm": 1,
		})
	resp, err := ActionSpeakDel(c)
	require.NoError(err)
	assert.Equal("删除成功", resp)
}
