package chatroom

import (
	"fmt"
	"html"
	"slices"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	durationTypeDay = iota + 1
	durationTypeHour
	durationTypeMinute
)

type hornMuteAddParam struct {
	UserID       int64  `form:"user_id" json:"user_id"`
	Duration     int    `form:"duration" json:"duration"`
	DurationType int    `form:"duration_type" json:"duration_type"`
	Reason       string `form:"reason" json:"reason"`
	Confirm      int    `form:"confirm" json:"confirm"`

	operatorID int64

	user          *liveuser.User
	expireTimeStr string
}

// ActionHornMuteAdd 全站喇叭封禁
/**
 * @api {post} /api/v2/admin/chatroom/hornmute/add 全站喇叭封禁
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} duration 禁言时长
 * @apiParam {number=1,2,3} duration_type 1: 天; 2: 小时; 3: 分钟
 * @apiParam {String} reason 封禁理由
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "封禁成功"
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "<p>确定要对该用户：封禁全站喇叭吗？</p>用户 ID: 12345<br>用户昵称: test<br>用户等级: 120<br>贵族等级: 神话"
 *     }
 *   }
 */
func ActionHornMuteAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newHornMuteAddParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.add()
	if err != nil {
		return nil, "", err
	}

	// 发送用户系统通知
	param.sendMuteSystemMessage()
	// 记录管理员操作日志
	param.sendMuteLog(c)
	return nil, "封禁成功", nil
}

func newHornMuteAddParam(c *handler.Context) (*hornMuteAddParam, error) {
	param := new(hornMuteAddParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 {
		return nil, actionerrors.ErrParamsMsg("请输入用户 ID!")
	}
	if !slices.Contains([]int{durationTypeDay, durationTypeHour, durationTypeMinute}, param.DurationType) {
		return nil, actionerrors.ErrParams
	}
	if param.Duration <= 0 {
		return nil, actionerrors.ErrParamsMsg("封禁时长需大于 0")
	}

	param.Reason = strings.TrimSpace(param.Reason)
	if param.Reason == "" {
		return nil, actionerrors.ErrParamsMsg("请填写封禁理由!")
	}

	param.user, err = liveuser.Find(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrUserNotFound
	}
	if param.Confirm == 0 {
		cp := hornMuteConfirmParam{
			muteType: muteTypeAdd,
			user:     param.user,
		}
		return nil, actionerrors.ErrConfirmRequired(cp.buildMessage(c), 1, true)
	}
	param.operatorID = c.UserID()
	return param, nil
}

func (param *hornMuteAddParam) add() error {
	now := goutil.TimeNow()
	var expireTime time.Time
	switch param.DurationType {
	case durationTypeDay:
		expireTime = now.AddDate(0, 0, param.Duration)
	case durationTypeHour:
		expireTime = now.Add(time.Duration(param.Duration) * time.Hour)
	case durationTypeMinute:
		expireTime = now.Add(time.Duration(param.Duration) * time.Minute)
	}
	// 过期时间精确到分钟 e.g. 2024-12-31 12:01:00
	expireTime = expireTime.Round(time.Minute)
	param.expireTimeStr = expireTime.Format(util.TimeFormatYMDHMS)
	m := &livemembers.Member{
		Helper: livemembers.Helper{
			RoomID:      0,
			UserID:      param.user.UserID(),
			Username:    param.user.Username,
			IconURL:     param.user.IconURL,
			CreatedTime: now,
			ExpireAt:    &expireTime,
			Status:      livemembers.StatusHornMute,
			UpdatedTime: now,
			OperatorID:  param.operatorID,
		},
	}
	err := m.SetMute()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *hornMuteAddParam) sendMuteSystemMessage() {
	msg := userapi.NewSystemMsgBox()
	title := "全站喇叭封禁惩罚通知"
	content := fmt.Sprintf("由于%s，您的全站喇叭功能已被封禁至 %s。在此期间，无法使用全站喇叭，请您遵守规范，谨慎发言！",
		html.EscapeString(param.Reason), param.expireTimeStr)
	msg.AddMessage(param.UserID, title, content)
	if err := msg.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *hornMuteAddParam) sendMuteLog(c util.UserContext) {
	var durationUnit string
	switch param.DurationType {
	case durationTypeDay:
		durationUnit = "天"
	case durationTypeHour:
		durationUnit = "小时"
	case durationTypeMinute:
		durationUnit = "分钟"
	default:
		panic("invalid durationType")
	}
	intro := fmt.Sprintf("【封禁全站喇叭】用户 ID: %d; 理由: %s; 封禁时长: %d %s; 解封时间: %s",
		param.UserID, param.Reason, param.Duration, durationUnit, param.expireTimeStr,
	)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageLiveMute)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type hornMuteRemove struct {
	UserID  int64 `form:"user_id" json:"user_id"`
	Confirm int   `form:"confirm" json:"confirm"`

	mute          *livemembers.Member
	expireTimeStr string
}

// ActionHornMuteRemove 全站喇叭封禁解除
/**
 * @api {post} /api/v2/admin/chatroom/hornmute/remove 全站喇叭封禁解除
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "解封成功"
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "<p>确定要对该用户：解封全站喇叭吗？</p>用户 ID: 12345<br>用户昵称: test<br>用户等级: 120<br>贵族等级: 神话"
 *     }
 *   }
 */
func ActionHornMuteRemove(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newHornMuteRemove(c)
	if err != nil {
		return nil, "", err
	}

	filter := bson.M{"_id": param.mute.OID}
	ok, err := livemembers.FindOneAndDelete(filter)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.ErrParamsMsg("删除失败")
	}

	// 发送用户系统通知
	param.sendMuteSystemMessage()
	// 记录管理员操作日志
	param.sendMuteLog(c)
	return nil, "解封成功", nil
}

func newHornMuteRemove(c *handler.Context) (*hornMuteRemove, error) {
	param := new(hornMuteRemove)
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if param.UserID <= 0 {
		return nil, actionerrors.ErrParamsMsg("请输入用户 ID!")
	}

	user, err := liveuser.Find(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	param.mute, err = livemembers.FindHornMute(user.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.mute == nil {
		return nil, actionerrors.ErrParamsMsg("该用户当前未被封禁！")
	}
	param.expireTimeStr = param.mute.ExpireAt.Format(util.TimeFormatYMDHMS)
	if param.Confirm == 0 {
		cp := hornMuteConfirmParam{
			muteType:      muteTypeRemove,
			user:          user,
			expireTimeStr: param.expireTimeStr,
		}
		return nil, actionerrors.ErrConfirmRequired(cp.buildMessage(c), 1, true)
	}
	return param, nil
}

func (param *hornMuteRemove) sendMuteSystemMessage() {
	msg := userapi.NewSystemMsgBox()
	title := "全站喇叭封禁解除"
	content := "现已恢复您的全站喇叭功能，请您遵守规范，谨慎发言！"
	msg.AddMessage(param.UserID, title, content)
	if err := msg.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *hornMuteRemove) sendMuteLog(c util.UserContext) {
	intro := fmt.Sprintf("【解封全站喇叭】用户 ID: %d; 原解封时间: %s",
		param.UserID, param.expireTimeStr)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogManageLiveMute)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

const (
	muteTypeAdd = iota
	muteTypeRemove
)

type hornMuteConfirmParam struct {
	muteType      int
	user          *liveuser.User
	expireTimeStr string
}

func (param *hornMuteConfirmParam) buildMessage(c goutil.UserContext) string {
	var operateMsg string
	switch param.muteType {
	case muteTypeAdd:
		operateMsg = "封禁"
	case muteTypeRemove:
		operateMsg = "解封"
	default:
		panic("unknown mute type")
	}
	userLevel := usercommon.Level(param.user.Contribution)
	msg := fmt.Sprintf("<p>确定要对该用户：%s全站喇叭吗？</p>用户 ID: %d<br>用户昵称: %s<br>用户等级: %d",
		operateMsg, param.user.UserID(), html.EscapeString(param.user.Username), userLevel)

	uv, err := vip.UserActivatedVip(param.user.UserID(), false, c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if uv != nil {
		msg += fmt.Sprintf("<br>贵族等级: %s", html.EscapeString(uv.Info.Title))
	}
	if param.muteType == muteTypeRemove {
		msg += fmt.Sprintf("<br>封禁到期时间: %s", param.expireTimeStr)
	}
	return msg
}
