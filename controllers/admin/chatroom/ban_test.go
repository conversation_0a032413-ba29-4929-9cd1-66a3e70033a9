package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// NOTICE: only for ban action
const testActionBanRoomID = int64(15)
const testActionBanRoomIDStr = "15"
const bannedRoomID = int64(369892) // room_test banned

func initTestBanData() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var r room.Room
	now := goutil.TimeNow()
	testRoom := room.Helper{
		RoomID:          testActionBanRoomID,
		Name:            "测试封禁解封房间",
		NameClean:       "测试封禁解封房间",
		Type:            "live",
		Status:          room.Status{Open: room.StatusOpenFalse, OpenTime: now.Unix()},
		Statistics:      room.Statistics{Revenue: banConfirmRevenue},
		CreatorID:       15,
		CreatorUsername: "15",
		CreatedTime:     now,
		UpdatedTime:     now,
	}

	err := service.MongoDB.Collection(room.CollectionName).
		FindOneAndUpdate(ctx, bson.M{"room_id": testActionBanRoomID}, bson.M{"$set": testRoom},
			options.FindOneAndUpdate().SetUpsert(true)).Decode(&r)
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}

	meta := livemeta.LiveMeta{
		RoomOID:      r.OID,
		RoomID:       r.RoomID,
		Online:       1,
		Accumulation: 12,
		Ratio:        1.3,
	}
	err = service.MongoDB.Collection(livemeta.CollectionName).
		FindOneAndUpdate(ctx, bson.M{"room_id": testActionBanRoomID}, bson.M{"$set": meta, "$unset": bson.M{"ban": ""}},
			options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}
	return nil
}

func TestCheckConfirm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := banParams{
		Confirm: 1,
	}
	assert.NoError(param.checkConfirm())

	param.Confirm = 0
	param.r = &room.Room{
		OID: primitive.NilObjectID,
		Helper: room.Helper{
			RoomID:          100,
			Statistics:      room.Statistics{Revenue: banConfirmRevenue},
			CreatorUsername: "100",
			CreatorID:       100,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livelog.Collection().DeleteMany(ctx, bson.M{"room_id": param.r.RoomID})
	require.NoError(err)

	message := fmt.Sprintf("主播 %s（MID：%d）为 S 级主播，请先联系运营同学商讨封禁惩罚力度，再进行封禁操作。"+
		"若确定该主播需要进行封禁处罚，请点击“确定”完成封禁", param.r.CreatorUsername, param.r.CreatorID)
	confirmErr := actionerrors.ErrConfirmRequired(message, 1)
	assert.Equal(confirmErr, param.checkConfirm())

	param.r.Statistics.Revenue = 0
	now := goutil.TimeNow()
	param.r.OID = primitive.NewObjectIDFromTimestamp(now)
	assert.NoError(param.checkConfirm())

	record := livelog.Record{
		OID:       primitive.NilObjectID,
		RoomOID:   param.r.OID,
		RoomID:    100,
		StartTime: goutil.NewTimeUnixMilli(now),
		EndTime:   goutil.NewTimeUnixMilli(now),
		Revenue:   1000000,
	}
	_, err = livelog.Collection().InsertOne(ctx, &record)
	require.NoError(err)
	assert.Equal(confirmErr, param.checkConfirm())
}

func TestActionAboutBan(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(initTestBanData())
	param := handler.M{
		"room_id": testActionBanRoomID,
		"type":    livemeta.TypeBanForever,
	}

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err := ActionBan(c)
	assert.EqualError(err, "请输入封禁理由")

	// confirm
	param["reason"] = "test"
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionBan(c)
	require.NotNil(err)
	r := err.(*handler.ActionError)
	assert.Equal(fmt.Sprintf("主播 %s（MID：%d）为 S 级主播，请先联系运营同学商讨封禁惩罚力度，再进行封禁操作。"+
		"若确定该主播需要进行封禁处罚，请点击“确定”完成封禁", testActionBanRoomIDStr, testActionBanRoomID), r.Info["msg"])
	assert.Equal(1, r.Info["confirm"])

	// 永久封禁
	param["confirm"] = r.Info["confirm"]
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionBan(c)
	require.Nil(err)
	foreverBan, err := livemeta.FindBanned(testActionBanRoomID)
	require.NoError(err)
	require.NotNil(foreverBan)
	assert.Equal(livemeta.TypeBanForever, foreverBan.Type)

	// 修改为按分钟封禁
	updateParam := handler.M{
		"room_id":  testActionBanRoomID,
		"type":     livemeta.TypeBanDuration,
		"duration": 10 * livemeta.OneMinute,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, updateParam)
	_, err = ActionUpdateBan(c)
	require.Nil(err)
	minuteBan, err := livemeta.FindBanned(testActionBanRoomID)
	require.NoError(err)
	require.NotNil(minuteBan)
	assert.Equal(livemeta.TypeBanDuration, minuteBan.Type)
	assert.Equal(10*livemeta.OneMinute, *minuteBan.Duration)
	assert.Equal(foreverBan.StartTime, minuteBan.StartTime)

	// 修改为按天封禁
	updateParam = handler.M{
		"room_id":  testActionBanRoomID,
		"type":     livemeta.TypeBanDuration,
		"duration": livemeta.OneDay,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, updateParam)
	_, err = ActionUpdateBan(c)
	require.Nil(err)
	dayBan, err := livemeta.FindBanned(testActionBanRoomID)
	require.NoError(err)
	require.NotNil(dayBan)
	assert.Equal(livemeta.TypeBanDuration, dayBan.Type)
	assert.Equal(livemeta.OneDay, *dayBan.Duration)
	assert.Equal(minuteBan.StartTime, dayBan.StartTime)

	// 解除封禁
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{"room_id": testActionBanRoomID})
	_, err = ActionUnban(c)
	require.Nil(err)
	ban, err := livemeta.FindBanned(testActionBanRoomID)
	require.NoError(err)
	require.Nil(ban)
}

func TestBanLoad(t *testing.T) {
	assert := assert.New(t)

	input := handler.M{
		"type":    livemeta.TypeBanDuration,
		"room_id": int64(existRoomID),
	}
	p := new(banParams)
	c := handler.NewTestContext(http.MethodPost, "/", true, input)
	assert.EqualError(p.banLoad(c), "请输入封禁时长")

	input = handler.M{
		"room_id":  12345678,
		"type":     livemeta.TypeBanDuration,
		"duration": livemeta.OneMinute,
		"reason":   "test",
	}
	p = new(banParams)
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	assert.Equal(actionerrors.ErrCannotFindRoom, p.banLoad(c))

	input["room_id"] = bannedRoomID
	p = new(banParams)
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	assert.EqualError(p.banLoad(c), "该直播间已在封禁中")
}

func TestUpdateBanLoad(t *testing.T) {
	assert := assert.New(t)

	input := handler.M{
		"room_id": 123456,
		"type":    livemeta.TypeBanDuration,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, input)
	p := new(banParams)
	assert.EqualError(p.updateBanLoad(c), "请输入封禁时长")

	input = handler.M{
		"room_id":  123456,
		"type":     livemeta.TypeBanDuration,
		"duration": livemeta.OneMinute,
		"reason":   "test",
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	p = new(banParams)
	assert.Equal(actionerrors.ErrCannotFindRoom, p.updateBanLoad(c))

	input["room_id"] = testActionBanRoomID
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	assert.EqualError(p.updateBanLoad(c), "当前直播间未被封禁")

	input["room_id"] = bannedRoomID
	input["duration"] = livemeta.OneMinute
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	assert.EqualError(p.updateBanLoad(c), "解封时间不可早于当前时间")
}

func TestNewDurationStr(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := banParams{
		Type: livemeta.TypeBanForever,
	}
	require.NoError(p.newDurationStr())
	assert.Equal("永久封禁", p.durationStr)

	p = banParams{
		Type:     livemeta.TypeBanDuration,
		Duration: 600,
	}
	require.NoError(p.newDurationStr())
	assert.Equal("10 分钟", p.durationStr)

	p = banParams{
		Type:     livemeta.TypeBanDuration,
		Duration: 86400,
	}
	require.NoError(p.newDurationStr())
	assert.Equal("1 天", p.durationStr)

	p = banParams{
		Type:     livemeta.TypeBanDuration,
		Duration: 50,
	}
	assert.EqualError(p.newDurationStr(), "请输入正确的封禁时长")

	p.Type = 9999
	assert.Equal(actionerrors.ErrParams, p.newDurationStr())
}
