package chatroom

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustombatchaddlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	kcForm := tutil.NewKeyChecker(t, tutil.FORM)
	kcJSON := tutil.NewKeyChecker(t, tutil.JSON)

	var p giftHideParams
	kcForm.Check(p, "gift_id", "confirm", "reason")
	kcJSON.Check(p, "gift_id", "confirm", "reason")

	var s giftSetorderParams
	kcForm.Check(s, "type", "order_ids", "hide_ids", "effective_time")
	kcJSON.Check(s, "type", "order_ids", "hide_ids", "effective_time")
}

func TestActionGiftHide(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time { return now })
	defer cancel()

	col := gift.Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := col.UpdateOne(ctx, bson.M{"gift_id": 1023},
		bson.M{"$set": bson.M{"gift_id": 1023, "order": 1, "type": gift.TypeNormal}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/hide", false, handler.M{"gift_id": "gift"})
	_, err = ActionGiftHide(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/hide", false, handler.M{"gift_id": int64(1023), "confirm": 0, "reason": "测试原因"})
	_, err = ActionGiftHide(c)
	require.EqualError(err, actionerrors.ErrConfirmRequired("礼物 ID：1023<br>礼物名称：礼物<br>售价（钻）：50<br>下架原因：测试原因", 1, true).Error())
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/hide", false, handler.M{"gift_id": int64(1023), "confirm": 1, "reason": "测试原因"})
	_, err = ActionGiftHide(c)
	require.NoError(err)
	var g gift.Gift
	require.NoError(col.FindOne(ctx, bson.M{"gift_id": 1023}).Decode(&g))
	assert.Zero(g.Order)
	assert.Nil(g.SetOrder)
	assert.Equal(now.Unix(), g.ToggleTime) // 下架时更新 toggle_time 字段

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/hide", false, handler.M{"gift_id": int64(-999)})
	_, err = ActionGiftHide(c)
	assert.Equal(actionerrors.ErrParamsMsg("礼物 -999 不存在或已下架"), err)
}

func TestActionGiftReorder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5s.Flush()
	shows, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.NotEmpty(shows)

	var testMedalGiftIDs []int64
	for i := range shows {
		if shows[i].Type == gift.TypeMedal {
			testMedalGiftIDs = append(testMedalGiftIDs, shows[i].GiftID)
		}
	}
	require.NotEmpty(testMedalGiftIDs)
	c := handler.NewTestContext("POST", "/", false, handler.M{
		"gift_ids": goutil.JoinInt64Array(testMedalGiftIDs, ","),
		"type":     gift.TabGiftMedal,
	})
	r, err := ActionGiftReorder(c)
	require.NoError(err)
	assert.Equal("排序成功", r)
	service.Cache5s.Flush()
	g, err := gift.FindShowingGiftByGiftID(testMedalGiftIDs[0])
	require.NoError(err)
	require.NotNil(g.Order)
	assert.Equal(10, g.Order)

	c = handler.NewTestContext("POST", "/", false, handler.M{"gift_ids": []int64{}})
	_, err = ActionGiftReorder(c)
	assert.Error(err)
}

func TestParamGiftReorderCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	shows, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.NotEmpty(shows)
	testGiftIDs := make([]int64, len(shows))
	for i := range shows {
		testGiftIDs[i] = shows[i].GiftID
	}

	param := paramGiftReorder{
		GiftIDs: "123,123",
		Type:    gift.TabGiftNormal,
	}
	err = param.check()
	require.NotNil(err)
	assert.EqualError(err, "礼物 ID 重复，请检查重新输入")

	testMedalGifts := make([]int64, 0, len(shows))
	for i := range shows {
		if shows[i].Type == gift.TypeMedal {
			testMedalGifts = append(testMedalGifts, shows[i].GiftID)
		}
	}
	param = paramGiftReorder{
		GiftIDs: goutil.JoinInt64Array(testMedalGifts, ","),
		Type:    gift.TabGiftMedal,
	}
	require.NoError(param.check())

	testMedalGifts = append(testMedalGifts, -9999)
	param = paramGiftReorder{
		GiftIDs: goutil.JoinInt64Array(testMedalGifts, ","),
		Type:    gift.TabGiftMedal,
	}
	require.Equal(actionerrors.ErrParamsMsg("礼物 IDs: -9999 不存在或被遗漏，请检查后重新输入"), param.check())
}

func TestParamGiftReorder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	shows, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.NotEmpty(shows)
	testGiftIDs := make([]int64, len(shows))
	for i := range shows {
		testGiftIDs[i] = shows[i].GiftID
	}

	param := paramGiftReorder{
		giftIDs: testGiftIDs,
	}
	require.NoError(param.reorder())
	service.Cache5s.Flush()
	g, err := gift.FindShowingGiftByGiftID(testGiftIDs[0])
	require.NoError(err)
	assert.Equal(10, g.Order)
}

func TestActionGiftSetOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &giftSetorderParams{
		Type:          gift.TabGiftNoble,
		HideIDs:       "",
		OrderIDs:      "",
		EffectiveTime: 0,
	}

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/setorder", true, param)
	_, err := ActionGiftSetOrder(c)
	assert.Equal(actionerrors.ErrParams, err)

	now := goutil.TimeNow()
	effectiveTime := now.Add(2 * time.Minute)
	param.EffectiveTime = effectiveTime.Unix()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := gift.Collection()
	testGiftIDs := []int64{1027, 1028, 1029}
	// 测试定时排序
	defer func() {
		_, err = col.UpdateMany(ctx, bson.M{"type": gift.TypeNoble}, bson.M{"$unset": bson.M{"setorder": ""}})
		assert.NoError(err)
		_, err = col.DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": testGiftIDs}})
		assert.NoError(err)
	}()
	g := gift.Gift{
		GiftID:    1027,
		Name:      "测试礼物定时排序",
		NameClean: "测试礼物定时排序",
		AddedTime: time.Date(2019, 10, 1, 0, 0, 0, 0, time.Local),
		Order:     10,
		Type:      gift.TypeNoble,
		SetOrder: &gift.SetOrder{
			EffectiveTime: now,
			Order:         10270,
		},
	}
	_, err = col.InsertOne(ctx, g)
	require.NoError(err)
	g.GiftID = 1028
	g.Name = "测试礼物定时排序 2"
	g.NameClean = "测试礼物定时排序 2"
	g.SetOrder.Order = 10280
	_, err = col.InsertOne(ctx, g)
	require.NoError(err)
	orderHideGift := g
	orderHideGift.GiftID = 1029
	orderHideGift.Name = "测试礼物定时排序 3"
	orderHideGift.NameClean = "测试礼物定时排序 3"
	orderHideGift.Order = 0
	orderHideGift.SetOrder.Order = 10290
	_, err = col.InsertOne(ctx, orderHideGift)
	require.NoError(err)

	filter := bson.M{
		"type":       bson.M{"$gt": gift.TypeHide},
		"order":      bson.M{"$gt": gift.OrderHide},
		"added_time": bson.M{"$lte": effectiveTime},
	}
	cur, err := gift.Collection().Find(ctx, filter, options.Find())
	require.NoError(err)

	defer cur.Close(ctx)
	var allAvailableGifts []gift.Gift
	err = cur.All(ctx, &allAvailableGifts)
	require.NoError(err)

	gifts, _ := gift.GroupGifts(allAvailableGifts, gift.GroupGiftsOptions{
		User:   nil,
		RoomID: nil,
	})

	// 统计所有命中的礼物
	giftIDs := make([]int64, 0, len(gifts))
	for i := range gifts[param.Type] {
		giftIDs = append(giftIDs, gifts[param.Type][i].GiftID)
	}
	checkingGiftIDs := util.Uniq(append(giftIDs, testGiftIDs...))
	require.NotEmpty(giftIDs)
	require.Equal([]int64{orderHideGift.GiftID}, util.DiffInt64(checkingGiftIDs, giftIDs))
	giftIDs = append(giftIDs, orderHideGift.GiftID)

	// 常规的上架部分礼物以及下架部分礼物
	param.HideIDs = goutil.JoinInt64Array(giftIDs[1:], ",")
	param.OrderIDs = goutil.JoinInt64Array(giftIDs[:1], ",")
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/setorder", true, param)
	_, err = ActionGiftSetOrder(c)
	require.NoError(err)

	var checkGift []gift.Gift
	// 检查下架礼物数据
	cur, err = col.Find(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs[1:]}}, options.Find())
	require.NoError(err)
	err = cur.All(ctx, &checkGift)
	require.NoError(err)
	for _, v := range checkGift {
		assert.Equal(effectiveTime.Unix(), v.SetOrder.EffectiveTime.Unix())
		assert.Equal(gift.OrderHide, v.SetOrder.Order)
	}

	// 检查排序礼物数据
	cur, err = col.Find(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs[:1]}}, options.Find())
	require.NoError(err)
	err = cur.All(ctx, &checkGift)
	require.NoError(err)
	for _, v := range checkGift {
		assert.Equal(effectiveTime.Unix(), v.SetOrder.EffectiveTime.Unix())
		assert.NotZero(v.SetOrder.Order)
	}

	// 重置礼物缓存
	service.Cache5s.Flush()

	// 仅上架礼物
	param.OrderIDs = goutil.JoinInt64Array(giftIDs, ",")
	param.HideIDs = ""
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/setorder", true, param)
	_, err = ActionGiftSetOrder(c)
	require.NoError(err)

	// 仅下架礼物
	param.OrderIDs = ""
	param.HideIDs = goutil.JoinInt64Array(giftIDs, ",")
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/setorder", true, param)
	_, err = ActionGiftSetOrder(c)
	require.NoError(err)

	cur, err = col.Find(ctx, bson.M{"gift_id": bson.M{"$in": giftIDs}}, options.Find())
	require.NoError(err)
	err = cur.All(ctx, &checkGift)
	require.NoError(err)
	for _, v := range checkGift {
		assert.Equal(effectiveTime.Unix(), v.SetOrder.EffectiveTime.Unix())
		assert.Equal(gift.OrderHide, v.SetOrder.Order)
	}
}

func TestGiftSetOrderParamCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := gift.Collection()

	testGiftIDs := []int64{1024, 1025, 1026}
	defer func() {
		_, err := col.DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": testGiftIDs}})
		assert.NoError(err)
	}()
	effectiveTime := goutil.TimeNow().Add(2 * time.Minute)
	orderHideGift := gift.Gift{
		GiftID:    1026,
		Name:      "礼物定时排序参数测试 3",
		NameClean: "礼物定时排序参数测试 3",
		AddedTime: time.Date(2019, 10, 1, 0, 0, 0, 0, time.Local),
		Order:     0,
		Type:      gift.TypeNoble,
		SetOrder: &gift.SetOrder{
			EffectiveTime: effectiveTime,
			Order:         10260,
		},
	}
	_, err := col.InsertMany(ctx, []interface{}{
		gift.Gift{
			GiftID:    1024,
			Name:      "礼物定时排序参数测试",
			NameClean: "礼物定时排序参数测试",
			AddedTime: time.Date(2019, 10, 1, 0, 0, 0, 0, time.Local),
			Order:     10240,
			Type:      gift.TypeNoble,
			SetOrder: &gift.SetOrder{
				EffectiveTime: effectiveTime,
				Order:         10240,
			},
		},
		gift.Gift{
			GiftID:    1025,
			Name:      "礼物定时排序参数测试 2",
			NameClean: "礼物定时排序参数测试 2",
			AddedTime: time.Date(2019, 10, 1, 0, 0, 0, 0, time.Local),
			Order:     10250,
			Type:      gift.TypeNoble,
			SetOrder: &gift.SetOrder{
				EffectiveTime: effectiveTime,
				Order:         10250,
			},
		},
		orderHideGift,
	})
	require.NoError(err)

	filter := bson.M{
		"type":       bson.M{"$gt": gift.TypeHide},
		"order":      bson.M{"$gt": gift.OrderHide},
		"added_time": bson.M{"$lte": effectiveTime},
	}
	cur, err := col.Find(ctx, filter, options.Find().
		SetSort(bson.D{{Key: "type", Value: 1}, {Key: "order", Value: 1}}))
	require.NoError(err)
	defer cur.Close(ctx)
	var allOnlineGifts []gift.Gift
	err = cur.All(ctx, &allOnlineGifts)
	require.NoError(err)
	require.GreaterOrEqual(len(allOnlineGifts), len(testGiftIDs))

	giftIDs := make([]int64, len(allOnlineGifts))
	for i := range allOnlineGifts {
		giftIDs[i] = allOnlineGifts[i].GiftID
	}

	inputGiftIDs := make([]int64, 0, len(allOnlineGifts))
	for i := range allOnlineGifts {
		if allOnlineGifts[i].Type == gift.TypeNoble ||
			(allOnlineGifts[i].Type == gift.TypeCustom && allOnlineGifts[i].NobleLevel > 0) {
			inputGiftIDs = append(inputGiftIDs, allOnlineGifts[i].GiftID)
		}
	}

	checkingGiftIDs := util.Uniq(append(inputGiftIDs, testGiftIDs...))
	require.Equal([]int64{orderHideGift.GiftID}, util.DiffInt64(checkingGiftIDs, inputGiftIDs))
	inputGiftIDs = append(inputGiftIDs, orderHideGift.GiftID)

	// 只输入排序礼物 ids
	orderIDs := goutil.JoinInt64Array(giftIDs, ",") + ",1024,1024"
	param := giftSetorderParams{
		Type:          gift.TabGiftNormal,
		OrderIDs:      orderIDs,
		EffectiveTime: effectiveTime.Unix(),
	}
	err = param.checkSetOrderParam()
	require.NotNil(err)
	assert.EqualError(err, "排序的礼物 ID 重复，请检查重新输入")

	param = giftSetorderParams{
		OrderIDs:      goutil.JoinInt64Array(inputGiftIDs, ","),
		Type:          gift.TabGiftNoble,
		EffectiveTime: effectiveTime.Unix(),
	}
	require.NoError(param.checkSetOrderParam())

	param = giftSetorderParams{
		OrderIDs:      goutil.JoinInt64Array(append(inputGiftIDs, -9999), ","),
		Type:          gift.TabGiftNoble,
		EffectiveTime: effectiveTime.Unix(),
	}
	require.Equal(actionerrors.ErrParamsMsg("礼物 ID: -9999 不存在"), param.checkSetOrderParam())

	// 只输入下架礼物 ids
	hideIDs := goutil.JoinInt64Array(inputGiftIDs, ",") + ",1024,1024"
	param = giftSetorderParams{
		Type:    gift.TabGiftNoble,
		HideIDs: hideIDs,
	}
	err = param.checkSetOrderParam()
	require.NotNil(err)
	assert.EqualError(err, "下架的礼物 ID 重复，请检查重新输入")

	param = giftSetorderParams{
		HideIDs:       goutil.JoinInt64Array(inputGiftIDs, ","),
		Type:          gift.TabGiftNoble,
		EffectiveTime: effectiveTime.Unix(),
	}
	require.NoError(param.checkSetOrderParam())

	// 同时输入下架和排序礼物 ids
	param = giftSetorderParams{
		OrderIDs:      goutil.JoinInt64Array(inputGiftIDs[1:], ","),
		HideIDs:       goutil.JoinInt64Array(inputGiftIDs[:1], ","),
		Type:          gift.TabGiftNoble,
		EffectiveTime: effectiveTime.Unix(),
	}
	require.NoError(param.checkSetOrderParam())

	param = giftSetorderParams{
		OrderIDs: goutil.JoinInt64Array(inputGiftIDs[0:], ","),
		HideIDs:  goutil.JoinInt64Array(inputGiftIDs[:1], ","),
	}
	require.Equal(actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 IDs：%d 同时出现在排序的礼物和下架的礼物中，请检查后重新输入", inputGiftIDs[0])), param.checkSetOrderParam())

	param = giftSetorderParams{
		OrderIDs: "test,test2",
	}
	require.EqualError(actionerrors.ErrParams, param.checkSetOrderParam().Error())

	param = giftSetorderParams{
		OrderIDs: "",
		HideIDs:  "test,test2",
	}
	require.EqualError(actionerrors.ErrParams, param.checkSetOrderParam().Error())
}

func TestAddGiftCustom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := addGiftCustomParam{
		GiftID: 123,
		UserID: 123444,
		Day:    5,
	}
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND custom_id = ?",
		livecustom.TypeUserCustomGift, param.GiftID).Error)
	require.NoError(param.addGiftCustom())
	checkExpireTime := goutil.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, param.Day+1)
	res, err := livecustom.FindUserCustomGift(param.UserID, param.GiftID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(checkExpireTime.Unix(), res.EndTime)

	require.NoError(param.addGiftCustom())
	checkExpireTime = time.Unix(res.EndTime, 0).AddDate(0, 0, param.Day)
	res, err = livecustom.FindUserCustomGift(param.UserID, param.GiftID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(checkExpireTime.Unix(), res.EndTime)
}

func TestActionGiftCustomAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftID := int64(1111)
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND custom_id = ?",
		livecustom.TypeUserCustomGift, testGiftID).Error)

	param := &addGiftCustomParam{
		UserID: 123,
		GiftID: 1111,
	}
	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/custom/add", true, param)
	_, err := ActionGiftCustomAdd(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	param.Day = 5
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/chatroom/gift/custom/add", true, param)
	resp, err := ActionGiftCustomAdd(c)
	require.NoError(err)
	assert.Equal("success", resp)
	exists, err := livecustom.IsUserCustomGiftExists(param.UserID, testGiftID)
	require.NoError(err)
	assert.True(exists)
}

func TestActionGiftRoomCustomAdd(t *testing.T) {
	t.Skip()
	assert := assert.New(t)

	param := &addGiftRoomCustomParam{
		RoomIDs: "123",
		GiftID:  1,
	}
	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err := ActionGiftRoomCustomAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = &addGiftRoomCustomParam{
		RoomIDs: testActionBanRoomIDStr + ",1234456",
		GiftID:  1,
		Day:     1,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionGiftRoomCustomAdd(c)
	assert.EqualError(err, "部分直播间不存在，请检查后重新输入")

	param = &addGiftRoomCustomParam{
		RoomIDs: testActionBanRoomIDStr,
		GiftID:  1,
		Day:     1,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = ActionGiftRoomCustomAdd(c)
	assert.EqualError(err, "不支持当前礼物")
}

func TestAddGiftRoomCustom(t *testing.T) {
	t.Skip() // 待补充
}

func TestCheckGiftParam(t *testing.T) {
	assert := assert.New(t)
	g := &gift.CSVGift{
		GiftID:     10000,
		Type:       1,
		Exclusive:  0,
		Price:      1,
		Point:      1,
		NobleLevel: 1,
		MedalLevel: 1,
		UserLevel:  1,
		UserID:     1,
		RoomID:     -1,
	}
	err := checkGiftParam(g)
	assert.EqualError(err, "礼物信息错误, 请检查后重新上传")

	g.RoomID = 0
	g.BaseGiftID = 60003
	err = checkGiftParam(g)
	assert.EqualError(err, "贵族礼物的模板礼物、礼物类型或贵族等级限制错误，请检查后重新上传")

	g.BaseGiftID = 0
	g.VipType = 3
	err = checkGiftParam(g)
	assert.EqualError(err, "贵族礼物的类别错误，请检查后重新上传")

	g.VipType = vip.TypeLiveHighness
	err = checkGiftParam(g)
	assert.NoError(err)
}

func TestActionGiftImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, err := ActionGiftImport(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{"csv_url": "https://fm.example.com/testdata/gift.csv"})
	r, err := ActionGiftImport(c)
	require.NoError(err)
	resp := r.(giftSetInfo)
	// 测试导入 1000 2000 3000 三个礼物
	assert.Equal("导入成功 3 个礼物", resp.Msg)
}

func TestActionGiftSetOnline(t *testing.T) {
	assert := assert.New(t)

	p := &giftSetOnlineParams{
		GiftIDs:       "1,2,3",
		EffectiveTime: -1,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, p)
	_, _, err := ActionGiftSetOnline(c)
	assert.EqualError(err, "生效时间不能早于当前时间")

	p = &giftSetOnlineParams{
		GiftIDs:       "1,1,2,3",
		EffectiveTime: goutil.TimeNow().Add(time.Minute).Unix(),
	}
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	_, _, err = ActionGiftSetOnline(c)
	assert.EqualError(err, "礼物 ID 重复，请检查后重新输入")

	p = &giftSetOnlineParams{
		GiftIDs:       "1,-999",
		EffectiveTime: goutil.TimeNow().Add(time.Minute).Unix(),
	}
	c = handler.NewTestContext(http.MethodPost, "", true, p)
	_, _, err = ActionGiftSetOnline(c)
	assert.EqualError(err, "部分礼物 ID 不存在，请检查后重新输入")
}

func TestGiftSetOnlineParams_check(t *testing.T) {
	assert := assert.New(t)

	p := &giftSetOnlineParams{
		giftIDs: []int64{1, 2, 3},
		giftMap: map[int64]*gift.Gift{
			1: {GiftID: 1, Order: 1},
			2: {GiftID: 2},
			3: {GiftID: 3},
		},
	}
	assert.EqualError(p.check(), "礼物 ID: 1 已经上架, 请检查后重新输入")

	p = &giftSetOnlineParams{
		giftIDs: []int64{1, 2, 3},
		giftMap: map[int64]*gift.Gift{
			1: {GiftID: 1, Type: gift.TypeDrawReceive},
			2: {GiftID: 2},
			3: {GiftID: 3},
		},
	}
	assert.EqualError(p.check(), "部分礼物类型不支持上架操作, 礼物 ID: 1, 请检查后重新输入")

	p = &giftSetOnlineParams{
		giftIDs: []int64{1, 2, 3},
		giftMap: map[int64]*gift.Gift{
			1: {GiftID: 1, Name: "A"},
			2: {GiftID: 2, Name: "B"},
			3: {GiftID: 3, Name: "C"},
		},
	}
	assert.EqualValues(p.check().(*handler.ActionError).Info["msg"], "确认上架以下礼物吗<br>1: A<br>2: B<br>3: C")
}

func TestCheckLuckyGiftPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testLuckyGiftIDs      = []int64{1, 2}
		testDrawReceiveGiftID = int64(11)
		testGiftMaps          = map[int64]*gift.Gift{
			1: {GiftID: 1, Name: "A", Type: gift.TypeDrawSend, AllowedNums: []int{1}},
			2: {GiftID: 2, Name: "B", Type: gift.TypeDrawSend, AllowedNums: []int{1, 99}},
		}
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := gift.CollectionDrawPool().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": testLuckyGiftIDs}})
	require.NoError(err)

	err = checkLuckyGiftPool(testLuckyGiftIDs, testGiftMaps)
	assert.EqualError(err, "随机礼物奖池未配置, 礼物 ID: 1,2, 请检查后重新输入")

	_, err = gift.CollectionDrawPool().InsertMany(ctx, bson.A{
		gift.PoolGift{PoolID: 22222, Type: gift.PoolTypeGift, GiftID: testLuckyGiftIDs[0], GiftNum: 1,
			Rates: map[int64]int{testDrawReceiveGiftID: 1}},
		gift.PoolGift{PoolID: 22223, Type: gift.PoolTypeGift, GiftID: testLuckyGiftIDs[1], GiftNum: 1,
			Rates: map[int64]int{testDrawReceiveGiftID: 1}},
	})
	require.NoError(err)
	cacheKey := keys.KeyOnlineGifts0.Format()
	service.Cache5s.Set(cacheKey, []gift.Gift{{GiftID: testDrawReceiveGiftID, Type: gift.TypeDrawReceive, Order: 1}}, 0)
	err = checkLuckyGiftPool(testLuckyGiftIDs, testGiftMaps)
	assert.EqualError(err, "礼物 ID: 2, 数量: 99 档位的奖池配置不存在, 请检查后重新输入")

	_, err = gift.CollectionDrawPool().InsertMany(ctx, bson.A{
		gift.PoolGift{PoolID: 22223, Type: gift.PoolTypeGift, GiftID: testLuckyGiftIDs[1], GiftNum: 99,
			Rates: map[int64]int{testDrawReceiveGiftID: 1}},
	})
	require.NoError(err)
	err = checkLuckyGiftPool(testLuckyGiftIDs, testGiftMaps)
	require.NoError(err)
}

func TestGiftSetOnlineParams_setOnline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time { return now })
	defer cancel()

	p := &giftSetOnlineParams{
		giftIDs:       []int64{1},
		EffectiveTime: 123456789,
	}
	require.NoError(p.setOnline())
	g, err := gift.FindByGiftID(p.giftIDs[0])
	require.NoError(err)
	require.NotNil(g)
	assert.Equal(p.EffectiveTime, g.SetOrder.EffectiveTime.Unix())
	assert.EqualValues(p.giftIDs[0], g.SetOrder.Order)

	// 立即上架时更新 toggle_time 字段
	p.EffectiveTime = 0
	require.NoError(p.setOnline())
	g, err = gift.FindByGiftID(p.giftIDs[0])
	require.NoError(err)
	require.NotNil(g)
	assert.Equal(now.Unix(), g.ToggleTime)
}

func TestGiftSetOnlineParams_sendBotMessage(t *testing.T) {
	assert := assert.New(t)

	p := &giftSetOnlineParams{
		giftIDs: []int64{1, 2, 3},
		giftMap: map[int64]*gift.Gift{
			1: {GiftID: 1, Name: "A"},
			2: {GiftID: 2, Name: "B"},
			3: {GiftID: 3, Name: "C"},
		},
		c: handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	var count int
	cancel := mrpc.SetMock("pushservice://api/bot", func(input any) (output any, err error) {
		count++
		return "success", nil
	})
	defer cancel()
	p.sendBotMessage()
	assert.Equal(1, count)
}

func TestActionExclusiveBatchAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftID := int64(2001)
	effectiveTime := int64(123456789)
	expireTime := int64(234567890)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cleanTestGiftData := func() {
		_, err := gift.Collection().DeleteOne(ctx, bson.M{"gift_id": testGiftID})
		require.NoError(err)
	}
	cleanTestGiftData()
	defer cleanTestGiftData()
	testGift := gift.Gift{
		GiftID:    testGiftID,
		Name:      "测试专属礼物",
		NameClean: "测试专属礼物",
		Type:      gift.TypeCustom,
		Price:     100,
		Order:     1,
		AddedTime: goutil.TimeNow(),
	}
	_, err := gift.Collection().InsertOne(ctx, testGift)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, _, err = ActionExclusiveBatchAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"csv_url": "http://fm.example.com/testdata/exclusive_user.csv",
	})
	_, _, err = ActionExclusiveBatchAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	cleanTestCustomData := func() {
		require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_id = ?", testGiftID).Error)
		require.NoError(livecustombatchaddlog.LiveCustomBatchAddLog{}.DB().Delete("", "custom_id = ?", testGiftID).Error)
	}
	cleanTestCustomData()
	defer cleanTestCustomData()

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"exclusive_type": gift.GiftExclusiveUser,
		"gift_id":        testGiftID,
		"csv_url":        "http://fm.example.com/testdata/exclusive_user.csv",
		"effective_time": effectiveTime,
		"expire_time":    expireTime,
		"confirm":        1,
	})
	_, msg, err := ActionExclusiveBatchAdd(c)
	require.NoError(err)
	assert.Equal("分配成功", msg)

	var batchAddLog livecustombatchaddlog.LiveCustomBatchAddLog
	err = batchAddLog.DB().Where("custom_id = ? AND custom_type = ?", testGiftID, livecustom.TypeUserCustomGift).First(&batchAddLog).Error
	require.NoError(err)
	assert.Equal(testGiftID, batchAddLog.CustomID)
	assert.True(strings.HasPrefix(batchAddLog.CSVURL, "sound://live/") && strings.HasSuffix(batchAddLog.CSVURL, ".csv"))
	assert.Equal(effectiveTime, batchAddLog.EffectiveTime)
	assert.Equal(expireTime, batchAddLog.ExpireTime)

	var customs []livecustom.LiveCustom
	err = livecustom.LiveCustom{}.DB().Where("custom_id = ? AND custom_type = ?", testGiftID, livecustom.TypeUserCustomGift).Find(&customs).Error
	require.NoError(err)
	require.Len(customs, 1)
	assert.Equal(testGiftID, customs[0].CustomID)
	assert.Equal(livecustom.TypeUserCustomGift, customs[0].CustomType)
	assert.Equal(effectiveTime, customs[0].StartTime)
	assert.Equal(expireTime, customs[0].EndTime)
	assert.Equal(batchAddLog.ID, customs[0].BatchID)
}

func TestActionExclusiveBatchDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, _, err := ActionExclusiveBatchDelete(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"batch_id": 999,
	})
	_, _, err = ActionExclusiveBatchDelete(c)
	assert.EqualError(err, "专属礼物批量分配记录不存在")

	testLog := &livecustombatchaddlog.LiveCustomBatchAddLog{
		CustomType: livecustom.TypeUserCustomGift,
		DeleteTime: 0,
	}
	require.NoError(testLog.DB().Create(testLog).Error)
	defer func() {
		require.NoError(testLog.DB().Delete(testLog).Error)
	}()

	testCustoms := []livecustom.LiveCustom{
		{
			CustomType: livecustom.TypeUserCustomGift,
			BatchID:    testLog.ID,
			DeleteTime: 0,
		},
		{
			CustomType: livecustom.TypeUserCustomGift,
			BatchID:    testLog.ID,
			DeleteTime: 0,
		},
	}
	for _, v := range testCustoms {
		require.NoError(v.DB().Create(&v).Error)
	}
	defer func() {
		for _, v := range testCustoms {
			require.NoError(v.DB().Delete(&v).Error)
		}
	}()

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"batch_id": testLog.ID,
		"confirm":  1,
	})
	_, msg, err := ActionExclusiveBatchDelete(c)
	require.NoError(err)
	assert.Equal("删除成功", msg)

	var count int64
	err = testLog.DB().Model(testLog).Where("id = ? AND delete_time = 0", testLog.ID).Count(&count).Error
	require.NoError(err)
	assert.Zero(count)

	err = livecustom.LiveCustom{}.DB().Model(&livecustom.LiveCustom{}).
		Where("batch_id = ? AND delete_time = 0", testLog.ID).Count(&count).Error
	require.NoError(err)
	assert.Zero(count)
}
