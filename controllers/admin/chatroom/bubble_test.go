package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
)

func TestActionBubbleInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/chatroom/bubble/info?bubble_id=0", true, nil)
	_, _, err := ActionBubbleInfo(c)
	assert.EqualError(err, "参数错误")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/admin/chatroom/bubble/info?bubble_id=9999999999", true, nil)
	_, _, err = ActionBubbleInfo(c)
	assert.EqualError(err, "气泡 ID 不存在")

	c = handler.NewTestContext(http.MethodGet, "/api/v2/admin/chatroom/bubble/info?bubble_id=100", true, nil)
	resp, _, err := ActionBubbleInfo(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(100, resp.(bubbleSimpleResp).BubbleID)
}

func TestNewAddBubbleParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	param, err := newAddBubbleParam(c)
	assert.Nil(param)
	assert.EqualError(err, "参数错误")

	body := addBubbleParam{
		BubbleID: 1919,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	param, err = newAddBubbleParam(c)
	require.NoError(err)
	assert.EqualValues(1919, param.BubbleID)
}

func TestAddBubbleParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	param := addBubbleParam{}
	assert.EqualError(param.check(), "参数错误")

	testBubble := bubble.Bubble{
		BubbleID: 191911,
		Usage:    "test_usage",
		Image:    "test_image.png",
	}
	param.BubbleID = testBubble.BubbleID
	_, err := bubble.Collection().DeleteOne(ctx, bson.M{"bubble_id": testBubble.BubbleID})
	require.NoError(err)
	key := keys.KeyBubbles.Format()
	service.Cache10s.Delete(key)
	assert.EqualError(param.check(), "当前仅支持飘屏类型")
	param.BubbleType = bubble.TypeNotify
	assert.EqualError(param.check(), "需填写正确用途")
	param.Usage = testBubble.Usage
	assert.EqualError(param.check(), "上传图片数量错误")
	param.ImageURLs = []upload.SourceURL{upload.SourceURL(testBubble.Image)}
	assert.EqualError(param.check(), "需填写正确格式的拉伸参数")
	param.Clip = "1_1_1_1"
	assert.Equal(param.check(), actionerrors.ErrConfirmRequired(
		"气泡 ID：191911<br>高亮色号：<br>普通色号：<br>拉伸参数：1_1_1_1<br>用途：test_usage<br>悬停状态：不悬停", 1, true))

	_, err = bubble.Collection().InsertOne(ctx, &testBubble)
	service.Cache10s.Delete(key)
	require.NoError(err)
	assert.EqualError(param.check(), "气泡 ID 已存在")
}

func TestAddBubbleParam_checkColor(t *testing.T) {
	assert := assert.New(t)

	p := addBubbleParam{}
	assert.NoError(p.checkColor())
	p.NormalColor = "1"
	assert.EqualError(p.checkColor(), "需填写正确的色号格式")
	p.NormalColor = "#FFFFFF"
	assert.NoError(p.checkColor())
	p.HighlightColor = "1"
	assert.EqualError(p.checkColor(), "需填写正确的色号格式")
	p.HighlightColor = "#FFFFFF"
	assert.NoError(p.checkColor())
}

func TestGetNotifyBubbleTargetPath(t *testing.T) {
	assert := assert.New(t)

	targetPath := getNotifyBubbleTargetPath(1919, "1_1_1_1", "test.jpg")
	assert.Equal("live/bubbles/notify/b1919_1_1_1_1.jpg", targetPath.String())
}

func TestAddBubbleParam_uploadImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := addBubbleParam{
		BubbleID:  1919,
		ImageURLs: []upload.SourceURL{upload.SourceURL(config.Conf.Service.Upload.URL + "test.png")},
		Clip:      "1_1_1_1",
	}
	var err error
	p.imageURLFilter, err = liveupload.NewImageURLFilter(p.ImageURLs)
	require.NoError(err)
	require.NoError(p.uploadImage())
	assert.Equal("live/bubbles/notify/b1919_1_1_1_1.png", p.targetPath.String())

	r, err := http.Get("http://static-test.maoercdn.com/" + p.targetPath.String())
	require.NoError(err)
	assert.Equal(http.StatusOK, r.StatusCode)
}

func TestAddBubbleParam_addBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := addBubbleParam{
		BubbleID:    1919182,
		NormalColor: "#FFFFFF",
		Float:       1,
		targetPath:  "bubbles/test_image_1_1_1_1.png",
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := bubble.Collection().DeleteOne(ctx, bson.M{"bubble_id": p.BubbleID})
	require.NoError(err)
	require.NoError(p.addBubble())
	key := keys.KeyBubbles.Format()
	service.Cache10s.Delete(key)
	b, err := bubble.FindOne(p.BubbleID)
	require.NoError(err)
	assert.Equal("#FFFFFF", b.NormalColor)
	assert.Equal(1, b.Float)
	assert.Equal("https://static-test.missevan.com/bubbles/test_image_1_1_1_1.png", b.Image)
	p.Replace = allowReplace
	p.NormalColor = ""
	p.Float = 0
	require.NoError(p.addBubble())
	service.Cache10s.Delete(key)
	b, err = bubble.FindOne(p.BubbleID)
	require.NoError(err)
	assert.Empty(b.NormalColor)
	assert.Zero(p.Float)
	assert.Equal("https://static-test.missevan.com/bubbles/test_image_1_1_1_1.png", b.Image)
}

func TestNewEditBubbleParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	param, err := newEditBubbleParam(c)
	assert.Nil(param)
	assert.EqualError(err, "参数错误")

	body := editBubbleParam{
		BubbleID: 1919,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	param, err = newEditBubbleParam(c)
	require.NoError(err)
	assert.EqualValues(1919, param.BubbleID)
}

func TestEditBubbleParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	param := editBubbleParam{}
	assert.EqualError(param.check(), "参数错误")

	testBubble := bubble.Bubble{
		BubbleID: 191911,
		Type:     bubble.TypeNotify,
	}
	param.BubbleID = testBubble.BubbleID
	_, err := bubble.Collection().DeleteOne(ctx, bson.M{"bubble_id": testBubble.BubbleID})
	require.NoError(err)
	key := keys.KeyBubbles.Format()
	service.Cache10s.Delete(key)
	assert.EqualError(param.check(), "气泡 ID 不存在")
	_, err = bubble.Collection().InsertOne(ctx, &testBubble)
	service.Cache10s.Delete(key)
	require.NoError(err)
	assert.EqualError(param.check(), "当前仅支持飘屏类型")
	param.BubbleType = bubble.TypeNotify
	require.NoError(param.check())
	assert.Equal(param.confirmMsg, "气泡 ID：191911")
}

func TestEditBubbleParam_processInput(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := editBubbleParam{
		Float:          -1,
		Confirm:        1,
		HighlightColor: "1",
		NormalColor:    "1",
		Clip:           "1",
		update:         bson.M{},
	}
	assert.EqualError(p.processInput(), "需填写正确的色号格式")
	p.HighlightColor = "#FFFFFF"
	assert.EqualError(p.processInput(), "需填写正确的色号格式")
	p.NormalColor = "#FFFFFF"
	assert.EqualError(p.processInput(), "需填写正确格式的拉伸参数")
	p.Clip = "1_1_1_1"
	p.confirmMsg = "气泡 ID：1919"
	require.NoError(p.processInput())
	assert.Equal("气泡 ID：1919<br>高亮色号：#FFFFFF<br>普通色号：#FFFFFF<br>拉伸参数：1_1_1_1", p.confirmMsg)
	p.confirmMsg = "气泡 ID：1919"
	p.Usage = "test"
	p.Confirm = 0
	p.Float = 0
	assert.Equal(p.processInput(), actionerrors.ErrConfirmRequired(
		"气泡 ID：1919<br>高亮色号：#FFFFFF<br>普通色号：#FFFFFF<br>拉伸参数：1_1_1_1<br>用途：test<br>悬停状态：不悬停", 1, true))
	assert.Len(p.update, 5)
}

func TestEditBubbleParam_processImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := editBubbleParam{
		BubbleID: 1919,
		update:   bson.M{},
	}
	assert.NoError(p.processImage())
	p.ImageURLs = []upload.SourceURL{"test.png"}
	assert.EqualError(p.processImage(), "需上传资源同时填写拉伸参数才可进行修改")
	p.Clip = "1_1_1_1"
	p.ImageURLs = []upload.SourceURL{}
	assert.EqualError(p.processImage(), "需上传资源同时填写拉伸参数才可进行修改")
	p.ImageURLs = []upload.SourceURL{upload.SourceURL(config.Conf.Service.Upload.URL + "test.png")}
	require.NoError(p.processImage())
	assert.Equal("oss://live/bubbles/notify/b1919_1_1_1_1.png", p.update["image"])

	r, err := http.Get("http://static-test.maoercdn.com/" + p.update["image"].(string)[6:])
	require.NoError(err)
	assert.Equal(http.StatusOK, r.StatusCode)
}

func TestEditBubbleParam_updateBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := bubble.Bubble{
		BubbleID: 1919,
	}
	p := editBubbleParam{
		BubbleID: b.BubbleID,
		update:   bson.M{"image": "test_image"},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := bubble.Collection().DeleteOne(ctx, bson.M{"bubble_id": p.BubbleID})
	require.NoError(err)
	require.NoError(p.updateBubble())
	key := keys.KeyBubbles.Format()
	service.Cache10s.Delete(key)
	bu, err := bubble.FindOne(p.BubbleID)
	require.NoError(err)
	assert.Nil(bu)
	_, err = bubble.Collection().InsertOne(ctx, b)
	require.NoError(err)
	require.NoError(p.updateBubble())
	service.Cache10s.Delete(key)
	bu, err = bubble.FindOne(p.BubbleID)
	require.NoError(err)
	assert.EqualValues("https://static-test.missevan.com/test_image", bu.Image)
}
