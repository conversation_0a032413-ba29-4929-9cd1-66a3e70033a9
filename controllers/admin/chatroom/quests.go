package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

type questDelParam struct {
	ID      string `form:"id" json:"id"`
	Confirm int    `form:"confirm" json:"confirm"`

	oid   primitive.ObjectID
	quest *quests.Quest
}

// ActionQuestDel 删除进房有奖配置
/**
 * @api {post} /api/v2/admin/chatroom/quest/del 删除配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} id 任务 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认删除该配置吗？"
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 */
func ActionQuestDel(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newQuestDelParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.delQuestByID()
	if err != nil {
		return nil, "", err
	}
	param.sendAdminLog(c)
	return nil, "删除成功", nil
}

func newQuestDelParam(c *handler.Context) (*questDelParam, error) {
	var param questDelParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

func (param *questDelParam) check() error {
	if param.ID == "" {
		return actionerrors.ErrParamsMsg("任务 ID 不可为空")
	}
	var err error
	param.oid, err = primitive.ObjectIDFromHex(param.ID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	quest, err := quests.FindQuestByOID(param.oid)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if quest == nil {
		return actionerrors.ErrNotFound("任务 ID 不存在")
	}
	if quest.IsFinished() {
		return actionerrors.ErrNotFound("不可删除已结束的任务")
	}
	param.quest = quest
	if param.Confirm == 0 {
		return actionerrors.ErrConfirmRequired("确认删除该配置吗？", 1)
	}
	return nil
}

func (param *questDelParam) delQuestByID() error {
	ok, err := quests.DelQuestByOID(param.oid)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrNotFound("任务 ID 不存在")
	}
	return nil
}

func (param *questDelParam) sendAdminLog(c *handler.Context) {
	box := goclient.NewAdminLogBox(c)
	message := fmt.Sprintf("删除配置 %s，奖励 ID: %d", param.ID, param.quest.RewardID)
	box.Add(userapi.CatalogManageQuests, message)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
