package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionChannelSet 设置直播间在推流
/**
 * @api {post} /api/v2/admin/chatroom/channel/set 设置直播间在推流
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 */
func ActionChannelSet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.IsPushing() {
		return nil, actionerrors.ErrRoomIsPushing
	}
	now := goutil.TimeNow()
	updates := bson.M{
		"status.channel": room.StatusChannel{
			Type:     room.TypeChannel,
			Event:    room.EventStart,
			Platform: "unset",
			Time:     util.TimeToUnixMilli(now),
		},
		"bstatus":      1,
		"updated_time": now,
	}
	r, err = room.Update(param.RoomID, updates)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = service.Redis.Del(keys.KeyRoomsRoomID1.Format(param.RoomID)).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// userID, ip, platform 使用默认值
	r.Channel.BuildAuthedURL(param.RoomID, r.CreatorID, 0, "127.0.0.1", nil, false, true)
	if r.Type == room.TypeLive {
		// NOTICE: 现在开播线上类型已统一为 connect, 兼容类型为 live 时 provider 统一下发 local
		r.Connect.Provider = room.ProviderLocal
	}

	payload := room.SetChannelNotify{
		StatusChannel: r.Status.Channel,
		RoomID:        r.RoomID,
		Channel:       r.Channel,
		Connect:       r.Connect,
	}
	err = userapi.Broadcast(r.RoomID, payload)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	intro := fmt.Sprintf("设置直播间在推流, 直播间 ID: %d", param.RoomID)
	adminLog := goclient.NewAdminLogBox(c)
	adminLog.Add(userapi.CatalogLiveSetChannel, intro)
	if err := adminLog.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}
