package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemedalreview"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionMedalPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	name := util.RandomCode(6)
	m := livemedalreview.LiveMedalReview{UserID: ExistsUserID, RoomID: 63888614, Name: name + "A"}
	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ? AND name = ?",
		m.UserID, livemedalreview.StatusReviewing, m.Name).FirstOrCreate(&m).Error)

	param := map[string]interface{}{"id": m.ID}
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/", tutil.ToRequestBody(param))
	c.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionMedalPass(c)
	require.NoError(err)

	// 勋章重名
	m = livemedalreview.LiveMedalReview{UserID: 111, RoomID: 111, Name: name + "a"}
	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ? AND name = ?", m.UserID, livemedalreview.StatusReviewing, m.Name).FirstOrCreate(&m).Error)
	param = map[string]interface{}{"id": m.ID}
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/", tutil.ToRequestBody(param))
	c.C.Request.Header.Add("Content-Type", "application/json")
	_, err = ActionMedalPass(c)
	assert.Equal(actionerrors.ErrMedalAlreadyExist, err)
}

func TestActionMedalRefuse(t *testing.T) {
	require := require.New(t)

	m := livemedalreview.LiveMedalReview{UserID: 123456789, RoomID: 1234567890, Name: "拒绝 A"}
	require.NoError(service.DB.Table(livemedalreview.TableName()).Where("user_id = ? AND status = ?",
		123456789, livemedalreview.StatusReviewing).FirstOrCreate(&m).Error)
	param := map[string]interface{}{"id": m.ID, "reason": "非法名称"}
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/", tutil.ToRequestBody(param))
	c.C.Request.Header.Add("Content-Type", "application/json")
	_, err := ActionMedalRefuse(c)
	require.NoError(err)
}

func TestNotifyMedalReview(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	after := &livemedalreview.LiveMedalReview{
		UserID: 55555,
		RoomID: 12345,
		Name:   "王炸",
		Status: livemedalreview.StatusPassed,
	}
	defer func() {
		require.NoError(service.DB.Table(messageassign.TableName()).
			Delete("", "recuid IN (?) AND title IN (?)", []int{55555, 10}, []string{"您的粉丝勋章申请已通过审核",
				"您的粉丝勋章名称修改申请已被通过", "您的粉丝勋章申请未通过审核", "您的粉丝勋章名称修改申请未通过审核"}).Error)
	}()
	systemParams := systemOperateParams{After: after}
	// 通过申请
	systemParams.notifyMedalReview()
	var message messageassign.MessageAssign
	require.NoError(service.DB.Order("id DESC").First(&message,
		"recuid = 55555 AND title = ?", "您的粉丝勋章申请已通过审核").Error)
	assert.Equal("您的粉丝勋章名称“王炸”已成功通过审核！快去提醒粉丝佩戴勋章吧~", message.Content)
	// 拒绝申请
	after.Status = livemedalreview.StatusRefused
	systemParams = systemOperateParams{After: after, Reason: "test"}
	systemParams.notifyMedalReview()
	message = messageassign.MessageAssign{}
	require.NoError(service.DB.Order("id DESC").First(&message,
		"recuid = 55555 AND title = ?", "您的粉丝勋章申请未通过审核").Error)
	assert.Equal("很遗憾 _(:3 」∠)_ 您的粉丝勋章名称“王炸”未通过审核。拒绝原因：test。", message.Content)
	// 通过修改申请
	after.UserID = 10
	after.RoomID = 22489473
	after.Status = livemedalreview.StatusPassed
	systemParams = systemOperateParams{After: after, HaveApply: true, UsedName: "小恶魔"}
	systemParams.notifyMedalReview()
	message = messageassign.MessageAssign{}
	require.NoError(service.DB.Order("id DESC").First(&message,
		"recuid = 10 AND title = ?", "您的粉丝勋章名称修改申请已被通过").Error)
	assert.Equal("您对粉丝勋章名称的修改已通过审核！成功由“小恶魔”修改为“王炸”。", message.Content)
	// 拒绝修改申请
	after.Status = livemedalreview.StatusRefused
	systemParams = systemOperateParams{After: after, HaveApply: true, Reason: "test"}
	systemParams.notifyMedalReview()
	message = messageassign.MessageAssign{}
	require.NoError(service.DB.Order("id DESC").First(&message,
		"recuid = 10 AND title = ?", "您的粉丝勋章名称修改申请未通过审核").Error)
	assert.Equal("很遗憾 _(:3 」∠)_ 您对粉丝勋章名称的修改未通过审核。拒绝原因：test。", message.Content)
}

func TestAdminMedalContent(t *testing.T) {
	assert := assert.New(t)
	s := systemOperateParams{
		After: &livemedalreview.LiveMedalReview{},
	}

	assert.PanicsWithValue("unsupported status: 0", func() {
		adminMedalContent(&s)
	})

	s.After.Status = livemedalreview.StatusPassed
	_, catalog := adminMedalContent(&s)
	assert.Equal(catalog, 89)

	s.After.Status = livemedalreview.StatusRefused
	_, catalog = adminMedalContent(&s)
	assert.Equal(catalog, 90)
}

func TestActionMedalList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "medal/list?type=test", false, nil)
	_, err := ActionMedalList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "medal/list?type=3&sort=errorTest", false, nil)
	_, err = ActionMedalList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("GET", "medal/list", false, nil)
	r, err := ActionMedalList(c)
	require.NoError(err)
	resp := r.(*medalListResp)
	assert.NotEmpty(resp.Data)

	c.C.Request, _ = http.NewRequest("GET", "medal/list?type=3&sort=create_time.desc", nil)
	r, err = ActionMedalList(c)
	require.NoError(err)
	respOther := r.(*medalListResp)
	assert.NotEmpty(respOther.Data)
	assert.GreaterOrEqual(len(resp.Data), len(respOther.Data))
}
