package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionTagRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	param := roomTagParams{
		TagID:   3,
		RoomIDs: "2233,2234,2236",
	}
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	r, err := ActionTagRoomAdd(c)
	require.NoError(err)
	assert.Equal("添加标签关联房间: 标签 3，直播间 2233,2234,2236", r)

	param = roomTagParams{
		TagID:   3,
		RoomIDs: "2233,2234,2236",
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	r, err = ActionTagRoomDel(c)
	require.NoError(err)
	assert.Equal("移除标签关联房间: 标签 3，直播间 2233,2234,2236", r)
}

func TestRoomTagParamsCheckParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{9999, 10000}}})
	require.NoError(err)

	var param roomTagParams
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.Equal(actionerrors.ErrParams, err)

	param.TagID = 1
	param.RoomIDs = "---"
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.EqualError(err, "新星标签不可操作")

	param.TagID = 999
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.EqualError(err, "标签不存在")

	param.TagID = 3
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.Equal(actionerrors.ErrParams, err)

	param.RoomIDs = "2233,2234,2234"
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.EqualError(err, "存在房间号重复，此次新增失败")

	param.RoomIDs = "2233,2234,9999,10000"
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newRoomTagParams(c)
	require.EqualError(err, "房间 9999、10000 不存在")

	param.RoomIDs = "2233,2234"
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	params, err := newRoomTagParams(c)
	require.Nil(err)
	assert.NotNil(params)
}

func TestRoomTagParams_addRecordAndBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(22489473)
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()
	err := liveroomtagrecord.DB().Where("room_id = ?", testRoomID).Delete(liveroomtagrecord.LiveRoomTagRecord{}).Error
	require.NoError(err)

	param := &roomTagParams{
		TagID:        tag.TagListenDrama,
		c:            handler.NewTestContext(http.MethodPost, "", true, nil),
		arrayRoomIDs: []int64{testRoomID},
		roomMap: map[int64]*room.Room{
			testRoomID: {
				Helper: room.Helper{
					RoomID:    testRoomID,
					CreatorID: 1,
					TagIDs:    []int64{},
				},
			},
		},
	}
	param.addRecordAndBroadcast(true)
	var list []*liveroomtagrecord.LiveRoomTagRecord
	err = liveroomtagrecord.DB().Where("operation = ? AND room_id = ?", liveroomtagrecord.OperationAdd, testRoomID).Find(&list).Error
	require.NoError(err)
	assert.Equal(1, len(list))

	param = &roomTagParams{
		TagID:        tag.TagListenDrama,
		c:            handler.NewTestContext(http.MethodPost, "", true, nil),
		arrayRoomIDs: []int64{testRoomID},
		roomMap: map[int64]*room.Room{
			testRoomID: {
				Helper: room.Helper{
					RoomID:    testRoomID,
					CreatorID: 1,
					TagIDs:    []int64{tag.TagListenDrama},
				},
			},
		},
	}
	param.addRecordAndBroadcast(false)
	list = list[:0]
	err = liveroomtagrecord.DB().Where("operation = ? AND room_id = ?", liveroomtagrecord.OperationRemove, testRoomID).Find(&list).Error
	require.NoError(err)
	assert.Equal(1, len(list))
}
