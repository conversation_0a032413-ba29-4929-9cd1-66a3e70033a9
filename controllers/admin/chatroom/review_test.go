package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionReviewList(t *testing.T) {
	assert := assert.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "image/list", nil)
	_, err := ActionReviewList(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "image/list?type=0", nil)
	r, err := ActionReviewList(c)
	require.NoError(t, err)
	resp := r.(*imageListResp)
	assert.NotEmpty(resp.Data)
}
