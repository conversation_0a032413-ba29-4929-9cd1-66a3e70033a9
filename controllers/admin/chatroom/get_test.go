package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	newC := func(uri string) *handler.Context {
		c := handler.CreateTestContext(true)
		c.C.Request, _ = http.NewRequest("GET", uri, nil)
		return c
	}
	_, err := ActionGet(newC("/get"))
	assert.Equal(actionerrors.ErrParams, err)
	r, err := ActionGet(newC("/get?creator_id=10"))
	require.NoError(err)
	resp := r.(*adminGetResp)
	assert.NotNil(resp.Room)
	assert.NotNil(resp.Vitality)
	r, err = ActionGet(newC(fmt.Sprintf("/get?room_id=%d", resp.RoomID)))
	require.NoError(err)
	resp = r.(*adminGetResp)
	assert.NotNil(resp.Room)
	assert.NotNil(resp.Vitality)
}
