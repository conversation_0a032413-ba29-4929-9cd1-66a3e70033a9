package chatroom

import (
	"fmt"
	"html"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// 任务类型
const (
	questTypeRoomView           = iota + 1 // 访问直播间
	questTypeRoomListenDuration            // 直播间连续收听时长（离开再进要重新统计）
)

type questImportParam struct {
	CSVURL    upload.SourceURL `form:"csv_url" json:"csv_url"`       // csv 地址
	Type      int              `form:"type" json:"type"`             // 任务类型 1: 访问直播间; 2: 直播间连续收听时长
	Intro     string           `form:"intro" json:"intro"`           // 任务描述
	StartTime int64            `form:"start_time" json:"start_time"` // 任务开始时间，单位：秒
	EndTime   int64            `form:"end_time" json:"end_time"`     // 任务结束时间，单位：秒
	Daily     bool             `form:"daily" json:"daily"`           // 是否每日刷新
	RewardID  int64            `form:"reward_id" json:"reward_id"`   // 奖励 ID
	Duration  int64            `form:"duration" json:"duration"`     // 直播间连续收听任务时长，单位：秒
	Confirm   int              `form:"confirm" json:"confirm"`       // 确认次数, 首次请求传 0

	roomIDs []int64
}

// ActionQuestImport 导入任务配置
/**
 * @api {post} /api/v2/admin/chatroom/quest/import 导入任务配置
 * @apiVersion 0.1.0
 * @apiGroup admin
 *
 * @apiParam {String} csv_url csv 地址，房间号列表
 * @apiParam {number=1,2} type 任务类型 1: 访问直播间; 2: 直播间连续收听时长
 * @apiParam {String} intro 任务描述
 * @apiParam {Number} start_time 任务开始时间，单位：秒
 * @apiParam {Number} end_time 任务结束时间，单位：秒
 * @apiParam {Boolean} daily 是否每日刷新
 * @apiParam {Number} reward_id 奖励 ID
 * @apiParam {Number} [duration] 直播间连续收听任务时长，单位：秒
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 导入成功:
 *   {
 *     "code": 0,
 *     "message": "导入成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认新增 2 条配置吗？<br>礼物名称: 梦想之声<br>奖励数量: 10<br>有效期: 86400 秒"
 *     }
 *   }
 *
 */
func ActionQuestImport(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newQuestImportParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.importQuest()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	param.sendAdminLog(c)
	return nil, "导入成功", nil
}

func newQuestImportParam(c *handler.Context) (*questImportParam, error) {
	var param questImportParam
	err := c.Bind(&param)
	if err != nil || param.CSVURL == "" {
		return nil, actionerrors.ErrParams
	}

	if param.Intro == "" || param.RewardID == 0 || param.StartTime == 0 || param.EndTime == 0 ||
		!goutil.HasElem([]int{questTypeRoomView, questTypeRoomListenDuration}, param.Type) {
		return nil, actionerrors.ErrParams
	}

	if param.StartTime >= param.EndTime {
		return nil, actionerrors.ErrParamsMsg("开始 / 结束时间有误，请检查后重试")
	}
	now := goutil.TimeNow().Unix()
	if param.StartTime < now || param.EndTime < now {
		return nil, actionerrors.ErrParamsMsg("开始 / 结束时间不能早于当前时间，请检查后重试")
	}

	if param.Type == questTypeRoomListenDuration && param.Duration <= 0 {
		return nil, actionerrors.ErrParamsMsg("直播间连续收听时长有误，请检查后重试")
	}

	r, err := reward.FindRewardByRewardIDWithCache(param.RewardID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrParamsMsg("奖励 ID 不存在，请检查后重试")
	}
	// TODO: 进房有奖只支持有持续时间的背包礼物奖励，后续支持其他类型奖励
	if r.Type != reward.TypeBackpack {
		return nil, actionerrors.ErrParamsMsg("只支持背包礼物奖励")
	}
	if r.Duration == 0 {
		return nil, actionerrors.ErrParamsMsg("只支持有持续时间的奖励")
	}

	g, err := gift.FindShowingGiftByGiftID(r.ElementID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 %d 不存在或已下架", r.ElementID))
	}

	param.roomIDs, err = param.readRoomIDByCSV()
	if err != nil {
		return nil, err
	}

	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认新增 %d 条配置吗？<br>"+
			"礼物名称: %s<br>"+
			"奖励数量: %d<br>"+
			"有效期: %d 秒",
			len(param.roomIDs), html.EscapeString(g.Name), r.ElementNum, r.Duration)
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return &param, nil
}

func (param *questImportParam) readRoomIDByCSV() ([]int64, error) {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	defer file.Close()

	allCSVRoomIDs, err := csv.ReadFirstColumnInt64(file)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(err.Error())
	}

	if err := checkRoomIDs(allCSVRoomIDs); err != nil {
		return nil, err
	}
	return allCSVRoomIDs, nil
}

func checkRoomIDs(csvRoomIDs []int64) error {
	uniqRoomIDs := sets.Uniq(csvRoomIDs)
	if len(uniqRoomIDs) != len(csvRoomIDs) {
		return actionerrors.ErrParamsMsg("存在重复的房间 ID，请检查后重试")
	}

	roomIDs, err := room.FindRoomIDs(bson.M{
		"room_id": bson.M{"$in": uniqRoomIDs},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	diffRoomIDs := sets.Diff(uniqRoomIDs, roomIDs)
	if len(diffRoomIDs) > 0 {
		return actionerrors.ErrParamsMsg(
			fmt.Sprintf("房间 %s 不存在, 请检查后重试", truncateElementsToString(diffRoomIDs)),
		)
	}
	return nil
}

func truncateElementsToString[T int | int64](elements []T) string {
	const limit = 5
	strList := make([]string, 0, limit+1)
	for i := 0; i < len(elements); i++ {
		if i >= limit {
			strList = append(strList, "...")
			break
		}
		strList = append(strList, fmt.Sprintf("%d", elements[i]))
	}
	return strings.Join(strList, ",")
}

func (param *questImportParam) importQuest() error {
	now := goutil.TimeNow().Unix()
	quest := quests.Quest{
		CreateTime:   now,
		ModifiedTime: now,
		Type:         quests.QuestTypeRoomView,
		StartTime:    param.StartTime,
		EndTime:      param.EndTime,
		Intro:        param.Intro,
		RewardID:     param.RewardID,
		Daily:        param.Daily,
	}
	if param.Type == questTypeRoomListenDuration {
		quest.Type = quests.QuestTypeRoomListenDuration
		quest.Duration = param.Duration
	}
	questList := make([]any, 0, len(param.roomIDs))
	for _, v := range param.roomIDs {
		quest.RoomID = v
		questList = append(questList, quest)
	}
	return quests.InsertQuests(questList)
}

func (param *questImportParam) sendAdminLog(c *handler.Context) {
	adminLogs := goclient.NewAdminLogBox(c)
	message := fmt.Sprintf("新增 %d 条数据，奖励 ID: %d", len(param.roomIDs), param.RewardID)
	adminLogs.Add(userapi.CatalogManageQuests, message)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
