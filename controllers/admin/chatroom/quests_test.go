package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionQuestsDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := quests.CollectionQuests()

	_, err = col.DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	now := util.TimeNow()
	_, err = col.InsertOne(ctx, bson.M{
		"_id":        oid,
		"start_time": now.Unix() - 20,
		"end_time":   now.Unix() - 10,
	})
	require.NoError(err)
	uri := "/api/v2/admin/chatroom/quest/del"

	c := handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      oid.Hex(),
		"confirm": 0,
	})
	_, _, err = ActionQuestDel(c)
	assert.Equal(err, actionerrors.ErrNotFound("不可删除已结束的任务"))

	_, err = col.UpdateOne(ctx,
		bson.M{"_id": oid},
		bson.M{"$set": bson.M{"end_time": now.Unix() + 10000}})
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      "",
		"confirm": 0,
	})
	_, _, err = ActionQuestDel(c)
	assert.Equal(err, actionerrors.ErrParamsMsg("任务 ID 不可为空"))

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      oid.Hex(),
		"confirm": 0,
	})
	_, _, err = ActionQuestDel(c)
	require.Equal(err, actionerrors.ErrConfirmRequired("确认删除该配置吗？", 1, false))

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      oid.Hex(),
		"confirm": 1,
	})
	_, msg, err := ActionQuestDel(c)
	require.NoError(err)
	assert.Equal("删除成功", msg)

	c = handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      oid.Hex(),
		"confirm": 1,
	})
	_, _, err = ActionQuestDel(c)
	assert.Equal(err, actionerrors.ErrNotFound("任务 ID 不存在"))
}

func TestNewQuestDelParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)
	uri := "/api/v2/admin/chatroom/quest/del"

	c := handler.NewTestContext(http.MethodPost, uri, true, handler.M{
		"id":      oid.Hex(),
		"confirm": 0,
	})
	param, err := newQuestDelParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(oid.Hex(), param.ID)
}

func TestQuestDelParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	// ID 为空
	param := &questDelParam{
		ID:      "",
		Confirm: 0,
	}
	err = param.check()
	assert.Equal(err, actionerrors.ErrParamsMsg("任务 ID 不可为空"))

	// ID 不合法
	param.ID = "34@23"
	err = param.check()
	assert.Error(err)

	// 任务不存在
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := service.MongoDB.Collection("quests")
	_, err = col.DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	param.ID = oid.Hex()
	err = param.check()
	assert.Equal(err, actionerrors.ErrNotFound("任务 ID 不存在"))

	// 任务已结束
	now := util.TimeNow()
	_, err = col.InsertOne(ctx, bson.M{
		"_id":        oid,
		"start_time": now.Unix() - 20,
		"end_time":   now.Unix() - 10,
	})
	require.NoError(err)
	err = param.check()
	assert.Equal(err, actionerrors.ErrNotFound("不可删除已结束的任务"))

	// 确认删除消息
	_, err = col.UpdateOne(ctx,
		bson.M{"_id": oid},
		bson.M{"$set": bson.M{"end_time": now.Unix() + 10000}})
	require.NoError(err)
	err = param.check()
	require.Equal(err, actionerrors.ErrConfirmRequired("确认删除该配置吗？", 1, false))

	// 正常
	param.Confirm = 1
	err = param.check()
	assert.NoError(err)
}

func TestQuestDelParam_delQuestByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := quests.CollectionQuests()
	_, err = col.DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	now := util.TimeNow()
	_, err = col.InsertOne(ctx, bson.M{
		"_id":        oid,
		"start_time": now.Unix(),
		"end_time":   now.Unix() + 10000,
	})
	require.NoError(err)
	param := &questDelParam{
		ID:      oid.Hex(),
		Confirm: 0,
		oid:     oid,
	}
	err = param.delQuestByID()
	assert.NoError(err)
}
