package chatroom

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type adminGetResp struct {
	*room.Room
	Vitality *int          `json:"vitality"`
	Ban      *livemeta.Ban `json:"ban,omitempty"`
}

// ActionGet 超管通过房间号或主播 ID 获取直播间
/**
 * @api {get} /api/v2/admin/chatroom/get 获取直播间
 * @apiDescription 超管通过房间号或用户 ID 获取直播间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} [room_id] 房间号
 * @apiParam {Number} [creator_id] 主播 ID，房间号和主播 ID 都有以房间号为准
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room_id": 152,
 *       "catalog_id": 107,
 *       "name": "12345",
 *       "announcement": "12345",
 *       "creator_id": 12345,
 *       "creator_username": "1234",
 *       "creator_iconurl": "https://static.maoercdn.com/avatars/icon01.png",
 *       "background": {
 *         "enable": false,
 *         "opacity": 1
 *       },
 *       "statistics": {
 *         "accumulation": 123,
 *         ...
 *       },
 *       "medal": {
 *         "name": "medal_name"
 *       },
 *       "status": {
 *         "open": 1
 *         ...
 *       },
 *       "vitality": 12,
 *       "ban": { // 未被封禁时，无该字段
 *         "type": 1, // 0: 永封, 1: 按时间封禁
 *         "duration": 60, // 封禁时长 单位：秒
 *         "start_time": 1584808200,
 *         "expire_time": 1584808800 // 永封无该字段
 *       }
 *     }
 *   }
 *
 */
func ActionGet(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	creatorID, _ := c.GetParamInt64("creator_id")
	opt := &room.FindOptions{FindCreator: true}
	resp := new(adminGetResp)
	var err error
	if roomID > 0 {
		resp.Room, err = room.Find(roomID, opt)
	} else if creatorID > 0 {
		resp.Room, err = room.FindOne(bson.M{"creator_id": creatorID}, opt)
	} else {
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.Room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	resp.Vitality, err = liveaddendum.Vitality(resp.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if resp.Vitality == nil && err == nil {
		logger.Warnf("room %d (creator id: %d) doesn't have vitality",
			resp.RoomID, resp.CreatorID)
	}

	resp.Ban, err = livemeta.FindBanned(resp.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}
