package chatroom

import (
	"errors"
	"fmt"
	"html"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

type questionCancelParam struct {
	Data   []questionCancelElement `form:"data" json:"data"`
	Reason string                  `form:"reason" json:"reason"`

	c           *handler.Context
	questionOID primitive.ObjectID
	room        *room.Room
	question    *livequestion.LiveQuestion
	adminBox    *goclient.AdminLogBox
}

type questionCancelElement struct {
	RoomID     int64  `form:"room_id" json:"room_id"`
	QuestionID string `form:"question_id" json:"question_id"`
}

// ActionQuestionCancel 取消提问
/**
 * @api {post} /api/v2/admin/chatroom/question/cancel 对主播直播间提问进行取消操作
 * @apiDescription 主播直播间对提问进行操作
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Object[]} data 该字段包含一个对象，对象中有需要删除的提问 ID question_id 以及对应的房间号 room_id
 * @apiParam {String} reason 删除理由
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     {
 *       "type": "question",
 *       "event": "answer",
 *       "answer_type": "cancel",
 *       "room_id": 10659544,
 *       "question": {
 *         "question_id": "5ef2fd3e2beff02c36bbfa79",
 *         "status": 3 // 0：未回答，1：正在回答，2：已回答，3：已取消
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionQuestionCancel(c *handler.Context) (handler.ActionResponse, error) {
	var param questionCancelParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	err = param.cancelQuestion()
	if err != nil {
		return nil, err
	}

	return "删除成功", nil
}

func findRoom(roomID int64) (*room.Room, error) {
	r, err := room.Find(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return r, nil
}

func (param *questionCancelParam) load(c *handler.Context) error {
	param.c = c
	err := param.c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	if len(param.Data) != 1 || param.Data[0].RoomID <= 0 {
		return actionerrors.ErrParams
	}

	param.questionOID, err = primitive.ObjectIDFromHex(param.Data[0].QuestionID)
	if err != nil {
		return actionerrors.ErrParams
	}

	param.room, err = findRoom(param.Data[0].RoomID)
	if err != nil {
		return err
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}

	param.question, err = livequestion.FindOne(bson.M{
		"_id":     param.questionOID,
		"room_id": param.Data[0].RoomID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.question == nil {
		return actionerrors.ErrNotFound("提问不存在")
	}
	if !param.question.IsUserVisible() {
		return actionerrors.ErrNotFound("提问已被取消或是隐藏")
	}

	param.adminBox = goclient.NewAdminLogBox(param.c)
	return nil
}

func (param *questionCancelParam) cancelQuestion() error {
	setStatus := livequestion.StatusCanceled
	// NOTICE: 如果是已回答的提问，应将状态设定为 StatusHidden（已隐藏）
	if param.question.Status == livequestion.StatusFinished {
		setStatus = livequestion.StatusHidden
	}

	success, err := livequestion.CancelQuestion(
		bson.M{
			"_id":     param.question.OID,
			"room_id": param.room.RoomID,
			"status":  bson.M{"$lt": livequestion.StatusCanceled},
		},
		setStatus)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		return actionerrors.NewErrServerInternal(errors.New("提问删除失败"), nil)
	}

	sysMsgFormat := "您好，您在主播 %s 直播间的提问“%s”已被管理员删除，删除原因为：%s。"
	// NOTICE: 如果当前操作的不是已回答的提问并且有交易记录，调用 RPC
	// 如：压测等测试数据会存在 TransactionID 为 0 的情况
	if param.question.Status != livequestion.StatusFinished && param.question.TransactionID != 0 {
		sysMsgFormat += "提问的钻石将返还至您的账户，请注意查收。"
		resp, err := userapi.CancelAsks(
			param.room.CreatorID, []int64{param.question.TransactionID}, userapi.NewUserContext(param.c))
		if err != nil {
			return err
		}

		// 检查并记录 cancel ask 中遇到的 resp.error
		livequestion.LogCancelAsks([]int64{param.question.TransactionID}, resp)
	}

	// 发送广播
	err = userapi.Broadcast(param.room.RoomID, map[string]interface{}{
		"type":        liveim.TypeQuestion,
		"event":       liveim.EventAnswer,
		"answer_type": liveim.AnswerTypeCancel,
		"room_id":     param.room.RoomID,
		"question": map[string]interface{}{
			// WORKAROUND: 兼容安卓 5.5.7 iOS 4.6.8 之前的老版本，之后的版本不再使用 _id 字段
			"_id":         param.Data[0].QuestionID,
			"question_id": param.Data[0].QuestionID,
			"status":      livequestion.StatusCanceled, // 无论是否隐藏，此处只会下发已取消
		},
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 发送系统通知
	sysMsg := []pushservice.SystemMsg{{
		Title:   "直播间提问删除通知",
		UserID:  param.question.UserID,
		Content: fmt.Sprintf(sysMsgFormat, html.EscapeString(param.room.CreatorUsername), html.EscapeString(param.question.Question), html.EscapeString(param.Reason)),
	}}
	err = service.PushService.SendSystemMsg(sysMsg)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 记录管理员日志
	param.sendAdminLog(param.question, param.room)

	return nil
}

func (param *questionCancelParam) sendAdminLog(question *livequestion.LiveQuestion, room *room.Room) {
	param.adminBox.Add(userapi.CatalogManageLiveQuestion,
		fmt.Sprintf("删除直播间提问，问题 ID：%s，问题内容：%s，价格（钻）：%d，删除时状态：%d，提问用户M号：%d，房间号：%d，主播M号：%d，提问时间：%s，删除理由：%s",
			question.OID.Hex(), question.Question, question.Price, question.Status,
			question.UserID, question.RoomID, room.CreatorID, question.CreatedTime.Format(util.TimeFormatYMDHMS), param.Reason))
	err := param.adminBox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
