package chatroom

import (
	"fmt"
	"html"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type speakManageParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	Confirm int   `form:"confirm" json:"confirm"`

	// 设置用
	All        int `form:"all" json:"all"`
	LiveLevel  int `form:"live_level" json:"live_level"`
	MedalLevel int `form:"medal_level" json:"medal_level"`
	SuperFan   int `form:"super_fan" json:"super_fan"`

	c    *handler.Context
	room *room.Room
	ss   *livemeta.SpeakSettings
}

// newSpeakSetParam 设置接口参数
func newSpeakSetParam(c *handler.Context) (*speakManageParam, error) {
	var param speakManageParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrParamsMsg("请输入有效的房间号")
	}
	param.ss = &livemeta.SpeakSettings{
		Enable:    true,
		All:       util.IntToBool(param.All),
		LiveLevel: param.LiveLevel,
	}
	if !param.ss.All && param.LiveLevel <= 0 && param.MedalLevel <= 0 {
		return nil, actionerrors.ErrParamsMsg("发言权限设置无效")
	}
	if param.MedalLevel > 0 {
		if param.room.Medal == nil {
			return nil, actionerrors.ErrParamsMsg("该房间未开通粉丝勋章，无法设置粉丝等级限制")
		}
		param.ss.Medal = &livemeta.MedalInfo{
			Level:    param.MedalLevel,
			SuperFan: util.IntToBool(param.SuperFan),
		}
	}
	return &param, nil
}

func newSpeakDelParam(c *handler.Context) (*speakManageParam, error) {
	var param speakManageParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrParamsMsg("请输入有效的房间号")
	}

	param.ss, err = livemeta.FindSpeakSettings(param.room.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.ss == nil || !param.ss.Enable {
		return nil, actionerrors.ErrParamsMsg("该直播间未设置发言权限，无法取消")
	}
	return &param, nil
}

func (param *speakManageParam) CheckConfirm() error {
	if param.Confirm == 1 {
		return nil
	}
	msg := fmt.Sprintf("房间号：%d<br>主播：%s", param.room.RoomID,
		html.EscapeString(param.room.CreatorUsername))
	if param.ss.All {
		msg += "<br>全部禁言：开"
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	msg += "<br>全部禁言：关"
	if param.ss.LiveLevel <= 0 {
		msg += "<br>直播等级限制：无"
	} else {
		msg += fmt.Sprintf("<br>直播等级限制：%d 级及以上", param.ss.LiveLevel)
	}
	if m := param.ss.Medal; m == nil {
		msg += "<br>粉丝等级限制：无"
	} else {
		msg += fmt.Sprintf("<br>粉丝等级限制：%d 级及以上", m.Level)
		if m.SuperFan {
			msg += "且为超粉"
		}
	}
	return actionerrors.ErrConfirmRequired(msg, 1, true)
}

func (param *speakManageParam) Set() error {
	err := livemeta.SetSpeakSettings(param.room.RoomID, param.ss)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *speakManageParam) Del() error {
	err := livemeta.SetSpeakSettings(param.room.RoomID, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *speakManageParam) Log(action string) string {
	/* 文案
	设置发言权限：[房间号]，[【全部禁言】开 or 关]，[【直播等级限制】选项]，[【粉丝等级限制】选项]
	删除发言权限：[房间号]，[【全部禁言】开 or 关]，[【直播等级限制】选项]，[【粉丝等级限制】选项]
	*/
	msg := fmt.Sprintf("%s：%d", action, param.room.RoomID)
	if param.ss.All {
		msg += "，【全部禁言】开"
	} else {
		msg += "，【全部禁言】关"
		if param.ss.LiveLevel > 0 {
			msg += fmt.Sprintf("，【直播等级限制】%d 级及以上", param.ss.LiveLevel)
		} else {
			msg += "，【直播等级限制】无"
		}

		if param.ss.Medal == nil {
			msg += "，【粉丝等级限制】无"
		} else {
			msg += fmt.Sprintf("，【粉丝等级限制】%d 级及以上", param.ss.Medal.Level)
			if param.ss.Medal.SuperFan {
				msg += "且为超粉"
			}
		}
	}

	box := userapi.NewAdminLogBox(param.c)
	box.AddAdminLog(msg, userapi.CatalogManageSpeak)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return msg
}

// ActionSpeakSet 设置房间发言权限
/**
 * @api {post} /api/v2/admin/chatroom/speak/set 设置房间发言权限
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [confirm=0] 弹窗控制参数
 * @apiParam {Number=0,1} all 全部禁言
 * @apiParam {Number} [live_level=0] 等级限制
 * @apiParam {Number} medal_level 粉丝等级限制
 * @apiParam {Number} super_fan 是否限制超粉，只有当 medal_level 不为 0 时生效
 *
 * @apiSuccessExample 确认设置发言权限弹窗:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "房间号：123456<br/>主播：XXX<br/>..."
 *     }
 *   }
 *
 * @apiSuccessExample 设置成功:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 */
func ActionSpeakSet(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSpeakSetParam(c)
	if err != nil {
		return nil, err
	}
	err = param.CheckConfirm()
	if err != nil {
		return nil, err
	}
	err = param.Set()
	if err != nil {
		return nil, err
	}
	param.Log("设置发言权限")
	return "设置成功", nil
}

// ActionSpeakDel 删除房间发言权限
/**
 * @api {post} /api/v2/admin/chatroom/speak/del 删除房间发言权限
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [confirm=0] 弹窗控制参数
 *
 * @apiSuccessExample 确认设置发言权限弹窗:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "房间号：123456<br/>主播：XXX<br/>..."
 *     }
 *   }
 *
 * @apiSuccessExample 设置成功:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 */
func ActionSpeakDel(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSpeakDelParam(c)
	if err != nil {
		return nil, err
	}
	err = param.CheckConfirm()
	if err != nil {
		return nil, err
	}
	err = param.Del()
	if err != nil {
		return nil, err
	}
	param.Log("删除发言权限")
	return "删除成功", nil
}
