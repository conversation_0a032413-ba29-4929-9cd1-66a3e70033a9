package chatroom

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	newC := func(param map[string]interface{}) *handler.Context {
		c := handler.CreateTestContext(true)
		c.C.Request, _ = http.NewRequest("POST", "/chatroom/close", tutil.ToRequestBody(param))
		c.C.Request.Header.Add("Content-Type", "application/json")
		return c
	}
	_, err := ActionClose(newC(nil))
	assert.Equal(actionerrors.ErrParams, err)
	_, err = ActionClose(newC(map[string]interface{}{"room_id": 99999999, "reason": "test"}))
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	roomID := int64(18113499)
	r, err := room.Update(roomID, bson.M{"status.open": room.StatusOpenTrue, "status.open_log_id": primitive.NewObjectID()})
	require.NoError(err)
	resp, err := ActionClose(newC(map[string]interface{}{"room_id": r.RoomID, "reason": "test"}))
	require.NoError(err)
	assert.Equal("切断成功", resp)
	resp, err = ActionClose(newC(map[string]interface{}{"room_id": r.RoomID, "reason": "test"}))
	require.NoError(err)
	assert.Equal("房间未开启，仅清空提问和连麦列表", resp)
}
