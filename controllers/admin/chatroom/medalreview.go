package chatroom

import (
	"fmt"
	"html"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemedalreview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const defaultMedalListPageFifty = 50

type systemOperateParams struct {
	C *handler.Context

	Reason    string
	UsedName  string
	HaveApply bool

	After *livemedalreview.LiveMedalReview
}

// ActionMedalPass 超管通过主播粉丝勋章审核
/**
 * @api {post} /api/v2/admin/chatroom/medal/pass 超管通过主播粉丝勋章审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} id 勋章审核 ID
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMedalPass(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		ID     int64 `form:"id" json:"id"`
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	haveApply, usedName := room.HaveMedal(params.RoomID)

	m, err := livemedalreview.FindOne(params.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if m == nil {
		return nil, actionerrors.ErrNotFound("无法找到该勋章")
	}
	// 勋章名称校验
	exist, err := room.MedalExists(m.Name, m.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exist {
		return nil, actionerrors.ErrMedalAlreadyExist
	}

	after, err := livemedalreview.UpdateMedal(params.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if after == nil {
		return nil, actionerrors.ErrNotFound("无法找到该勋章")
	}
	systemParams := systemOperateParams{C: c, After: after, UsedName: usedName, HaveApply: haveApply}
	// 发送系统私信和管理员操作日志
	systemParams.sendSystemOperateLog()

	return "success", nil
}

// ActionMedalRefuse 超管拒绝主播粉丝勋章审核
/**
 * @api {post} /api/v2/admin/chatroom/medal/refuse 超管拒绝主播粉丝勋章审核
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} id 勋章审核 ID
 * @apiParam {String} reason 拒绝原因
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMedalRefuse(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		ID     int64  `form:"id" json:"id"`
		Reason string `form:"reason" json:"reason"`
	}
	err := c.Bind(&params)
	if err != nil || params.ID <= 0 || params.Reason == "" {
		return nil, actionerrors.ErrParams
	}

	after, err := livemedalreview.FinishReviewing(params.ID, livemedalreview.StatusRefused, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if after == nil {
		return nil, actionerrors.ErrNotFound("无法找到该勋章")
	}

	systemParams := systemOperateParams{C: c, After: after, Reason: params.Reason}
	// 发送系统私信和管理员操作日志
	systemParams.sendSystemOperateLog()

	return "success", nil
}

const (
	sortCreateTime     = "create_time"
	sortCreateTimeDesc = "create_time.desc"
)

var sortMedalMap = map[string]string{
	sortCreateTime:     livemedalreview.CreateTimeAsc,
	sortCreateTimeDesc: livemedalreview.CreateTimeDesc,
}

type medalListResp struct {
	Data       []*livemedalreview.WithUserInfo `json:"Datas"`
	Pagination goutil.Pagination               `json:"pagination"`
}

// ActionMedalList 超管审核主播粉丝勋章列表
/**
 * @api {get} /api/v2/admin/chatroom/medal/list 超管审核主播粉丝勋章列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=50] page size
 * @apiParam {Number} [user_id] 主播 ID
 * @apiParam {string} [medal_name] 勋章名称
 * @apiParam {string} [username] 主播昵称
 * @apiParam {Number} [type=0] 状态，0：全部，1：待审核，2：已拒绝，3：已通过
 * @apiParam {string} [sort=create_time.desc] 排序方式: create_time, create_time.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "id": 12,
 *           "username": "主播昵称",
 *           "user_id": "主播 ID",
 *           "name": 123, // 勋章名称
 *           "create_time": 1234567890,
 *           "status": 0, // -1: 已拒绝, 0 审核中, 1 通过
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 50
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMedalList(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage(&handler.PageOption{DefaultPageSize: defaultMedalListPageFifty})
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	opt := livemedalreview.FindOptions{}
	err = c.C.BindQuery(&opt)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if opt.Sort == "" {
		opt.Sort = livemedalreview.CreateTimeDesc
	} else {
		sort, ok := sortMedalMap[opt.Sort]
		if !ok {
			return nil, actionerrors.ErrParams
		}
		opt.Sort = sort
	}

	resp := new(medalListResp)
	resp.Data, resp.Pagination, err = livemedalreview.FindMedalReview(p, pageSize, opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}

func (s *systemOperateParams) notifyMedalReview() {
	var title, content string
	status := s.After.Status
	switch {
	case status == livemedalreview.StatusPassed && !s.HaveApply:
		title = "您的粉丝勋章申请已通过审核"
		content = fmt.Sprintf("您的粉丝勋章名称“%s”已成功通过审核！快去提醒粉丝佩戴勋章吧~", html.EscapeString(s.After.Name))
	case status == livemedalreview.StatusPassed && s.HaveApply:
		title = "您的粉丝勋章名称修改申请已被通过"
		content = fmt.Sprintf("您对粉丝勋章名称的修改已通过审核！成功由“%s”修改为“%s”。", html.EscapeString(s.UsedName), html.EscapeString(s.After.Name))
	case status == livemedalreview.StatusRefused && !s.HaveApply:
		title = "您的粉丝勋章申请未通过审核"
		content = fmt.Sprintf("很遗憾 _(:3 」∠)_ 您的粉丝勋章名称“%s”未通过审核。拒绝原因：%s。", html.EscapeString(s.After.Name), html.EscapeString(s.Reason))
	case status == livemedalreview.StatusRefused && s.HaveApply:
		title = "您的粉丝勋章名称修改申请未通过审核"
		content = fmt.Sprintf("很遗憾 _(:3 」∠)_ 您对粉丝勋章名称的修改未通过审核。拒绝原因：%s。", html.EscapeString(s.Reason))
	}

	err := messageassign.SystemMessageAssign(s.After.UserID, title, content)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func adminMedalContent(s *systemOperateParams) (string, int) {
	// 操作日志
	var catalog int
	var intro string
	switch s.After.Status {
	case livemedalreview.StatusPassed:
		catalog = userapi.PassLiveMedal
		intro = fmt.Sprintf("审核通过粉丝勋章，粉丝勋章名称：%s", s.After.Name)
	case livemedalreview.StatusRefused:
		catalog = userapi.RefuseLiveMedal
		intro = fmt.Sprintf("审核拒绝粉丝勋章，粉丝勋章名称：%s，拒绝原因：%s", s.After.Name, s.Reason)
	default:
		panic(fmt.Sprintf("unsupported status: %d", s.After.Status))
	}
	return intro, catalog
}

func (s *systemOperateParams) sendSystemOperateLog() {
	// 发送系统私信
	s.notifyMedalReview()
	// 管理员操作日志
	adminLogs := userapi.NewAdminLogBox(s.C)
	adminLogs.AddAdminLog(adminMedalContent(s))
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
