package chatroom

import (
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 重置直播间名称类型
const (
	TypeNameWithUsername = iota
	TypeNameWithUserID
)

// 重置直播间信息类型
const (
	ElemResetName       = "name"
	ElemResetCover      = "cover"
	ElemResetBackground = "background"
	ElemResetIntro      = "intro"
)

type resetParam struct {
	RoomIDs string   `form:"room_ids" json:"room_ids"`
	Type    int      `form:"type" json:"type"`
	Elems   []string `form:"elems" json:"elems"`

	c       *handler.Context
	roomIDs []int64
	rooms   []*room.Simple
	elemMap map[string]struct{}
}

// ActionReset 重置直播间信息
/**
 * @api {post} /api/v2/admin/chatroom/reset 重置直播间信息
 * @apiDescription 重置直播间名称、封面图、背景图、简介每次最多支持同时重置 100 个
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} room_ids 房间号, 批量修改使用半角逗号分割, 如: 123,234,456
 * @apiParam {number=0,1} [type=0] 重置名称类型 0: {username}的直播间, 1: 主播 {userID} 的直播间
 * @apiParam {String[]} elems 需要重置的元素 ["name", "cover", "background", "intro"]
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "重置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionReset(c *handler.Context) (handler.ActionResponse, error) {
	var param resetParam
	if err := param.load(c); err != nil {
		return nil, err
	}
	if err := param.reset(); err != nil {
		return nil, err
	}

	return "重置成功", nil
}

func (p *resetParam) load(c *handler.Context) (err error) {
	err = c.Bind(p)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.c = c
	if p.RoomIDs == "" || len(p.Elems) == 0 || (p.Type != TypeNameWithUserID && p.Type != TypeNameWithUsername) {
		return actionerrors.ErrParams
	}

	p.elemMap = make(map[string]struct{})
	for _, elem := range p.Elems {
		p.elemMap[elem] = struct{}{}
	}

	p.roomIDs, err = goutil.SplitToInt64Array(p.RoomIDs, ",")
	if err != nil || len(p.roomIDs) == 0 {
		return actionerrors.ErrParams
	}
	p.rooms, err = room.ListSimples(bson.M{"room_id": bson.M{"$in": p.roomIDs}},
		nil, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.rooms) != len(p.roomIDs) {
		return actionerrors.ErrParamsMsg("部分直播间不存在，请检查后重新输入")
	}

	return nil
}

func (p *resetParam) reset() (err error) {
	now := goutil.TimeNow()
	nowUnix := now.Unix()
	for _, r := range p.rooms {
		mongoSet := bson.M{}
		updates := handler.M{}
		if _, ok := p.elemMap[ElemResetName]; ok {
			newName := p.newName(r)
			mongoSet["name"] = newName
			mongoSet["name_clean"] = newName
			updates["title"] = newName
		}

		if _, ok := p.elemMap[ElemResetIntro]; ok {
			mongoSet["announcement"] = ""
			updates["intro"] = ""
		}

		if _, ok := p.elemMap[ElemResetCover]; ok {
			mongoSet["cover"] = nil
		}
		if _, ok := p.elemMap[ElemResetBackground]; ok {
			mongoSet["background"] = nil
		}
		if len(updates) > 0 {
			updates["modified_time"] = nowUnix
			// 更新 live 表
			err := service.DB.Table(live.TableName()).Where("room_id = ?", r.RoomID).Updates(updates).Error
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
		}
		mongoSet["updated_time"] = now
		_, err = room.Update(r.RoomID, mongoSet)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		intro := fmt.Sprintf("重置直播间 %s, 房间 ID: %d", strings.Join(p.Elems, ", "), r.RoomID)
		adminLogs := userapi.NewAdminLogBox(p.c)
		adminLogs.AddAdminLog(intro, userapi.CatalogLiveResetInfo)
		// 管理员操作日志
		// TODO: missevan-go 需要支持 send 成功之后清空队列
		err = adminLogs.Send()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return
}

func (p *resetParam) newName(r *room.Simple) string {
	switch p.Type {
	case TypeNameWithUsername:
		return fmt.Sprintf("%s的直播间", r.CreatorUsername)
	case TypeNameWithUserID:
		return fmt.Sprintf("主播 %d 的直播间", r.CreatorID)
	default:
		panic(fmt.Sprintf("unsupported type: %d", p.Type))
	}
}
