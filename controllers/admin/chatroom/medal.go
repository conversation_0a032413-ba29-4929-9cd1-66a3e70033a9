package chatroom

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
)

// ActionSetmedal 设置房间的勋章
/**
 * @api {post} /api/v2/admin/chatroom/setmedal 超管设置房间的勋章
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} name 勋章名称
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_id": 123,
 *     "name": "勋章名"
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionSetmedal(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64  `json:"room_id"`
		Name   string `json:"name"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.Name == "" || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID,
		&room.FindOptions{
			DisableAll: true,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.Limit != nil {
		return nil, actionerrors.ErrLimitedRoom
	}
	if r.Statistics.Revenue < livemedal.RevenueThreshold {
		return nil, actionerrors.ErrParamsMsg("该房间无法设置勋章")
	}
	exists, err := room.MedalExists(param.Name, r.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrMedalAlreadyExist
	}
	now := goutil.TimeNow()
	update := bson.M{"medal": room.NewMedal(param.Name), "updated_time": now}
	_, err = room.Update(param.RoomID, update)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = livemedal.UpdateName(param.RoomID, param.Name)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 发送管理员操作日志
	adminLogs := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置直播间粉丝勋章，房间号：%d, 勋章名称: %s", param.RoomID, param.Name)
	adminLogs.AddAdminLog(intro, userapi.PassLiveMedal)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

type multiAddParams struct {
	CreatorID         int64            `json:"creator_id"`
	CSVURL            upload.SourceURL `json:"csv_url"` // CSV 文件地址
	PointMultiAdd     int              `json:"point_multi_add"`
	ThresholdMultiAdd int              `json:"threshold_multi_add"`
	StartTime         int64            `json:"start_time"`
	EndTime           int64            `json:"end_time"`
	Confirm           int              `json:"confirm"`

	room       *room.Room
	rooms      []*room.Room
	creatorIDs []int64
}

// ActionMedalMultiAdd 设置直播间亲密度及上限倍数
/**
 * @api {post} /api/v2/admin/chatroom/medal/add-multi 设置直播间亲密度及上限倍数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} [creator_id=0] 主播用户 ID, 值为 0 时表示全站
 * @apiParam {String} csv_url CSV 文件地址，与主播用户 ID 二选一
 * @apiParam {Number} [point_multi_add=0] 亲密度加的倍数
 * @apiParam {Number} [threshold_multi_add=0] 亲密度上限加的倍数
 * @apiParam {Number} start_time 开始时间，秒级时间戳
 * @apiParam {Number} end_time 结束时间，秒级时间戳
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "确认为主播 test (用户 ID: 111) 配置亲密度倍数增加: 1, 上限增加: 0, 时间: 2006-01-02 15:04:05 - 2006-01-03 15:04:05 吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalMultiAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param multiAddParams
	err := c.Bind(&param)
	if err != nil || param.CreatorID < 0 || param.PointMultiAdd < 0 || param.ThresholdMultiAdd < 0 ||
		(param.PointMultiAdd == 0 && param.ThresholdMultiAdd == 0) {
		return nil, actionerrors.ErrParams
	}
	if param.CreatorID != 0 && param.CSVURL != "" {
		// 填写名单行为与上传名单行为互斥
		return nil, actionerrors.ErrParamsMsg("名单输入只能选择一个入口")
	}
	if param.CSVURL == "" {
		// CreatorID 为 0 时表示全站
		if param.CreatorID != 0 {
			param.room, err = room.FindOne(bson.M{"creator_id": param.CreatorID}, &room.FindOptions{DisableAll: true})
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			if param.room == nil {
				return nil, actionerrors.ErrCannotFindRoom
			}
		}
		param.creatorIDs = append(param.creatorIDs, param.CreatorID)
	} else {
		// 批量上传的主播都是配置同样的时间段，若需要配置不同时间段需要分开配置
		err = param.parseCreatorIDsInCSV()
		if err != nil {
			return nil, err
		}
		param.rooms, err = room.FindAll(bson.M{"creator_id": bson.M{"$in": param.creatorIDs}})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if len(param.rooms) != len(param.creatorIDs) {
			return nil, actionerrors.ErrParamsMsg("csv 名单有误")
		}
	}

	if param.Confirm == 0 {
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确认为%s配置亲密度倍数增加: %d, 上限增加: %d, 时间: %s - %s 吗？", param.buildMessageArgs()...), 1)
	}

	var multi liverecommendedelements.PointMulti
	if param.PointMultiAdd > 0 {
		multi.PointMultiAdd = int64(param.PointMultiAdd)
	}
	if param.ThresholdMultiAdd > 0 {
		multi.ThresholdMultiAdd = int64(param.ThresholdMultiAdd)
	}
	err = liverecommendedelements.BatchAddRoomMedalPointMulti(param.creatorIDs, time.Unix(param.StartTime, 0), time.Unix(param.EndTime, 0), multi)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	adminLog := goclient.NewAdminLogBox(c)
	adminLog.Add(userapi.CatalogManageRoomMedal,
		fmt.Sprintf("设置直播间勋章亲密度倍数, 为%s配置亲密度倍数增加: %d, 上限增加: %d, 时间: %s - %s", param.buildMessageArgs()...))
	err = adminLog.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

func (param *multiAddParams) parseCreatorIDsInCSV() error {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 文件读取失败，请检查文件后再试")
	}
	defer file.Close()
	param.creatorIDs, err = csv.ReadFirstColumnInt64(file)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.creatorIDs) == 0 {
		return actionerrors.ErrParamsMsg("csv 名单为空")
	}
	if len(param.creatorIDs) != len(util.Uniq(param.creatorIDs)) {
		return actionerrors.ErrParamsMsg("存在重复的M号，请检查后重试")
	}
	return nil
}

func (param *multiAddParams) buildMessageArgs() []any {
	var addCreatorsInfo string
	switch {
	case param.room != nil:
		addCreatorsInfo = fmt.Sprintf("主播 %s (用户 ID: %d) ", param.room.CreatorUsername, param.room.CreatorID)
	case len(param.rooms) > 0:
		addCreatorsInfo = fmt.Sprintf("上传的 %d 位主播", len(param.rooms))
	default:
		addCreatorsInfo = "全站"
	}
	return []any{
		addCreatorsInfo,
		param.PointMultiAdd, param.ThresholdMultiAdd,
		time.Unix(param.StartTime, 0).Format(util.TimeFormatYMDHMS), time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHMS),
	}
}

// ActionMedalMultiRemove 移除直播间亲密度及上限倍数
/**
 * @api {post} /api/v2/admin/chatroom/medal/remove-multi 移除直播间亲密度及上限倍数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} record_id 记录 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "移除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalMultiRemove(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RecordID int64 `json:"record_id"`
	}
	err := c.Bind(&param)
	if err != nil || param.RecordID <= 0 {
		return nil, actionerrors.ErrParams
	}
	elem, err := liverecommendedelements.FindOneAndDelete(param.RecordID, liverecommendedelements.ElementMultiMedalPoint)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if elem == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	adminLog := goclient.NewAdminLogBox(c)
	intro := "移除直播间勋章亲密度倍数及上限倍数, "
	if elem.ElementID == 0 {
		intro += "全站配置, "
	} else {
		intro += fmt.Sprintf("主播用户 ID: %d, ", elem.ElementID)
	}
	intro += fmt.Sprintf("记录 ID: %d", elem.ElementID)

	adminLog.Add(userapi.CatalogManageRoomMedal, intro)
	err = adminLog.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "移除成功", nil
}
