package chatroom

import (
	"fmt"
	"html"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

// ActionVitalityReduce 扣除主播元气值
/**
 * @api {post} /api/v2/admin/chatroom/vitality/reduce 扣除主播元气值
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {Number} reason 扣除原因
 * @apiParam {Number} value 扣除的值
 * @apiParamExample {json} Request-Example:
 *   {
 *     "creator_id": 12,
 *     "reason": "原因",
 *     "value": 1
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "扣分成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionVitalityReduce(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		CreatorID int64  `json:"creator_id"`
		Reason    string `json:"reason"`
		Value     uint   `json:"value"`
	}
	err := c.Bind(&param)
	if err != nil ||
		param.CreatorID == 0 || param.Reason == "" || param.Value == 0 {
		return nil, actionerrors.ErrParams
	}
	after, err := liveaddendum.Punish(param.CreatorID, liveaddendum.OperatorSub, param.Value, param.Reason)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if after == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	// 发送系统通知
	sysMsg := []pushservice.SystemMsg{{
		Title:  "元气值扣除通知",
		UserID: param.CreatorID,
		Content: fmt.Sprintf(
			"很遗憾 _(:3 」∠)_ 因存在违规行为，您的元气值被扣除 %d 分。扣分原因：%s。请遵守直播规范，期待变回元气值满满的你哦！"+
				"如有疑问，请联系<a href=\"%smfeedback/sobot\">站内客服</a>。",
			param.Value, html.EscapeString(param.Reason), config.Conf.Params.URL.Main),
	}}
	err = service.PushService.SendSystemMsgWithOptions(sysMsg,
		&pushservice.SystemMsgOptions{
			DisableHTMLEscape: true,
		})
	if err != nil {
		logger.Errorf("系统私信失败: %v", err)
		// PASS
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	box.AddAdminLog(liveaddendum.AdminLogInfo(liveaddendum.OperatorSub, param.CreatorID))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "扣分成功", nil
}

// ActionVitalityIncrease 增加主播元气值
/**
 * @api {post} /api/v2/admin/chatroom/vitality/increase 增加主播元气值
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {Number} value 增加的值
 * @apiParamExample {json} Request-Example:
 *     {
 *       "creator_id": 12,
 *       "value": 1
 *     }
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": "返还成功"
 *     }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionVitalityIncrease(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		CreatorID int64 `form:"creator_id" json:"creator_id"`
		Value     uint  `form:"value" json:"value"`
	}
	err := c.Bind(&param)
	if err != nil ||
		param.CreatorID <= 0 || param.Value == 0 {
		return nil, actionerrors.ErrParams
	}
	after, err := liveaddendum.IncreaseVitality(param.CreatorID, param.Value)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if after == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	err = messageassign.SystemMessageAssign(after.ID, "元气值返还通知",
		fmt.Sprintf(
			"亲爱的主播，您申诉的内容已通过，现已为您恢复 %d 分元气值，感谢您的理解与配合。",
			param.Value))
	if err != nil {
		logger.Errorf("系统私信失败: %v", err)
		// PASS
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	box.AddAdminLog(liveaddendum.AdminLogInfo(liveaddendum.OperatorAdd, param.CreatorID))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "返还成功", nil
}
