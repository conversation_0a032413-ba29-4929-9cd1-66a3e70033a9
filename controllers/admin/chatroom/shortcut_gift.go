package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type shortcutGiftAddParam struct {
	RoomIDs   string `form:"room_ids" json:"room_ids"`
	GiftID    int64  `form:"gift_id" json:"gift_id"`
	StartTime int64  `form:"start_time" json:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time"`
	Confirm   int    `form:"confirm" json:"confirm"`
}

// ActionShortcutGiftAdd 新增快捷送礼礼物配置
/**
 * @api {post} /api/v2/admin/chatroom/shortcut/gift/add 新增快捷送礼礼物配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} [room_ids] 房间 ID 多个 ID 用半角逗号分隔，为空时默认全站配置
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} start_time 开始时间, 秒级时间戳 e.g. 1578844800
 * @apiParam {Number} end_time 结束时间, 秒级时间戳 e.g. 1578844800
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiSuccessExample 确认新增配置:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "礼物 ID: 1\n礼物名称: test\n配置直播间: 1,2,3,4"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionShortcutGiftAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param shortcutGiftAddParam
	err := c.Bind(&param)
	if err != nil || param.GiftID <= 0 || param.StartTime <= 0 || param.EndTime <= 0 {
		return nil, actionerrors.ErrParams
	}
	if param.EndTime <= goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("结束时间需大于当前时间")
	}
	if param.StartTime >= param.EndTime {
		return nil, actionerrors.ErrParamsMsg("开始时间需小于结束时间")
	}
	roomIDs, roomIDsMsg, err := shortcutRoomIDs(param.RoomIDs)
	if err != nil {
		return nil, err
	}
	g, err := gift.FindByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg("输入正确礼物 id")
	}

	switch g.Type {
	case gift.TypeNormal:
		// PASS
	case gift.TypeRoomCustom:
		if param.RoomIDs == "" {
			return nil, actionerrors.ErrParamsMsg("全局配置只能配置普通礼物")
		}
		// TODO: 优化批量校验直播间是否拥有指定礼物的方式
		for i := range roomIDs {
			if !g.OwnedByRoom(roomIDs[i]) {
				return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("房间 %d 未拥有礼物 %d", roomIDs[i], g.GiftID))
			}
		}
	default:
		return nil, actionerrors.ErrParamsMsg("礼物类型错误")
	}
	if param.Confirm == 0 {
		return nil, actionerrors.ErrConfirmRequired(fmt.Sprintf("礼物 ID: %d\n礼物名称: %s\n配置直播间: %s", g.GiftID, g.Name, roomIDsMsg), 1)
	}

	err = livecustom.AddShortcutGifts(roomIDs, param.GiftID, param.StartTime, param.EndTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := userapi.NewAdminLogBox(c)
	box.AddAdminLog(fmt.Sprintf("新增直播间快捷礼物配置, 直播间: %s, 礼物 ID: %d", roomIDsMsg, g.GiftID), userapi.CatalogManageShortcutGift)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

func shortcutRoomIDs(roomIDsStr string) ([]int64, string, error) {
	if roomIDsStr == "" {
		return []int64{0}, "全部", nil
	}
	arrayRoomIDs, err := goutil.SplitToInt64Array(roomIDsStr, ",")
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	list := util.FindInt64Duplicates(arrayRoomIDs)
	if len(list) != 0 {
		return nil, "", actionerrors.ErrParamsMsg("存在房间号重复，此次新增失败")
	}
	rooms, err := room.FindAll(bson.M{
		"room_id": bson.M{"$in": arrayRoomIDs},
		"limit":   bson.M{"$exists": false},
	})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	roomMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Room)
	if len(rooms) != len(arrayRoomIDs) {
		list := make([]int64, 0, util.Abs(int64(len(rooms)-len(arrayRoomIDs))))
		for _, r := range arrayRoomIDs {
			if _, ok := roomMap[r]; !ok {
				list = append(list, r)
			}
		}
		return nil, "", actionerrors.ErrParamsMsg(fmt.Sprintf("房间 %s 不存在", goutil.JoinInt64Array(list, "、")))
	}
	return arrayRoomIDs, roomIDsStr, nil
}

type shortcutGiftRemoveParam struct {
	RecordIDs string `form:"record_ids" json:"record_ids"`
	Confirm   int    `form:"confirm" json:"confirm"`
}

// ActionShortcutGiftRemove 移除快捷送礼礼物配置
/**
 * @api {post} /api/v2/admin/chatroom/shortcut/gift/remove 移除快捷送礼礼物信息配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {String} record_ids 记录 ID, 多个 ID 用半角逗号分隔
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "移除成功"
 *   }
 *
 * @apiSuccessExample 确认移除配置:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "移除记录 ID: 1,2,3,4"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionShortcutGiftRemove(c *handler.Context) (handler.ActionResponse, error) {
	var param shortcutGiftRemoveParam
	err := c.Bind(&param)
	if err != nil || param.RecordIDs == "" {
		return nil, actionerrors.ErrParams
	}
	arrayIDs, err := goutil.SplitToInt64Array(param.RecordIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	list := util.FindInt64Duplicates(arrayIDs)
	if len(list) != 0 {
		return nil, actionerrors.ErrParams
	}
	res, err := livecustom.FindShortcutByIDs(arrayIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(res) != len(arrayIDs) {
		return nil, actionerrors.ErrParamsMsg("部分记录不存在, 请检查后重新输入")
	}
	if param.Confirm == 0 {
		return nil, actionerrors.ErrConfirmRequired(fmt.Sprintf("移除记录 ID: %s", param.RecordIDs), 1)
	}

	err = livecustom.RemoveShortcutGifts(arrayIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := userapi.NewAdminLogBox(c)
	box.AddAdminLog(fmt.Sprintf("移除直播间快捷礼物配置, 记录 ID: %s", param.RecordIDs), userapi.CatalogManageShortcutGift)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "移除成功", nil
}
