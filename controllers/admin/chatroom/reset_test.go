package chatroom

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

var (
	resetRoomID    int64 = 20200923
	resetRoomIDStr       = "20200923"
	resetUserID    int64 = 20200923
	username             = "Test_重置名称"
	cover                = "test.png"

	existsRoomID = 172842330 // chatroom/activity_test insert
)

func insertResetTestRoom() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoom := room.Helper{
		RoomID:          resetRoomID,
		Name:            "重置名称房间",
		NameClean:       "重置名称房间",
		Type:            "live",
		Announcement:    "intro",
		Cover:           &cover,
		Background:      &room.Background{Enable: false, Opacity: 1},
		Status:          room.Status{Open: room.StatusOpenFalse},
		CatalogID:       catalog.CatalogIDLiveHypnosis,
		CreatorID:       resetUserID,
		CreatorUsername: username,
	}
	err := room.Collection().FindOneAndUpdate(ctx, bson.M{"room_id": resetRoomID},
		bson.M{"$set": testRoom}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil && err != mongo.ErrNoDocuments {
		logger.Fatal(err)
	}

	_, err = live.Save(&live.SaveParams{
		CreatorID: resetUserID,
		Title:     "重置名称房间",
		Intro:     "intro",
		Status:    live.StatusClose,
		RoomID:    resetRoomID,
	})
	if err != nil {
		logger.Fatal(err)
	}
}

func TestActionReset(t *testing.T) {
	cancel := mrpc.SetMock("go://util/addadminlog", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	insertResetTestRoom()
	t.Run("重置名称", func(t *testing.T) {
		require := require.New(t)
		c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_ids": resetRoomIDStr,
			"elems": []string{"name"}})
		_, err := ActionReset(c)
		require.NoError(err)

		c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_ids": resetRoomIDStr,
			"type": 1, "elems": []string{"name"}})
		_, err = ActionReset(c)
		require.NoError(err)
	})

	t.Run("重置封面", func(t *testing.T) {
		require := require.New(t)
		c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_ids": resetRoomIDStr,
			"elems": []string{"cover"}})
		_, err := ActionReset(c)
		require.NoError(err)
	})

	t.Run("重置背景图", func(t *testing.T) {
		require := require.New(t)
		c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_ids": resetRoomIDStr,
			"elems": []string{"background"}})
		_, err := ActionReset(c)
		require.NoError(err)
	})

	t.Run("重置简介", func(t *testing.T) {
		require := require.New(t)
		c := handler.NewTestContext(http.MethodPost, "/", true, handler.M{"room_ids": resetRoomIDStr,
			"elems": []string{"intro"}})
		_, err := ActionReset(c)
		require.NoError(err)
	})

	t.Run("重置多个房间的名称、背景图、封面图、简介", func(t *testing.T) {
		require := require.New(t)
		c := handler.NewTestContext(http.MethodPost, "/", true,
			handler.M{"room_ids": fmt.Sprintf("%d,%d", resetRoomID, existsRoomID), "elems": []string{"name",
				"cover", "background", "intro"}})
		_, err := ActionReset(c)
		require.NoError(err)
	})
}

func TestActionResetLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/", false, nil)
	p := new(resetParam)
	assert.Equal(actionerrors.ErrParams, p.load(c))

	input := handler.M{"room_ids": resetRoomIDStr, "elems": []string{"name", "cover", "background", "intro"}}
	p = new(resetParam)
	c = handler.NewTestContext(http.MethodPost, "/", false, input)
	require.NoError(p.load(c))
	assert.Equal([]int64{resetRoomID}, p.roomIDs)

	input["room_ids"] = "123," + resetRoomIDStr
	p = new(resetParam)
	c = handler.NewTestContext(http.MethodPost, "/", false, input)
	assert.EqualError(p.load(c), "部分直播间不存在，请检查后重新输入")
	assert.Equal([]int64{123, resetRoomID}, p.roomIDs)
}
