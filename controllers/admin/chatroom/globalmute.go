package chatroom

import (
	"fmt"
	"html"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type operationMute struct {
	c *handler.Context

	expireTime string
	operation  int
	reason     string
	userID     int64
}

type globalMuteParams struct {
	UserID   int64  `form:"user_id" json:"user_id"`
	Duration int    `form:"duration" json:"duration"`
	Reason   string `form:"reason" json:"reason"`

	u *liveuser.UserInfo
}

// ActionGlobalMuteAdd 直播间全站禁言
/**
 * @api {post} /api/v2/admin/chatroom/globalmute/add 直播间全站禁言
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} duration 禁言时长, 单位: 天
 * @apiParam {string} reason 禁言原因
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "全站禁言成功"
 *   }
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGlobalMuteAdd(c *handler.Context) (handler.ActionResponse, error) {
	var params globalMuteParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = params.check()
	if err != nil {
		return nil, err
	}

	now := goutil.TimeNow()
	// 过期时间精确到分钟 e.g. 2020-02-01 12:01:00
	expireTime := now.AddDate(0, 0, params.Duration).Round(time.Minute)
	// WORKAROUND: audio-chatroom 中禁言用户的缓存时长为 3 分钟，为防止到提示时间还没解除禁言，
	// 这边数据库中实际禁言到期时间比提示客户消息中的到期时间提前 3 分钟
	realExpireTime := expireTime.Add(-3 * time.Minute)
	m := &livemembers.Member{
		Helper: livemembers.Helper{
			RoomID:      0,
			UserID:      params.UserID,
			Username:    params.u.Username,
			IconURL:     params.u.IconURL,
			CreatedTime: now,
			ExpireAt:    &realExpireTime,
			Status:      livemembers.StatusMute,
			UpdatedTime: now,
			OperatorID:  c.UserID(),
		},
	}
	err = m.SetMute()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 清除全站禁言用户缓存
	deleteRoomsGlobalMuteCache()

	// 记录管理员操作日志
	mute := operationMute{
		c: c, userID: params.UserID, reason: params.Reason,
		expireTime: expireTime.Format(util.TimeFormatYMDHMS), operation: userapi.AddGlobalMute,
	}
	mute.sendMuteLog()
	// 发送用户系统通知
	mute.sendMuteSystemMessage()
	return "全站禁言成功", nil
}

func (p *globalMuteParams) check() error {
	if p.Duration <= 0 {
		return actionerrors.ErrParamsMsg("请输入禁言时长")
	}

	if p.Reason == "" {
		return actionerrors.ErrParamsMsg("请选择理由")
	}

	userInfo, err := liveuser.FindUserInfo(p.UserID, 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if userInfo == nil {
		return actionerrors.ErrCannotFindUser
	}
	// 超管后台无法禁言超管
	if userInfo.IsRole(liveuser.RoleStaff) {
		return actionerrors.NewErrForbidden("无法禁言超管")
	}
	p.u = userInfo
	return nil
}

// ActionGlobalMuteRemove 解除全站禁言
/**
 * @api {post} /api/v2/admin/chatroom/globalmute/remove 解除全站禁言
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "解除全站禁言成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionGlobalMuteRemove(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		UserID int64 `form:"user_id" json:"user_id"`
	}
	err := c.Bind(&params)
	if err != nil || params.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	filter := bson.M{"user_id": params.UserID, "status": livemembers.StatusMute, "room_id": 0}
	success, err := livemembers.FindOneAndDelete(filter)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		return nil, actionerrors.ErrParamsMsg("该用户不在全站禁言状态中, 请检查后重试")
	}
	// 清除全站禁言用户缓存
	deleteRoomsGlobalMuteCache()

	mute := operationMute{c: c, userID: params.UserID, operation: userapi.RemoveGlobalMute}
	// 记录管理员操作日志
	mute.sendMuteLog()
	// 发送用户系统通知
	mute.sendMuteSystemMessage()
	return "解除全站禁言成功", nil
}

func (mute *operationMute) sendMuteLog() {
	var intro string

	switch mute.operation {
	case userapi.AddGlobalMute:
		intro = fmt.Sprintf("直播间全站禁言惩罚, 用户 ID: %d 因%s, 全站禁言至 %s",
			mute.userID, mute.reason, mute.expireTime)
	case userapi.RemoveGlobalMute:
		intro = fmt.Sprintf("直播间全站禁言解除, 用户 ID: %d", mute.userID)
	}
	adminLogs := userapi.NewAdminLogBox(mute.c)
	adminLogs.AddAdminLog(intro, mute.operation)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (mute *operationMute) sendMuteSystemMessage() {
	var title, content string
	switch mute.operation {
	case userapi.AddGlobalMute:
		title = "直播间全站禁言惩罚"
		content = fmt.Sprintf("由于%s，您已被全站禁言至 %s。全站禁言期间，无法在直播间内发送聊天消息，无法使用全站喇叭。",
			html.EscapeString(mute.reason), mute.expireTime)
	case userapi.RemoveGlobalMute:
		title = "直播间全站禁言解除"
		content = "现已解除您的全站禁言，请您遵守规范，谨慎发言！"
	}
	msg := userapi.NewSystemMsgBox()
	msg.AddMessage(mute.userID, title, content)
	if err := msg.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionGlobalMuteGet 获取用户全站禁言信息
/**
 * @api {get} /api/v2/admin/chatroom/globalmute/get 获取用户全站禁言信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "",
 *       "operator_id": 12,
 *       "status": 3,
 *       "expire_at": "2020-03-07T04:20:00Z"
 *     } // 没有被禁言返回 null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (404) {Number} code 500020004
 * @apiError (404) {String} info 无法找到该用户
 *
 */
func ActionGlobalMuteGet(c *handler.Context) (handler.ActionResponse, error) {
	userID, err := c.GetParamInt64("user_id")
	if err != nil || userID <= 0 {
		return nil, actionerrors.ErrParams
	}
	userInfo, err := liveuser.FindUserInfo(userID, 0)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if userInfo == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	member, err := livemembers.GlobalMuteByUserID(userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if member == nil {
		return nil, nil
	}
	member.Username = userInfo.Username
	member.IconURL = userInfo.IconURL

	return member, nil
}

func deleteRoomsGlobalMuteCache() {
	if err := service.Redis.Del(keys.KeyRoomsGlobalMute0.Format()).Err(); err != nil {
		logger.Errorf("redis del error: %v", err)
		// PASS
	}
}
