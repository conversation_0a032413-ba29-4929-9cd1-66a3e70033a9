package chatroom

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	sortUserID         = "user_id"
	sortUserIDDesc     = "user_id.desc"
	sortRoomID         = "room_id"
	sortRoomIDDesc     = "room_id.desc"
	sortUploadTime     = "upload_time"
	sortUploadTimeDesc = "upload_time.desc"
)

var sortMap = map[string]string{
	sortUserID:         livereview.UserIDAsc,
	sortUserIDDesc:     livereview.UserIDDesc,
	sortRoomID:         livereview.RoomIDAsc,
	sortRoomIDDesc:     livereview.RoomIDDesc,
	sortUploadTime:     livereview.UploadTimeAsc,
	sortUploadTimeDesc: livereview.UploadTimeDesc,
}

type imageListResp struct {
	Data       []*livereview.WithUserInfo `json:"Datas"`
	Pagination goutil.Pagination          `json:"pagination"`
}

// ActionReviewList 超管审核直播间信息列表
/**
 * @api {get} /api/v2/admin/chatroom/review/list 超管审核直播间信息列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} [type = 0] 状态，0：封面图，1：背景图，2: 标题
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 * @apiParam {Number} [room_id] 房间号
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {Number} [username] 用户名
 * @apiParam {Number} [sort=upload_time.desc] 排序方式: user_id,user_id.desc,room_id,room_id.desc,upload_time,upload_time.desc
 *
 * @apiSuccessExample 图片审核（type 传 0 和 1）:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "user_id": 12,
 *           "username": "用户名",
 *           "room_id": 123,
 *           "upload_time": 1234567890,
 *           "type": 0,
 *           "status": 0,
 *           "image_url": "http://static.missevan.com/example.png"
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample 标题审核（type 传 2）:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "user_id": 12,
 *           "username": "用户名",
 *           "room_id": 123,
 *           "upload_time": 1234567890,
 *           "type": 2,
 *           "status": 0,
 *           "name": "标题标题"
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionReviewList(c *handler.Context) (handler.ActionResponse, error) {
	reviewType, err := c.GetParamInt("type")
	if err != nil || !livereview.TypeValid(reviewType) {
		return nil, actionerrors.ErrParams
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	opt := livereview.FindOptions{}
	opt.RoomID, _ = c.GetParamInt64("room_id")
	opt.UserID, _ = c.GetParamInt64("user_id")
	opt.Username, _ = c.GetParamString("username")
	opt.Sort = sortMap[c.GetDefaultParamString("sort", sortUploadTimeDesc)]
	resp := new(imageListResp)
	resp.Data, resp.Pagination, err = livereview.FindReviewing(reviewType, p, pageSize, opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}
