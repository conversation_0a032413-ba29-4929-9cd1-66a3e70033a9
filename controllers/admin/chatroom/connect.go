package chatroom

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// ActionConnectProviderSet 设置直播间连麦提供商
/**
 * @api {post} /api/v2/admin/chatroom/connect/provider/set 设置直播间连麦提供商
 * @apiDescription 设置直播间连麦提供商，设置完毕主播重新开播即可生效
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {string=agora} provider 连麦服务提供商, 当前仅支持配置 agora, 不传或者传递非 agora 的值表示将从 agora 白名单中移除
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "操作成功"
 *   }
 *
 */
func ActionConnectProviderSet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID   int64  `form:"room_id" json:"room_id"`
		Provider string `form:"provider" json:"provider"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	exists, err := room.Exists(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}
	key := keys.KeyRoomsEnableAgora0.Format()
	var content string
	if param.Provider == room.ProviderAgora {
		// 新增白名单
		result, err := service.Redis.SAdd(key, param.RoomID).Result()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if result <= 0 {
			return nil, actionerrors.NewErrForbidden("当前直播间已在声网白名单中，请勿重复添加")
		}
		content = "新增声网白名单"
	} else {
		// 移除白名单
		result, err := service.Redis.SRem(key, param.RoomID).Result()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if result <= 0 {
			return nil, actionerrors.NewErrForbidden("当前直播间未在声网白名单中，请检查后重新输入")
		}
		content = "移除声网白名单"
	}

	intro := fmt.Sprintf("设置直播间连麦提供商, %s, 直播间 ID: %d", content, param.RoomID)
	adminLogs := goclient.NewAdminLogBox(c)
	adminLogs.Add(userapi.CatalogManageRoomConnect, intro)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "操作成功", nil
}
