package chatroom

import (
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livetagcontrollist"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/util"
)

type tagAllowListParam struct {
	TagID   int64  `form:"tag_id" json:"tag_id"`
	UserIDs string `form:"user_ids" json:"user_ids"`
	Confirm int    `form:"confirm" json:"confirm"`

	rooms     []*room.Room
	roomMap   map[int64]*room.Room
	allowList []*livetagcontrollist.LiveTagControlList
}

func newTagAllowListParam(c *handler.Context, isAdd bool) (*tagAllowListParam, error) {
	var param tagAllowListParam
	err := c.Bind(&param)
	if err != nil || param.TagID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.UserIDs = strings.TrimSpace(param.UserIDs)
	if param.UserIDs == "" {
		return nil, actionerrors.ErrParamsMsg("M号不得为空")
	}

	t, err := tag.FindTagByID(param.TagID, tag.TypeLiveTag)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if t == nil {
		return nil, actionerrors.ErrNotFound("标签不存在")
	}

	userIDs, err := util.SplitToInt64Array(param.UserIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(userIDs) > 50 {
		return nil, actionerrors.ErrParamsMsg("M号数量不得超过 50 个")
	}
	userIDs = util.Uniq(userIDs)
	users, err := mowangskuser.FindSimpleList(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(users) != len(userIDs) {
		userMap := util.ToMap(users, "ID").(map[int64]*mowangskuser.Simple)
		diffUserIDs := make([]int64, 0, len(userIDs)-len(users))
		for _, id := range userIDs {
			if _, ok := userMap[id]; !ok {
				diffUserIDs = append(diffUserIDs, id)
			}
		}
		return nil, actionerrors.ErrParamsMsg("存在无效的M号：" + util.JoinInt64Array(diffUserIDs, "，"))
	}

	param.rooms, err = room.FindAll(bson.M{"creator_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.roomMap = make(map[int64]*room.Room, len(param.rooms))
	roomCreatorIDMap := make(map[int64]*room.Room, len(param.rooms))
	roomIDs := make([]int64, 0, len(param.rooms))
	for _, r := range param.rooms {
		param.roomMap[r.RoomID] = r
		roomCreatorIDMap[r.CreatorID] = r
		roomIDs = append(roomIDs, r.RoomID)
	}
	if len(roomIDs) != len(userIDs) {
		diffUserIDs := make([]int64, 0, len(userIDs)-len(roomIDs))
		for _, id := range userIDs {
			if _, ok := roomCreatorIDMap[id]; !ok {
				diffUserIDs = append(diffUserIDs, id)
			}
		}
		return nil, actionerrors.ErrParamsMsg("存在非主播身份的M号：" + util.JoinInt64Array(diffUserIDs, "，"))
	}

	param.allowList, err = livetagcontrollist.ControlList(param.TagID, livetagcontrollist.StatusAllowRoomAddTag, roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if isAdd {
		if len(roomIDs) == len(param.allowList) {
			return nil, actionerrors.NewErrForbidden("用户都已在白名单中")
		}
	} else {
		if len(param.allowList) == 0 {
			return nil, actionerrors.NewErrForbidden("用户都不在白名单中")
		}
	}
	if param.Confirm == 0 {
		if isAdd {
			return nil, actionerrors.ErrConfirmRequired(
				fmt.Sprintf("确定要将 %d 位用户加入 pia 戏功能白名单么？", len(roomIDs)-len(param.allowList)), 1)
		}
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确定要将 %d 位用户移出 pia 戏功能白名单么？", len(param.allowList)), 1)
	}
	return &param, nil
}

// ActionTagAllowListAdd 添加标签白名单
/**
 * @api {post} /api/v2/admin/chatroom/tag/allowlist/add 添加标签白名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} tag_id 标签 ID
 * @apiParam {String} user_ids 用户 ID 列表，多个用户 ID 用英文逗号分隔
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "添加成功！"
 *     }
 *   }
 *
 * @apiSuccessExample 确认加入白名单:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要将 5 位用户加入 pia 戏功能白名单吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionTagAllowListAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newTagAllowListParam(c, true)
	if err != nil {
		return nil, err
	}
	allowListMap := util.ToMap(param.allowList, "RoomID").(map[int64]*livetagcontrollist.LiveTagControlList)
	sliceCap := util.MaxInt64(int64(len(param.rooms)-len(param.allowList)), 0)
	newRoomIDs := make([]int64, 0, sliceCap)
	newUserIDs := make([]int64, 0, sliceCap)
	for _, r := range param.rooms {
		if _, ok := allowListMap[r.RoomID]; !ok {
			newRoomIDs = append(newRoomIDs, r.RoomID)
			newUserIDs = append(newUserIDs, r.CreatorID)
		}
	}
	err = livetagcontrollist.Add(param.TagID, livetagcontrollist.StatusAllowRoomAddTag, newRoomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	logbox := goclient.NewAdminLogBox(c)
	switch param.TagID {
	case tag.TagListenDrama:
		logbox.Add(userapi.CatalogManageTag,
			fmt.Sprintf("添加【pia 戏】白名单（一键进入听剧标签，ID %d）：%s", param.TagID, util.JoinInt64Array(newUserIDs, "，")))
	}
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return handler.M{"msg": "添加成功！"}, nil
}

// ActionTagAllowListRemove 移除标签白名单
/**
 * @api {post} /api/v2/admin/chatroom/tag/allowlist/remove 移除标签白名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} tag_id 标签 ID
 * @apiParam {String} user_ids 用户 ID 列表，多个用户 ID 用英文逗号分隔
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "移出成功！"
 *     }
 *   }
 *
 * @apiSuccessExample 确认移出白名单:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要将 5 位用户移出 pia 戏功能白名单吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionTagAllowListRemove(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newTagAllowListParam(c, false)
	if err != nil {
		return nil, err
	}
	removeRoomIDs := make([]int64, 0, len(param.allowList))
	removeUserIDs := make([]int64, 0, len(param.allowList))
	for _, allow := range param.allowList {
		if r, ok := param.roomMap[allow.RoomID]; ok {
			removeRoomIDs = append(removeRoomIDs, r.RoomID)
			removeUserIDs = append(removeUserIDs, r.CreatorID)
		}
	}
	err = livetagcontrollist.Remove(param.TagID, livetagcontrollist.StatusAllowRoomAddTag, removeRoomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	logbox := goclient.NewAdminLogBox(c)
	switch param.TagID {
	case tag.TagListenDrama:
		logbox.Add(userapi.CatalogManageTag,
			fmt.Sprintf("移出【pia 戏】白名单（一键进入听剧标签，ID %d）：%s", param.TagID, util.JoinInt64Array(removeUserIDs, "，")))
	}
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return handler.M{"msg": "移出成功！"}, nil
}
