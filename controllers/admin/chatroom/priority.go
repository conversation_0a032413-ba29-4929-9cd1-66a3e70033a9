package chatroom

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// ActionPlaybackPrioritySet 设置直播间回放处理优先级
/**
 * @api {post} /api/v2/admin/chatroom/playback/priority/set 设置直播间回放处理优先级
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} priority 处理直播间回放优先级，数值越大越优先
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionPlaybackPrioritySet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID   int64 `form:"room_id" json:"room_id"`
		Priority int   `form:"priority" json:"priority"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	exists, err := room.Exists(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrCannotFindRoom
	}
	ok, err := livemeta.SetPlaybackPriority(param.RoomID, param.Priority)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrCannotFindResource
	}

	// 发送管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置直播间回放处理优先级，房间号: %d, 优先级: %d", param.RoomID, param.Priority)
	box.Add(userapi.CatalogLivePlaybackPrioritySet, intro)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}
