package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestShortcutGiftParamTags(t *testing.T) {
	addTags := []string{"room_ids", "gift_id", "start_time", "end_time", "confirm"}
	removeTags := []string{"record_ids", "confirm"}

	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(shortcutGiftAddParam{}, addTags...)
	kc.Check(shortcutGiftRemoveParam{}, removeTags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(shortcutGiftAddParam{}, addTags...)
	kc.Check(shortcutGiftRemoveParam{}, removeTags...)
}

func TestActionShortcutGiftAddAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testGift := new(gift.Gift)
	err := gift.Collection().FindOne(ctx, bson.M{"type": gift.TypeNormal}).Decode(&testGift)
	require.NoError(err)
	rooms, err := room.ListSimples(bson.M{}, options.Find().SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)
	testRoomIDs := make([]int64, len(rooms))
	for i := range rooms {
		testRoomIDs[i] = rooms[i].RoomID
	}
	testRoomIDsStr := goutil.JoinInt64Array(testRoomIDs, ",")

	t.Run("add", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
		_, err := ActionShortcutGiftAdd(c)
		assert.Equal(actionerrors.ErrParams, err)

		now := goutil.TimeNow()
		param := shortcutGiftAddParam{
			RoomIDs:   "1,-999999",
			GiftID:    testGift.GiftID,
			StartTime: now.Unix(),
			EndTime:   now.Add(10 * time.Second).Unix(),
			Confirm:   0,
		}
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, err = ActionShortcutGiftAdd(c)
		assert.EqualError(err, "房间 -999999 不存在")

		param.RoomIDs = testRoomIDsStr
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, confirmResp := ActionShortcutGiftAdd(c)
		assert.EqualValues(confirmResp.(*handler.ActionError).Info["msg"],
			fmt.Sprintf("礼物 ID: %d\n礼物名称: %s\n配置直播间: %s", testGift.GiftID, testGift.Name, testRoomIDsStr))

		require.NoError(livecustom.LiveCustom{}.DB().
			Delete("", "custom_type = ? AND element_id IN (?)",
				livecustom.TypeShortcutGift, testRoomIDs).Error)
		param = shortcutGiftAddParam{
			RoomIDs:   testRoomIDsStr,
			GiftID:    testGift.GiftID,
			StartTime: now.Unix(),
			EndTime:   now.Add(time.Minute).Unix(),
			Confirm:   1,
		}
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, err = ActionShortcutGiftAdd(c)
		require.NoError(err)
		res, err := livecustom.FindRoomShowShortcut(testRoomIDs[1])
		require.NoError(err)
		require.NotNil(res)
	})

	t.Run("remove", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
		_, err := ActionShortcutGiftRemove(c)
		assert.Equal(actionerrors.ErrParams, err)

		var res []*livecustom.LiveCustom
		err = livecustom.LiveCustom{}.DB().
			Where("custom_type = ? AND element_id IN (?)", livecustom.TypeShortcutGift, testRoomIDs).
			Find(&res).Error
		require.NoError(err)
		require.NotEmpty(res)
		ids := make([]int64, len(res))
		var idsStr string
		for i := range res {
			ids[i] = res[i].ID
		}
		idsStr = goutil.JoinInt64Array(ids, ",")
		param := shortcutGiftRemoveParam{
			RecordIDs: idsStr + ",-999999",
			Confirm:   0,
		}
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, err = ActionShortcutGiftRemove(c)
		require.EqualError(err, "部分记录不存在, 请检查后重新输入")

		param = shortcutGiftRemoveParam{
			RecordIDs: idsStr,
			Confirm:   0,
		}
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, confirmResp := ActionShortcutGiftRemove(c)
		assert.EqualValues(confirmResp.(*handler.ActionError).Info["msg"], fmt.Sprintf("移除记录 ID: %s", idsStr))

		param = shortcutGiftRemoveParam{
			RecordIDs: idsStr,
			Confirm:   1,
		}
		c = handler.NewTestContext(http.MethodPost, "", true, param)
		_, err = ActionShortcutGiftRemove(c)
		require.NoError(err)
	})
}
