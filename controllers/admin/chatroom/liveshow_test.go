package chatroom

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	liveshow "github.com/MiaoSiLa/live-service/models/mongodb/liveshows"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCreateLiveShow(t *testing.T) {
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := liveshow.Collection().DeleteMany(ctx, bson.M{
		"creator_id": 12,
	})
	require.NoError(err)

	tomorrow := goutil.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1)
	param := newLiveShowParams{
		CreatorID:     12,
		ShowStartTime: tomorrow.Add(2 * time.Hour).Unix(),
		ShowType:      1,
	}
	c := handler.NewTestContext("POST", "", true, param)
	_, err = ActionCreateLiveShow(c)
	require.EqualError(err, "确认添加此主播（房间 ID：18113499）个人场？")

	param.Confirm = 1
	c = handler.NewTestContext("POST", "", true, param)
	_, err = ActionCreateLiveShow(c)
	require.NoError(err)

	c = handler.NewTestContext("POST", "", true, param)
	_, err = ActionCreateLiveShow(c)
	require.EqualError(err, "个人场时间冲突")
}

func TestActionCancelLiveShow(t *testing.T) {
	require := require.New(t)

	show, err := liveshow.FindOne(bson.M{"creator_id": 15})
	require.NoError(err)
	require.NotNil(show)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err = liveshow.Collection().UpdateOne(ctx, bson.M{"_id": show.OID}, bson.M{"$set": bson.M{"status": liveshow.ShowStatusCheckApproved}})
	require.NoError(err)

	c := handler.NewTestContext("POST", "", true, cancelLiveShowParams{
		LiveShowID: show.OID.Hex(),
	})
	_, err = ActionCancelLiveShow(c)
	require.EqualError(err, "确认取消零月3（房间 ID：15）个人场朗月挑战资格？")

	c = handler.NewTestContext("POST", "", true, cancelLiveShowParams{
		LiveShowID: show.OID.Hex(),
		Confirm:    1,
	})
	_, err = ActionCancelLiveShow(c)
	require.NoError(err)
}
