package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// live_user 中已插入
const ExistsUserID = int64(3387502)

func TestActionMuteAddAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{
		"user_id":  int64(10),
		"duration": 1,
		"reason":   "其他",
	}
	filter := bson.M{"user_id": 10, "status": livemembers.StatusMute, "room_id": 0}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemembers.Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	// 新增全局禁言
	now := goutil.TimeNow().AddDate(0, 0, 1)
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGlobalMuteAdd(c)
	require.NoError(err)
	m := new(livemembers.Member)
	require.NoError(livemembers.Collection().FindOne(ctx, filter, options.FindOne().
		SetProjection(bson.M{"user_id": 1, "expire_at": 1})).Decode(&m))
	assert.Equal(int64(10), m.UserID)
	assert.WithinDuration(m.ExpireAt.Add(3*time.Minute), now, 100*time.Second)

	// 解除全局禁言
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGlobalMuteRemove(c)
	require.NoError(err)

	// 解除数据库中不存在的禁言
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGlobalMuteRemove(c)
	assert.Error(err, "该用户不在全站禁言状态中, 请检查后重试")

	// 解除禁言参数错误
	param["user_id"] = 0
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGlobalMuteRemove(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	p := globalMuteParams{
		UserID:   1234567890,
		Duration: 0,
		Reason:   "",
	}
	assert.Error(p.check(), "请输入禁言时长")
	p.Duration = 1
	assert.Error(p.check(), "请选择理由")
	p.Reason = "test"
	require.Equal(actionerrors.ErrCannotFindUser, p.check())
	p.UserID = 12
	require.Equal(actionerrors.NewErrForbidden("无法禁言超管"), p.check())
	p.UserID = 10
	require.NoError(p.check())
}

func TestSendMuteSystemMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var actualTitle, actualContent string
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]any)
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		actualTitle = systemMsgList[0].Title
		actualContent = systemMsgList[0].Content
		return "success", nil
	})
	defer cancel()

	param := operationMute{
		userID:     12345,
		operation:  userapi.AddGlobalMute,
		reason:     "发广告",
		expireTime: "2020-02-12 10:12:00",
	}
	// 全站禁言
	param.sendMuteSystemMessage()
	assert.Equal("直播间全站禁言惩罚", actualTitle)
	content := fmt.Sprintf("由于%s，您已被全站禁言至 %s。全站禁言期间，无法在直播间内发送聊天消息，无法使用全站喇叭。",
		param.reason, param.expireTime)
	assert.Equal(content, actualContent)

	// 解除全站禁言
	param.operation = userapi.RemoveGlobalMute
	param.sendMuteSystemMessage()
	assert.Equal("直播间全站禁言解除", actualTitle)
	assert.Equal("现已解除您的全站禁言，请您遵守规范，谨慎发言！", actualContent)
}

func TestActionGlobalMuteGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expireTime := goutil.TimeNow().Add(time.Minute)
	m := livemembers.Helper{
		UserID:   ExistsUserID,
		Status:   livemembers.StatusMute,
		ExpireAt: &expireTime,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": m.UserID, "status": m.Status, "room_id": m.RoomID}
	_, err := livemembers.Collection().UpdateOne(ctx, filter, bson.M{"$set": m}, options.Update().SetUpsert(true))
	require.NoError(err)
	// 正常获取禁言信息
	c := handler.NewTestContext("GET", fmt.Sprintf("/?user_id=%d", ExistsUserID), true, nil)
	r, err := ActionGlobalMuteGet(c)
	require.NoError(err)
	member := r.(*livemembers.Member)
	assert.Equal(m.UserID, member.UserID)
	// 未被禁言用户
	c = handler.NewTestContext("GET", "/?user_id=12", true, nil)
	r, err = ActionGlobalMuteGet(c)
	require.NoError(err)
	require.Empty(r)
	// 用户不存在
	c = handler.NewTestContext("GET", "/?user_id=1234567", true, nil)
	_, err = ActionGlobalMuteGet(c)
	require.Equal(actionerrors.ErrCannotFindUser, err)
	// 参数错误
	c = handler.NewTestContext("GET", "/", true, nil)
	_, err = ActionGlobalMuteGet(c)
	require.Equal(actionerrors.ErrParams, err)
}

func TestDeleteGlobalMuteUsersCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.PrefixFormatter("rooms/mute/global").Format()
	require.NoError(service.Redis.SAdd(key, 1234567890).Err())

	deleteRoomsGlobalMuteCache()
	assert.Equal(int64(0), service.Redis.Exists(key).Val())
}
