package chatroom

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionFansSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	v := make(map[string]interface{})
	newContext := func() *handler.Context {
		c := handler.CreateTestContext(true)
		c.C.Request, _ = http.NewRequest("POST", "/setmedal", tutil.ToRequestBody(v))
		return c
	}
	_, err := ActionSetmedal(newContext())
	assert.Equal(actionerrors.ErrParams, err)
	v["name"] = strconv.FormatInt(goutil.TimeNow().Unix(), 10)
	v["room_id"] = 95
	_, err = ActionSetmedal(newContext())
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
	v["room_id"] = 369892
	_, err = ActionSetmedal(newContext())
	assert.True(err != nil && "该房间无法设置勋章" == err.Error(), err)
	v["room_id"] = 18113499
	_, err = ActionSetmedal(newContext())
	require.NoError(err)
	v["room_id"] = 22489473
	r, err := room.Find(18113499)
	require.NoError(err)
	assert.Equal(v["name"], r.Medal.Name)
	_, err = ActionSetmedal(newContext())
	assert.Equal(actionerrors.ErrMedalAlreadyExist, err)

	// 受限房间
	v["room_id"] = room.TestLimitedRoomID
	_, err = ActionSetmedal(newContext())
	assert.Equal(actionerrors.ErrLimitedRoom, err)
}

func TestActionMedalMultiAddAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := multiAddParams{
		CreatorID:         12,
		PointMultiAdd:     0,
		ThresholdMultiAdd: 0,
	}
	_, err := ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.Equal(actionerrors.ErrParams, err)

	// CreatorID 和 csv 文件只能选择一个
	param = multiAddParams{
		CreatorID:         12,
		CSVURL:            "",
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	param.CSVURL = upload.SourceURL(fmt.Sprintf("https://fm.example.com/testdata/%s.csv", "multi_medal"))
	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("名单输入只能选择一个入口"), err)

	// 上传的 csv 文件中的 M 号有误
	param = multiAddParams{
		CreatorID:         0,
		CSVURL:            "https://fm.example.com/testdata/wrong_multi_medal.csv",
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	testCreatorIDs := [][]string{
		{"主播 M 号"}, {"123456789123456789"},
	}
	testFilePath := "../../../testdata/wrong_multi_medal.csv"
	err = createTestCSV(testFilePath, testCreatorIDs)
	require.NoError(err)
	defer cleanTestFile(testFilePath)

	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("csv 名单有误"), err)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	// 用 csv 文件上传成功
	param = multiAddParams{
		CSVURL:            "https://fm.example.com/testdata/add-multi.csv",
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	testCreatorIDs = [][]string{
		{"主播 M 号"}, {"14"},
	}
	testFilePath = "../../../testdata/add-multi.csv"
	err = createTestCSV(testFilePath, testCreatorIDs)
	require.NoError(err)
	defer cleanTestFile(testFilePath)

	param = multiAddParams{
		CSVURL:            upload.SourceURL(fmt.Sprintf("https://fm.example.com/testdata/%s.csv", "add-multi")),
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.Equal(actionerrors.ErrConfirmRequired("确认为上传的 1 位主播配置亲密度倍数增加: 1, 上限增加: 1, 时间: 1970-01-01 08:00:00 - 2023-04-24 00:05:00 吗？", 1), err)

	param = multiAddParams{
		CreatorID:         14,
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.Equal(actionerrors.ErrConfirmRequired("确认为主播  (用户 ID: 14) 配置亲密度倍数增加: 1, 上限增加: 1, 时间: 1970-01-01 08:00:00 - 2023-04-24 00:05:00 吗？", 1), err)

	param = multiAddParams{
		CreatorID:         12,
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	assert.EqualError(actionerrors.ErrConfirmRequired("确认为主播 零月 (用户 ID: 12) 配置亲密度倍数增加: 1, 上限增加: 1, 时间: 1970-01-01 08:00:00 - 2023-04-24 00:05:00 吗？", 1), err.Error())

	param = multiAddParams{
		CreatorID:         12,
		PointMultiAdd:     1,
		ThresholdMultiAdd: 1,
		StartTime:         0,
		EndTime:           goutil.TimeNow().Add(5 * time.Minute).Unix(),
		Confirm:           1,
	}
	_, err = ActionMedalMultiAdd(handler.NewTestContext(http.MethodPost, "/medal/add-multi", true, param))
	require.NoError(err)

	elem, err := liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementMultiMedalPoint, param.CreatorID)
	require.NoError(err)
	require.NotNil(elem)
	assert.Equal(`{"point_multi_add":1,"threshold_multi_add":1}`, elem.ExtendedFields)

	_, err = ActionMedalMultiRemove(handler.NewTestContext(http.MethodPost, "/medal/remove-multi", true,
		handler.M{"record_id": elem.ID}))
	require.NoError(err)
	elem, err = liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementMultiMedalPoint, param.CreatorID)
	require.NoError(err)
	assert.Nil(elem)
}
