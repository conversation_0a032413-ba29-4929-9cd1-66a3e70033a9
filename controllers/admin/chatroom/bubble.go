package chatroom

import (
	"fmt"
	"html"
	"path/filepath"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/admin/recommended"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	notAllowReplace = iota
	allowReplace
)

type bubbleSimpleResp struct {
	BubbleID int64  `json:"bubble_id"`
	Type     int    `json:"type"`
	ImageURL string `json:"image_url"`
	// 悬停效果
	Float int `json:"float,omitempty"`
	// 控制闪光
	Shine int `json:"shine,omitempty"`
	// 文本颜色
	NormalColor    string `json:"normal_color"`
	HighlightColor string `json:"highlight_color"`
	// usage 字段仅用于表示气泡的用途，不会被读取使用
	Usage string `json:"usage,omitempty"`
}

// ActionBubbleInfo 查询气泡信息
/**
 * @api {get} /api/v2/admin/chatroom/bubble/info 查询气泡信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiPermission staff
 *
 * @apiParam {Number} bubble_id 气泡 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "bubble_id": 70001,
 *       "type": 1,
 *       "usage": "test",
 *       "normal_color": "#FFFFFF",
 *       "highlight_color": "#FFFFFF",
 *       "image_url": "http://test_image.png"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionBubbleInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	bubbleID, err := c.GetDefaultParamInt64("bubble_id", 0)
	if err != nil || bubbleID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	b, err := bubble.FindOne(bubbleID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if b == nil {
		return nil, "", actionerrors.ErrNotFound("气泡 ID 不存在")
	}
	return bubbleSimpleResp{
		BubbleID:       b.BubbleID,
		Type:           b.Type,
		ImageURL:       b.Image,
		Float:          b.Float,
		Shine:          b.Shine,
		NormalColor:    b.NormalColor,
		HighlightColor: b.HighlightColor,
		Usage:          b.Usage,
	}, "", nil
}

type addBubbleParam struct {
	BubbleID       int64              `json:"bubble_id"`
	BubbleType     int                `json:"bubble_type"`
	HighlightColor string             `json:"highlight_color"`
	NormalColor    string             `json:"normal_color"`
	ImageURLs      []upload.SourceURL `json:"image_urls"`
	Clip           string             `json:"clip"`
	Usage          string             `json:"usage"`
	Float          int                `json:"float,omitempty"`
	Confirm        int                `json:"confirm,omitempty"`
	Replace        int                `json:"replace,omitempty"`

	targetPath     upload.TargetPath
	imageURLFilter *liveupload.ImageURLFilter
}

// ActionBubbleAdd 添加气泡
/**
 * @api {post} /api/v2/admin/chatroom/bubble/add 添加气泡
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiPermission staff
 *
 * @apiParam {Number} bubble_id 气泡 ID
 * @apiParam {Number} bubble_type 气泡类型，1 为飘屏气泡，目前只支持飘屏气泡
 * @apiParam {String} highlight_color 高亮色号
 * @apiParam {String} normal_color 普通色号
 * @apiParam {String[]} image_urls 气泡上传图片，尺寸为 709*108 或 372*108
 * @apiParam {String} clip 拉伸参数（格式：top_right_bottom_left）
 * @apiParam {String} usage 用途
 * @apiParam {number=0,1} [float=0] 是否悬停，1 为悬停，0 为不悬停，默认不悬停，仅飘屏支持
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiParam {number=0,1} [replace=0] 是否允许替换，0 为不允许替换，1 为允许替换，默认不允许
 *
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "添加成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "气泡 ID：2<br>高亮色号：#FFFFFF<br>普通色号：#FFFFFF<br>拉伸参数：top_right_bottom_left<br>用途：测试<br>悬停状态：悬停"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionBubbleAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAddBubbleParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.uploadImage()
	if err != nil {
		return nil, "", err
	}
	err = param.addBubble()
	if err != nil {
		return nil, "", err
	}
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("添加气泡，气泡 ID：%d", param.BubbleID)
	box.Add(userapi.CatalogManageBubble, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "添加成功", nil
}

func newAddBubbleParam(c *handler.Context) (*addBubbleParam, error) {
	var param addBubbleParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

func (p *addBubbleParam) check() error {
	if p.BubbleID <= 0 {
		return actionerrors.ErrParams
	}
	if p.Replace == notAllowReplace {
		b, err := bubble.FindOne(p.BubbleID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if b != nil {
			return actionerrors.ErrParamsMsg("气泡 ID 已存在")
		}
	}
	if p.BubbleType != bubble.TypeNotify {
		return actionerrors.ErrParamsMsg("当前仅支持飘屏类型")
	}
	err := p.checkColor()
	if err != nil {
		return err
	}
	if p.Usage == "" {
		return actionerrors.ErrParamsMsg("需填写正确用途")
	}
	p.imageURLFilter, err = liveupload.NewImageURLFilter(p.ImageURLs)
	if err != nil {
		return actionerrors.ErrParamsMsg(err.Error())
	}
	ok := util.IsValidClip(p.Clip)
	if !ok {
		return actionerrors.ErrParamsMsg("需填写正确格式的拉伸参数")
	}
	if p.Confirm == 0 {
		floatStr := "不悬停"
		if p.Float != bubble.FloatDisable {
			floatStr = "悬停"
		}
		msg := fmt.Sprintf("气泡 ID：%d<br>高亮色号：%s<br>普通色号：%s<br>拉伸参数：%s<br>用途：%s<br>悬停状态：%s",
			p.BubbleID, p.HighlightColor, p.NormalColor, p.Clip, html.EscapeString(p.Usage), floatStr)
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return nil
}

func (p *addBubbleParam) checkColor() error {
	if p.NormalColor != "" {
		isColor, err := util.IsColor(p.NormalColor)
		if err != nil || !isColor {
			return actionerrors.ErrParamsMsg("需填写正确的色号格式")
		}
	}
	if p.HighlightColor != "" {
		isColor, err := util.IsColor(p.HighlightColor)
		if err != nil || !isColor {
			return actionerrors.ErrParamsMsg("需填写正确的色号格式")
		}
	}
	return nil
}

func getNotifyBubbleTargetPath(bubbleID int64, clip string, primaryImageURL upload.SourceURL) upload.TargetPath {
	return upload.TargetPath(fmt.Sprintf(
		"%snotify/b%d_%s%s",
		storage.PathPrefixBubble,
		bubbleID,
		clip,
		filepath.Ext(primaryImageURL.String()),
	))
}

func (p *addBubbleParam) uploadImage() error {
	p.targetPath = getNotifyBubbleTargetPath(p.BubbleID, p.Clip, p.imageURLFilter.Primary)
	_, err := recommended.UploadImageWithTarget(p.imageURLFilter, p.targetPath)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (p *addBubbleParam) addBubble() error {
	now := goutil.TimeNow()
	b := bubble.Bubble{
		BubbleID:     p.BubbleID,
		Type:         p.BubbleType,
		CreateTime:   now,
		ModifiedTime: now,
		Image:        config.Conf.Params.URL.CDN + p.targetPath.String(),
		Usage:        p.Usage,
		Float:        p.Float,
	}
	if p.HighlightColor != "" {
		b.HighlightColor = p.HighlightColor
	}
	if p.NormalColor != "" {
		b.NormalColor = p.NormalColor
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	if p.Replace == allowReplace {
		err := bubble.Collection().FindOneAndReplace(ctx,
			bson.M{"bubble_id": p.BubbleID},
			&b,
			options.FindOneAndReplace().SetUpsert(true),
		).Err()
		if err != nil && !mongodb.IsNoDocumentsError(err) {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	} else {
		_, err := bubble.Collection().InsertOne(ctx, &b)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

type editBubbleParam struct {
	BubbleID       int64              `json:"bubble_id"`
	BubbleType     int                `json:"bubble_type"`
	HighlightColor string             `json:"highlight_color"`
	NormalColor    string             `json:"normal_color"`
	ImageURLs      []upload.SourceURL `json:"image_urls"`
	Clip           string             `json:"clip"`
	Usage          string             `json:"usage"`
	Float          int                `json:"float"`
	Confirm        int                `json:"confirm"`

	prevBubble     *bubble.Bubble
	confirmMsg     string
	update         bson.M
	imageURLFilter *liveupload.ImageURLFilter
}

// ActionBubbleEdit 修改气泡
/**
 * @api {post} /api/v2/admin/chatroom/bubble/edit 修改气泡
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/chatroom
 *
 * @apiPermission staff
 *
 * @apiParam {Number} bubble_id 气泡 ID
 * @apiParam {Number} bubble_type 气泡类型，1 为飘屏气泡，目前只支持飘屏气泡
 * @apiParam {String} [highlight_color] 高亮色号
 * @apiParam {String} [normal_color] 普通色号
 * @apiParam {String[]} [image_urls] 气泡上传图片，尺寸为 709*108 或 372*108
 * @apiParam {String} [clip 拉伸参数]（格式：top_right_bottom_left）
 * @apiParam {String} [usage] 用途
 * @apiParam {number=-1,0,1} [float=-1] 是否悬停，-1 为不修改，1 为悬停，0 为不悬停，默认不修改，仅飘屏支持
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "修改成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "气泡 ID：2<br>高亮色号：#FFFFFF<br>普通色号：#FFFFFF<br>拉伸参数：top_right_bottom_left<br>用途：测试<br>悬停状态：悬停"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionBubbleEdit(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newEditBubbleParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.processInput()
	if err != nil {
		return nil, "", err
	}
	err = param.processImage()
	if err != nil {
		return nil, "", err
	}
	err = param.updateBubble()
	if err != nil {
		return nil, "", err
	}
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("修改气泡，气泡 ID：%d", param.BubbleID)
	box.Add(userapi.CatalogManageBubble, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "修改成功", nil
}

func newEditBubbleParam(c *handler.Context) (*editBubbleParam, error) {
	var param editBubbleParam
	param.Float = -1 // 默认为不修改 float
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.update = bson.M{}
	return &param, nil
}

func (p *editBubbleParam) check() error {
	if p.BubbleID <= 0 {
		return actionerrors.ErrParams
	}
	var err error
	p.prevBubble, err = bubble.FindOne(p.BubbleID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.prevBubble == nil {
		return actionerrors.ErrParamsMsg("气泡 ID 不存在")
	}
	if p.BubbleType != bubble.TypeNotify || p.prevBubble.Type != bubble.TypeNotify {
		return actionerrors.ErrParamsMsg("当前仅支持飘屏类型")
	}
	p.confirmMsg = fmt.Sprintf("气泡 ID：%d", p.BubbleID)
	return nil
}

func (p *editBubbleParam) processInput() error {
	if p.HighlightColor != "" {
		isColor, err := util.IsColor(p.HighlightColor)
		if err != nil || !isColor {
			return actionerrors.ErrParamsMsg("需填写正确的色号格式")
		}
		p.confirmMsg += fmt.Sprintf("<br>高亮色号：%s", p.HighlightColor)
		p.update["highlight_color"] = p.HighlightColor
	}
	if p.NormalColor != "" {
		isColor, err := util.IsColor(p.NormalColor)
		if err != nil || !isColor {
			return actionerrors.ErrParamsMsg("需填写正确的色号格式")
		}
		p.confirmMsg += fmt.Sprintf("<br>普通色号：%s", p.NormalColor)
		p.update["normal_color"] = p.NormalColor
	}
	if p.Clip != "" {
		ok := util.IsValidClip(p.Clip)
		if !ok {
			return actionerrors.ErrParamsMsg("需填写正确格式的拉伸参数")
		}
		p.confirmMsg += fmt.Sprintf("<br>拉伸参数：%s", p.Clip)
		p.update["clip"] = p.Clip
	}
	if p.Usage != "" {
		p.confirmMsg += fmt.Sprintf("<br>用途：%s", html.EscapeString(p.Usage))
		p.update["usage"] = p.Usage
	}
	if p.Float != -1 {
		floatStr := "不悬停"
		if p.Float != bubble.FloatDisable {
			floatStr = "悬停"
		}
		p.confirmMsg += fmt.Sprintf("<br>悬停状态：%s", floatStr)
		p.update["float"] = p.Float
	}
	if p.Confirm == 0 {
		return actionerrors.ErrConfirmRequired(p.confirmMsg, 1, true)
	}
	return nil
}

func (p *editBubbleParam) processImage() error {
	if len(p.ImageURLs) == 0 && p.Clip == "" {
		return nil
	}
	if len(p.ImageURLs) == 0 || p.Clip == "" {
		return actionerrors.ErrParamsMsg("需上传资源同时填写拉伸参数才可进行修改")
	}
	var err error
	p.imageURLFilter, err = liveupload.NewImageURLFilter(p.ImageURLs)
	if err != nil {
		return actionerrors.ErrParamsMsg(err.Error())
	}
	targetPath := getNotifyBubbleTargetPath(p.BubbleID, p.Clip, p.imageURLFilter.Primary)
	_, err = recommended.UploadImageWithTarget(p.imageURLFilter, targetPath)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.update["image"] = config.Conf.Params.URL.CDN + targetPath.String()
	return nil
}

func (p *editBubbleParam) updateBubble() error {
	p.update["modified_time"] = goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := bubble.Collection().UpdateOne(ctx,
		bson.M{"bubble_id": p.BubbleID},
		bson.M{"$set": p.update},
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}
