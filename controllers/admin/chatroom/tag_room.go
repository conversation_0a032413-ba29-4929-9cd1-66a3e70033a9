package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomTagParams struct {
	TagID   int64  `form:"tag_id" json:"tag_id"`
	RoomIDs string `form:"room_ids" json:"room_ids"`

	c            *handler.Context
	roomMap      map[int64]*room.Room
	arrayRoomIDs []int64
}

// ActionTagRoomAdd 房间添加标签
/**
 * @api {post} /api/v2/admin/chatroom/tag/room/add 房间添加标签
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} tag_id 标签 ID
 * @apiParam {String} room_ids 房间 ID 多个 ID 用 , 分隔
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "添加标签: 3，房间：2233 添加成功"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 加入听剧标签主播消息
 *   {
 *     "type": "pia",
 *     "event": "start",
 *     "room_id": 10659544,
 *     "by": "admin"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionTagRoomAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRoomTagParams(c)
	if err != nil {
		return nil, err
	}
	err = room.AddTag(param.arrayRoomIDs, param.TagID)
	if err != nil {
		return nil, err
	}
	param.addRecordAndBroadcast(true)

	intro := fmt.Sprintf("添加标签关联房间: 标签 %d，直播间 %s", param.TagID, param.RoomIDs)
	logbox := goclient.NewAdminLogBox(c)
	logbox.Add(userapi.CatalogChangeRoomTag, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return intro, nil
}

// ActionTagRoomDel 房间移除标签
/**
 * @api {post} /api/v2/admin/chatroom/tag/room/del 房间移除标签
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} tag_id 标签 ID
 * @apiParam {String} room_ids 房间 ID 多个 ID 用 , 分隔
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "移除标签: 3，房间：2233 移除成功"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 移出听剧标签主播消息
 *   {
 *     "type": "pia",
 *     "event": "stop",
 *     "room_id": 10659544,
 *     "by": "admin"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionTagRoomDel(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRoomTagParams(c)
	if err != nil {
		return nil, err
	}

	err = room.RemoveTag(param.arrayRoomIDs, param.TagID)
	if err != nil {
		return nil, err
	}
	param.addRecordAndBroadcast(false)

	intro := fmt.Sprintf("移除标签关联房间: 标签 %d，直播间 %s", param.TagID, param.RoomIDs)
	logbox := goclient.NewAdminLogBox(c)
	logbox.Add(userapi.CatalogChangeRoomTag, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return intro, nil
}

func newRoomTagParams(c *handler.Context) (*roomTagParams, error) {
	var param roomTagParams
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.TagID <= 0 || param.RoomIDs == "" {
		return nil, actionerrors.ErrParams
	}
	if param.TagID == tag.TagNova {
		return nil, actionerrors.ErrParamsMsg("新星标签不可操作")
	}
	param.c = c

	tag, err := tag.FindTagByID(param.TagID, tag.TypeLiveTag)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if tag == nil {
		return nil, actionerrors.ErrParamsMsg("标签不存在")
	}
	param.arrayRoomIDs, err = util.SplitToInt64Array(param.RoomIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	list := util.FindInt64Duplicates(param.arrayRoomIDs)
	if len(list) != 0 {
		return nil, actionerrors.ErrParamsMsg("存在房间号重复，此次新增失败")
	}
	rooms, err := room.FindAll(bson.M{
		"room_id": bson.M{"$in": param.arrayRoomIDs},
		"limit":   bson.M{"$exists": false},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.roomMap = goutil.ToMap(rooms, "RoomID").(map[int64]*room.Room)
	if len(rooms) != len(param.arrayRoomIDs) {
		list := make([]int64, 0, util.Abs(int64(len(rooms)-len(param.arrayRoomIDs))))
		for _, r := range param.arrayRoomIDs {
			if _, ok := param.roomMap[r]; !ok {
				list = append(list, r)
			}
		}
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("房间 %s 不存在", goutil.JoinInt64Array(list, "、")))
	}
	return &param, nil
}

func (param *roomTagParams) addRecordAndBroadcast(isAdd bool) {
	if param.TagID != tag.TagListenDrama {
		return
	}

	var (
		roomIDs = make([]int64, 0, len(param.arrayRoomIDs))
		elems   = make([]*userapi.BroadcastElem, 0, len(param.arrayRoomIDs))
	)
	for _, roomID := range param.arrayRoomIDs {
		r, ok := param.roomMap[roomID]
		if !ok {
			continue
		}
		// NOTICE: 若是添加标签，房间已在该标签下，不发 im 消息；若是移除标签，房间已不在该标签下，不发 im 消息
		if ok = r.ContainsTag(param.TagID); (isAdd && ok) || (!isAdd && !ok) {
			continue
		}

		roomIDs = append(roomIDs, roomID)

		elems = append(elems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  r.RoomID,
			UserID:  r.CreatorID,
			Payload: tag.NewPiaPayload(isAdd, r.RoomID, true),
		})
	}
	if len(roomIDs) == 0 {
		return
	}

	var err error
	if isAdd {
		err = liveroomtagrecord.AddRecords(nil, param.TagID, roomIDs, param.c.UserID(),
			liveroomtagrecord.RoleAdmin, liveroomtagrecord.OperationAdd)
	} else {
		err = liveroomtagrecord.AddRecords(nil, param.TagID, roomIDs, param.c.UserID(),
			liveroomtagrecord.RoleAdmin, liveroomtagrecord.OperationRemove)
	}
	if err != nil {
		logger.WithFields(logger.Fields{
			"is_add":   isAdd,
			"user_id":  param.c.UserID(),
			"room_ids": roomIDs,
		}).Error(err)
		// PASS: 房间的 tag 已经加了，设置白名单出错不影响发 im 消息给用户
	}
	err = userapi.BroadcastMany(elems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
