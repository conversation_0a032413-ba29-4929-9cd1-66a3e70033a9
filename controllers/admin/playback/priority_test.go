package playback

import (
	"net/http"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	service.SetDBUseSQLite()

	os.Exit(m.Run())
}

func TestActionPlaybackPrioritySet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{
		"_id":      "5f0581867cf6c8a9bdbf6447",
		"priority": 1,
	}

	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionPlaybackPrioritySet(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	collection := service.MongoDB.Collection(models.CollectionNamePlayback)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testRoomID := 20210218
	var res models.Playback
	err = collection.FindOneAndUpdate(ctx,
		bson.M{"room_id": testRoomID, "archive": models.ArchiveUnready},
		bson.M{"$set": bson.M{"room_id": testRoomID, "archive": models.ArchiveUnready}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&res)
	require.NoError(err)

	param["_id"] = res.OID
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionPlaybackPrioritySet(c)
	require.NoError(err)
}
