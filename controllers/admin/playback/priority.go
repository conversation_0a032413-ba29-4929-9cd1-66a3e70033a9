package playback

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// ActionPlaybackPrioritySet 设置指定直播回放处理的优先级
/**
 * @api {post} /api/v2/admin/playback/priority/set 设置指定直播回放处理的优先级
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/playback
 *
 * @apiParam {String} _id 回放记录 ID
 * @apiParam {Number} priority 回放处理优先级，数值越大越优先
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionPlaybackPrioritySet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		OID      primitive.ObjectID `form:"_id" json:"_id"`
		Priority int                `form:"priority" json:"priority"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	ok, err := models.SetPriority(param.OID, param.Priority)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrCannotFindResource
	}

	// 发送管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置指定直播回放处理的优先级，_id: %s, 优先级: %d", param.OID.Hex(), param.Priority)
	box.Add(userapi.CatalogPlaybackPrioritySet, intro)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}
