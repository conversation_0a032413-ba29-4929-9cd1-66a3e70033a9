package giftwall

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ErrShowGiftIDRepeat 上墙礼物重复绑定错误
func ErrShowGiftIDRepeat(giftIDs []int64) error {
	return actionerrors.ErrParamsMsg(fmt.Sprintf("该礼物已被其他上墙礼物绑定: %s", goutil.JoinInt64Array(giftIDs, ",")))
}

type giftListParam struct {
	p, pageSize int64

	giftWallGifts []*giftwall.Gift
	giftMap       map[int64]*gift.Gift
	pa            goutil.Pagination
}

type giftWallListResp struct {
	Data       []*giftwall.ListItem `json:"data"`
	Pagination goutil.Pagination    `json:"pagination"`
}

// ActionGiftList 礼物墙列表
/**
 * @api {get} /api/v2/admin/giftwall/gift/list 礼物墙上墙礼物列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 分页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "show_gift_id": "1",
 *           "show_gift_name": "show-礼物",
 *           "show_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/001.png",
 *           "target_gift_id": "2",
 *           "target_gift_name": "target-礼物",
 *           "target_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/002.png",
 *           "effective_gift_id": "3,4,5",
 *           "effective_gift_name": "礼物3,礼物4,礼物5",
 *           "effective_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/003.png" // 只取 effective_gift 下第一个礼物的图标地址
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionGiftList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGiftListParam(c)
	if err != nil {
		return nil, err
	}
	err = param.findGifts()
	if err != nil {
		return nil, err
	}
	return param.resp(), nil
}

func newGiftListParam(c *handler.Context) (*giftListParam, error) {
	param := new(giftListParam)
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *giftListParam) findGifts() error {
	var err error
	param.giftWallGifts, param.pa, err = giftwall.ListGifts(param.p, param.pageSize)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 初始化 cap 为礼物墙包含的所有 ID（1*show + 1*target + 3*effective），默认 EffectiveGiftIDs 包含 3 个 ID
	giftIDs := make([]int64, 0, len(param.giftWallGifts)*5)
	for _, g := range param.giftWallGifts {
		giftIDs = append(giftIDs, g.ShowGiftID, g.TargetGiftID)
		giftIDs = append(giftIDs, g.EffectiveGiftIDs...)
	}
	param.giftMap, err = gift.FindGiftMapByGiftIDs(giftIDs) // FindGiftMapByGiftIDs 中已对 gift_ids 去重，这里无需重复去重
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *giftListParam) resp() *giftWallListResp {
	list := make([]*giftwall.ListItem, 0, len(param.giftWallGifts))
	for _, wg := range param.giftWallGifts {
		list = append(list, giftwall.NewListItem(wg, param.giftMap))
	}
	return &giftWallListResp{
		Data:       list,
		Pagination: param.pa,
	}
}

func checkGiftWallGiftIDs(showGiftID, targetGiftID int64, effectiveGiftIDs []int64) error {
	// 校验礼物 ID 是否有效
	giftIDs := []int64{showGiftID, targetGiftID}
	giftIDs = append(giftIDs, effectiveGiftIDs...)
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(giftIDs) == len(giftMap) {
		return nil
	}
	var invalidGiftIDs []int64
	for _, id := range giftIDs {
		if _, ok := giftMap[id]; !ok {
			invalidGiftIDs = append(invalidGiftIDs, id)
		}
	}
	if len(invalidGiftIDs) != 0 {
		return actionerrors.ErrInvalidGiftIDs(invalidGiftIDs)
	}
	return nil
}

type giftAddParam struct {
	ShowGiftID       int64  `form:"show_gift_id" json:"show_gift_id"`
	TargetGiftID     int64  `form:"target_gift_id" json:"target_gift_id"`
	EffectiveGiftIDs string `form:"effective_gift_ids" json:"effective_gift_ids"`

	c                *handler.Context
	effectiveGiftIDs []int64
}

// ActionGiftAdd 新增礼物墙礼物关系
/**
 * @api {post} /api/v2/admin/giftwall/gift/add 新增上墙礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} show_gift_id 上墙礼物 ID
 * @apiParam {Number} target_gift_id 定位礼物 ID
 * @apiParam {String} effective_gift_ids 有效礼物 ID, 例如: 1,2,4 使用逗号分割 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "操作成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGiftAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGiftAddParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.add()
	if err != nil {
		return nil, err
	}
	return "操作成功", nil
}

func newGiftAddParam(c *handler.Context) (*giftAddParam, error) {
	param := new(giftAddParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.ShowGiftID <= 0 || param.TargetGiftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	// 解析有效礼物 ID
	param.effectiveGiftIDs, err = util.SplitToInt64Array(param.EffectiveGiftIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("有效礼物 ID 解析异常")
	}
	if len(param.effectiveGiftIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *giftAddParam) check() error {
	err := checkGiftWallGiftIDs(param.ShowGiftID, param.TargetGiftID, param.effectiveGiftIDs)
	if err != nil {
		return err
	}
	// 校验上墙礼物 ID 是否存在
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var g *giftwall.Gift
	err = giftwall.GiftCollection().FindOne(ctx,
		bson.M{"show_gift_id": param.ShowGiftID},
		options.FindOne().SetProjection(bson.M{"_id": 1})).Decode(&g)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if g != nil {
		return actionerrors.ErrParamsMsg("该上墙礼物已存在，不可重复添加")
	}
	// 校验有效礼物是否被其他上墙礼物绑定
	exists, ids, err := giftwall.ExistEffectiveGiftIDs(param.effectiveGiftIDs)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return ErrShowGiftIDRepeat(ids)
	}
	return nil
}

func (param *giftAddParam) add() error {
	err := giftwall.AddGiftWallGift(param.ShowGiftID, param.TargetGiftID, param.effectiveGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(param.c)
	box.Add(userapi.CatalogManageGiftWall,
		fmt.Sprintf("【新增礼物关系】上墙礼物 %d，定位礼物 %d，有效礼物 %s", param.ShowGiftID, param.TargetGiftID, param.EffectiveGiftIDs))
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// ActionGiftDel 删除礼物墙礼物关系
/**
 * @api {post} /api/v2/admin/giftwall/gift/del 删除上墙礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiPermission staff
 *
 * @apiParam {Number} show_gift_id 上墙礼物 ID
 * @apiParam {number=0,1} confirm 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "操作成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定删除上墙礼物：1000 barbie-doll 的礼物关系吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGiftDel(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		ShowGiftID int64 `form:"show_gift_id" json:"show_gift_id"`
		Confirm    int   `form:"confirm" json:"confirm"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	gwg, err := giftwall.FindOneByShowGiftID(param.ShowGiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if gwg == nil {
		return nil, actionerrors.ErrNotFound("未查询到该上墙礼物")
	}
	period, err := giftwall.FindUnfinishedPeriodByShowGiftID(param.ShowGiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 上墙礼物绑定了未开始或当前周期则不可被删除
	if period != nil {
		startDate := time.Unix(period.StartTime, 0).Format(util.TimeFormatYMD)
		endDate := time.Unix(period.EndTime, 0).Add(-24 * time.Hour).Format(util.TimeFormatYMD)
		return nil, actionerrors.NewErrForbidden(fmt.Sprintf("该上墙礼物已被当前周期 %s ~ %s 绑定", startDate, endDate))
	}
	if param.Confirm == 0 {
		g, err := gift.FindByGiftID(gwg.ShowGiftID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if g == nil {
			return nil, actionerrors.ErrNotFound("未查询到相关礼物信息")
		}
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确定删除上墙礼物：%d %s 的礼物关系吗？", g.GiftID, g.Name), 1)
	}
	err = giftwall.DelGiftByShowGiftID(param.ShowGiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageGiftWall,
		fmt.Sprintf("【删除礼物关系】上墙礼物 %d，定位礼物 %d，有效礼物 %v", gwg.ShowGiftID, gwg.TargetGiftID, goutil.JoinInt64Array(gwg.EffectiveGiftIDs, ",")))
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "操作成功", nil
}

type giftUpdateParam struct {
	ShowGiftID       int64  `form:"show_gift_id" json:"show_gift_id"`
	TargetGiftID     int64  `form:"target_gift_id" json:"target_gift_id"`
	EffectiveGiftIDs string `form:"effective_gift_ids" json:"effective_gift_ids"`

	c                *handler.Context
	effectiveGiftIDs []int64
}

// ActionGiftUpdate 更新礼物墙礼物关系
/**
 * @api {post} /api/v2/admin/giftwall/gift/update 更新礼物墙礼物关系
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} show_gift_id 上墙礼物 ID
 * @apiParam {Number} target_gift_id 定位礼物 ID
 * @apiParam {String} effective_gift_ids 有效礼物 ID, 例如: 1,2,4 使用逗号分割 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "操作成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGiftUpdate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newGiftUpdateParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.update()
	if err != nil {
		return nil, err
	}
	return "操作成功", nil
}

func newGiftUpdateParam(c *handler.Context) (*giftUpdateParam, error) {
	param := new(giftUpdateParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.ShowGiftID <= 0 || param.TargetGiftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	// 解析有效礼物 ID
	param.effectiveGiftIDs, err = util.SplitToInt64Array(param.EffectiveGiftIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("有效礼物 ID 解析异常")
	}
	if len(param.effectiveGiftIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *giftUpdateParam) check() error {
	err := checkGiftWallGiftIDs(param.ShowGiftID, param.TargetGiftID, param.effectiveGiftIDs)
	if err != nil {
		return err
	}

	// 查询上墙礼物关联关系
	gwgift, err := giftwall.FindOneByShowGiftID(param.ShowGiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if gwgift == nil {
		return actionerrors.ErrNotFound("未查询到该上墙礼物")
	}
	// 校验有效礼物是否被其他上墙礼物绑定
	exists, ids, err := giftwall.ExistEffectiveGiftIDs(param.effectiveGiftIDs, &giftwall.Options{IgnoreOID: gwgift.OID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return ErrShowGiftIDRepeat(ids)
	}
	return nil
}

func (param *giftUpdateParam) update() error {
	err := giftwall.UpdateOneByShowGiftID(param.ShowGiftID, param.TargetGiftID, param.effectiveGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(param.c)
	box.Add(userapi.CatalogManageGiftWall,
		fmt.Sprintf("【修改礼物关系】上墙礼物 %d，定位礼物 %d，有效礼物 %s", param.ShowGiftID, param.TargetGiftID, param.EffectiveGiftIDs))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
