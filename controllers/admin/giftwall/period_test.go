package giftwall

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionPeriodDetails(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	now := goutil.TimeNow()
	testGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().InsertOne(ctx, &giftwall.Gift{
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
		ShowGiftID:       testGiftID,
		TargetGiftID:     testGiftID,
		EffectiveGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, &giftwall.Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)

	uri := fmt.Sprintf("/api/v2/admin/giftwall/period/list?period_id=%s", res.InsertedID.(primitive.ObjectID).Hex())
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	resp, err := ActionPeriodDetails(c)
	require.NoError(err)
	require.IsType(handler.M{}, resp)
	data := resp.(handler.M)["data"].([]*giftwall.ListItem)
	assert.LessOrEqual(len(data), 20)
}

func TestNewPeriodDetailsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	now := goutil.TimeNow()
	testGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().InsertOne(ctx, &giftwall.Gift{
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
		ShowGiftID:       testGiftID,
		TargetGiftID:     testGiftID,
		EffectiveGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, &giftwall.Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)

	uri := fmt.Sprintf("/api/v2/admin/giftwall/period/list?period_id=%s", res.InsertedID.(primitive.ObjectID).Hex())
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	param, err := newPeriodDetailsParam(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestPeriodDetailsParam_findPeriodInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	now := goutil.TimeNow()
	testGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().InsertOne(ctx, &giftwall.Gift{
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
		ShowGiftID:       testGiftID,
		TargetGiftID:     testGiftID,
		EffectiveGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, &giftwall.Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: []int64{testGiftID},
	})
	require.NoError(err)

	param := &periodDetailsParam{
		periodOID: res.InsertedID.(primitive.ObjectID),
	}
	err = param.findPeriodInfo()
	require.NoError(err)
	assert.NotEmpty(param.giftWallGiftsMap)
	assert.NotEmpty(param.giftMap)
}

func TestPeriodDetailsParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftIDs := []int64{1000, 2000, 10095}
	param := &periodDetailsParam{
		giftIDs:          testGiftIDs,
		giftWallGiftsMap: make(map[int64]*giftwall.Gift),
		giftMap:          make(map[int64]*gift.Gift),
	}
	for _, id := range testGiftIDs {
		param.giftMap[id] = &gift.Gift{
			GiftID: id,
			Name:   "哈哈哈",
			Icon:   "https://static.missevan.com/gifts/avatarframes/003.png",
		}
		param.giftWallGiftsMap[id] = &giftwall.Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: testGiftIDs,
		}
	}

	resp := param.resp()
	require.Len(resp["data"], len(testGiftIDs))
	for i, id := range testGiftIDs {
		sg := resp["data"].([]*giftwall.ListItem)[i]
		if sg == nil {
			continue
		}
		assert.Equal(sg.ShowGiftID, strconv.FormatInt(id, 10), i)
	}
}

func TestActionPeriodDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime := now.Unix()
	endTime := now.Add(giftwall.PeriodDuration).Unix()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := giftwall.PeriodCollection().InsertOne(ctx, bson.M{
		"start_time": startTime,
		"end_time":   endTime,
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/period/del", true, handler.M{
		"period_id": res.InsertedID.(primitive.ObjectID).Hex(),
		"confirm":   0,
	})
	_, err = ActionPeriodDel(c)
	assert.EqualError(err, "只能删除未开始的周期")

	_, err = giftwall.PeriodCollection().UpdateOne(ctx,
		bson.M{"_id": res.InsertedID},
		bson.M{"$set": bson.M{"start_time": startTime + 1}},
	)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/period/del", true, handler.M{
		"period_id": res.InsertedID.(primitive.ObjectID).Hex(),
		"confirm":   0,
	})
	_, err = ActionPeriodDel(c)
	assert.Equal(err, actionerrors.ErrConfirmRequired(fmt.Sprintf("确定删除礼物墙周期吗？<br>%s 至 %s", "2000-01-03", "2000-01-16"), 1, true))

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/period/del", true, handler.M{
		"period_id": res.InsertedID.(primitive.ObjectID).Hex(),
		"confirm":   1,
	})
	resp, err := ActionPeriodDel(c)
	require.NoError(err)
	assert.Equal("删除成功", resp)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/period/del", true, handler.M{
		"period_id": res.InsertedID.(primitive.ObjectID).Hex(),
		"confirm":   1,
	})
	_, err = ActionPeriodDel(c)
	assert.EqualError(err, "该周期不存在")
}

func TestActionPeriodAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(giftwall.PeriodDuration)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().InsertMany(ctx, []interface{}{
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       1000,
			TargetGiftID:     1000,
			EffectiveGiftIDs: []int64{1000},
		},
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       2000,
			TargetGiftID:     2000,
			EffectiveGiftIDs: []int64{2000},
		},
	})
	require.NoError(err)
	_, err = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"start_date":    startTime.Format(util.TimeFormatYMD),
		"end_date":      endTime.Add(-1 * time.Second).Format(util.TimeFormatYMD),
		"show_gift_ids": "1000,2000",
		"rewards": []*giftwall.RewardInfo{{
			Threshold: 1,
			Type:      1,
			ElementID: 1000,
		}, {
			Threshold: 2,
			Type:      2,
			ElementID: 1000,
		}},
		"confirm": 1,
	})
	resp, err := ActionPeriodAdd(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal("提交成功", resp.(string))

	ps, err := giftwall.FindPeriodsByType(startTime, endTime, giftwall.PeriodTypeNormal)
	require.NoError(err)
	require.NotEmpty(ps)
	assert.Equal([]int64{1000, 2000}, ps[0].ShowGiftIDs)
}

func TestNewPeriodAddParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()

	tests := []struct {
		name    string
		wantErr error
		c       *handler.Context
	}{
		{
			name:    "周期持续时间有误",
			wantErr: actionerrors.ErrParamsMsg("周期日期范围非 2 个自然周"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(time.Hour).Format(util.TimeFormatYMD)}),
		},
		{
			name:    "上墙礼物未输入",
			wantErr: actionerrors.ErrParamsMsg("请输入上墙礼物名单"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD)}), // 真实输入是：2号～16号，故 -1s 将时间调整到 15号
		},
		{
			name:    "点亮目标和奖励设置未输入",
			wantErr: actionerrors.ErrParamsMsg("请输入点亮目标和奖励设置"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2"}),
		},
		{
			name:    "点亮目标和奖励设置输入异常",
			wantErr: actionerrors.ErrParamsMsg("请输入点亮目标和奖励设置"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2", "rewards": []*giftwall.RewardInfo{}}),
		},
		{
			name:    "点亮目标输入异常",
			wantErr: actionerrors.ErrParamsMsg("第 1 项点亮目标数错误"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2", "rewards": []*giftwall.RewardInfo{{Threshold: 0, Type: 1, ElementID: 100}, {Threshold: 2, Type: 2, ElementID: 200}}}),
		},
		{
			name:    "奖励类型选择异常",
			wantErr: actionerrors.ErrParamsMsg("第 1 项奖励类型错误"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2", "rewards": []*giftwall.RewardInfo{{Threshold: 1, Type: 9, ElementID: 100}, {Threshold: 2, Type: 2, ElementID: 200}}}),
		},
		{
			name:    "奖励 ID 输入异常",
			wantErr: actionerrors.ErrParamsMsg("第 1 项奖励 ID 错误"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2", "rewards": []*giftwall.RewardInfo{{Threshold: 1, Type: 1, ElementID: -1}, {Threshold: 2, Type: 2, ElementID: 200}}}),
		},
		{
			name:    "礼物墙类型错误",
			wantErr: actionerrors.ErrParamsMsg("礼物墙类型错误"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "type": 233, "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
		{
			name:    "未设置礼物墙白名单",
			wantErr: actionerrors.ErrParamsMsg("请先上传白名单"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "type": 1, "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
		{
			name:    "礼物墙周期M号白名单格式错误",
			wantErr: actionerrors.ErrParamsMsg("礼物墙周期M号白名单格式错误"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "type": 1, "csv_url": "https://fm.example.com/testdata/add-giftwall.csv", "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
		{
			name:    "白名单内数据有重复",
			wantErr: actionerrors.ErrParamsMsg("白名单内数据有重复"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "type": 1, "csv_url": "https://fm.example.com/testdata/add-giftwall-repeat.csv", "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
		{
			name:    "白名单内容需为M号",
			wantErr: actionerrors.ErrParamsMsg("白名单内容需为M号"),
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "type": 1, "csv_url": "https://fm.example.com/testdata/add-giftwall-notfound.csv", "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
		{
			name:    "pass",
			wantErr: nil,
			c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{"start_date": now.Format(util.TimeFormatYMD), "end_date": now.Add(giftwall.PeriodDuration - time.Second).Format(util.TimeFormatYMD), "show_gift_ids": "1,2", "rewards": json.RawMessage(`[{"threshold":1,"type":1,"element_id":100},{"threshold":2,"type":2,"element_id":200}]`)}),
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.name == "白名单内数据有重复" {
				testCreatorIDs := [][]string{
					{"主播 M 号"}, {"14"}, {"14"},
				}
				testFilePath := "../../../testdata/add-giftwall-repeat.csv"
				require.NoError(createTestCSV(testFilePath, testCreatorIDs))
				defer cleanTestFile(testFilePath)
			} else if test.name == "白名单内容需为M号" {
				testCreatorIDs := [][]string{
					{"主播 M 号"}, {"6862165"},
				}
				testFilePath := "../../../testdata/add-giftwall-notfound.csv"
				require.NoError(createTestCSV(testFilePath, testCreatorIDs))
				defer cleanTestFile(testFilePath)
			}

			param, err := newPeriodAddParam(test.c)
			require.Equal(test.wantErr, err)
			if err != nil {
				return
			}
			require.NotNil(param.rewards)
			assert.EqualValues(1, param.rewards[0].Threshold)
			assert.EqualValues(2, param.rewards[1].Threshold)
			assert.EqualValues(giftwall.RewardTypeCustomGift, param.rewards[0].Type)
			assert.EqualValues(giftwall.RewardTypeAppearance, param.rewards[1].Type)
			assert.EqualValues(100, param.rewards[0].ElementID)
			assert.EqualValues(200, param.rewards[1].ElementID)
		})
	}
}

func createTestCSV(filePath string, data [][]string) error {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			return err
		}
	}

	// 创建 CSV 文件
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入 CSV 数据
	writer := csv.NewWriter(file)
	defer writer.Flush()
	if err := writer.WriteAll(data); err != nil {
		return err
	}
	return nil
}

func cleanTestFile(filePath string) {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			logger.Error(err)
		}
	}
}

func TestPeriodAddParam_check(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(giftwall.PeriodDuration)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{})
	require.NoError(err)
	_, err = giftwall.PeriodCollection().InsertOne(ctx, bson.M{"start_time": startTime.Unix(), "end_time": endTime.Unix(), "type": giftwall.PeriodTypeNormal})
	require.NoError(err)
	service.Cache5Min.Flush()

	param := &periodAddParam{startTime: startTime, endTime: endTime, Type: giftwall.PeriodTypeNormal}
	err = param.check()
	assert.EqualError(err, "已存在该礼物墙周期！")

	param = &periodAddParam{
		startTime: endTime.Add(giftwall.PeriodMaxSpanDuration),
		endTime:   endTime.Add(giftwall.PeriodMaxSpanDuration),
		Type:      giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "仅可创建未来的 5 个周期")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration + time.Hour),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000},
		Type:      giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "当前选择的周期与上个礼物墙周期间隔了奇数周")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{9999},
		Type:      giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "存在无效礼物 ID，请检查后重试")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 1000},
		Type:      giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "存在重复的礼物 ID，请检查后重试")

	_, err = giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000}},
	})
	require.NoError(err)
	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		Type:      giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "存在尚未录入的上墙礼物，请先前往上墙礼物管理后台录入: 1000,2000")

	_, err = giftwall.GiftCollection().InsertMany(ctx, []interface{}{
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       1000,
			TargetGiftID:     1000,
			EffectiveGiftIDs: []int64{1000},
		},
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       2000,
			TargetGiftID:     2000,
			EffectiveGiftIDs: []int64{2000},
		},
	})
	require.NoError(err)

	// 测试礼物已被其他类型的礼物墙使用
	_, err = giftwall.PeriodCollection().InsertOne(ctx, bson.M{"start_time": param.startTime.Unix(), "end_time": param.endTime.Unix(), "type": giftwall.PeriodTypePremium, "show_gift_ids": []int64{1000}})
	require.NoError(err)
	err = param.check()
	assert.EqualError(err, "有礼物在甄选礼物墙上生效，无法加入经典礼物墙。礼物 IDs: 1000")
	_, err = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"type": giftwall.PeriodTypePremium})
	require.NoError(err)

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 3, Type: giftwall.RewardTypeCustomGift, ElementID: 100},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "点亮目标数不得超过上墙礼物数量")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: 9, ElementID: 1000},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "参数错误")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
			{Threshold: 1, Type: giftwall.RewardTypeAppearance, ElementID: 1000},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "点亮目标数不得重复或者需要递增")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 2, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
			{Threshold: 1, Type: giftwall.RewardTypeAppearance, ElementID: 1000},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "点亮目标数不得重复或者需要递增")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
			{Threshold: 2, Type: giftwall.RewardTypeCustomGift, ElementID: 9999},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "点亮目标和奖励，存在无效礼物 ID: 9999")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeAppearance, ElementID: 1000},
			{Threshold: 2, Type: giftwall.RewardTypeAppearance, ElementID: 9999},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "点亮目标和奖励，存在无效外观 ID: 9999")

	param = &periodAddParam{
		startTime: startTime.Add(giftwall.PeriodDuration),
		endTime:   endTime.Add(giftwall.PeriodDuration),
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
			{Threshold: 2, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
		},
		Type: giftwall.PeriodTypeNormal,
	}
	err = param.check()
	assert.EqualError(err, "奖励的礼物 ID 不得重复")
}

func TestPeriodAddParam_insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000}},
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertMany(ctx, []interface{}{
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       1000,
			TargetGiftID:     1000,
			EffectiveGiftIDs: []int64{1000},
		},
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       2000,
			TargetGiftID:     2000,
			EffectiveGiftIDs: []int64{2000},
		},
	})
	require.NoError(err)
	startTime, endTime := now, now.Add(giftwall.PeriodDuration)
	_, err = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": startTime.Unix(),
		"end_time":   endTime.Unix(),
	})
	require.NoError(err)

	param := &periodAddParam{
		StartDate: startTime.Format(util.TimeFormatYMD),
		EndDate:   endTime.Format(util.TimeFormatYMD),
		Type:      giftwall.PeriodTypeNormal,
		giftIDs:   []int64{1000, 2000},
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1000},
			{Threshold: 2, Type: giftwall.RewardTypeCustomGift, ElementID: 2000},
			{Threshold: 3, Type: giftwall.RewardTypeAppearance, ElementID: 3000},
		},
		rewardGifts: map[int64]*gift.Gift{
			1000: {Name: "一千"},
			2000: {Name: "两千"},
		},
		rewardAppearances: map[int64]*appearance.Appearance{
			3000: {Name: "年度冠军"},
		},
		c:         handler.NewTestContext(http.MethodPost, "", true, handler.M{}),
		startTime: now,
		endTime:   now.Add(giftwall.PeriodDuration),
	}
	err = param.insert()
	assert.Equal(err, actionerrors.ErrConfirmRequired(fmt.Sprintf("确定创建礼物墙周期吗？<br>"+
		`<li>周期时间：<font color="red">%s 至 %s</font></li>`+
		`<li>礼物墙类型：%s</li>`+
		`<li>上墙礼物数量：<font color="red">%d</font></li>`+
		"<li>点亮目标及奖励：%s</li>",
		"2000-01-03", "2000-01-16", "经典礼物墙", len(param.giftIDs), "[1,直播间专属礼物,一千],[2,直播间专属礼物,两千],[3,直播间外观,年度冠军]"), 1, true))

	param.Confirm = 1
	err = param.insert()
	require.NoError(err)
	ps, err := giftwall.FindPeriodsByType(startTime, endTime, giftwall.PeriodTypeNormal)
	require.NoError(err)
	require.NotEmpty(ps)
	assert.Equal([]int64{1000, 2000}, ps[0].ShowGiftIDs)
}

func mockUpdateShowGiftIDsData(t *testing.T) interface{} {
	require := require.New(t)

	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(giftwall.PeriodDuration)
	testGiftID1000, testGiftID2000 := int64(1000), int64(2000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{testGiftID1000, testGiftID2000}},
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertMany(ctx, []interface{}{
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       testGiftID1000,
			TargetGiftID:     testGiftID1000,
			EffectiveGiftIDs: []int64{testGiftID1000},
		},
		&giftwall.Gift{
			CreateTime:       now.Unix(),
			ModifiedTime:     now.Unix(),
			ShowGiftID:       testGiftID2000,
			TargetGiftID:     testGiftID2000,
			EffectiveGiftIDs: []int64{testGiftID2000},
		},
	})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, &giftwall.Period{
		StartTime:   startTime.Unix(),
		EndTime:     endTime.Unix(),
		Type:        giftwall.PeriodTypeNormal,
		ShowGiftIDs: []int64{testGiftID1000, testGiftID2000},
	})
	require.NoError(err)
	return res.InsertedID
}

func TestActionPeriodUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	oid := mockUpdateShowGiftIDsData(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"confirm":       1,
		"period_id":     oid,
		"show_gift_ids": "1000,2000",
	})
	resp, err := ActionPeriodUpdate(c)
	require.NoError(err)
	require.NotEmpty(resp)
	p, err := giftwall.FindByPeriodOID(oid.(primitive.ObjectID))
	require.NoError(err)
	assert.Equal([]int64{1000, 2000}, p.ShowGiftIDs)
}

func TestNewPeriodUpdateParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid := mockUpdateShowGiftIDsData(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"period_id": "",
	})
	_, err := newPeriodUpdateParam(c)
	assert.EqualError(err, "周期 ID 错误")

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"period_id":     oid.(primitive.ObjectID).Hex(),
		"show_gift_ids": "",
		"rewards":       []*giftwall.RewardInfo{},
	})
	_, err = newPeriodUpdateParam(c)
	assert.EqualError(err, "至少更新一项")

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"period_id":     oid.(primitive.ObjectID).Hex(),
		"show_gift_ids": "1000,2000",
		"rewards": []*giftwall.RewardInfo{{
			Threshold: 1,
			Type:      1,
			ElementID: 1000,
		}, {
			Threshold: 2,
			Type:      2,
			ElementID: 1000,
		}},
	})
	param, err := newPeriodUpdateParam(c)
	require.NoError(err)
	require.NotNil(param)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"period_id":     oid.(primitive.ObjectID).Hex(),
		"show_gift_ids": "。",
	})
	_, err = newPeriodUpdateParam(c)
	assert.EqualError(err, "上墙礼物 ID 格式错误")
}

func TestPeriodUpdateParam_check(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	oid := mockUpdateShowGiftIDsData(t)

	tests := []struct {
		name    string
		param   *periodUpdateParam
		wantErr string
	}{
		{
			"90900,1000",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				giftIDs:  []int64{90900, 1000},
			},
			"存在无效礼物 ID，请检查后重试",
		},
		{
			"1000,1000,2000",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				giftIDs:  []int64{1000, 1000, 2000},
			},
			"存在重复礼物 ID，请检查后重试",
		},
		{
			"1000,40049",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				giftIDs:  []int64{1000, 40049},
			},
			"存在尚未录入的上墙礼物，请先前往上墙礼物管理后台录入: 40049",
		},
		{
			"Type 参数错误",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				rewards: []*giftwall.RewardInfo{
					{Threshold: 1, Type: 10, ElementID: 100},
					{Threshold: 2, Type: 2, ElementID: 200},
				},
			},
			"参数错误",
		},
		{
			"点亮目标数不得超过上墙礼物数量",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				rewards: []*giftwall.RewardInfo{
					{Threshold: 3, Type: 1, ElementID: 100},
					{Threshold: 4, Type: 2, ElementID: 200},
				},
			},
			"点亮目标数不得超过上墙礼物数量",
		},
		{
			"点亮目标数不得重复或者需要递增",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				rewards: []*giftwall.RewardInfo{
					{Threshold: 2, Type: 1, ElementID: 100},
					{Threshold: 2, Type: 2, ElementID: 200},
				},
			},
			"点亮目标数不得重复或者需要递增",
		},
		{
			"点亮目标数不得重复或者需要递增",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				rewards: []*giftwall.RewardInfo{
					{Threshold: 2, Type: 1, ElementID: 100},
					{Threshold: 1, Type: 2, ElementID: 200},
				},
			},
			"点亮目标数不得重复或者需要递增",
		},
		{
			"经典礼物墙更新礼物墙M号白名单",
			&periodUpdateParam{
				PeriodID: oid.(primitive.ObjectID).Hex(),
				rewards: []*giftwall.RewardInfo{
					{Threshold: 2, Type: 1, ElementID: 100},
				},
				CSVURL: "test.CSV",
			},
			"周期 id 为经典礼物墙，无法上传白名单",
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.param.check()
			assert.EqualError(err, test.wantErr)
		})
	}
}

func TestPeriodUpdateParam_update(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	oid := mockUpdateShowGiftIDsData(t)

	objectOID := oid.(primitive.ObjectID)
	p, err := giftwall.FindByPeriodOID(objectOID)
	require.NoError(err)
	param := &periodUpdateParam{
		Confirm:     0,
		PeriodID:    oid.(primitive.ObjectID).Hex(),
		ShowGiftIDs: "1,2",
		giftIDs:     []int64{1000, 2},
		c:           handler.NewTestContext(http.MethodPost, "", true, handler.M{}),
		rewards: []*giftwall.RewardInfo{
			{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 4000},
			{Threshold: 2, Type: giftwall.RewardTypeCustomGift, ElementID: 5000},
			{Threshold: 3, Type: giftwall.RewardTypeAppearance, ElementID: 6000},
		},
		rewardGifts: map[int64]*gift.Gift{
			4000: {Name: "四千"},
			5000: {Name: "五千"},
		},
		rewardAppearances: map[int64]*appearance.Appearance{
			6000: {Name: "座驾"},
		},
		periodHasStarted: true,
		period:           p,
	}
	// 测试已开始的周期的弹窗
	err = param.update()
	assert.Equal(err, actionerrors.ErrConfirmRequired("确定更新礼物墙周期吗？<br>周期：2000-01-01 ~ 2000-01-14<br>删除礼物 ID: 2000<br>增加礼物 ID: 2<br>上墙礼物总量：2<br>【新增】点亮目标及奖励：[1,直播间专属礼物,四千],[2,直播间专属礼物,五千],[3,直播间外观,座驾]", 1, true))
	// 测试已开始的周期修改成功
	param.Confirm = 1
	err = param.update()
	require.NoError(err)
	p, err = giftwall.FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{1000, 2}, p.ShowGiftIDs)
	assert.Equal(param.rewards, p.Rewards)

	param = &periodUpdateParam{
		Confirm:     0,
		PeriodID:    oid.(primitive.ObjectID).Hex(),
		ShowGiftIDs: "1,2",
		giftIDs:     []int64{2000, 2},
		c:           handler.NewTestContext(http.MethodPost, "", true, handler.M{}),
		rewards: []*giftwall.RewardInfo{
			{Threshold: 7, Type: giftwall.RewardTypeCustomGift, ElementID: 7000},
			{Threshold: 8, Type: giftwall.RewardTypeCustomGift, ElementID: 8000},
			{Threshold: 9, Type: giftwall.RewardTypeAppearance, ElementID: 9000},
		},
		rewardGifts: map[int64]*gift.Gift{
			7000: {Name: "七千"},
			8000: {Name: "八千"},
		},
		rewardAppearances: map[int64]*appearance.Appearance{
			9000: {Name: "年度最佳"},
		},
		periodHasStarted: false,
		period:           p,
	}
	// 测试未开始的周期的弹窗
	err = param.update()
	assert.Equal(err, actionerrors.ErrConfirmRequired("确定更新礼物墙周期吗？<br>周期：2000-01-01 ~ 2000-01-14<br>删除礼物 ID: 1000<br>增加礼物 ID: 2000<br>上墙礼物总量：2<br>【覆盖】点亮目标及奖励：[7,直播间专属礼物,七千],[8,直播间专属礼物,八千],[9,直播间外观,年度最佳]", 1, true))
	// 测试未开始的周期修改成功
	param.Confirm = 1
	err = param.update()
	require.NoError(err)
	p, err = giftwall.FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2000, 2}, p.ShowGiftIDs)
	assert.Equal(param.rewards, p.Rewards)
}

func TestFormatGiftIDs(t *testing.T) {
	assert := assert.New(t)

	str := formatGiftIDs([]string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19"})
	assert.Equal("1,2,3,4,5,6,7,<br>8,9,10,11,12,13,14,<br>15,16,17,18,19", str)

	str = formatGiftIDs([]string{"1", "2", "3", "4", "5", "6", "7", "8"})
	assert.Equal("1,2,3,4,5,6,7,<br>8", str)

	str = formatGiftIDs([]string{"1", "2", "3", "4", "5", "6"})
	assert.Equal("1,2,3,4,5,6", str)
}

func TestFindRewardGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	g1 := gift.Gift{
		OID:       primitive.NewObjectID(),
		Name:      "测试直播间专属礼物 1",
		NameClean: "测试直播间专属礼物 1",
		GiftID:    50100,
		Icon:      "oss://live/gifts/icons/1000.png",
		Type:      gift.TypeRoomCustom,
	}
	g2 := gift.Gift{
		OID:       primitive.NewObjectID(),
		Name:      "测试直播间专属礼物 2",
		NameClean: "测试直播间专属礼物 2",
		GiftID:    50101,
		Icon:      "oss://live/gifts/icons/1000.png",
		Type:      gift.TypeRoomCustom,
	}
	_, err := gift.Collection().DeleteMany(ctx,
		bson.M{"gift_id": bson.M{"$in": []int64{g1.GiftID, g2.GiftID}}})
	require.NoError(err)
	_, err = gift.Collection().InsertMany(ctx, []interface{}{g1, g2})
	require.NoError(err)

	rewardGifts, err := findRewardGifts([]int64{g1.GiftID, g2.GiftID})
	require.NoError(err)
	require.NotNil(rewardGifts[g1.GiftID])
	require.NotNil(rewardGifts[g2.GiftID])
	assert.Equal(g1.GiftID, rewardGifts[g1.GiftID].GiftID)
	assert.Equal("测试直播间专属礼物 1", rewardGifts[g1.GiftID].Name)
	assert.Equal(g2.GiftID, rewardGifts[g2.GiftID].GiftID)
	assert.Equal("测试直播间专属礼物 2", rewardGifts[g2.GiftID].Name)
}

func TestFindRewardAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	a1 := &appearance.Appearance{
		ID:             1000,
		Name:           "测试查询外观模版 1",
		Type:           appearance.TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &appearance.MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime: now.Unix(),
	}
	a2 := &appearance.Appearance{
		ID:             1001,
		Name:           "测试查询外观模版 2",
		Type:           appearance.TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &appearance.MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime: now.Unix(),
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{a1.ID, a2.ID}}})
	require.NoError(err)
	_, err = appearance.Collection().InsertMany(ctx, []interface{}{a1, a2})
	require.NoError(err)

	rewardAppearances, err := findRewardAppearances([]int64{a1.ID, a2.ID})
	require.NoError(err)
	require.NotNil(rewardAppearances[a1.ID])
	require.NotNil(rewardAppearances[a2.ID])
	assert.Equal(a1.ID, rewardAppearances[a1.ID].ID)
	assert.Equal(a2.ID, rewardAppearances[a2.ID].ID)
	assert.Equal("测试查询外观模版 1", rewardAppearances[a1.ID].Name)
	assert.Equal("测试查询外观模版 2", rewardAppearances[a2.ID].Name)
}

func TestFindRewardLiveStickers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不存在的专属表情
	_, err := findRewardLiveStickers([]int64{5467477})
	assert.EqualError(err, "点亮目标和奖励，存在无效直播间专属表情 ID: 5467477")

	// 测试获取专属表情
	p := livesticker.LiveSticker{
		ID:   234456,
		Name: "测试表情",
		Icon: "oss://sticker_package/superfans/icon.png",
	}
	require.NoError(service.LiveDB.Create(&p).Error)
	defer func() {
		require.NoError(service.LiveDB.Table(p.TableName()).Delete("", "id = ?", p.ID).Error)
	}()
	rewardLiveStickers, err := findRewardLiveStickers([]int64{p.ID})
	require.NoError(err)
	require.Len(rewardLiveStickers, 1)
	assert.Equal(p.ID, rewardLiveStickers[p.ID].ID)
	assert.Equal("测试表情", rewardLiveStickers[p.ID].Name)
	assert.Equal("https://static-test.missevan.com/sticker_package/superfans/icon.png", rewardLiveStickers[p.ID].Icon)
}
