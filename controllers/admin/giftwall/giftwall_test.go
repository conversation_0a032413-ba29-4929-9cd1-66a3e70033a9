package giftwall

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestActionGiftList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().InsertOne(ctx, &giftwall.Gift{
		CreateTime:       goutil.TimeNow().Unix(),
		ModifiedTime:     goutil.TimeNow().Unix(),
		ShowGiftID:       1000,
		TargetGiftID:     1000,
		EffectiveGiftIDs: []int64{1000, 2000, 10095},
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/chatroom/giftwall/gift/list", true, nil)
	resp, err := ActionGiftList(c)
	require.NoError(err)
	require.IsType(&giftWallListResp{}, resp)
	response := resp.(*giftWallListResp)
	assert.NotEmpty(response.Data)
	assert.LessOrEqual(len(response.Data), 20)
	assert.EqualValues(1, response.Pagination.P)
	assert.EqualValues(20, response.Pagination.PageSize)
}

func TestNewGiftWallListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/giftwall/gift/list", true, nil)
	param, err := newGiftListParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(1, param.p)
	assert.EqualValues(20, param.pageSize)
}

func TestGiftListParam_findGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftIDs := []int64{1000, 2000, 10095}
	param := &giftListParam{
		p:             1,
		pageSize:      20,
		giftWallGifts: make([]*giftwall.Gift, 0, len(testGiftIDs)),
	}
	for _, id := range testGiftIDs {
		param.giftWallGifts = append(param.giftWallGifts, &giftwall.Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: testGiftIDs,
		})
	}
	err := param.findGifts()
	require.NoError(err)
	assert.GreaterOrEqual(len(param.giftMap), 3)
}

func TestGiftListParam_resp(t *testing.T) {
	assert := assert.New(t)

	testGiftIDs := []int64{1000, 2000, 10095}
	param := &giftListParam{
		p:             1,
		pageSize:      20,
		giftWallGifts: make([]*giftwall.Gift, 0, len(testGiftIDs)),
		giftMap:       make(map[int64]*gift.Gift),
	}
	for _, id := range testGiftIDs {
		param.giftWallGifts = append(param.giftWallGifts, &giftwall.Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: testGiftIDs,
		})
		param.giftMap[id] = &gift.Gift{
			GiftID: id,
			Name:   "name",
			Icon:   "https://static.missevan.com/gifts/avatarframes/001.png",
		}
	}

	resp := param.resp()
	assert.Len(resp.Data, len(testGiftIDs))
}

func TestActionGiftAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       1000,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	resp, err := ActionGiftAdd(c)
	require.NoError(err)
	assert.Equal("操作成功", resp)
}

func TestNewGiftWallAddParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       1000,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	param, err := newGiftAddParam(c)
	require.NoError(err)
	require.NotNil(param)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	_, err = newGiftAddParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       1000,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,ss,10095",
	})
	_, err = newGiftAddParam(c)
	assert.EqualError(err, "有效礼物 ID 解析异常")
}

func TestGiftAddParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	param, err := newGiftAddParam(c)
	require.NoError(err)
	require.NotNil(param)
	err = param.check()
	require.NoError(err)

	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	param, err = newGiftAddParam(c)
	require.NoError(err)
	require.NotNil(param)
	err = param.check()
	assert.EqualError(err, "该上墙礼物已存在，不可重复添加")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       2000, // 存在的礼物 ID
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	param, err = newGiftAddParam(c)
	require.NoError(err)
	require.NotNil(param)
	err = param.check()
	assert.EqualError(err, "该礼物已被其他上墙礼物绑定: 1000")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/add", true, handler.M{
		"show_gift_id":       100000,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	param, err = newGiftAddParam(c)
	require.NoError(err)
	require.NotNil(param)
	err = param.check()
	assert.EqualError(err, "无效礼物 ID: 100000")
}

func TestGiftAddParam_add(t *testing.T) {
	assert := assert.New(t)

	param := &giftAddParam{
		ShowGiftID:       1000,
		TargetGiftID:     2000,
		effectiveGiftIDs: []int64{1000, 2000, 10095},
		c:                handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err := param.add()
	assert.NoError(err)
}

func TestActionGiftDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(giftwall.PeriodDuration)
	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": testShowGiftID,
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)
	service.Cache5Min.Flush()
	_, err = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"show_gift_ids": bson.M{"$in": bson.A{testShowGiftID}},
	})
	require.NoError(err)
	_, err = giftwall.PeriodCollection().InsertOne(ctx, bson.M{
		"start_time":    startTime.Unix(),
		"end_time":      endTime.Unix(),
		"show_gift_ids": []int64{testShowGiftID},
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/del", true, handler.M{
		"show_gift_id": testShowGiftID,
		"confirm":      1,
	})
	_, err = ActionGiftDel(c)
	startDate := time.Unix(startTime.Unix(), 0).Format(util.TimeFormatYMD)
	endDate := time.Unix(endTime.Unix(), 0).Add(-24 * time.Hour).Format(util.TimeFormatYMD)
	assert.EqualError(err, fmt.Sprintf("该上墙礼物已被当前周期 %s ~ %s 绑定", startDate, endDate))

	service.Cache5Min.Flush()
	_, err = giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"show_gift_ids": bson.M{"$in": bson.A{testShowGiftID}},
	})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/del", true, handler.M{
		"show_gift_id": testShowGiftID,
		"confirm":      1,
	})
	resp, err := ActionGiftDel(c)
	require.NoError(err)
	assert.Equal("操作成功", resp)
	g, err := giftwall.FindOneByShowGiftID(testShowGiftID)
	require.NoError(err)
	assert.Nil(g)
}

func TestActionGiftUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/giftwall/gift/update", true, handler.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": "1000,2000,10095",
	})
	resp, err := ActionGiftUpdate(c)
	require.NoError(err)
	assert.Equal("操作成功", resp)
}

func TestGiftUpdateParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	param := &giftUpdateParam{
		ShowGiftID:       testShowGiftID,
		TargetGiftID:     2000,
		effectiveGiftIDs: []int64{1000, 2000, 10095},
	}
	err = param.check()
	require.NoError(err)

	param = &giftUpdateParam{
		ShowGiftID:       60006, // 存在的礼物 ID
		TargetGiftID:     2000,
		effectiveGiftIDs: []int64{1000, 2000, 10095},
	}
	err = param.check()
	assert.EqualError(err, "未查询到该上墙礼物")

	param = &giftUpdateParam{
		ShowGiftID:       100000, // 不存在的礼物 ID
		TargetGiftID:     2000,
		effectiveGiftIDs: []int64{1000, 2000, 10095},
	}
	err = param.check()
	assert.EqualError(err, "无效礼物 ID: 100000")

	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       2000,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)
	param = &giftUpdateParam{
		ShowGiftID:       testShowGiftID,
		TargetGiftID:     2000,
		effectiveGiftIDs: []int64{1000, 2000, 10095},
	}
	err = param.check()
	assert.EqualError(err, "该礼物已被其他上墙礼物绑定: 2000")
}

func TestGiftUpdateParam_update(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	_, err = giftwall.GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	param := &giftUpdateParam{
		ShowGiftID:       testShowGiftID,
		TargetGiftID:     3000,
		effectiveGiftIDs: []int64{1000, 3000, 10095},
		c:                handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.update()
	assert.NoError(err)
}
