package giftwall

import (
	"fmt"
	"strings"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenotice"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

type addNoticeParam struct {
	Content string `form:"content" json:"content"`
	OpenURL string `form:"open_url" json:"open_url"`
	Confirm int    `form:"confirm" json:"confirm"`
}

// ActionGiftWallNoticeSet 添加礼物墙公告
/**
 * @api {post} /api/v2/admin/giftwall/notice/set 添加礼物墙公告
 * @apiDescription 新增公告会下线所有旧公告
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/giftwall
 *
 * @apiParam {String} content 公告内容
 * @apiParam {String} [open_url] 公告跳转链接
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "新增成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "当前已有生效中的公告，新增公告将替换线上已有的公告内容，确认要新增吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGiftWallNoticeSet(c *handler.Context) (handler.ActionResponse, error) {
	var param addNoticeParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.Content = strings.TrimSpace(param.Content)
	if param.Content == "" {
		return nil, actionerrors.ErrParams
	}
	if util.UTF8Width(param.Content) > 60 {
		return nil, actionerrors.ErrParamsMsg("公告最多支持 30 个中文字符")
	}
	param.OpenURL = strings.TrimSpace(param.OpenURL)
	if param.OpenURL != "" && !util.IsValidURL(param.OpenURL, false) {
		return nil, actionerrors.ErrParamsMsg("跳转链接仅支持前缀为 https:// 的地址")
	}

	if param.Confirm == 0 {
		exists, err := livenotice.ExistsType(livenotice.TypeGiftWall)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if exists {
			return nil, actionerrors.ErrConfirmRequired("当前已有生效中的公告，新增公告将替换线上已有的公告内容，确认要新增吗？", 1)
		}
		return nil, actionerrors.ErrConfirmRequired("确认要新增公告内容吗？", 1)
	}

	err = livenotice.AddGiftWallNotice(param.Content, param.OpenURL)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	livenotice.ClearCache(livenotice.TypeGiftWall)

	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("新增直播间礼物墙公告, content: %s, open_url: %s", param.Content, param.OpenURL)
	box.Add(userapi.CatalogManageGiftWall, intro)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "新增成功", nil
}

type removeNoticeParam struct {
	NoticeID int64 `form:"notice_id" json:"notice_id"`
}

// ActionGiftWallNoticeDel 下线礼物墙公告
/**
 * @api {post} /api/v2/admin/giftwall/notice/del 下线礼物墙公告
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/giftwall
 *
 * @apiParam {Number} notice_id 公告 id
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "下线成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionGiftWallNoticeDel(c *handler.Context) (handler.ActionResponse, error) {
	var param removeNoticeParam
	err := c.Bind(&param)
	if err != nil || param.NoticeID <= 0 {
		return nil, actionerrors.ErrParams
	}

	notice, err := livenotice.FindOne(param.NoticeID, livenotice.TypeGiftWall)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if notice == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	err = livenotice.DelGiftWallNotice(notice.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	livenotice.ClearCache(livenotice.TypeGiftWall)
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("下线礼物墙公告，id: %d", param.NoticeID)
	box.Add(userapi.CatalogManageGiftWall, intro)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "下线成功", nil
}
