package giftwall

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livenotice"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGiftWallNoticeSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := addNoticeParam{
		Content: "new content",
		OpenURL: "http://fm.missevan.com",
		Confirm: 0,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionGiftWallNoticeSet(c)
	assert.EqualError(err, "跳转链接仅支持前缀为 https:// 的地址")

	param.OpenURL = "https://fm.missevan.com"
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGiftWallNoticeSet(c)
	assert.EqualError(err, "当前已有生效中的公告，新增公告将替换线上已有的公告内容，确认要新增吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionGiftWallNoticeSet(c)
	require.NoError(err)
}

func TestActionGiftWallNoticeDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var l livenotice.LiveNotice
	require.NoError(l.DB().Take(&l, "type = ? AND delete_time = 0", livenotice.TypeGiftWall).Error)
	param := removeNoticeParam{
		NoticeID: l.ID,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionGiftWallNoticeDel(c)
	require.NoError(err)
	var ln livenotice.LiveNotice
	require.NoError(l.DB().Take(&ln, "id = ?", l.ID).Error)
	assert.NotZero(ln.DeleteTime)
}
