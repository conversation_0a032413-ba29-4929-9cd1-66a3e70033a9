package appearances

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionAppearanceAdd 新增外观资源
/**
 * @api {post} /api/v2/admin/appearance/add 新增外观资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} appearance_id 外观 ID
 * @apiParam {Number} appearance_type 外观类型
 * @apiParam {String} name 外观名称
 * @apiParam {String} intro 外观描述
 * @apiParam {number=1,2} from 外观来源 1：活动 2：定制
 * @apiParam {String[]} icon_urls 外观中心 icon
 * @apiParam {String[]} image_urls 资源文件
 * @apiParam {Number} [expire_time] 结束日期时间戳，单位：秒，不传表示永久有效
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "创建成功"
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionAppearanceAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	// API 通过 AppearanceParam 接口的方法实现增加外观的流程
	// 支持不同外观类型时只需要增加具体的结构体实现 AppearanceParam 接口
	return nil, "创建成功", nil
}

// ActionAppearanceUpdate 修改外观资源
/**
 * @api {post} /api/v2/admin/appearance/update 修改外观资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} appearance_id 外观 ID
 * @apiParam {Number} appearance_type 外观类型
 * @apiParam {String} [name] 外观名称
 * @apiParam {String} [intro] 外观描述
 * @apiParam {number=0,1,2} [from=0] 外观来源 0：不修改来源 1：活动 2：定制，不传表示不修改
 * @apiParam {String[]} [icon_urls] 外观中心 icon
 * @apiParam {String[]} [image_urls] 资源文件
 * @apiParam {number=0,1} update_expire_time 是否修改结束时间 0：不修改 1：修改，避免 expire_time 传 0 时产生是否修改结束时间的歧义
 * @apiParam {Number} [expire_time=0] 结束日期时间戳，当 update_expire_time 为 1 时生效，单位：秒，传 0 表示永久有效
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "修改成功"
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionAppearanceUpdate(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "修改成功", nil
}
