package admin

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("admin", h.Name)
	assert.Zero(len(h.Middlewares))
	assert.Equal(11, len(h.SubHandlers), "len(h.SubHandlers)")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := HandlerV2()
	assert.Equal("admin", h.Name)
	assert.Zero(len(h.Middlewares))
	assert.Equal(7, len(h.Sub<PERSON>and<PERSON>), "len(h.SubHandlers)")
}

func TestSubHandler(t *testing.T) {
	t.Run("activity", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := activityHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("activity", h.Name)
		kc.Check(h, "rank/increase")
	})
	t.Run("chatroom", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)

		hs := chatroomHandler().SubHandlers
		require.Len(hs, 5)

		h := hs[0]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h,
			"setmedal",
			"medal/add-multi", "medal/remove-multi",
			"channel/set", "channel/provider/set",
			"connect/provider/set",
			"score/set",
			"playback/priority/set",
			"gift/hide", "gift/reorder", "gift/setorder", "gift/custom/add", "gift/room/custom/add",
			"tag/set", "tag/editstatus", "tag/sortorder",
			"tag/room/add", "tag/room/del",
			"speak/set", "speak/del",
			"tag/allowlist/add", "tag/allowlist/remove",
			"shortcut/gift/add", "shortcut/gift/remove",
		)

		h = hs[1]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "hotsuppression/update")

		h = hs[2]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "liveshow/create", "liveshow/cancel")

		h = hs[3]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "gift/import")

		h = hs[4]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "close",
			"get", "reset",
			"ban", "updateban", "unban",
			"globalmute/add", "globalmute/remove", "globalmute/get",
			"catalog/set",
			"review/list", "review/pass", "review/refuse",
			"vitality/reduce", "vitality/increase",
			"question/cancel",
			"medal/pass", "medal/refuse", "medal/list",
		)
	})

	t.Run("guild", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		require := require.New(t)
		h := guildHandler()
		assert.Equal("guild", h.Name)
		require.Len(h.SubHandlers, 6)
		assert.Len(h.SubHandlers[0].Middlewares, 1)
		assert.Len(h.SubHandlers[1].Middlewares, 1)
		kc.Check(h.SubHandlers[0], "adminlist")
		kc.Check(h.SubHandlers[1], "adminpass", "adminreject")
		kc.Check(h.SubHandlers[2], "deleteguild", "updateguild", "guild-transfer-creator")
		kc.Check(h.SubHandlers[3], "terminate/batch-create")
		kc.Check(h.SubHandlers[4], "adminmanage", "guildrate", "guildsprofit", "guildprofitview")

		require.Len(h.SubHandlers[5].SubHandlers, 2)
		kc.Check(h.SubHandlers[5].SubHandlers[0], "squarehot/vacancy/list", "squarehot/vacancy/update", "schedule/list", "schedule/pass", "schedule/refuse")
		kc.Check(h.SubHandlers[5].SubHandlers[1], "banner/review/list", "banner/review/pass", "banner/review/reject", "banner/count")
	})
	t.Run("noble", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := nobleHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("noble", h.Name)
		kc.Check(h, "recommend/cancel", "horn/add")
	})
	t.Run("playback", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := playbackHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("playback", h.Name)
		kc.Check(h, "priority/set")
	})
	t.Run("recommended", func(t *testing.T) {
		require := require.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := recommendedHandler()
		require.Equal(2, len(h.SubHandlers))
		kc.Check(h.SubHandlers[0],
			"get", "set",
			"addevent", "delevent",
			"addliveicon", "delliveicon", "listliveicon",
			"addavatarframe", "delavatarframe", "listavatarframe",
			"addsquarehot", "delsquarehot",
			"addpopup", "editpopup", "delpopup",
			"listschedule", "listtopschedule", "addschedule", "editschedule", "delschedule",
			"listnobleroom",
			"setlivenotice", "listlivenotice",
			"listlivebanners", "listcurrentlivebanners", "addlivebanner", "editlivebanner", "dellivebanner",
			"listbanner", "setbanner", "delbanner",
			"addbackground", "delbackground",
			"blocklist/room/add", "blocklist/room/remove",
			"blocklist/guild/add", "blocklist/guild/remove",
			"creatorcard/add", "creatorcard/delete",
		)
		kc.Check(h.SubHandlers[1], "sound/message/add", "sound/message/delete")
	})
	t.Run("user", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := userHandler()
		require.Len(h.SubHandlers, 2)

		h1 := h.SubHandlers[0]
		assert.Equal("user", h1.Name)
		kc.Check(h1,
			"rank/love/set", "rank/love/get", "rank/love/publish",
			"wall/send",
			"givegift",
			"cleargift",
			"givecreatorgift",
			"clearcreatorgift",
			"reissuegift",
			"appearance/manage",
			"appearance/sync",
			"recommendnum/update",
			"medal/point/add",
		)

		h2 := h.SubHandlers[1]
		assert.Equal("user", h2.Name)
		kc.Check(h2,
			"ban/add", "ban/remove",
		)
	})
	t.Run("exclusive", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := exclusiveHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("exclusive", h.Name)
		kc.Check(h, "creator/list", "creator/add", "creator/edit", "creator/delete")
	})
	t.Run("withdrawal", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := withdrawalHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("withdrawal", h.Name)
		kc.Check(h, "setmonthlimit", "setmin")
	})
	t.Run("diygift", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := diyGiftHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("diygift", h.Name)
		kc.Check(h, "list")
	})
	t.Run("giftwall", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := giftWallHandler()
		assert.Len(h.Middlewares, 1)
		assert.Equal("giftwall", h.Name)
		kc.Check(h, "gift/list", "gift/add", "gift/del", "gift/update",
			"period/details", "period/add", "period/update", "period/del",
			"notice/set", "notice/del")
	})
	t.Run("chatroom v2", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)

		hs := chatroomHandlerV2().SubHandlers
		require.Len(hs, 3)

		h := hs[0]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "quest/import", "quest/del",
			"bubble/add", "bubble/edit", "bubble/info",
			"gift/set-online")

		h = hs[1]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "gift/exclusive/batch-add", "gift/exclusive/batch-delete")

		h = hs[2]
		assert.Len(h.Middlewares, 1)
		assert.Equal("chatroom", h.Name)
		kc.Check(h, "hornmute/add", "hornmute/remove")
	})
	t.Run("noble v2", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := nobleHandlerV2()
		assert.Len(h.Middlewares, 1)
		assert.Equal("noble", h.Name)
		kc.Check(h, "horn/remove")
	})
	t.Run("sticker v2", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := stickerHandlerV2()
		assert.Len(h.Middlewares, 1)
		assert.Equal("sticker", h.Name)
		kc.Check(h, "add", "update", "assign/add", "assign/update", "assign/del")
	})
	t.Run("luckygift v2", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := luckyGiftHandlerV2()
		assert.Len(h.Middlewares, 1)
		assert.Equal("luckygift", h.Name)
		kc.Check(h, "drop/config/import", "drop/config/del", "drop/config/clear-valid")
	})
	t.Run("daily-task v2", func(t *testing.T) {
		assert := assert.New(t)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		h := dailyTaskHandlerV2()
		assert.Len(h.Middlewares, 1)
		assert.Equal("daily-task", h.Name)
		kc.Check(h, "allowlist/import", "allowlist/delete")
	})
}
