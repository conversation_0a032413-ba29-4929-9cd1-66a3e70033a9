package diygifts

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygiftdresses"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifteffects"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type diyGiftData struct {
	GiftID              int64  `json:"gift_id"`
	GiftType            string `json:"gift_type"`
	Words               string `json:"words"`
	Avatars             string `json:"avatars"`
	DressCount          int    `json:"dress_count"`
	EffectCount         int    `json:"effect_count"`
	UploadedEffectCount int    `json:"uploaded_effect_count"`
	Status              string `json:"status"`
	CreateTime          int64  `json:"create_time"`
	ModifiedTime        int64  `json:"modified_time"`
}

type diyGiftListResp struct {
	Data       []diyGiftData     `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionDiyGiftList 超管获取 diy 礼物列表
/**
 * @api {get} /api/v2/admin/diygift/list 超管获取 diy 礼物列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 *
 * @apiSuccessExample:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "gift_id": 1,
 *           "gift_type": "贵族礼物-上神",
 *           "words": "有", // 赠言配置
 *           "avatars": "有", // 头像配置
 *           "dress_count": 2, // 特效配置项数量
 *           "effect_count": 2, // 应有特效数量
 *           "uploaded_effect_count": 2, // 已上传特效数量
 *           "status": "生效中", // 状态 0：未生效；1：生效中
 *           "create_time": 1650959826,
 *           "modified_time": 1650959826
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionDiyGiftList(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	diyGifts, pg, err := diygifts.ListDiyGiftsByPage(p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := diyGiftListResp{
		Pagination: pg,
	}
	if len(diyGifts) == 0 {
		resp.Data = make([]diyGiftData, 0)
		return resp, nil
	}
	giftIDs := make([]int64, 0, len(diyGifts))
	for _, diyGift := range diyGifts {
		giftIDs = append(giftIDs, diyGift.GiftID)
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	diyGiftsEffectCountMap, err := diygifteffects.GetDiyGiftsEffectCountMap(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	diyGiftsDressCountMap, err := diygiftdresses.GetDiyGiftsDressCountMap(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Data = make([]diyGiftData, 0, len(diyGifts))
	for _, v := range diyGifts {
		resp.Data = append(resp.Data, diyGiftData{
			GiftID:              v.GiftID,
			GiftType:            buildGiftTypeName(giftMap[v.GiftID]),
			Words:               formatDiyGiftBoolToString(v.Words != nil),
			Avatars:             formatDiyGiftBoolToString(v.Avatars != nil),
			DressCount:          len(v.DressTypes),
			EffectCount:         diyGiftEffectCount(&v, diyGiftsDressCountMap),
			UploadedEffectCount: diyGiftsEffectCountMap[v.GiftID],
			Status:              formatDiyGiftStatus(v.Status),
			CreateTime:          v.CreateTime,
			ModifiedTime:        v.ModifiedTime,
		})
	}
	return resp, nil
}

// diyGiftEffectCount 计算应有特效数量
func diyGiftEffectCount(diyGift *diygifts.DiyGift, diyGiftsDressCountMap map[int64]map[int]int) int {
	if len(diyGift.DressTypes) == 0 {
		return 0
	}
	dressTypeMap, ok := diyGiftsDressCountMap[diyGift.GiftID]
	if !ok {
		return 0
	}
	count := 1
	for _, dressType := range diyGift.DressTypes {
		// DIY 礼物特效是不同配置项下的不同装扮配置组合而成，应有特效数量为每个配置项下的装扮配置数相乘
		count *= dressTypeMap[dressType.Type]
	}
	return count
}

func formatDiyGiftStatus(status int) string {
	if status == diygifts.StatusOn {
		return "生效中"
	}
	return "未生效"
}

func formatDiyGiftBoolToString(b bool) string {
	if b {
		return "有"
	}
	return "无"
}

func buildGiftTypeName(g *gift.Gift) string {
	if g == nil {
		return "礼物不存在"
	}
	switch g.Type {
	case gift.TypeNoble:
		if g.VipType == gift.VipTypeLiveHighness {
			return "贵族礼物 - 上神"
		}
		return "贵族礼物"
	case gift.TypeCustom:
		if g.NobleLevel == 0 {
			return "定制礼物"
		}
		if g.VipType == gift.VipTypeLiveHighness {
			return "贵族定制礼物 - 上神"
		}
		return "贵族定制礼物"
	case gift.TypeMedal:
		return fmt.Sprintf("粉丝礼物 - %d 级", g.MedalLevel)
	case gift.TypeSuperFan:
		return "超粉礼物"
	case gift.TypeNormal:
		return "普通礼物"
	default:
		return "未知礼物"
	}
}
