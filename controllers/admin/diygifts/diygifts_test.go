package diygifts

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestActionDiyGiftList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/diygifts/list", true, nil)
	resp, err := ActionDiyGiftList(c)
	require.NoError(err)
	r := resp.(diyGiftListResp)
	require.NotEmpty(r.Data)
	assert.EqualValues(2, r.Data[0].GiftID)
	g, err := gift.FindByGiftID(r.Data[0].GiftID)
	require.NoError(err)
	require.NotNil(g)
	assert.EqualValues(buildGiftTypeName(g), r.Data[0].GiftType)
	assert.Equal("有", r.Data[0].Words)
	assert.Equal("有", r.Data[0].Avatars)
	assert.Equal(2, r.Data[0].DressCount)
	assert.Equal(2, r.Data[0].EffectCount)
	assert.Equal(2, r.Data[0].UploadedEffectCount)
	require.NotEmpty(r.Pagination)
	assert.EqualValues(5, r.Pagination.Count)
	assert.EqualValues(1, r.Pagination.MaxPage)
}

func TestDiyGiftEffectCount(t *testing.T) {
	assert := assert.New(t)

	testGiftID := int64(2)
	diyGiftsDressCountMap := map[int64]map[int]int{
		testGiftID: {
			1: 0,
			2: 2,
		},
	}
	diyGift := &diygifts.DiyGift{
		GiftID: 1,
	}
	assert.Zero(diyGiftEffectCount(diyGift, diyGiftsDressCountMap))

	diyGift.GiftID = testGiftID
	assert.Zero(diyGiftEffectCount(diyGift, diyGiftsDressCountMap))

	diyGift.DressTypes = []diygifts.DressType{
		{Type: 1},
		{Type: 2},
	}
	assert.Zero(diyGiftEffectCount(diyGift, diyGiftsDressCountMap))

	diyGiftsDressCountMap[testGiftID][1] = 2
	assert.Equal(4, diyGiftEffectCount(diyGift, diyGiftsDressCountMap))
}

func TestBuildGiftTypeName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("礼物不存在", buildGiftTypeName(nil))

	g := &gift.Gift{
		Type: gift.TypeCustom,
	}
	assert.Equal("定制礼物", buildGiftTypeName(g))

	g.NobleLevel = 1
	assert.Equal("贵族定制礼物", buildGiftTypeName(g))

	g.VipType = gift.VipTypeLiveHighness
	assert.Equal("贵族定制礼物 - 上神", buildGiftTypeName(g))
	g.Type = gift.TypeMedal
	g.MedalLevel = 7
	assert.Equal("粉丝礼物 - 7 级", buildGiftTypeName(g))

	g.Type = gift.TypeNoble
	assert.Equal("贵族礼物 - 上神", buildGiftTypeName(g))

	g.VipType = gift.VipTypeLiveNoble
	assert.Equal("贵族礼物", buildGiftTypeName(g))

	g.Type = gift.TypeSuperFan
	assert.Equal("超粉礼物", buildGiftTypeName(g))

	g.Type = gift.TypeNormal
	assert.Equal("普通礼物", buildGiftTypeName(g))

	g.Type = gift.TypeHide
	assert.Equal("未知礼物", buildGiftTypeName(g))
}
