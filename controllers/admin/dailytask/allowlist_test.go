package dailytask

import (
	"encoding/csv"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAllowlistImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementDailyTaskAllowList).Error
	require.NoError(err)

	nowUnix := goutil.TimeNow()
	testCSVMsg := [][]string{
		{"主播 ID", "开始时间", "结束时间"},
		{"9074509", nowUnix.Format(goutil.TimeFormatHMS), nowUnix.Add(time.Minute).Format(goutil.TimeFormatHMS)},
	}
	testFilePath := "../../../testdata/allowlsit.csv"
	cleanup, err := createTestCSV(testFilePath, testCSVMsg)
	require.NoError(err)
	defer cleanup()

	body := allowlistImportParam{
		CSVURL: "https://fm.example.com/testdata/allowlsit.csv",
	}
	c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/import", true, body)
	_, _, err = ActionAllowlistImport(c)
	assert.Equal(actionerrors.ErrConfirmRequired("确认新增 1 条配置吗？", 1, true), err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/import", true, body)
	_, msg, err := ActionAllowlistImport(c)
	require.NoError(err)
	assert.Equal("导入成功", msg)
}

func TestNewAllowlistImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/import", true, nil)
	_, err := newAllowlistImport(c)
	assert.Equal(actionerrors.ErrParams, err)

	testCSVMsg := [][]string{
		{"主播 ID", "开始时间", "结束时间"},
		{"9074501", "2025-03-05 12:00:00", "2025-03-06 12:00:00"},
	}
	testFilePath := "../../../testdata/allowlsit.csv"
	cleanup, err := createTestCSV(testFilePath, testCSVMsg)
	require.NoError(err)
	defer cleanup()

	body := allowlistImportParam{
		CSVURL: "https://fm.example.com/testdata/allowlsit.csv",
	}
	c = handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/import", true, body)
	param, err := newAllowlistImport(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(1, len(param.imports))
}

func TestAllowlistImportParam_parseCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := allowlistImportParam{
		CSVURL: "https://fm.example.com/testdata/allowlsit.csv",
	}
	err := param.parseCSV()
	assert.EqualError(err, "CSV 不可用，请检查后再试")

	testCSVMsg := [][]string{
		{"主播 ID", "开始时间", "结束时间"},
	}
	testFilePath := "../../../testdata/allowlsit.csv"
	cleanup, err := createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "CSV 不可为空")

	testCSVMsg = [][]string{
		{"主播 ID", "开始时间", "结束时间"},
		{"9074501", "2025-03-05", "2025-03-05"},
	}
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "主播 9074501 开始时间格式错误")

	testCSVMsg[1][1] = "2025-03-05 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "主播 9074501 结束时间格式错误")

	testCSVMsg[1][2] = "2025-03-05 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "主播 9074501 结束时间不能小于开始时间")

	testCSVMsg[1][2] = "2025-03-06 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "主播 9074501 开始时间结束不能小于当前时间")

	nowUnix := goutil.TimeNow()
	testCSVMsg[1] = []string{"9074501", nowUnix.Format(goutil.TimeFormatHMS), nowUnix.Add(time.Minute).Format(goutil.TimeFormatHMS)}
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "有不存在的主播")

	testCSVMsg[1][0] = "9074509"
	cleanup, err = createTestCSV(testFilePath, testCSVMsg)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	require.NoError(err)
	assert.Equal(1, len(param.imports))
}

func createTestCSV(filePath string, data [][]string) (func(), error) {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			return func() {}, err
		}
	}

	cleanupFunc := func() {
		_, err := os.Stat(filePath)
		if err == nil {
			err := os.Remove(filePath)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}

	// 创建 CSV 文件
	file, err := os.Create(filePath)
	if err != nil {
		return cleanupFunc, err
	}
	defer file.Close()

	// 写入 CSV 数据
	writer := csv.NewWriter(file)
	defer writer.Flush()
	if err := writer.WriteAll(data); err != nil {
		return cleanupFunc, err
	}
	return cleanupFunc, nil
}

func TestAllowlistImportParam_insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementDailyTaskAllowList).Error
	require.NoError(err)

	now := goutil.TimeNow()
	param := allowlistImportParam{
		imports: []*allowlistCSVItem{
			{
				CreatorID: 9074501,
				startTime: now.Unix(),
				endTime:   now.Add(time.Hour).Unix(),
			},
			{
				CreatorID: 9074502,
				startTime: now.Unix(),
				endTime:   now.Add(time.Hour).Unix(),
			},
		},
	}
	require.NoError(param.insert())
	recommendTags, err := liverecommendedelements.ListDailyTaskAllowList()
	require.NoError(err)
	assert.Equal(2, len(recommendTags))
}
