package dailytask

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type allowlistDeleteParam struct {
	ID      int64 `form:"id" json:"id"`
	Confirm int   `form:"confirm" json:"confirm"`
}

// ActionAllowlistDelete 日常任务主播白名单删除配置
/**
 * @api {post} /api/v2/admin/daily-task/allowlist/delete 日常任务主播白名单删除配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id 记录 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认删除主播 六月 (主播 ID: 123456) 开始时间为 2024-12-29 12:00:00 结束时间为 2024-12-30 12:00:00 的配置吗？"
 *     }
 *   }
 */
func ActionAllowlistDelete(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAllowlistDelete(c)
	if err != nil {
		return nil, "", err
	}
	if err = param.delete(); err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "删除成功", nil
}

func newAllowlistDelete(c *handler.Context) (*allowlistDeleteParam, error) {
	var param allowlistDeleteParam
	if err := c.Bind(&param); err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.ID <= 0 {
		return nil, actionerrors.ErrParams
	}
	element, err := liverecommendedelements.FindElement(param.ID, liverecommendedelements.ElementDailyTaskAllowList)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, actionerrors.ErrNotFound("配置不存在")
	}

	// 判断配置是否过期
	if element.ExpireTime <= goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("删除失败，配置已经过期")
	}

	user, err := liveuser.Find(element.ElementID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认删除主播 %s (主播 ID: %d) 开始时间为 %s 结束时间为 %s 的配置吗？",
			user.Username, element.ElementID,
			time.Unix(*element.StartTime, 0).Format(goutil.TimeFormatHMS),
			time.Unix(element.ExpireTime, 0).Format(goutil.TimeFormatHMS))
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	return &param, nil
}

func (param *allowlistDeleteParam) delete() error {
	element, err := liverecommendedelements.FindOneAndDelete(param.ID, liverecommendedelements.ElementDailyTaskAllowList)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return actionerrors.NewErrForbidden("该数据已删除")
	}
	return nil
}

func (param *allowlistDeleteParam) addAdminLog(c goutil.UserContext) {
	msg := fmt.Sprintf("日常任务主播白名单删除记录 ID 为 %d 的数据", param.ID)
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageDailyTask, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
