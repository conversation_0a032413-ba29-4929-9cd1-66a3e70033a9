package dailytask

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAllowlistDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("无参校验", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, nil)
		_, _, err := ActionAllowlistDelete(c)
		assert.Equal(actionerrors.ErrParams, err)
	})
	t.Run("无效记录 ID", func(t *testing.T) {
		body := allowlistDeleteParam{
			ID: -1,
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, _, err := ActionAllowlistDelete(c)
		assert.Equal(actionerrors.ErrParams, err)
	})
	t.Run("不存在的记录 ID", func(t *testing.T) {
		body := allowlistDeleteParam{
			ID: int64(9999999),
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, _, err := ActionAllowlistDelete(c)
		assert.Equal(actionerrors.ErrNotFound("配置不存在"), err)
	})
	t.Run("配置已经过期", func(t *testing.T) {
		record := new(liverecommendedelements.Model)
		err := service.DB.Table(liverecommendedelements.TableName()).
			Find(&record, "element_id = ? AND element_type = ? AND sort = ? AND expire_time < ? ", 9074509, liverecommendedelements.ElementDailyTaskAllowList, 1, goutil.TimeNow().Unix()).Error
		require.NoError(err)
		require.NotNil(record)
		ID := record.ID

		body := allowlistDeleteParam{
			ID: ID,
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, _, err = ActionAllowlistDelete(c)
		assert.Equal(actionerrors.ErrParamsMsg("删除失败，配置已经过期"), err)
	})
	t.Run("成功删除", func(t *testing.T) {
		record := new(liverecommendedelements.Model)
		err := service.DB.Table(liverecommendedelements.TableName()).
			Find(&record, "element_id = ? AND element_type = ? AND sort = ? AND expire_time > ? ", 9074509, liverecommendedelements.ElementDailyTaskAllowList, 1, goutil.TimeNow().Unix()).Error
		require.NoError(err)
		require.NotNil(record)
		ID := record.ID

		// 首次请求确认
		body := allowlistDeleteParam{
			ID: ID,
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, err = newAllowlistDelete(c)
		assert.Equal(actionerrors.ErrConfirmRequired("确认删除主播  (主播 ID: 9074509) 开始时间为 1970-01-01 08:00:00 结束时间为 2037-04-16 09:06:29 的配置吗？", 1, true), err)

		// 确认删除
		body.Confirm = 1
		c = handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, msg, err := ActionAllowlistDelete(c)
		require.NoError(err)
		assert.Equal("删除成功", msg)

		// 验证数据库
		var count int64
		err = service.DB.Table(liverecommendedelements.TableName()).
			Where("id = ?", ID).
			Where("element_type = ?", liverecommendedelements.ElementDailyTaskAllowList).
			Where("sort = ?", 1).
			Count(&count).Error
		require.NoError(err)
		assert.Equal(int64(0), count)
	})
}

func TestNewAllowlistDelete(t *testing.T) {
	assert := assert.New(t)

	t.Run("无参校验", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, nil)
		_, err := newAllowlistDelete(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("无效记录 ID", func(t *testing.T) {
		body := allowlistDeleteParam{
			ID: -1,
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, err := newAllowlistDelete(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	t.Run("不存在的记录 ID", func(t *testing.T) {
		body := allowlistDeleteParam{
			ID: int64(9999999),
		}
		c := handler.NewTestContext(http.MethodPost, "/dailytask/allowlist/delete", true, body)
		_, err := newAllowlistDelete(c)
		assert.Equal(actionerrors.ErrNotFound("配置不存在"), err)
	})
}

func TestAllowlistDeleteParam_delete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := new(liverecommendedelements.Model)
	err := service.DB.Table(liverecommendedelements.TableName()).
		Find(&record, "element_id = ? AND element_type = ? AND sort = ? ", 9074509, liverecommendedelements.ElementDailyTaskAllowList, 1).Error
	require.NoError(err)
	require.NotNil(record)

	param := allowlistDeleteParam{
		ID:      record.ID,
		Confirm: 1,
	}
	// 确认删除
	err = param.delete()
	require.NoError(err)

	// 验证数据库
	var count int64
	err = service.DB.Table(liverecommendedelements.TableName()).
		Where("element_id = ?", 9074509).
		Where("element_type = ?", liverecommendedelements.ElementDailyTaskAllowList).
		Where("sort = ?", 1).
		Where("expire_time > ? ", goutil.TimeNow()).
		Count(&count).Error
	require.NoError(err)
	assert.Equal(int64(0), count)
}
