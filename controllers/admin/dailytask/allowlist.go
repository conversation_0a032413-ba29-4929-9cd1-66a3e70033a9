package dailytask

import (
	"fmt"
	"strings"
	"time"

	"github.com/gocarina/gocsv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type allowlistImportParam struct {
	CSVURL  upload.SourceURL `form:"csv_url" json:"csv_url"`
	Confirm int              `form:"confirm" json:"confirm"` // 确认次数, 首次请求传 0

	imports []*allowlistCSVItem
}

type allowlistCSVItem struct {
	CreatorID    int64  `csv:"主播 ID"`
	StartTimeStr string `csv:"开始时间"` // 格式: 2024-12-30 12:00:00
	EndTimeStr   string `csv:"结束时间"` // 格式: 2024-12-30 12:00:00

	startTime int64 // 单位：秒级时间戳
	endTime   int64 // 单位：秒级时间戳
}

// ActionAllowlistImport 日常任务主播白名单导入
/**
 * @api {post} /api/v2/admin/daily-task/allowlist/import 日常任务主播白名单导入
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} csv_url csv 地址
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "导入成功"
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认新增 2 条配置吗？"
 *     }
 *   }
 */
func ActionAllowlistImport(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAllowlistImport(c)
	if err != nil {
		return nil, "", err
	}
	if err = param.insert(); err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "导入成功", nil
}

func newAllowlistImport(c *handler.Context) (*allowlistImportParam, error) {
	var param allowlistImportParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.CSVURL == "" {
		return nil, actionerrors.ErrParams
	}
	if err = param.parseCSV(); err != nil {
		return nil, err
	}

	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认新增 %d 条配置吗？", len(param.imports))
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	return &param, nil
}

func (param *allowlistImportParam) parseCSV() error {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	skipBOMFile, err := csv.SkipBOM(file)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if err := gocsv.Unmarshal(skipBOMFile, &param.imports); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.imports) == 0 {
		return actionerrors.ErrParamsMsg("CSV 不可为空")
	}

	creatorIDs := make([]int64, 0, len(param.imports))
	for _, v := range param.imports {
		prefixMsg := fmt.Sprintf("主播 %d ", v.CreatorID)
		startTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.StartTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "开始时间格式错误")
		}
		v.startTime = startTime.Unix()
		endTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.EndTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间格式错误")
		}
		v.endTime = endTime.Unix()

		if v.startTime >= v.endTime {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间不能小于开始时间")
		}
		now := goutil.TimeNow().Unix()
		if v.startTime <= now || v.endTime < now {
			return actionerrors.ErrParamsMsg(prefixMsg + "开始时间结束不能小于当前时间")
		}
		creatorIDs = append(creatorIDs, v.CreatorID)
	}

	uniqCreatorIDs := sets.Uniq(creatorIDs)
	lives, err := live.FindLives(uniqCreatorIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(lives) != len(uniqCreatorIDs) {
		return actionerrors.ErrParamsMsg("有不存在的主播")
	}
	return nil
}

func (param allowlistImportParam) insert() error {
	nowUnix := goutil.TimeNow().Unix()
	elements := make([]*liverecommendedelements.LiveRecommendedElements, 0, len(param.imports))
	for _, v := range param.imports {
		elements = append(elements, &liverecommendedelements.LiveRecommendedElements{
			Sort:         1,
			ElementID:    v.CreatorID,
			ElementType:  liverecommendedelements.ElementDailyTaskAllowList,
			StartTime:    goutil.NewInt64(v.startTime),
			ExpireTime:   v.endTime,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
		})
	}
	err := servicedb.SplitBatchInsert(service.DB, liverecommendedelements.LiveRecommendedElements{}.TableName(), elements, 1000, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *allowlistImportParam) addAdminLog(c goutil.UserContext) {
	msg := fmt.Sprintf("日常任务主播白名单导入 %d 条数据", len(param.imports))
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageDailyTask, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
