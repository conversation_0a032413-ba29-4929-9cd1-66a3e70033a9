package exclusive

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestExclusiveCreatorAddParamsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	keys := []string{"creator_id", "guild_id", "contract_end", "confirm"}
	kc.Check(exclusiveCreatorAddParams{}, keys...)

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(exclusiveCreatorAddParams{}, keys...)
}

func TestActionExclusiveCreatorAdd(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(interface{}) (interface{}, error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	tm := util.BeginningOfDay(goutil.TimeNow())
	body := exclusiveCreatorAddParams{
		CreatorID:   107,
		GuildID:     3,
		ContractEnd: tm.AddDate(3, 0, 0).Unix(),
		Confirm:     3,
	}

	// 测试添加成功
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/exclusive/creator/add", true, body)
	resp, err := ActionExclusiveCreatorAdd(c)
	assert.Equal("添加成功", resp)
	assert.Nil(err)
}

func TestNewExclusiveCreatorAddParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(now)
	param := &exclusiveCreatorAddParams{
		CreatorID:   3,
		GuildID:     10000,
		ContractEnd: now.Unix(),
		Confirm:     0,
	}

	// 测试身份到期时间必须大于当前时间
	_, err := newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("身份到期时间必须大于当前时间"), err)

	// 测试公会不存在
	param.ContractEnd = tm.AddDate(3, 0, 0).Unix()
	_, err = newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrGuildNotExist, err)

	// 测试用户不存在
	param.GuildID = 3
	param.CreatorID = 10000
	_, err = newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrUserNotFound, err)

	// 测试主播没有加入公会
	param.CreatorID = 1
	_, err = newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrNotFound("该主播未加入该公会，无法成为三方独家主播"), err)

	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 21).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              21,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       107,
		GuildContractID: 23333475,
		ContractEnd:     now.AddDate(3, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	// 测试该主播已经是三方独家主播
	param.CreatorID = 107
	_, err = newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrExclusiveCreatorConflicted("该主播已经是三方独家主播"), err)

	// 测试正常
	param.CreatorID = 108
	p, err := newExclusiveCreatorAddParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.NoError(err)
	assert.Equal(param.CreatorID, p.CreatorID)
	assert.Equal(param.GuildID, p.GuildID)
	assert.Equal(param.ContractEnd, p.ContractEnd)
	assert.Equal(param.Confirm, p.Confirm)
}

func TestCheckConfirm(t *testing.T) {
	assert := assert.New(t)

	tm := util.BeginningOfDay(goutil.TimeNow())
	param := &exclusiveCreatorAddParams{
		CreatorID:   108,
		GuildID:     3,
		ContractEnd: tm.AddDate(3, 0, 0).Unix(),
		Confirm:     0,
		userInfo: &mowangskuser.Simple{
			Username: "测试&用户",
		},
		guildInfo: &guild.Guild{
			Name: "测试公会&名称",
		},
		contract: &livecontract.LiveContract{
			ContractEnd: tm.AddDate(3, 0, 0).Unix(),
		},
	}

	// 测试 confirm = 0
	err := param.checkConfirm()
	info := fmt.Sprintf("<p><b>添加三方独家主播</b></p><p>确认将该主播设置为三方独家主播吗？</p>"+
		"<p>主播昵称：测试&amp;用户</p><p>主播 M 号：108</p>"+
		"<p>公会名称：测试公会&amp;名称</p><p>公会 ID：3</p><p>身份到期时间：%s</p>",
		time.Unix(param.ContractEnd, 0).Format(util.TimeFormatYMDHMS))
	assert.Equal(actionerrors.ErrConfirmRequired(info, goutil.BitMask(1), true), err)

	// 测试 confirm 第 1 位为 1 并且原合约到期时间小于三方独家主播合约到期时间
	param.Confirm.Set(exclusiveAddConfirmInfo)
	param.contract.ContractEnd = tm.AddDate(2, 0, 0).Unix()
	err = param.checkConfirm()
	info = fmt.Sprintf("<p><b>修改合约到期时间确认</b></p>"+
		"<p>您设置的三方独家主播身份到期时间晚于合约到期时间，将延长合约到期时间至 %s</p>"+
		"<p>公会原合约到期时间为 %s</p>",
		time.Unix(param.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.contract.ContractEnd, 0).Format(util.TimeFormatYMDHMS))
	assert.Equal(actionerrors.ErrConfirmRequired(info, goutil.BitMask(3), true), err)

	// 测试 confirm 第 2 位为 1
	param.Confirm.Set(exclusiveAddConfirmContractEnd)
	assert.Nil(param.checkConfirm())
}

func TestAdd(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(now)

	// 生成测试合约数据
	require.NoError(livecontract.LiveContract{}.DB().Delete("", "id = ?", 1000).Error)
	contract := livecontract.LiveContract{
		ID:               1000,
		GuildID:          101,
		LiveID:           105,
		GuildOwner:       11,
		Status:           livecontract.StatusContracting,
		ContractStart:    goutil.TimeNow().Unix(),
		ContractDuration: 2,
		ContractEnd:      tm.AddDate(2, 0, 0).Unix(),
	}
	require.NoError(livecontract.LiveContract{}.DB().Save(&contract).Error)

	// 生成未处理的续约申请数据
	require.NoError(contractapplyment.ContractApplyment{}.DB().Delete(nil, "id = ?", 10000).Error)
	application := contractapplyment.ContractApplyment{
		ID:                 10000,
		GuildID:            contract.GuildID,
		LiveID:             contract.LiveID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               45,
		Type:               contractapplyment.TypeGuildRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.Add(5 * time.Minute).Unix(),
	}
	require.NoError(application.DB().Save(&application).Error)

	param := &exclusiveCreatorAddParams{
		CreatorID:   contract.LiveID,
		GuildID:     contract.GuildID,
		ContractEnd: tm.AddDate(3, 0, 0).Unix(),
		Confirm:     2,
		contract:    &contract,
	}

	require.NoError(param.add())

	// 验证合约到期时间是否更改
	lc := livecontract.LiveContract{}
	require.NoError(livecontract.LiveContract{}.DB().First(&lc, contract.ID).Error)
	assert.Equal(param.ContractEnd, lc.ContractEnd)

	// 验证未处理的续约申请是否失效
	application1 := contractapplyment.ContractApplyment{}
	require.NoError(application1.DB().First(&application1, application.ID).Error)
	assert.Equal(contractapplyment.StatusInvalid, application1.Status)

	// 验证三方独家主播记录是否生成
	exclusive := exclusivecreator.TripartiteExclusiveCreator{}
	require.NoError(exclusive.DB().
		Where("guild_id = ? AND creator_id = ? AND status = ?", contract.GuildID, contract.LiveID, exclusivecreator.StatusValid).
		First(&exclusive).Error)
	assert.Equal(param.ContractEnd, exclusive.ContractEnd)
}
