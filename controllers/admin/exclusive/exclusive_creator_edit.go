package exclusive

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type exclusiveCreatorEditElem struct {
	ID int64 `form:"id" json:"id"`
}

type exclusiveCreatorEditParams struct {
	Data        []exclusiveCreatorEditElem `form:"data" json:"data" binding:"required"`
	ContractEnd int64                      `form:"contract_end" json:"contract_end" binding:"required"`
	Confirm     int                        `form:"confirm" json:"confirm"`

	guildInfo        *guild.Guild
	userInfo         *mowangskuser.Simple
	contract         *livecontract.LiveContract
	exclusiveCreator *exclusivecreator.TripartiteExclusiveCreator
	context          *handler.Context
}

// ActionExclusiveCreatorEdit 超管修改三方独家主播
/**
 * @api {post} /api/v2/admin/exclusive/creator/edit 超管修改三方独家主播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/exclusive
 *
 * @apiParam {Object[]} data
 * @apiParam {Number} data.id 三方独家主播记录 ID
 * @apiParam {Number} contract_end 主播身份到期时间
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiParamExample {json} Request-Example:
 *   {
 *     "data": [{
 *       "id": 12
 *     }],
 *     "contract_end": 1647475200
 *   }
 * @apiSuccessExample 修改成功:
 *   {
 *     "code": 0,
 *     "info": "修改成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": ""
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionExclusiveCreatorEdit(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newExclusiveCreatorEditParams(c)
	if err != nil {
		return nil, err
	}

	err = params.checkConfirm()
	if err != nil {
		return nil, err
	}

	err = params.edit()
	if err != nil {
		return nil, err
	}

	params.sendSystemMsg()

	params.addAdminLog()

	return "修改成功", nil
}

func newExclusiveCreatorEditParams(c *handler.Context) (*exclusiveCreatorEditParams, error) {
	p := new(exclusiveCreatorEditParams)
	err := c.Bind(p)
	if err != nil || len(p.Data) == 0 {
		return nil, actionerrors.ErrParams
	}
	if p.ContractEnd <= goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("身份到期时间必须大于当前时间")
	}
	p.context = c

	if len(p.Data) != 1 {
		return nil, actionerrors.ErrParamsMsg("暂不支持批量修改")
	}

	// 根据 ID 获取三方独家主播记录
	p.exclusiveCreator, err = exclusivecreator.FindByID(p.Data[0].ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.exclusiveCreator == nil {
		return nil, actionerrors.ErrNotFindExclusiveCreator
	}

	p.guildInfo, err = guild.Find(p.exclusiveCreator.GuildID, guild.CheckedPass)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.guildInfo == nil {
		return nil, actionerrors.ErrGuildNotExist
	}

	p.userInfo, err = mowangskuser.FindByUserID(p.exclusiveCreator.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.userInfo == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	// 主播和公会签约中的合同
	p.contract, err = livecontract.FindInContractingByLiveID(p.exclusiveCreator.CreatorID, p.exclusiveCreator.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.contract == nil {
		return nil, actionerrors.ErrParamsMsg("主播和公会未签约")
	}

	return p, nil
}

func (p *exclusiveCreatorEditParams) checkConfirm() error {
	if p.Confirm != 0 {
		return nil
	}

	var info string
	if p.contract.ContractEnd < p.ContractEnd {
		info = fmt.Sprintf("<p><b>修改身份到期时间确认</b></p>"+
			"<p>您设置的三方独家主播身份到期时间晚于合约到期时间，将延长合约到期时间至 %s</p>"+
			"<p>公会原合约到期时间为 %s</p>",
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
			time.Unix(p.contract.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		)
	} else {
		info = fmt.Sprintf("<p><b>修改身份到期时间确认</b></p>"+
			"<p>确认将三方独家主播身份到期时间从 %s 改为 %s 吗？</p>",
			time.Unix(p.exclusiveCreator.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		)
	}

	return actionerrors.ErrConfirmRequired(info, 1, true)
}

func (p *exclusiveCreatorEditParams) edit() error {
	now := goutil.TimeNow()
	nowStamp := now.Unix()

	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 若原合约到期时间小于三方独家主播身份到期时间，则延长原合约到期时间并失效未处理的续约申请
		if p.contract.ContractEnd < p.ContractEnd {
			db := tx.Table(livecontract.TableName()).
				Where("id = ?", p.contract.ID).
				Update(map[string]interface{}{
					"contract_end":  p.ContractEnd,
					"modified_time": nowStamp,
				})
			if err := db.Error; err != nil {
				return err
			}
			if db.RowsAffected == 0 {
				return servicedb.ErrNoRowsAffected
			}

			// 失效未处理的续约申请
			types := []int64{
				contractapplyment.TypeLiveRenew,
				contractapplyment.TypeGuildRenew,
			}
			err := tx.Table(contractapplyment.TableName()).
				Where("guild_id = ? AND live_id = ? AND status = ? AND type IN (?) AND expire_time > ?",
					p.exclusiveCreator.GuildID, p.exclusiveCreator.CreatorID, contractapplyment.StatusPending, types, nowStamp).
				Update(map[string]interface{}{
					"status":        contractapplyment.StatusInvalid,
					"process_time":  nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
		}

		// 更新三方独家主播记录
		err := exclusivecreator.TripartiteExclusiveCreator{}.DB().
			Where("id = ?", p.Data[0].ID).
			Update(map[string]interface{}{
				"contract_end":  p.ContractEnd,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		if err == servicedb.ErrNoRowsAffected {
			return actionerrors.ErrContractNotExist
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (p *exclusiveCreatorEditParams) sendSystemMsg() {
	userIDs := make([]int64, 0, 2)
	userIDs = append(userIDs, p.guildInfo.UserID)
	// 获取主播的经纪人
	agentID, err := guildagent.AgentID(p.exclusiveCreator.CreatorID, p.exclusiveCreator.GuildID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 若公会长也是该主播的经纪人，发送一次系统通知即可
	if agentID > 0 && agentID != p.guildInfo.UserID {
		userIDs = append(userIDs, agentID)
	}
	// 经纪人和公会长系统通知内容
	sysMsgs := make([]pushservice.SystemMsg, 0, len(userIDs)+1)
	for _, userID := range userIDs {
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: userID,
			Title:  fmt.Sprintf("主播%s的三方独家主播身份到期时间被变更", p.userInfo.Username),
			Content: fmt.Sprintf("主播%s的三方独家主播身份到期时间变更为 %s，请知悉。",
				p.userInfo.Username,
				time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS)),
		})
	}
	// 主播本人系统通知内容
	sysMsgs = append(sysMsgs, pushservice.SystemMsg{
		UserID: p.exclusiveCreator.CreatorID,
		Title:  "您的三方独家主播身份到期时间被变更",
		Content: fmt.Sprintf("您的三方独家主播身份到期时间变更为 %s，请知悉。",
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS)),
	})
	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *exclusiveCreatorEditParams) addAdminLog() {
	intro := fmt.Sprintf("修改三方独家主播%s（用户 ID：%d）的身份到期时间为 %s，所属公会%s（公会 ID：%d）",
		p.userInfo.Username,
		p.userInfo.ID,
		time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		p.guildInfo.Name,
		p.guildInfo.ID)
	box := goclient.NewAdminLogBox(p.context)
	box.Add(userapi.CatalogManageExclusiveCreator, intro, goclient.AdminLogOptions{
		ChannelID: &p.exclusiveCreator.CreatorID,
	})
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
