package exclusive

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestExclusiveCreatorDeleteParamsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(exclusiveCreatorDeleteParams{}, "data")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(exclusiveCreatorDeleteParams{}, "data")
}

func TestExclusiveCreatorDeleteElem(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(exclusiveCreatorDeleteElem{}, "id")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(exclusiveCreatorDeleteElem{}, "id")
}

func TestActionExclusiveCreatorDelete(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(now)
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 11).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              11,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       108,
		GuildContractID: 23333476,
		ContractEnd:     tm.AddDate(2, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(interface{}) (interface{}, error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	// 测试移除成功
	body := exclusiveCreatorDeleteParams{Data: []exclusiveCreatorDeleteElem{{ID: 11}}}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/exclusive/creator/delete", true, body)
	resp, _ := ActionExclusiveCreatorDelete(c)
	assert.Equal("移除成功", resp)
}

func TestNewExclusiveCreatorDeleteParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 12).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              12,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       108,
		GuildContractID: 23333476,
		ContractEnd:     now.AddDate(3, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	// 测试 len(param.Data) 为 0 参数错误
	param := new(exclusiveCreatorDeleteParams)
	_, err := newExclusiveCreatorDeleteParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParams, err)

	param.Data = []exclusiveCreatorDeleteElem{{ID: 12}, {ID: 13}}
	// 测试不支持批量删除
	_, err = newExclusiveCreatorDeleteParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("暂不支持批量移除"), err)

	// 测试三方独家主播不存在
	param.Data = []exclusiveCreatorDeleteElem{{ID: 100}}
	_, err = newExclusiveCreatorDeleteParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrNotFindExclusiveCreator, err)

	// 测试正常
	param.Data = []exclusiveCreatorDeleteElem{{ID: 12}}
	p, err := newExclusiveCreatorDeleteParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	require.NoError(err)
	assert.Equal(param.Data, p.Data)
}

func TestDelete(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 13).Error)
	now := goutil.TimeNow()
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              13,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       108,
		GuildContractID: 23333475,
		ContractEnd:     now.AddDate(3, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	param := &exclusiveCreatorDeleteParams{Data: []exclusiveCreatorDeleteElem{{ID: 13}}}
	require.NoError(param.delete())
	exclusive := exclusivecreator.TripartiteExclusiveCreator{}
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().First(&exclusive, 13).Error)
	assert.Equal(exclusivecreator.StatusDeleted, exclusive.Status)
}
