package exclusive

import (
	"fmt"
	"strings"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type exclusiveCreatorDeleteElem struct {
	ID int64 `form:"id" json:"id"`
}

type exclusiveCreatorDeleteParams struct {
	Data []exclusiveCreatorDeleteElem `form:"data" json:"data" binding:"required"`

	guildInfo        *guild.Guild
	userInfo         *mowangskuser.Simple
	exclusiveCreator *exclusivecreator.TripartiteExclusiveCreator
	context          *handler.Context
}

// ActionExclusiveCreatorDelete 超管移除三方独家主播
/**
 * @api {post} /api/v2/admin/exclusive/creator/delete 超管移除三方独家主播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/exclusive
 *
 * @apiParam {Object[]} data
 * @apiParam {Number} data.id 三方独家主播记录 ID
 *
 * @apiParamExample {json} Request-Example:
 *   {
 *     "data": [{
 *       "id": 12
 *     }]
 *   }
 *
 * @apiSuccessExample 移除成功:
 *   {
 *     "code": 0,
 *     "info": "移除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionExclusiveCreatorDelete(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newExclusiveCreatorDeleteParams(c)
	if err != nil {
		return nil, err
	}

	err = params.delete()
	if err != nil {
		return nil, err
	}

	params.sendSystemMsg()

	params.addAdminLog()

	return "移除成功", nil
}

func newExclusiveCreatorDeleteParams(c *handler.Context) (*exclusiveCreatorDeleteParams, error) {
	p := new(exclusiveCreatorDeleteParams)

	err := c.Bind(p)
	if err != nil || len(p.Data) == 0 {
		return nil, actionerrors.ErrParams
	}
	p.context = c

	if len(p.Data) != 1 {
		return nil, actionerrors.ErrParamsMsg("暂不支持批量移除")
	}

	// 根据 ID 获取三方独家主播记录
	p.exclusiveCreator, err = exclusivecreator.FindByID(p.Data[0].ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.exclusiveCreator == nil {
		return nil, actionerrors.ErrNotFindExclusiveCreator
	}

	return p, nil
}

func (p *exclusiveCreatorDeleteParams) delete() error {
	db := exclusivecreator.TripartiteExclusiveCreator{}.DB().
		Where("id = ? AND status = ?", p.Data[0].ID, exclusivecreator.StatusValid).
		Update(map[string]interface{}{
			"status":        exclusivecreator.StatusDeleted,
			"modified_time": goutil.TimeNow().Unix(),
		})
	if db.Error != nil {
		return actionerrors.NewErrServerInternal(db.Error, nil)
	}
	if db.RowsAffected == 0 {
		return actionerrors.ErrNotFindExclusiveCreator
	}

	return nil
}

func (p *exclusiveCreatorDeleteParams) sendSystemMsg() {
	var err error
	// 获取公会信息
	p.guildInfo, err = guild.Find(p.exclusiveCreator.GuildID, guild.CheckedPass)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if p.guildInfo == nil {
		return
	}

	// 获取用户信息
	p.userInfo, err = mowangskuser.FindByUserID(p.exclusiveCreator.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if p.userInfo == nil {
		return
	}

	userIDs := make([]int64, 0, 2)
	userIDs = append(userIDs, p.guildInfo.UserID)

	// 获取主播的经纪人
	agentID, err := guildagent.AgentID(p.exclusiveCreator.CreatorID, p.exclusiveCreator.GuildID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 若公会长也是该主播的经纪人，发送一次系统通知即可
	if agentID > 0 && agentID != p.guildInfo.UserID {
		userIDs = append(userIDs, agentID)
	}
	// 经纪人和公会长系统通知内容
	sysMsgs := make([]pushservice.SystemMsg, 0, len(userIDs)+1)
	for _, userID := range userIDs {
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: userID,
			Title:  fmt.Sprintf("主播%s的三方独家主播身份被超管取消", p.userInfo.Username),
			Content: fmt.Sprintf("主播%s的三方独家主播身份已于 %s 被超管取消，请知悉。",
				p.userInfo.Username,
				goutil.TimeNow().Format(util.TimeFormatYMDHMS)),
		})
	}
	// 主播本人系统通知内容
	sysMsgs = append(sysMsgs, pushservice.SystemMsg{
		UserID: p.exclusiveCreator.CreatorID,
		Title:  "您的三方独家主播身份被超管取消",
		Content: fmt.Sprintf("您的三方独家主播身份已于 %s 被超管取消，请知悉。",
			goutil.TimeNow().Format(util.TimeFormatYMDHMS)),
	})
	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *exclusiveCreatorDeleteParams) addAdminLog() {
	elem := []string{fmt.Sprintf("移除三方独家主播，合约 ID: %d", p.exclusiveCreator.GuildContractID)}
	if p.userInfo != nil {
		elem = append(elem, fmt.Sprintf("主播%s（用户 ID：%d）", p.userInfo.Username, p.userInfo.ID))
	}
	if p.guildInfo != nil {
		elem = append(elem, fmt.Sprintf("所属公会%s（公会 ID：%d）", p.guildInfo.Name, p.guildInfo.ID))
	}
	intro := strings.Join(elem, "，")
	box := goclient.NewAdminLogBox(p.context)
	box.Add(userapi.CatalogManageExclusiveCreator, intro, goclient.AdminLogOptions{
		ChannelID: &p.exclusiveCreator.CreatorID,
	})
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
