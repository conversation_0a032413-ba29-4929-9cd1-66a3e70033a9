package exclusive

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestExclusiveCreatorEditParamsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	keys := []string{"data", "contract_end", "confirm"}
	kc.Check(exclusiveCreatorEditParams{}, keys...)

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(exclusiveCreatorEditParams{}, keys...)
}

func TestExclusiveCreatorEditElem(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(exclusiveCreatorEditElem{}, "id")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(exclusiveCreatorEditElem{}, "id")
}

func TestActionExclusiveCreatorEdit(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(goutil.TimeNow())
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 10).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              10,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       108,
		GuildContractID: 23333475,
		ContractEnd:     tm.AddDate(2, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	body := exclusiveCreatorEditParams{
		Data:        []exclusiveCreatorEditElem{{ID: 10}},
		ContractEnd: tm.AddDate(3, 0, 0).Unix(),
		Confirm:     1,
	}

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(interface{}) (interface{}, error) {
			return handler.M{
				"count": 2,
			}, nil
		})
	defer cancel()

	// 测试修改成功
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/exclusive/creator/edit", true, body)
	resp, err := ActionExclusiveCreatorEdit(c)
	require.Nil(err)
	assert.Equal("修改成功", resp)
}

func TestNewExclusiveCreatorEditParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(goutil.TimeNow())
	contractEnd := tm.AddDate(3, 0, 0).Unix()
	// 生成测试数据
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 10).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              10,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       108,
		GuildContractID: 23333476,
		ContractEnd:     contractEnd,
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	param := exclusiveCreatorEditParams{
		ContractEnd: contractEnd,
		Confirm:     0,
	}

	// 测试 len(param.Data) 为 0 参数错误
	_, err := newExclusiveCreatorEditParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParams, err)

	// 测试不支持批量修改
	param.Data = []exclusiveCreatorEditElem{{ID: 10}, {ID: 11}}
	_, err = newExclusiveCreatorEditParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("暂不支持批量修改"), err)

	// 测试身份到期时间必须大于当前时间
	param.ContractEnd = now.Unix()
	_, err = newExclusiveCreatorEditParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrParamsMsg("身份到期时间必须大于当前时间"), err)

	// 测试三方独家主播不存在
	param.ContractEnd = tm.AddDate(3, 0, 0).Unix()
	param.Data = []exclusiveCreatorEditElem{{100}}
	_, err = newExclusiveCreatorEditParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	assert.Equal(actionerrors.ErrNotFindExclusiveCreator, err)

	// 测试正常
	param.Data[0].ID = 10
	p, err := newExclusiveCreatorEditParams(handler.NewTestContext(http.MethodPost, "/", true, param))
	require.NoError(err)
	assert.Equal(param.Data, p.Data)
	assert.Equal(param.Confirm, p.Confirm)
	assert.Equal(param.ContractEnd, p.ContractEnd)
}

func TestEditCheckConfirm(t *testing.T) {
	assert := assert.New(t)

	tm := util.BeginningOfDay(goutil.TimeNow())
	contractEnd := tm.AddDate(3, 0, 0).Unix()
	param := &exclusiveCreatorEditParams{
		ContractEnd: tm.AddDate(4, 0, 0).Unix(),
		Confirm:     0,
		contract: &livecontract.LiveContract{
			ContractEnd: contractEnd,
		},
		exclusiveCreator: &exclusivecreator.TripartiteExclusiveCreator{
			ContractEnd: contractEnd,
		},
	}

	// 测试 confirm = 0，原合约到期时间小于修改的到期时间
	err := param.checkConfirm()
	info := fmt.Sprintf("<p><b>修改身份到期时间确认</b></p>"+
		"<p>您设置的三方独家主播身份到期时间晚于合约到期时间，将延长合约到期时间至 %s</p>"+
		"<p>公会原合约到期时间为 %s</p>",
		time.Unix(param.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.contract.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
	)
	assert.Equal(actionerrors.ErrConfirmRequired(info, 1, true), err)

	// 测试 confirm = 0，原合约到期时间大于等于修改的到期时间
	param.ContractEnd = tm.AddDate(2, 0, 0).Unix()
	err = param.checkConfirm()
	info = fmt.Sprintf("<p><b>修改身份到期时间确认</b></p>"+
		"<p>确认将三方独家主播身份到期时间从 %s 改为 %s 吗？</p>",
		time.Unix(param.exclusiveCreator.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
	)
	assert.Equal(actionerrors.ErrConfirmRequired(info, 1, true), err)

	// 测试 confirm = 1
	param.Confirm = 1
	assert.Nil(param.checkConfirm())
}

func TestEdit(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	tm := util.BeginningOfDay(goutil.TimeNow())
	// 生成测试合约数据
	require.NoError(livecontract.LiveContract{}.DB().Delete("", "id = ?", 1000).Error)
	contract := livecontract.LiveContract{
		ID:               1000,
		GuildID:          101,
		LiveID:           105,
		GuildOwner:       11,
		Status:           livecontract.StatusContracting,
		ContractStart:    goutil.TimeNow().Unix(),
		ContractDuration: 2,
		ContractEnd:      tm.AddDate(2, 0, 0).Unix(),
	}
	require.NoError(livecontract.LiveContract{}.DB().Save(&contract).Error)

	// 生成未处理的续约申请数据
	require.NoError(contractapplyment.ContractApplyment{}.DB().Delete("", "id = ?", 10000).Error)
	application := contractapplyment.ContractApplyment{
		ID:                 10000,
		GuildID:            contract.GuildID,
		LiveID:             contract.LiveID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               45,
		Type:               contractapplyment.TypeGuildRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.Add(5 * time.Minute).Unix(),
	}
	require.NoError(application.DB().Save(&application).Error)

	// 生成三方独家主播数据
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 18).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              18,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         contract.GuildID,
		CreatorID:       contract.LiveID,
		GuildContractID: 23333475,
		ContractEnd:     now.AddDate(2, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	param := &exclusiveCreatorEditParams{
		Data:             []exclusiveCreatorEditElem{{ID: tec.ID}},
		ContractEnd:      tm.AddDate(3, 0, 0).Unix(),
		Confirm:          1,
		contract:         &contract,
		exclusiveCreator: &tec,
	}

	require.NoError(param.edit())

	// 验证合约到期时间是否更改
	lc := livecontract.LiveContract{}
	require.NoError(livecontract.LiveContract{}.DB().First(&lc, contract.ID).Error)
	assert.Equal(param.ContractEnd, lc.ContractEnd)

	// 验证未处理的续约申请是否失效
	application1 := contractapplyment.ContractApplyment{}
	require.NoError(application1.DB().First(&application1, application.ID).Error)
	assert.Equal(contractapplyment.StatusInvalid, application1.Status)

	// 验证三方独家主播到期时间是否更改
	exclusive := exclusivecreator.TripartiteExclusiveCreator{}
	require.NoError(exclusive.DB().First(&exclusive, tec.ID).Error)
	assert.Equal(param.ContractEnd, exclusive.ContractEnd)
}
