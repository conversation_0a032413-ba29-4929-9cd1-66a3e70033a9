package exclusive

import (
	"fmt"
	"html"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	exclusiveAddConfirmInfo        = iota + 1 // 第 1 位表示设置三方独家主播信息确认框
	exclusiveAddConfirmContractEnd            // 第 2 位表示延长合约到期时间确认框
)

type exclusiveCreatorAddParams struct {
	CreatorID   int64          `form:"creator_id" json:"creator_id" binding:"required"`
	GuildID     int64          `form:"guild_id" json:"guild_id" binding:"required"`
	ContractEnd int64          `form:"contract_end" json:"contract_end" binding:"required"`
	Confirm     goutil.BitMask `form:"confirm" json:"confirm"`

	guildInfo *guild.Guild
	userInfo  *mowangskuser.Simple
	contract  *livecontract.LiveContract
}

// ActionExclusiveCreatorAdd 超管添加三方独家主播
/**
 * @api {post} /api/v2/admin/exclusive/creator/add 超管添加三方独家主播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/exclusive
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {Number} contract_end 主播身份到期时间
 * @apiParam {number=0,1,3} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 添加成功:
 *   {
 *     "code": 0,
 *     "info": "添加成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": ""
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionExclusiveCreatorAdd(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newExclusiveCreatorAddParams(c)
	if err != nil {
		return nil, err
	}

	err = params.checkConfirm()
	if err != nil {
		return nil, err
	}

	err = params.add()
	if err != nil {
		return nil, err
	}

	params.sendSystemMsg()

	params.addAdminLog(c)

	return "添加成功", nil
}

func newExclusiveCreatorAddParams(c *handler.Context) (*exclusiveCreatorAddParams, error) {
	p := new(exclusiveCreatorAddParams)

	err := c.Bind(p)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if p.ContractEnd <= goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("身份到期时间必须大于当前时间")
	}

	p.guildInfo, err = guild.Find(p.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.guildInfo == nil || p.guildInfo.Checked != guild.CheckedPass {
		return nil, actionerrors.ErrGuildNotExist
	}

	p.userInfo, err = mowangskuser.FindByUserID(p.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.userInfo == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	// 主播和公会签约中的合同
	p.contract, err = livecontract.FindInContractingByLiveID(p.CreatorID, p.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.contract == nil {
		return nil, actionerrors.ErrNotFound("该主播未加入该公会，无法成为三方独家主播")
	}

	// 主播是否是三方独家主播
	exclusive, err := exclusivecreator.IsExclusiveCreator(p.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exclusive {
		return nil, actionerrors.ErrExclusiveCreatorConflicted("该主播已经是三方独家主播")
	}

	return p, nil
}

func (p *exclusiveCreatorAddParams) checkConfirm() error {
	if !p.Confirm.IsSet(exclusiveAddConfirmInfo) {
		info := fmt.Sprintf("<p><b>添加三方独家主播</b></p><p>确认将该主播设置为三方独家主播吗？</p>"+
			"<p>主播昵称：%s</p><p>主播 M 号：%d</p><p>公会名称：%s</p><p>公会 ID：%d</p><p>身份到期时间：%s</p>",
			html.EscapeString(p.userInfo.Username),
			p.CreatorID,
			html.EscapeString(p.guildInfo.Name),
			p.GuildID,
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS))
		p.Confirm.Set(exclusiveAddConfirmInfo)
		return actionerrors.ErrConfirmRequired(info, p.Confirm, true)
	}

	if !p.Confirm.IsSet(exclusiveAddConfirmContractEnd) && p.contract.ContractEnd < p.ContractEnd {
		info := fmt.Sprintf("<p><b>修改合约到期时间确认</b></p>"+
			"<p>您设置的三方独家主播身份到期时间晚于合约到期时间，将延长合约到期时间至 %s</p>"+
			"<p>公会原合约到期时间为 %s</p>",
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
			time.Unix(p.contract.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		)
		p.Confirm.Set(exclusiveAddConfirmContractEnd)
		return actionerrors.ErrConfirmRequired(info, p.Confirm, true)
	}

	return nil
}

func (p *exclusiveCreatorAddParams) add() error {
	now := goutil.TimeNow()
	nowStamp := now.Unix()

	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 若原合约到期时间小于三方独家主播身份到期时间，则延长原合约到期时间
		if p.contract.ContractEnd < p.ContractEnd {
			err := tx.Table(livecontract.TableName()).
				Where("id = ? ", p.contract.ID).
				Update(map[string]interface{}{
					"contract_end":  p.ContractEnd,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
		}

		// 失效未处理的续约和解约申请
		applicationTypes := []int64{
			contractapplyment.TypeLiveRenew,
			contractapplyment.TypeGuildRenew,
			contractapplyment.TypeLiveTerminate,
			contractapplyment.TypeLiveTerminateForcely,
			contractapplyment.TypeGuildExpel,
		}
		err := tx.Table(contractapplyment.TableName()).
			Where("guild_id = ? AND live_id = ? AND status = ? AND type IN (?) AND expire_time > ?",
				p.GuildID, p.CreatorID, contractapplyment.StatusPending, applicationTypes, nowStamp).
			Update(map[string]interface{}{
				"status":        contractapplyment.StatusInvalid,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return err
		}

		// 添加三方独家主播记录
		tec := exclusivecreator.TripartiteExclusiveCreator{
			CreateTime:      nowStamp,
			ModifiedTime:    nowStamp,
			GuildID:         p.GuildID,
			CreatorID:       p.CreatorID,
			GuildContractID: p.contract.ID,
			ContractEnd:     p.ContractEnd,
			Status:          exclusivecreator.StatusValid,
		}
		err = tec.DB().Save(&tec).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (p *exclusiveCreatorAddParams) sendSystemMsg() {
	// 获取主播的经纪人
	userIDs := make([]int64, 0, 2)
	userIDs = append(userIDs, p.guildInfo.UserID)
	agentID, err := guildagent.AgentID(p.CreatorID, p.GuildID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 若公会长也是该主播的经纪人，发送一次系统通知即可
	if agentID > 0 && agentID != p.guildInfo.UserID {
		userIDs = append(userIDs, agentID)
	}
	// 经纪人和公会长系统通知内容
	sysMsgs := make([]pushservice.SystemMsg, 0, len(userIDs)+1)
	for _, userID := range userIDs {
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: userID,
			Title:  fmt.Sprintf("主播%s成为猫耳三方独家主播", p.userInfo.Username),
			Content: fmt.Sprintf("主播%s已成为猫耳三方独家主播，三方独家身份到期时间为 %s，在此期间不得与主播解约。",
				p.userInfo.Username,
				time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS)),
		})
	}
	// 主播本人系统通知内容
	sysMsgs = append(sysMsgs, pushservice.SystemMsg{
		UserID: p.CreatorID,
		Title:  "您已成为猫耳的三方独家主播",
		Content: fmt.Sprintf("您已成为猫耳三方独家主播，三方独家主播身份到期时间为 %s，在此期间不得与直播公会%s解约。",
			time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
			p.guildInfo.Name),
	})
	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *exclusiveCreatorAddParams) addAdminLog(c *handler.Context) {
	intro := fmt.Sprintf("添加三方独家主播%s（用户 ID：%d），三方独家身份到期时间为 %s，所属公会%s（公会 ID：%d）",
		p.userInfo.Username,
		p.userInfo.ID,
		time.Unix(p.ContractEnd, 0).Format(util.TimeFormatYMDHMS),
		p.guildInfo.Name,
		p.guildInfo.ID)
	box := userapi.NewAdminLogBox(c)
	box.AddWithChannelID(userapi.CatalogManageExclusiveCreator, p.CreatorID, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
