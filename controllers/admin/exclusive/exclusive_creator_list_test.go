package exclusive

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestCreatorListItem(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CreatorListItem{},
		"id", "creator_id", "username", "iconurl", "guild_id", "guild_name",
		"status", "status_str", "contract_end", "contract_end", "create_time")
}

func TestActionExclusiveCreatorList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除脏数据
	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete(nil, "id > 4").Error)

	// 测试三方独家主播列表
	c := handler.NewTestContext(http.MethodGet, "/api/v2/admin/exclusive/creator/list?type=1", true, nil)
	resp, err := ActionExclusiveCreatorList(c)
	require.NoError(err)
	res := resp.(*CreatorListResp)
	assert.NotEmpty(res.Data)
	assert.Len(res.Data, 2)
}

func TestLoad(t *testing.T) {
	assert := assert.New(t)

	param := new(CreatorListParams)
	err := param.load(handler.NewTestContext(http.MethodGet, "/?type=1&creator_id=123&guild_id=1&p=1&pagesize=20", true, nil))
	assert.NoError(err)
	assert.Equal(param.lister.Status, []int{exclusivecreator.StatusValid})

	err = param.load(handler.NewTestContext(http.MethodGet, "/?type=2&creator_id=123&guild_id=1&p=1&pagesize=20", true, nil))
	assert.NoError(err)
	assert.Equal(param.lister.Status, []int{exclusivecreator.StatusExpired, exclusivecreator.StatusDeleted})

	// 测试参数错误
	err = param.load(handler.NewTestContext(http.MethodGet, "/?type=1&creator_id=-123&guild_id=1&p=1&pagesize=20", true, nil))
	assert.Equal(actionerrors.ErrParams, err)
}
