package exclusive

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CreatorListParams 请求三方独家主播列表参数
type CreatorListParams struct {
	lister exclusivecreator.TripartiteCreatorLister
	Type   int
}

// CreatorListItem 三方独家主播列表字段
type CreatorListItem struct {
	ID          int64  `json:"id"`
	CreatorID   int64  `json:"creator_id"`
	Username    string `json:"username"`
	IconURL     string `json:"iconurl"`
	GuildID     int64  `json:"guild_id"`
	GuildName   string `json:"guild_name"`
	Status      int    `json:"status"`
	StatusStr   string `json:"status_str"`
	ContractEnd int64  `json:"contract_end"`
	CreateTime  int64  `json:"create_time"`
}

// CreatorListResp 三方独家主播列表响应内容
type CreatorListResp struct {
	Data       []*CreatorListItem `json:"data"`
	Pagination goutil.Pagination  `json:"pagination"`
}

// 列表类型
const (
	exclusiveListTypeValid   = iota + 1 // 生效中
	exclusiveListTypeInvalid            // 已失效
)

// ActionExclusiveCreatorList 超管获取三方独家主播列表
/**
 * @api {get} /api/v2/admin/exclusive/creator/list 超管获取三方独家主播列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/exclusive
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 * @apiParam {Number} [creator_id] 主播 ID
 * @apiParam {Number} [guild_id] 公会 ID
 * @apiParam {number=1,2} [type=1] 获取列表类型（1: 生效中；2: 已失效）
 *
 * @apiSuccessExample:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 1,
 *           "creator_id": 1,
 *           "username": "用户名",
 *           "guild_id": 3,
 *           "guild_name": "公会名称",
 *           "status": 0,
 *           "status_str": "生效中",
 *           "contract_end": 1626418800,
 *           "create_time": 1626418800,
 *           "iconurl": "https://static-test.missevan.com/avatars/icon01.png"
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionExclusiveCreatorList(ctx *handler.Context) (handler.ActionResponse, error) {
	param := new(CreatorListParams)
	if err := param.load(ctx); err != nil {
		return nil, err
	}

	return param.exclusiveCreatorList()
}

func (p *CreatorListParams) load(c *handler.Context) error {
	var err error
	p.lister.P, p.lister.PageSize, err = c.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	p.lister.CreatorID, _ = c.GetParamInt64("creator_id")
	p.lister.GuildID, _ = c.GetParamInt64("guild_id")
	p.Type, _ = c.GetDefaultParamInt("type", exclusiveListTypeValid)
	if p.lister.CreatorID < 0 ||
		p.lister.GuildID < 0 ||
		!goutil.HasElem([]int{exclusiveListTypeValid, exclusiveListTypeInvalid}, p.Type) {
		return actionerrors.ErrParams
	}

	p.lister.Status = []int{exclusivecreator.StatusValid}
	if p.Type == exclusiveListTypeInvalid {
		p.lister.Status = []int{exclusivecreator.StatusExpired, exclusivecreator.StatusDeleted}
	}

	return nil
}

func (p *CreatorListParams) exclusiveCreatorList() (*CreatorListResp, error) {
	ecl, pa, err := p.lister.List()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	eclLen := len(ecl)
	resp := &CreatorListResp{
		Pagination: pa,
		Data:       make([]*CreatorListItem, 0, eclLen),
	}
	if eclLen == 0 {
		return resp, nil
	}

	creatorIDs := make([]int64, 0, eclLen)
	guildIDs := make([]int64, 0, eclLen)
	for _, tec := range ecl {
		creatorIDs = append(creatorIDs, tec.CreatorID)
		guildIDs = append(guildIDs, tec.GuildID)
	}
	creatorIDs = util.Uniq(creatorIDs)
	guildIDs = util.Uniq(guildIDs)

	usersMap, err := mowangskuser.FindSimpleMap(creatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	guildMap, err := guild.FindSimpleMap(guildIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	for _, t := range ecl {
		u, ok := usersMap[t.CreatorID]
		if !ok {
			logger.Errorf("主播不存在，主播 ID 为: %d", t.CreatorID)
			return nil, actionerrors.ErrUserNotFound
		}
		g, ok := guildMap[t.GuildID]
		if !ok {
			logger.Errorf("公会不存在，公会 ID 为: %d", t.GuildID)
			return nil, actionerrors.ErrGuildNotExist
		}
		resp.Data = append(resp.Data, &CreatorListItem{
			ID:          t.ID,
			CreatorID:   t.CreatorID,
			GuildID:     t.GuildID,
			Username:    u.Username,
			IconURL:     u.IconURL,
			GuildName:   g.Name,
			Status:      t.Status,
			ContractEnd: t.ContractEnd,
			CreateTime:  t.CreateTime,
			StatusStr:   exclusivecreator.StatusStr(t.Status),
		})
	}

	return resp, nil
}
