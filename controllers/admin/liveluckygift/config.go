package liveluckygift

import (
	"fmt"
	"strings"
	"time"

	"github.com/gocarina/gocsv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckygiftdrop"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
)

type dropConfigImportParam struct {
	CSVURL  upload.SourceURL `form:"csv_url" json:"csv_url"`
	Confirm int              `form:"confirm" json:"confirm"` // 确认次数，首次请求传 0

	imports []*dropConfigCSVItem
}

type dropConfigCSVItem struct {
	StartTimeStr      string `csv:"开始时间"`    // 格式：2024-12-30 12:00:00
	EndTimeStr        string `csv:"结束时间"`    // 格式：2024-12-30 12:00:00
	GiftID            int64  `csv:"随机礼物 ID"` // 随机礼物 ID
	GiftNum           int64  `csv:"随机礼物档位"`  // 随机礼物档位
	PrizeGiftID       int64  `csv:"投放礼物 ID"` // 投放礼物 ID
	PrizeGiftTotalNum int64  `csv:"投放礼物总数量"` // 投放礼物总数量

	startTime int64 // 单位：秒级时间戳
	endTime   int64 // 单位：秒级时间戳
}

// ActionDropConfigImport 导入随机大奖掉落配置
/**
 * @api {post} /api/v2/admin/luckygift/drop/config/import 导入随机大奖掉落配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} csv_url csv 地址
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "导入成功"
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容：
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认新增 2 条配置吗？"
 *     }
 *   }
 */
func ActionDropConfigImport(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDropConfigImportParam(c)
	if err != nil {
		return nil, "", err
	}
	if err = param.insert(); err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "导入成功", nil
}

func newDropConfigImportParam(c *handler.Context) (*dropConfigImportParam, error) {
	var param dropConfigImportParam
	err := c.Bind(&param)
	if err != nil || param.CSVURL == "" {
		return nil, actionerrors.ErrParams
	}

	if err = param.parseCSV(); err != nil {
		return nil, err
	}

	if err = param.checkGifts(); err != nil {
		return nil, err
	}

	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认新增 %d 条配置吗？", len(param.imports))
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return &param, nil
}

func (param *dropConfigImportParam) parseCSV() error {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	skipBOMFile, err := csv.SkipBOM(file)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if err := gocsv.Unmarshal(skipBOMFile, &param.imports); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.imports) == 0 {
		return actionerrors.ErrParamsMsg("CSV 不可为空")
	}

	for i, v := range param.imports {
		prefixMsg := fmt.Sprintf("第 %d 条数据：", i+1)
		startTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.StartTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "开始时间格式错误")
		}
		v.startTime = startTime.Unix()
		endTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.EndTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间格式错误")
		}
		v.endTime = endTime.Unix()
		if v.startTime >= v.endTime {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间不能小于开始时间")
		}
	}
	return nil
}

func (param *dropConfigImportParam) checkGifts() error {
	giftIDs := make([]int64, 0, len(param.imports))
	for _, item := range param.imports {
		giftIDs = append(giftIDs, item.GiftID)
	}
	giftIDs = util.Uniq(giftIDs)
	poolGifts, err := gift.ListPoolGift(giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	poolMap := make(map[int64]map[int]*gift.PoolGift, len(giftIDs))
	for _, pool := range poolGifts {
		if _, ok := poolMap[pool.GiftID]; !ok {
			poolMap[pool.GiftID] = make(map[int]*gift.PoolGift, 3)
		}
		poolMap[pool.GiftID][pool.GiftNum] = pool
	}
	for _, item := range param.imports {
		giftPools, ok := poolMap[item.GiftID]
		if !ok {
			msg := fmt.Sprintf("礼物 ID：%d，无法找到该礼物奖池", item.GiftID)
			return actionerrors.ErrNotFound(msg)
		}
		pool, ok := giftPools[int(item.GiftNum)]
		if !ok {
			msg := fmt.Sprintf("礼物 ID：%d，档位数量：%d，无法找到该档位奖池", item.GiftID, item.GiftNum)
			return actionerrors.ErrNotFound(msg)
		}
		if _, ok := pool.Rates[item.PrizeGiftID]; !ok {
			msg := fmt.Sprintf("礼物 ID：%d，档位数量：%d，投放礼物 ID：%d，投放礼物不在奖池中",
				item.GiftID, item.GiftNum, item.PrizeGiftID)
			return actionerrors.ErrParamsMsg(msg)
		}
	}
	return nil
}

func (param *dropConfigImportParam) insert() error {
	nowUnix := goutil.TimeNow().Unix()
	configs := make([]*liveluckygiftdrop.Config, 0, len(param.imports))
	for _, v := range param.imports {
		configs = append(configs, &liveluckygiftdrop.Config{
			StartTime:         v.startTime,
			EndTime:           v.endTime,
			GiftID:            v.GiftID,
			GiftNum:           v.GiftNum,
			PrizeGiftID:       v.PrizeGiftID,
			PrizeGiftTotalNum: v.PrizeGiftTotalNum,
			CreateTime:        nowUnix,
			ModifiedTime:      nowUnix,
		})
	}
	err := liveluckygiftdrop.InsertConfigs(configs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *dropConfigImportParam) addAdminLog(c goutil.UserContext) {
	msg := fmt.Sprintf("随机大奖掉落配置 新增 %d 条数据", len(param.imports))
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageLiveLuckyGiftDrop, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type dropConfigDelParam struct {
	ID      int64 `form:"id" json:"id"`
	Confirm int   `form:"confirm" json:"confirm"` // 确认次数，首次请求传 0

	config *liveluckygiftdrop.Config
}

// ActionDropConfigDel 删除随机大奖掉落配置
/**
 * @api {post} /api/v2/admin/luckygift/drop/config/del 删除随机大奖掉落配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id 配置 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容：
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认删除随机大奖掉落配置<br>开始时间：2025-02-25 12:00:00，结束时间：2025-02-25 14:00:00<br>随机礼物 ID：%d，档位：%d<br>投放礼物 ID：%d，总数量：%d<br>删除后设置将不再生效"
 *     }
 *   }
 */
func ActionDropConfigDel(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDropConfigDelParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.delete()
	if err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "删除成功", nil
}

func newDropConfigDelParam(c *handler.Context) (*dropConfigDelParam, error) {
	var param dropConfigDelParam
	err := c.Bind(&param)
	if err != nil || param.ID <= 0 {
		return nil, actionerrors.ErrParams
	}
	conf, err := liveluckygiftdrop.FindConfigByID(param.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if conf == nil {
		return nil, actionerrors.ErrNotFound("配置不存在")
	}
	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认删除随机大奖掉落配置<br>"+
			"开始时间：%s，结束时间：%s<br>随机礼物 ID：%d，档位：%d<br>投放礼物 ID：%d，总数量：%d<br>"+
			"删除后设置将不再生效",
			time.Unix(conf.StartTime, 0).Format(goutil.TimeFormatHMS),
			time.Unix(conf.EndTime, 0).Format(goutil.TimeFormatHMS),
			conf.GiftID, conf.GiftNum, conf.PrizeGiftID, conf.PrizeGiftTotalNum)
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	param.config = conf
	return &param, nil
}

func (param *dropConfigDelParam) delete() error {
	ok, err := liveluckygiftdrop.DeleteConfigs([]int64{param.ID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("配置删除失败")
	}
	return nil
}

func (param *dropConfigDelParam) addAdminLog(c goutil.UserContext) {
	box := goclient.NewAdminLogBox(c)
	conf := param.config
	box.Add(userapi.CatalogManageLiveLuckyGiftDrop, fmt.Sprintf("删除随机大奖掉落配置数据，ID: %d；"+
		"配置详情：开始时间: %s，结束时间: %s，随机礼物 ID: %d，随机礼物档位: %d，投放礼物 ID: %d，总数量: %d",
		conf.ID, time.Unix(conf.StartTime, 0).Format(goutil.TimeFormatHMS),
		time.Unix(conf.EndTime, 0).Format(goutil.TimeFormatHMS),
		conf.GiftID, conf.GiftNum, conf.PrizeGiftID, conf.PrizeGiftTotalNum))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type dropConfigClearValidParam struct {
	Confirm int `form:"confirm" json:"confirm"`

	clearConfigs []*liveluckygiftdrop.Config
}

// ActionDropConfigClearValid 批量清空所有有效随机大奖掉落配置
/**
 * @api {post} /api/v2/admin/luckygift/drop/config/clear-valid 批量清空所有有效随机大奖掉落配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "清空成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容：
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认清空所有 %d 条有效随机大奖掉落配置吗？<br>删除后设置将不再生效"
 *     }
 *   }
 */
func ActionDropConfigClearValid(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newDropConfigClearValidParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.clear()
	if err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "清空成功", nil
}

func newDropConfigClearValidParam(c *handler.Context) (*dropConfigClearValidParam, error) {
	var param dropConfigClearValidParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.clearConfigs, err = liveluckygiftdrop.FindValidConfigs()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.clearConfigs) == 0 {
		return nil, actionerrors.ErrNotFound("无有效配置")
	}
	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认清空所有 %d 条有效随机大奖掉落配置吗？<br>"+
			"删除后配置将不再生效", len(param.clearConfigs))
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return &param, nil
}

func (param *dropConfigClearValidParam) clear() error {
	configIDs := goutil.SliceMap(param.clearConfigs, func(conf *liveluckygiftdrop.Config) int64 {
		return conf.ID
	})
	_, err := liveluckygiftdrop.DeleteConfigs(configIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *dropConfigClearValidParam) addAdminLog(c goutil.UserContext) {
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageLiveLuckyGiftDrop, fmt.Sprintf("批量清空有效随机大奖掉落配置数据 %d 条", len(param.clearConfigs)))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
