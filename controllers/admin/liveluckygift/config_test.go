package liveluckygift

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckygiftdrop"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestActionDropConfigImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.LiveDB.Delete(&liveluckygiftdrop.Config{}).Error)

	testConfigs := [][]string{
		{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
		{"2025-12-30 00:00:00", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
	}
	testFilePath := "../../../testdata/drop_config.csv"
	cleanup, err := createTestCSV(testFilePath, testConfigs)
	defer cleanup()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.CollectionDrawPool().UpdateOne(ctx, bson.M{
		"gift_id":  80010,
		"gift_num": 100,
		"type":     gift.PoolTypeGift,
	}, bson.M{
		"$set": bson.M{
			"rates": bson.M{
				"90050": 1,
			},
		},
	}, options.Update().SetUpsert(true))
	require.NoError(err)

	body := dropConfigImportParam{
		CSVURL: "https://fm.example.com/testdata/drop_config.csv",
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, body)
	_, _, err = ActionDropConfigImport(c)
	assert.Equal(actionerrors.ErrConfirmRequired("确认新增 1 条配置吗？", 1, true), err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, body)
	_, msg, err := ActionDropConfigImport(c)
	require.NoError(err)
	assert.Equal("导入成功", msg)

	var dropConfigs []liveluckygiftdrop.Config
	require.NoError(liveluckygiftdrop.DB().Table(liveluckygiftdrop.Config{}.TableName()).Find(&dropConfigs).Error)
	assert.Equal(1, len(dropConfigs))
}

func TestNewPositionImportParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := gift.CollectionDrawPool().UpdateOne(ctx, bson.M{
		"gift_id":  80010,
		"gift_num": 100,
		"type":     gift.PoolTypeGift,
	}, bson.M{
		"$set": bson.M{
			"rates": bson.M{
				"90050": 1,
			},
		},
	}, options.Update().SetUpsert(true))
	require.NoError(err)

	t.Run("参数异常", func(t *testing.T) {
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, nil)
		_, err := newDropConfigImportParam(c)
		assert.Equal(actionerrors.ErrParams, err)
	})
	t.Run("参数中时间格式异常", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30 00:00:00", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
			{"2025-12-30", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
		}
		testFilePath := "../../../testdata/drop_config.csv"
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		body := dropConfigImportParam{
			CSVURL: "https://fm.example.com/testdata/drop_config.csv",
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, body)
		_, err = newDropConfigImportParam(c)
		assert.Equal(actionerrors.ErrParamsMsg("第 2 条数据：开始时间格式错误"), err)
	})
	t.Run("参数待确认", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30 00:00:00", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
		}
		testFilePath := "../../../testdata/drop_config.csv"
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		body := dropConfigImportParam{
			CSVURL: "https://fm.example.com/testdata/drop_config.csv",
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, body)
		_, err = newDropConfigImportParam(c)
		assert.Equal(actionerrors.ErrConfirmRequired("确认新增 1 条配置吗？", 1, true), err)
	})
	t.Run("参数正常", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30 00:00:00", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
		}
		testFilePath := "../../../testdata/drop_config.csv"
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		body := dropConfigImportParam{
			CSVURL: "https://fm.example.com/testdata/drop_config.csv",
		}
		body.Confirm = 1
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/import", true, body)
		param, err := newDropConfigImportParam(c)
		require.NoError(err)
		require.NotNil(param)
		assert.Equal(1, len(param.imports))
	})
}

func TestPositionImportParam_parseCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := dropConfigImportParam{
		CSVURL: "https://fm.example.com/testdata/recommend-invalid.csv",
	}
	testFilePath := "../../../testdata/drop_config.csv"

	t.Run("CSV 不可用", func(t *testing.T) {
		assert.EqualError(param.parseCSV(), "CSV 不可用，请检查后再试")
	})
	t.Run("CSV 不可为空", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
		}
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		param.CSVURL = "https://fm.example.com/testdata/drop_config.csv"
		assert.EqualError(param.parseCSV(), "CSV 不可为空")
	})
	t.Run("开始时间格式错误", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
		}
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		assert.EqualError(param.parseCSV(), "第 1 条数据：开始时间格式错误")
	})
	t.Run("结束时间格式错误", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30 00:00:00", "2025-12-31", "80010", "100", "90050", "10"},
		}
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		assert.EqualError(param.parseCSV(), "第 1 条数据：结束时间格式错误")
	})
	t.Run("结束时间不能小于开始时间", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-31 00:00:00", "2025-12-30 00:00:00", "80010", "100", "90050", "10"},
		}
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		assert.EqualError(param.parseCSV(), "第 1 条数据：结束时间不能小于开始时间")
	})
	t.Run("正常", func(t *testing.T) {
		dropConfigs := [][]string{
			{"开始时间", "结束时间", "随机礼物 ID", "随机礼物档位", "投放礼物 ID", "投放礼物总数量"},
			{"2025-12-30 00:00:00", "2025-12-31 00:00:00", "80010", "100", "90050", "10"},
		}
		cleanup, err := createTestCSV(testFilePath, dropConfigs)
		defer cleanup()
		require.NoError(err)
		require.NoError(param.parseCSV())
	})
}

func createTestCSV(filePath string, data [][]string) (func(), error) {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			return func() {}, err
		}
	}

	cleanupFunc := func() {
		_, err := os.Stat(filePath)
		if err == nil {
			err := os.Remove(filePath)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}

	// 创建 CSV 文件
	file, err := os.Create(filePath)
	if err != nil {
		return cleanupFunc, err
	}
	defer file.Close()

	// 写入 CSV 数据
	writer := csv.NewWriter(file)
	defer writer.Flush()
	if err := writer.WriteAll(data); err != nil {
		return cleanupFunc, err
	}
	return cleanupFunc, nil
}

func TestDropConfigImportParam_checkGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testItem := dropConfigCSVItem{
		GiftID:      80010,
		GiftNum:     100,
		PrizeGiftID: 90050,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	param := dropConfigImportParam{
		imports: []*dropConfigCSVItem{&testItem},
	}
	t.Run("未配置礼物奖池", func(t *testing.T) {
		_, err := gift.CollectionDrawPool().DeleteMany(ctx, bson.M{
			"gift_id": testItem.GiftID,
		})
		require.NoError(err)
		assert.EqualError(param.checkGifts(), "礼物 ID：80010，无法找到该礼物奖池")
	})
	t.Run("未配置指定数量奖池", func(t *testing.T) {
		pool := gift.PoolGift{
			GiftID: testItem.GiftID,
			Type:   gift.PoolTypeGift,
		}
		_, err := gift.CollectionDrawPool().InsertOne(ctx, pool)
		require.NoError(err)
		assert.EqualError(param.checkGifts(), "礼物 ID：80010，档位数量：100，无法找到该档位奖池")
	})
	t.Run("发奖不在奖池里", func(t *testing.T) {
		_, err := gift.CollectionDrawPool().UpdateOne(ctx, bson.M{
			"gift_id": 80010,
			"type":    gift.PoolTypeGift,
		}, bson.M{
			"$set": bson.M{
				"gift_num": 100,
			},
		}, options.Update().SetUpsert(true))
		require.NoError(err)
		assert.EqualError(param.checkGifts(), "礼物 ID：80010，档位数量：100，投放礼物 ID：90050，投放礼物不在奖池中")
	})
	t.Run("奖池正常", func(t *testing.T) {
		_, err := gift.CollectionDrawPool().UpdateOne(ctx, bson.M{
			"gift_id":  80010,
			"gift_num": 100,
			"type":     gift.PoolTypeGift,
		}, bson.M{
			"$set": bson.M{"rates": bson.M{
				"90050": 1,
			}},
		}, options.Update().SetUpsert(true))
		require.NoError(err)
		assert.NoError(param.checkGifts())
	})
}

func TestDropConfigImportParam_insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.LiveDB.Delete(&liveluckygiftdrop.Config{}).Error)

	now := goutil.TimeNow()
	param := dropConfigImportParam{
		imports: []*dropConfigCSVItem{
			{
				startTime:         now.Unix(),
				endTime:           now.Add(time.Hour).Unix(),
				GiftID:            80010,
				GiftNum:           100,
				PrizeGiftID:       90050,
				PrizeGiftTotalNum: 10,
			},
			{
				startTime:         now.Add(time.Hour).Unix(),
				endTime:           now.Add(2 * time.Hour).Unix(),
				GiftID:            80010,
				GiftNum:           100,
				PrizeGiftID:       90050,
				PrizeGiftTotalNum: 10,
			},
		},
	}
	require.NoError(param.insert())
	var dropConfigs []liveluckygiftdrop.Config
	require.NoError(liveluckygiftdrop.DB().Table(liveluckygiftdrop.Config{}.TableName()).Find(&dropConfigs).Error)
	assert.Equal(2, len(dropConfigs))

	require.NoError(service.LiveDB.Delete(&liveluckygiftdrop.Config{}).Error)
	param = dropConfigImportParam{
		imports: []*dropConfigCSVItem{
			{
				startTime:         now.Unix(),
				endTime:           now.Add(time.Hour).Unix(),
				GiftID:            80010,
				GiftNum:           100,
				PrizeGiftID:       90050,
				PrizeGiftTotalNum: 10,
			},
		},
	}
	require.NoError(param.insert())
	require.NoError(liveluckygiftdrop.DB().Table(liveluckygiftdrop.Config{}.TableName()).Find(&dropConfigs).Error)
	assert.Equal(1, len(dropConfigs))
}

func TestActionDropConfigDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Delete(&liveluckygiftdrop.Config{}).Error
	require.NoError(err)

	now := goutil.TimeNow()
	dropConfig := &liveluckygiftdrop.Config{
		StartTime:         now.Unix(),
		EndTime:           now.Add(time.Hour).Unix(),
		GiftID:            80010,
		GiftNum:           100,
		PrizeGiftID:       90050,
		PrizeGiftTotalNum: 10,
	}
	err = service.LiveDB.Create(&dropConfig).Error
	require.NoError(err)

	t.Run("参数异常", func(t *testing.T) {
		body := dropConfigDelParam{}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/del", true, body)
		_, _, err = ActionDropConfigDel(c)
		assert.Equal(actionerrors.ErrParams, err)
	})
	t.Run("ID 不存在", func(t *testing.T) {
		body := dropConfigDelParam{
			ID: 1000,
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/del", true, body)
		_, _, err = ActionDropConfigDel(c)
		assert.EqualError(err, "配置不存在")
	})
	t.Run("删除待确认", func(t *testing.T) {
		body := dropConfigDelParam{
			ID: dropConfig.ID,
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/del", true, body)
		_, _, err = ActionDropConfigDel(c)
		assert.Equal(actionerrors.ErrConfirmRequired(fmt.Sprintf("确认删除随机大奖掉落配置<br>开始时间：%s，结束时间：%s"+
			"<br>随机礼物 ID：80010，档位：100<br>投放礼物 ID：90050，总数量：10<br>删除后设置将不再生效",
			now.Format(goutil.TimeFormatHMS), now.Add(time.Hour).Format(goutil.TimeFormatHMS)),
			1, true), err)
	})
	t.Run("删除成功", func(t *testing.T) {
		body := dropConfigDelParam{
			ID:      dropConfig.ID,
			Confirm: 1,
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/del", true, body)
		_, msg, err := ActionDropConfigDel(c)
		require.NoError(err)
		assert.Equal("删除成功", msg)
		exists, err := servicedb.Exists(liveluckygiftdrop.DB().Table(liveluckygiftdrop.Config{}.TableName()).Where("delete_time = ?", 0))
		require.NoError(err)
		assert.False(exists)
	})
}

func TestActionDropConfigClearValid(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.LiveDB.Delete(&liveluckygiftdrop.Config{}).Error)

	t.Run("无配置可清", func(t *testing.T) {
		body := dropConfigClearValidParam{}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/clear-valid", true, body)
		_, _, err := ActionDropConfigClearValid(c)
		assert.EqualError(err, "无有效配置")
	})

	now := goutil.TimeNow()
	dropConfig := &liveluckygiftdrop.Config{
		StartTime:         now.Unix(),
		EndTime:           now.Add(time.Hour).Unix(),
		GiftID:            80010,
		GiftNum:           100,
		PrizeGiftID:       90050,
		PrizeGiftTotalNum: 10,
	}
	require.NoError(service.LiveDB.Create(&dropConfig).Error)

	t.Run("清空待确认", func(t *testing.T) {
		body := dropConfigClearValidParam{}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/clear-valid", true, body)
		_, _, err := ActionDropConfigClearValid(c)
		assert.Equal(actionerrors.ErrConfirmRequired(
			"确认清空所有 1 条有效随机大奖掉落配置吗？<br>删除后配置将不再生效",
			1, true), err)
	})
	t.Run("清空成功", func(t *testing.T) {
		body := dropConfigClearValidParam{
			Confirm: 1,
		}
		c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/luckygift/drop/config/clear-valid", true, body)
		_, msg, err := ActionDropConfigClearValid(c)
		require.NoError(err)
		assert.Equal("清空成功", msg)
		exists, err := servicedb.Exists(liveluckygiftdrop.DB().Table(liveluckygiftdrop.Config{}.TableName()).
			Where("delete_time = ?", 0))
		require.NoError(err)
		assert.False(exists)
	})
}
