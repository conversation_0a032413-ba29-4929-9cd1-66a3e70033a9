package recommended

import (
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
)

// UploadImage 将筛选好的图片文件上传到 Storage，prefix 用于存储路径前缀
// TODO: 迁移该函数到 upload 包或是 storage 包
func UploadImage(urlFilter *liveupload.ImageURLFilter, prefix string) (string, error) {
	// 创建重命名
	targetPath := urlFilter.Primary.NewTargetPath(prefix)
	return UploadImageWithTarget(urlFilter, targetPath)
}

// UploadImageWithSuffix 将筛选好的图片文件上传到 Storage，prefix 用于存储路径前缀，suffix 用于存储文件名的后缀
func UploadImageWithSuffix(urlFilter *liveupload.ImageURLFilter, prefix string, suffix string) (string, error) {
	// 创建重命名
	targetPath := liveupload.NewTargetPathWithSuffix(urlFilter.Primary, prefix, suffix)
	return UploadImageWithTarget(urlFilter, targetPath)
}

// UploadImageWithTarget 将筛选好的图片文件上传到 Storage，targetPath 为储存的路径
// TODO: 迁移该函数到 upload 包或是 storage 包
func UploadImageWithTarget(urlFilter *liveupload.ImageURLFilter, targetPath upload.TargetPath) (string, error) {
	// Storage 上传
	err := storage.UploadToTarget(urlFilter.Primary, targetPath.String())
	if err != nil {
		return "", err
	}
	// 如果有备选，需要上传备选文件，譬如 WebP 需要补充上传 PNG 文件作为 WebP 的降级支持
	if urlFilter.Secondary != "" {
		secondaryTargetPath := urlFilter.SecondaryTargetPath(targetPath)
		// Storage 上传
		err := storage.UploadToTarget(urlFilter.Secondary, secondaryTargetPath.String())
		if err != nil {
			return "", err
		}
	}
	// 如果有备选，需要上传备选文件，譬如 AVIF 需要补充上传 WebP 和 PNG 文件作为 AVIF 的降级支持
	if urlFilter.Tertiary != "" {
		tertiaryTargetPath := urlFilter.TertiaryTargetPath(targetPath)
		// Storage 上传
		err := storage.UploadToTarget(urlFilter.Tertiary, tertiaryTargetPath.String())
		if err != nil {
			return "", err
		}
	}

	return storage.ParseSchemeURL(targetPath.String()), nil
}
