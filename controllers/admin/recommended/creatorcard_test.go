package recommended

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCreatorCardAdd(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	nowUnix := goutil.TimeNow().Unix()

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/add", true, handler.M{
		"room_ids": "223344",
		"urls": []upload.SourceURL{
			"https://fm.example.com/testdata/test.webp",
			"https://fm.example.com/testdata/test.png",
		},
		"clip":        "1_1_1_1",
		"start_time":  nowUnix,
		"expire_time": nowUnix + 10,
	})
	resp, err := ActionCreatorCardAdd(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/add", true, handler.M{
		"clip": "1_1_1",
	})
	_, err = ActionCreatorCardAdd(c)
	assert.EqualError(err, "参数错误")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/add", true, handler.M{
		"room_ids": "223344,909090",
		"clip":     "1_1_1_1",
	})
	_, err = ActionCreatorCardAdd(c)
	assert.EqualError(err, "部分直播间不存在，请检查后重新输入")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/add", true, handler.M{
		"room_ids":    "223344",
		"clip":        "1_1_1_1",
		"start_time":  nowUnix,
		"expire_time": nowUnix,
	})
	_, err = ActionCreatorCardAdd(c)
	assert.EqualError(err, "请输入正确的开始结束时间")

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/add", true, handler.M{
		"room_ids":    "223344",
		"urls":        []upload.SourceURL{"https://static.example.com/live/room/icon/icon01.jpg"},
		"clip":        "1_1_1_1",
		"start_time":  nowUnix,
		"expire_time": nowUnix + 10,
	})
	_, err = ActionCreatorCardAdd(c)
	assert.Equal(http.StatusBadRequest, err.(*handler.ActionError).Status)
	assert.EqualError(err, liveupload.ErrImageType.Error())
}

func TestActionCreatorCardDelete(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRecommendedID := int64(101010011001)
	err := service.DB.Create(&liverecommendedelements.LiveRecommendedElements{
		ID:          testRecommendedID,
		Sort:        1,
		ElementID:   121212,
		ElementType: liverecommendedelements.ElementCreatorCard,
	}).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/delete", true, handler.M{
		"id": testRecommendedID,
	})
	resp, err := ActionCreatorCardDelete(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/creatorcard/delete", true, handler.M{
		"id": testRecommendedID,
	})
	_, err = ActionCreatorCardDelete(c)
	assert.EqualError(err, "主播信息背景不存在")
}
