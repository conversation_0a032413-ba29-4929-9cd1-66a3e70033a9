package recommended

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(paramLiveIcon{}, "room_ids", "urls", "start_time", "expire_time")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(paramLiveIcon{}, "room_ids", "urls", "start_time", "expire_time")

	kc = tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(liveIcon{}, "id", "sort", "element_id", "element_type", "url", "start_time",
		"expire_time", "create_time", "modified_time")
}

func TestLiveIcon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID, err := room.FindRoomID(10)
	roomIDStr := strconv.FormatInt(roomID, 10)
	require.NoError(err)

	now := goutil.TimeNow()
	startTime := now.Add(-time.Minute).Unix()
	expireTime := now.Add(time.Minute).Unix()
	// test params
	input := paramLiveIcon{
		RoomIDs:    roomIDStr,
		URLs:       []upload.SourceURL{"https://static.example.com/live/room/icon/icon01.jpg"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddLiveIcon(c)
	assert.Equal(http.StatusBadRequest, err.(*handler.ActionError).Status)

	input.URLs = []upload.SourceURL{"https://static.example.com/live/room/icon/icon01.webp"}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddLiveIcon(c)
	assert.EqualError(err, liveupload.ErrWebPRequireImage.Error())

	input.URLs = []upload.SourceURL{"https://static.example.com/live/room/icon/icon01.png"}
	input.ExpireTime = startTime
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddLiveIcon(c)
	assert.EqualError(err, "请输入正确的开始结束时间")

	input = paramLiveIcon{
		RoomIDs:    roomIDStr + ",99999999",
		URLs:       []upload.SourceURL{"https://static.example.com/live/room/icon/icon01.png"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddLiveIcon(c)
	assert.EqualError(err, "部分直播间不存在，请检查后重新输入")

	// test
	input = paramLiveIcon{
		RoomIDs: roomIDStr,
		URLs: []upload.SourceURL{
			"https://fm.example.com/testdata/test.webp",
			"https://fm.example.com/testdata/test.png",
		},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddLiveIcon(c)
	assert.NoError(err)

	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	resp, err := ActionListLiveIcon(c)
	require.NoError(err)
	r := resp.(*liveIconListResp)
	require.NotZero(len(r.Data))
	hasRoom := false
	for _, v := range r.Data {
		assert.GreaterOrEqual(v.ExpireTime, now.Unix())
		if v.RoomID == roomID {
			hasRoom = true
		}
	}
	assert.True(hasRoom, "应该含有测试房间")

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": r.Data[0].ID})
	_, err = ActionDelLiveIcon(c)
	require.NoError(err)
}

func TestJSONTag(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(tutil.KeyExists(tutil.JSON, resultLiveIcon{},
		"id", "room_id",
		"user_id", "username",
		"url", "start_time", "expire_time",
		"create_time", "modified_time"))
}
