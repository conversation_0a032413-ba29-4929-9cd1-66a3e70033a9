package recommended

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveScheduleTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(liveSchedule{}, "id", "sort", "room_id", "cover_url", "start_time", "expire_time",
		"tag", "name", "status", "creator_username")
	kc.Check(liveSchedules{}, "time", "data")
}

func TestActionSchedule(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 1. ActionAddSchedule
	tomorrow := goutil.TimeNow().AddDate(0, 0, 1)
	startTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	// 测试受限房间
	c := handler.NewTestContext(http.MethodPost, "/addschedule", false, paramAddSchedule{
		itemRecommended: itemRecommended{
			Sort:     1,
			RoomID:   room.TestLimitedRoomID,
			CoverURL: upload.SourceURL("https://static.missevan.com/avatars/icon01.png"),
			Tag:      liverecommendedelements.AttrTagArtist,
		},
		Intervals: []timeInterval{{StartTime: startTime.Unix()}},
	})
	_, err := ActionAddSchedule(c)
	assert.Equal(actionerrors.ErrLimitedRoom, err)

	addInput := paramAddSchedule{
		itemRecommended: itemRecommended{
			Sort:     1,
			RoomID:   22489473,
			CoverURL: upload.SourceURL("https://static-test.missevan.com/avatars/icon01.png"),
			Tag:      liverecommendedelements.AttrTagArtist,
		},
		Intervals: []timeInterval{{StartTime: startTime.Unix()}},
	}
	c = handler.NewTestContext(http.MethodPost, "/addschedule", true, addInput)
	newData, err := ActionAddSchedule(c)
	require.NoError(err)
	resp := newData.([]*liveSchedule)
	require.NotEmpty(resp)
	require.NotNil(resp[0])
	id := resp[0].ID
	var m liverecommendedelements.Model
	err = service.DB.Where("id = ?", id).Take(&m).Error
	require.NoError(err)
	assert.Equal(addInput.Sort, m.Sort)
	assert.Equal(addInput.RoomID, m.ElementID)
	assert.Equal("oss://avatars/icon01.png", m.Cover)
	assert.Empty(m.Name)
	assert.Equal(util.NewBitMaskFromFlag(liverecommendedelements.AttrTagArtist), m.Attr)
	// 测试同一时间段被删除之后再插入是否能成功
	require.NoError(service.DB.Table(m.TableName()).Where("id = ?", id).Update("sort", 0).Error)
	c = handler.NewTestContext(http.MethodPost, "/addschedule", true, addInput)
	newData2, err := ActionAddSchedule(c)
	require.NoError(err)
	resp = newData2.([]*liveSchedule)
	require.NotEmpty(resp)
	require.NotNil(resp[0])
	id2 := resp[0].ID
	assert.NotEqual(id, id2)
	id = id2

	modifiedTime := m.ModifiedTime

	// ActionEditSchedule 不修改封面图
	editInput := paramEditSchedule{
		ID: id,
		itemRecommended: itemRecommended{
			Sort:   2,
			RoomID: 22489473,
			Tag:    liverecommendedelements.AttrTagArtist,
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/editschedule", true, editInput)
	_, err = ActionEditSchedule(c)
	require.NoError(err)

	// ActionEditSchedule
	cover := "avatars/icon02.png"
	editInput = paramEditSchedule{
		ID: id,
		itemRecommended: itemRecommended{
			Sort:     2,
			RoomID:   22489473,
			CoverURL: upload.SourceURL(config.Conf.Service.Storage["oss"].PublicURL + cover),
			Tag:      liverecommendedelements.AttrTagQualityAnchor,
		},
	}

	c = handler.NewTestContext(http.MethodPost, "/editschedule", true, editInput)
	_, err = ActionEditSchedule(c)
	require.NoError(err)
	m = liverecommendedelements.Model{}
	err = service.DB.Where("id = ?", id).First(&m).Error
	require.NoError(err)
	assert.Equal(editInput.Sort, m.Sort)
	assert.Equal(editInput.RoomID, m.ElementID)
	assert.Equal("oss://"+cover, m.Cover)
	assert.GreaterOrEqual(m.ModifiedTime, modifiedTime)
	assert.Equal(util.NewBitMaskFromFlag(liverecommendedelements.AttrTagQualityAnchor), m.Attr)

	// error param
	editInput.Tag = liverecommendedelements.AttrTagNoble
	editInput.ID = 999999
	editInput.Tag = liverecommendedelements.AttrTagArtist
	c = handler.NewTestContext(http.MethodPost, "/editschedule", true, editInput)
	_, err = ActionEditSchedule(c)
	assert.EqualError(err, "记录的 ID 不存在")
	// 封面图允许修改
	var nobleID int64 = 45311
	cover = "avatars/icon02.png"
	editInput = paramEditSchedule{
		ID: nobleID,
		itemRecommended: itemRecommended{
			Sort:     1,
			RoomID:   22489473,
			CoverURL: upload.SourceURL(config.Conf.Service.Storage["oss"].PublicURL + cover),
			Tag:      liverecommendedelements.AttrTagNoble,
		},
	}
	c = handler.NewTestContext("POST", "/editschedule", true, editInput)
	_, err = ActionEditSchedule(c)
	assert.NoError(err)

	// 3. ActionListSchedule
	c = handler.NewTestContext(http.MethodGet, "/?date="+tomorrow.Format("2006-01-02"), false, nil)
	r, err := ActionListSchedule(c)
	require.NoError(err)
	result := r.([]liveSchedules)
	assert.Len(result, 48)

	// 4. ActionDelSchedule
	c = handler.NewTestContext(http.MethodPost, "/", true, strings.NewReader(fmt.Sprintf(`{"id":%d}`, id)))
	_, err = ActionDelSchedule(c)
	require.NoError(err)

	err = service.DB.Where("id = ?", id).Take(&m).Error
	require.NoError(err)
	assert.Zero(m.Sort)

	c = handler.NewTestContext(http.MethodPost, "/delschedule", true,
		strings.NewReader(`{"id":999999}`))
	_, err = ActionDelSchedule(c)
	assert.EqualError(err, "找不到要取消的推荐！")
}

func TestItemRecommended_checkThenUpload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := itemRecommended{
		CoverURL: "https://www.example.com/test.png",
	}
	require.NoError(p.checkThenUpload())
	assert.Equal(p.CoverURL.String(), p.coverURL)

	p = itemRecommended{
		CoverURL: "",
	}
	require.NoError(p.checkThenUpload())
	assert.Empty(p.coverURL)
}

func TestAddExtraInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.NoError(addExtraInfo(nil))
	assert.NoError(addExtraInfo([]*liveSchedule{nil}))

	l := []*liveSchedule{
		{RoomID: room.TestExistsRoomID},
	}
	require.NoError(addExtraInfo(l))
	assert.NotEmpty(l[0].CoverURL)
}

func TestActionListTopSchedule(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/listtopschedule", true, nil)
	r, err := ActionListTopSchedule(c)
	require.NoError(err)
	assert.NotEmpty(r)
}
