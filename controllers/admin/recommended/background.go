package recommended

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type backgroundParam struct {
	RoomIDs    string             `form:"room_ids" json:"room_ids"`
	URLs       []upload.SourceURL `form:"urls" json:"urls"`
	Opacity    float64            `form:"opacity" json:"opacity"`
	StartTime  int64              `form:"start_time" json:"start_time"`
	ExpireTime int64              `form:"expire_time" json:"expire_time"`
}

type liveBackground struct {
	ID           int64  `gorm:"column:id"`
	Sort         int    `gorm:"column:sort"`
	ElementID    int64  `gorm:"column:element_id"`
	ElementType  int    `gorm:"column:element_type"`
	URL          string `gorm:"column:url"`
	StartTime    int64  `gorm:"column:start_time"`
	ExpireTime   int64  `gorm:"column:expire_time"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
}

// ActionAddBackground 设置直播间推荐背景图
/**
 * @api {post} /api/v2/admin/recommended/addbackground 设置直播间推荐背景图
 * @apiDescription 设置直播间背景图, 动态背景不需要额外上传 apng 资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} room_ids 直播间 IDs, 半角逗号分隔
 * @apiParam {String} urls 背景地址
 * @apiParam {Number} opacity 透明度
 * @apiParam {Number} start_time 开始时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} expire_time 结束时间, 秒级时间戳, 如: 1578844800
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionAddBackground(c *handler.Context) (handler.ActionResponse, error) {
	var param backgroundParam
	err := c.Bind(&param)
	if err != nil || param.RoomIDs == "" || param.Opacity < 0 {
		return nil, actionerrors.ErrParams
	}
	roomIDs, err := goutil.SplitToInt64Array(param.RoomIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	roomIDs = util.Uniq(roomIDs)
	ok, err := room.ExistsAll(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrParamsMsg("部分直播间不存在，请检查后重新输入")
	}

	filter, err := liveupload.NewImageURLFilter(param.URLs)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(err.Error())
	}
	targetPath, err := UploadImage(filter, storage.PathPrefixBackground)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	inserts := make([]*liveBackground, len(roomIDs))
	now := goutil.TimeNow().Unix()
	for i := range roomIDs {
		inserts[i] = &liveBackground{
			Sort:         1,
			ElementID:    roomIDs[i],
			ElementType:  liverecommendedelements.ElementBackground,
			URL:          liverecommendedelements.BuildBackgroundURL(targetPath, param.Opacity),
			StartTime:    param.StartTime,
			ExpireTime:   param.ExpireTime,
			CreateTime:   now,
			ModifiedTime: now,
		}
	}
	err = servicedb.BatchInsert(service.DB, liverecommendedelements.TableName(), inserts)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置直播间推荐背景图, 直播间 IDs: %s, 开始时间: %s, 结束时间: %s", goutil.JoinInt64Array(roomIDs, ","),
		time.Unix(param.StartTime, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.ExpireTime, 0).Format(util.TimeFormatYMDHMS),
	)
	box.Add(userapi.CatalogRecommendedBackgroundAdd, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

// ActionDelBackground 取消直播间推荐背景图
/**
 * @api {post} /api/v2/admin/recommended/delbackground 取消直播间推荐背景图
 * @apiDescription 取消设置直播间推荐背景图后，会使用主播最后审核通过的背景图
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "取消成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionDelBackground(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	model, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementBackground)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if model == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("取消直播间推荐背景图, 直播间 ID: %d, 推荐 ID: %d", model.ElementID, input.ID)
	box.Add(userapi.CatalogRecommendedBackgroundDel, intro, goclient.AdminLogOptions{
		ChannelID: &model.ElementID,
	})
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "取消成功", nil
}
