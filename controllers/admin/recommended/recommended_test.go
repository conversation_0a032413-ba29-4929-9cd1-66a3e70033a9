package recommended

import (
	"encoding/csv"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)
	m.Run()
}

func TestGetSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 受限房间
	c := handler.NewTestContext(http.MethodPost, "/set", true, reqSet{
		Position: 1,
		RoomID:   goutil.NewInt64(room.TestLimitedRoomID),
	})
	_, err := ActionSet(c)
	assert.Equal(actionerrors.ErrLimitedRoom, err)

	// find a room_id
	roomID, err := room.FindRoomID(10)
	require.NoError(err)

	// test save
	req := reqSet{
		Position: 1,
		RoomID:   &roomID,
	}

	c = handler.NewTestContext(http.MethodGet, "/set", true, req)
	resp, err := ActionSet(c)
	require.NoError(err)
	assert.Equal(resp, "保存成功")

	// save twice
	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodPost, "/set", tutil.ToRequestBody(req))
	resp, err = ActionSet(c)
	require.NoError(err)
	assert.Equal(resp, "保存成功")

	// should get only one record
	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/get", nil)
	resp, err = ActionGet(c)
	require.NoError(err)
	data := resp.([]liverecommendedelements.Room)
	assert.NotEmpty(data)

	count := 0
	for _, v := range data {
		if v.Position == 1 {
			count++
			assert.NotZero(v.RoomID)
		}
	}
	assert.Equal(1, count, "测试的推荐位上应该有且仅有一个直播间")

	// 测试清空推荐位
	req = reqSet{Position: 1}
	req.RoomID = new(int64)

	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodPost, "/set", tutil.ToRequestBody(req))
	resp, err = ActionSet(c)
	require.NoError(err)
	assert.Equal(resp.(string), "清空成功")

	// 空推荐位的 room_id 是 0
	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/get", nil)
	resp, err = ActionGet(c)
	require.NoError(err)
	data = resp.([]liverecommendedelements.Room)
	count = 0
	for _, v := range data {
		if v.Position == 1 {
			count++
			assert.Zero(v.RoomID)
		}
	}
	assert.Equal(1, count, "测试的第一个推荐位应该有且仅有一个")
}

func TestActionPositionImport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementLiveTabRecommend).Error
	require.NoError(err)

	now := goutil.TimeNow()

	testRoomIDs := [][]string{
		{"房间 ID", "推荐位置", "开始时间", "结束时间"},
		{"4381915", "2", now.Format(goutil.TimeFormatHMS), now.Add(time.Minute).Format(goutil.TimeFormatHMS)},
	}
	testFilePath := "../../../testdata/recommend.csv"
	cleanup, err := createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	body := positionImportParam{
		CSVURL: "https://fm.example.com/testdata/recommend.csv",
		Type:   recommendedTypeLiveTab,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, body)
	_, _, err = ActionPositionImport(c)
	assert.Equal(actionerrors.ErrConfirmRequired("确认新增 1 条配置吗？", 1, true), err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, body)
	_, msg, err := ActionPositionImport(c)
	require.NoError(err)
	assert.Equal("导入成功", msg)
	recommendTags, err := liverecommendedelements.ListLiveTabRecommend()
	require.NoError(err)
	assert.Equal(1, len(recommendTags))
}

func TestNewPositionImportParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, nil)
	_, err := newPositionImportParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	testRoomIDs := [][]string{
		{"房间 ID", "推荐位置", "开始时间", "结束时间"},
		{"4381915", "2", "2024-12-11 12:00:00", "2024-12-31 12:00:00"},
	}
	testFilePath := "../../../testdata/recommend.csv"
	cleanup, err := createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	body := positionImportParam{
		CSVURL: "https://fm.example.com/testdata/recommend.csv",
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, body)
	_, err = newPositionImportParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.Type = recommendedTypeLiveTab
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, body)
	_, err = newPositionImportParam(c)
	assert.Equal(actionerrors.ErrConfirmRequired("确认新增 1 条配置吗？", 1, true), err)

	body.Confirm = 1
	body.Type = recommendedTypeLiveTab
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/import", true, body)
	param, err := newPositionImportParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(1, len(param.imports))
}

func TestPositionImportParam_parseCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := positionImportParam{
		Type:   recommendedTypeLiveTab,
		CSVURL: "https://fm.example.com/testdata/recommend-invalid.csv",
	}
	err := param.parseCSV()
	assert.EqualError(err, "CSV 不可用，请检查后再试")

	testRoomIDs := [][]string{
		{"房间 ID", "推荐位置", "开始时间", "结束时间"},
	}
	testFilePath := "../../../testdata/recommend.csv"
	cleanup, err := createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)

	param.CSVURL = "https://fm.example.com/testdata/recommend.csv"
	err = param.parseCSV()
	assert.EqualError(err, "CSV 不可为空")

	testRoomIDs = [][]string{
		{"房间 ID", "推荐位置", "开始时间", "结束时间"},
		{"9074501", "10", "2024-12-11", "2024-12-31"},
	}
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "房间 9074501 开始时间格式错误")

	testRoomIDs[1][2] = "2024-12-11 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "房间 9074501 结束时间格式错误")

	testRoomIDs[1][3] = "2024-12-11 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "房间 9074501 结束时间不能小于开始时间")

	testRoomIDs[1][3] = "2024-12-31 12:00:00"
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "房间 9074501 推荐位置错误，位置范围 1-8")

	testRoomIDs[1][1] = "8"
	param.Type = recommendedTypeSearch
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "房间 9074501 推荐位置错误，位置范围 1-3")

	testRoomIDs[1][1] = "1"
	param.Type = recommendedTypeSearch
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	assert.EqualError(err, "有不存在的房间")

	testRoomIDs[1][0] = "4381915"
	cleanup, err = createTestCSV(testFilePath, testRoomIDs)
	defer cleanup()
	require.NoError(err)
	err = param.parseCSV()
	require.NoError(err)
}

func createTestCSV(filePath string, data [][]string) (func(), error) {
	_, err := os.Stat(filePath)
	if err == nil {
		err := os.Remove(filePath)
		if err != nil {
			return func() {}, err
		}
	}

	cleanupFunc := func() {
		_, err := os.Stat(filePath)
		if err == nil {
			err := os.Remove(filePath)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}

	// 创建 CSV 文件
	file, err := os.Create(filePath)
	if err != nil {
		return cleanupFunc, err
	}
	defer file.Close()

	// 写入 CSV 数据
	writer := csv.NewWriter(file)
	defer writer.Flush()
	if err := writer.WriteAll(data); err != nil {
		return cleanupFunc, err
	}
	return cleanupFunc, nil
}

func TestPositionImportParam_insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type IN (?)", []int{
			liverecommendedelements.ElementLiveTabRecommend,
			liverecommendedelements.ElementSearchRecommend,
		}).Error
	require.NoError(err)

	now := goutil.TimeNow()
	param := positionImportParam{
		Type: recommendedTypeLiveTab,
		imports: []*recommendCSVItem{
			{
				RoomID:    9074501,
				Position:  1,
				startTime: now.Unix(),
				endTime:   now.Add(time.Hour).Unix(),
			},
			{
				RoomID:    9074502,
				Position:  2,
				startTime: now.Unix(),
				endTime:   now.Add(time.Hour).Unix(),
			},
		},
	}
	require.NoError(param.insert())
	recommendTags, err := liverecommendedelements.ListLiveTabRecommend()
	require.NoError(err)
	assert.Equal(2, len(recommendTags))

	param = positionImportParam{
		Type: recommendedTypeSearch,
		imports: []*recommendCSVItem{
			{
				RoomID:    9074501,
				Position:  1,
				startTime: now.Unix(),
				endTime:   now.Add(time.Hour).Unix(),
			},
		},
	}
	require.NoError(param.insert())
	recommendTags, err = liverecommendedelements.ListSearchRecommend()
	require.NoError(err)
	assert.Equal(1, len(recommendTags))
}

func TestActionPositionDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.DB.Delete(&liverecommendedelements.LiveRecommendedElements{},
		"element_type = ?", liverecommendedelements.ElementLiveTabRecommend).Error
	require.NoError(err)

	body := positionDelParam{
		ID: 1555555,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/del", true, body)
	_, _, err = ActionPositionDel(c)
	assert.EqualError(err, "请输入正确的 ID")

	now := goutil.TimeNow().Unix()
	element := &liverecommendedelements.LiveRecommendedElements{
		Sort:        0,
		ElementID:   22489473,
		ElementType: liverecommendedelements.ElementLiveTabRecommend,
		StartTime:   goutil.NewInt64(now),
		ExpireTime:  now,
	}
	err = service.DB.Create(&element).Error
	require.NoError(err)
	body.ID = element.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/del", true, body)
	_, _, err = ActionPositionDel(c)
	assert.EqualError(err, "请输入正确的 ID")

	err = service.DB.Table(liverecommendedelements.TableName()).Where("id = ?", element.ID).Update("sort", 1).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/del", true, body)
	_, _, err = ActionPositionDel(c)
	assert.EqualError(err, "该设置已下线，无法删除")

	err = service.DB.Table(liverecommendedelements.TableName()).Where("id = ?", element.ID).Update("expire_time", now+1).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/del", true, body)
	_, _, err = ActionPositionDel(c)
	assert.Equal(actionerrors.ErrConfirmRequired("确认删除推荐直播间固定位置<br>直播间 ID: 22489473<br>主播昵称: <br>固定位置: 1<br>删除后设置将不再生效", 1, true), err)

	body.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/position/del", true, body)
	_, msg, err := ActionPositionDel(c)
	require.NoError(err)
	assert.Equal("删除成功", msg)
	exists, err := servicedb.Exists(service.DB.Table(liverecommendedelements.TableName()).
		Where("id = ? AND sort > ?", element.ID, liverecommendedelements.SortDeleted))
	require.NoError(err)
	assert.False(exists)
}

func TestActionAlgorithmExposureAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理测试数据
	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementAlgorithmExposure).Error
	require.NoError(err)

	now := goutil.TimeNow()

	// 测试参数错误
	api := "/api/v2/admin/recommended/algorithm-exposure/add"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)
	_, _, err = ActionAlgorithmExposureAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试房间 ID 为 0
	input := paramAlgorithmExposureAdd{
		CatalogID:       liverecommendedelements.SquareTypeHot,
		RoomID:          0,
		ExposureLevelID: 2,
		StartTime:       now.Unix(),
		ExpireTime:      now.Add(time.Hour).Unix(),
	}
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, _, err = ActionAlgorithmExposureAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试过期时间小于开始时间
	input.RoomID = 4381915
	input.ExpireTime = now.Unix()
	input.StartTime = now.Add(time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, _, err = ActionAlgorithmExposureAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试受限房间
	input.RoomID = room.TestLimitedRoomID
	input.StartTime = now.Unix()
	input.ExpireTime = now.Add(time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, _, err = ActionAlgorithmExposureAdd(c)
	assert.Equal(actionerrors.ErrLimitedRoom, err)

	// 测试正常添加
	roomID, err := room.FindRoomID(10)
	require.NoError(err)
	input.RoomID = roomID
	input.StartTime = now.Unix()
	input.ExpireTime = now.Add(time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, msg, err := ActionAlgorithmExposureAdd(c)
	require.NoError(err)
	assert.Equal("设置成功", msg)

	// 验证数据是否正确添加
	var count int64
	err = service.DB.Table(liverecommendedelements.TableName()).
		Where("element_type = ? AND element_id = ?", liverecommendedelements.ElementAlgorithmExposure, roomID).
		Count(&count).Error
	require.NoError(err)
	assert.Equal(int64(1), count)
}

func TestActionAlgorithmExposureDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理测试数据
	err := service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementAlgorithmExposure).Error
	require.NoError(err)

	// 测试参数错误
	api := "/api/v2/admin/recommended/algorithm-exposure/del"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)
	_, _, err = ActionAlgorithmExposureDel(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试记录不存在
	input := struct {
		ID int64 `form:"id" json:"id"`
	}{ID: 999999}
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, _, err = ActionAlgorithmExposureDel(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	// 添加测试数据
	now := goutil.TimeNow()
	roomID, err := room.FindRoomID(10)
	require.NoError(err)

	extendedFields := `{"exposure_level": 2}`
	element := &liverecommendedelements.Model{
		Sort:        1,
		ElementID:   roomID,
		ElementType: liverecommendedelements.ElementAlgorithmExposure,
		Attribute: liverecommendedelements.Attribute{
			Name:      strconv.FormatInt(liverecommendedelements.SquareTypeHot, 10),
			StartTime: goutil.NewInt64(now.Unix()),
		},
		ExpireTime:     now.Add(time.Hour).Unix(),
		ExtendedFields: extendedFields,
	}
	err = service.DB.Create(element).Error
	require.NoError(err)

	// 测试正常删除
	input.ID = element.ID
	c = handler.NewTestContext(http.MethodPost, api, true, input)
	_, msg, err := ActionAlgorithmExposureDel(c)
	require.NoError(err)
	assert.Equal("取消成功", msg)

	// 验证数据是否正确删除
	var exists bool
	exists, err = servicedb.Exists(service.DB.Table(liverecommendedelements.TableName()).
		Where("id = ? AND sort > ?", element.ID, liverecommendedelements.SortDeleted))
	require.NoError(err)
	assert.False(exists)
}
