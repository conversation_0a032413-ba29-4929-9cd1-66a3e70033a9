package recommended

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAvatarFrameAddAndList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	userIDStr := "12"
	user, err := mowangskuser.FindByUserID(userID)
	require.NoError(err)
	assert.NotNil(user)
	now := goutil.TimeNow()
	startTime := now.Add(-time.Minute).Unix()
	expireTime := now.Add(time.Minute).Unix()
	defer func() {
		// 清理过期数据
		service.DB.Table(liverecommendedelements.TableName()).
			Where("element_id = ? AND element_type = ? AND expire_time < ?",
				userID, liverecommendedelements.ElementAvatarFrame, now.Unix()).Delete("")
	}()

	// test err image URL params
	input := avatarFrameParam{
		UserIDs:    userIDStr,
		URLs:       []upload.SourceURL{"https://fm.example.com/testdata/test.jpg"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c := handler.NewTestContext(http.MethodPost, "", true, input)
	_, err = ActionAddAvatarFrame(c)
	require.Equal(http.StatusBadRequest, err.(*handler.ActionError).Status)

	// test add: no user err
	input = avatarFrameParam{
		UserIDs:    "12,99999999",
		URLs:       []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddAvatarFrame(c)
	require.EqualError(err, "部分用户不存在，请检查后重新输入")

	// test err time params
	input = avatarFrameParam{
		UserIDs:    userIDStr,
		URLs:       []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
		StartTime:  startTime,
		ExpireTime: startTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err = ActionAddAvatarFrame(c)
	require.EqualError(err, "请输入正确的开始结束时间")

	// test add: image url error
	input = avatarFrameParam{
		UserIDs:    userIDStr,
		URLs:       []upload.SourceURL{"oss://test.webp"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, tutil.ToRequestBody(input))
	_, err = ActionAddAvatarFrame(c)
	assert.EqualError(err, "请求上传的资源不被支持，请重新选择")

	// test add: image url error
	input = avatarFrameParam{
		UserIDs:    userIDStr,
		URLs:       []upload.SourceURL{"https://fm.example.com/testdata/test.gif", "https://fm.example.com/testdata/test.webp"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, tutil.ToRequestBody(input))
	_, err = ActionAddAvatarFrame(c)
	assert.EqualError(err, liveupload.ErrImageType.Error())

	// test add: normally
	input = avatarFrameParam{
		UserIDs:    userIDStr,
		URLs:       []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
		StartTime:  startTime,
		ExpireTime: expireTime,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, tutil.ToRequestBody(input))
	_, err = ActionAddAvatarFrame(c)
	require.NoError(err)

	// test list
	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	resp, err := ActionListAvatarFrame(c)
	require.NoError(err)
	r := resp.(*avatarFrameListResp)
	require.NotZero(len(r.Data))
	for _, v := range r.Data {
		assert.GreaterOrEqual(now.Unix(), v.StartTime)
		assert.GreaterOrEqual(v.ExpireTime, now.Unix())
	}
	// test delete
	var id int64
	for i := range r.Data {
		if r.Data[i].UserID == userID {
			id = r.Data[i].ID
			break
		}
	}
	c = handler.NewTestContext("POST", "/", true, handler.M{"id": id})
	_, err = ActionDelAvatarFrame(c)
	require.NoError(err)
}

func TestParseExpireTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	_date, err := parseExpireTime("2019-1-1")
	assert.Equal(actionerrors.ErrParams, err)

	_date, err = parseExpireTime("2019-01-01")
	assert.Equal(actionerrors.ErrParams, err)

	_date, err = parseExpireTime("2019-01-01 12:00")
	assert.Equal(actionerrors.ErrParams, err)

	_date, err = parseExpireTime("2019-01-01 23:59:59")
	require.NoError(err)
	_date.Unix()
	assert.Equal("2019-01-01 23:59:59", _date.Format(layoutDateTime))
}
