package recommended

import (
	"encoding/json"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// NoExtra 无额外配置，所有直播间展示图标
	NoExtra = 0
	// SetExtraAllowList 非直播间全部展示，满足配置的直播间才展示图标
	SetExtraAllowList = 1
	// SetExtraBlockList 非直播间全部展示，满足配置的直播间不展示图标
	SetExtraBlockList = 2
	// NoChange 在修改小窗时表示展示图标的配置无需修改
	NoChange = -1
)

type liveEventParam struct {
	Name       string             `form:"name" json:"name"`
	Covers     []upload.SourceURL `form:"covers" json:"covers"`
	OldCovers  []upload.SourceURL `form:"old_covers" json:"old_covers"`
	URL        string             `form:"url" json:"url"`
	Sort       int                `form:"sort" json:"sort"`
	ShowClose  bool               `form:"show_close" json:"show_close"`
	StartTime  int64              `form:"start_time" json:"start_time"`
	ExpireTime int64              `form:"expire_time" json:"expire_time"`

	SetExtra      int64  `form:"set_extra" json:"set_extra"`
	CatalogIDs    string `form:"catalog_ids" json:"catalog_ids"`
	TagIDs        string `form:"tag_ids" json:"tag_ids"`
	RoomIDs       string `form:"room_ids" json:"room_ids"`
	catalogIDList []int64
	tagIDList     []int64
	roomIDList    []int64

	urlFilter         *liveupload.ImageURLFilter
	oldCoverURLFilter *liveupload.ImageURLFilter
}

// ActionAddEvent 添加直播间活动图标
/**
 * @api {post} /api/v2/admin/recommended/addevent  添加直播间活动图标
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} name 活动名称简介
 * @apiParam {String[]} covers 活动图标
 * @apiParam {String} url 跳转路径
 * @apiParam {Number} sort 排序
 * @apiParam {Boolean} show_close 控制图标是否能被关掉
 * @apiParam {Number} start_time 开始时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} expire_time 结束时间, 秒级时间戳, 如: 1578844800
 * @apiParam {number=0,1} set_extra 设置配置项。0：不设置配置项，所有直播间都展示图标。1：满足配置的直播间才展示图标
 * @apiParam {String} [catalog_ids] 分区 ID 列表，多个分区 ID 用英文逗号隔开，为空的话则不对分区做限制
 * @apiParam {String} [tag_ids] 标签 ID 列表，多个标签 ID 用英文逗号隔开，为空的话则不对标签做限制
 * @apiParam {String} [room_ids] 多个直播间房号，多个直播间房号用英文逗号隔开，为空的话则不对直播间做限制
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "保存成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionAddEvent(c *handler.Context) (handler.ActionResponse, error) {
	var param liveEventParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if err = param.checkEvent(); err != nil {
		return nil, err
	}

	// 上传
	targetPath, err := UploadImage(param.urlFilter, storage.PathPrefixEvent)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	oldTargetPath, err := UploadImage(param.oldCoverURLFilter, storage.PathPrefixEvent)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	m := liverecommendedelements.Event{
		Sort:      param.Sort,
		ShowClose: param.ShowClose,
		Attribute: liverecommendedelements.Attribute{
			Name:      param.Name,
			Cover:     targetPath,
			URL:       param.URL,
			StartTime: &param.StartTime,
		},
		ExpireTime: param.ExpireTime,
	}

	// 处理 ExtendedFields
	ossOldTargetPath, _ := service.Storage.Format(oldTargetPath)
	extends := liverecommendedelements.EventExtendedFields{
		OldCover: ossOldTargetPath, // 存储老活动图标
	}
	if param.SetExtra == SetExtraAllowList {
		extends.CatalogIDs = param.catalogIDList
		extends.TagIDs = param.tagIDList
		extends.RoomIDs = param.roomIDList
	}
	v, err := json.Marshal(extends)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	m.ExtendedFields = string(v)
	if err = m.Save(service.DB); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	liverecommendedelements.ClearEventCache()
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("添加直播间活动图标, 活动名称：%s, 开始时间: %d, 结束时间: %d",
		param.Name, param.StartTime, param.ExpireTime)
	box.AddAdminLog(intro, userapi.CatalogRecommendedLiveEventAdd)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "保存成功", nil
}

func (param *liveEventParam) checkEvent() error {
	if len(param.Covers) == 0 || param.URL == "" || param.Name == "" || param.Sort <= 0 || len(param.OldCovers) == 0 {
		return actionerrors.ErrParams
	}
	var err error
	param.urlFilter, err = getURLFilter(param.Covers)
	if err != nil {
		return err
	}
	param.oldCoverURLFilter, err = getURLFilter(param.OldCovers)
	if err != nil {
		return err
	}

	r := new(liverecommendedelements.Model)
	err = liverecommendedelements.TableEvent(service.DB).
		Where("sort > ?", liverecommendedelements.SortDeleted).
		Where("expire_time > ?", goutil.TimeNow().Unix()).
		Where("name = ? OR url = ?", param.Name, param.URL).
		First(r).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r.ID != 0 {
		return actionerrors.ErrParamsMsg("该活动已存在，请检查后重新提交")
	}
	if param.SetExtra == NoExtra {
		return nil
	}
	if param.CatalogIDs == "" && param.TagIDs == "" && param.RoomIDs == "" {
		return actionerrors.ErrParamsMsg("必须配置活动图标的展示范围")
	}
	getArrayAndDuplicates := func(ids string, idContent string) ([]int64, error) {
		array, err := util.SplitToInt64Array(ids, ",")
		if err != nil {
			msg := fmt.Sprintf("%s名单格式错误", idContent)
			return nil, actionerrors.ErrParamsMsg(msg)
		}
		list := util.FindInt64Duplicates(array)
		if len(list) != 0 {
			msg := fmt.Sprintf("有重复的%s ID，重复 ID 为 %s", idContent, goutil.JoinInt64Array(list, ","))
			return nil, actionerrors.ErrParamsMsg(msg)
		}

		return array, nil
	}
	if param.catalogIDList, err = getArrayAndDuplicates(param.CatalogIDs, "分区"); err != nil {
		return err
	}
	if param.tagIDList, err = getArrayAndDuplicates(param.TagIDs, "标签"); err != nil {
		return err
	}
	if param.roomIDList, err = getArrayAndDuplicates(param.RoomIDs, "直播间"); err != nil {
		return err
	}

	rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": param.roomIDList}})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != len(param.roomIDList) {
		m := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Room)
		list := make([]int64, 0, util.Abs(int64(len(rooms)-len(param.roomIDList))))
		for _, r := range param.roomIDList {
			if _, ok := m[r]; !ok {
				list = append(list, r)
			}
		}
		return actionerrors.ErrParamsMsg(fmt.Sprintf("有不存在的直播间 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(list, ",")))
	}
	var notExistIDList []int64
	for _, v := range param.catalogIDList {
		if !catalog.LiveCatalogExists(v) {
			notExistIDList = append(notExistIDList, v)
		}
	}
	if len(notExistIDList) != 0 {
		msg := fmt.Sprintf("有不存在的分区 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(notExistIDList, ","))
		return actionerrors.ErrParamsMsg(msg)
	}
	for _, v := range param.tagIDList {
		t, err := tag.FindTagByID(v, tag.TypeLiveTag)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if t == nil || t.Status == tag.StatusHide {
			notExistIDList = append(notExistIDList, v)
		}
	}
	if len(notExistIDList) != 0 {
		msg := fmt.Sprintf("有不存在的标签 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(notExistIDList, ","))
		return actionerrors.ErrParamsMsg(msg)
	}
	return nil
}

func getURLFilter(covers []upload.SourceURL) (*liveupload.ImageURLFilter, error) {
	urlStrings := make([]upload.SourceURL, 0, len(covers))
	for i := range covers {
		if ok := storage.CheckStorage(covers[i]); !ok {
			return nil, actionerrors.ErrParamsMsg("请求上传的资源不被支持，请重新选择")
		}
		urlStrings = append(urlStrings, upload.SourceURL(covers[i]))
	}

	urlFilter, err := liveupload.NewImageURLFilter(urlStrings)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(err.Error())
	}
	return urlFilter, nil
}

// ActionDelEvent 删除直播间活动图标
/**
 * @api {post} /api/v2/admin/recommended/delevent 删除直播间活动图标
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 直播间活动图标 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionDelEvent(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil || input.ID <= 0 {
		return nil, actionerrors.ErrParams
	}
	element, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementEvent)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	liverecommendedelements.ClearEventCache()
	intro := fmt.Sprintf("删除直播间活动图标, 活动名称: %s", element.Name)
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	box.AddWithChannelID(userapi.CatalogRecommendedLiveEventDel, input.ID, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功", nil
}
