package recommended

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMedalFrameAddParam_checkParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	timeNow := goutil.TimeNow().Unix()
	timeBefore := timeNow - 1
	timeAfter := timeNow + 1

	existElement := liverecommendedelements.LiveRecommendedElements{
		Sort:        1,
		ElementID:   100100,
		ElementType: liverecommendedelements.ElementMedalFrame,
		URL:         "oss://live/medalframes/existed/level${level}_0_9_0_54.png",
		StartTime:   &timeNow,
		ExpireTime:  timeAfter,
	}
	require.NoError(service.DB.Delete(liverecommendedelements.LiveRecommendedElements{}, "element_id = ?", existElement.ElementID).Error)
	require.NoError(service.DB.Create(&existElement).Error)

	tests := []struct {
		name        string
		param       *medalFrameAddParam
		expectedErr error
	}{
		{
			name:        "房间号为空",
			param:       &medalFrameAddParam{DirName: "uuid dir", StartTime: timeNow, ExpireTime: timeAfter},
			expectedErr: actionerrors.ErrParamsMsg("请输入房间 ID"),
		},
		{
			name:        "不存在的勋章类型",
			param:       &medalFrameAddParam{RoomID: 1111, Type: 2, DirName: "dir", StartTime: timeNow, ExpireTime: timeAfter},
			expectedErr: actionerrors.ErrParamsMsg("请选择正确的勋章类型"),
		},
		{
			name:        "文件夹名为空",
			param:       &medalFrameAddParam{RoomID: 1111, DirName: "", StartTime: timeNow, ExpireTime: timeAfter},
			expectedErr: actionerrors.ErrParamsMsg("请输入文件夹名"),
		},
		{
			name:        "输入时间有误",
			param:       &medalFrameAddParam{RoomID: 1111, DirName: "dir", StartTime: timeNow, ExpireTime: timeBefore},
			expectedErr: actionerrors.ErrParamsMsg("输入时间有误"),
		},
		{
			name:        "房间号存在",
			param:       &medalFrameAddParam{RoomID: 18113499, DirName: "uuid dir", StartTime: timeNow, ExpireTime: timeAfter},
			expectedErr: nil,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.param.checkParams()
			assert.Equal(tc.expectedErr, err)
		})
	}
}

func TestMedalFrameAddParam_checkDir(t *testing.T) {
	testCases := []struct {
		name           string
		param          *medalFrameAddParam
		expectedErrNil bool
	}{
		{
			name: "静态勋章文件夹",
			param: &medalFrameAddParam{
				Type: 0, DirName: "20c653e1160f40d8a784f0a1b375f283",
			},
			expectedErrNil: true,
		},
		{
			name: "动态勋章文件夹",
			param: &medalFrameAddParam{
				Type: 1, DirName: "941ffb3f848c4b259305e9d0d39cfe0e",
			},
			expectedErrNil: true,
		},
		{
			name: "不存在的文件夹",
			param: &medalFrameAddParam{
				Type: 0, DirName: "1111",
			},
			expectedErrNil: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.param.checkDir()
			if tc.expectedErrNil && err != nil {
				t.Fatalf("expected error is nil, got: %v", err)
			}
			if !tc.expectedErrNil && err == nil {
				t.Fatal("expected error is not nil, got nil")
			}
		})
	}
}

func TestActionMedalFrameAdd(t *testing.T) {
	testCases := []struct {
		name        string
		params      handler.M
		expectedErr error
		expectedCnt int
	}{{
		name: "请求缺少参数",
		params: handler.M{
			"room_id": 0,
			"type":    0,
		},
		expectedErr: actionerrors.ErrParamsMsg("请输入房间 ID"),
	}, {
		name: "房间号不存在",
		params: handler.M{
			"room_id":     111,
			"type":        0,
			"dir_name":    "placeholder",
			"start_time":  1,
			"expire_time": 10,
		},
		expectedErr: actionerrors.ErrCannotFindRoom,
	}, {
		name: "弹窗信息",
		params: handler.M{
			"room_id":     18113499,
			"type":        0,
			"dir_name":    "20c653e1160f40d8a784f0a1b375f283",
			"start_time":  1,
			"expire_time": 10,
			"confirm":     0,
		},
		expectedErr: actionerrors.ErrConfirmRequired(
			"确认为该主播添加专属粉丝勋章吗？<br>房间号：18113499<br>主播昵称：零月", 1, true),
	},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/medalframe/add", true, tc.params)
			_, _, err := ActionMedalFrameAdd(c)
			if !assert.Equal(t, tc.expectedErr, err) {
				t.Fatalf("expected error is: %v, got: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestActionMedalFrameDel(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRecommendedID := int64(101010011001)
	err := service.DB.Delete(&liverecommendedelements.LiveRecommendedElements{}, "id = ?", testRecommendedID).Error
	require.NoError(err)
	err = service.DB.Create(&liverecommendedelements.LiveRecommendedElements{
		ID:          testRecommendedID,
		Sort:        1,
		ElementID:   18113499,
		ElementType: liverecommendedelements.ElementMedalFrame,
	}).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/medalframe/del", true, handler.M{
		"id":      testRecommendedID,
		"confirm": 0,
	})
	_, _, err = ActionMedalFrameDel(c)
	require.Equal("确认删除该主播的专属粉丝勋章吗？<br>房间号：18113499<br>主播昵称：零月", err.(*handler.ActionError).Info["msg"])

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/medalframe/del", true, handler.M{
		"id":      testRecommendedID,
		"confirm": 1,
	})
	_, resp, err := ActionMedalFrameDel(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/medalframe/del", true, handler.M{
		"id":      testRecommendedID,
		"confirm": 1,
	})
	_, _, err = ActionMedalFrameDel(c)
	assert.EqualError(err, "粉丝勋章 ID 输入有误")
}
