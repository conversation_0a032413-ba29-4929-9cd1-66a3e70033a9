package recommended

import (
	"fmt"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const halfHour = 30 * util.SecondOneMinute

type liveSchedule struct {
	ID              int64                          `json:"id"`
	Sort            int                            `json:"sort"` // 推荐值
	RoomID          int64                          `json:"room_id"`
	StartTime       int64                          `json:"start_time"`
	ExpireTime      int64                          `json:"expire_time"`
	Tag             int                            `json:"tag"`  // 待弃用
	Name            string                         `json:"name"` // 用户设置的房间名，对应 mongodb rooms collection 中的 name 字段
	Status          liverecommendedelements.Status `json:"status"`
	CreatorUsername string                         `json:"creator_username"` // 主播昵称
	CoverURL        string                         `json:"cover_url"`
}

func newLiveSchedule(m liverecommendedelements.Model) *liveSchedule {
	return &liveSchedule{
		ID:         m.ID,
		Sort:       m.Sort,
		RoomID:     m.ElementID,
		StartTime:  *m.StartTime,
		ExpireTime: m.ExpireTime,
		Tag:        liverecommendedelements.AttrToTag(m.Attr),
	}
}

type liveSchedules struct {
	Time int64           `json:"time"`
	Data []*liveSchedule `json:"data"`
}

// ActionListSchedule 获取 "直播推荐" 排期
/**
 * @api {get} /api/v2/admin/recommended/listschedule 获取 "直播推荐" 排期
 * @apiVersion 0.1.0
 * @apiDescription 获取某一天的全部排期
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} [date] 例如 "2006-01-02"，不传默认是今天
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "time": 1584806400,
 *         "data": [
 *           {
 *             "id": 10232,
 *             "sort": 2,
 *             "room_id": 22489473,
 *             "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *             "start_time": 1585152000,
 *             "expire_time": 1584808200,
 *             "tag": 1, // 0: 不显示 tag 1: 显示艺人 tag 2: 显示原外站主播 tag 3: 显示神话推荐 tag
 *             "name": "233333333", // 用户设置的房间名称
 *             "status": {
 *               "open": 0
 *             },
 *             "creator_username": "bless"
 *           },
 *           ...
 *         ]
 *       },
 *       {
 *         "time": 1584808200,
 *         "data": []
 *       },
 *       ...
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionListSchedule(c *handler.Context) (handler.ActionResponse, error) {
	day, _ := c.GetParamString("date")
	var bod time.Time
	if day != "" {
		var err error
		bod, err = time.ParseInLocation(util.TimeFormatYMD, day, time.Local)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
	} else {
		bod = goutil.BeginningOfDay(goutil.TimeNow())
	}
	eod := bod.AddDate(0, 0, 1)
	schedules, err := liverecommendedelements.ListScheduleDuration(bod, eod)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 每半小时一个时间段，返回一天 48 个时间段的数据
	resp := make([]liveSchedules, 0, 48)
	for st := bod; st.Before(eod); st = st.Add(30 * time.Minute) {
		resp = append(resp, liveSchedules{Time: st.Unix(), Data: []*liveSchedule{}})
	}
	list := make([]*liveSchedule, 0, len(schedules))
	for i := range schedules {
		s := newLiveSchedule(schedules[i])
		list = append(list, s)
		for j := range resp {
			// 同一条推荐可能在多个时间段出现
			if resp[j].Time >= s.ExpireTime {
				break
			}
			if resp[j].Time >= s.StartTime {
				resp[j].Data = append(resp[j].Data, s)
			}
		}
	}
	err = addExtraInfo(list)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func addExtraInfo(list []*liveSchedule) error {
	if len(list) == 0 {
		return nil
	}
	roomIDs := make([]int64, 0, len(list))
	for _, v := range list {
		// list 中的指针可能为空，比如 ActionAddSchedule 中，代表这条数据没有插入
		if v != nil {
			roomIDs = append(roomIDs, v.RoomID)
		}
	}
	if len(roomIDs) == 0 {
		return nil
	}
	rooms, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	roomMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Simple)
	for i := range list {
		// list 中的指针可能为空，比如 ActionAddSchedule 中，代表这条数据没有插入
		if list[i] == nil {
			continue
		}
		if r := roomMap[list[i].RoomID]; r != nil {
			list[i].Name = r.Name
			list[i].Status.Open = r.Status.Open
			list[i].CreatorUsername = r.CreatorUsername
			list[i].CoverURL = r.CoverURL
		}
	}
	return nil
}

// ActionListTopSchedule 获取当前的 "直播推荐" 排期
/**
 * @api {get} /api/v2/admin/recommended/listtopschedule 获取当前的 "直播推荐" 排期
 * @apiVersion 0.1.0
 * @apiDescription 获取当前时刻的 "直播推荐"
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "id": 3090,
 *         "sort": 2,
 *         "room_id": 22489473,
 *         "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *         "start_time": 1578844800,
 *         "expire_time": 1578846600,
 *         "tag": 1, // 0: 不显示 tag 1: 显示艺人 tag 2: 显示原外站主播 tag 3: 显示神话推荐 tag 4: 显示官签主播 tag
 *         "name": "2223333",
 *         "status": {
 *           "open": 0
 *         },
 *         "creator_username": "bless"
 *       }
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionListTopSchedule(c *handler.Context) (handler.ActionResponse, error) {
	models, err := liverecommendedelements.ListSchedule(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	list := make([]*liveSchedule, 0, len(models))
	for _, m := range models {
		s := newLiveSchedule(m)
		list = append(list, s)
	}
	err = addExtraInfo(list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

type itemRecommended struct {
	RoomID   int64            `form:"room_id" json:"room_id"`
	Sort     int              `form:"sort" json:"sort"`
	CoverURL upload.SourceURL `form:"cover_url" json:"cover_url"`
	Tag      int              `form:"tag" json:"tag"`

	coverURL string
}

func (item itemRecommended) checkRoomID() error {
	if item.RoomID <= 0 {
		return actionerrors.ErrParams
	}
	r, err := room.Find(item.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrNotFound(fmt.Sprintf("房间 %d 不存在", item.RoomID))
	}
	if r.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}
	return nil
}

func (item *itemRecommended) checkThenUpload() (err error) {
	if item.CoverURL == "" {
		return
	}
	if ok := storage.CheckStorage(item.CoverURL); ok {
		item.coverURL, err = storage.UploadToOSS(item.CoverURL, storage.PathPrefixSchedule)
		if err != nil {
			return err
		}
	} else {
		// TODO: 兼容旧的 HTTP 地址，后续移除
		item.coverURL, _ = service.Storage.Format(item.CoverURL.String())
	}
	return
}

type timeInterval struct {
	StartTime  int64 `json:"start_time"`
	ExpireTime int64 `json:"expire_time"`
}

type paramAddSchedule struct {
	itemRecommended
	Intervals []timeInterval `json:"intervals"`
}

// ActionAddSchedule 添加推荐
/**
 * @api {post} /api/v2/admin/recommended/addschedule 添加推荐
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} sort 推荐值，非零，小的值优先
 * @apiParam {String} [cover_url] 封面图链接地址
 * @apiParam {String} top_name 房间名称
 * @apiParam {number=0,1,2,4} tag 推荐类型 // 0: 正常推荐 1: 艺人推荐 2: 原外站艺人 4: 官签主播
 * @apiParam {Object[]} intervals 排期的时间区间
 * @apiParam {Number} intervals.start_time 排期的开始时间
 * @apiParam {Number} intervals.expire_time 排期的结束时间
 * @apiParamExample {json} Request-Example:
 *     {
 *       "room_id": 22489473,
 *       "sort": 1,
 *       "tag": 1,
 *       "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *       "intervals": [
 *         {
 *           "start_time": 1579017600,
 *         },
 *         {
 *           "start_time": 1579019400,
 *         },
 *       ],
 *     }
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "id": 3090,
 *           "sort": 2,
 *           "room_id": 22489473,
 *           "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *           "start_time": 1578844800,
 *           "expire_time": 1578846600,
 *           "tag": 1,
 *           "name": "2223333",
 *           "status": {
 *             "open": 0
 *           },
 *           "creator_username": "bless"
 *         },
 *         null, // 某一时间段 room_id 重复或者已达数量上线导致添加失败时，对应返回 null
 *       ]
 *     }
 */
func ActionAddSchedule(c *handler.Context) (handler.ActionResponse, error) {
	var param paramAddSchedule
	err := c.Bind(&param)
	if err != nil || param.Sort == 0 {
		return nil, actionerrors.ErrParams
	}
	// 不支持直接设置神话推荐的 tag
	if param.Tag == liverecommendedelements.AttrTagNoble ||
		param.Tag < liverecommendedelements.AttrTagNormal || param.Tag >= liverecommendedelements.AttrTagLimit {
		return nil, actionerrors.ErrParams
	}
	err = param.checkRoomID()
	if err != nil {
		return nil, err
	}

	minStartTime := minAllowedStartTime()
	for i, v := range param.Intervals {
		if v.StartTime < minStartTime {
			return nil, actionerrors.ErrParamsMsg("存在过期时间段")
		}
		if v.StartTime%halfHour != 0 {
			return nil, actionerrors.ErrParams
		}
		param.Intervals[i].ExpireTime = v.StartTime + halfHour
	}
	err = param.checkThenUpload()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	box := goclient.NewAdminLogBox(c)
	m := make([]liverecommendedelements.Model, len(param.Intervals))
	result := make([]*liveSchedule, len(m))
	for i, v := range param.Intervals {
		m[i].Sort = param.Sort
		m[i].ElementID = param.RoomID
		m[i].ElementType = liverecommendedelements.ElementSchedule
		m[i].Cover = param.coverURL
		m[i].StartTime = &v.StartTime
		m[i].ExpireTime = v.ExpireTime
		// m 的 attr 代表 tag
		m[i].Attr = util.NewBitMaskFromFlag(param.Tag)
		// 允许部分数据成功
		var count int64
		err = liverecommendedelements.TableSchedule(service.DB).
			Where("sort > ?", liverecommendedelements.SortDeleted).
			Where("start_time = ?", v.StartTime).Count(&count).Error
		if err != nil {
			logger.Errorf("Failed to insert: %v", err)
			continue
		}
		if count >= liverecommendedelements.MaxTopRoomCount {
			continue
		}
		dbStatus := liverecommendedelements.TableSchedule(service.DB).
			Where("sort > ?", liverecommendedelements.SortDeleted).
			Where("start_time = ? AND element_id = ?", v.StartTime, param.RoomID).
			FirstOrCreate(&m[i])
		if err := dbStatus.Error; err != nil {
			logger.Errorf("Failed to insert: %v", err)
			continue
		}
		if dbStatus.RowsAffected == 0 {
			continue
		}
		result[i] = newLiveSchedule(m[i])
		intro := fmt.Sprintf("添加“推荐直播”推荐, 房间号: %d, 推荐 ID: %d, 推荐时间: %s 至 %s", param.RoomID, m[i].ID,
			time.Unix(*m[i].StartTime, 0).Format(goutil.TimeFormatHMS),
			time.Unix(m[i].ExpireTime, 0).Format(goutil.TimeFormatHMS))
		box.Add(userapi.CatalogTopAdd, intro)
	}
	err = addExtraInfo(result)
	if err != nil {
		return nil, err
	}
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return result, nil
}

func minAllowedStartTime() int64 {
	return goutil.TimeNow().Unix() - halfHour
}

type paramEditSchedule struct {
	ID int64 `form:"id" json:"id"`
	itemRecommended
}

// ActionEditSchedule 编辑推荐
/**
 * @api {post} /api/v2/admin/recommended/editschedule 编辑推荐
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录的 ID
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} sort 推荐值，非零，小的值优先
 * @apiParam {number=0,1,2,4} tag 推荐类型 // 0: 正常推荐 1: 艺人推荐 2: 原外站艺人 4: 官签主播
 * @apiParam {String} [cover_url] 封面图链接地址
 * @apiParam {String} top_name 后台设置的房间名，已弃用
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "保存成功"
 *   }
 */
func ActionEditSchedule(c *handler.Context) (handler.ActionResponse, error) {
	var param paramEditSchedule
	err := c.Bind(&param)
	if err != nil || param.Sort == 0 {
		return nil, actionerrors.ErrParams
	}
	err = param.checkRoomID()
	if err != nil {
		return nil, err
	}
	var m liverecommendedelements.Model
	err = liverecommendedelements.TableSchedule(service.DB).Where("id = ?", param.ID).Take(&m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrParamsMsg("记录的 ID 不存在")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if m.StartTime == nil {
		return nil, actionerrors.ErrNotFound("数据错误")
	}
	var id int64
	err = liverecommendedelements.TableSchedule(service.DB).Select("id").Limit(1).
		Where("sort > ?", liverecommendedelements.SortDeleted).
		Where("start_time = ?", m.StartTime).
		Where("element_id = ?", param.RoomID).
		Where("id <> ?", param.ID).Row().Scan(&id)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err == nil {
		return nil, actionerrors.ErrParamsMsg("无法更新，检测到房间 ID 已存在")
	}
	// TODO: 仅支持图片上传后的服务器地址
	err = param.checkThenUpload()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.coverURL != "" {
		m.Cover = param.coverURL
	}

	paramAttr := util.NewBitMaskFromFlag(param.Tag)
	if m.Attr.IsSet(liverecommendedelements.AttrTagNoble) {
		if m.ElementID != param.RoomID {
			return nil, actionerrors.ErrParamsMsg("不可修改神话推荐房间号")
		}
		if m.Attr != paramAttr {
			return nil, actionerrors.ErrParamsMsg("不能推荐此类型")
		}
	}

	m.Sort = param.Sort
	m.ElementID = param.RoomID
	m.Attr = paramAttr

	// NOTE: 使用 gorm.Update(s) 必须使用 .Model(&m), 才会调用 m.BeforeUpdate
	// TODO: 放到 models/ 下
	updates := map[string]interface{}{
		"sort":       m.Sort,
		"element_id": m.ElementID,
		"attr":       m.Attr,
		"cover":      m.Cover,
	}

	db := liverecommendedelements.TableSchedule(service.DB).Model(&m).
		Where("id = ?", param.ID).
		Where("expire_time > ?", goutil.TimeNow().Unix()).
		Updates(updates)
	if err := db.Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if db.RowsAffected == 0 {
		return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "更新失败，请检查推荐是否过期或不存在")
	}
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("修改“推荐直播”推荐，房间号：%d, 推荐 ID: %d", param.RoomID, param.ID)
	box.Add(userapi.CatalogTopEdit, intro)
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "保存成功", nil
}

// ActionDelSchedule 删除推荐
/**
 * @api {post} /api/v2/admin/recommended/delschedule 删除推荐
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录的 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "取消推荐成功"
 *   }
 */
func ActionDelSchedule(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `json:"id"`
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	var attr goutil.BitMask
	err = liverecommendedelements.TableSchedule(service.DB).Select("attr").Where("id = ?", input.ID).Row().Scan(&attr)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrParamsMsg("找不到要取消的推荐！")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if attr.IsSet(liverecommendedelements.AttrTagNoble) {
		return nil, actionerrors.ErrParamsMsg("无法取消神话推荐！")
	}

	err = deleteSchedule(input.ID)
	if err != nil {
		return nil, err
	}
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("删除“推荐直播”推荐，推荐 ID: %d", input.ID)
	box.AddAdminLog(intro, userapi.CatalogTopRemove)
	if err = box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "取消推荐成功", nil
}

func deleteSchedule(id int64) error {
	nowUnix := goutil.TimeNow().Unix()
	db := liverecommendedelements.TableSchedule(service.DB).Where("id = ?", id).
		Where("expire_time > ?", nowUnix).Updates(
		map[string]interface{}{
			"modified_time": nowUnix,
			"sort":          liverecommendedelements.SortDeleted,
		})
	if err := db.Error; err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if db.RowsAffected == 0 {
		return actionerrors.NewUnknownError(http.StatusBadRequest, "删除失败，请检查推荐是否过期或不存在")
	}
	return nil
}
