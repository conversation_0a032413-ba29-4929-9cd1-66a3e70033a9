package recommended

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBackpackTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(liveBackground{}, "id", "sort", "element_id", "element_type", "url", "start_time", "expire_time", "create_time", "modified_time")
}

func TestActionAddAndDeleteBackground(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		existsRoomID    = int64(18113499)
		existsRoomIDStr = strconv.FormatInt(existsRoomID, 10)

		now   = goutil.TimeNow()
		param = handler.M{
			"room_ids":    existsRoomIDStr,
			"urls":        []string{"https://fm.example.com/testdata/test.webp", "https://fm.example.com/testdata/test.png"},
			"opacity":     0.55,
			"start_time":  now.Unix(),
			"expire_time": now.Add(time.Minute).Unix(),
		}
	)

	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionAddBackground(c)
	require.NoError(err)
	m := new(liverecommendedelements.Model)
	err = service.DB.Where("element_id = ? AND start_time = ?", existsRoomID, param["start_time"]).First(&m).Error
	require.NoError(err)
	assert.Equal(1, m.Sort)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": m.ID})
	_, err = ActionDelBackground(c)
	require.NoError(err)
	m = new(liverecommendedelements.Model)
	err = service.DB.Where("element_id = ? AND start_time = ?", existsRoomID, param["start_time"]).First(&m).Error
	require.NoError(err)
	assert.Zero(m.Sort)
}
