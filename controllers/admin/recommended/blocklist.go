package recommended

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// ActionBlockListRoomAdd 添加推荐位直播间黑名单
/**
 * @api {post} /api/v2/admin/recommended/blocklist/room/add 添加推荐位直播间黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {String} reason 原因
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "添加成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionBlockListRoomAdd(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		CreatorID int64  `form:"creator_id" json:"creator_id" binding:"gt=0"`
		Reason    string `form:"reason" json:"reason" binding:"required"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	r, err := room.FindOne(bson.M{"creator_id": params.CreatorID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrParamsMsg("主播不存在，请检查后重新输入")
	}
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrParamsMsg("当前主播已在黑名单中")
	}
	err = guildrecommendblocklist.Add(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	guildID, err := livecontract.GetGuildID(params.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var intro string
	if guildID == 0 {
		intro = fmt.Sprintf("添加推荐位黑名单, 直播间 ID: %d, 所属公会 ID: %d, 原因: %s", r.RoomID, r.GuildID, params.Reason)
	} else {
		intro = fmt.Sprintf("添加推荐位黑名单, 直播间 ID: %d, 原因: %s", r.RoomID, params.Reason)
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogRecommendRoomsBlockListManage, intro, goclient.AdminLogOptions{
		ChannelID: &r.RoomID,
	})
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "添加成功", nil
}

// ActionBlockListRoomRemove 删除推荐位直播间黑名单
/**
 * @api {post} /api/v2/admin/recommended/blocklist/room/remove 删除推荐位直播间黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} creator_id 主播 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "移除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionBlockListRoomRemove(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		CreatorID int64 `form:"creator_id" json:"creator_id" binding:"gt=0"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"creator_id": params.CreatorID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrParamsMsg("主播不存在，请检查后重新输入")
	}
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrParamsMsg("当前主播不在黑名单中")
	}
	err = guildrecommendblocklist.Remove(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	guildID, err := livecontract.GetGuildID(params.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var intro string
	if guildID != 0 {
		intro = fmt.Sprintf("删除推荐位黑名单, 直播间 ID: %d, 所属公会 ID: %d", r.RoomID, r.GuildID)
	} else {
		intro = fmt.Sprintf("删除推荐位黑名单, 直播间 ID: %d", r.RoomID)
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogRecommendRoomsBlockListManage, intro, goclient.AdminLogOptions{
		ChannelID: &r.RoomID,
	})
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "移除成功", nil
}

// ActionBlockListGuildAdd 添加推荐位公会黑名单
/**
 * @api {post} /api/v2/admin/recommended/blocklist/guild/add 添加推荐位公会黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} guild_id 公会 ID
 * @apiParam {String} reason 原因
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "添加成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionBlockListGuildAdd(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID int64  `form:"guild_id" json:"guild_id" binding:"gt=0"`
		Reason  string `form:"reason" json:"reason" binding:"required"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	g, err := guild.Find(params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg("公会不存在，请检查后重新输入")
	}
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendGuild, params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrParamsMsg("当前公会已在黑名单中")
	}
	err = guildrecommendblocklist.Add(guildrecommendblocklist.TypeElementRecommendGuild, params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	intro := fmt.Sprintf("添加推荐位黑名单, 公会 ID: %d, 原因: %s", params.GuildID, params.Reason)
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogRecommendRoomsBlockListManage, intro, goclient.AdminLogOptions{
		ChannelID: &params.GuildID,
	})
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "添加成功", nil
}

// ActionBlockListGuildRemove 删除推荐位公会黑名单
/**
 * @api {post} /api/v2/admin/recommended/blocklist/guild/remove 删除推荐位公会黑名单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} guild_id 公会 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "移除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionBlockListGuildRemove(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		GuildID int64 `form:"guild_id" json:"guild_id" binding:"gt=0"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	g, err := guild.Find(params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg("公会不存在，请检查后重新输入")
	}
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendGuild, params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrParamsMsg("当前公会不在黑名单中")
	}
	err = guildrecommendblocklist.Remove(guildrecommendblocklist.TypeElementRecommendGuild, params.GuildID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	intro := fmt.Sprintf("删除推荐位黑名单, 公会 ID: %d", params.GuildID)
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogRecommendRoomsBlockListManage, intro, goclient.AdminLogOptions{
		ChannelID: &params.GuildID,
	})
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "移除成功", nil
}
