package recommended

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type paramSquareSetHot struct {
	CatalogID  int64 `form:"catalog_id" json:"catalog_id"`
	RoomID     int64 `form:"room_id" json:"room_id"`
	Position   int   `form:"position" json:"position"`
	StartTime  int64 `form:"start_time" json:"start_time"`
	ExpireTime int64 `form:"expire_time" json:"expire_time"`

	catalogName     string
	recommendedName string
}

func (p *paramSquareSetHot) check() error {
	if p.RoomID == 0 || p.ExpireTime <= p.StartTime || p.CatalogID == 0 {
		return actionerrors.ErrParams
	}
	if p.Position <= 0 || p.Position > 20 {
		return actionerrors.ErrParamsMsg("推荐位置应该大于 0 且不超过 20")
	}

	r, err := room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}

	p.catalogName, p.recommendedName, err = squareCatalogNameByCatalogID(p.CatalogID)
	if err != nil {
		return err
	}
	conflictElements, err := liverecommendedelements.FindSquareElement(p.Position, p.recommendedName, p.StartTime, p.ExpireTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(conflictElements) > 0 {
		return actionerrors.ErrParamsMsg("时段冲突")
	}
	return nil
}

func squareCatalogNameByCatalogID(catalogID int64) (catalogName, recommendedName string, err error) {
	recommendedName = strconv.FormatInt(catalogID, 10)
	switch {
	case catalogID == liverecommendedelements.SquareTypeHot:
		catalogName = "热门"
	case catalogID == liverecommendedelements.SquareTypeNova:
		catalogName = "新星"
	case catalogID > 0:
		catalogMap, err := catalog.AllLiveCatalogsMap()
		if err != nil {
			return "", "", actionerrors.NewErrServerInternal(err, nil)
		}
		ca, ok := catalogMap[catalogID]
		if !ok {
			return "", "", actionerrors.ErrParamsMsg("没有找到你要的分区")
		}
		catalogName = ca.CatalogName
	default:
		err = actionerrors.ErrParamsMsg("推荐分区错误")
	}
	return
}

// ActionAddSquareHot 设置直播广场推荐直播间
/**
 * @api {post} /api/v2/admin/recommended/addsquarehot 设置直播广场推荐直播间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} catalog_id 分区 ID, 大于 0 是子分区 ID, -1: 热门，-2: 新星
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} position 位置, 值需要大于 0 且不超过 20
 * @apiParam {Number} start_time 开始时间, 如: 1578844800
 * @apiParam {Number} expire_time 结束时间, 如: 1578844800
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 */
func ActionAddSquareHot(c *handler.Context) (handler.ActionResponse, error) {
	var input paramSquareSetHot
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = input.check()
	if err != nil {
		return nil, err
	}

	extendedFields := liverecommendedelements.SquareExtendedFields{
		From: liverecommendedelements.SquareFromOperator,
	}
	extendedFieldsBytes, err := json.Marshal(extendedFields)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	m := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        input.Position,
		ElementID:   input.RoomID,
		Attribute: liverecommendedelements.Attribute{
			Name:      input.recommendedName,
			StartTime: &input.StartTime,
		},
		ExpireTime:     input.ExpireTime,
		ExtendedFields: string(extendedFieldsBytes),
	}
	if err = service.DB.Create(&m).Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置直播广场推荐直播间, 直播间 ID: %d, 分区: %s, 位置: %d", m.ElementID, input.catalogName, m.Sort)
	box.AddAdminLog(intro, userapi.CatalogRecommendedOpenListSet)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "设置成功", nil
}

// ActionDelSquareHot 取消置顶直播广场推荐直播间
/**
 * @api {post} /api/v2/admin/recommended/delsquarehot 取消置顶直播广场推荐直播间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "取消成功"
 *   }
 *
 * @apiError (404) {Number} code 501010004
 * @apiError (404) {String} info "ID 记录未找到"
 *
 */
func ActionDelSquareHot(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	element, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementSquare)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	var catalogID int64
	if element.Name != "" {
		catalogID, err = strconv.ParseInt(element.Name, 10, 64)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	catalogName, _, err := squareCatalogNameByCatalogID(catalogID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("取消直播广场推荐直播间, 直播间 ID: %d, 分区: %s", element.ElementID, catalogName)
	box.AddAdminLog(intro, userapi.CatalogRecommendedOpenListDel)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return "取消成功", nil
}
