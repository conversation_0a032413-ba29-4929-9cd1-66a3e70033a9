package recommended

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAddAndDeleteEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var e liveEventParam
	kcForm := tutil.NewKeyChecker(t, tutil.FORM)
	kcForm.Check(e, "name", "covers", "old_covers", "url", "sort", "show_close", "start_time", "expire_time",
		"set_extra", "catalog_ids", "tag_ids", "room_ids")

	kcJSON := tutil.NewKeyChecker(t, tutil.JSON)
	kcJSON.Check(e, "name", "covers", "old_covers", "url", "sort", "show_close", "start_time", "expire_time",
		"set_extra", "catalog_ids", "tag_ids", "room_ids")

	now := goutil.TimeNow()
	nowUnix := now.Unix()
	param := liveEventParam{
		Name:       fmt.Sprintf("event-%d", nowUnix),
		URL:        fmt.Sprintf("https://test.missevan.com/event%d", nowUnix),
		Sort:       1,
		ShowClose:  true,
		StartTime:  now.Unix(),
		ExpireTime: now.Add(time.Minute).Unix(),
	}
	clearTestData := func(name string) {
		require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "name = ?", name).Error)
	}
	// 清空数据
	defer clearTestData(param.Name)

	// 超量传入
	webpCover := upload.SourceURL("https://fm.example.com/testdata/test.webp")
	param.Covers = []upload.SourceURL{webpCover, webpCover, webpCover}
	param.OldCovers = []upload.SourceURL{webpCover, webpCover, webpCover}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionAddEvent(c)
	assert.EqualError(err, liveupload.ErrAVIFImageType.Error())

	// 测试错误的传入
	param.Covers = []upload.SourceURL{
		upload.SourceURL("https://fm.example.com/testdata/test.webp"),
		upload.SourceURL("https://fm.example.com/testdata/test.gif"),
	}
	param.OldCovers = []upload.SourceURL{
		upload.SourceURL("https://fm.example.com/testdata/test.webp"),
		upload.SourceURL("https://fm.example.com/testdata/test.gif"),
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddEvent(c)
	assert.EqualError(err, liveupload.ErrImageType.Error())

	// 不支持的文件类型上传
	param.Covers = []upload.SourceURL{
		upload.SourceURL("oss://test.webp"),
		upload.SourceURL("oss://test.gif"),
	}
	param.OldCovers = []upload.SourceURL{
		upload.SourceURL("oss://test.webp"),
		upload.SourceURL("oss://test.gif"),
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddEvent(c)
	assert.EqualError(err, "请求上传的资源不被支持，请重新选择")

	param.Covers = []upload.SourceURL{
		upload.SourceURL("https://fm.example.com/testdata/test.webp"),
		upload.SourceURL("https://fm.example.com/testdata/test.png"),
	}
	param.OldCovers = []upload.SourceURL{
		upload.SourceURL("https://fm.example.com/testdata/test.webp"),
		upload.SourceURL("https://fm.example.com/testdata/test.png"),
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddEvent(c)
	require.NoError(err)
	m := new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("name = ? AND start_time = ?", param.Name, param.StartTime).First(&m).Error)
	assert.Equal(1, m.Sort)
	// 重复插入
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddEvent(c)
	assert.EqualError(err, "该活动已存在，请检查后重新提交")

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": m.ID})
	_, err = ActionDelEvent(c)
	require.NoError(err)
	m = new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("name = ? AND start_time = ?", param.Name, param.StartTime).First(&m).Error)
	assert.Zero(m.Sort)
	nowUnix = goutil.TimeNow().Unix()
	param = liveEventParam{
		Name:       fmt.Sprintf("event-%d", nowUnix),
		Covers:     []upload.SourceURL{upload.SourceURL("https://fm.example.com/testdata/test.png")},
		OldCovers:  []upload.SourceURL{upload.SourceURL("https://fm.example.com/testdata/test_old.png")},
		URL:        fmt.Sprintf("https://test.missevan.com/event%d", nowUnix),
		Sort:       1,
		ShowClose:  true,
		StartTime:  now.Unix(),
		ExpireTime: now.Add(time.Minute).Unix(),
		SetExtra:   SetExtraAllowList,
		CatalogIDs: "104",
		TagIDs:     "1,2",
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	// 清空数据
	defer clearTestData(param.Name)
	_, err = ActionAddEvent(c)
	require.NoError(err)
}

func TestCheckEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	nowUnix := now.Unix()
	param := &liveEventParam{
		Name:       fmt.Sprintf("event-%d", nowUnix),
		Covers:     []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
		OldCovers:  []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
		URL:        fmt.Sprintf("https://test.missevan.com/event%d", nowUnix),
		Sort:       1,
		ShowClose:  true,
		StartTime:  now.Unix(),
		ExpireTime: now.Add(time.Minute).Unix(),
		SetExtra:   1,
	}
	assert.EqualError(param.checkEvent(), "必须配置活动图标的展示范围")
	param.RoomIDs = "123，456"
	assert.EqualError(param.checkEvent(), "直播间名单格式错误")
	param.RoomIDs = "123,456,123,789"
	assert.EqualError(param.checkEvent(), "有重复的直播间 ID，重复 ID 为 123")
	param.RoomIDs = "22334,123,22335"
	assert.EqualError(param.checkEvent(), "有不存在的直播间 ID，不存在的 ID 为 123")
	param.RoomIDs = "22334,22335"
	param.CatalogIDs = "1,104,2"
	assert.EqualError(param.checkEvent(), "有不存在的分区 ID，不存在的 ID 为 1,2")
	param.CatalogIDs = "104"
	param.TagIDs = "1,88"
	assert.EqualError(param.checkEvent(), "有不存在的标签 ID，不存在的 ID 为 88")
	param.TagIDs = "1"
	assert.Nil(param.checkEvent())
	param.SetExtra = SetExtraAllowList
	assert.Nil(param.checkEvent())
	param.SetExtra = NoExtra
	m := liverecommendedelements.Event{
		Sort: param.Sort,
		Attribute: liverecommendedelements.Attribute{
			Name: param.Name,
		},
		ExpireTime: now.Add(time.Minute).Unix(),
	}
	require.NoError(m.Save(service.DB))
	assert.EqualError(param.checkEvent(), "该活动已存在，请检查后重新提交")
	service.DB.Table(liverecommendedelements.TableName()).Where("expire_time = ?", m.ExpireTime).Delete("")
}
