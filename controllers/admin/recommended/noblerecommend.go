package recommended

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	sortStartTime      = "start_time"
	sortStartTimeDesc  = "start_time.desc"
	sortCreateTime     = "create_time"
	sortCreateTimeDesc = "create_time.desc"
)

var sortRecommendMap = map[string]string{
	sortStartTime:      livenoblerecommend.StartTimeAsc,
	sortStartTimeDesc:  livenoblerecommend.StartTimeDesc,
	sortCreateTime:     livenoblerecommend.CreateTimeAsc,
	sortCreateTimeDesc: livenoblerecommend.CreateTimeDesc,
}

type nobleRecommendListResp struct {
	Data       []*livenoblerecommend.WithUserInfo `json:"Datas"`
	Pagination goutil.Pagination                  `json:"pagination"`
}

// ActionListNobleRoom 贵族推荐直播列表
/**
 * @api {get} /api/v2/admin/recommended/listnobleroom 贵族推荐直播列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 * @apiParam {Number} [id] 推荐申请 ID
 * @apiParam {Number} [room_id] 房间 ID
 * @apiParam {String} [from_username] 推荐人昵称
 * @apiParam {String} [creator_username]  推荐的主播昵称
 * @apiParam {number=-1,0,1} [status=0] 推荐状态 -1: 已过期, 0: 未过期, 1: 全部
 * @apiParam {String} [sort=start_time.desc] 排序: start_time,start_time.desc,create_time,create_time.desc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "id": 12,
 *           "creator_username": "bless", // 主播昵称
 *           "creator_id": 10, // 主播 ID
 *           "from_username": "零月", // 推荐人昵称
 *           "from_user_id": 12, // 推荐人 ID
 *           "room_id": 12345, // 直播间 ID
 *           "start_time": 1234567890,
 *           "end_time": 1234567890,
 *           "create_time": 1234567890,
 *           "recommend_time": "1970-01-01 08:00 至 08:30"
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionListNobleRoom(c *handler.Context) (handler.ActionResponse, error) {
	opt := livenoblerecommend.FindOptions{}
	err := c.C.BindQuery(&opt)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	opt.P, opt.PageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	opt.Sort = sortRecommendMap[c.GetDefaultParamString("sort", sortCreateTimeDesc)]
	resp := new(nobleRecommendListResp)
	resp.Data, resp.Pagination, err = livenoblerecommend.FindRecommendList(opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}
