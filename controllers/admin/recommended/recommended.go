package recommended

import (
	"encoding/json"
	"fmt"
	"html"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gocarina/gocsv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/recommended"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/recommendedexposurelevel"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// ActionGet 获取“首页正在直播后台”的后台设置
/**
 * @api {get} /api/v2/admin/recommended/get 获取“首页正在直播后台”的后台设置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "position": 1,                                                   // 第几推荐位
 *         "room_id": 22489473,                                             // 房间号，为 0 时代表该推荐位未设置
 *         "name": "23333333",                                              // 直播标题
 *         "cover_url": "https://static.missevan.com/profile/01.png",       // 直播封面图
 *         "creator_username": "bless01",                                   // 主播昵称
 *         "creator_iconurl": "https://static.missevan.com/profile/01.png", // 主播头像
 *         "status": {
 *           "open": 1,                                                     // 是否正在直播
 *         }
 *       },
 *       {
 *         "position": 2,
 *         "room_id": 22489474,
 *         "name": "22222223",
 *         "cover_url": "https://static.missevan.com/profile/02.png",
 *         "creator_username": "bless02",
 *         "creator_iconurl": "https://static.missevan.com/profile/02.png",
 *         "status": {
 *           "open": 1,
 *         }
 *       },
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGet(*handler.Context) (handler.ActionResponse, error) {
	return recommended.ReadConfiguredRoom(3)
}

type reqSet struct {
	Position int    `json:"position"`
	RoomID   *int64 `json:"room_id"`

	room *room.Room
}

func (input *reqSet) load(c *handler.Context) error {
	err := c.BindJSON(input)
	if err != nil {
		return actionerrors.ErrParams
	}

	if input.RoomID == nil || *input.RoomID < 0 {
		return actionerrors.ErrParams
	}

	if input.Position < 1 || input.Position > 3 {
		return actionerrors.ErrParamsMsg("无效的推荐位")
	}

	return nil
}

func (input *reqSet) checkRoom() (err error) {
	input.room, err = room.Find(*input.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if input.room == nil {
		return actionerrors.ErrNotFound("没有找到你要的直播间哦…")
	}
	if input.room.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}

	input.room.SchemeToURL()
	return nil
}

// ActionSet 设置/清空推荐位
/**
 * @api {post} /api/v2/admin/recommended/set 设置/清空推荐位
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} position 位置：只有 1，2，3
 * @apiParam {Number} room_id 房间号：0 代表清空 position 推荐位
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "清空成功"
 *   }
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "保存成功"
 *   }
 */
func ActionSet(c *handler.Context) (handler.ActionResponse, error) {
	var input reqSet
	err := input.load(c)
	if err != nil {
		return nil, err
	}

	if input.RoomID == nil {
		return nil, actionerrors.ErrParams
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	if *input.RoomID == 0 {
		roomList, err := liverecommendedelements.FindRoomByPosition(input.Position)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		roomIDList := make([]int64, len(roomList))
		for i, v := range roomList {
			roomIDList[i] = v.ID
		}
		err = liverecommendedelements.ClearRoom(input.Position)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		intro := fmt.Sprintf("清空推荐位，推荐位：%d，被清空的房间列表：%d", input.Position, roomIDList)
		box.AddAdminLog(intro, userapi.CatalogRecommendedLiveRecommendClear)
		err = box.Send()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return "清空成功", nil
	}

	err = input.checkRoom()
	if err != nil {
		return nil, err
	}

	err = liverecommendedelements.SaveRoom(service.DB, input.Position, *input.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	intro := fmt.Sprintf("设置推荐位，推荐位：%d，房间 ID：%d", input.Position, *input.RoomID)
	box.AddAdminLog(intro, userapi.CatalogRecommendedLiveRecommendSet)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "保存成功", nil
}

const (
	recommendedTypeLiveTab = iota + 1
	recommendedTypeSearch
)

type positionImportParam struct {
	CSVURL  upload.SourceURL `form:"csv_url" json:"csv_url"`
	Type    int              `form:"type" json:"type"`
	Confirm int              `form:"confirm" json:"confirm"` // 确认次数, 首次请求传 0

	imports []*recommendCSVItem
}

type recommendCSVItem struct {
	RoomID       int64  `csv:"房间 ID"`
	Position     int    `csv:"推荐位置"`
	StartTimeStr string `csv:"开始时间"` // 格式: 2024-12-30 12:00:00
	EndTimeStr   string `csv:"结束时间"` // 格式: 2024-12-30 12:00:00

	startTime int64 // 单位：秒级时间戳
	endTime   int64 // 单位：秒级时间戳
}

// ActionPositionImport 导入底导和搜索直播推荐位配置
/**
 * @api {post} /api/v2/admin/recommended/position/import 导入底导和搜索直播推荐位配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} csv_url csv 地址
 * @apiParam {number=1,2} type 配置类型 1：底导推荐，2：搜索推荐
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "导入成功"
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认新增 2 条配置吗？"
 *     }
 *   }
 */
func ActionPositionImport(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newPositionImportParam(c)
	if err != nil {
		return nil, "", err
	}
	if err = param.insert(); err != nil {
		return nil, "", err
	}
	param.addAdminLog(c)
	return nil, "导入成功", nil
}

func newPositionImportParam(c *handler.Context) (*positionImportParam, error) {
	var param positionImportParam
	err := c.Bind(&param)
	if err != nil || param.CSVURL == "" ||
		!slices.Contains([]int{recommendedTypeLiveTab, recommendedTypeSearch}, param.Type) {
		return nil, actionerrors.ErrParams
	}

	if err = param.parseCSV(); err != nil {
		return nil, err
	}

	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认新增 %d 条配置吗？", len(param.imports))
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	return &param, nil
}

func (param *positionImportParam) parseCSV() error {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	skipBOMFile, err := csv.SkipBOM(file)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if err := gocsv.Unmarshal(skipBOMFile, &param.imports); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.imports) == 0 {
		return actionerrors.ErrParamsMsg("CSV 不可为空")
	}

	roomIDs := make([]int64, 0, len(param.imports))
	for _, v := range param.imports {
		roomIDs = append(roomIDs, v.RoomID)
		prefixMsg := fmt.Sprintf("房间 %d ", v.RoomID)
		startTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.StartTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "开始时间格式错误")
		}
		v.startTime = startTime.Unix()
		endTime, err := time.ParseInLocation(goutil.TimeFormatHMS, strings.TrimSpace(v.EndTimeStr), time.Local)
		if err != nil {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间格式错误")
		}
		v.endTime = endTime.Unix()
		if v.startTime >= v.endTime {
			return actionerrors.ErrParamsMsg(prefixMsg + "结束时间不能小于开始时间")
		}
		switch param.Type {
		case recommendedTypeLiveTab:
			if v.Position < 0 || v.Position > 8 {
				return actionerrors.ErrParamsMsg(prefixMsg + "推荐位置错误，位置范围 1-8")
			}
		case recommendedTypeSearch:
			if v.Position < 0 || v.Position > 3 {
				return actionerrors.ErrParamsMsg(prefixMsg + "推荐位置错误，位置范围 1-3")
			}
		default:
			panic("invalid type")
		}
	}
	// 存在同一个直播间配置不同时间段，允许存在重复直播间
	ok, err := room.ExistsAll(sets.Uniq(roomIDs))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrParamsMsg("有不存在的房间")
	}
	return nil
}

func (param *positionImportParam) insert() error {
	var elementType int
	switch param.Type {
	case recommendedTypeLiveTab:
		elementType = liverecommendedelements.ElementLiveTabRecommend
	case recommendedTypeSearch:
		elementType = liverecommendedelements.ElementSearchRecommend
	default:
		panic("invalid elementType")
	}

	now := goutil.TimeNow().Unix()
	elements := make([]*liverecommendedelements.LiveRecommendedElements, 0, len(param.imports))
	for _, v := range param.imports {
		elements = append(elements, &liverecommendedelements.LiveRecommendedElements{
			Sort:         v.Position,
			ElementID:    v.RoomID,
			ElementType:  elementType,
			StartTime:    goutil.NewInt64(v.startTime),
			ExpireTime:   v.endTime,
			CreateTime:   now,
			ModifiedTime: now,
		})
	}
	err := servicedb.SplitBatchInsert(service.DB, liverecommendedelements.LiveRecommendedElements{}.TableName(), elements, 1000, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *positionImportParam) addAdminLog(c goutil.UserContext) {
	var elementTypeMsg string
	switch param.Type {
	case recommendedTypeLiveTab:
		elementTypeMsg = "底部导航直播推荐"
	case recommendedTypeSearch:
		elementTypeMsg = "搜索页直播推荐"
	default:
		panic("invalid type")
	}
	msg := fmt.Sprintf("%s 新增 %d 条数据", elementTypeMsg, len(param.imports))
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageLiveRecommend, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type positionDelParam struct {
	ID      int64 `form:"id" json:"id"`
	Confirm int   `form:"confirm" json:"confirm"`
}

// ActionPositionDel 删除底导和搜索直播推荐位配置
/**
 * @api {post} /api/v2/admin/recommended/position/del 删除底导和搜索直播推荐位配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id 配置 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "确认删除推荐直播间固定位置<br>直播间 ID: 3013063<br>主播昵称: 1234<br>固定位置: 1<br>删除后设置将不再生效"
 *     }
 *   }
 */
func ActionPositionDel(c *handler.Context) (handler.ActionResponse, string, error) {
	var param positionDelParam
	err := c.Bind(&param)
	if err != nil || param.ID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	var element liverecommendedelements.LiveRecommendedElements
	err = service.DB.Table(liverecommendedelements.LiveRecommendedElements{}.TableName()).
		Take(&element, "id = ? AND sort > ? AND element_type IN (?)",
			param.ID, liverecommendedelements.SortDeleted, []int{liverecommendedelements.ElementLiveTabRecommend, liverecommendedelements.ElementSearchRecommend}).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, "", actionerrors.ErrNotFound("请输入正确的 ID")
		}
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow().Unix()
	if now >= element.ExpireTime {
		return nil, "", actionerrors.NewErrForbidden("该设置已下线，无法删除")
	}

	if param.Confirm == 0 {
		var username string
		l, err := live.FindLiveByRoomID(element.ElementID)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if l != nil {
			username = l.Username
		} else {
			logger.WithField("room_id", element.ElementID).Error("live not found")
			// PASS
		}
		msg := fmt.Sprintf("确认删除推荐直播间固定位置<br>直播间 ID: %d<br>主播昵称: %s<br>固定位置: %d<br>删除后设置将不再生效",
			element.ElementID, html.EscapeString(username), element.Sort)
		return nil, "", actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	db := service.DB.Table(liverecommendedelements.TableName()).
		Where("id = ? AND sort > ?", element.ID, liverecommendedelements.SortDeleted).
		Updates(map[string]any{
			"sort":          liverecommendedelements.SortDeleted,
			"modified_time": now,
		})
	if db.Error != nil {
		return nil, "", actionerrors.NewErrServerInternal(db.Error, nil)
	}
	if db.RowsAffected == 0 {
		return nil, "", actionerrors.ErrNotFound("删除失败，请刷新列表后重试")
	}

	param.addAdminLog(c, element)
	return nil, "删除成功", nil
}

func (param *positionDelParam) addAdminLog(c goutil.UserContext, element liverecommendedelements.LiveRecommendedElements) {
	var elementTypeMsg string
	switch element.ElementType {
	case liverecommendedelements.ElementLiveTabRecommend:
		elementTypeMsg = "底部导航直播推荐"
	case liverecommendedelements.ElementSearchRecommend:
		elementTypeMsg = "搜索页直播推荐"
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageLiveRecommend, fmt.Sprintf("删除%s数据, ID: %d; 房间号: %d; 位置: %d", elementTypeMsg, element.ID, element.ElementID, element.Sort))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type paramAlgorithmExposureAdd struct {
	CatalogID       int64 `form:"catalog_id" json:"catalog_id"`
	RoomID          int64 `form:"room_id" json:"room_id"`
	ExposureLevelID int64 `form:"exposure_level_id" json:"exposure_level_id"`
	StartTime       int64 `form:"start_time" json:"start_time"`
	ExpireTime      int64 `form:"expire_time" json:"expire_time"`

	catalogName     string
	recommendedName string
	exposureLevel   *recommendedexposurelevel.Model
}

// ActionAlgorithmExposureAdd 添加直播页直播列表第 4 个直播间推荐算法曝光干预
/**
 * @api {post} /api/v2/admin/recommended/algorithm-exposure/add 添加直播页直播列表第 4 个直播间推荐算法曝光干预
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} catalog_id 分区 ID, 大于 0 是子分区 ID, -1: 热门，-2: 新星
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始时间（单位：秒）, 如: 1578844800
 * @apiParam {Number} expire_time 结束时间（单位：秒）, 如: 1578844800
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "设置成功",
 *     "data": null
 *   }
 */
func ActionAlgorithmExposureAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	var input paramAlgorithmExposureAdd
	err := c.Bind(&input)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}

	err = input.check()
	if err != nil {
		return nil, "", err
	}
	extends := liverecommendedelements.AlgorithmExposureExtendedFields{
		ExposureLevelID: input.ExposureLevelID,
	}
	v, err := json.Marshal(extends)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	m := liverecommendedelements.Model{
		Sort:        4,
		ElementID:   input.RoomID,
		ElementType: liverecommendedelements.ElementAlgorithmExposure,
		Attribute: liverecommendedelements.Attribute{
			Name:      input.recommendedName,
			StartTime: &input.StartTime,
		},
		ExpireTime:     input.ExpireTime,
		ExtendedFields: string(v),
	}
	if err = service.DB.Create(&m).Error; err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置直播页直播列表第 4 个直播间推荐算法曝光干预, 直播间 ID: %d, 分区: %s, 曝光等级 ID: %d, 曝光等级：%s",
		m.ElementID, input.catalogName, input.ExposureLevelID, input.exposureLevel.Level)
	box.Add(userapi.CatalogManageAlgorithmExposure, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil, "设置成功", nil
}

func (p *paramAlgorithmExposureAdd) check() error {
	if p.RoomID <= 0 || p.ExpireTime <= p.StartTime || p.CatalogID == 0 {
		return actionerrors.ErrParams
	}

	r, err := room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}

	p.catalogName, p.recommendedName, err = squareCatalogNameByCatalogID(p.CatalogID)
	if err != nil {
		return err
	}
	conflictElements, err := liverecommendedelements.FindAlgorithmExposureElement(p.recommendedName, p.RoomID, p.StartTime, p.ExpireTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(conflictElements) > 0 {
		return actionerrors.ErrParamsMsg("时段冲突")
	}

	p.exposureLevel, err = recommendedexposurelevel.FindEnabledByID(p.ExposureLevelID, recommendedexposurelevel.SceneLive)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.exposureLevel == nil {
		return actionerrors.ErrParamsMsg("曝光等级不存在")
	}

	return nil
}

// ActionAlgorithmExposureDel 删除直播页直播列表第 4 个直播间算法曝光干预
/**
 * @api {post} /api/v2/admin/recommended/algorithm-exposure/del 删除直播页直播列表第 4 个直播间算法曝光干预
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
func ActionAlgorithmExposureDel(c *handler.Context) (handler.ActionResponse, string, error) {
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil || input.ID <= 0 {
		return nil, "", actionerrors.ErrParams
	}

	element, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementAlgorithmExposure)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, "", actionerrors.ErrCannotFindResource
	}
	var catalogName string
	if element.Name != "" {
		catalogID, err := strconv.ParseInt(element.Name, 10, 64)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		catalogName, _, err = squareCatalogNameByCatalogID(catalogID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	box := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("取消直播页直播列表第 4 个直播间推荐算法曝光干预, 记录 ID: %d, 直播间 ID: %d, 分区: %s", input.ID, element.ElementID, catalogName)
	box.Add(userapi.CatalogManageAlgorithmExposure, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil, "取消成功", nil
}
