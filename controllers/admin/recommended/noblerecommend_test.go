package recommended

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionNobleRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	n := livenoblerecommend.NobleRecommend{ID: 2, FromUserID: 12, CreatorID: 10, RoomID: 12345,
		StartTime: 0, EndTime: livenoblerecommend.EndTime(0)}
	require.NoError(service.DB.Table(livenoblerecommend.TableName()).
		FirstOrCreate(&n).Error)
	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/api/v2/admin/recommended/listnobleroom?from_user_id=12&room_id=12345&status=1", nil)
	r, err := ActionListNobleRoom(c)
	require.NoError(err)
	resp := r.(*nobleRecommendListResp)
	assert.Equal(n.CreatorID, resp.Data[0].CreatorID)
	assert.Equal("bless", resp.Data[0].CreatorUsername)

	c.C.Request, _ = http.NewRequest("GET", fmt.Sprintf("/api/v2/admin/recommended/listnobleroom?id=%d&status=-1", resp.Data[0].ID), nil)
	r, err = ActionListNobleRoom(c)
	require.NoError(err)
	resp = r.(*nobleRecommendListResp)
	assert.Len(resp.Data, 1)
	assert.Equal(n.CreatorID, resp.Data[0].CreatorID)
	assert.Equal(n.FromUserID, resp.Data[0].FromUserID)

	c.C.Request, _ = http.NewRequest("GET", "/api/v2/admin/recommended/listnobleroom?room_id=12345678", nil)
	r, err = ActionListNobleRoom(c)
	require.NoError(err)
	resp = r.(*nobleRecommendListResp)
	assert.Len(resp.Data, 0)

	c.C.Request, _ = http.NewRequest("GET", "/api/v2/admin/recommended/listnobleroom?status=err", nil)
	_, err = ActionListNobleRoom(c)
	assert.Equal(actionerrors.ErrParams, err)
}
