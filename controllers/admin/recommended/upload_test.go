package recommended

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUploadImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter, err := liveupload.NewImageURLFilter([]upload.SourceURL{"https://fm.example.com/testdata/test.webp", "https://fm.example.com/testdata/test.png"})
	require.NoError(err)
	str := filter.Primary.NewTargetPath("prefix")
	str2, err := UploadImage(filter, "prefix")
	require.NoError(err)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+str.String(), str2)

	filter, err = liveupload.NewImageURLFilter([]upload.SourceURL{"https://fm.example.com/testdata/test.png"})
	require.NoError(err)
	str = filter.Primary.NewTargetPath("prefix")
	str2, err = UploadImage(filter, "prefix")
	require.NoError(err)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+str.String(), str2)
}

func TestSourceURL_NewTargetPathWithSuffix(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	filter, err := liveupload.NewImageURLFilter([]upload.SourceURL{"https://fm.example.com/testdata/test.webp", "https://fm.example.com/testdata/test.png"})
	require.NoError(err)
	path := liveupload.NewTargetPathWithSuffix(filter.Primary, "prefix", "1_1_1_1")
	assert.Equal("prefix200001/01/2c145cdb5c7695288ac63e704424b9fa_1_1_1_1.webp", path.String())
}

func TestUploadImageWithSuffix(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	filter, err := liveupload.NewImageURLFilter([]upload.SourceURL{"https://fm.example.com/testdata/test.webp", "https://fm.example.com/testdata/test.png"})
	require.NoError(err)
	url, err := UploadImageWithSuffix(filter, storage.PathPrefixCreatorcard, "1_1_1_1")
	require.NoError(err)
	assert.Equal("https://static-test.missevan.com/live/creatorcard/200001/01/2c145cdb5c7695288ac63e704424b9fa_1_1_1_1.webp", url)
}
