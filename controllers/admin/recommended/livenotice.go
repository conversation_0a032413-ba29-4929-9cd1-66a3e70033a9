package recommended

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type liveNoticeInfo struct {
	Limit    int    `form:"limit" json:"limit"`
	Title    string `form:"title" json:"title,omitempty"`
	BubbleID int64  `form:"bubble_id" json:"bubble_id,omitempty"`
}

type paramSetLiveNotice struct {
	RoomIDs string `form:"room_ids" json:"room_ids"`
	liveNoticeInfo

	c          *handler.Context
	listRoomID []int64
}

// ActionSetLiveNotice 设置直播开播全站通知
/**
 * @api {post} /api/v2/admin/recommended/setlivenotice 设置直播开播全站通知
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} room_ids 房间 IDs e.g. "1,2,3" // 半角逗号分割
 * @apiParam {Number} limit 通知条数
 * @apiParam {String} [title] 主播称号
 * @apiParam {Number} [bubble_id] 气泡 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010500, 100010002
 * @apiError (500) {String} info 服务器内部错误, 数据库错误
 */
func ActionSetLiveNotice(c *handler.Context) (handler.ActionResponse, error) {
	// 校验参数
	param := paramSetLiveNotice{c: c}
	err := param.load()
	if err != nil {
		return nil, err
	}
	// 保存数据
	err = param.save()
	if err != nil {
		return nil, err
	}
	// 管理员操作日志
	param.sendAdminLog()

	return "success", nil
}

func (p *paramSetLiveNotice) load() error {
	err := p.c.Bind(&p)
	if err != nil {
		return actionerrors.ErrParams
	}
	if p.RoomIDs == "" {
		return actionerrors.ErrParamsMsg("请输入房间 ID")
	}
	// TODO: 检查逻辑整合到 model 中
	roomIDs := strings.Split(p.RoomIDs, ",")
	listRoomID := make([]int64, 0, len(roomIDs))
	for _, id := range roomIDs {
		roomID, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			return actionerrors.ErrParams
		}
		listRoomID = append(listRoomID, roomID)
	}
	p.listRoomID = listRoomID
	rooms, err := room.ListSimples(bson.M{
		"room_id": bson.M{"$in": p.listRoomID},
		"limit":   bson.M{"$exists": false},
	}, nil,
		&room.FindOptions{Projection: bson.M{"room_id": 1}, DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	exists := make(map[int64]struct{}, len(rooms))
	for _, r := range rooms {
		exists[r.RoomID] = struct{}{}
	}
	errRoomIDs := make([]string, 0, len(roomIDs))
	for i, roomID := range listRoomID {
		if _, ok := exists[roomID]; !ok {
			errRoomIDs = append(errRoomIDs, roomIDs[i])
		}
	}
	if len(errRoomIDs) > 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("房间 %s 不存在", strings.Join(errRoomIDs, ",")))
	}

	if p.BubbleID != 0 {
		bubble, err := bubble.FindOne(p.BubbleID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if bubble == nil {
			return actionerrors.ErrNotFound("气泡不存在")
		}
	}

	return nil
}

func (p *paramSetLiveNotice) save() error {
	redisParam := make(map[string]interface{}, len(p.listRoomID))
	delFields := make([]string, 0, len(p.listRoomID))
	for _, roomID := range p.listRoomID {
		info, err := json.Marshal(p.liveNoticeInfo)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		redisParam[keys.FieldRewardRoom1.Format(roomID)] = info
		delFields = append(delFields, keys.FieldRewardUsedCounter1.Format(roomID))
	}

	key := keys.KeyRoomsBroadcastOpen0.Format()
	pipe := service.Redis.TxPipeline()
	pipe.HMSet(key, redisParam)
	pipe.HDel(key, delFields...)
	_, err := pipe.Exec()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (p *paramSetLiveNotice) sendAdminLog() {
	adminLogs := userapi.NewAdminLogBox(p.c)
	intro := fmt.Sprintf("设置直播开播全站通知, 直播间 IDs: %s, 通知条数: %d", p.RoomIDs, p.Limit)
	if p.Title != "" {
		intro += fmt.Sprintf(", 称号: %s", p.Title)
	}
	if p.BubbleID != 0 {
		intro += fmt.Sprintf(", 气泡 ID: %d", p.BubbleID)
	}
	adminLogs.AddAdminLog(intro,
		userapi.SetLiveNotice)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type liveNotice struct {
	RoomID    int64  `json:"room_id"`
	CreatorID int64  `json:"creator_id"`
	Username  string `json:"username"`

	liveNoticeInfo
}

type respListLiveNotice struct {
	Data       []*liveNotice     `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionListLiveNotice 设置直播开播全站通知记录
/**
 * @api {get} /api/v2/admin/recommended/listlivenotice 设置直播开播全站通知记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "creator_id": 10, // 主播 ID
 *           "username": "主播昵称",
 *           "title": "主播称号",
 *           "room_id": 10, // 直播间 ID
 *           "limit": 5 // 剩余次数
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionListLiveNotice(c *handler.Context) (handler.ActionResponse, error) {
	// WORKAROUND: HScan 参数 count 存在不确定性, 暂全部取出, 后续会迁移到 mysql
	result := hscanAll(0, nil)

	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	count := int64(len(result))
	pa := goutil.MakePagination(count, p, pageSize)
	resp := respListLiveNotice{Pagination: pa, Data: make([]*liveNotice, 0)}
	if !pa.Valid() {
		return &resp, nil
	}
	resp.Data = result[pa.Offset():min(pa.Offset()+pa.PageSize, count)]

	roomIDs := make([]int64, len(resp.Data))
	for i, r := range resp.Data {
		roomIDs[i] = r.RoomID
	}
	roomSimple, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil,
		&room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomMap := make(map[int64]*room.Simple, len(roomSimple))
	for _, r := range roomSimple {
		roomMap[r.RoomID] = r
	}

	for _, r := range resp.Data {
		if room, ok := roomMap[r.RoomID]; ok {
			r.Username = room.CreatorUsername
			r.CreatorID = room.CreatorID
		}
	}
	return &resp, nil
}

// TODO: 后续支持传入函数参数去处理具体业务逻辑, 变成通用函数
func hscanAll(cur uint64, res []*liveNotice) []*liveNotice {
	key := keys.KeyRoomsBroadcastOpen0.Format()
	rs, cur, err := service.Redis.HScan(key, cur, "reward_*", 50).Result()
	if err != nil {
		logger.Error(err)
		return res
	}
	// REVIEW: 后面试下 value 拼接成 JSON 数组 [{},{}] 之后再一起 Unmarshal
	for i := 0; i < len(rs)-1; i += 2 {
		var ln liveNotice
		ln.RoomID = keys.ParseRewardRoomID(rs[i])
		err := json.Unmarshal([]byte(rs[i+1]), &ln)
		if err != nil {
			logger.Error(err)
			continue
		}
		res = append(res, &ln)
	}

	if cur != 0 {
		hscanAll(cur, res)
	}
	return res
}
