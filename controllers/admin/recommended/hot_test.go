package recommended

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSquareTags(t *testing.T) {
	setHotKeys := []string{"catalog_id", "room_id", "position", "start_time", "expire_time"}
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(paramSquareSetHot{}, setHotKeys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(paramSquareSetHot{}, setHotKeys...)
}

func TestSquareCatalogName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cn, rn, err := squareCatalogNameByCatalogID(-1)
	require.NoError(err)
	assert.Equal("热门", cn)
	assert.Equal("-1", rn)

	cn, rn, err = squareCatalogNameByCatalogID(-2)
	require.NoError(err)
	assert.Equal("新星", cn)
	assert.Equal("-2", rn)

	cn, rn, err = squareCatalogNameByCatalogID(104)
	require.NoError(err)
	assert.Equal("音乐", cn)
	assert.Equal("104", rn)

	_, _, err = squareCatalogNameByCatalogID(10000)
	assert.EqualError(err, "没有找到你要的分区")

	_, _, err = squareCatalogNameByCatalogID(-3)
	assert.EqualError(err, "推荐分区错误")
}

func TestSetHotCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var p paramSquareSetHot
	assert.Equal(actionerrors.ErrParams, p.check(), "room_id 测试")
	p.RoomID = 1234
	assert.Equal(actionerrors.ErrParams, p.check(), "时间段测试")
	p.StartTime = goutil.TimeNow().Unix()
	p.ExpireTime = p.StartTime + 1000
	assert.Equal(actionerrors.ErrParams, p.check(), "分区测试")
	p.CatalogID = 104
	assert.EqualError(p.check(), "推荐位置应该大于 0 且不超过 20")
	p.Position = 13
	assert.Equal(actionerrors.ErrCannotFindRoom, p.check(), "房间找不到")
	p.RoomID = room.TestLimitedRoomID
	assert.Equal(actionerrors.ErrLimitedRoom, p.check(), "受限房间")
	p.RoomID = testRoomID
	require.NoError(p.check())
	assert.NotEmpty(p.catalogName)
	assert.NotEmpty(p.recommendedName)
	require.NoError(service.DB.Where("sort = ?", p.Position).Delete(liverecommendedelements.Model{}).Error)
	st := p.StartTime - 60
	r := liverecommendedelements.Model{
		ElementType: liverecommendedelements.ElementSquare,
		Sort:        p.Position,
		ElementID:   1,
		Attribute: liverecommendedelements.Attribute{
			Name:      p.recommendedName,
			StartTime: &st,
		},
		ExpireTime: p.ExpireTime,
	}
	require.NoError(service.DB.Create(&r).Error)
	assert.EqualError(p.check(), "时段冲突")
	require.NoError(service.DB.Where("sort = ?", p.Position).Delete(liverecommendedelements.Model{}).Error)
}

func TestSquareHot(t *testing.T) {
	require := require.New(t)

	roomID := int64(22489473)
	catalogID := int64(106)
	catalogIDStr := "106"
	now := goutil.TimeNow()
	input := paramSquareSetHot{
		RoomID:     roomID,
		CatalogID:  catalogID,
		Position:   1,
		StartTime:  now.Unix(),
		ExpireTime: now.Add(time.Minute).Unix(),
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, input)
	_, err := ActionAddSquareHot(c)
	require.NoError(err)

	// test list
	r, _, err := liverecommendedelements.ListSquareHotRecommended(1, 20)
	require.NoError(err)

	// test delete
	var id int64
	for i := range r {
		if r[i].ElementID == roomID && r[i].Name == catalogIDStr {
			id = r[i].ID
			break
		}
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": id})
	_, err = ActionDelSquareHot(c)
	require.NoError(err)

	// 清理过期数据
	service.DB.Table(liverecommendedelements.TableName()).
		Where("element_id = ? AND element_type = ? AND name = ?",
			roomID, liverecommendedelements.ElementSquare, catalogIDStr).Delete("")
}
