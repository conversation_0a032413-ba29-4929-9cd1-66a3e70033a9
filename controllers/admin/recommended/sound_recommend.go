package recommended

import (
	"fmt"
	"html"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type message struct {
	Message string `form:"message" json:"message"`
}
type paramMessage struct {
	Data         []message `form:"data" json:"data"`
	Confirm      int       `form:"confirm" json:"confirm"`
	rawList      []string
	filteredList []string
}

// ActionSoundMessageDelete 删除音频播放页随机推荐语
/**
 * @api {post} /api/v2/admin/recommended/sound/message/delete 删除音频播放页随机推荐语
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Object[]} data 包含要删除的随机推荐语列表，如 {"data":[{"message":"xxx"}]}
 * @apiParam {String} data.message 推荐语
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 删除成功:
 *   {
 *     "code": 0,
 *     "info": "删除成功！”
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要删除如下随机推荐语吗？<br/>“[1]”"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 *
 */
func ActionSoundMessageDelete(c *handler.Context) (handler.ActionResponse, error) {
	var param paramMessage
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.rawList = make([]string, 0, len(param.Data))
	for _, v := range param.Data {
		param.rawList = append(param.rawList, v.Message)
	}
	if err := param.checkDeleteParams(); err != nil {
		return nil, err
	}

	if param.Confirm == 0 {
		confirmMsg := `确定要删除如下随机推荐语吗？`
		for _, v := range param.rawList {
			confirmMsg += fmt.Sprintf(`<br/>“[%s]”`, html.EscapeString(v))
		}
		return nil, actionerrors.ErrConfirmRequired(confirmMsg, 1, true)
	}
	err = params.DeleteSoundMessage(param.rawList)
	if err != nil {
		return nil, err
	}
	intro := fmt.Sprintf("删除随机推荐语: %s", param.rawList)
	adminLog := goclient.NewAdminLogBox(c)
	adminLog.Add(userapi.CatalogManageSoundRecommend, intro)
	if err := adminLog.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功！", nil
}

func (p paramMessage) checkDeleteParams() error {
	if len(p.rawList) <= 0 {
		return actionerrors.ErrParams
	}
	return nil
}

// ActionSoundMessageAdd 新增音频播放页随机推荐语
/**
 * @api {post} /api/v2/admin/recommended/sound/message/add 新增音频播放页随机推荐语
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Object[]} data 包含要新增的随机推荐语列表，如 {"data":[{"message":"xxx"}]}
 * @apiParam {String} data.message 推荐语
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 提交成功:
 *   {
 *     "code": 0,
 *     "info": "提交成功！"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "本次新增如下 2 条推荐语（已自动删除 1 条重复内容）<br/>提交后将立即生效，确认要提交吗？<div style="background:white;color:black;">1. s6<br/>2. s7</div>"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 *
 */
func ActionSoundMessageAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param paramMessage
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if err := param.checkAddParams(); err != nil {
		return nil, err
	}
	param.filteredList = goutil.UniqString(param.rawList)
	listToAdd, err := param.messageListToInsert()
	if err != nil {
		return nil, err
	}
	if len(listToAdd) == 0 {
		return nil, actionerrors.ErrParamsMsg("提交内容与线上重复，请检查后重试")
	}
	conflictCount := len(param.rawList) - len(listToAdd)
	if param.Confirm == 0 {
		var confirmMsg string
		if conflictCount == 0 {
			confirmMsg = fmt.Sprintf(`本次新增如下 %d 条推荐语`, len(listToAdd))
		} else {
			confirmMsg = fmt.Sprintf(`本次新增如下 %d 条推荐语<br/>（已自动删除 %d 条重复或与线上冲突的内容）`,
				len(listToAdd), conflictCount)
		}
		confirmMsg += `<br/>提交后将立即生效，确认要提交吗？`
		confirmMsg += `<br/><div style="background:white;color:black;">`
		for k, v := range listToAdd {
			if k > 0 {
				confirmMsg += "<br/>"
			}
			confirmMsg += fmt.Sprintf(`%d. %s`, k+1, html.EscapeString(v))
		}
		confirmMsg += `</div>`
		return nil, actionerrors.ErrConfirmRequired(confirmMsg, 1, true)
	}
	err = params.AddSoundMessage(listToAdd)
	if err != nil {
		return nil, err
	}
	intro := fmt.Sprintf("新增随机推荐语: %s", listToAdd)
	adminLog := goclient.NewAdminLogBox(c)
	adminLog.Add(userapi.CatalogManageSoundRecommend, intro)
	if err := adminLog.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "提交成功！", nil
}

func (p paramMessage) messageListToInsert() ([]string, error) {
	r, err := params.FindSoundRecommend()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	existMessages := make(map[string]bool, len(r.Live.MessagePool))
	for _, v := range r.Live.MessagePool {
		existMessages[v] = true
	}
	l := make([]string, 0, len(p.filteredList))
	for _, v := range p.filteredList {
		if _, ok := existMessages[v]; !ok {
			l = append(l, v)
		}
	}
	return l, nil
}

func (p *paramMessage) checkAddParams() error {
	if len(p.Data) <= 0 {
		return actionerrors.ErrParams
	}
	if len(p.Data) > 20 {
		return actionerrors.ErrParamsMsg("一次最多新增 20 条！")
	}
	rawList := make([]string, 0, len(p.Data))
	for _, v := range p.Data {
		if v.Message == "" {
			return actionerrors.ErrParamsMsg("请先填写内容")
		}
		width := util.UTF8Width(v.Message)
		// 英文字母算半个字，汉字算 1 个字，大于 15 个字返回错误
		// 15 个字的 utf-8 字符的显示宽度为 30
		if width > 30 {
			return actionerrors.ErrParamsMsg("最多输入 15 字")
		}
		rawList = append(rawList, v.Message)
	}
	p.rawList = rawList
	return nil
}
