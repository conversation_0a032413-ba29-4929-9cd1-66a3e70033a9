package recommended

import (
	"encoding/json"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	var p livePopupParam
	kcForm := tutil.NewKeyChecker(t, tutil.FORM)
	kcForm.Check(p, "id", "event_id", "sort", "mini_url", "image_url", "fold_image_url", "full_url", "open_url",
		"start_time", "end_time", "set_extra", "catalog_ids", "room_ids", "tag_ids", "room_ids_csv_url")

	kcJSON := tutil.NewKeyChecker(t, tutil.JSON)
	kcJSON.Check(p, "id", "event_id", "sort", "mini_url", "image_url", "fold_image_url", "full_url", "open_url",
		"start_time", "end_time", "set_extra", "catalog_ids", "room_ids", "tag_ids", "room_ids_csv_url")
}

func TestActionAddAndDeletePopup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	param := handler.M{
		"event_id":       1999,
		"sort":           1,
		"image_url":      "https://fm.example.com/testdata/test.png",
		"mini_url":       "http://fm.example.com/mini?webview=1",
		"fold_image_url": "https://fm.example.com/testdata/test.png",
		"full_url":       "http://fm.example.com/full_url?webview=1",
		"start_time":     now.Add(time.Minute).Unix(),
		"end_time":       now.Add(time.Minute).Unix() + 100,
	}

	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionAddPopup(c)
	require.NoError(err)
	m := new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", param["event_id"], param["end_time"], 11).Take(&m).Error)

	p1 := handler.M{
		"event_id":       2121,
		"sort":           21,
		"image_url":      "https://fm.example.com/testdata/test.png",
		"mini_url":       "http://fm.example.com/mini21?webview=1",
		"fold_image_url": "https://fm.example.com/testdata/test.png",
		"full_url":       "http://fm.example.com/full_url21?webview=1",
		"start_time":     now.Add(time.Minute).Unix(),
		"end_time":       now.Add(time.Minute).Unix() + 100,
		"set_extra":      SetExtraAllowList,
		"catalog_ids":    "104,105,-1",
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, p1)
	_, err = ActionAddPopup(c)
	require.Equal(actionerrors.ErrParamsMsg("有不存在的分区 ID，不存在的 ID 为 -1"), err)
	p1["catalog_ids"] = "104,105"
	p1["room_ids"] = "4381915"
	p1["set_extra"] = 3
	c = handler.NewTestContext(http.MethodPost, "/", true, p1)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Delete("").Error)

	_, err = ActionAddPopup(c)
	assert.EqualError(err, "名单类型错误")

	p1["set_extra"] = SetExtraAllowList
	c = handler.NewTestContext(http.MethodPost, "/", true, p1)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Delete("").Error)

	_, err = ActionAddPopup(c)
	require.NoError(err)
	m1 := new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Take(&m1).Error)
	assert.Equal(`{"all_room_show":false,"catalog_ids":[104,105],"room_ids":[4381915]}`, m1.ExtendedFields)

	p1["set_extra"] = SetExtraBlockList
	c = handler.NewTestContext(http.MethodPost, "/", true, p1)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Delete("").Error)

	_, err = ActionAddPopup(c)
	require.NoError(err)
	m1 = new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Take(&m1).Error)
	assert.JSONEq(`{"all_room_show":false,"catalog_ids":[104,105],"room_ids":[4381915],"application_type":1}`, m1.ExtendedFields)

	p1["room_ids"] = "1,15,516,1003,2233,2234,2236,8888,10240,10243,10244,11000,22330,22331,22332,22333,22334,22335,122340,122341,122349,223344,223355,346286,369892,676768,676769,676770,3192516,4381915,5202465,8697456,9074511,11223344,18113499,18113899,20200923,20210609,20230918,22489473,24113499,61618635,63888614,65150486,100000005,100000006,100000007,100000009,100000010,100000013,100000014,100000015,100000016,114693474,141317526,170624474,172842330,173076071,186192636,343348035,347142108,347142109,789808782,999888777"
	c = handler.NewTestContext(http.MethodPost, "/", true, p1)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Delete("").Error)
	_, err = ActionAddPopup(c)
	require.NoError(err)
	m1 = new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", p1["event_id"], p1["end_time"], 11).Take(&m1).Error)
	var e liverecommendedelements.PopupShowConfig
	err = json.Unmarshal([]byte(m1.ExtendedFields), &e)
	require.NoError(err)
	assert.False(e.AllRoomShow)
	assert.Equal([]int64{104, 105}, e.CatalogIDs)
	assert.Empty(e.RoomIDs)
	assert.NotZero(e.RoomApplicationID)

	var a application.Application
	err = service.LiveDB.Take(&a, "id = ?", e.RoomApplicationID).Error
	require.NoError(err)
	assert.Equal(application.TypeBlockList, a.Type)
	assert.Equal(application.ElementTypeRoomID, a.ElementType)

	var count int
	err = service.LiveDB.Table(application.Element{}.TableName()).Where("application_id = ?", a.ID).Count(&count).Error
	require.NoError(err)
	assert.EqualValues(64, count)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": m.ID})
	_, err = ActionDelPopup(c)
	require.NoError(err)
	m = new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", param["event_id"], param["end_time"], 11).Take(&m).Error)
	assert.Zero(m.Sort)
	// 清空数据
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "element_id = ?", param["event_id"]).Error)

	// 测试 OpenUrl
	param["full_url"] = ""
	param["image_url"] = "https://fm.example.com/testdata/test.png"
	param["open_url"] = "http://fm.example.com/open_url?webview=1"
	param["end_time"] = now.Add(2 * time.Minute).Unix()
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddPopup(c)
	assert.NoError(err)
	m = new(liverecommendedelements.Model)
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ? AND element_type = ?", param["event_id"], param["end_time"], 11).Take(&m).Error)
	// 清空数据
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "element_id = ?", param["event_id"]).Error)

	// end_time 比 start_time 小
	param["end_time"] = now.Add(-time.Minute * 2)
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddPopup(c)
	assert.Equal(actionerrors.ErrParams, err)

	// open_url 和 full_url 互斥时
	param["full_url"] = "http://fm.example.com/full_url?webview=1"
	param["open_url"] = "http://fm.example.com/open_url?webview=1"
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionAddPopup(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 找不到对应资源错误
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": 1})
	_, err = ActionDelPopup(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	// ActionDelPopup 操作发生错误
	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"id": -1})
	_, err = ActionDelPopup(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestActionEditPopup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 数据库插入测试用数据
	param := livePopupParam{
		Sort:         1,
		StartTime:    goutil.TimeNow().Unix(),
		EndTime:      goutil.TimeNow().Unix() + 100,
		MiniURL:      "test_mini_url",
		ImageURL:     "test_image_url",
		FoldImageURL: "test_fold_image_url",
		FullURL:      "test_full_url",
	}
	require.NoError(param.buildParamURL())
	err := param.addPopup()
	require.NoError(err)
	defer func() {
		require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("",
			"cover = ? AND expire_time = ?", param.ImageURL, param.EndTime).Error)
	}()
	// 找到数据 ID
	var m liverecommendedelements.Model
	require.NoError(service.DB.Where("cover = ? AND expire_time = ?",
		param.ImageURL, param.EndTime).Take(&m).Error)
	param.ID = m.ID

	p := handler.M{
		"id":        m.ID,
		"sort":      2,
		"image_url": "https://fm.example.com/testdata/test.png",
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, p)
	resp, err := ActionEditPopup(c)
	require.NoError(err)
	assert.Equal("修改成功", resp)
	assert.NoError(err)
	require.NoError(service.DB.Where("id = ?", param.ID).Take(&m).Error)
	assert.Equal(p["sort"], m.Sort)
	assert.Contains(m.Cover, "https://static-test.missevan.com/"+storage.PathPrefixPopupImage)
}

func TestNewLivePopupParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{
		"id":       10010,
		"event_id": 20020,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	newParam, err := newLivePopupParam(c)
	require.NoError(err)
	assert.EqualValues(param["id"], newParam.ID)
	assert.EqualValues(param["event_id"], newParam.EventID)
}

func TestLivePopupParam_uploadImages(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := livePopupParam{
		ImageURL:     "https://fm.example.com/testdata/empty.a",
		FoldImageURL: "https://fm.example.com/testdata/test.a",
	}
	assert.EqualError(param.uploadImages(), "图片格式只支持 AVIF, WebP, PNG, GIF")

	param.ImageURL = "https://fm.example.com/testdata/test.png"
	assert.EqualError(param.uploadImages(), "图片格式只支持 AVIF, WebP, PNG, GIF")

	param.ImageURL = "https://fm.example.com/testdata/test.png"
	param.FoldImageURL = "https://fm.example.com/testdata/test.png"
	require.NoError(param.uploadImages())

	assert.Contains(param.FoldImageURL, "https://static-test.missevan.com/"+storage.PathPrefixPopupImage)
	assert.Contains(param.ImageURL, "https://static-test.missevan.com/"+storage.PathPrefixPopupImage)
}

func createFile(path, content string) (func() error, error) {
	file, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	cleanup := func() error {
		return os.Remove(path)
	}
	if err != nil {
		return cleanup, err
	}

	_, err = file.Write([]byte(content))
	_ = file.Close()
	if err != nil {
		return cleanup, err
	}
	return cleanup, nil
}

func TestLivePopupParam_parseCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanUp, err := createFile("../../../testdata/rooms.csv", "user_id\n114514\r\n1111\n\n2222\n")
	defer func() {
		require.NoError(cleanUp())
	}()
	require.NoError(err)

	p := livePopupParam{
		RoomIDsCSVURL: "https://fm.example.com/testdata/rooms.csv",
	}
	require.NoError(p.parseCSV())
	assert.Equal(3, len(p.roomIDs))
	assert.EqualValues(114514, p.roomIDs[0])

	_, err = createFile("../../../testdata/rooms.csv", "user_id\nabcd\n")
	require.NoError(err)
	assert.EqualError(p.parseCSV(), "CSV 数据格式错误，请检查后再试")
}

func TestCheckBeforeAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &livePopupParam{
		MiniURL:      "http://fm.example.com/mini_url",
		FoldImageURL: "https://fm.example.com/testdata/test.png",
		ImageURL:     "https://fm.example.com/testdata/test.png",
		FullURL:      " ",
		OpenURL:      " ",
		StartTime:    0,
		EndTime:      1,
	}
	assert.Equal(actionerrors.ErrParams, p.checkBeforeAdd())

	p = &livePopupParam{
		MiniURL:      "http://fm.example.com/mini_url",
		FoldImageURL: "https://fm.example.com/testdata/test.png",
		ImageURL:     "https://fm.example.com/testdata/test.png",
		FullURL:      "http://fm.example.com/full_url",
		StartTime:    0,
		EndTime:      1,
	}
	require.NoError(p.checkBeforeAdd())
	assert.Contains(p.url, p.MiniURL)
	assert.Contains(p.url, p.FullURL)
	assert.Contains(p.url, "https://static-test.missevan.com/"+storage.PathPrefixPopupImage)
}

func TestLivePopupParam_checkBeforeEdit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := livePopupParam{
		EventID:   114514,
		Sort:      1,
		StartTime: goutil.TimeNow().Unix(),
		EndTime:   goutil.TimeNow().Unix() + 100,
		FullURL:   "https://fm.example.com/full_url",
		OpenURL:   "https://fm.example.com/open_url",
		// open 和 full 只能有一个存在
	}
	// 没有 id
	err := param.checkBeforeEdit()
	assert.Equal(actionerrors.ErrParams, err)
	param.ID = 10010
	// open 和 full 同时存在
	err = param.checkBeforeEdit()
	assert.Equal(actionerrors.ErrParams, err)

	// 测试数据不存在
	param.FullURL = ""
	err = param.checkBeforeEdit()
	assert.Error(err)
	assert.EqualError(err, "活动小窗不存在，请检查后重新提交")
	// 添加数据
	err = param.addPopup()
	require.NoError(err)
	defer func() {
		require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("",
			"element_id = ? AND expire_time = ?", param.EventID, param.EndTime).Error)
	}()
	// 找到数据 ID
	var m liverecommendedelements.Model
	require.NoError(service.DB.Where("element_id = ? AND expire_time = ?",
		param.EventID, param.EndTime).Take(&m).Error)
	param.ID = m.ID
	// 测试数据正确
	param.model = &liverecommendedelements.Model{
		Attribute: liverecommendedelements.Attribute{
			URL: "test;test;test;",
		},
	}
	err = param.checkBeforeEdit()
	assert.NoError(err)

	// 测试时间参数
	param.FoldImageURL = ""
	param.StartTime = param.EndTime + 100
	err = param.checkBeforeEdit()
	assert.Equal(actionerrors.ErrParams, err)
	param.StartTime = 0
	err = param.checkBeforeEdit()
	assert.NoError(err)
	param.EndTime = 0
	err = param.checkBeforeEdit()
	assert.NoError(err)

	// 测试错误 SetExtraAllowList 参数
	param.SetExtra = 99
	err = param.checkBeforeEdit()
	assert.Equal(actionerrors.ErrParams, err)
}

func TestLivePopupParam_parseModelURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	model := liverecommendedelements.Model{
		Attribute: liverecommendedelements.Attribute{
			URL: "test1;test2;test3;",
		},
	}
	param1 := livePopupParam{
		MiniURL: "test4",
		model:   &model,
	}
	require.NoError(param1.parseModelURL())
	assert.Equal("test4;test2;test3;", param1.url)

	param1 = livePopupParam{
		OpenURL: "test5",
		model:   &model,
	}
	require.NoError(param1.parseModelURL())
	assert.Equal("test1;test2;test5;", param1.url)

	param1 = livePopupParam{
		FullURL: "test6",
		model:   &model,
	}
	require.NoError(param1.parseModelURL())
	assert.Equal("test1;test2;;test6", param1.url)

	// 原链接数量错误，生成新链接
	model.URL = "test1;test2"
	param1 = livePopupParam{
		MiniURL: "test4",
		model:   &model,
	}
	require.NoError(param1.parseModelURL())
	assert.Equal("test4;;;", param1.url)
}

func TestLivePopupParam_fillTimes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := livePopupParam{
		Sort:      1,
		ImageURL:  "test_image_url",
		StartTime: goutil.TimeNow().Unix(),
		EndTime:   goutil.TimeNow().Unix() + 100,
	}
	require.NoError(param.addPopup())
	defer func() {
		require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("",
			"cover = ? AND expire_time = ?", param.ImageURL, param.EndTime).Error)
	}()

	// 找到数据 ID
	var m liverecommendedelements.Model
	require.NoError(service.DB.Where("cover = ? AND expire_time = ?",
		param.ImageURL, param.EndTime).Take(&m).Error)
	p := livePopupParam{ID: m.ID}
	require.NoError(p.fillTimes())
	assert.Equal(param.StartTime, p.StartTime)
	assert.Equal(param.EndTime, p.EndTime)
	require.NoError(p.fillTimes())

	// 测试找不到数据
	p.ID = 191929294
	p.StartTime = 0
	assert.Equal(actionerrors.ErrCannotFindResource, p.fillTimes())
}

func TestLivePopupParam_editPopup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试数据库中找不到
	param := livePopupParam{}
	_, err := param.editPopup()
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	// 数据库插入测试用数据
	param = livePopupParam{
		Sort:         1,
		StartTime:    goutil.TimeNow().Unix() - 200,
		EndTime:      goutil.TimeNow().Unix() - 100,
		MiniURL:      "test_mini_url",
		ImageURL:     "test_image_url",
		FoldImageURL: "test_fold_image_url",
		FullURL:      "test_full_url",
	}
	err = param.addPopup()
	require.NoError(err)
	defer func() {
		require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("",
			"cover = ? AND expire_time = ?", param.ImageURL, param.EndTime).Error)
	}()
	// 找到数据 ID
	var m liverecommendedelements.Model
	require.NoError(service.DB.Where("cover = ? AND expire_time = ?",
		param.ImageURL, param.EndTime).Take(&m).Error)

	// 测试不修改任何数据
	param = livePopupParam{ID: m.ID, SetExtra: NoChange}
	mTime := int64(1000)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Where(
		"id = ?", m.ID).Updates(map[string]interface{}{"modified_time": mTime}).Error)
	_, err = param.editPopup()
	require.EqualError(err, "未修改直播间活动小窗的任何参数")
	require.NoError(service.DB.Where("id = ?", m.ID).Take(&m).Error)
	assert.Equal(mTime, m.ModifiedTime)
	// 测试修改数据
	param = livePopupParam{ID: m.ID, Sort: 2, ImageURL: "test_cover"}
	intro, err := param.editPopup()
	require.NoError(err)
	assert.NotEmpty(intro)
	require.NoError(service.DB.Where("id = ?", m.ID).Take(&m).Error)
	assert.Equal(param.Sort, m.Sort)
	assert.Equal(param.ImageURL, m.Cover)
	assert.NotEqual(mTime, m.ModifiedTime)
	// 测试线上环境无法修改过期小窗
	require.NoError(os.Setenv(util.EnvDeploy, util.DeployEnvProd))
	defer func() {
		require.NoError(os.Setenv(util.EnvDeploy, ""))
	}()
	_, err = param.editPopup()
	assert.EqualError(err, "无法找到对应资源")
}

func TestLivePopupParam_log(t *testing.T) {
	assert := assert.New(t)

	expectedIntro := ", 所有直播间显示"
	param := livePopupParam{SetExtra: 0}
	cancel := mrpc.SetMock("go://util/addadminlog", func(input interface{}) (output interface{}, err error) {
		intro := (*input.(*[]adminlogger.AdminLog))[0].Intro
		assert.Equal(expectedIntro, intro)
		return "success", nil
	})
	defer cancel()

	// 测试所有直播间显示
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	param.log(c, "")

	// 测试不修改显示
	param.SetExtra = 99
	expectedIntro = ", 不修改显示"
	param.log(c, "")

	// 测试各种限制显示
	param.SetExtra = 1
	param.catalogIDs = []int64{1, 2}
	param.tagIDs = []int64{3, 4}
	param.roomIDs = []int64{5, 6}
	expectedIntro = ", 分区 1,2 显示, 标签 3,4 显示, 直播间 5,6 显示"
	param.log(c, "")
}

func TestLivePopupParam_newUpdateModel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不修改的时候
	p := livePopupParam{SetExtra: NoChange}
	m, err := p.newUpdateModel()
	require.NoError(err)
	assert.Nil(m)

	// 测试修改的时候
	p = livePopupParam{
		Sort:     1,
		EventID:  20020,
		ImageURL: "test_cover_2",
		url:      "test_url_2",
		SetExtra: NoExtra,
	}
	m, err = p.newUpdateModel()
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(p.Sort, m["sort"])
	assert.Equal(p.EventID, m["element_id"])
	assert.Equal(p.ImageURL, m["cover"])
	assert.Equal(p.url, m["url"])
	assert.Equal(`{"all_room_show":true}`, m["extended_fields"])
	assert.NotNil(m["modified_time"])
}

func TestLivePopupParam_checkDuplicates(t *testing.T) {
	assert := assert.New(t)

	err := (&livePopupParam{}).checkDuplicates("直播间", []int64{1, 1})
	assert.EqualError(err, "有重复的直播间 ID，重复 ID 为 1")
}

func TestNewExtendedFields(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := livePopupParam{SetExtra: NoChange}
	extendedFields, err := p.newExtendedFields()
	require.NoError(err)
	assert.Empty(extendedFields)

	p = livePopupParam{SetExtra: NoExtra}
	extendedFields, err = p.newExtendedFields()
	require.NoError(err)
	assert.Equal(`{"all_room_show":true}`, extendedFields)

	p = livePopupParam{SetExtra: SetExtraAllowList}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("必须配置小窗的展示范围"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, CatalogIDs: ","}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("分区名单格式错误"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, TagIDs: ","}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("标签名单格式错误"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDs: ","}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("直播间名单格式错误"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, CatalogIDs: "1,2,2,3"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有重复的分区 ID，重复 ID 为 2"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, TagIDs: "1,2,3,3"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有重复的标签 ID，重复 ID 为 3"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDs: "1,1,2,3"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有重复的直播间 ID，重复 ID 为 1"), err)

	cleanUp, err := createFile("../../../testdata/rooms.csv", "user_id\n114514\n114514\n")
	defer func() {
		require.NoError(cleanUp())
	}()
	require.NoError(err)
	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDsCSVURL: "https://fm.example.com/testdata/rooms.csv"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有重复的直播间 ID，重复 ID 为 114514"), err)

	p = livePopupParam{SetExtra: SetExtraAllowList, CatalogIDs: "104,-1"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的分区 ID，不存在的 ID 为 -1"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, TagIDs: "1,2,3,4"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的标签 ID，不存在的 ID 为 3,4"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDs: "4381915,3"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的直播间 ID，不存在的 ID 为 3"), err)

	cleanUp1, err := createFile("../../../testdata/rooms1.csv", "user_id\n114514\n1919\n")
	defer func() {
		require.NoError(cleanUp1())
	}()
	require.NoError(err)
	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDsCSVURL: "https://fm.example.com/testdata/rooms1.csv"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的直播间 ID，不存在的 ID 为 114514,1919"), err)

	p = livePopupParam{SetExtra: SetExtraAllowList, RoomIDs: "4381915"}
	extendedFields, err = p.newExtendedFields()
	require.NoError(err)
	assert.Equal([]int64{4381915}, p.roomIDs)
	assert.Equal(`{"all_room_show":false,"room_ids":[4381915]}`, extendedFields)
	p = livePopupParam{SetExtra: SetExtraAllowList, TagIDs: "-1"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的标签 ID，不存在的 ID 为 -1"), err)
	p = livePopupParam{SetExtra: SetExtraAllowList, CatalogIDs: "123,111,104"}
	_, err = p.newExtendedFields()
	require.Equal(actionerrors.ErrParamsMsg("有不存在的分区 ID，不存在的 ID 为 123,111"), err, "下线的分区无法提交")
	p = livePopupParam{SetExtra: SetExtraAllowList, CatalogIDs: "118,104"}
	extendedFields, err = p.newExtendedFields()
	require.NoError(err)
	assert.Equal(`{"all_room_show":false,"catalog_ids":[118,104]}`, extendedFields, "二级分区可提交")
}

func TestLivePopupParam_trimSpace(t *testing.T) {
	assert := assert.New(t)

	p := &livePopupParam{
		MiniURL:      " http://fm.example.com/mini_url ",
		FoldImageURL: "\t\n\nhttp://fm.example.com/fold_url\t\n\r",
		ImageURL:     "http://fm.example.com/image_url ",
		OpenURL:      "	http://fm.example.com/open_url	",
		FullURL:      "       http://fm.example.com/full_url",
	}
	p.trimSpace()

	assert.Equal("http://fm.example.com/mini_url", p.MiniURL)
	assert.Equal("http://fm.example.com/fold_url", p.FoldImageURL)
	assert.Equal("http://fm.example.com/image_url", p.ImageURL)
	assert.Equal("http://fm.example.com/open_url", p.OpenURL)
	assert.Equal("http://fm.example.com/full_url", p.FullURL)
}
