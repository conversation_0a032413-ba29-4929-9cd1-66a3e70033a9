package recommended

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testRoomID = int64(22489473)

func TestNewLiveBanner(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	lb := newLiveBanner(liverecommendedelements.Model{
		ID:          1,
		ElementID:   3,
		ElementType: liverecommendedelements.ElementLiveBanner,
		Attribute: liverecommendedelements.Attribute{
			Name:      "title",
			Cover:     "oss://abc;oss://123",
			StartTime: &now,
		},
		ExpireTime: now + 1800,
	})
	assert.Equal(liveBanner{
		ID:            1,
		RoomID:        3,
		Title:         "title",
		ImageURL:      storage.ParseSchemeURL("oss://abc"),
		SmallImageURL: storage.ParseSchemeURL("oss://123"),
		StartTime:     now,
		ExpireTime:    now + 1800,
	}, *lb)
}

func TestAddExtraInfoLiveBanner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list := []*liveBanner{{RoomID: testRoomID}, nil}
	err := addExtraInfoLiveBanner(list)
	require.NoError(err)
	assert.NotEmpty(list[0].CreatorID)
	assert.NotEmpty(list[0].CreatorUsername)
	assert.Nil(list[1])
}

func TestActionLiveBanners(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nextDay := util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1)
	nextDayUnix := nextDay.Unix()

	add := AddLiveBannerParam{
		liveBannerParam: liveBannerParam{
			CreatorID:     10,
			Title:         "",
			ImageURL:      "oss://image.png",
			SmallImageURL: "oss://small_image.png",
		},
		Intervals: []timeInterval{
			{
				StartTime: nextDayUnix,
			},
			{
				StartTime: nextDayUnix + 3600,
			},
		},
	}
	c := handler.NewTestContext("POST", "/addlivebanner", false, add)
	data, err := ActionAddLiveBanner(c)
	require.NoError(err)
	assert.Len(data, 2)
	list := data.([]*liveBanner)
	if list[0] != nil {
		assert.NotZero(list[0].ID)
	}
	if list[1] != nil {
		assert.NotZero(list[1].ID)
	}

	// 添加受限房间
	add = AddLiveBannerParam{
		liveBannerParam: liveBannerParam{
			CreatorID:     room.TestLimitedRoomCreatorID,
			Title:         "",
			ImageURL:      "oss://image.png",
			SmallImageURL: "oss://small_image.png",
		},
		Intervals: []timeInterval{{StartTime: nextDayUnix}},
	}
	c = handler.NewTestContext("POST", "/addlivebanner", false, add)
	_, err = ActionAddLiveBanner(c)
	assert.Equal(actionerrors.ErrLimitedRoom, err)

	c = handler.NewTestContext("GET", "/listlivebanners?date="+nextDay.Format("2006-01-02"), false, nil)
	data, err = ActionListLiveBanners(c)
	require.NoError(err)
	assert.Len(data, 24)
	results := data.([]liveBannerResult)
	require.NotEmpty(results[0].Data)
	require.NotEmpty(results[1].Data)

	_, err = ActionListCurrentLiveBanners(nil)
	require.NoError(err)

	c = handler.NewTestContext("POST", "/delbanner", false,
		strings.NewReader(fmt.Sprintf(`{"id":%d}`, results[0].Data[0].ID)))
	_, err = ActionDelLiveBanner(c)
	require.NoError(err)

	edit := EditLiveBannerParam{
		ID: results[1].Data[0].ID,
		liveBannerParam: liveBannerParam{
			CreatorID:     10,
			Title:         "modified",
			ImageURL:      "oss://image.png",
			SmallImageURL: "oss://small_image.png",
		},
	}
	c = handler.NewTestContext("POST", "/editlivebanner", false, edit)
	_, err = ActionEditLiveBanner(c)
	require.NoError(err)

	c = handler.NewTestContext("GET", "/listlivebanners?date="+nextDay.Format("2006-01-02"), false, nil)
	data, err = ActionListLiveBanners(c)
	require.NoError(err)

	results = data.([]liveBannerResult)
	assert.Empty(results[0].Data)
	require.NotEmpty(results[1].Data)
	assert.Equal(edit.Title, results[1].Data[0].Title)
}

func TestJSONTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(liveBannerResult{}, "data", "time")
}

func TestBannerCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var rec liverecommendedelements.Model
	require.NoError(service.DB.Where("start_time < expire_time").First(&rec).Error)

	when := time.Unix(*rec.StartTime, 0)
	key := keys.KeyBannersMetaBanner1.Format(when.Minute() / 5)
	val, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	if val == 0 {
		// 防止脏数据影响其他测试
		require.NoError(service.Redis.Set(key, "{}", 10*time.Second).Err())
	}
	delBannerCacheWithID(rec.ID, when)
	val, err = service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(val)
}

func TestActionSetBanner(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testURL := "https://fm.uat.missevan.com/live/61687673"
	testImageURL := "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg"

	var params []SetBannerParam
	params = append(params, SetBannerParam{
		URL:           testURL,
		AppURL:        testURL,
		ImageURL:      testImageURL,
		SmallImageURL: testImageURL,
		Title:         "测试置顶",
		Order:         -1,
	})
	params = append(params, SetBannerParam{
		URL:           testURL,
		AppURL:        testURL,
		ImageURL:      testImageURL,
		SmallImageURL: testImageURL,
		Title:         "测试 0",
		Order:         0,
	})
	params = append(params, SetBannerParam{
		URL:           testURL,
		AppURL:        testURL,
		ImageURL:      testImageURL,
		SmallImageURL: testImageURL,
		Title:         "测试 1",
		Order:         1,
	})
	params = append(params, SetBannerParam{
		URL:           " " + testURL + " ",
		AppURL:        " " + testURL,
		ImageURL:      testImageURL + " ",
		SmallImageURL: testImageURL,
		Title:         " 测试删除首尾空格 ",
		Order:         1,
	})
	c := handler.NewTestContext("POST", "/editlivebanner", true, params)
	r, err := ActionSetBanner(c)
	require.NoError(err)
	assert.NotEmpty(r)
	resp := r.(handler.M)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(resp, "banners")
	banners := resp["banners"].([]models.Banner)
	require.Len(banners, 4)
	assert.NotEqual(banners[0].Order, banners[1].Order)
	assert.Equal("测试删除首尾空格", banners[3].Title)
	assert.Equal(testURL, banners[3].URL)
	assert.Equal(testURL, banners[3].AppURL)
	assert.Equal(testImageURL, banners[3].ImageURL)

	// 清空情况
	params = []SetBannerParam{}
	c = handler.NewTestContext("POST", "/editlivebanner", true, params)
	r, err = ActionSetBanner(c)
	require.NoError(err)
	require.IsType(resp, r)
	resp = r.(handler.M)
	emptyBanner, ok := resp["banners"]
	require.True(ok)
	assert.NotNil(emptyBanner)
	assert.Len(emptyBanner, 0)
}

func TestActionListBanner(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext("GET", "/listbanner", true, nil)
	banners, err := ActionListBanner(c)
	require.NoError(err)
	m := banners.(handler.M)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(m, "banners")
	data := m["banners"].([]*models.Banner)
	assert.NotNil(data)
}
