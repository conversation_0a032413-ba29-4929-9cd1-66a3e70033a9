package recommended

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const livePopupURLSize = 4

type livePopupParam struct {
	ID           int64  `form:"id" json:"id"`
	EventID      int64  `form:"event_id" json:"event_id"`
	Sort         int    `form:"sort" json:"sort"`
	MiniURL      string `form:"mini_url" json:"mini_url"`
	ImageURL     string `form:"image_url" json:"image_url"`
	FoldImageURL string `form:"fold_image_url" json:"fold_image_url"`
	FullURL      string `form:"full_url" json:"full_url"`
	OpenURL      string `form:"open_url" json:"open_url"`
	StartTime    int64  `form:"start_time" json:"start_time"`
	EndTime      int64  `form:"end_time" json:"end_time"`

	SetExtra      int              `form:"set_extra" json:"set_extra"`
	CatalogIDs    string           `form:"catalog_ids" json:"catalog_ids"`
	TagIDs        string           `form:"tag_ids" json:"tag_ids"`
	RoomIDs       string           `form:"room_ids" json:"room_ids"`
	RoomIDsCSVURL upload.SourceURL `form:"room_ids_csv_url" json:"room_ids_csv_url"`

	catalogIDs []int64
	tagIDs     []int64
	roomIDs    []int64

	eventURL string
	url      string
	model    *liverecommendedelements.Model

	applicationID int64
}

// ActionAddPopup 添加直播间活动小窗
/**
 * @api {post} /api/v2/admin/recommended/addpopup  添加直播间活动小窗
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} [event_id] 活动 ID
 * @apiParam {Number} sort 排序
 * @apiParam {String} mini_url 小窗地址
 * @apiParam {String} image_url 图片地址
 * @apiParam {String} fold_image_url 折叠状态图片地址
 * @apiParam {String} [full_url] 半窗地址（该参数与 open_url 互斥，同时只能传入一个）
 * @apiParam {String} [open_url] 新窗口地址（该参数与 full_url 互斥，同时只能传入一个）
 * @apiParam {Number} start_time 开始时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} end_time 结束时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} set_extra 是否部分直播间显示，0 为否，1 白名单可见，2 黑名单不可见
 * @apiParam {String} [catalog_ids] 显示小窗的分区 ID 列表，只支持一级分区，由数字和英文逗号组成，如: 1,2,3
 * @apiParam {String} [tag_ids] 显示小窗的标签 ID 列表，由数字和英文逗号组成
 * @apiParam {String} [room_ids] 显示小窗的直播间 ID 列表，由数字和英文逗号组成
 * @apiParam {String} [room_ids_csv_url] 显示小窗的直播间 ID 列表，由 csv 文件传入（与 room_ids 互斥）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "保存成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionAddPopup(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLivePopupParam(c)
	if err != nil {
		return nil, err
	}
	err = param.checkBeforeAdd()
	if err != nil {
		return nil, err
	}
	err = param.addPopup()
	if err != nil {
		return nil, err
	}
	// 管理员操作日志
	intro := fmt.Sprintf("添加直播间活动小窗, 图片地址: %s, 活动页面地址: %s, 开始时间: %s, 结束时间: %s",
		param.ImageURL, param.eventURL, time.Unix(param.StartTime, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHMS))
	param.log(c, intro)
	return "保存成功", nil
}

func (param *livePopupParam) addPopup() error {
	extendedFields, err := param.newExtendedFields()
	if err != nil {
		return err
	}
	m := liverecommendedelements.Popup{
		ElementID: param.EventID,
		Sort:      param.Sort,
		Attribute: liverecommendedelements.Attribute{
			Cover:     param.ImageURL,
			URL:       param.url,
			StartTime: &param.StartTime,
		},
		ExpireTime:     param.EndTime,
		ExtendedFields: extendedFields,
	}
	popupID, err := m.Save(service.DB)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	liverecommendedelements.ClearPopupCache()

	// 如果创建了新的名单，就把名单简介更新为带有小窗 ID 的名单简介
	if param.applicationID != 0 {
		err = application.UpdateIntro(param.applicationID, param.getApplicationIntro(popupID))
		if err != nil {
			logger.WithFields(logger.Fields{
				"popup_id":       popupID,
				"application_id": param.applicationID,
			}).Error(err)
			// PASS
		}
	}

	return nil
}

// ActionEditPopup 修改直播间活动小窗
/**
 * @api {post} /api/v2/admin/recommended/editpopup  修改直播间活动小窗
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 小窗 ID
 * @apiParam {Number} [event_id] 活动 ID
 * @apiParam {Number} [sort] 排序
 * @apiParam {String} [mini_url] 小窗地址
 * @apiParam {String} [image_url] 图片地址
 * @apiParam {String} [fold_image_url] 折叠状态图片地址
 * @apiParam {String} [full_url] 半窗地址（该参数与 open_url 互斥，同时只能传入一个）
 * @apiParam {String} [open_url] 新窗口地址（该参数与 full_url 互斥，同时只能传入一个）
 * @apiParam {Number} [start_time] 开始时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} [end_time] 结束时间, 秒级时间戳, 如: 1578844800
 * @apiParam {Number} set_extra 是否部分直播间显示，0 为否，1 白名单可见，2 黑名单不可见，-1 不修改
 * @apiParam {String} [catalog_ids] 显示小窗的分区 ID 列表，只支持一级分区，由数字和英文逗号组成，如: 1,2,3
 * @apiParam {String} [tag_ids] 显示小窗的标签 ID 列表，由数字和英文逗号组成
 * @apiParam {String} [room_ids] 显示小窗的直播间 ID 列表，由数字和英文逗号组成
 * @apiParam {String} [room_ids_csv_url] 显示小窗的直播间 ID 列表，由 csv 文件传入（与 room_ids 互斥）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "修改成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionEditPopup(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLivePopupParam(c)
	if err != nil {
		return nil, err
	}
	err = param.checkBeforeEdit()
	if err != nil {
		return nil, err
	}
	intro, err := param.editPopup()
	if err != nil {
		return nil, err
	}
	// 管理员操作日志
	param.log(c, intro)
	return "修改成功", nil
}

func newLivePopupParam(c *handler.Context) (*livePopupParam, error) {
	param := new(livePopupParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	param.trimSpace()
	return param, nil
}

// 图片上传到 storage
func uploadToStorage(url string) (string, error) {
	if url == "" {
		return "", nil
	}
	imageSourceURL := upload.SourceURL(url)
	urlFilter, err := liveupload.NewImageURLFilter([]upload.SourceURL{imageSourceURL})
	if err != nil {
		return "", actionerrors.ErrParamsMsg(err.Error())
	}
	if ok := storage.CheckStorage(imageSourceURL); !ok {
		return "", actionerrors.ErrParamsMsg("请求上传的资源不被支持，请重新选择")
	}
	imageURL, err := UploadImage(urlFilter, storage.PathPrefixPopupImage)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	return imageURL, nil
}

func (param *livePopupParam) uploadImages() error {
	var err error
	param.ImageURL, err = uploadToStorage(param.ImageURL)
	if err != nil {
		return err
	}
	param.FoldImageURL, err = uploadToStorage(param.FoldImageURL)
	if err != nil {
		return err
	}
	return nil
}

func (param *livePopupParam) parseCSV() error {
	res, err := service.Upload.ToResource(param.RoomIDsCSVURL)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	file, err := res.Open()
	if err != nil {
		return actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	r := csv.NewReader(file)
	// 跳过第一行表头
	_, err = r.Read()
	if err != nil && err != io.EOF {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	var roomIDs []int64
	for {
		record, err := r.Read()
		if err != nil {
			if err == io.EOF {
				break
			}
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 本行为空时跳过
		if len(record[0]) == 0 {
			continue
		}
		roomID, err := strconv.ParseInt(record[0], 10, 64)
		if err != nil {
			return actionerrors.ErrParamsMsg("CSV 数据格式错误，请检查后再试")
		}
		roomIDs = append(roomIDs, roomID)
	}
	if len(roomIDs) == 0 {
		return actionerrors.ErrParamsMsg("CSV 不可为空")
	}

	param.roomIDs = roomIDs
	return param.checkDuplicates("直播间", roomIDs)
}

func (param *livePopupParam) checkBeforeAdd() error {
	if param.MiniURL == "" || param.ImageURL == "" || param.FoldImageURL == "" {
		return actionerrors.ErrParams
	}
	// full 和 open 不能同时为空
	if param.FullURL == "" && param.OpenURL == "" {
		return actionerrors.ErrParams
	}
	// full_url 和 open_url 互斥
	if param.FullURL != "" && param.OpenURL != "" {
		return actionerrors.ErrParams
	}
	// 结束时间不能小于开始时间，结束时间不能为零，且俩时间不能为负数
	if param.EndTime <= 0 || param.EndTime <= param.StartTime || param.StartTime < 0 {
		return actionerrors.ErrParams
	}
	// 上传图片到 storage，如果图片不符合规范会报错
	err := param.uploadImages()
	if err != nil {
		return err
	}
	// 把无意义的 ID 设置为 0，防止被误用
	param.ID = 0
	return param.buildParamURL()
}

func (param *livePopupParam) checkBeforeEdit() error {
	// 校验参数
	if param.ID == 0 {
		return actionerrors.ErrParams
	}
	if param.StartTime < 0 || param.EndTime < 0 {
		return actionerrors.ErrParams
	}
	// 确认开始时间小于结束时间
	if param.StartTime != 0 || param.EndTime != 0 {
		err := param.fillTimes()
		if err != nil {
			return err
		}
		if param.StartTime >= param.EndTime {
			return actionerrors.ErrParams
		}
	}
	// 确保 setextra 为规范值
	if !goutil.HasElem([]int{NoExtra, SetExtraAllowList, SetExtraBlockList, NoChange}, param.SetExtra) {
		return actionerrors.ErrParams
	}
	// full_url 和 open_url 互斥
	if param.FullURL != "" && param.OpenURL != "" {
		return actionerrors.ErrParams
	}
	// 上传图片到 storage，如果图片不符合规范会报错
	err := param.uploadImages()
	if err != nil {
		return err
	}
	// 如果小窗不存在就报错
	err = param.popupExists()
	if err != nil {
		return err
	}
	return param.parseModelURL()
}

func (param *livePopupParam) parseModelURL() error {
	if param.model == nil || param.model.URL == "" {
		return param.buildParamURL()
	}
	// 链接格式为 MiniURL;FoldImageURL;OpenURL;FullURL
	urls := strings.Split(param.model.URL, ";")
	// 如果原链接数量不符合要求则直接生成新链接
	if len(urls) != livePopupURLSize {
		logger.WithFields(logger.Fields{
			"popup_id": param.ID,
			"event_id": param.EventID,
		}).Warn("修改的小窗 url 参数长度有误，已重新生成")
		return param.buildParamURL()
	}
	if param.MiniURL == "" {
		param.MiniURL = urls[0]
	}
	if param.FoldImageURL == "" {
		param.FoldImageURL = urls[1]
	}
	// Full 和 Open 链接只能有一个存在，因此需要一起修改来保证数据正确
	if param.OpenURL == "" && param.FullURL == "" {
		param.OpenURL = urls[2]
		param.FullURL = urls[3]
	}
	return param.buildParamURL()
}

func (param *livePopupParam) fillTimes() error {
	if param.StartTime != 0 && param.EndTime != 0 {
		return nil
	}
	m, err := liverecommendedelements.FindElement(param.ID, liverecommendedelements.ElementPopup)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if m == nil {
		return actionerrors.ErrCannotFindResource
	}
	if param.StartTime == 0 && m.StartTime != nil {
		param.StartTime = *m.StartTime
	}
	if param.EndTime == 0 {
		param.EndTime = m.ExpireTime
	}
	// 保存 model 以便后续检查
	param.model = m
	return nil
}

func (param *livePopupParam) editPopup() (string, error) {
	m, err := param.newUpdateModel()
	if err != nil {
		return "", err
	}
	if m == nil {
		return "", actionerrors.ErrParamsMsg("未修改直播间活动小窗的任何参数")
	}

	db := service.DB.Table(liverecommendedelements.TableName()).
		Where("sort > ?", liverecommendedelements.SortDeleted).
		Where("id = ?", param.ID)
	if goutil.IsProdEnv() {
		// 线上环境额外加上对于过期时间的检测，不允许修改已经过期的小窗
		db = db.Where("expire_time > ?", goutil.TimeNow().Unix())
	}
	db = db.Updates(m)
	if db.Error != nil {
		return "", actionerrors.NewErrServerInternal(db.Error, nil)
	}
	if db.RowsAffected == 0 {
		return "", actionerrors.ErrCannotFindResource
	}
	liverecommendedelements.ClearPopupCache()

	intro := fmt.Sprintf("修改直播间活动小窗, 小窗 ID: %d, 图片地址: %s, 活动页面地址: %s, 开始时间: %s, "+
		"结束时间: %s", param.ID, param.ImageURL, param.eventURL,
		time.Unix(param.StartTime, 0).Format(util.TimeFormatYMDHMS),
		time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHMS))
	return intro, nil
}

func (param *livePopupParam) log(c *handler.Context, intro string) {
	box := goclient.NewAdminLogBox(c)
	switch param.SetExtra {
	case NoExtra:
		intro += ", 所有直播间显示"
	case SetExtraAllowList, SetExtraBlockList:
		display := "显示"
		if param.SetExtra == SetExtraBlockList {
			display = "不显示"
		}

		if len(param.catalogIDs) != 0 {
			intro += fmt.Sprintf(", 分区 %s %s", goutil.JoinInt64Array(param.catalogIDs, ","), display)
		}
		if len(param.tagIDs) != 0 {
			intro += fmt.Sprintf(", 标签 %s %s", goutil.JoinInt64Array(param.tagIDs, ","), display)
		}
		if len(param.roomIDs) != 0 {
			intro += fmt.Sprintf(", 直播间 %s %s", goutil.JoinInt64Array(param.roomIDs, ","), display)
		}
	default:
		intro += ", 不修改显示"
	}
	box.Add(userapi.CatalogRecommendedLivePopupAdd, intro)
	err := box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// updateModel
func (param *livePopupParam) newUpdateModel() (map[string]interface{}, error) {
	// 获取 extended fields
	extendedFields, err := param.newExtendedFields()
	if err != nil {
		return nil, err
	}
	// 创建更新 model
	updateModel := make(map[string]interface{})
	if param.Sort != 0 {
		updateModel["sort"] = param.Sort
	}
	if param.EventID != 0 {
		updateModel["element_id"] = param.EventID
	}
	if param.EndTime != 0 {
		updateModel["expire_time"] = param.EndTime
	}
	if extendedFields != "" {
		updateModel["extended_fields"] = extendedFields
	}
	// 更新 attribute 内容
	if param.ImageURL != "" {
		updateModel["cover"] = param.ImageURL
	}
	if param.url != "" {
		updateModel["url"] = param.url
	}
	if param.StartTime != 0 {
		updateModel["start_time"] = &param.StartTime
	}
	if len(updateModel) == 0 {
		return nil, nil
	}
	// 加上更新时间
	updateModel["modified_time"] = goutil.TimeNow().Unix()
	return updateModel, nil
}

func (param *livePopupParam) checkDuplicates(idContent string, array []int64) error {
	list := util.FindInt64Duplicates(array)
	if len(list) != 0 {
		msg := fmt.Sprintf("有重复的%s ID，重复 ID 为 %s", idContent, goutil.JoinInt64Array(list, ","))
		return actionerrors.ErrParamsMsg(msg)
	}
	return nil
}

func (param *livePopupParam) newExtendedFields() (string, error) {
	if param.SetExtra == NoChange {
		return "", nil
	}
	showConfig := &liverecommendedelements.PopupShowConfig{
		AllRoomShow: !util.IntToBool(param.SetExtra),
	}
	if showConfig.AllRoomShow {
		extendedFields, err := json.Marshal(showConfig)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
		return string(extendedFields), nil
	}
	getArrayAndDuplicates := func(ids string, idContent string) ([]int64, error) {
		array, err := util.SplitToInt64Array(ids, ",")
		if err != nil {
			msg := fmt.Sprintf("%s名单格式错误", idContent)
			return nil, actionerrors.ErrParamsMsg(msg)
		}
		if err = param.checkDuplicates(idContent, array); err != nil {
			return nil, err
		}

		return array, nil
	}

	switch param.SetExtra {
	case SetExtraAllowList:
		showConfig.ApplicationType = liverecommendedelements.ApplicationAllowList
	case SetExtraBlockList:
		showConfig.ApplicationType = liverecommendedelements.ApplicationBlockList
	default:
		return "", actionerrors.ErrParamsMsg("名单类型错误")
	}

	var err error
	if param.catalogIDs, err = getArrayAndDuplicates(param.CatalogIDs, "分区"); err != nil {
		return "", err
	}
	if param.tagIDs, err = getArrayAndDuplicates(param.TagIDs, "标签"); err != nil {
		return "", err
	}

	if param.RoomIDsCSVURL != "" && param.RoomIDs != "" {
		return "", actionerrors.ErrParamsMsg("填写 room_id 与上传名单行为互斥")
	}
	if param.RoomIDsCSVURL != "" {
		if err = param.parseCSV(); err != nil {
			return "", err
		}
	} else {
		if param.roomIDs, err = getArrayAndDuplicates(param.RoomIDs, "直播间"); err != nil {
			return "", err
		}
	}

	if len(param.catalogIDs) == 0 && len(param.tagIDs) == 0 && len(param.roomIDs) == 0 {
		return "", actionerrors.ErrParamsMsg("必须配置小窗的展示范围")
	}

	rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": param.roomIDs}})
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != len(param.roomIDs) {
		m := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Room)
		list := make([]int64, 0)
		for _, r := range param.roomIDs {
			if _, ok := m[r]; !ok {
				list = append(list, r)
			}
		}
		return "", actionerrors.ErrParamsMsg(fmt.Sprintf("有不存在的直播间 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(list, ",")))
	}

	var nonexistentCatalogIDs []int64
	// 查询所有可见的一级分区，一级分区的 SubCatalogs 会有二级分区的信息
	catalogs, err := catalog.FindCatalog(false)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	allCatalogs := make(map[int64]*catalog.LiveCatalog)
	for _, c := range catalogs {
		allCatalogs[c.ID] = c
		for _, subCatalog := range c.SubCatalogs {
			allCatalogs[subCatalog.ID] = subCatalog
		}
	}
	for _, v := range param.catalogIDs {
		if _, ok := allCatalogs[v]; !ok {
			// 分区查不到或者不可见，都视为不存在
			nonexistentCatalogIDs = append(nonexistentCatalogIDs, v)
		}
	}
	if len(nonexistentCatalogIDs) != 0 {
		msg := fmt.Sprintf("有不存在的分区 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(nonexistentCatalogIDs, ","))
		return "", actionerrors.ErrParamsMsg(msg)
	}
	if len(param.tagIDs) > 0 {
		tags, err := tag.ListByID(param.tagIDs, tag.TypeLiveTag)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
		if len(tags) == 0 {
			msg := fmt.Sprintf("有不存在的标签 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(param.tagIDs, ","))
			return "", actionerrors.ErrParamsMsg(msg)
		}
		tagMap := goutil.ToMap(tags, "ID").(map[int64]*tag.Tag)
		var nonexistentTagIDs []int64
		for _, v := range param.tagIDs {
			// 标签不存在或隐藏的时候，视为标签不存在
			if t, ok := tagMap[v]; !ok || t.Status == tag.StatusHide {
				nonexistentTagIDs = append(nonexistentTagIDs, v)
			}
		}
		if len(nonexistentTagIDs) != 0 {
			msg := fmt.Sprintf("有不存在的标签 ID，不存在的 ID 为 %s", goutil.JoinInt64Array(nonexistentTagIDs, ","))
			return "", actionerrors.ErrParamsMsg(msg)
		}
	}

	showConfig.CatalogIDs = param.catalogIDs
	showConfig.TagIDs = param.tagIDs

	err = param.setRoomIDs(rooms, showConfig)
	if err != nil {
		return "", err
	}

	extendedFields, err := json.Marshal(showConfig)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	return string(extendedFields), nil
}

func (param *livePopupParam) getApplicationIntro(popupID int64) string {
	var intro string
	if popupID == 0 {
		intro = "直播间小窗房间"
	} else {
		intro = fmt.Sprintf("直播间小窗（id: %d）房间", popupID)
	}

	switch param.SetExtra {
	case SetExtraAllowList:
		intro += "白名单"
	case SetExtraBlockList:
		intro += "黑名单"
	}

	return intro
}

func (param *livePopupParam) setRoomIDs(rooms []*room.Room, showConfig *liverecommendedelements.PopupShowConfig) error {
	// 如果房间数小于等于 20 个，则使用 config 存储，否则使用 db 存储
	if len(rooms) <= 20 {
		showConfig.RoomIDs = param.roomIDs
		return nil
	}

	var applicationType int
	if showConfig.ApplicationType == liverecommendedelements.ApplicationBlockList {
		applicationType = application.TypeBlockList
	} else {
		applicationType = application.TypeAllowList
	}

	var applicationID int64
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		// 新建小窗时 intro 是不包含小窗 ID 的，等小窗创建完成后会更新为带有小窗 ID 的 intro
		a, err := application.CreatePopupApplication(tx, param.getApplicationIntro(param.ID), applicationType, application.ElementTypeRoomID)
		if err != nil {
			return err
		}

		now := goutil.TimeNow().Unix()
		elements := make([]*application.Element, 0, len(rooms))
		for _, r := range rooms {
			elements = append(elements, &application.Element{
				ApplicationID: a.ID,
				ElementID:     r.RoomID,
				CreateTime:    now,
				ModifiedTime:  now,
			})
		}

		err = servicedb.SplitBatchInsert(tx, application.Element{}.TableName(), elements, 1000, false)
		if err != nil {
			return err
		}

		applicationID = a.ID
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	showConfig.RoomApplicationID = applicationID
	param.applicationID = applicationID

	return nil
}

func (param *livePopupParam) buildParamURL() error {
	popupItem := &liverecommendedelements.PopupRespItem{
		MiniURL:      param.MiniURL,
		FoldImageURL: param.FoldImageURL,
	}
	if param.FullURL != "" {
		param.eventURL = param.FullURL
		popupItem.FullURL = param.FullURL
	} else {
		param.eventURL = param.OpenURL
		popupItem.OpenURL = param.OpenURL
	}
	param.url = popupItem.PopupURL()
	if utf8.RuneCountInString(param.url) > 1000 {
		// 数据库 URL 字段长度为 1000
		return actionerrors.NewErrForbidden("路径过长，小窗路径最大长度为 1000")
	}
	return nil
}

func (param *livePopupParam) trimSpace() {
	param.MiniURL = strings.TrimSpace(param.MiniURL)
	param.ImageURL = strings.TrimSpace(param.ImageURL)
	param.FoldImageURL = strings.TrimSpace(param.FoldImageURL)
	param.FullURL = strings.TrimSpace(param.FullURL)
	param.OpenURL = strings.TrimSpace(param.OpenURL)
}

func (param *livePopupParam) popupExists() error {
	if param.model == nil {
		m, err := liverecommendedelements.FindElement(param.ID, liverecommendedelements.ElementPopup)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if m == nil {
			// 没有找到对应小窗
			return actionerrors.ErrNotFound("活动小窗不存在，请检查后重新提交")
		}
		param.model = m
	}

	if goutil.IsProdEnv() && param.model.ExpireTime <= goutil.TimeNow().Unix() {
		// 线上环境下不允许修改过期小窗
		return actionerrors.ErrNotFound("活动小窗已过期，如需修改请重新添加")
	}
	return nil
}

// ActionDelPopup 删除直播间活动小窗
/**
 * @api {post} /api/v2/admin/recommended/delpopup  删除直播间活动小窗
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 直播间活动小窗 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionDelPopup(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil || input.ID <= 0 {
		return nil, actionerrors.ErrParams
	}

	element, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementPopup)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	liverecommendedelements.ClearPopupCache()

	intro := fmt.Sprintf("删除直播间活动小窗, 小窗 ID: %d, 小窗 URL: %s", element.ID, element.URL)
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogRecommendedLivePopupDel, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功", nil
}
