package recommended

import (
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type liveBanner struct {
	ID              int64                          `json:"id"`
	RoomID          int64                          `json:"room_id"`
	Title           string                         `json:"title"`
	ImageURL        string                         `json:"image_url"`
	SmallImageURL   string                         `json:"small_image_url"`
	CreatorID       int64                          `json:"creator_id"`
	CreatorUsername string                         `json:"creator_username"`
	Status          liverecommendedelements.Status `json:"status"`
	StartTime       int64                          `json:"start_time"`
	ExpireTime      int64                          `json:"expire_time"`
}

func newLiveBanner(m liverecommendedelements.Model) *liveBanner {
	lb := &liveBanner{
		ID:         m.ID,
		RoomID:     m.ElementID,
		Title:      m.Name,
		StartTime:  *m.StartTime,
		ExpireTime: m.ExpireTime,
	}
	urls := strings.Split(m.Cover, ";")
	if len(urls) == 2 {
		lb.ImageURL, lb.SmallImageURL = storage.ParseSchemeURL(urls[0]), storage.ParseSchemeURL(urls[1])
	} else {
		logger.Error("封面图错误")
		// PASS
	}
	return lb
}

func addExtraInfoLiveBanner(list []*liveBanner) error {
	if len(list) == 0 {
		return nil
	}
	roomIDs := make([]int64, 0, len(list))
	for _, v := range list {
		if v != nil {
			roomIDs = append(roomIDs, v.RoomID)
		}
	}
	roomSimple, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(
			bson.M{
				"room_id":          1,
				"name":             1,
				"creator_id":       1,
				"creator_username": 1,
				"status.open":      1,
			},
		),
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i, v := range list {
		if v == nil {
			continue
		}
		for _, r := range roomSimple {
			if v.RoomID == r.RoomID {
				if list[i].Title == "" {
					list[i].Title = r.Name
				}
				list[i].CreatorID = r.CreatorID
				list[i].CreatorUsername = r.CreatorUsername
				if r.IsOpen() {
					list[i].Status.Open = 1
				}
			}
		}
	}
	return nil
}

type liveBannerResult struct {
	Time int64         `json:"time"`
	Data []*liveBanner `json:"data"`
}

// ActionListLiveBanners 获取某天的直播 banner 排期
/**
 * @api {get} /api/v2/admin/recommended/listlivebanners 获取某天的直播 banner 排期
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} [date] 例如 "2006-01-02"，不传默认是今天
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "time": 1584806400,
 *           "data": [
 *             {
 *               "id": 10232,
 *               "room_id": "162883684",
 *               "title": "原创歌曲推荐",
 *               "image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *               "small_image_url": "http://static.missevan.com/mimages/202004/01/bc909b64c37b95596bd2774edcf0b6f1235809.jpg",
 *               "creator_id": 10,
 *               "creator_username": "bless",
 *               "start_time": 1584806400,
 *               "expire_time": 1584808200,
 *             },
 *             ...
 *           ]
 *         },
 *         {
 *           "time": 1584808200,
 *           "data": []
 *         },
 *         ...
 *       ]
 *     }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 */
func ActionListLiveBanners(c *handler.Context) (handler.ActionResponse, error) {
	date := c.GetDefaultParam("date", goutil.TimeNow().Format("2006-01-02"))
	dayStart, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	records, err := liverecommendedelements.ListLiveBanners(dayStart, dayStart.AddDate(0, 0, 1))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	result := make(map[int64][]*liveBanner)
	list := make([]*liveBanner, 0, len(records))
	for _, m := range records {
		b := newLiveBanner(m)
		list = append(list, b)
		result[b.StartTime] = append(result[b.StartTime], b)
	}
	err = addExtraInfoLiveBanner(list)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 每一个小时一个时间段，返回一天 24 个时间段的数据
	array := []liveBannerResult{}
	for tm := dayStart; tm.Before(dayStart.AddDate(0, 0, 1)); tm = tm.Add(time.Hour) {
		t := tm.Unix()
		if v, ok := result[t]; ok {
			array = append(array, liveBannerResult{Time: t, Data: v})
		} else {
			array = append(array, liveBannerResult{Time: t, Data: []*liveBanner{}})
		}
	}
	return array, nil
}

// ActionListCurrentLiveBanners 获取当前时段直播 banners
/**
 * @api {get} /api/v2/admin/recommended/listcurrentlivebanners 获取当前时段直播 banners
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "id": 10232,
 *           "room_id": "162883684",
 *           "title": "原创歌曲推荐",
 *           "image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "small_image_url": "http://static.missevan.com/mimages/202004/01/bc909b64c37b95596bd2774edcf0b6f1235809.jpg",
 *           "status": {
 *             "open": 0
 *           },
 *           "creator_id": 10,
 *           "creator_username": "bless",
 *         }
 *       ]
 *     }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 */
func ActionListCurrentLiveBanners(c *handler.Context) (handler.ActionResponse, error) {
	records, err := liverecommendedelements.ListLiveBannersAt(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	list := make([]*liveBanner, 0, len(records))
	for _, m := range records {
		b := newLiveBanner(m)
		list = append(list, b)
	}
	err = addExtraInfoLiveBanner(list)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return list, nil
}

// ActionDelLiveBanner 取消 banner
/**
 * @api {post} /api/v2/admin/recommended/dellivebanner 取消 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录的 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "取消推荐成功"
 *     }
 */
func ActionDelLiveBanner(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		ID int64 `json:"id"`
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	ok, err := liverecommendedelements.DeleteLiveBanner(input.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "删除失败，请检查推荐是否过期或不存在")
	}
	delBannerCacheWithID(input.ID, goutil.TimeNow())
	return "取消推荐成功", nil
}

// EditLiveBannerParam 编辑直播推荐 banner 的参数
type EditLiveBannerParam struct {
	ID int64 `json:"id"`
	liveBannerParam
}

type liveBannerParam struct {
	CreatorID     int64            `json:"creator_id"`
	Title         string           `json:"title"`
	ImageURL      upload.SourceURL `json:"image_url"`
	SmallImageURL upload.SourceURL `json:"small_image_url,omitempty"`

	roomID        int64
	imageURL      string
	smallImageURL string
}

func (param *liveBannerParam) check() (err error) {
	r, err := room.FindOne(bson.M{"creator_id": param.CreatorID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrParamsMsg("主播 ID 对应的房间 ID 不存在")
	}
	if r.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}
	param.roomID = r.RoomID

	_, err = url.ParseRequestURI(param.ImageURL.String())
	if err != nil {
		return actionerrors.ErrParams
	}
	// TODO: 前端移除之后删除下面的 if 判断
	if param.SmallImageURL != "" {
		_, err = url.ParseRequestURI(param.SmallImageURL.String())
		if err != nil {
			return actionerrors.ErrParams
		}
	}
	return nil
}

func (param *liveBannerParam) mightOrMightNotUpload() (err error) {
	if ok := storage.CheckStorage(param.ImageURL); ok {
		param.imageURL, err = storage.UploadToOSS(param.ImageURL, storage.PathPrefixBanner)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	} else {
		param.imageURL = param.ImageURL.String()
	}

	if param.SmallImageURL != "" {
		if ok := storage.CheckStorage(param.SmallImageURL); ok {
			param.smallImageURL, err = storage.UploadToOSS(param.SmallImageURL, storage.PathPrefixBanner)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
		} else {
			param.smallImageURL = param.SmallImageURL.String()
		}
	}
	return nil
}

// ActionEditLiveBanner 编辑直播推荐 banner
/**
 * @api {post} /api/v2/admin/recommended/editlivebanner 编辑直播推荐 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录的 ID
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {String} title banner 标题
 * @apiParam {String} image_url Web banner 图链接地址
 * @apiParam {String} [small_image_url] App banner 图链接地址
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "保存成功"
 *     }
 */
func ActionEditLiveBanner(c *handler.Context) (handler.ActionResponse, error) {
	var param EditLiveBannerParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.mightOrMightNotUpload()
	if err != nil {
		return nil, err
	}
	var m liverecommendedelements.Model
	err = liverecommendedelements.TableLiveBanners(service.DB).Where("id = ?", param.ID).Take(&m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrParamsMsg("记录的 ID 不存在")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if m.StartTime == nil {
		return nil, actionerrors.ErrNotFound("数据错误")
	}

	var id int64
	err = liverecommendedelements.TableLiveBanners(service.DB).Select("id").Limit(1).
		Where("start_time = ?", m.StartTime).
		Where("element_id = ?", param.roomID).
		Where("id <> ?", param.ID).Row().Scan(&id)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err == nil {
		return nil, actionerrors.ErrParamsMsg("无法更新，检测到相同的房间 ID 已存在")
	}

	db := liverecommendedelements.TableLiveBanners(service.DB).Model(&m).
		Where("id = ?", param.ID).
		Where("expire_time > ?", goutil.TimeNow().Unix()).
		Updates(map[string]interface{}{
			"element_id": param.roomID,
			"name":       param.Title,
			"cover":      param.imageURL + ";" + param.smallImageURL,
		})
	if err := db.Error; err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if db.RowsAffected == 0 {
		return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "更新失败，请检查推荐是否过期或存在")
	}
	delBannerCacheWithID(param.ID, goutil.TimeNow())
	return "保存成功", nil
}

// AddLiveBannerParam 添加直播 banner 的参数
type AddLiveBannerParam struct {
	liveBannerParam
	Intervals []timeInterval `json:"intervals"`
}

// ActionAddLiveBanner 添加直播 banner
/**
 * @api {post} /api/v2/admin/recommended/addlivebanner 添加直播 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {String} title banner 标题
 * @apiParam {String} image_url Web banner 图链接地址
 * @apiParam {String} [small_image_url] App banner 图链接地址
 * @apiParam {Object[]} intervals 排期的时间区间 // TODO: 统一改为使用 periods
 * @apiParam {Number} intervals.start_time 排期的开始时间
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "id": 10232,
 *           "room_id": "162883684",
 *           "title": "原创歌曲推荐",
 *           "image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "small_image_url": "http://static.missevan.com/mimages/202004/01/bc909b64c37b95596bd2774edcf0b6f1235809.jpg",
 *           "creator_id": 10,
 *           "creator_username": "bless",
 *           "start_time": 1584806400,
 *           "expire_time": 1584808200,
 *         },
 *         null,
 *         {
 *           "id": 10232,
 *           "room_id": "162883684",
 *           "title": "原创歌曲推荐",
 *           "image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *           "small_image_url": "http://static.missevan.com/mimages/202004/01/bc909b64c37b95596bd2774edcf0b6f1235809.jpg",
 *           "creator_id": 10,
 *           "creator_username": "bless",
 *           "start_time": 1584808200,
 *           "expire_time": 1584810000,
 *         }
 *       ]
 *     }
 */
func ActionAddLiveBanner(c *handler.Context) (handler.ActionResponse, error) {
	var param AddLiveBannerParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.mightOrMightNotUpload()
	if err != nil {
		return nil, err
	}

	minStartTime := goutil.TimeNow().Unix() - util.SecondOneHour
	for i, v := range param.Intervals {
		if v.StartTime < minStartTime {
			return nil, actionerrors.ErrParamsMsg("存在过期时间段")
		}
		if v.StartTime%util.SecondOneHour != 0 {
			return nil, actionerrors.ErrParams
		}
		param.Intervals[i].ExpireTime = v.StartTime + util.SecondOneHour
	}

	m := liverecommendedelements.Model{
		ElementID:   param.roomID,
		ElementType: liverecommendedelements.ElementLiveBanner,
		Attribute: liverecommendedelements.Attribute{
			Name:  param.Title,
			Cover: param.imageURL + ";" + param.smallImageURL,
		},
	}
	result := make([]*liveBanner, len(param.Intervals))
	for i, v := range param.Intervals {
		m.ID = 0
		m.StartTime = &v.StartTime
		m.ExpireTime = v.ExpireTime

		// 允许部分数据成功
		var count int64
		err = liverecommendedelements.TableLiveBanners(service.DB).Where("start_time = ?", v.StartTime).Count(&count).Error
		if err != nil {
			logger.Errorf("Failed to insert: %v", err)
			continue
		}
		if count >= 5 {
			continue
		}
		dbStatus := liverecommendedelements.TableLiveBanners(service.DB).
			Where("start_time = ? AND element_id = ?", v.StartTime, param.roomID).
			FirstOrCreate(&m)
		if err := dbStatus.Error; err != nil {
			logger.Errorf("Failed to insert: %v", err)
			continue
		}
		if dbStatus.RowsAffected == 0 {
			continue
		}

		result[i] = newLiveBanner(m)
	}
	err = addExtraInfoLiveBanner(result)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	nowUnix := now.Unix()
	for i := range result {
		if result[i] != nil && nowUnix >= result[i].StartTime && nowUnix < result[i].ExpireTime {
			delBannerCache(now)
			break
		}
	}
	return result, nil
}

func delBannerCache(when time.Time) {
	err := service.Redis.Del(keys.KeyBannersMetaBanner1.Format(when.Minute() / 5)).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

func delBannerCacheWithID(bannerID int64, when time.Time) {
	var rec liverecommendedelements.Model
	// NOTICE: 这里未判断其他参数是否正确，由调用方判断
	err := service.DB.Select("start_time, expire_time").First(&rec, "id = ?", bannerID).Error
	if err != nil {
		if !servicedb.IsErrNoRows(err) {
			logger.Error(err)
		}
		return
	}
	whenUnix := when.Unix()
	if whenUnix < rec.ExpireTime && (rec.StartTime == nil || whenUnix >= *rec.StartTime) {
		delBannerCache(when)
	}
}

// SetBannerParam 设置 banner 请求参数
type SetBannerParam struct {
	Title         string `form:"title" json:"title"`
	URL           string `form:"url" json:"url"`
	ImageURL      string `form:"image_url" json:"image_url"`
	SmallImageURL string `form:"small_image_url,omitempty" json:"small_image_url,omitempty"`
	AppURL        string `form:"app_url" json:"app_url"`
	Order         int    `form:"order" json:"order"`
}

// ActionSetBanner 设置直播常驻 banner
/**
 * @api {post} /api/v2/admin/recommended/setbanner 设置直播常驻 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} title 标题
 * @apiParam {String} url 直播间 URL
 * @apiParam {String} app_url app 直播间 URL
 * @apiParam {String} image_url 图片 URL
 * @apiParam {String} [small_image_url] 小尺寸图片 URL
 * @apiParam {Number} order 顺序 (负数为置顶 banner 图，正数为正常 banner 图)
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "banners": [
 *           {
 *             "title": 原创歌曲推荐,
 *             "url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *             "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *             "small_image_url": "http://static.missevan.com/mimages/202004/01/170f319a0f47c2b7a1a33fb525e298c2235809.jpg",
 *             "app_url": "http://static.missevan.com/mimages/202004/01/bc909b64c37b95596bd2774edcf0b6f1235809.jpg",
 *             "order": 0
 *           }
 *         ]
 *       }
 *     }
 */
func ActionSetBanner(c *handler.Context) (handler.ActionResponse, error) {
	var params []SetBannerParam
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	sort.Slice(params, func(i, j int) bool {
		return params[i].Order < params[j].Order
	})
	titles := make([]string, len(params))
	bannerModels := make([]models.Banner, len(params))
	for i := range params {
		titles[i] = strings.TrimSpace(params[i].Title)
		bannerModels[i] = models.Banner{
			URL:           strings.TrimSpace(params[i].URL),
			AppURL:        strings.TrimSpace(params[i].AppURL),
			ImageURL:      strings.TrimSpace(params[i].ImageURL),
			SmallImageURL: strings.TrimSpace(params[i].SmallImageURL),
			Title:         titles[i],
			Order:         &params[i].Order,
			CreateTime:    goutil.TimeNow(),
		}
	}
	err = models.ClearAllBanners()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(params) != 0 {
		err = models.AddBanners(bannerModels)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	box := userapi.NewAdminLogBox(c)
	bannerLog := strings.Join(titles, ", ")
	intro := fmt.Sprintf("设置 banner: [%s]", bannerLog)
	box.AddAdminLog(intro, userapi.CatalogSetBanner)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return handler.M{
		"banners": bannerModels,
	}, nil
}

// ActionListBanner 管理员获取直播常驻 banner
/**
 * @api {get} /api/v2/admin/recommended/listbanner 管理员获取直播常驻 banner
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiSuccessExample Success-Response:
 *    {
 *      "code": 0,
 *      "info": {
 *        "banners": [
 *          {
 *            "url": "https://fm.uat.missevan.com/live/61687673",
 *            "app_url": "https://fm.uat.missevan.com/live/61687673",
 *            "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "small_image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "title": "super banner",
 *            "order": -1
 *          },
 *          {
 *            "url": "https://fm.uat.missevan.com/live/61687673",
 *            "app_url": "https://fm.uat.missevan.com/live/61687673",
 *            "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "small_image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "title": "测试 0",
 *            "order": 0
 *          },
 *          {
 *            "url": "https://fm.uat.missevan.com/live/61687673",
 *            "app_url": "https://fm.uat.missevan.com/live/61687673",
 *            "image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "small_image_url": "http://static.missevan.com/mimages/202004/16/1f15d04f31da46f9d4b2d143da1743ae010320.jpg",
 *            "title": "测试 1",
 *            "order": 1
 *          }
 *        ]
 *      }
 *    }
 */
func ActionListBanner(*handler.Context) (handler.ActionResponse, error) {
	banners, err := models.FindAllBanners()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return handler.M{
		"banners": banners,
	}, err
}
