package recommended

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionSoundMessageDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/delete", true, nil)
	_, err := ActionSoundMessageDelete(c)
	require.Equal(actionerrors.ErrParams, err)

	p := paramMessage{}
	c = handler.NewTestContext(http.MethodPost, "/delete", true, p)
	_, err = ActionSoundMessageDelete(c)
	require.Equal(actionerrors.ErrParams, err)

	p = paramMessage{
		Data: []message{
			{Message: "1"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/delete", true, p)
	_, err = ActionSoundMessageDelete(c)
	require.Equal(actionerrors.ErrConfirmRequired(`确定要删除如下随机推荐语吗？<br/>“[1]”`, 1, true), err)
	p.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/delete", true, p)
	resp, err := ActionSoundMessageDelete(c)
	require.NoError(err)
	assert.Equal("删除成功！", resp)
}

func TestCheckDeleteParams(t *testing.T) {
	require := require.New(t)

	p := paramMessage{}
	require.Equal(actionerrors.ErrParams, p.checkDeleteParams())
	p.rawList = []string{"xx"}
	require.NoError(p.checkDeleteParams())
}

func TestActionSoundMessageAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/add", true, nil)
	_, err := ActionSoundMessageAdd(c)
	require.Equal(actionerrors.ErrParams, err)
	p := paramMessage{}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	require.Equal(actionerrors.ErrParams, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	messagesToAppend := []string{"s1", "s2", "s3"}
	update := bson.M{"$set": bson.M{"live.message_pool": messagesToAppend}}
	_, err = params.Collection().UpdateOne(ctx, bson.M{"key": params.KeySoundRecommend}, update)
	require.NoError(err)
	update = bson.M{"$pull": bson.M{"live.message_pool": bson.M{"$in": []string{"s4", "s5", "s6", "s7"}}}}
	_, err = params.Collection().UpdateOne(ctx, bson.M{"key": params.KeySoundRecommend}, update)
	require.NoError(err)
	cacheKey := keys.KeyParams1.Format(params.KeySoundRecommend)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	p = paramMessage{
		Data: []message{
			{Message: "s4"},
			{Message: "s5"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	msg := `本次新增如下 2 条推荐语<br/>提交后将立即生效，确认要提交吗？<br/><div style="background:white;color:black;">1. s4<br/>2. s5</div>`
	require.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)

	p = paramMessage{
		Data: []message{
			{Message: "s3"},
			{Message: "s6"},
			{Message: "s7"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	msg = `本次新增如下 2 条推荐语<br/>（已自动删除 1 条重复或与线上冲突的内容）<br/>提交后将立即生效，确认要提交吗？<br/><div style="background:white;color:black;">1. s6<br/>2. s7</div>`
	require.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)

	p = paramMessage{
		Data: []message{
			{Message: "s3"},
			{Message: "s6"},
			{Message: "s7"},
			{Message: "s7"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	msg = `本次新增如下 2 条推荐语<br/>（已自动删除 2 条重复或与线上冲突的内容）<br/>提交后将立即生效，确认要提交吗？<br/><div style="background:white;color:black;">1. s6<br/>2. s7</div>`
	require.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)

	p.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	resp, err := ActionSoundMessageAdd(c)
	require.NoError(err)
	assert.Equal("提交成功！", resp)
	p = paramMessage{
		Data: []message{
			{Message: "s3"},
			{Message: "s6"},
			{Message: "s8"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	msg = `本次新增如下 1 条推荐语<br/>（已自动删除 2 条重复或与线上冲突的内容）<br/>提交后将立即生效，确认要提交吗？<br/><div style="background:white;color:black;">1. s8</div>`
	require.Equal(actionerrors.ErrConfirmRequired(msg, 1, true), err)
	p = paramMessage{
		Confirm: 1,
		Data: []message{
			{Message: "s3"},
			{Message: "s6"},
			{Message: "s7"},
		},
	}
	c = handler.NewTestContext(http.MethodPost, "/add", true, p)
	_, err = ActionSoundMessageAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("提交内容与线上重复，请检查后重试"), err)
}

func TestMessageListToInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	messagesToAppend := []string{"d1", "d2", "d3"}
	update := bson.M{"$set": bson.M{"live.message_pool": messagesToAppend}}
	_, err := params.Collection().UpdateOne(ctx, bson.M{"key": params.KeySoundRecommend}, update)
	require.NoError(err)
	cacheKey := keys.KeyParams1.Format(params.KeySoundRecommend)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	p := paramMessage{}
	r, err := p.messageListToInsert()
	require.NoError(err)
	assert.Equal([]string{}, r)

	p.filteredList = []string{"d1", "d2", "e2"}
	r, err = p.messageListToInsert()
	require.NoError(err)
	assert.Equal([]string{"e2"}, r)
}

func TestCheckAddParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := paramMessage{}
	require.Equal(actionerrors.ErrParams, p.checkAddParams())
	p.Data = []message{{""}, {"xxx"}}
	require.Equal(actionerrors.ErrParamsMsg("请先填写内容"), p.checkAddParams())
	p.Data = []message{{strings.Repeat("测", 16)}}
	require.Equal(actionerrors.ErrParamsMsg("最多输入 15 字"), p.checkAddParams())
	p.Data = []message{{strings.Repeat("测", 15)}}
	require.NoError(p.checkAddParams())
	assert.Equal([]string{strings.Repeat("测", 15)}, p.rawList)
	p.Data = make([]message, 22)
	require.Equal(actionerrors.ErrParamsMsg("一次最多新增 20 条！"), p.checkAddParams())
	p.Data = []message{{strings.Repeat("a", 26)}}
	require.Nil(p.checkAddParams())
	p.Data = []message{{strings.Repeat("a", 31)}}
	require.Equal(actionerrors.ErrParamsMsg("最多输入 15 字"), p.checkAddParams())
	p.Data = []message{{strings.Repeat(",", 31)}}
	require.Equal(actionerrors.ErrParamsMsg("最多输入 15 字"), p.checkAddParams())
}
