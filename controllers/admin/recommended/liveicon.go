package recommended

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const layoutDateTime = "2006-01-02 15:04:05"

type paramLiveIcon struct {
	RoomIDs    string             `form:"room_ids" json:"room_ids"`
	URLs       []upload.SourceURL `form:"urls" json:"urls"`
	StartTime  int64              `form:"start_time" json:"start_time"`
	ExpireTime int64              `form:"expire_time" json:"expire_time"`

	roomIDs   []int64
	urlFilter *liveupload.ImageURLFilter
}

type liveIcon struct {
	ID           int64  `gorm:"column:id" json:"-"`
	Sort         int    `gorm:"column:sort" json:"sort"`
	ElementID    int64  `gorm:"column:element_id" json:"element_id"`
	ElementType  int    `gorm:"column:element_type" json:"element_type"`
	URL          string `gorm:"column:url" json:"url"`
	StartTime    int64  `gorm:"column:start_time" json:"-"`
	ExpireTime   int64  `gorm:"column:expire_time" json:"-"`
	CreateTime   int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`
}

func (p *paramLiveIcon) check() error {
	if p.StartTime >= p.ExpireTime || p.ExpireTime <= goutil.TimeNow().Unix() {
		return actionerrors.ErrParamsMsg("请输入正确的开始结束时间")
	}

	var err error
	p.urlFilter, err = liveupload.NewImageURLFilter(p.URLs)
	if err != nil {
		return actionerrors.ErrParamsMsg(err.Error())
	}
	p.roomIDs, err = util.SplitToInt64Array(p.RoomIDs, ",")
	if err != nil || len(p.roomIDs) == 0 || len(p.roomIDs) > 100 {
		return actionerrors.ErrParams
	}
	exists, err := room.ExistsAll(p.roomIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return actionerrors.ErrParamsMsg("部分直播间不存在，请检查后重新输入")
	}
	return nil
}

// ActionAddLiveIcon 新建直播广场运营配置图标
/**
 * @api {post} /api/v2/admin/recommended/addliveicon 新建直播广场运营配置图标
 * @apiDescription 批量添加直播间广场直播间角标，每次请求最大支持 100 个
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} room_ids 房间号 半角逗号分隔, 如: 123,456
 * @apiParam {String[]} urls 图标链接地址，图片规格：360 * 240 后缀：.png、.gif、.webp
 * @apiParam {Number} start_time 开始时间, 秒级时间戳 e.g. 1578844800
 * @apiParam {Number} expire_time 过期时间, 秒级时间戳 e.g. 1578844800
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "新建成功"
 *   }
 */
func ActionAddLiveIcon(c *handler.Context) (handler.ActionResponse, error) {
	var param paramLiveIcon
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 校验参数
	err = param.check()
	if err != nil {
		return nil, err
	}

	// 上传
	targetPath, err := UploadImage(param.urlFilter, storage.PathPrefixLiveIcon)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := userapi.NewAdminLogBox(c)
	elements := make([]*liveIcon, len(param.roomIDs))
	now := goutil.TimeNow().Unix()
	for i, roomID := range param.roomIDs {
		elements[i] = &liveIcon{
			ElementType:  liverecommendedelements.ElementLiveIcon,
			ElementID:    roomID,
			URL:          targetPath,
			Sort:         1, // sort 0 表示逻辑删除
			StartTime:    param.StartTime,
			ExpireTime:   param.ExpireTime,
			CreateTime:   now,
			ModifiedTime: now,
		}

		intro := fmt.Sprintf("配置直播广场运营配置图标，房间 ID：%d，图标 URL：%s，开始时间：%s，过期时间：%s", param.roomIDs,
			targetPath, time.Unix(param.StartTime, 0).Format(layoutDateTime),
			time.Unix(param.ExpireTime, 0).Format(layoutDateTime))
		box.AddAdminLog(intro, userapi.CatalogRecommendedLiveIconAdd)
	}

	// check 检查过参数，elements 不会为空
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err = servicedb.BatchInsert(tx, liverecommendedelements.TableName(), elements); err != nil {
			return err
		}
		// 管理员操作日志
		if err = box.Send(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "新建成功", nil
}

// ActionDelLiveIcon 删除指定图标
/**
 * @api {post} /api/v2/admin/recommended/delliveicon 删除指定图标
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 这一行记录的 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (404) {Number} code 501010004
 * @apiError (404) {String} info "ID 记录未找到"
 *
 */
func ActionDelLiveIcon(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: 支持批量删除
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	model, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementLiveIcon)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if model == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	model.ParseSchemeURL()
	intro := fmt.Sprintf("删除指定图标, 房间 ID：%d, 图标的 URL：%s", model.ElementID, model.URL)
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	box.AddWithChannelID(userapi.CatalogRecommendedLiveIconDel, input.ID, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功", nil
}

type resultLiveIcon struct {
	ID int64 `json:"id"`

	RoomID   int64  `json:"room_id"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`

	URL        string `json:"url"`
	StartTime  int64  `json:"start_time"`
	ExpireTime int64  `json:"expire_time"`

	CreateTime   int64 `json:"create_time"`
	ModifiedTime int64 `json:"modified_time"`
}

type liveIconListResp struct {
	Data       []*resultLiveIcon `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionListLiveIcon 获取所有图标
/**
 * @api {get} /api/v2/admin/recommended/listliveicon 获取所有图标
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 110,
 *           "room_id": 22489473,
 *           "user_id": 10,
 *           "username": "bless",
 *           "url": "https://static.example.com/avatars/icon01.png",
 *           "expire_date": "2019-12-17 00:00:00",
 *           "create_time": 1576319637,
 *           "modified_time": 1576319637
 *         },
 *       ]
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionListLiveIcon(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	list, pa, err := liverecommendedelements.FindListElement(liverecommendedelements.ElementLiveIcon, "", p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	roomIDs := make([]int64, 0, len(list))
	results := make([]*resultLiveIcon, len(list))
	for i, v := range list {
		roomIDs = append(roomIDs, v.ElementID)

		results[i] = &resultLiveIcon{
			ID:           v.ID,
			RoomID:       v.ElementID,
			URL:          v.URL,
			StartTime:    *v.StartTime,
			ExpireTime:   v.ExpireTime,
			CreateTime:   v.CreateTime,
			ModifiedTime: v.ModifiedTime,
		}
	}

	filter := bson.M{"room_id": bson.M{"$in": roomIDs}}
	projection := bson.M{"room_id": 1, "creator_id": 1, "creator_username": 1}
	simples, err := room.ListSimples(filter, options.Find().SetProjection(projection))
	if err != nil {
		logger.Errorf("ActionListLiveIcon ListSimples error: %v", err)
		return results, nil
	}
	for i := range results {
		for _, v := range simples {
			if results[i].RoomID == v.RoomID {
				results[i].UserID = v.CreatorID
				results[i].Username = v.CreatorUsername
			}
		}
	}
	return &liveIconListResp{Data: results, Pagination: pa}, nil
}
