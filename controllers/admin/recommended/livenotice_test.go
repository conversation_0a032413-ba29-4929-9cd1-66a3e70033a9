package recommended

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestLiveNoticeLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	input := paramSetLiveNotice{
		RoomIDs:        "",
		liveNoticeInfo: liveNoticeInfo{Limit: 10, Title: "年度冠军"},
	}

	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, nil)
	param := paramSetLiveNotice{c: c}
	err := param.load()
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	assert.EqualError(err, "请输入房间 ID")

	input.RoomIDs = "369892,2a"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	assert.Equal(actionerrors.ErrParams, err)

	input.RoomIDs = "369892,12345543,1000101"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	require.EqualError(err, "房间 12345543,1000101 不存在")

	input.RoomIDs = "369892,100000006"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	require.NoError(err)

	input.BubbleID = -100
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	require.EqualError(err, "气泡不存在")

	input.BubbleID = 21406
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	param.c = c
	err = param.load()
	require.NoError(err)
}

func TestLiveNoticeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(369892)

	key := keys.KeyRoomsBroadcastOpen0.Format()
	fieldKey := keys.FieldRewardUsedCounter1.Format(testRoomID)
	err := service.Redis.HMSet(key, map[string]interface{}{
		fieldKey: 1,
	}).Err()
	require.NoError(err)

	param := paramSetLiveNotice{
		liveNoticeInfo: liveNoticeInfo{Limit: 10, Title: "年度冠军"},
		listRoomID:     []int64{testRoomID},
	}
	err = param.save()
	require.NoError(err)
	result, err := service.Redis.HMGet(keys.KeyRoomsBroadcastOpen0.Format(),
		keys.FieldRewardRoom1.Format(369892)).Result()
	require.NoError(err)
	var re liveNoticeInfo
	require.NoError(json.Unmarshal([]byte(result[0].(string)), &re))
	assert.Equal("年度冠军", re.Title)
	assert.Equal(10, re.Limit)

	exists, err := service.Redis.HExists(key, fieldKey).Result()
	require.NoError(err)
	assert.False(exists)
}

func TestActionSetLiveNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	input := paramSetLiveNotice{
		RoomIDs:        "369892,12345678901",
		liveNoticeInfo: liveNoticeInfo{Limit: 10, Title: "年度冠军", BubbleID: 128},
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	_, err := ActionSetLiveNotice(c)
	assert.EqualError(err, "房间 12345678901 不存在")

	input.RoomIDs = "369892,100000006"
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/recommended/setlivenotice", true, input)
	_, err = ActionSetLiveNotice(c)
	require.NoError(err)
	result, err := service.Redis.HMGet(keys.KeyRoomsBroadcastOpen0.Format(),
		keys.FieldRewardRoom1.Format(369892)).Result()
	require.NoError(err)
	var re liveNoticeInfo
	require.NoError(json.Unmarshal([]byte(result[0].(string)), &re))
	assert.Equal("年度冠军", re.Title)
	assert.Equal(10, re.Limit)
	assert.EqualValues(128, re.BubbleID)
}

func TestActionListLiveNotice(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	c := handler.NewTestContext(http.MethodGet, "/?p=1&page_size=1", true, nil)
	r, err := ActionListLiveNotice(c)
	require.NoError(err)
	resp := r.(*respListLiveNotice)
	assert.Len(resp.Data, 1)

	c = handler.NewTestContext(http.MethodGet, "/?p=1000&page_size=100", true, nil)
	r, err = ActionListLiveNotice(c)
	require.NoError(err)
	resp = r.(*respListLiveNotice)
	assert.Len(resp.Data, 0)
}

func TestHScanAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result := hscanAll(0, nil)
	require.NotEmpty(result)
	assert.NotEmpty(result[0].RoomID)
	assert.NotEmpty(result[0].Title)
}
