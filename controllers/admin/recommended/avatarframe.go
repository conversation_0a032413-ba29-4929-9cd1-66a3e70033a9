package recommended

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type avatarFrameParam struct {
	UserIDs    string             `form:"user_ids" json:"user_ids"`
	URLs       []upload.SourceURL `form:"urls" json:"urls"`
	StartTime  int64              `form:"start_time" json:"start_time"`
	ExpireTime int64              `form:"expire_time" json:"expire_time"`

	userIDs   []int64
	urlFilter *liveupload.ImageURLFilter
}

type avatarFrameInfo struct {
	ID int64 `json:"id"`

	UserID   int64  `json:"user_id"`
	UserName string `json:"username"`
	URL      string `json:"url"`

	StartTime  int64 `json:"start_time"`
	ExpireTime int64 `json:"expire_time"`
	CreateTime int64 `json:"create_time"`
}

type avatarFrame struct {
	ID           int64  `gorm:"column:id" json:"-"`
	Sort         int    `gorm:"column:sort" json:"sort"`
	ElementID    int64  `gorm:"column:element_id" json:"element_id"`
	ElementType  int    `gorm:"column:element_type" json:"element_type"`
	URL          string `gorm:"column:url" json:"url"`
	StartTime    int64  `gorm:"column:start_time" json:"-"`
	ExpireTime   int64  `gorm:"column:expire_time" json:"-"`
	CreateTime   int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`
}

func (p *avatarFrameParam) check() error {
	if p.StartTime >= p.ExpireTime || p.ExpireTime <= goutil.TimeNow().Unix() {
		return actionerrors.ErrParamsMsg("请输入正确的开始结束时间")
	}
	for i := range p.URLs {
		if ok := storage.CheckStorage(p.URLs[i]); !ok {
			return actionerrors.ErrParamsMsg("请求上传的资源不被支持，请重新选择")
		}
	}

	var err error
	p.urlFilter, err = liveupload.NewImageURLFilter(p.URLs)
	if err != nil {
		return actionerrors.ErrParamsMsg(err.Error())
	}

	p.userIDs, err = util.SplitToInt64Array(p.UserIDs, ",")
	if err != nil || len(p.userIDs) == 0 || len(p.userIDs) > 100 {
		return actionerrors.ErrParams
	}

	exists, err := mowangskuser.ExistsAll(p.userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return actionerrors.ErrParamsMsg("部分用户不存在，请检查后重新输入")
	}

	return nil
}

// ActionAddAvatarFrame 添加用户头像框
/**
 * @api {post} /api/v2/admin/recommended/addavatarframe 添加用户头像框
 * @apiDescription 批量为用户添加头像框，每次请求最大支持 100 个
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {String} user_ids 用户 IDs 半角逗号分隔, 如: 123,456
 * @apiParam {String[]} urls 头像框链接地址，图片规格 240 * 240 后缀：.png、.gif、.webp
 * @apiParam {Number} start_time 开始时间, 秒级时间戳 e.g. 1578844800
 * @apiParam {Number} expire_time 过期时间, 秒级时间戳 e.g. 1578844800
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "新建成功"
 *   }
 */
func ActionAddAvatarFrame(c *handler.Context) (handler.ActionResponse, error) {
	var input avatarFrameParam
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 校验参数
	err = input.check()
	if err != nil {
		return nil, err
	}

	// 上传
	targetPath, err := UploadImage(input.urlFilter, storage.PathPrefixAvatarFrame)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	now := goutil.TimeNow()
	box := userapi.NewAdminLogBox(c)
	elements := make([]*avatarFrame, len(input.userIDs))
	for i, userID := range input.userIDs {
		elements[i] = &avatarFrame{
			ElementType:  liverecommendedelements.ElementAvatarFrame,
			ElementID:    userID,
			URL:          targetPath, // 确保写入数据库时，有 webp 则写入 webp，其他时候存入其他的类型
			Sort:         1,          // sort 0 表示逻辑删除
			ExpireTime:   input.ExpireTime,
			StartTime:    input.StartTime,
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
		}
		intro := fmt.Sprintf("配置头像框，用户 ID：%d，头像框地址：%s，开始时间：%s，过期时间：%s", userID, targetPath,
			time.Unix(input.StartTime, 0).Format(layoutDateTime), time.Unix(input.ExpireTime, 0).Format(layoutDateTime))
		box.AddAdminLog(intro, userapi.CatalogRecommendedAvatarFrameAdd)
	}
	// check 检查过参数，elements 不会为空
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err = servicedb.BatchInsert(tx, liverecommendedelements.TableName(), elements); err != nil {
			return err
		}
		// 管理员操作日志
		if err = box.Send(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "新建成功", nil
}

// ActionDelAvatarFrame 删除指定头像框
/**
 * @api {post} /api/v2/admin/recommended/delavatarframe 删除指定头像框
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} id 记录 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (404) {Number} code 501010004
 * @apiError (404) {String} info "ID 记录未找到"
 *
 */
func ActionDelAvatarFrame(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: 支持批量删除
	var input struct {
		ID int64 `form:"id" json:"id"`
	}
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	model, err := liverecommendedelements.FindOneAndDelete(input.ID, liverecommendedelements.ElementAvatarFrame)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if model == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("删除指定头像框, 用户 ID: %d, 头像框链接: %s", model.ElementID, model.URL)
	box.AddWithChannelID(userapi.CatalogRecommendedAvatarFrameDel, input.ID, intro)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功", nil
}

type avatarFrameListResp struct {
	Data       []*avatarFrameInfo `json:"data"`
	Pagination goutil.Pagination  `json:"pagination"`
}

// ActionListAvatarFrame 获取头像框
/**
 * @api {get} /api/v2/admin/recommended/listavatarframe 获取头像框
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/recommended
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 110,
 *           "user_id": 22489473,
 *           "username": "bless",
 *           "url": "https://static.example.com/avatars/icon01.png",
 *           "expire_date": "2019-12-17 00:00:00",
 *           "create_time": 1594695035
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionListAvatarFrame(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// sort 默认按照 create_time 倒序
	list, pa, err := liverecommendedelements.FindListElement(liverecommendedelements.ElementAvatarFrame, "create_time DESC", p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	userIDs := make([]int64, len(list))
	for i := 0; i < len(list); i++ {
		userIDs[i] = list[i].ElementID
	}
	// TODO:修改查询方式，应该把 live_recommended_elements 和 mowangskuser 进行联表查询而不是先把 userID 列表算出再去 mowangskuser 查询
	usersMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	avatarFrameList := make([]*avatarFrameInfo, len(list))
	for i, v := range list {
		var username string
		if usersMap[v.ElementID] != nil {
			username = usersMap[v.ElementID].Username
		}
		avatarFrameList[i] = &avatarFrameInfo{
			ID:         v.ID,
			UserID:     v.ElementID,
			UserName:   username,
			URL:        v.URL,
			StartTime:  *v.StartTime,
			ExpireTime: v.ExpireTime,
			CreateTime: v.CreateTime,
		}
	}
	return &avatarFrameListResp{Data: avatarFrameList, Pagination: pa}, nil
}

func parseExpireTime(expireDate string) (time.Time, error) {
	expireTime, err := time.ParseInLocation(layoutDateTime, expireDate, time.Local)
	if err != nil {
		return time.Time{}, actionerrors.ErrParams
	}
	return expireTime, nil
}
