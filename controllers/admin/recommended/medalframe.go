package recommended

import (
	"fmt"
	"html"
	"strings"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// 粉丝勋章的类型和文件后缀
const (
	typeMedalFrameStatic  = 0
	typeMedalFrameDynamic = 1

	suffixStaticPNG   = "0_9_0_54.png"  // 静态勋章资源的文件后缀
	suffixDynamicWEBP = "0_9_0_55.webp" // 动态勋章资源的 webp 文件后缀
	suffixDynamicPNG  = "0_9_0_55.png"  // 动态勋章资源的 apng 文件后缀
)

type medalFrameAddParam struct {
	RoomID     int64  `form:"room_id" json:"room_id"`
	Type       int8   `form:"type" json:"type"`         // 粉丝勋章类型 0-静态 1-动态
	DirName    string `form:"dir_name" json:"dir_name"` // 勋章上传到的文件夹
	StartTime  int64  `form:"start_time" json:"start_time"`
	ExpireTime int64  `form:"expire_time" json:"expire_time"`
	Confirm    int64  `form:"confirm" json:"confirm"`

	r *room.Room
}

// 校验入参
func (m *medalFrameAddParam) checkParams() error {
	// 1. 检查各字段的值非空
	if m.RoomID <= 0 {
		return actionerrors.ErrParamsMsg("请输入房间 ID")
	}
	if m.Type != typeMedalFrameStatic && m.Type != typeMedalFrameDynamic {
		return actionerrors.ErrParamsMsg("请选择正确的勋章类型")
	}
	m.DirName = strings.TrimSpace(m.DirName)
	if m.DirName == "" {
		return actionerrors.ErrParamsMsg("请输入文件夹名")
	}
	if m.StartTime <= 0 || m.ExpireTime <= 0 || m.StartTime >= m.ExpireTime {
		return actionerrors.ErrParamsMsg("输入时间有误")
	}

	// 2. 检查房间存在
	r, err := room.Find(m.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	m.r = r
	return nil
}

// checkDir 检查文件夹下是否存在用户勋章资源
func (m *medalFrameAddParam) checkDir() error {
	bucket, err := service.Storage.Get(config.DefaultCDNScheme)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 静态勋章校验 png 文件存在，动态勋章校验 webp 和 apng 文件都存在
	suffixes := make([]string, 0, 2)
	if m.Type == typeMedalFrameStatic {
		suffixes = append(suffixes, fmt.Sprintf("level01_%s", suffixStaticPNG))
	}
	if m.Type == typeMedalFrameDynamic {
		suffixes = append(suffixes, fmt.Sprintf("level01_%s", suffixDynamicPNG))
		suffixes = append(suffixes, fmt.Sprintf("level01_%s", suffixDynamicWEBP))
	}

	for _, suffix := range suffixes {
		objKey := fmt.Sprintf("%s%s/%s",
			storage.PathPrefixMedalFrame,
			m.DirName,
			suffix,
		)
		_, err = bucket.GetObject(objKey)
		if err != nil {
			return actionerrors.ErrParamsMsg("获取不到文件夹下用户勋章资源")
		}
	}
	return nil
}

// ActionMedalFrameAdd 新增主播粉丝勋章资源
/**
 * @api {post} /api/v2/admin/recommended/medalframe/add 新增主播粉丝勋章资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {number=0,1} [type=0] 粉丝勋章类型 0：静态，1：动态
 * @apiParam {String} dir_name 粉丝勋章上传到的文件夹名
 * @apiParam {Number} start_time 开始日期时间戳，单位：秒
 * @apiParam {Number} expire_time 结束日期时间戳，单位：秒
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success"
 *     "data": null
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionMedalFrameAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	var param medalFrameAddParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	err = param.checkParams()
	if err != nil {
		return nil, "", err
	}
	err = param.checkDir()
	if err != nil {
		return nil, "", err
	}

	if param.Confirm == 0 {
		// 弹窗提示确认消息
		return nil, "", actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确认为该主播添加专属粉丝勋章吗？<br>房间号：%d<br>主播昵称：%s", param.r.RoomID, html.EscapeString(param.r.CreatorUsername)), 1, true)
	}

	var url string
	if param.Type == typeMedalFrameStatic {
		url = fmt.Sprintf("%s://%s%s/level${level}_%s", config.DefaultCDNScheme, storage.PathPrefixMedalFrame, param.DirName, suffixStaticPNG)
	} else {
		url = fmt.Sprintf("%s://%s%s/level${level}_%s", config.DefaultCDNScheme, storage.PathPrefixMedalFrame, param.DirName, suffixDynamicWEBP)
	}
	// 新建粉丝勋章记录
	element := liverecommendedelements.LiveRecommendedElements{
		Sort:        1,
		ElementID:   param.r.RoomID,
		ElementType: liverecommendedelements.ElementMedalFrame,
		URL:         url,
		StartTime:   &param.StartTime,
		ExpireTime:  param.ExpireTime,
	}
	if err = service.DB.Create(&element).Error; err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageRecommendedElements, fmt.Sprintf("配置直播间定制粉丝勋章，id：%d，房间号：%d", element.ID, element.ElementID))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "success", nil
}

// ActionMedalFrameDel 删除主播粉丝勋章资源
/**
 * @api {post} /api/v2/admin/recommended/medalframe/del 删除主播粉丝勋章资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id recommended element ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success"
 *     "data": null
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionMedalFrameDel(c *handler.Context) (handler.ActionResponse, string, error) {
	var param struct {
		ID      int64 `form:"id" json:"id"`
		Confirm int   `form:"confirm" json:"confirm"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	if param.Confirm == 0 {
		frame, err := liverecommendedelements.FindElement(param.ID, liverecommendedelements.ElementMedalFrame)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if frame == nil {
			return nil, "", actionerrors.ErrNotFound("粉丝勋章 ID 输入有误")
		}
		r, err := room.Find(frame.ElementID)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return nil, "", actionerrors.ErrCannotFindRoom
		}
		return nil, "", actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确认删除该主播的专属粉丝勋章吗？<br>房间号：%d<br>主播昵称：%s", r.RoomID, html.EscapeString(r.CreatorUsername)), 1, true)
	}
	element, err := liverecommendedelements.FindOneAndDelete(param.ID, liverecommendedelements.ElementMedalFrame)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, "", actionerrors.ErrNotFound("粉丝勋章 ID 输入有误")
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageRecommendedElements, fmt.Sprintf("删除直播间定制粉丝勋章，id：%d，房间号：%d", param.ID, element.ElementID))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "success", nil
}
