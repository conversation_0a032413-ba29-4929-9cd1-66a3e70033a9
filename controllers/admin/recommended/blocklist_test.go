package recommended

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/guildrecommendblocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestBlockListRoomAddAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(12)
	r, err := room.FindOne(bson.M{"creator_id": testUserID}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
	require.NoError(guildrecommendblocklist.Remove(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID))
	param := handler.M{"creator_id": testUserID, "reason": "test"}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListRoomAdd(c)
	require.NoError(err)
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	require.NoError(err)
	assert.True(exists)

	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListRoomAdd(c)
	assert.EqualError(err, "当前主播已在黑名单中")

	param["creator_id"] = -10
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListRoomRemove(c)
	assert.Equal(actionerrors.ErrParams, err)

	param["creator_id"] = testUserID
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListRoomRemove(c)
	require.NoError(err)
	exists, err = guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendLive, r.RoomID)
	require.NoError(err)
	assert.False(exists)

	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListRoomRemove(c)
	assert.EqualError(err, "当前主播不在黑名单中")
}

func TestBlockListGuildAddAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGuildID := int64(3)
	require.NoError(guildrecommendblocklist.Remove(guildrecommendblocklist.TypeElementRecommendGuild, testGuildID))
	param := handler.M{"guild_id": testGuildID, "reason": "test"}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionBlockListGuildAdd(c)
	require.NoError(err)
	exists, err := guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendGuild, testGuildID)
	require.NoError(err)
	assert.True(exists)

	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListGuildAdd(c)
	assert.EqualError(err, "当前公会已在黑名单中")

	param["guild_id"] = -10
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListGuildAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	param["guild_id"] = testGuildID
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListGuildRemove(c)
	require.NoError(err)
	exists, err = guildrecommendblocklist.Exists(guildrecommendblocklist.TypeElementRecommendGuild, testGuildID)
	require.NoError(err)
	assert.False(exists)

	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBlockListGuildRemove(c)
	assert.EqualError(err, "当前公会不在黑名单中")
}
