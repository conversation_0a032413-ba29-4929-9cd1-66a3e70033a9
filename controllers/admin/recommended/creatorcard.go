package recommended

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCreatorCardAdd 新增主播信息背景
/**
 * @api {post} /api/v2/admin/recommended/creatorcard/add 新增主播信息背景
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} room_ids 房间 ID
 * @apiParam {String[]} urls 上传图片
 * @apiParam {String} clip 拉伸参数
 * @apiParam {Number} start_time 开始日期时间戳，单位：秒
 * @apiParam {Number} expire_time 结束日期时间戳，单位：秒
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_ids": "2233,223344", // 使用逗号分割多个房间 ID，最多支持 100 个房间批量更新
 *     "urls": ["https://www.uat.missevan.com/files/2023-02-28/4d68f69eaaf82e8573df01baa1706892.png"],
 *     "clip": "1_1_1_1", // top_right_bottom_left
 *     "start_time": 1677643200, // 单位：秒
 *     "expire_time": 1687643200 // 单位：秒
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionCreatorCardAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomIDs    string             `json:"room_ids"`
		URLs       []upload.SourceURL `json:"urls"`
		Clip       string             `json:"clip"`
		StartTime  int64              `json:"start_time"`
		ExpireTime int64              `json:"expire_time"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	ok := util.IsValidClip(param.Clip)
	if !ok {
		return nil, actionerrors.ErrParams
	}
	roomIDs, err := util.SplitToInt64Array(param.RoomIDs, ",")
	if err != nil || len(roomIDs) == 0 || len(roomIDs) > 100 {
		return nil, actionerrors.ErrParams
	}
	exists, err := room.ExistsAll(roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrParamsMsg("部分直播间不存在，请检查后重新输入")
	}
	nowUnix := goutil.TimeNow().Unix()
	if param.StartTime >= param.ExpireTime || param.ExpireTime <= nowUnix {
		return nil, actionerrors.ErrParamsMsg("请输入正确的开始结束时间")
	}

	filter, err := liveupload.NewImageURLFilter(param.URLs)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(err.Error())
	}
	targetPath, err := UploadImageWithSuffix(filter, storage.PathPrefixCreatorcard, param.Clip)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	elements := make([]*liverecommendedelements.LiveRecommendedElements, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		elements = append(elements, &liverecommendedelements.LiveRecommendedElements{
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			Sort:         1,
			ElementID:    roomID,
			ElementType:  liverecommendedelements.ElementCreatorCard,
			URL:          targetPath,
			StartTime:    goutil.NewInt64(param.StartTime),
			ExpireTime:   param.ExpireTime,
		})
	}
	err = servicedb.BatchInsert(service.DB, liverecommendedelements.TableName(), elements)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageRecommendedElements, fmt.Sprintf("新增主播信息背景，房间号：%s，背景图片：%s", param.RoomIDs, targetPath))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}

// ActionCreatorCardDelete 删除主播信息背景
/**
 * @api {post} /api/v2/admin/recommended/creatorcard/delete 删除主播信息背景
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {Number} id recommended_element ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionCreatorCardDelete(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		ID int64 `json:"id"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	element, err := liverecommendedelements.FindOneAndDelete(param.ID, liverecommendedelements.ElementCreatorCard)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if element == nil {
		return nil, actionerrors.ErrNotFound("主播信息背景不存在")
	}
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageRecommendedElements, fmt.Sprintf("删除主播信息背景，id：%d，房间号：%d", param.ID, element.ElementID))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}
