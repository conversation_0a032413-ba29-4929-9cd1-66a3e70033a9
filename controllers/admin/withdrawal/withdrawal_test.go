package withdrawal

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestActionSetMonthLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试取消限制
	// 测试参数错误
	param := bson.M{
		"creator_id": -1,
		"type":       typeCancel,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionSetMonthLimit(c)
	assert.EqualError(err, "参数错误")

	// 测试主播不存在
	param = bson.M{
		"creator_id": 999,
		"type":       typeCancel,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMonthLimit(c)
	assert.EqualError(err, "主播不存在")

	require.NoError(service.Redis.SRem(keys.KeyUserIDsWithoutWithdrawLevelOneLimit0.Format(), 10).Err())
	// 测试取消成功
	param = bson.M{
		"creator_id": 10,
		"type":       typeCancel,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMonthLimit(c)
	require.NoError(err)

	// 测试无需取消
	param = bson.M{
		"creator_id": 10,
		"type":       typeCancel,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMonthLimit(c)
	assert.EqualError(err, "无需取消")

	// 测试恢复限制
	// 测试恢复成功
	param = bson.M{
		"creator_id": 10,
		"type":       typeRestore,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMonthLimit(c)
	require.NoError(err)

	// 测试已有限制
	param = bson.M{
		"creator_id": 3400560,
		"type":       typeRestore,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMonthLimit(c)
	assert.EqualError(err, "已有限制")
}

func TestActionSetMin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试必须大于等于 100
	param := bson.M{
		"price": 10,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err := ActionSetMin(c)
	assert.EqualError(err, "必须大于等于 100")

	require.NoError(service.Redis.Del(keys.KeyMinWithdrawValueLimit0.Format()).Err())
	// 测试设置成功
	param = bson.M{
		"price": 100,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionSetMin(c)
	require.NoError(err)
}
