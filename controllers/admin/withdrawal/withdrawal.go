package withdrawal

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	typeCancel  = iota + 1 // 取消限制
	typeRestore            // 恢复限制
)

// minPrice 最低提现金额
const minPrice = 100

// ActionSetMonthLimit 取消或恢复小于等于十万元月提现上限
/**
 * @api {post} /api/v2/admin/withdrawal/setmonthlimit 取消或恢复小于等于十万元月提现上限
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/withdrawal
 *
 * @apiParam {Number} creator_id 主播 ID
 * @apiParam {Number} type 类型 1: 取消限制 2: 恢复限制
 * @apiParamExample {json} Request-Example:
 *   {
 *     "creator_id": 346286,
 *     "type": 1
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "恢复成功"
 *   }
 */
func ActionSetMonthLimit(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		CreatorID int64 `form:"creator_id" json:"creator_id"`
		Type      int   `form:"type" json:"type"`
	}
	err := c.Bind(&params)
	if err != nil || params.CreatorID <= 0 || !util.HasElem([]int{typeCancel, typeRestore}, params.Type) {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"creator_id": params.CreatorID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrParamsMsg("主播不存在")
	}

	var message string
	key := keys.KeyUserIDsWithoutWithdrawLevelOneLimit0.Format()
	exists, err := service.Redis.SIsMember(key, params.CreatorID).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	switch params.Type {
	case typeCancel:
		if exists {
			return nil, actionerrors.NewErrForbidden("无需取消")
		}
		err := service.Redis.SAdd(key, params.CreatorID).Err()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		message = "取消"
	case typeRestore:
		if !exists {
			return nil, actionerrors.NewErrForbidden("已有限制")
		}
		err := service.Redis.SRem(key, params.CreatorID).Err()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		message = "恢复"
	}
	adminLogs := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("%s主播 %d 月提现总额小于等于十万元上限成功", message, params.CreatorID)
	adminLogs.AddAdminLog(intro, userapi.CatalogLiveSetWithdrawalLimit)
	return message + "成功", nil
}

// ActionSetMin 设置最低提现金额
/**
 * @api {post} /api/v2/admin/withdrawal/setmin 设置最低提现金额
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/withdrawal
 *
 * @apiParam {Number} price 金额（单位：元，控制所有主播的最低提现金额）
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 */
func ActionSetMin(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		Price int64 `form:"price" json:"price"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if params.Price < minPrice {
		return nil, actionerrors.NewErrForbidden(fmt.Sprintf("必须大于等于 %d", minPrice))
	}
	err = service.Redis.Set(keys.KeyMinWithdrawValueLimit0.Format(), params.Price, 0).Err()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	intro := fmt.Sprintf("设置最低提现金额为 %d 元成功", params.Price)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddAdminLog(intro, userapi.CatalogLiveSetWithdrawalLimit)
	return "设置成功", nil
}
