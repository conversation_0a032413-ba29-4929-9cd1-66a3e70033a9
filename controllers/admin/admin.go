package admin

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/controllers/activity"
	"github.com/MiaoSiLa/live-service/controllers/admin/chatroom"
	"github.com/MiaoSiLa/live-service/controllers/admin/dailytask"
	"github.com/MiaoSiLa/live-service/controllers/admin/diygifts"
	"github.com/MiaoSiLa/live-service/controllers/admin/exclusive"
	"github.com/MiaoSiLa/live-service/controllers/admin/giftwall"
	adminguild "github.com/MiaoSiLa/live-service/controllers/admin/guild"
	"github.com/MiaoSiLa/live-service/controllers/admin/liveluckygift"
	"github.com/MiaoSiLa/live-service/controllers/admin/playback"
	"github.com/MiaoSiLa/live-service/controllers/admin/recommended"
	"github.com/MiaoSiLa/live-service/controllers/admin/sticker"
	"github.com/MiaoSiLa/live-service/controllers/admin/user"
	"github.com/MiaoSiLa/live-service/controllers/admin/userranklove"
	"github.com/MiaoSiLa/live-service/controllers/admin/withdrawal"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/luckybag"
	"github.com/MiaoSiLa/live-service/controllers/guild"
	"github.com/MiaoSiLa/live-service/controllers/noble"
	"github.com/MiaoSiLa/live-service/controllers/webuser"
	"github.com/MiaoSiLa/live-service/middlewares"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

const (
	roleOperationSuperiorPlatformEdit  role.Role = "operation_superior_platform_edit"  // 运营 - 资深平台编辑
	roleOperationSeniorPlatformEdit    role.Role = "operation_senior_platform_edit"    // 运营 - 高级平台编辑
	roleOperationSeniorPlatformSupport role.Role = "operation_senior_platform_support" // 运营 - 高级平台支持
	roleOperationSettlementSupport     role.Role = "operation_settlement_support"      // 运营 - 结算支持
	roleOperationEventIntern           role.Role = "operation_event_intern"            // 运营 - 活动实习生
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "admin",
		SubHandlers: []handler.Handler{
			activityHandler(),
			chatroomHandler(),
			guildHandler(),
			nobleHandler(),
			playbackHandler(),
			recommendedHandler(),
			userHandler(),
			exclusiveHandler(), // 三方独家主播后台相关
			withdrawalHandler(),
			diyGiftHandler(),
			giftWallHandler(),
		},
	}
}

// HandlerV2 返回 handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "admin",
		SubHandlers: []handler.HandlerV2{
			luckyBagHandler(),
			chatroomHandlerV2(),
			nobleHandlerV2(),
			recommendedHandlerV2(),
			stickerHandlerV2(),
			luckyGiftHandlerV2(),
			dailyTaskHandlerV2(),
		},
	}
}

func userHandler() handler.Handler {
	return handler.Handler{
		SubHandlers: []handler.Handler{
			{
				Name:        "user",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
				Actions: map[string]*handler.Action{
					"rank/love/get":     handler.NewAction(handler.GET, userranklove.ActionGet, true),
					"rank/love/set":     handler.NewAction(handler.POST, userranklove.ActionSet, true),
					"rank/love/publish": handler.NewAction(handler.POST, userranklove.ActionPublish, true),

					"wall/send": handler.NewAction(handler.POST, webuser.ActionAdminWallSend, true),

					"givegift":         handler.NewAction(handler.POST, user.ActionGiveGift, true),
					"cleargift":        handler.NewAction(handler.POST, user.ActionClearGift, true),
					"givecreatorgift":  handler.NewAction(handler.POST, user.ActionGiveCreatorGift, true),
					"clearcreatorgift": handler.NewAction(handler.POST, user.ActionClearCreatorGift, true),

					"reissuegift": handler.NewAction(handler.POST, user.ActionReissueGift, true),

					"appearance/manage": handler.NewAction(handler.POST, user.ActionManageUserAppearance, true),
					"appearance/sync":   handler.NewAction(handler.POST, user.ActionSyncUserAppearance, true),

					"recommendnum/update": handler.NewAction(handler.POST, user.ActionRecommendNumUpdate, true),

					"medal/point/add": handler.NewAction(handler.POST, user.ActionMedalPointAdd, true),
				},
			},
			{
				Name: "user",
				// 额外允许「审核 - 直播组」访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditLive)},
				Actions: map[string]*handler.Action{
					"ban/add":    handler.NewAction(handler.POST, user.ActionBanAdd, true),
					"ban/remove": handler.NewAction(handler.POST, user.ActionBanRemove, true),
				},
			},
		},
	}
}

func guildHandler() handler.Handler {
	adminList := handler.Handler{
		Name:        "",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveFinance, role.LiveAdmin, role.GuildAccountAdmin)},
		Actions: map[string]*handler.Action{
			"adminlist": handler.NewAction(handler.GET, guild.ActionAdminlist, true), // 超管审核公会列表
		},
	}

	// 审核
	audit := handler.Handler{
		Name:        "",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveAdmin, role.GuildAccountAdmin)},
		Actions: map[string]*handler.Action{
			"adminpass":   handler.NewAction(handler.POST, guild.ActionAdminPass, true),   // 超管通过公会创建
			"adminreject": handler.NewAction(handler.POST, guild.ActionAdminreject, true), // 超管拒绝公会创建
		},
	}

	update := handler.Handler{
		Name:        "",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveAdmin, roleOperationSettlementSupport)},
		Actions: map[string]*handler.Action{
			"deleteguild":            handler.NewAction(handler.POST, guild.ActionDeleteGuild, true),          // 注销公会
			"updateguild":            handler.NewAction(handler.POST, guild.ActionUpdateGuild, true),          // 更新公会
			"guild-transfer-creator": handler.NewAction(handler.POST, guild.ActionGuildTransferCreator, true), // 转移公会
		},
	}

	applyment := handler.Handler{
		Name:        "applyment",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveAdmin, role.GuildAccountAdmin)},
		Actions: map[string]*handler.Action{
			"terminate/batch-create": handler.NewAction(handler.POST, guild.ActionApplyTerminateBatchCreate, true), // 平台批量发起解约公会主播的申请
		},
	}

	other := handler.Handler{
		Name:        "",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"adminmanage":     handler.NewAction(handler.GET, guild.ActionAdminmanage, true),     // 超管公会管理列表
			"guildrate":       handler.NewAction(handler.POST, guild.ActionGuildrate, true),      // 超管公会管理列表
			"guildsprofit":    handler.NewAction(handler.GET, guild.ActionGuildsProfit, true),    // 平台获取所有公会收益信息
			"guildprofitview": handler.NewAction(handler.GET, guild.ActionGuildProfitView, true), // 平台获取公会收益详情
		},
	}

	recommendHandler := handler.Handler{
		SubHandlers: []handler.Handler{
			{
				Name:        "recommend",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
				Actions: map[string]*handler.Action{
					"squarehot/vacancy/list":   handler.NewAction(handler.GET, guild.ActionSquareHotVacancyList, true),    // 查看推荐次数
					"squarehot/vacancy/update": handler.NewAction(handler.POST, guild.ActionSquareHotVacancyUpdate, true), // 更新推荐次数
					"schedule/list":            handler.NewAction(handler.GET, adminguild.ActionScheduleReviewList, true),
					"schedule/pass":            handler.NewAction(handler.POST, adminguild.ActionScheduleReviewPass, true),
					"schedule/refuse":          handler.NewAction(handler.POST, adminguild.ActionScheduleReviewRefuse, true),
				},
			},
			{
				Name: "recommend",
				// 额外允许「审核 - 直播组」访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditLive)},
				Actions: map[string]*handler.Action{
					"banner/review/list":   handler.NewAction(handler.GET, guild.ActionReviewBannerList, true),
					"banner/review/pass":   handler.NewAction(handler.POST, guild.ActionReviewBannerPass, true),
					"banner/review/reject": handler.NewAction(handler.POST, guild.ActionReviewBannerReject, true),
					"banner/count":         handler.NewAction(handler.GET, guild.ActionGuildBannerCount, true),
				},
			},
		},
	}

	return handler.Handler{
		Name:        "guild",
		SubHandlers: []handler.Handler{adminList, audit, update, applyment, other, recommendHandler},
	}
}

func chatroomHandler() handler.Handler {
	return handler.Handler{
		SubHandlers: []handler.Handler{
			{
				Name:        "chatroom",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
				Actions: map[string]*handler.Action{
					"setmedal": handler.NewAction(handler.POST, chatroom.ActionSetmedal, true),

					"medal/add-multi":    handler.NewAction(handler.POST, chatroom.ActionMedalMultiAdd, true),
					"medal/remove-multi": handler.NewAction(handler.POST, chatroom.ActionMedalMultiRemove, true),

					"channel/set":          handler.NewAction(handler.POST, chatroom.ActionChannelSet, true),
					"channel/provider/set": handler.NewAction(handler.POST, chatroom.ActionChannelProviderSet, true),

					"connect/provider/set": handler.NewAction(handler.POST, chatroom.ActionConnectProviderSet, true),

					"score/set": handler.NewAction(handler.POST, chatroom.ActionExtraScoreSet, true),

					"playback/priority/set": handler.NewAction(handler.POST, chatroom.ActionPlaybackPrioritySet, true),

					"gift/hide":            handler.NewAction(handler.POST, chatroom.ActionGiftHide, true),
					"gift/reorder":         handler.NewAction(handler.POST, chatroom.ActionGiftReorder, true),
					"gift/setorder":        handler.NewAction(handler.POST, chatroom.ActionGiftSetOrder, true),
					"gift/custom/add":      handler.NewAction(handler.POST, chatroom.ActionGiftCustomAdd, true),
					"gift/room/custom/add": handler.NewAction(handler.POST, chatroom.ActionGiftRoomCustomAdd, true),

					"tag/set":        handler.NewAction(handler.POST, chatroom.ActionTagSet, true),
					"tag/editstatus": handler.NewAction(handler.POST, chatroom.ActionTagEditStatus, true),
					"tag/sortorder":  handler.NewAction(handler.POST, chatroom.ActionTagSortOrder, true),

					"tag/room/add": handler.NewAction(handler.POST, chatroom.ActionTagRoomAdd, true),
					"tag/room/del": handler.NewAction(handler.POST, chatroom.ActionTagRoomDel, true),

					"speak/set": handler.NewAction(handler.POST, chatroom.ActionSpeakSet, true),
					"speak/del": handler.NewAction(handler.POST, chatroom.ActionSpeakDel, true),

					"tag/allowlist/add":    handler.NewAction(handler.POST, chatroom.ActionTagAllowListAdd, true),
					"tag/allowlist/remove": handler.NewAction(handler.POST, chatroom.ActionTagAllowListRemove, true),

					"shortcut/gift/add":    handler.NewAction(handler.POST, chatroom.ActionShortcutGiftAdd, true),
					"shortcut/gift/remove": handler.NewAction(handler.POST, chatroom.ActionShortcutGiftRemove, true),
				},
			},
			{
				Name: "chatroom",
				// 额外允许审核组访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditGroup, role.AuditLive)},
				Actions: map[string]*handler.Action{
					"hotsuppression/update": handler.NewAction(handler.POST, chatroom.ActionHotSuppressionUpdate, true),
				},
			},
			{
				Name: "chatroom",
				// 额外允许运营活动实习生访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, roleOperationEventIntern)},
				Actions: map[string]*handler.Action{
					"liveshow/create": handler.NewAction(handler.POST, chatroom.ActionCreateLiveShow, true),
					"liveshow/cancel": handler.NewAction(handler.POST, chatroom.ActionCancelLiveShow, true),
				},
			},
			{
				Name: "chatroom",
				// 额外允许「直播 - 资源组」访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.LiveResource)},
				Actions: map[string]*handler.Action{
					"gift/import": handler.NewAction(handler.POST, chatroom.ActionGiftImport, true),
				},
			},
			{
				Name: "chatroom",
				// 额外允许「审核 - 直播组」访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditLive)},
				Actions: map[string]*handler.Action{
					"close":     handler.NewAction(handler.POST, chatroom.ActionClose, true),
					"get":       handler.NewAction(handler.GET, chatroom.ActionGet, true),
					"reset":     handler.NewAction(handler.POST, chatroom.ActionReset, true),
					"ban":       handler.NewAction(handler.POST, chatroom.ActionBan, true),
					"updateban": handler.NewAction(handler.POST, chatroom.ActionUpdateBan, true),
					"unban":     handler.NewAction(handler.POST, chatroom.ActionUnban, true),

					"globalmute/add":    handler.NewAction(handler.POST, chatroom.ActionGlobalMuteAdd, true),
					"globalmute/remove": handler.NewAction(handler.POST, chatroom.ActionGlobalMuteRemove, true),
					"globalmute/get":    handler.NewAction(handler.GET, chatroom.ActionGlobalMuteGet, true),

					"catalog/set": handler.NewAction(handler.POST, chatroom.ActionCatalogSet, true),

					"review/list":   handler.NewAction(handler.GET, chatroom.ActionReviewList, true),
					"review/pass":   handler.NewAction(handler.POST, chatroom.ActionReviewPass, true),
					"review/refuse": handler.NewAction(handler.POST, chatroom.ActionReviewRefuse, true),

					"vitality/reduce":   handler.NewAction(handler.POST, chatroom.ActionVitalityReduce, true),
					"vitality/increase": handler.NewAction(handler.POST, chatroom.ActionVitalityIncrease, true),

					"question/cancel": handler.NewAction(handler.POST, chatroom.ActionQuestionCancel, true),

					"medal/pass":   handler.NewAction(handler.POST, chatroom.ActionMedalPass, true),
					"medal/refuse": handler.NewAction(handler.POST, chatroom.ActionMedalRefuse, true),
					"medal/list":   handler.NewAction(handler.GET, chatroom.ActionMedalList, true),
				},
			},
		},
	}
}

func recommendedHandler() handler.Handler {
	return handler.Handler{
		SubHandlers: []handler.Handler{
			{
				Name:        "recommended",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
				Actions: map[string]*handler.Action{
					"get":             handler.NewAction(handler.GET, recommended.ActionGet, true),
					"set":             handler.NewAction(handler.POST, recommended.ActionSet, true),
					"addevent":        handler.NewAction(handler.POST, recommended.ActionAddEvent, true),
					"delevent":        handler.NewAction(handler.POST, recommended.ActionDelEvent, true),
					"addliveicon":     handler.NewAction(handler.POST, recommended.ActionAddLiveIcon, true),
					"delliveicon":     handler.NewAction(handler.POST, recommended.ActionDelLiveIcon, true),
					"listliveicon":    handler.NewAction(handler.GET, recommended.ActionListLiveIcon, true),
					"addavatarframe":  handler.NewAction(handler.POST, recommended.ActionAddAvatarFrame, true),
					"delavatarframe":  handler.NewAction(handler.POST, recommended.ActionDelAvatarFrame, true),
					"listavatarframe": handler.NewAction(handler.GET, recommended.ActionListAvatarFrame, true),
					"addsquarehot":    handler.NewAction(handler.POST, recommended.ActionAddSquareHot, true),
					"delsquarehot":    handler.NewAction(handler.POST, recommended.ActionDelSquareHot, true),
					"addpopup":        handler.NewAction(handler.POST, recommended.ActionAddPopup, true),
					"editpopup":       handler.NewAction(handler.POST, recommended.ActionEditPopup, true),
					"delpopup":        handler.NewAction(handler.POST, recommended.ActionDelPopup, true),

					"listschedule":    handler.NewAction(handler.GET, recommended.ActionListSchedule, true),
					"listtopschedule": handler.NewAction(handler.GET, recommended.ActionListTopSchedule, true),
					"addschedule":     handler.NewAction(handler.POST, recommended.ActionAddSchedule, true),
					"editschedule":    handler.NewAction(handler.POST, recommended.ActionEditSchedule, true),
					"delschedule":     handler.NewAction(handler.POST, recommended.ActionDelSchedule, true),
					"listnobleroom":   handler.NewAction(handler.GET, recommended.ActionListNobleRoom, true),
					"setlivenotice":   handler.NewAction(handler.POST, recommended.ActionSetLiveNotice, true),
					"listlivenotice":  handler.NewAction(handler.GET, recommended.ActionListLiveNotice, true),

					"listlivebanners":        handler.NewAction(handler.GET, recommended.ActionListLiveBanners, true),
					"listcurrentlivebanners": handler.NewAction(handler.GET, recommended.ActionListCurrentLiveBanners, true),
					"addlivebanner":          handler.NewAction(handler.POST, recommended.ActionAddLiveBanner, true),
					"editlivebanner":         handler.NewAction(handler.POST, recommended.ActionEditLiveBanner, true),
					"dellivebanner":          handler.NewAction(handler.POST, recommended.ActionDelLiveBanner, true),
					"listbanner":             handler.NewAction(handler.GET, recommended.ActionListBanner, true),
					"setbanner":              handler.NewAction(handler.POST, recommended.ActionSetBanner, true),

					// TODO: delbanner 改为 dellivebanner 前端替换完后删除
					"delbanner": handler.NewAction(handler.POST, recommended.ActionDelLiveBanner, true),

					"addbackground": handler.NewAction(handler.POST, recommended.ActionAddBackground, true),
					"delbackground": handler.NewAction(handler.POST, recommended.ActionDelBackground, true),

					"blocklist/room/add":     handler.NewAction(handler.POST, recommended.ActionBlockListRoomAdd, true),
					"blocklist/room/remove":  handler.NewAction(handler.POST, recommended.ActionBlockListRoomRemove, true),
					"blocklist/guild/add":    handler.NewAction(handler.POST, recommended.ActionBlockListGuildAdd, true),
					"blocklist/guild/remove": handler.NewAction(handler.POST, recommended.ActionBlockListGuildRemove, true),

					"creatorcard/add":    handler.NewAction(handler.POST, recommended.ActionCreatorCardAdd, true),
					"creatorcard/delete": handler.NewAction(handler.POST, recommended.ActionCreatorCardDelete, true),
				},
			},
			// 音频播放页随机推荐语配置后台
			{
				Name: "recommended",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin,
					roleOperationSeniorPlatformEdit, roleOperationSuperiorPlatformEdit, roleOperationSeniorPlatformSupport)},
				Actions: map[string]*handler.Action{
					"sound/message/add":    handler.NewAction(handler.POST, recommended.ActionSoundMessageAdd, true),
					"sound/message/delete": handler.NewAction(handler.POST, recommended.ActionSoundMessageDelete, true),
				},
			},
		},
	}
}

func activityHandler() handler.Handler {
	return handler.Handler{
		Name:        "activity",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"rank/increase": handler.NewAction(handler.POST, activity.ActionRankIncrease, true),
		},
	}
}

func nobleHandler() handler.Handler {
	return handler.Handler{
		Name:        "noble",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"recommend/cancel": handler.NewAction(handler.POST, noble.ActionAdminCancelRecommend, true),
			"horn/add":         handler.NewAction(handler.POST, noble.ActionAdminHornAdd, true),
		},
	}
}

func playbackHandler() handler.Handler {
	return handler.Handler{
		Name:        "playback",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"priority/set": handler.NewAction(handler.POST, playback.ActionPlaybackPrioritySet, true),
		},
	}
}

func exclusiveHandler() handler.Handler {
	return handler.Handler{
		Name:        "exclusive",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditGroup)},
		Actions: map[string]*handler.Action{
			"creator/list":   handler.NewAction(handler.GET, exclusive.ActionExclusiveCreatorList, true),    // 超管获取三方独家主播列表
			"creator/add":    handler.NewAction(handler.POST, exclusive.ActionExclusiveCreatorAdd, true),    // 超管添加三方独家主播
			"creator/edit":   handler.NewAction(handler.POST, exclusive.ActionExclusiveCreatorEdit, true),   // 超管修改三方独家主播
			"creator/delete": handler.NewAction(handler.POST, exclusive.ActionExclusiveCreatorDelete, true), // 超管移除三方独家主播
		},
	}
}

func withdrawalHandler() handler.Handler {
	return handler.Handler{
		Name:        "withdrawal",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"setmonthlimit": handler.NewAction(handler.POST, withdrawal.ActionSetMonthLimit, true),
			"setmin":        handler.NewAction(handler.POST, withdrawal.ActionSetMin, true),
		},
	}
}

func diyGiftHandler() handler.Handler {
	return handler.Handler{
		Name:        "diygift",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"list": handler.NewAction(handler.GET, diygifts.ActionDiyGiftList, true),
		},
	}
}

func giftWallHandler() handler.Handler {
	return handler.Handler{
		Name:        "giftwall",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.Action{
			"gift/list":   handler.NewAction(handler.GET, giftwall.ActionGiftList, true),
			"gift/add":    handler.NewAction(handler.POST, giftwall.ActionGiftAdd, true),
			"gift/del":    handler.NewAction(handler.POST, giftwall.ActionGiftDel, true),
			"gift/update": handler.NewAction(handler.POST, giftwall.ActionGiftUpdate, true),

			"period/details": handler.NewAction(handler.GET, giftwall.ActionPeriodDetails, true),
			"period/add":     handler.NewAction(handler.POST, giftwall.ActionPeriodAdd, true),
			"period/update":  handler.NewAction(handler.POST, giftwall.ActionPeriodUpdate, true),
			"period/del":     handler.NewAction(handler.POST, giftwall.ActionPeriodDel, true),

			"notice/set": handler.NewAction(handler.POST, giftwall.ActionGiftWallNoticeSet, true),
			"notice/del": handler.NewAction(handler.POST, giftwall.ActionGiftWallNoticeDel, true),
		},
	}
}

func luckyBagHandler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "luckybag",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditLive)},
		Actions: map[string]*handler.ActionV2{
			"remove": handler.NewActionV2(handler.POST, luckybag.ActionAdminRemove, handler.ActionOption{LoginRequired: true}),
		},
	}
}

func chatroomHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		SubHandlers: []handler.HandlerV2{
			{
				Name:        "chatroom",
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
				Actions: map[string]*handler.ActionV2{
					"quest/import": handler.NewActionV2(handler.POST, chatroom.ActionQuestImport, handler.ActionOption{LoginRequired: true}),
					"quest/del":    handler.NewActionV2(handler.POST, chatroom.ActionQuestDel, handler.ActionOption{LoginRequired: true}),

					"bubble/add":  handler.NewActionV2(handler.POST, chatroom.ActionBubbleAdd, handler.ActionOption{LoginRequired: true}),
					"bubble/edit": handler.NewActionV2(handler.POST, chatroom.ActionBubbleEdit, handler.ActionOption{LoginRequired: true}),
					"bubble/info": handler.NewActionV2(handler.GET, chatroom.ActionBubbleInfo, handler.ActionOption{LoginRequired: true}),

					"gift/set-online": handler.NewActionV2(handler.POST, chatroom.ActionGiftSetOnline, handler.ActionOption{LoginRequired: true}),
				},
			},
			{
				Name: "chatroom",
				// 额外允许直播 - 资源组访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.LiveResource)},
				Actions: map[string]*handler.ActionV2{
					"gift/exclusive/batch-add":    handler.NewActionV2(handler.POST, chatroom.ActionExclusiveBatchAdd, handler.ActionOption{LoginRequired: true}),
					"gift/exclusive/batch-delete": handler.NewActionV2(handler.POST, chatroom.ActionExclusiveBatchDelete, handler.ActionOption{LoginRequired: true}),
				},
			},
			{
				Name: "chatroom",
				// 额外允许直播 - 审核组访问
				Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin, role.AuditLive)},
				Actions: map[string]*handler.ActionV2{
					"hornmute/add":    handler.NewActionV2(handler.POST, chatroom.ActionHornMuteAdd, handler.ActionOption{LoginRequired: true}),
					"hornmute/remove": handler.NewActionV2(handler.POST, chatroom.ActionHornMuteRemove, handler.ActionOption{LoginRequired: true}),
				},
			},
		},
	}
}

func nobleHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "noble",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.ActionV2{
			"horn/remove": handler.NewActionV2(handler.POST, user.ActionAdminHornRemove, handler.ActionOption{LoginRequired: true}),
		},
	}
}

func recommendedHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "recommended",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.ActionV2{
			"medalframe/add": handler.NewActionV2(handler.POST, recommended.ActionMedalFrameAdd, handler.ActionOption{LoginRequired: true}),
			"medalframe/del": handler.NewActionV2(handler.POST, recommended.ActionMedalFrameDel, handler.ActionOption{LoginRequired: true}),

			"position/import": handler.NewActionV2(handler.POST, recommended.ActionPositionImport, handler.ActionOption{LoginRequired: true}),
			"position/del":    handler.NewActionV2(handler.POST, recommended.ActionPositionDel, handler.ActionOption{LoginRequired: true}),

			"algorithm-exposure/add": handler.NewActionV2(handler.POST, recommended.ActionAlgorithmExposureAdd, handler.ActionOption{LoginRequired: true}),
			"algorithm-exposure/del": handler.NewActionV2(handler.POST, recommended.ActionAlgorithmExposureDel, handler.ActionOption{LoginRequired: true}),
		},
	}
}

func stickerHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "sticker",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.ActionV2{
			"add":           handler.NewActionV2(handler.POST, sticker.ActionStickerAdd, handler.ActionOption{LoginRequired: true}),
			"update":        handler.NewActionV2(handler.POST, sticker.ActionStickerUpdate, handler.ActionOption{LoginRequired: true}),
			"assign/add":    handler.NewActionV2(handler.POST, sticker.ActionStickerAssignAdd, handler.ActionOption{LoginRequired: true}),
			"assign/update": handler.NewActionV2(handler.POST, sticker.ActionStickerAssignUpdate, handler.ActionOption{LoginRequired: true}),
			"assign/del":    handler.NewActionV2(handler.POST, sticker.ActionStickerAssignDel, handler.ActionOption{LoginRequired: true}),
		},
	}
}

func luckyGiftHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "luckygift",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.ActionV2{
			"drop/config/import":      handler.NewActionV2(handler.POST, liveluckygift.ActionDropConfigImport, handler.ActionOption{LoginRequired: true}),
			"drop/config/del":         handler.NewActionV2(handler.POST, liveluckygift.ActionDropConfigDel, handler.ActionOption{LoginRequired: true}),
			"drop/config/clear-valid": handler.NewActionV2(handler.POST, liveluckygift.ActionDropConfigClearValid, handler.ActionOption{LoginRequired: true}),
		},
	}
}

func dailyTaskHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name:        "daily-task",
		Middlewares: gin.HandlersChain{middlewares.IsRole(role.LiveOperator, role.LiveAdmin)},
		Actions: map[string]*handler.ActionV2{
			"allowlist/import": handler.NewActionV2(handler.POST, dailytask.ActionAllowlistImport, handler.ActionOption{LoginRequired: true}),
			"allowlist/delete": handler.NewActionV2(handler.POST, dailytask.ActionAllowlistDelete, handler.ActionOption{LoginRequired: true}),
		},
	}
}
