package sticker

import (
	"fmt"
	"path/filepath"
	"strings"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/admin/recommended"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	maxLenStickerName  = 10 // 表情名称最大长度
	maxLenStickerIntro = 20 // 表情描述最大长度
)

const (
	// 表情资源类型的请求参数分类，0：不修改，1：静态表情，2：动态表情
	typeStickerDefault = iota // 不修改表情类型只有在修改表情的接口会用到
	typeStickerStatic
	typeStickerDynamic
)

// stickerParam 新增表情资源的请求参数
type stickerParam struct {
	StickerID   int64              `json:"sticker_id"`
	StickerType int                `json:"sticker_type"`
	Name        string             `json:"name"`
	Intro       string             `json:"intro"`
	Icon        upload.SourceURL   `json:"icon"`
	ImageURLs   []upload.SourceURL `json:"image_urls"` // 表情图像数组，静态表情上传一张 png，动态表情上传 png 和 webp

	// 表情资源的 Storage 地址
	iconTarget  string
	imageTarget string
	iconFilter  *liveupload.ImageURLFilter
	imageFilter *liveupload.ImageURLFilter
}

// checkResource 检查资源文件
func (p *stickerParam) checkResource() error {
	var err error
	if p.Icon != "" {
		p.iconFilter, err = liveupload.NewImageURLFilter([]upload.SourceURL{p.Icon})
		if err != nil {
			return actionerrors.ErrParamsMsg(err.Error())
		}
	}
	if len(p.ImageURLs) > 0 {
		p.imageFilter, err = liveupload.NewImageURLFilter(p.ImageURLs)
		if err != nil {
			return actionerrors.ErrParamsMsg(err.Error())
		}
	}
	return nil
}

// checkAdd 校验新增表情资源的请求参数
func (p *stickerParam) checkAdd() error {
	if p.StickerID <= 0 {
		return actionerrors.ErrParamsMsg("表情 ID 输入有误")
	}
	if len(p.Name) == 0 || util.UTF8Width(p.Name)/2 >= maxLenStickerName {
		return actionerrors.ErrParamsMsg("表情名称长度有误")
	}
	if len(p.Intro) == 0 || util.UTF8Width(p.Intro)/2 >= maxLenStickerIntro {
		return actionerrors.ErrParamsMsg("表情描述长度有误")
	}

	switch p.StickerType {
	case typeStickerStatic:
		if p.Icon == "" || len(p.ImageURLs) != 1 {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
	case typeStickerDynamic:
		if p.Icon == "" || len(p.ImageURLs) != 2 {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
	default:
		return actionerrors.ErrParamsMsg("请选择正确的表情类型")
	}
	err := p.checkResource()
	if err != nil {
		return err
	}

	sticker, err := livesticker.FindSticker(p.StickerID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if sticker != nil {
		return actionerrors.ErrParamsMsg("表情 ID 已存在")
	}
	return nil
}

// checkUpdate 校验修改表情资源的请求参数
func (p *stickerParam) checkUpdate() error {
	if p.StickerID <= 0 {
		return actionerrors.ErrParamsMsg("表情 ID 输入有误")
	}
	if len(p.Name) != 0 && util.UTF8Width(p.Name)/2 >= maxLenStickerName {
		return actionerrors.ErrParamsMsg("表情名称长度有误")
	}
	if len(p.Intro) != 0 && util.UTF8Width(p.Intro)/2 >= maxLenStickerIntro {
		return actionerrors.ErrParamsMsg("表情描述长度有误")
	}
	err := p.checkResource()
	if err != nil {
		return err
	}

	// 确认要修改的表情存在
	sticker, err := livesticker.FindSticker(p.StickerID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if sticker == nil {
		return actionerrors.ErrParamsMsg("不存在该表情 ID")
	}

	// 要修改的表情类型不能和修改前的表情类型一致
	beforeType := typeStickerStatic
	if filepath.Ext(strings.ToLower(sticker.Image)) != ".png" {
		beforeType = typeStickerDynamic
	}
	if p.StickerType == beforeType {
		if beforeType == typeStickerStatic {
			return actionerrors.ErrParamsMsg("原表情为静态！")
		}
		return actionerrors.ErrParamsMsg("原表情为动态！")
	}

	switch p.StickerType {
	case typeStickerDefault:
		// 如果不改变表情类型，更新图片需要上传全部的图片资源
		if beforeType == typeStickerStatic && (len(p.ImageURLs) != 0 && len(p.ImageURLs) != 1) {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
		if beforeType == typeStickerDynamic && (len(p.ImageURLs) != 0 && len(p.ImageURLs) != 2) {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
	case typeStickerStatic:
		// 动态资源转静态资源，必传 1 张 png 图片
		if len(p.ImageURLs) != 1 {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
	case typeStickerDynamic:
		// 静态资源转动态资源，必传 1 张 apng 和 1 张 webp 图片
		if len(p.ImageURLs) != 2 {
			return actionerrors.ErrParamsMsg("表情资源未正确上传")
		}
	default:
		return actionerrors.ErrParamsMsg("请选择正确的表情类型")
	}
	return nil
}

// upload 上传资源到 Storage
func (p *stickerParam) upload() error {
	// 在前置条件中已经检查过了类型和对应需要上传的资源
	var err error
	if p.Icon != "" {
		p.iconTarget = fmt.Sprintf("%s://%s%d%s", config.DefaultCDNScheme, storage.PathPrefixLiveStickerIcon, p.StickerID, p.iconFilter.Primary.Ext())
		_, err = recommended.UploadImageWithTarget(p.iconFilter, upload.TargetPath(p.iconTarget))
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	if len(p.ImageURLs) > 0 {
		p.imageTarget = fmt.Sprintf("%s://%s%d%s", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, p.StickerID, p.imageFilter.Primary.Ext())
		_, err = recommended.UploadImageWithTarget(p.imageFilter, upload.TargetPath(p.imageTarget))
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

// ActionStickerAdd 新增表情资源
/**
 * @api {post} /api/v2/admin/sticker/add 新增表情资源
 * @apiDescription 新增表情资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/sticker
 *
 * @apiParam {Number} sticker_id 表情 ID
 * @apiParam {number=1,2} sticker_type 表情类型 1: 静态表情，2: 动态表情
 * @apiParam {String} name 表情名称
 * @apiParam {String} intro 表情描述
 * @apiParam {String} icon 表情 icon 图片地址
 * @apiParam {String[]} image_urls 表情图片地址，静态表情传 1 张 png，动态表情传 1 张 png 和 1 张 webp
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "新增表情成功！",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionStickerAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	var param stickerParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	err = param.checkAdd()
	if err != nil {
		return nil, "", err
	}

	err = param.upload()
	if err != nil {
		return nil, "", err
	}

	timeNowUnix := goutil.TimeNow().Unix()
	sticker := &livesticker.LiveSticker{
		ID:           param.StickerID,
		Name:         param.Name,
		Intro:        param.Intro,
		Icon:         param.iconTarget,
		Image:        param.imageTarget,
		CreateTime:   timeNowUnix,
		ModifiedTime: timeNowUnix,
	}
	err = livesticker.DB().Create(sticker).Error
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageSticker, fmt.Sprintf("【新增表情】表情 ID %d", param.StickerID))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "新增表情成功！", nil
}

// ActionStickerUpdate 修改表情资源
/**
 * @api {post} /api/v2/admin/sticker/update 修改表情资源
 * @apiDescription 修改表情资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/sticker
 *
 * @apiParam {Number} sticker_id 表情 ID
 * @apiParam {number=0,1,2} sticker_type 表情类型 0: 不修改，1: 修改为静态表情，2: 修改为动态表情
 * @apiParam {String} [name] 表情名称
 * @apiParam {String} [intro] 表情描述
 * @apiParam {String} [icon] 表情 icon 图片地址
 * @apiParam {String[]} [image_urls] 表情图片地址，静态表情传 1 张 png，动态表情传 1 张 png 和 1 张 webp
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "修改表情成功！",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionStickerUpdate(c *handler.Context) (handler.ActionResponse, string, error) {
	var param stickerParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}

	err = param.checkUpdate()
	if err != nil {
		return nil, "", err
	}

	err = param.upload()
	if err != nil {
		return nil, "", err
	}

	updateAttrs, logFields := genUpdateInfo(&param)
	// 如果只传入了表情 ID，表示没有修改项
	if len(logFields) == 1 {
		return nil, "", actionerrors.ErrParamsMsg("无修改内容！")
	}

	err = livesticker.DB().Model(&livesticker.LiveSticker{}).
		Where("id = ?", param.StickerID).Updates(updateAttrs).Error
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageSticker, fmt.Sprintf("【修改表情】%s", strings.Join(logFields, "；")))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "修改表情成功！", nil
}

// genUpdateInfo 获取需要更新到数据库的字段和修改的字段信息
func genUpdateInfo(param *stickerParam) (map[string]any, []string) {
	updateAttrs := make(map[string]any, 6)
	logFields := make([]string, 0, 6)
	logFields = append(logFields, fmt.Sprintf("表情 ID %d", param.StickerID))

	if param.Name != "" {
		updateAttrs["name"] = param.Name
		logFields = append(logFields, "表情名称")
	}
	if param.Intro != "" {
		updateAttrs["intro"] = param.Intro
		logFields = append(logFields, "表情描述")
	}
	// 表情类型修改时，才需要改变 image 字段的值
	if param.StickerType != typeStickerDefault {
		updateAttrs["image"] = param.imageTarget
		logFields = append(logFields, "表情类型")
	}
	if param.Icon != "" {
		updateAttrs["icon"] = param.iconTarget
		logFields = append(logFields, "icon")
	}
	if len(param.ImageURLs) == 1 {
		logFields = append(logFields, "表情 png")
	}
	if len(param.ImageURLs) == 2 {
		logFields = append(logFields, "表情 webp")
	}
	updateAttrs["modified_time"] = goutil.TimeNow().Unix()
	return updateAttrs, logFields
}
