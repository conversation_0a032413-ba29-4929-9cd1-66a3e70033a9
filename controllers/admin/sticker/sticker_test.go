package sticker

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestStickerParam_checkResource(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name        string
		param       stickerParam
		expectedErr error
	}{
		{
			name: "icon 格式有误",
			param: stickerParam{
				Icon: "https://fm.example.com/testdata/test.webp",
			},
			expectedErr: actionerrors.ErrParamsMsg("上传图片资源含有 WebP 时，必须同时上传 PNG 文件"),
		}, {
			name: "image png 格式有误",
			param: stickerParam{
				ImageURLs: []upload.SourceURL{"https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: actionerrors.ErrParamsMsg("上传图片资源含有 WebP 时，必须同时上传 PNG 文件"),
		}, {
			name: "image webp 格式有误",
			param: stickerParam{
				ImageURLs: []upload.SourceURL{
					"https://fm.example.com/testdata/test.png",
					"https://fm.example.com/testdata/test.png"},
			},
			expectedErr: actionerrors.ErrParamsMsg("上传两个资源文件时需包含 WebP 和 PNG 文件"),
		}, {
			name: "正确的资源格式",
			param: stickerParam{
				Icon: "https://fm.example.com/testdata/test.png",
				ImageURLs: []upload.SourceURL{
					"https://fm.example.com/testdata/test.webp",
					"https://fm.example.com/testdata/test.png",
				},
			},
			expectedErr: nil,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.param.checkResource()
			assert.Equal(tc.expectedErr, err)
		})
	}
}

func TestStickerParam_checkAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(livesticker.DB().Delete(livesticker.LiveSticker{}, "id = ?", 100).Error)
	testCases := []struct {
		name        string
		param       stickerParam
		expectedErr error
	}{
		{
			name:        "未输入表情 ID",
			param:       stickerParam{},
			expectedErr: actionerrors.ErrParamsMsg("表情 ID 输入有误"),
		}, {
			name: "表情名称长度有误",
			param: stickerParam{
				StickerID: 100,
				Name:      "十个中文字符的长度。",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情名称长度有误"),
		}, {
			name: "表情描述长度有误",
			param: stickerParam{
				StickerID: 100,
				Name:      "表情名",
				Intro:     "十个中文字符的长度。十个中文字符的长度。",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情描述长度有误"),
		}, {
			name: "静态表情未添加资源地址",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerStatic,
				Name:        "表情名",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test.png",
				ImageURLs:   []upload.SourceURL{},
			},
			expectedErr: actionerrors.ErrParamsMsg("表情资源未正确上传"),
		}, {
			name: "动态表情未添加资源地址",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerDynamic,
				Name:        "表情名",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test.png",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
			},
			expectedErr: actionerrors.ErrParamsMsg("表情资源未正确上传"),
		}, {
			name: "存在的表情 ID",
			param: stickerParam{
				StickerID:   1,
				StickerType: typeStickerDynamic,
				Name:        "表情名",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test.png",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: actionerrors.ErrParamsMsg("表情 ID 已存在"),
		}, {
			name: "正常的请求参数",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerDynamic,
				Name:        "表情名",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test.png",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.param.checkAdd()
			assert.Equal(tc.expectedErr, err)
		})
	}
}

func TestStickerParam_checkUpdate(t *testing.T) {
	assert := assert.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	// testdata 中已经存在 ID 为 1 的动态表情，这里创建静态表情用于测试
	staticSticker := livesticker.LiveSticker{
		ID:           100,
		CreateTime:   timeNowUnix,
		ModifiedTime: timeNowUnix,
		Name:         "静态表情",
		Intro:        "静态表情测试",
		Icon:         "oss://live/stickers/icons/100.png",
		Image:        "oss://live/stickers/100.png",
	}
	require.NoError(t, livesticker.DB().Delete(&livesticker.LiveSticker{}, "id = ?", staticSticker.ID).Error)
	require.NoError(t, livesticker.DB().Create(&staticSticker).Error)
	testCases := []struct {
		name        string
		param       stickerParam
		expectedErr error
	}{
		{
			name: "未输入表情 ID",
			param: stickerParam{
				StickerID: 0,
			},
			expectedErr: actionerrors.ErrParamsMsg("表情 ID 输入有误"),
		}, {
			name: "表情名称长度有误",
			param: stickerParam{
				StickerID: 1,
				Name:      "十个中文字符的长度。",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情名称长度有误"),
		}, {
			name: "表情描述长度有误",
			param: stickerParam{
				StickerID: 1,
				Name:      "表情名",
				Intro:     "十个中文字符的长度。十个中文字符的长度。",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情描述长度有误"),
		}, {
			name: "不存在的表情 ID",
			param: stickerParam{
				StickerID: 5,
			},
			expectedErr: actionerrors.ErrParamsMsg("不存在该表情 ID"),
		}, {
			name: "同种类型更新",
			param: stickerParam{
				StickerID:   1,
				StickerType: typeStickerDynamic,
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: actionerrors.ErrParamsMsg("原表情为动态！"),
		}, {
			name: "静态表情不改变类型传 webp 资源",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerDefault,
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: actionerrors.ErrParamsMsg("表情资源未正确上传"),
		}, {
			name: "静态表情转动态表情不传资源",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerDynamic,
				Name:        "修改表情名",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情资源未正确上传"),
		}, {
			name: "动态表情转静态表情不传资源",
			param: stickerParam{
				StickerID:   1,
				StickerType: typeStickerStatic,
				Name:        "修改表情名",
				Icon:        "https://fm.example.com/testdata/test.png",
			},
			expectedErr: actionerrors.ErrParamsMsg("表情资源未正确上传"),
		}, {
			name: "静态表情转动态表情",
			param: stickerParam{
				StickerID:   100,
				StickerType: typeStickerDynamic,
				Name:        "修改表情名",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedErr: nil,
		}, {
			name: "动态表情转静态表情",
			param: stickerParam{
				StickerID:   1,
				StickerType: typeStickerStatic,
				Name:        "修改表情名",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
			},
			expectedErr: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.param.checkUpdate()
			assert.Equal(tc.expectedErr, err)
		})
	}
}

func TestStickerParam_upload(t *testing.T) {
	require := require.New(t)

	testStickerID := int64(999999)
	testCases := []struct {
		name           string
		param          stickerParam
		expectedNilErr bool
	}{
		{
			name: "资源文件获取失败",
			param: stickerParam{
				StickerID:   testStickerID,
				StickerType: typeStickerStatic,
				Name:        "表情名称",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test_nonexist.png",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test_nonexist.png"},
			},
			expectedNilErr: false,
		},
		{
			name: "正确的上传资源文件",
			param: stickerParam{
				StickerID:   testStickerID,
				StickerType: typeStickerDynamic,
				Name:        "表情名称",
				Intro:       "表情描述",
				Icon:        "https://fm.example.com/testdata/test.png",
				ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
			},
			expectedNilErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 构建正确的 ImageURLFilter
			require.NoError(tc.param.checkResource())
			err := tc.param.upload()
			if tc.expectedNilErr && err != nil {
				t.Fatalf("expected error is nil, got %v", err)
			}
			if !tc.expectedNilErr && err == nil {
				t.Fatal("expected error is not nil, got nil")
			}
		})
	}

	// 从 storage 删除上传的测试数据
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveStickerIcon, testStickerID)))
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, testStickerID)))
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.webp", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, testStickerID)))
}

func TestActionStickerAdd(t *testing.T) {
	require := require.New(t)

	testStickerID := int64(999999)
	require.NoError(livesticker.DB().Delete(&livesticker.LiveSticker{}, "id = ?", testStickerID).Error)
	param := stickerParam{
		StickerID:   testStickerID,
		StickerType: typeStickerStatic,
		Name:        "表情名称最多9字！",
		Intro:       "静态表情描述",
		Icon:        "https://fm.example.com/testdata/test.png",
		ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png"},
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err := ActionStickerAdd(c)
	require.NoError(err)
	sticker, err := livesticker.FindSticker(testStickerID)
	require.NoError(err)
	require.Equal(testStickerID, sticker.ID)
	require.Equal(param.Name, sticker.Name)
	require.Equal(param.Intro, sticker.Intro)
	require.Equal("https://static-test.missevan.com/live/stickers/icons/999999.png", sticker.Icon)
	require.Equal("https://static-test.missevan.com/live/stickers/999999.png", sticker.Image)

	// 从 storage 删除上传的测试数据
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveStickerIcon, testStickerID)))
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, testStickerID)))
}

func TestActionStickerUpdate(t *testing.T) {
	require := require.New(t)

	testStickerID := int64(999999)
	timeNowUnix := goutil.TimeNow().Unix()
	staticSticker := livesticker.LiveSticker{
		ID:           testStickerID,
		CreateTime:   timeNowUnix,
		ModifiedTime: timeNowUnix,
		Name:         "静态表情",
		Intro:        "静态表情测试",
		Icon:         "oss://live/stickers/icons/999999.png",
		Image:        "oss://live/stickers/999999.png",
	}
	require.NoError(livesticker.DB().Delete(&livesticker.LiveSticker{}, "id = ?", staticSticker.ID).Error)
	require.NoError(livesticker.DB().Create(&staticSticker).Error)

	param := stickerParam{
		StickerID:   staticSticker.ID,
		StickerType: typeStickerDynamic,
		Name:        "表情名称最多9字！",
		Intro:       "更新静态表情描述",
		Icon:        "https://fm.example.com/testdata/test.png",
		ImageURLs:   []upload.SourceURL{"https://fm.example.com/testdata/test.png", "https://fm.example.com/testdata/test.webp"},
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err := ActionStickerUpdate(c)
	require.NoError(err)
	sticker, err := livesticker.FindSticker(staticSticker.ID)
	require.NoError(err)
	require.Equal(param.Name, sticker.Name)
	require.Equal(param.Intro, sticker.Intro)
	require.Equal("https://static-test.missevan.com/live/stickers/icons/999999.png", sticker.Icon)
	require.Equal("https://static-test.missevan.com/live/stickers/999999.webp", sticker.Image)

	// 从 storage 删除上传的测试数据
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveStickerIcon, testStickerID)))
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.png", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, testStickerID)))
	require.NoError(service.Storage.Delete(fmt.Sprintf("%s://%s%d.webp", config.DefaultCDNScheme, storage.PathPrefixLiveSticker, testStickerID)))
}
