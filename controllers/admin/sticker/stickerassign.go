package sticker

import (
	"fmt"
	"html"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	// 支持上传的最大列表长度
	maxLenOwnerIDs   = 1000
	maxLenStickerIDs = 10
)

// stickerAssignParam 分配表情资源的请求参数
type stickerAssignParam struct {
	PackageType int    `json:"package_type"` // 操作的表情包类型 2: 用户专属, 3: 直播间专属
	OwnerIDs    string `json:"owner_ids"`    // 房间号 / M号 列表，使用半角逗号分割
	StickerIDs  string `json:"sticker_ids"`  // 表情 ID 列表，使用半角逗号分割
	StartTime   int64  `json:"start_time"`   // 生效时间，秒级时间戳
	ExpireTime  int64  `json:"expire_time"`  // 失效时间，秒级时间戳
	Confirm     int64  `json:"confirm"`

	ownerIDs   []int64 // 持有表情包的房间或用户 ID
	stickerIDs []int64 // 需要被分配的表情 ID
	pkgIDs     []int64 // 需要被分配表情的表情包 ID
	rooms      []*room.Room
	users      []*mowangskuser.Simple
	stickers   []*livesticker.LiveSticker
}

// check 检查请求参数符合要求
func (p *stickerAssignParam) check() error {
	var err error
	stickerIDs, err := goutil.SplitToInt64Array(p.StickerIDs, ",")
	if err != nil {
		return actionerrors.ErrParamsMsg("表情上传格式有误")
	}
	if len(stickerIDs) == 0 {
		return actionerrors.ErrParamsMsg("未上传表情 ID")
	}
	if len(stickerIDs) > maxLenStickerIDs {
		return actionerrors.ErrParamsMsg("表情 ID 数量超出限制")
	}
	if p.StartTime < 0 || p.ExpireTime < 0 {
		return actionerrors.ErrParams
	}
	timeNowUnix := goutil.TimeNow().Unix()
	if p.StartTime != 0 && p.StartTime < timeNowUnix {
		return actionerrors.NewErrForbidden("开始时间不得早于当前时间！")
	}
	if p.StartTime == 0 {
		p.StartTime = timeNowUnix
	}
	if p.ExpireTime != 0 {
		if p.ExpireTime < timeNowUnix {
			return actionerrors.NewErrForbidden("结束时间不得早于当前时间！")
		}
		if p.ExpireTime <= p.StartTime {
			return actionerrors.NewErrForbidden("结束时间不得早于开始时间！")
		}
	}

	uniqStickerIDs := sets.Uniq(stickerIDs)
	if len(uniqStickerIDs) != len(stickerIDs) {
		return actionerrors.ErrParamsMsg("存在重复的表情 ID")
	}
	p.stickerIDs = uniqStickerIDs

	// 检查上传的房间号或M号数量符合要求且都存在
	ownerIDs, err := goutil.SplitToInt64Array(p.OwnerIDs, ",")
	if err != nil {
		return actionerrors.ErrParamsMsg("房间号 / M号上传格式有误")
	}
	if len(ownerIDs) == 0 {
		return actionerrors.ErrParamsMsg("未上传房间号 / M号")
	}
	if len(ownerIDs) > maxLenOwnerIDs {
		return actionerrors.ErrParamsMsg("房间号 / M号数量超出限制")
	}
	uniqOwnerIDs := sets.Uniq(ownerIDs)
	if len(uniqOwnerIDs) != len(ownerIDs) {
		return actionerrors.ErrParamsMsg("存在重复的房间号 / M号")
	}
	p.ownerIDs = uniqOwnerIDs
	switch p.PackageType {
	case livesticker.TypeUser:
		p.users, err = mowangskuser.FindSimpleList(p.ownerIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(p.ownerIDs) != len(p.users) {
			return actionerrors.ErrParamsMsg("有不存在的M号，请检查后重试！")
		}
	case livesticker.TypeRoom:
		p.rooms, err = room.List(bson.M{"room_id": bson.M{"$in": p.ownerIDs}}, nil, &room.FindOptions{DisableAll: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(p.ownerIDs) != len(p.rooms) {
			return actionerrors.ErrParamsMsg("有不存在的房间号，请检查后重试！")
		}
	default:
		return actionerrors.ErrParamsMsg("不支持的操作类型")
	}

	// 获取表情列表信息
	p.stickers, err = livesticker.FindStickersByIDs(p.stickerIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.stickers) != len(p.stickerIDs) {
		return actionerrors.ErrParamsMsg("有不存在的表情 ID，请检查后重试！")
	}
	return nil
}

// preparePackages 准备需要分配表情的专属表情包并返回 ID，当部分直播间或用户不存在专属表情包时创建并分配专属表情包
func (p *stickerAssignParam) preparePackages() ([]int64, error) {
	pkgOwners, err := livesticker.FindPackageOwners(p.PackageType, p.ownerIDs, time.Unix(p.StartTime, 0))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	pkgIDs := make([]int64, 0, len(p.ownerIDs))
	for _, owners := range pkgOwners {
		pkgIDs = append(pkgIDs, owners.PackageID)
	}
	// 在 confirm = 0 的情况下直接返回，只对已存在的表情包做分配的冲突检查
	if p.Confirm == 0 {
		return pkgIDs, nil
	}

	// 在 confirm = 1 的情况下为没有专属表情包的对象新建表情包
	pkgOwnerIDs := make([]int64, 0, len(pkgOwners))
	for _, o := range pkgOwners {
		switch p.PackageType {
		case livesticker.TypeUser:
			pkgOwnerIDs = append(pkgOwnerIDs, o.UserID)
		case livesticker.TypeRoom:
			pkgOwnerIDs = append(pkgOwnerIDs, o.RoomID)
		}
	}
	noPkgOwnerIDs := sets.Diff(p.ownerIDs, pkgOwnerIDs)

	// 为没有专属表情包的房间和用户创建专属表情包
	newPkgs, err := livesticker.BatchAssignExclusivePackage(p.PackageType, noPkgOwnerIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取需要分配表情的全部表情包 ID
	for _, pkg := range newPkgs {
		pkgIDs = append(pkgIDs, pkg.ID)
	}
	return pkgIDs, nil
}

// checkConflictStickers 检查时间冲突的表情分配
func (p *stickerAssignParam) checkConflictStickerAssignment() error {
	var mapIDs []int64

	db := livesticker.DB().Table(livesticker.PackageStickerMap{}.TableName()).
		Where("sticker_id IN (?) AND package_id IN (?)", p.stickerIDs, p.pkgIDs)
	db = buildConflictQuery(db, p.StartTime, p.ExpireTime)
	err := db.Pluck("id", &mapIDs).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(mapIDs) > 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("与已有配置冲突，请检查后重试！冲突 ID: %v", mapIDs))
	}
	return nil
}

// confirmError 返回确认弹窗的 error
func (p *stickerAssignParam) confirmError() *handler.ActionError {
	var owners string
	ownerNames := make([]string, 0, len(p.ownerIDs))
	switch p.PackageType {
	case livesticker.TypeUser:
		for _, u := range p.users {
			ownerNames = append(ownerNames, html.EscapeString(u.Username))
		}
		owners = "用户昵称：" + strings.Join(ownerNames, "；")
	case livesticker.TypeRoom:
		for _, r := range p.rooms {
			ownerNames = append(ownerNames, html.EscapeString(r.CreatorUsername))
		}
		owners = "主播昵称：" + strings.Join(ownerNames, "；")
	}

	stickers := make([]string, 0, len(p.stickerIDs))
	for _, sticker := range p.stickers {
		stickers = append(stickers, fmt.Sprintf("%d %s", sticker.ID, sticker.Name))
	}

	confirmMsg := fmt.Sprintf("确认分配表情吗？<br> %s <br>表情：%s<br>开始时间：%s<br>结束时间：%s",
		owners, strings.Join(stickers, "；"), timeFormat(p.StartTime), timeFormat(p.ExpireTime))
	return actionerrors.ErrConfirmRequired(confirmMsg, 1, true)
}

func (p *stickerAssignParam) insertPackageMap() error {
	timeNowUnix := goutil.TimeNow().Unix()
	pkgStickerMaps := make([]*livesticker.PackageStickerMap, 0, len(p.pkgIDs)*len(p.stickerIDs))
	for _, pkgID := range p.pkgIDs {
		for _, stickerID := range p.stickerIDs {
			pkgMap := &livesticker.PackageStickerMap{
				CreateTime:   timeNowUnix,
				ModifiedTime: timeNowUnix,
				PackageID:    pkgID,
				StickerID:    stickerID,
				StartTime:    p.StartTime,
				ExpireTime:   p.ExpireTime,
			}
			pkgStickerMaps = append(pkgStickerMaps, pkgMap)
		}
	}
	err := servicedb.BatchInsert(livesticker.DB(), livesticker.PackageStickerMap{}.TableName(), pkgStickerMaps)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// ActionStickerAssignAdd 分配表情资源
/**
 * @api {post} /api/v2/admin/sticker/assign/add 分配表情资源
 * @apiDescription 分配表情资源
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/sticker
 *
 * @apiParam {number=2,3} package_type 操作类型 2: 用户专属，3: 直播间专属
 * @apiParam {String} owner_ids 房间号 / M号，批量设置， 使用半角逗号分割
 * @apiParam {String} sticker_ids 表情 ID 列表，批量设置，使用半角逗号分割
 * @apiParam {Number} [start_time] 开始时间，秒级时间戳，不传表示立即生效
 * @apiParam {Number} [expire_time] 过期时间，秒级时间戳，不传表示不过期
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "分配成功！",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionStickerAssignAdd(c *handler.Context) (handler.ActionResponse, string, error) {
	var param stickerAssignParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}

	// 获取当前存在的表情包 ID，用户确认前只校验已经存在的表情包，用户确认后校验已存在的和新创建的表情包
	param.pkgIDs, err = param.preparePackages()
	if err != nil {
		return nil, "", err
	}

	err = param.checkConflictStickerAssignment()
	if err != nil {
		return nil, "", err
	}

	if param.Confirm == 0 {
		return nil, "", param.confirmError()
	}

	err = param.insertPackageMap()
	if err != nil {
		return nil, "", err
	}
	// 记录后台管理日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageSticker, fmt.Sprintf("【分配表情】表情包 ID %s，表情 ID %s",
		goutil.JoinInt64Array(param.pkgIDs, "；"), goutil.JoinInt64Array(param.stickerIDs, "；")))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "分配成功！", nil
}

// stickerAssignUpdateParam 更新表情资源配置的请求参数
type stickerAssignUpdateParam struct {
	ID         int64 `json:"id"`          // 表情关系配置 ID
	StartTime  int64 `json:"start_time"`  // 生效时间，秒级时间戳
	ExpireTime int64 `json:"expire_time"` // 失效时间，秒级时间戳
	Confirm    int64 `json:"confirm"`

	pkgStickerMap *livesticker.PackageStickerMap
	user          *mowangskuser.Simple
	room          *room.Room
	sticker       *livesticker.LiveSticker
}

// check 检查配置存在且修改的时间与现有配置不冲突
func (p *stickerAssignUpdateParam) check() (err error) {
	timeNowUnix := goutil.TimeNow().Unix()
	p.pkgStickerMap, err = livesticker.FindMap(p.ID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.pkgStickerMap == nil {
		return actionerrors.ErrNotFound("配置 ID 不存在！")
	}

	if p.StartTime != 0 && p.StartTime < timeNowUnix {
		return actionerrors.NewErrForbidden("开始时间不得早于当前时间！")
	}
	if p.StartTime == 0 {
		p.StartTime = timeNowUnix
	}
	if p.ExpireTime != 0 {
		if p.ExpireTime < timeNowUnix {
			return actionerrors.NewErrForbidden("结束时间不得早于当前时间！")
		}
		if p.ExpireTime <= p.StartTime {
			return actionerrors.NewErrForbidden("结束时间不得早于开始时间！")
		}
	}

	var mapIDs []int64
	// 排除当前记录后，查询现有的表情包和表情匹配的配置与更新的配置不冲突
	db := livesticker.DB().Table(livesticker.PackageStickerMap{}.TableName()).
		Where("id <> ? AND package_id = ? AND sticker_id = ?", p.pkgStickerMap.ID, p.pkgStickerMap.PackageID, p.pkgStickerMap.StickerID)
	db = buildConflictQuery(db, p.StartTime, p.ExpireTime)
	err = db.Pluck("id", &mapIDs).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(mapIDs) > 0 {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("与已有配置冲突，请检查后重试！冲突 ID: %v", mapIDs))
	}
	return nil
}

// confirmError 返回确认弹窗错误
func (p *stickerAssignUpdateParam) confirmError() *handler.ActionError {
	confirmMsg := "确认修改表情配置时间吗？<br>"
	if p.user != nil {
		confirmMsg += "用户昵称：" + html.EscapeString(p.user.Username)
	}
	if p.room != nil {
		confirmMsg += "主播昵称：" + html.EscapeString(p.room.CreatorUsername)
	}
	confirmMsg += fmt.Sprintf("<br>表情：%d %s<br>原开始时间：%s<br>原结束时间：%s<br>新开始时间：%s<br>新结束时间：%s<br>",
		p.sticker.ID, p.sticker.Name, timeFormat(p.pkgStickerMap.StartTime), timeFormat(p.pkgStickerMap.ExpireTime), timeFormat(p.StartTime), timeFormat(p.ExpireTime))
	return actionerrors.ErrConfirmRequired(confirmMsg, 1, true)
}

// ActionStickerAssignUpdate 修改表情配置时间
/**
 * @api {post} /api/v2/admin/sticker/assign/update 修改表情配置时间
 * @apiDescription 修改表情配置时间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/sticker
 *
 * @apiParam {Number} id 表情关系配置 ID
 * @apiParam {Number} [start_time] 开始时间，秒级时间戳，不传表示立即生效
 * @apiParam {Number} [expire_time] 过期时间，秒级时间戳，不传表示不过期
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "修改成功！",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionStickerAssignUpdate(c *handler.Context) (handler.ActionResponse, string, error) {
	var param stickerAssignUpdateParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}

	if param.Confirm == 0 {
		param.user, param.room, param.sticker, err = getPackageStickerMapDetails(param.pkgStickerMap)
		if err != nil {
			return nil, "", err
		}
		return nil, "", param.confirmError()
	}

	// 更新配置的生效时间
	err = livesticker.DB().Model(param.pkgStickerMap).UpdateColumns(map[string]interface{}{
		"start_time":    param.StartTime,
		"expire_time":   param.ExpireTime,
		"modified_time": goutil.TimeNow().Unix(),
	}).Error
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	// 记录后台管理日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageSticker, fmt.Sprintf("【修改表情配置时间】任务 ID %d；新开始时间 %s；新结束时间 %s",
		param.pkgStickerMap.ID, timeFormat(param.StartTime), timeFormat(param.ExpireTime)))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "修改成功！", nil
}

// stickerAssignDelParam 删除表情配置的请求参数
type stickerAssignDelParam struct {
	ID      int64 `json:"id"` // package_sticker_map ID
	Confirm int64 `json:"confirm"`

	pkgStickerMap *livesticker.PackageStickerMap
	user          *mowangskuser.Simple
	room          *room.Room
	sticker       *livesticker.LiveSticker
}

// check 检查要删除的配置存在
func (p *stickerAssignDelParam) check() (err error) {
	p.pkgStickerMap, err = livesticker.FindMap(p.ID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.pkgStickerMap == nil {
		return actionerrors.ErrParamsMsg("配置 ID 不存在！")
	}
	return nil
}

// confirmMsg 删除表情配置的弹窗消息
func (p *stickerAssignDelParam) confirmError() *handler.ActionError {
	confirmMsg := "确认删除表情配置吗？<br>"
	if p.user != nil {
		confirmMsg += "用户昵称：" + html.EscapeString(p.user.Username)
	}
	if p.room != nil {
		confirmMsg += "主播昵称：" + html.EscapeString(p.room.CreatorUsername)
	}
	confirmMsg += fmt.Sprintf("<br>表情：%d %s<br>开始时间：%s<br>结束时间：%s",
		p.sticker.ID, p.sticker.Name, timeFormat(p.pkgStickerMap.StartTime), timeFormat(p.pkgStickerMap.ExpireTime))
	return actionerrors.ErrConfirmRequired(confirmMsg, 1, true)
}

// ActionStickerAssignDel 删除表情配置
/**
 * @api {post} /api/v2/admin/sticker/assign/del 删除表情配置
 * @apiDescription 删除表情配置
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/sticker
 *
 * @apiParam {Number} id 表情关系配置 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功！",
 *     "data": null
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionStickerAssignDel(c *handler.Context) (handler.ActionResponse, string, error) {
	var param stickerAssignDelParam
	err := c.Bind(&param)
	if err != nil {
		return nil, "", actionerrors.ErrParams
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}

	if param.Confirm == 0 {
		param.user, param.room, param.sticker, err = getPackageStickerMapDetails(param.pkgStickerMap)
		if err != nil {
			return nil, "", err
		}
		return nil, "", param.confirmError()
	}

	err = livesticker.DB().Delete(livesticker.PackageStickerMap{}, "id = ?", param.pkgStickerMap.ID).Error
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	// 记录后台管理日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageSticker, fmt.Sprintf("【删除表情配置】任务 ID %d", param.pkgStickerMap.ID))
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, "删除成功！", nil
}

// buildConflictQuery 增加检查新的时间区间是否冲突的查询条件
func buildConflictQuery(db *gorm.DB, startTime, expireTime int64) *gorm.DB {
	if expireTime == 0 {
		// 当新插入的配置永久生效时，判断新插入配置的生效时间是否在某个有效配置之前
		db = db.Where("expire_time >= ? OR expire_time = 0", startTime)
	} else {
		// 当新插入的配置有失效时间时，满足任一以下条件时配置冲突：
		// 1. 新区间的生效时间在已存在的区间范围内
		// 2. 新区间的失效时间在已存在的区间范围内
		// 3. 新区间完整包括已存在的区间
		db = db.Where("(start_time <= ? AND (expire_time = 0 OR expire_time >= ?)) OR "+
			"(start_time <= ? AND (expire_time = 0 OR expire_time >= ?)) OR "+
			"(start_time >= ? AND (expire_time <> 0 AND expire_time <= ?))",
			startTime, startTime,
			expireTime, expireTime,
			startTime, expireTime)
	}
	return db
}

// timeFormat 获取时间戳的字符串表现形式，如果时间戳为 0 表示永久生效
func timeFormat(t int64) string {
	if t == 0 {
		return "永久生效"
	}
	return time.Unix(t, 0).Format(util.TimeFormatYMDHMS)
}

// getPackageStickerMapDetails 获取表情配置关系的相关实体信息
func getPackageStickerMapDetails(m *livesticker.PackageStickerMap) (*mowangskuser.Simple, *room.Room, *livesticker.LiveSticker, error) {
	var (
		pkgOwner livesticker.PackageOwner
		u        *mowangskuser.Simple
		r        *room.Room
	)

	// 获取表情包的持有对象信息
	err := livesticker.DB().Take(&pkgOwner, "package_id = ?", m.PackageID).Error
	if err != nil {
		return nil, nil, nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if pkgOwner.UserID != 0 {
		u, err = mowangskuser.FindByUserID(pkgOwner.UserID)
		if err != nil {
			return nil, nil, nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if u == nil {
			return nil, nil, nil, actionerrors.ErrCannotFindUser
		}
	}
	if pkgOwner.RoomID != 0 {
		r, err = room.FindOne(bson.M{"room_id": pkgOwner.RoomID}, &room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, nil, nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return nil, nil, nil, actionerrors.ErrCannotFindRoom
		}
	}

	// 获取表情资源信息
	sticker, err := livesticker.FindSticker(m.StickerID)
	if err != nil {
		return nil, nil, nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if sticker == nil {
		return nil, nil, nil, actionerrors.ErrNotFound("查询不到配置对应的表情资源")
	}
	return u, r, sticker, nil
}
