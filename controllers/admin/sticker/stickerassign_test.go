package sticker

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestStickerAssignParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	notExistedStickerID := 100
	require.NoError(livesticker.DB().Delete(&livesticker.LiveSticker{}, "id = ?", notExistedStickerID).Error)
	testCases := []struct {
		name          string
		param         stickerAssignParam
		expectedError error
	}{
		{
			name: "未传表情",
			param: stickerAssignParam{
				StickerIDs: "",
			},
			expectedError: actionerrors.ErrParamsMsg("未上传表情 ID"),
		}, {
			name: "输入负数的时间",
			param: stickerAssignParam{
				StickerIDs: "1",
				StartTime:  -1,
				ExpireTime: 0,
			},
			expectedError: actionerrors.ErrParams,
		}, {
			name: "开始时间早于当前时间",
			param: stickerAssignParam{
				StickerIDs: "1",
				StartTime:  100,
			},
			expectedError: actionerrors.NewErrForbidden("开始时间不得早于当前时间！"),
		}, {
			name: "结束时间早于当前时间",
			param: stickerAssignParam{
				StickerIDs: "1",
				ExpireTime: 99,
			},
			expectedError: actionerrors.NewErrForbidden("结束时间不得早于当前时间！"),
		}, {
			name: "房间号 / M号上传格式错误",
			param: stickerAssignParam{
				OwnerIDs:   "1，2，3",
				StickerIDs: "1",
			},
			expectedError: actionerrors.ErrParamsMsg("房间号 / M号上传格式有误"),
		}, {
			name: "未传房间号 / M号",
			param: stickerAssignParam{
				OwnerIDs:   "",
				StickerIDs: "1",
			},
			expectedError: actionerrors.ErrParamsMsg("未上传房间号 / M号"),
		}, {
			name: "有不存在的房间号",
			param: stickerAssignParam{
				OwnerIDs:    "1111,18113499",
				PackageType: livesticker.TypeRoom,
				StickerIDs:  "1",
			},
			expectedError: actionerrors.ErrParamsMsg("有不存在的房间号，请检查后重试！"),
		}, {
			name: "有不存在的M号",
			param: stickerAssignParam{
				OwnerIDs:    "1111",
				PackageType: livesticker.TypeUser,
				StickerIDs:  "1",
			},
			expectedError: actionerrors.ErrParamsMsg("有不存在的M号，请检查后重试！"),
		}, {
			name: "有不存在的表情 ID",
			param: stickerAssignParam{
				OwnerIDs:    "18113499",
				PackageType: livesticker.TypeRoom,
				StickerIDs:  "100",
			},
			expectedError: actionerrors.ErrParamsMsg("有不存在的表情 ID，请检查后重试！"),
		}, {
			name: "正常处理的请求",
			param: stickerAssignParam{
				OwnerIDs:    "18113499",
				PackageType: livesticker.TypeRoom,
				StickerIDs:  "1",
			},
			expectedError: nil,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			err := testCase.param.check()
			assert.Equal(testCase.expectedError, err)
		})
	}
}

func TestStickerAssignParam_preparePackages(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	typeRoomParam := stickerAssignParam{
		PackageType: livesticker.TypeRoom,
		OwnerIDs:    "100，4381915",
		StickerIDs:  "1",
		StartTime:   timeNowUnix,

		ownerIDs: []int64{100, 4381915},
		Confirm:  0,
	}
	roomPkgIDs, err := typeRoomParam.preparePackages()
	require.NoError(err)
	assert.Equal(1, len(roomPkgIDs))

	typeRoomParam = stickerAssignParam{
		PackageType: livesticker.TypeRoom,
		OwnerIDs:    "100，4381915",
		StickerIDs:  "1",
		StartTime:   timeNowUnix,

		ownerIDs: []int64{100, 4381915},
		Confirm:  1,
	}
	roomPkgIDs, err = typeRoomParam.preparePackages()
	require.NoError(err)
	assert.Equal(2, len(roomPkgIDs))

	typeUserParam := stickerAssignParam{
		PackageType: livesticker.TypeUser,
		OwnerIDs:    "101",
		StickerIDs:  "1",
		StartTime:   timeNowUnix,

		ownerIDs: []int64{101},
		Confirm:  1,
	}
	userPkgIds, err := typeUserParam.preparePackages()
	require.NoError(err)
	assert.Equal(1, len(userPkgIds))

	require.NoError(livesticker.DB().Table(livesticker.Package{}.TableName()).
		Delete(nil, "create_time = ?", timeNowUnix).Error)
	require.NoError(livesticker.DB().Table(livesticker.PackageOwner{}.TableName()).
		Delete(nil, "create_time = ?", timeNowUnix).Error)
}

func TestStickerAssignParam_checkConflictStickerAssignment(t *testing.T) {
	assert := assert.New(t)

	timeUnix := int64(1682260000)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(timeUnix, 0)
	})
	defer cancel()

	testCases := []struct {
		name          string
		param         stickerAssignParam
		expectedError error
	}{
		// 测试数据中表情 1 分配给表情包 1，有效期从 1682265600 到 2000000000
		{
			name: "生效时间在已有配置区间内",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{1},
				pkgIDs:      []int64{1},
				StartTime:   1682265601,
				ExpireTime:  2000000001,
			},
			expectedError: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [1]"),
		}, {
			name: "失效时间在已有配置区间内",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{1},
				pkgIDs:      []int64{1},
				StartTime:   1682265699,
				ExpireTime:  1999999999,
			},
			expectedError: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [1]"),
		}, {
			name: "新的区间包含已配置区间",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{1},
				pkgIDs:      []int64{1},
				StartTime:   1682265699,
				ExpireTime:  2000000001,
			},
			expectedError: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [1]"),
		}, {
			name: "新的区间在已配置区间之前",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{1},
				pkgIDs:      []int64{1},
				StartTime:   0,
				ExpireTime:  1682265000,
			},
			expectedError: nil,
		}, {
			name: "新的区间在已配置区间之后",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{1},
				pkgIDs:      []int64{1},
				StartTime:   2000000001,
				ExpireTime:  0,
			},
			expectedError: nil,
		},
		// 测试数据中表情 3 分配给表情包 2，有效期从 1682265600 到永久
		{
			name: "新的永久区间包含已配置区间",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{3},
				pkgIDs:      []int64{2},
				StartTime:   1682265000,
				ExpireTime:  0,
			},
			expectedError: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [3]"),
		}, {
			name: "新的永久区间在已配置区间内",
			param: stickerAssignParam{
				PackageType: livesticker.TypeRoom,
				stickerIDs:  []int64{3},
				pkgIDs:      []int64{2},
				StartTime:   1682266000,
				ExpireTime:  0,
			},
			expectedError: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [3]"),
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			err := testCase.param.checkConflictStickerAssignment()
			assert.Equal(testCase.expectedError, err)
		})
	}
}

func TestStickerAssignParam_insertPackageMap(t *testing.T) {
	require := require.New(t)

	timeUnix := int64(1682265000)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(timeUnix, 0)
	})
	defer cancel()

	param := stickerAssignParam{
		PackageType: livesticker.TypeRoom,
		stickerIDs:  []int64{1, 2, 3},
		pkgIDs:      []int64{101, 102, 103},
		StartTime:   timeUnix,
	}
	err := param.insertPackageMap()
	require.NoError(err)
	var pkgMaps []livesticker.PackageStickerMap
	err = livesticker.DB().Table(livesticker.PackageStickerMap{}.TableName()).
		Find(&pkgMaps, "start_time = ?", timeUnix).Error
	require.NoError(err)
	require.Len(pkgMaps, len(param.stickerIDs)*len(param.pkgIDs))
}

func TestActionStickerAssignAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	// 测试数据中已有配置
	param := stickerAssignParam{
		PackageType: livesticker.TypeRoom,
		OwnerIDs:    "4381915", // package_id = 2
		StickerIDs:  "1",
		StartTime:   0,
		ExpireTime:  0,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err := ActionStickerAssignAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [6]"), err)

	// 验证弹窗消息
	param = stickerAssignParam{
		PackageType: livesticker.TypeUser,
		OwnerIDs:    "123",
		StickerIDs:  "3,4",
		StartTime:   timeNowUnix,
		ExpireTime:  0,
		Confirm:     0,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err = ActionStickerAssignAdd(c)
	assert.Equal(err.(*handler.ActionError).Info["msg"],
		fmt.Sprintf("确认分配表情吗？<br> 用户昵称：test_user <br>表情：3 专属表情 1；4 专属表情 2<br>开始时间：%s<br>结束时间：永久生效",
			time.Unix(timeNowUnix, 0).Format(util.TimeFormatYMDHMS)))

	// 成功分配表情
	param = stickerAssignParam{
		PackageType: livesticker.TypeUser,
		OwnerIDs:    "123",
		StickerIDs:  "3,4",
		StartTime:   timeNowUnix,
		ExpireTime:  0,
		Confirm:     1,
	}
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err = ActionStickerAssignAdd(c)
	require.NoError(err)
	var pkgMaps []livesticker.PackageStickerMap
	err = livesticker.DB().Table(livesticker.PackageStickerMap{}.TableName()).
		Find(&pkgMaps, "start_time = ?", timeNowUnix).Error
	require.NoError(err)
	require.Equal(2, len(pkgMaps)) // 为用户分配了 2 个表情
	require.NoError(livesticker.DB().Delete(livesticker.PackageStickerMap{}, "start_time = ?", timeNowUnix).Error)
}

func TestStickerAssignUpdateParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time {
		return timeNow
	})
	defer cancel()

	// 构建一条重复的数据测试更新时的配置冲突
	conflictMap := livesticker.PackageStickerMap{
		ID:         123,
		PackageID:  2,
		StickerID:  1,
		StartTime:  timeNow.Unix(),
		ExpireTime: 0,
	}
	require.NoError(livesticker.DB().Delete(livesticker.PackageStickerMap{}, "start_time = ?", timeNow.Unix()).Error)
	require.NoError(livesticker.DB().Create(&conflictMap).Error)

	testCases := []struct {
		name        string
		param       stickerAssignUpdateParam
		expectedErr error
	}{
		{
			name: "配置 ID 不存在",
			param: stickerAssignUpdateParam{
				ID: 100,
			},
			expectedErr: actionerrors.ErrNotFound("配置 ID 不存在！"),
		},
		{
			name: "开始时间早于当前时间",
			param: stickerAssignUpdateParam{
				ID:        1,
				StartTime: timeNow.Unix() - 100,
			},
			expectedErr: actionerrors.NewErrForbidden("开始时间不得早于当前时间！"),
		},
		{
			name: "结束时间早于当前时间",
			param: stickerAssignUpdateParam{
				ID:         1,
				StartTime:  timeNow.Unix(),
				ExpireTime: timeNow.Unix() - 100,
			},
			expectedErr: actionerrors.NewErrForbidden("结束时间不得早于当前时间！"),
		},
		{
			name: "与已有配置冲突",
			param: stickerAssignUpdateParam{
				ID:         6,
				StartTime:  timeNow.Unix(),
				ExpireTime: 0,
			},
			expectedErr: actionerrors.ErrParamsMsg("与已有配置冲突，请检查后重试！冲突 ID: [123]"),
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			err := testCase.param.check()
			assert.Equal(testCase.expectedErr, err)
		})
	}
}

func TestStickerAssignUpdateParam_confirmMsg(t *testing.T) {
	assert := assert.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	param := stickerAssignUpdateParam{
		StartTime:  0,
		ExpireTime: 0,
		pkgStickerMap: &livesticker.PackageStickerMap{
			StartTime:  timeNowUnix,
			ExpireTime: 0,
		},
		user:    &mowangskuser.Simple{Username: "测试用户"},
		sticker: &livesticker.LiveSticker{ID: 1, Name: "测试表情"},
	}
	confirmErr := param.confirmError()
	assert.Equal(fmt.Sprintf("确认修改表情配置时间吗？<br>用户昵称：%s<br>表情：%d %s<br>原开始时间：%s<br>原结束时间：%s<br>新开始时间：%s<br>新结束时间：%s<br>",
		param.user.Username, param.sticker.ID, param.sticker.Name, timeFormat(param.pkgStickerMap.StartTime), timeFormat(param.pkgStickerMap.ExpireTime), timeFormat(param.StartTime), timeFormat(param.ExpireTime)),
		confirmErr.Info["msg"])

	param = stickerAssignUpdateParam{
		StartTime:  0,
		ExpireTime: 0,
		pkgStickerMap: &livesticker.PackageStickerMap{
			StartTime:  timeNowUnix,
			ExpireTime: 0,
		},
		room: &room.Room{
			Helper: room.Helper{CreatorUsername: "测试主播"},
		},
		sticker: &livesticker.LiveSticker{ID: 1, Name: "测试表情"},
	}
	confirmErr = param.confirmError()
	assert.Equal(fmt.Sprintf("确认修改表情配置时间吗？<br>主播昵称：%s<br>表情：%d %s<br>原开始时间：%s<br>原结束时间：%s<br>新开始时间：%s<br>新结束时间：%s<br>",
		param.room.CreatorUsername, param.sticker.ID, param.sticker.Name, timeFormat(param.pkgStickerMap.StartTime), timeFormat(param.pkgStickerMap.ExpireTime), timeFormat(param.StartTime), timeFormat(param.ExpireTime)),
		confirmErr.Info["msg"])
}

func TestActionStickerAssignUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	existedMap := livesticker.PackageStickerMap{
		ID:           100,
		CreateTime:   timeNowUnix,
		ModifiedTime: timeNowUnix,
		PackageID:    2,
		StickerID:    2,
		StartTime:    timeNowUnix,
		ExpireTime:   timeNowUnix + 100,
	}
	require.NoError(livesticker.DB().Create(&existedMap).Error)

	param := stickerAssignUpdateParam{
		ID:         100,
		StartTime:  timeNowUnix + 100,
		ExpireTime: timeNowUnix + 1000,
		Confirm:    0,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err := ActionStickerAssignUpdate(c)
	assert.True(strings.HasPrefix(err.(*handler.ActionError).Info["msg"].(string), "确认修改表情配置时间吗？<br>主播昵称："))

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, msg, err := ActionStickerAssignUpdate(c)
	require.NoError(err)
	assert.Equal("修改成功！", msg)

	var result livesticker.PackageStickerMap
	require.NoError(livesticker.DB().Take(&result, "id = ?", param.ID).Error)
	assert.Equal(param.StartTime, result.StartTime)
	assert.Equal(param.ExpireTime, result.ExpireTime)
	require.NoError(livesticker.DB().Delete(&existedMap).Error)
}

func TestStickerAssignDelParam_check(t *testing.T) {
	assert := assert.New(t)

	timeNow := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time {
		return timeNow
	})
	defer cancel()

	testCases := []struct {
		name        string
		param       stickerAssignDelParam
		expectedErr error
	}{
		{
			name: "配置 ID 不存在",
			param: stickerAssignDelParam{
				ID: 100,
			},
			expectedErr: actionerrors.ErrParamsMsg("配置 ID 不存在！"),
		},
		{
			name: "配置 ID 存在",
			param: stickerAssignDelParam{
				ID: 1,
			},
			expectedErr: nil,
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			err := testCase.param.check()
			assert.Equal(testCase.expectedErr, err)
		})
	}
}

func TestStickerAssignDelParam_confirmMsg(t *testing.T) {
	assert := assert.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	param := stickerAssignDelParam{
		pkgStickerMap: &livesticker.PackageStickerMap{
			StartTime:  timeNowUnix,
			ExpireTime: 0,
		},
		user:    &mowangskuser.Simple{Username: "测试用户"},
		sticker: &livesticker.LiveSticker{ID: 1, Name: "测试表情"},
	}
	confirmErr := param.confirmError()
	assert.Equal(fmt.Sprintf("确认删除表情配置吗？<br>用户昵称：%s<br>表情：%d %s<br>开始时间：%s<br>结束时间：%s",
		param.user.Username, param.sticker.ID, param.sticker.Name, timeFormat(param.pkgStickerMap.StartTime), timeFormat(param.pkgStickerMap.ExpireTime)),
		confirmErr.Info["msg"])

	param = stickerAssignDelParam{
		pkgStickerMap: &livesticker.PackageStickerMap{
			StartTime:  timeNowUnix,
			ExpireTime: 0,
		},
		room: &room.Room{
			Helper: room.Helper{CreatorUsername: "测试主播"},
		},
		sticker: &livesticker.LiveSticker{ID: 1, Name: "测试表情"},
	}
	confirmErr = param.confirmError()
	assert.Equal(fmt.Sprintf("确认删除表情配置吗？<br>主播昵称：%s<br>表情：%d %s<br>开始时间：%s<br>结束时间：%s",
		param.room.CreatorUsername, param.sticker.ID, param.sticker.Name, timeFormat(param.pkgStickerMap.StartTime), timeFormat(param.pkgStickerMap.ExpireTime)),
		confirmErr.Info["msg"])
}

func TestActionStickerAssignDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNowUnix := goutil.TimeNow().Unix()
	existedMap := livesticker.PackageStickerMap{
		ID:           100,
		CreateTime:   timeNowUnix,
		ModifiedTime: timeNowUnix,
		PackageID:    2,
		StickerID:    2,
		StartTime:    timeNowUnix,
		ExpireTime:   timeNowUnix + 100,
	}
	require.NoError(livesticker.DB().Create(&existedMap).Error)

	param := stickerAssignUpdateParam{
		ID:      100,
		Confirm: 0,
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, _, err := ActionStickerAssignDel(c)
	require.IsType(&handler.ActionError{}, err)
	assert.True(strings.HasPrefix(err.(*handler.ActionError).Info["msg"].(string), "确认删除表情配置吗？<br>主播昵称："))

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, msg, err := ActionStickerAssignDel(c)
	require.NoError(err)
	assert.Equal("删除成功！", msg)

	var result livesticker.PackageStickerMap
	err = livesticker.DB().Take(&result, "id = ?", param.ID).Error
	assert.True(servicedb.IsErrNoRows(err))
}

func TestGetPackageStickerMapDetails(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name        string
		pkgMap      *livesticker.PackageStickerMap
		expectedErr error
	}{
		{
			name: "查询不到用户",
			pkgMap: &livesticker.PackageStickerMap{
				PackageID: 3,
			},
			expectedErr: actionerrors.ErrCannotFindUser,
		},
		{
			name: "查询不到表情",
			pkgMap: &livesticker.PackageStickerMap{
				PackageID: 2,
			},
			expectedErr: actionerrors.ErrNotFound("查询不到配置对应的表情资源"),
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			_, _, _, err := getPackageStickerMapDetails(testCase.pkgMap)
			assert.Equal(testCase.expectedErr, err)
		})
	}
}
