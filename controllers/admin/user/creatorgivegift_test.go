package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	creatorID1      = 1
	creatorID370478 = 370478
)

func TestNewSetCreatorGiftParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	endTime := int64(9123456789)

	clearAPI := "/api/v2/admin/user/clearcreatorgift"
	c := handler.NewTestContext("POST", clearAPI, true, setCreatorGiftParam{
		GiftID:     0,
		CreatorIDs: "",
	})
	_, err := newSetCreatorGiftParam(c, false)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试礼物不存在
	c = handler.NewTestContext("POST", clearAPI, true, setCreatorGiftParam{
		GiftID:     40002, // 不存在礼物
		CreatorIDs: "1",
	})
	_, err = newSetCreatorGiftParam(c, false)
	assert.EqualError(err, "礼物不存在")

	// 测试重复 ID
	c = handler.NewTestContext("POST", clearAPI, true, setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,1,370478",
	})
	_, err = newSetCreatorGiftParam(c, false)
	assert.EqualError(err, "主播 1 重复输入")

	// 测试主播不存在
	c = handler.NewTestContext("POST", clearAPI, true, setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,2,3,3456835",
	})
	_, err = newSetCreatorGiftParam(c, false)
	assert.EqualError(err, "主播 2, 3, 3456835 不存在")

	// 清空背包礼物 正常情况
	giveAPI := "/api/v2/admin/user/givecreatorgift"
	c = handler.NewTestContext("POST", giveAPI, true, setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,370478",
	})
	param, err := newSetCreatorGiftParam(c, false)
	require.NoError(err)
	assert.Equal([]int64{creatorID1, creatorID370478}, param.arrCreatorIDs)
	assert.Equal(int64(40001), param.GiftID)

	// 发放背包礼物
	c = handler.NewTestContext("POST", giveAPI, true, setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "123,3456835",
	})
	_, err = newSetCreatorGiftParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("POST", giveAPI, true, setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,370478",
		Num:        3,
		EndTime:    endTime,
	})
	param, err = newSetCreatorGiftParam(c, true)
	require.NoError(err)
	assert.Equal([]int64{creatorID1, creatorID370478}, param.arrCreatorIDs)
	assert.Equal(int64(40001), param.GiftID)
	assert.Equal(int64(3), param.Num)
	assert.Equal(endTime, param.EndTime)
}

func TestActionGiveCreatorGiftAndActionClearCreatorGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var endTime int64 = 9123456789
	// 测试发放不存在的礼物
	param := setCreatorGiftParam{
		GiftID:     123,
		CreatorIDs: "1",
		Num:        3,
		EndTime:    endTime,
	}
	giveAPI := "/api/v2/admin/user/givecreatorgift"
	c := handler.NewTestContext("POST", giveAPI, true, param)
	_, err := ActionGiveCreatorGift(c)
	assert.EqualError(err, "礼物不存在")

	// 测试发放不支持的礼物
	param = setCreatorGiftParam{
		GiftID:     3,
		CreatorIDs: "1",
		Num:        3,
		EndTime:    endTime,
	}
	c = handler.NewTestContext("POST", giveAPI, true, param)
	_, err = ActionGiveCreatorGift(c)
	assert.EqualError(err, "该礼物不支持")

	// 测试正常发放
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{
		"user_id": bson.M{"$in": []int64{1, 370478}},
		"gift_id": 40001,
	})
	require.NoError(err)
	param = setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,370478",
		Num:        3,
		EndTime:    endTime,
	}
	c = handler.NewTestContext("POST", giveAPI, true, param)
	res, err := ActionGiveCreatorGift(c)
	require.NoError(err)
	assert.Equal("礼物发放成功", res)
	// 测试发放不支持的礼物
	param = setCreatorGiftParam{
		GiftID:     8,
		CreatorIDs: "1",
	}
	clearAPI := "/api/v2/admin/user/backpack/clearcreatorgift"
	c = handler.NewTestContext("POST", clearAPI, true, param)
	_, err = ActionClearCreatorGift(c)
	assert.EqualError(err, "该礼物不支持")
	// 测试正常清空
	param = setCreatorGiftParam{
		GiftID:     40001,
		CreatorIDs: "1,370478",
	}
	c = handler.NewTestContext("POST", clearAPI, true, param)
	res, err = ActionClearCreatorGift(c)
	require.NoError(err)
	assert.Equal("清空礼物 40001，主播 1: 3 个、370478: 3 个 清空成功", res)
}
