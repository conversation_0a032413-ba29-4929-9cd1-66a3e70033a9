package user

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	userID123     = 123
	userID3456835 = 3456835
)

func TestNewSetGiftParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	endTime := int64(9123456789)

	clearURL := "/api/v2/admin/user/cleargift"
	c := handler.NewTestContext(http.MethodPost, clearURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  0,
		UserIDs: "",
	})
	_, err := newSetGiftParam(c, false)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试礼物不存在
	c = handler.NewTestContext(http.MethodPost, clearURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  40002, // 不存在礼物
		UserIDs: "1",
	})
	_, err = newSetGiftParam(c, false)
	assert.EqualError(err, "礼物不存在")

	// 测试重复 ID
	c = handler.NewTestContext(http.MethodPost, clearURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "1,1,3",
	})
	_, err = newSetGiftParam(c, false)
	assert.EqualError(err, "用户 1 重复输入")

	// 测试用户不存在
	c = handler.NewTestContext(http.MethodPost, clearURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "1,2,3,3456835",
	})
	_, err = newSetGiftParam(c, false)
	assert.EqualError(err, "用户 1, 2, 3 不存在")

	// 清空背包礼物 正常情况
	c = handler.NewTestContext(http.MethodPost, clearURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "123,3456835",
	})
	param, err := newSetGiftParam(c, false)
	require.NoError(err)
	assert.Equal([]int64{userID123, userID3456835}, param.arrUserIDs)
	assert.Equal(useritems.GiftID40001, param.GiftID)

	// 发放背包礼物
	giveURL := "/api/v2/admin/user/givegift"
	c = handler.NewTestContext(http.MethodPost, giveURL, true, setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "123,3456835",
	})
	_, err = newSetGiftParam(c, true)
	assert.Equal(actionerrors.ErrParams, err)

	p := setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "123,3456835",
		Num:     3,
		EndTime: endTime,
	}
	c = handler.NewTestContext(http.MethodPost, giveURL, true, p)
	_, err = newSetGiftParam(c, true)
	require.Equal(err, actionerrors.ErrConfirmRequired(
		"确认给 2 个用户发放 幻雪冰城 × 3 吗？", 1, true))
	p.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, giveURL, true, p)
	param, err = newSetGiftParam(c, true)
	require.NoError(err)
	assert.Equal([]int64{userID123, userID3456835}, param.arrUserIDs)
	assert.Equal(useritems.GiftID40001, param.GiftID)
	assert.Equal(int64(3), param.Num)
	assert.Equal(int64(endTime), param.EndTime)

	c = handler.NewTestContext(http.MethodPost, giveURL, true, setGiftParam{
		Type:    useritems.BpItemTypeItem,
		ItemID:  312312312213123123,
		UserIDs: "123,3456835",
		Num:     3,
		EndTime: endTime,
	})
	_, err = newSetGiftParam(c, true)
	assert.EqualError(err, "道具不存在")

	c = handler.NewTestContext(http.MethodPost, giveURL, true, setGiftParam{
		Type:    useritems.BpItemTypeItem,
		ItemID:  1,
		UserIDs: "123,3456835",
		Num:     3,
		EndTime: endTime,
	})
	param, err = newSetGiftParam(c, true)
	require.NoError(err)
	require.NotNil(param.item)
	assert.NotNil(param.item)
}

func TestSetGiftParam_give(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUsers = []int64{133123123123}
		testItem  = &backpackitem.LiveBackpackItem{ID: 1, MoreInfo: &backpackitem.More{VipID: 12}}
		testGift  = &gift.Gift{GiftID: useritems.GiftID40001, Type: gift.TypeFree}
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := useritems.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testUsers}})
	require.NoError(err)

	param := setGiftParam{
		Type:       useritems.BpItemTypeItem,
		EndTime:    goutil.TimeNow().Add(time.Hour * 24 * 30).Unix(),
		Num:        9,
		item:       testItem,
		arrUserIDs: testUsers,
		c:          handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	resp, err := param.give()
	require.NoError(err)
	assert.Equal("道具发放成功", resp)
	count, err := useritems.CountItemNum(testUsers[0], testItem.ID)
	require.NoError(err)
	assert.EqualValues(param.Num, count)

	param = setGiftParam{
		Type:       useritems.BpItemTypeGift,
		EndTime:    goutil.TimeNow().Add(time.Hour * 24 * 30).Unix(),
		Num:        19,
		gift:       testGift,
		arrUserIDs: testUsers,
		c:          handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	resp, err = param.give()
	require.NoError(err)
	assert.Equal("礼物发放成功", resp)
	count, err = useritems.CountGiftNum(testUsers[0], testGift.GiftID)
	require.NoError(err)
	assert.EqualValues(param.Num, count)
}

func TestActionGiveGiftAndActionClearGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	endTime := int64(9123456789)
	// 测试发放不存在的礼物
	param := setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  123,
		UserIDs: "123",
		Num:     3,
		EndTime: endTime,
	}
	c := handler.NewTestContext("POST", "/api/v2/admin/user/givegift", true, param)
	_, err := ActionGiveGift(c)
	assert.EqualError(err, "礼物不存在")

	// 测试发放不支持的礼物
	param = setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  3,
		UserIDs: "123",
		Num:     3,
		EndTime: endTime,
	}
	c = handler.NewTestContext("POST", "/api/v2/admin/user/givegift", true, param)
	_, err = ActionGiveGift(c)
	assert.EqualError(err, "该礼物不支持")

	// 测试正常发放
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{
		"user_id": bson.M{"$in": []int64{123, 3456835}},
		"gift_id": 40001,
	})
	require.NoError(err)
	param = setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "123,3456835",
		Num:     3,
		EndTime: endTime,
		Confirm: 1,
	}
	c = handler.NewTestContext("POST", "/api/v2/admin/user/givegift", true, param)
	res, err := ActionGiveGift(c)
	require.NoError(err)
	assert.Equal("礼物发放成功", res)
	// 测试发放不支持的礼物
	param = setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  8,
		UserIDs: "123",
	}
	c = handler.NewTestContext("POST", "/api/v2/admin/user/givegift", true, param)
	_, err = ActionClearGift(c)
	assert.EqualError(err, "该礼物不支持")
	// 测试正常清空
	param = setGiftParam{
		Type:    useritems.BpItemTypeGift,
		GiftID:  useritems.GiftID40001,
		UserIDs: "123,3456835",
	}
	c = handler.NewTestContext("POST", "/api/v2/admin/user/cleargift", true, param)
	res, err = ActionClearGift(c)
	require.NoError(err)
	assert.Equal("清空礼物 幻雪冰城 (40001)，用户 123: 3 个、3456835: 3 个 清空成功", res)

	// 测试清空道具
	var (
		testUserID = int64(123)
		testItemID = int64(1)
	)
	service.Cache5Min.Flush()
	cleanup := mrpc.SetMock(vip.URLVipList, func(any) (any, error) {
		return handler.M{"Datas": []handler.M{
			{"id": 12, "level": 4, "title": "大咖"},
		}}, nil
	})
	defer cleanup()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	err = useritems.AddItemToUsers([]int64{testUserID}, &backpackitem.LiveBackpackItem{ID: testItemID, Type: backpackitem.TypeNobleTrialCard},
		1111, useritems.SourceAdmin, 0, goutil.TimeNow().AddDate(0, 0, 1).Unix())
	require.NoError(err)
	param = setGiftParam{
		Type:    useritems.BpItemTypeItem,
		ItemID:  testItemID,
		UserIDs: "123",
	}
	c = handler.NewTestContext("POST", "/api/v2/admin/user/cleargift", true, param)
	res, err = ActionClearGift(c)
	require.NoError(err)
	assert.Equal(fmt.Sprintf("清空道具 大咖体验卡 1 天 (%d)，用户 %d: %d 个 清空成功", param.ItemID, testUserID, 1111), res)
}

func TestSetGiftParam_typeName(t *testing.T) {
	assert := assert.New(t)

	param := &setGiftParam{
		Type: useritems.BpItemTypeGift,
	}
	name := param.typeName()
	assert.Equal("礼物", name)

	param = &setGiftParam{
		Type: useritems.BpItemTypeItem,
	}
	name = param.typeName()
	assert.Equal("道具", name)

	param = &setGiftParam{
		Type: 312312312,
	}
	assert.PanicsWithValue("背包道具类型错误", func() {
		param.typeName()
	})
}

func TestSetGiftParam_backpackItemName(t *testing.T) {
	assert := assert.New(t)

	param := &setGiftParam{
		gift: &gift.Gift{
			Name: "哈哈哈哈哈",
		},
		item: &backpackitem.LiveBackpackItem{
			Name: "呜呜呜呜呜",
		},
	}

	param.Type = useritems.BpItemTypeGift
	assert.Equal(param.gift.Name, param.backpackItemName())

	param.Type = useritems.BpItemTypeItem
	assert.Equal(param.item.Name, param.backpackItemName())

	param.Type = 312312312
	assert.PanicsWithValue("背包道具类型错误", func() {
		param.backpackItemName()
	})
}
