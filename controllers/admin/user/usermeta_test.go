package user

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

func TestActionRecommendNumUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := userstatus.GeneralSetOne(bson.M{"user_id": 9074509}, bson.M{"recommend_num": 5})
	require.NoError(err)

	msgSent := false
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		msgSent = true
		return "success", nil
	})
	defer cancel()

	body := recommendNumUpdateParam{
		UserID:  9074509,
		Type:    recommendNumUpdateTypeAdd,
		Confirm: 1,
		Num:     1,
	}
	c := handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	_, err = ActionRecommendNumUpdate(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.Reason = "test"
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	resp, err := ActionRecommendNumUpdate(c)
	require.NoError(err)
	assert.Equal("success", resp)
	assert.True(msgSent)
}

func TestNewRecommendNumUpdateParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := recommendNumUpdateParam{
		UserID: 9074509,
		Num:    1,
	}
	c := handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	_, err := newRecommendNumUpdateParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.Reason = "test"
	body.Type = 0
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	_, err = newRecommendNumUpdateParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.Type = recommendNumUpdateTypeAdd
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	_, err = newRecommendNumUpdateParam(c)
	assert.EqualError(err, "确认为 <b>上神</b> 用户 <b>上神测试用户</b> 增加 1 次神话推荐机会吗？")

	body.Confirm = 1
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	param, err := newRecommendNumUpdateParam(c)
	require.NoError(err)
	assert.Equal(1, param.updateNum)

	body.Type = recommendNumUpdateTypeSub
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	param, err = newRecommendNumUpdateParam(c)
	require.NoError(err)
	assert.Equal(-1, param.updateNum)

	body.UserID = 349524
	c = handler.NewTestContext("POST", "/api/v2/admin/user/recommendnum/update", true, body)
	_, err = newRecommendNumUpdateParam(c)
	assert.EqualError(err, "该用户不是神话 / 上神贵族")
}

func TestRecommendNumUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := userstatus.GeneralSetOne(bson.M{"user_id": 907450901}, bson.M{"recommend_num": 1})
	require.NoError(err)
	param := recommendNumUpdateParam{
		UserID:    907450901,
		updateNum: 1,
	}
	err = param.recommendNumUpdate()
	require.NoError(err)
	assert.EqualValues(2, param.recommendNum)

	param.updateNum = -3
	err = param.recommendNumUpdate()
	require.EqualError(err, "推荐次数不足，无法扣除！")
}

func TestSendUpdateMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		assert.EqualValues(3013091, systemMsgList[0].UserID)
		assert.EqualValues("神话推荐次数增加", systemMsgList[0].Title)
		return "success", nil
	})
	defer cancel()

	param := recommendNumUpdateParam{
		Type:         recommendNumUpdateTypeAdd,
		UserID:       3013091,
		Reason:       "test",
		Num:          1,
		recommendNum: 10,
		uv: &vip.UserVip{
			Title: "上神",
		},
	}
	param.sendUpdateMessage()
}

func TestUpdateTypeStr(t *testing.T) {
	assert := assert.New(t)

	param := recommendNumUpdateParam{
		Type: recommendNumUpdateTypeAdd,
	}
	assert.Equal("增加", param.updateTypeStr())

	param.Type = recommendNumUpdateTypeSub
	assert.Equal("扣除", param.updateTypeStr())

	param.Type = 3
	assert.PanicsWithValue("type error", func() {
		param.updateTypeStr()
	})
}

func TestActionAdminHornRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  = int64(12)
		testHornNum = 2
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := usermeta.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": testUserID},
		bson.M{"$set": bson.M{"noble_horn_num": testHornNum}}).Err()
	require.NoError(err)

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  0,
		"horn_num": testHornNum,
		"confirm":  0,
	})
	_, _, err = ActionAdminHornRemove(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数错误
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  testUserID,
		"horn_num": 0,
		"confirm":  0,
	})
	_, _, err = ActionAdminHornRemove(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试弹窗内容
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  testUserID,
		"horn_num": 1,
		"confirm":  0,
	})
	_, _, confirmResp := ActionAdminHornRemove(c)
	assert.EqualValues(confirmResp.(*handler.ActionError).Info["msg"],
		fmt.Sprintf("用户 零月 (ID: %d) 当前拥有 %d 个喇叭，确认扣除 1 个喇叭吗？", testUserID, testHornNum))

	// 测试扣除喇叭成功
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  testUserID,
		"horn_num": testHornNum,
		"confirm":  1,
	})
	_, _, err = ActionAdminHornRemove(c)
	require.NoError(err)
	// 断言喇叭数量
	assert.Zero(userstatus.HornNum(testUserID))
}
