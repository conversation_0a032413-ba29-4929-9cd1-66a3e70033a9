package user

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type banUserParams struct {
	UserID   int64  `form:"user_id" json:"user_id" binding:"gt=0"`
	Type     int    `form:"type" json:"type" binding:"oneof=0 1"`
	Duration int64  `form:"duration" json:"duration" binding:"required_with=Type"`
	Reason   string `form:"reason" json:"reason" binding:"required"`
	Confirm  int    `form:"confirm" json:"confirm"`

	user *liveuser.User
}

const (
	richUserLevelLimit = 40
	richUserSpendLimit = 100000 // 单位钻石
)

// ActionBanAdd 直播间封禁用户账号
/**
 * @api {post} /api/v2/admin/user/ban/add 直播间封禁用户账号
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=0,1} [type=0] 禁言时长类型 0: 永久封禁, 1: 按时间封禁
 * @apiParam {Number} [duration] 封禁时长, 单位:秒, 暂只支持按分钟或天封禁
 * @apiParam {String} reason 原因
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "封禁成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "此用户为金主用户，是否继续操作？（金主用户：巨星及以上贵族、40 天内直播消费 1 万元以上或直播等级 50 级及以上）"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionBanAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param banUserParams
	err := c.Bind(&param)
	// TODO: define validator tag for "duration"
	if err != nil || (param.Type == userstatus.TypeBanDuration && param.Duration <= 0) {
		return nil, actionerrors.ErrParams
	}
	param.user, err = liveuser.Find(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	ban, err := userstatus.FindBanned(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if ban != nil {
		return nil, actionerrors.ErrParamsMsg("当前用户已被封禁")
	}
	if param.Confirm == 0 {
		isRichUser, err := param.isRichUser()
		if err != nil {
			return nil, err
		}
		if isRichUser {
			message := "此用户为金主用户，是否继续操作？<br>（金主用户：巨星及以上贵族、40 天内直播消费 1 万元以上或直播等级 50 级及以上）"
			return nil, actionerrors.ErrConfirmRequired(message, 1, true)
		}
	}
	startTime := goutil.TimeNow().Unix()
	err = userstatus.BanUser(param.UserID, startTime, param.Duration, param.Type)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 记录操作日志
	var intro string
	switch param.Type {
	case userstatus.TypeBanDuration:
		intro = fmt.Sprintf("直播间封禁用户账号, 用户 ID: %d, 原因: %s, 封禁到期时间: %s",
			param.UserID, param.Reason, time.Unix(startTime+param.Duration, 0).Format(util.TimeFormatYMDHMS))
	case userstatus.TypeBanForever:
		intro = fmt.Sprintf("直播间封禁用户账号, 用户 ID: %d, 原因: %s, 封禁时长: 永久封禁",
			param.UserID, param.Reason)
	default:
		panic(fmt.Sprintf("unsupported type: %d", param.Type))
	}
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddWithChannelID(userapi.CatalogAddBannedUser, param.UserID, intro)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "封禁成功", nil
}

// 金主: 巨星及以上贵族、40 天内直播消费 1 万元以上或直播等级 50 级及以上
func (p banUserParams) isRichUser() (bool, error) {
	if usercommon.Level(p.user.Contribution) >= richUserLevelLimit {
		return true, nil
	}
	uv, err := vip.UserActivatedVip(p.UserID, false, nil)
	if err != nil {
		return false, actionerrors.NewErrServerInternal(err, nil)
	}
	if vip.IsVipGte(uv, vip.TypeLiveNoble, vip.NobleLevel5) {
		return true, nil
	}
	consumption, err := userconsumption.FindUserConsumptionInLast40Days(p.UserID)
	if err != nil {
		return false, actionerrors.NewErrServerInternal(err, nil)
	}
	if consumption >= richUserSpendLimit {
		return true, nil
	}
	return false, nil
}

// ActionBanRemove 直播间解封用户账号
/**
 * @api {post} /api/v2/admin/user/ban/remove 直播间解封用户账号
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "解封成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionBanRemove(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		UserID int64 `form:"user_id" json:"user_id" binding:"gt=0"`
	}
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	ban, err := userstatus.FindBanned(params.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if ban == nil {
		return nil, actionerrors.ErrParamsMsg("当前用户未被封禁")
	}

	if err = userstatus.Unban(params.UserID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 记录管理员操作日志
	intro := fmt.Sprintf("直播间解封用户, 用户 ID: %d", params.UserID)
	adminLogs := userapi.NewAdminLogBox(c)
	adminLogs.AddWithChannelID(userapi.CatalogRemoveBanUser, params.UserID, intro)
	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "解封成功", nil
}
