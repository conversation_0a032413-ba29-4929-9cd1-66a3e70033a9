package user

import (
	"fmt"
	"strings"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type setCreatorGiftParam struct {
	GiftID     int64  `json:"gift_id"`
	CreatorIDs string `json:"creator_ids"`
	Num        int64  `json:"num"`
	EndTime    int64  `json:"end_time"`

	gift          *gift.Gift
	arrCreatorIDs []int64
	c             *handler.Context
}

// ActionGiveCreatorGift 给主播发放礼物
/**
 * @api {post} /api/v2/admin/user/givecreatorgift 给主播发放礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} gift_id 发放礼物 ID
 * @apiParam {Number} num 发放礼物数量
 * @apiParam {String} creator_ids 主播 IDs
 * @apiParam {Number} end_time 礼物过期时间
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "礼物发放成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionGiveCreatorGift(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newSetCreatorGiftParam(c, true)
	if err != nil {
		return nil, err
	}
	return params.giveGift()
}

func newSetCreatorGiftParam(c *handler.Context, isGive bool) (*setCreatorGiftParam, error) {
	var param setCreatorGiftParam
	err := c.Bind(&param)
	if err != nil || param.GiftID <= 0 || param.CreatorIDs == "" {
		return nil, actionerrors.ErrParams
	}
	if isGive && (param.Num <= 0 || param.EndTime <= goutil.TimeNow().Unix()) {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	param.gift, err = gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.gift == nil {
		return nil, actionerrors.ErrNotFound("礼物不存在")
	}
	if !creatoritems.IsBackpackGift(param.gift) {
		return nil, actionerrors.ErrParamsMsg("该礼物不支持")
	}

	// check users
	list, err := util.SplitToInt64Array(param.CreatorIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	repeatList := util.FindInt64Duplicates(list)
	if len(repeatList) != 0 {
		str := goutil.JoinInt64Array(repeatList, ", ")
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("主播 %s 重复输入", str))
	}

	simpleRoomMap, err := room.FindSimpleMapByCreatorID(list, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(simpleRoomMap) != len(list) {
		errList := make([]int64, 0, len(list))
		for _, u := range list {
			if _, ok := simpleRoomMap[u]; !ok {
				errList = append(errList, u)
			}
		}
		str := goutil.JoinInt64Array(errList, ", ")
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("主播 %s 不存在", str))
	}
	param.arrCreatorIDs = list
	return &param, nil
}

func (param *setCreatorGiftParam) giveGift() (string, error) {
	err := creatoritems.AddGiftToCreators(param.arrCreatorIDs, param.gift, param.Num,
		goutil.TimeNow().Unix(), param.EndTime)
	if err != nil {
		return "", actionerrors.NewErrServerInternal(err, nil)
	}
	logbox := goclient.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("发放主播背包礼物 ID：%d、主播 ID：%s、数量 %d、过期时间 %s",
		param.GiftID, param.CreatorIDs, param.Num, time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHHMM))
	logbox.Add(userapi.CatalogManageCreatorBackpack, intro)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "礼物发放成功", nil
}

// ActionClearCreatorGift 清空主播背包礼物
/**
 * @api {post} /api/v2/admin/user/clearcreatorgift 清空主播背包礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {String} creator_ids 主播 IDs
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "礼物清空成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionClearCreatorGift(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newSetCreatorGiftParam(c, false)
	if err != nil {
		return nil, err
	}
	return params.unsetGift()
}

func (param *setCreatorGiftParam) unsetGift() (handler.ActionResponse, error) {
	normalList := make([]string, 0, len(param.arrCreatorIDs))
	var errList []int64
	for _, userID := range param.arrCreatorIDs {
		count, err := creatoritems.CountGiftNum(userID, param.GiftID)
		if err != nil {
			logger.Error(err)
			errList = append(errList, userID)
			continue
		}
		if count == 0 {
			continue
		}
		err = creatoritems.UnsetGift(userID, param.GiftID)
		if err != nil {
			logger.Error(err)
			errList = append(errList, userID)
			continue
		}
		normalList = append(normalList, fmt.Sprintf("%d: %d 个", userID, count))
	}

	logbox := goclient.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("清空礼物 %d", param.GiftID)
	if len(normalList) != 0 {
		intro += fmt.Sprintf("，主播 %s 清空成功", strings.Join(normalList, "、"))
	}
	if len(errList) != 0 {
		intro += fmt.Sprintf("，主播 %s 清空异常", goutil.JoinInt64Array(errList, "、"))
	}
	logbox.Add(userapi.CatalogManageCreatorBackpack, intro)
	err := logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return intro, nil
}
