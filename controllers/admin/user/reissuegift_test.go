package user

import (
	"encoding/json"
	"fmt"
	"testing"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestReissueGiftTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(reissueGiftParam{}, "user_id", "room_id", "gift_id", "gift_num",
		"lucky_gift_id", "lucky_gift_num", "broadcast_type", "confirm")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(reissueGiftParam{}, "user_id", "room_id", "gift_id", "gift_num",
		"lucky_gift_id", "lucky_gift_num", "broadcast_type", "confirm")
}

func TestCheckLuckyGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := reissueGiftParam{
		GiftID:       80001,
		LuckyGiftID:  10001,
		LuckyGiftNum: 1,
	}
	assert.EqualError(param.checkLuckyGift(), "随机礼物不正确")

	param.LuckyGiftID = 80001
	assert.EqualError(param.checkLuckyGift(), "抽出的礼物不在奖池中")

	param.GiftID = 90001
	require.NoError(param.checkLuckyGift())
}

func TestSendEffects(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cannel := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		var body struct {
			RoomID  int64                 `json:"room_id"`
			Payload livegifts.RoomMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotEmpty(payload.Gift)
		assert.Equal(int64(125), payload.Gift.GiftID)
		assert.Equal("test", payload.Gift.Name)
		require.NotEmpty(payload.Combo)
		assert.Equal("https://test.svga", payload.Combo.EffectURL)
		// 测试随机礼物内容
		if body.RoomID == 211681102 {
			require.NotEmpty(payload.Lucky)
			assert.Equal(int64(90001), payload.Lucky.GiftID)
			assert.Equal("lucky", payload.Lucky.Name)
			assert.Equal("lucky.jpg", payload.Lucky.IconURL)
			assert.Equal(int64(1000), payload.Lucky.Price)
			assert.Equal(1, payload.Lucky.Num)
		}

		return "success", nil
	})
	defer cannel()

	param := reissueGiftParam{
		RoomID: 211681101,
		UserID: 12,
		user: &liveuser.Simple{
			UID:      12,
			Username: "bless",
		},
		gift: &gift.Gift{
			GiftID:         125,
			Name:           "test",
			Icon:           "test",
			EffectDuration: 0,
			NotifyDuration: 0,
			Price:          1000,
			Comboable:      1,
			ComboEffect:    "https://test.svga",
			LuckyEffect:    "http://test.mp4",
		},
		GiftNum: 100,
	}
	param.room = new(room.Room)
	param.room.RoomID = param.RoomID
	require.NotPanics(func() { param.sendEffect() })

	param.RoomID = 211681102
	param.gift.Type = gift.TypeDrawReceive
	param.LuckyGiftNum = 1
	param.luckyGift = &gift.Gift{
		GiftID:         90001,
		Name:           "lucky",
		Icon:           "lucky.jpg",
		EffectDuration: 0,
		NotifyDuration: 0,
		Price:          1000,
		Comboable:      1,
		ComboEffect:    "https://test.svga",
	}
	require.NotPanics(func() { param.sendEffect() })
}

func TestNotifyBroadcastAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var notifyCount int
	broadcastAll := mrpc.SetMock("im://broadcast/all", func(input interface{}) (output interface{}, err error) {
		notifyCount++
		var body struct {
			Payload gift.NotifyPayload `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotEmpty(payload.User)
		assert.Equal(int64(12), payload.User.UserID())
		require.NotEmpty(payload.Gift)
		assert.Equal(int64(125), payload.Gift.GiftID)
		require.NotEmpty(payload.NotifyBubble)
		assert.Equal("custom", payload.NotifyBubble.Type)
		assert.Equal(int64(100), payload.NotifyBubble.BubbleID)
		assert.Equal(`<font color="#FFFFFF"><b>bless</b></font> `+
			`<font color="#FFFFFF">给</font> `+
			`<font color="#FFFFFF"><b>test</b></font> `+
			`<font color="#FFFFFF">送出</font> `+
			`<font color="#FFFFFF"><b>100 个test</b></font>`+
			`<font color="#FFFFFF">，快来围观吧~</font>`, payload.Message)
		return "success", nil
	})
	defer broadcastAll()

	broadcast := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		notifyCount++
		var body struct {
			Payload gift.NotifyPayload `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotEmpty(payload.User)
		assert.Equal(int64(12), payload.User.UserID())
		require.NotEmpty(payload.Gift)
		assert.Equal(int64(125), payload.Gift.GiftID)
		require.NotEmpty(payload.NotifyBubble)
		assert.Equal("custom", payload.NotifyBubble.Type)
		assert.Equal(int64(100), payload.NotifyBubble.BubbleID)
		assert.Equal(`<font color="#FFFFFF"><b>bless</b></font> `+
			`<font color="#FFFFFF">给</font> `+
			`<font color="#FFFFFF"><b>test</b></font> `+
			`<font color="#FFFFFF">送出</font> `+
			`<font color="#FFFFFF"><b>1000 个test</b></font>`+
			`<font color="#FFFFFF">，快来围观吧~</font>`, payload.Message)
		return "success", nil
	})
	defer broadcast()

	r := new(room.Room)
	r.CreatorUsername = "test"
	param := reissueGiftParam{
		RoomID: 211681101,
		user: &liveuser.Simple{
			UID:      12,
			Username: "bless",
		},
		room: r,
		gift: &gift.Gift{
			GiftID:         125,
			Name:           "test",
			Icon:           "test",
			EffectDuration: 0,
			NotifyDuration: 0,
			NotifyBubbleID: 100,
			Price:          1000,
			Comboable:      1,
			ComboEffect:    "https://test.svga",
		},
		GiftNum: 100,
	}
	param.BroadcastType = reissueGiftNotifyDisabled
	param.notifyBroadcastAll()
	assert.Zero(notifyCount)
	param.BroadcastType = reissueGiftBroadcastNormal
	param.notifyBroadcastAll()
	assert.Equal(1, notifyCount)
	param.gift.Price = 1
	param.notifyBroadcastAll()
	assert.Equal(1, notifyCount)

	param.BroadcastType = reissueGiftBroadcastRoom
	param.GiftNum = 1000
	param.gift.Price = 1
	param.gift.Type = gift.TypeDrawReceive
	param.luckyGift = &gift.Gift{Name: "12345"}
	param.LuckyGiftNum = 123
	param.notifyBroadcastAll()
	assert.Equal(1, notifyCount)
	param.gift.NotifyMessage = gift.DefaultComboNotifyMessage
	param.notifyBroadcastAll()
	assert.Equal(2, notifyCount)
}

func TestNewReissueGiftParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := reissueGiftParam{
		UserID:        12,
		RoomID:        0,
		GiftID:        125,
		GiftNum:       0,
		BroadcastType: 0,
	}

	c := handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err := newReissueGiftParam(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	param.RoomID = 211681101
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	g := gift.Gift{
		GiftID: 125,
		Order:  0,
	}
	_, err = gift.Collection().DeleteOne(ctx, bson.M{"gift_id": g.GiftID})
	require.NoError(err)

	param.GiftNum = 100
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	require.EqualError(err, "礼物 125 不存在")

	_, err = gift.Collection().InsertOne(ctx, &g)
	require.NoError(err)
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	assert.EqualError(err, "无法找到该聊天室")

	param.GiftID = 301
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	require.EqualError(err, actionerrors.ErrCannotFindRoom.Message)

	param.RoomID = 18113499
	param.Confirm = 1
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	assert.EqualError(err, "送礼人不能是房主")

	param.UserID = 248506
	param.Confirm = 0
	param.BroadcastType = reissueGiftBroadcastRoom
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	assert.Equal(actionerrors.ErrConfirmRequired(
		fmt.Sprintf("确定要补发礼物特效吗？飘屏类型：%s", param.reissueGiftNotifyToMessage()), 1), err)

	param.Confirm = 1
	param.BroadcastType = reissueGiftNotifyDisabled
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	reissue, err := newReissueGiftParam(c)
	require.NoError(err)
	assert.Nil(reissue.bubble)

	param.UserID = 3456835
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	reissue, err = newReissueGiftParam(c)
	require.NoError(err)
	assert.NotNil(reissue.gift)
	assert.NotNil(reissue.user)
	assert.NotNil(reissue.room)

	param.LuckyGiftID = 80001
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	_, err = newReissueGiftParam(c)
	require.EqualError(err, actionerrors.ErrParams.Message)

	param.LuckyGiftNum = 1
	param.GiftID = 90001
	c = handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	reissue, err = newReissueGiftParam(c)
	require.NoError(err)
	require.NotEmpty(reissue.gift)
	require.NotEmpty(reissue.luckyGift)
}

func TestActionReissueGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := reissueGiftParam{
		UserID:        3456835,
		RoomID:        18113499,
		GiftID:        301,
		GiftNum:       100,
		BroadcastType: 0,
		Confirm:       1,
	}

	c := handler.NewTestContext("POST", "/api/v2/admin/user/reissuegift", true, param)
	resp, err := ActionReissueGift(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestReissueGiftParamReissueGiftNotifyToMessage(t *testing.T) {
	assert := assert.New(t)

	var param reissueGiftParam
	param.BroadcastType = reissueGiftBroadcastNormal
	assert.Equal("按照送礼金额正常发送飘屏", param.reissueGiftNotifyToMessage())
	param.BroadcastType = reissueGiftNotifyDisabled
	assert.Equal("不发飘屏", param.reissueGiftNotifyToMessage())
	param.BroadcastType = reissueGiftBroadcastRoom
	assert.Equal("发给指定直播间", param.reissueGiftNotifyToMessage())

	param.BroadcastType = -1
	assert.PanicsWithValue("不支持的飘屏类型", func() {
		param.reissueGiftNotifyToMessage()
	})
}

func TestReissueGiftParam_reissueMultiGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := reissueGiftParam{
		gift: &gift.Gift{Name: "一起送礼物", Price: 100, Comboable: 2, Combos: []gift.Combo{
			{TargetPrice: 100, ComboEffect: "https://test.svga", WebComboEffect: "https://test.svga"},
			{TargetPrice: 1000, RepeatAddPrice: 800, ComboEffect: "https://test-1.svga", WebComboEffect: "https://test-1.svga"},
		}},
		GiftNum: 18,
		room:    &room.Room{},
		user:    &liveuser.Simple{},
	}
	var count int
	cancel := mrpc.SetMock("im://broadcast/many", func(input interface{}) (output interface{}, err error) {
		count++
		var elems []userapi.BroadcastElem
		err = json.Unmarshal(input.(json.RawMessage), &elems)
		require.NoError(err)
		assert.Len(elems, 2)
		return "success", nil
	})
	defer cancel()

	p.reissueMultiGift()
	assert.Equal(1, count)
}
