package user

import (
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type setGiftParam struct {
	Type    int    `json:"type"`
	GiftID  int64  `json:"gift_id"`
	ItemID  int64  `json:"item_id"`
	UserIDs string `json:"user_ids"`
	Num     int64  `json:"num"`
	EndTime int64  `json:"end_time"`
	Confirm int64  `json:"confirm"`

	elementID  int64
	gift       *gift.Gift
	item       *backpackitem.LiveBackpackItem
	arrUserIDs []int64
	c          *handler.Context
}

// ActionGiveGift 发放礼物
/**
 * @api {post} /api/v2/admin/user/givegift 发放礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} type 背包道具类型，1: 背包礼物，2: 背包道具
 * @apiParam {Number} gift_id 发放礼物 ID
 * @apiParam {Number} item_id 发放道具 ID
 * @apiParam {Number} num 发放礼物数量
 * @apiParam {String} user_ids 用户 ID
 * @apiParam {Number} end_time 礼物过期时间
 * @apiParam {number=0,1} [confirm=0] 确认次数，首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "礼物发放成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "确认给 1 个用户发放 礼物 × 1 吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionGiveGift(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newSetGiftParam(c, true)
	if err != nil {
		return nil, err
	}
	resp, err := params.give()
	if err != nil {
		return nil, err
	}
	return resp, err
}

func newSetGiftParam(c *handler.Context, isGive bool) (*setGiftParam, error) {
	var param setGiftParam
	err := c.Bind(&param)
	if err != nil || param.UserIDs == "" {
		return nil, actionerrors.ErrParams
	}
	if isGive && (param.Num <= 0 || param.EndTime <= goutil.TimeNow().Unix()) {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	switch param.Type {
	case useritems.BpItemTypeGift:
		if param.GiftID <= 0 {
			return nil, actionerrors.ErrParams
		}
		param.elementID = param.GiftID
		param.gift, err = gift.FindShowingGiftByGiftID(param.GiftID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.gift == nil {
			return nil, actionerrors.ErrParamsMsg("礼物不存在")
		}
		if !useritems.IsBackpackGift(param.gift) {
			return nil, actionerrors.ErrParamsMsg("该礼物不支持")
		}
	case useritems.BpItemTypeItem:
		if param.ItemID <= 0 {
			return nil, actionerrors.ErrParams
		}
		param.elementID = param.ItemID
		param.item, err = backpackitem.FindOneItem(param.ItemID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.item == nil {
			return nil, actionerrors.ErrParamsMsg("道具不存在")
		}
	default:
		return nil, actionerrors.ErrParamsMsg("背包道具类型错误")
	}

	// check users
	list, err := util.SplitToInt64Array(param.UserIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	repeatList := util.FindInt64Duplicates(list)
	if len(repeatList) != 0 {
		str := goutil.JoinInt64Array(repeatList, ", ")
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("用户 %s 重复输入", str))
	}

	userMap, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": list}}, nil))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userMap) != len(list) {
		errList := make([]int64, 0, len(list))
		for _, u := range list {
			if _, ok := userMap[u]; !ok {
				errList = append(errList, u)
			}
		}
		str := goutil.JoinInt64Array(errList, ", ")
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("用户 %s 不存在", str))
	}
	if param.Confirm == 0 && param.Type == useritems.BpItemTypeGift && isGive {
		message := fmt.Sprintf("确认给 %d 个用户发放 %s × %d 吗？", len(list), param.gift.Name, param.Num)
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	}
	param.arrUserIDs = list
	return &param, nil
}

func (param *setGiftParam) give() (string, error) {
	switch param.Type {
	case useritems.BpItemTypeGift:
		err := useritems.AddGiftToUsers(param.arrUserIDs, param.gift, param.Num, useritems.SourceAdmin,
			goutil.TimeNow().Unix(), param.EndTime)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
	case useritems.BpItemTypeItem:
		err := useritems.AddItemToUsers(param.arrUserIDs, param.item, param.Num, useritems.SourceAdmin,
			goutil.TimeNow().Unix(), param.EndTime)
		if err != nil {
			return "", actionerrors.NewErrServerInternal(err, nil)
		}
	default:
		return "", actionerrors.ErrParamsMsg("背包道具类型错误")
	}

	logbox := goclient.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("发放%s %s (%d)、用户 ID：%s、数量 %d、过期时间 %s",
		param.typeName(), param.backpackItemName(), param.elementID, param.UserIDs, param.Num, time.Unix(param.EndTime, 0).Format(util.TimeFormatYMDHHMM))
	logbox.Add(userapi.CatalogGiveBackpackItem, intro)
	err := logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return fmt.Sprintf("%s发放成功", param.typeName()), nil
}

// ActionClearGift 清空背包礼物
/**
 * @api {post} /api/v2/admin/user/cleargift 清空背包礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} type 背包道具类型，1: 背包礼物，2: 背包道具
 * @apiParam {Number} gift_id 发放礼物 ID
 * @apiParam {Number} item_id 发放道具 ID
 * @apiParam {String} user_ids 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "礼物清空成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionClearGift(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newSetGiftParam(c, false)
	if err != nil {
		return nil, err
	}
	return params.unsetGift()
}

func (param *setGiftParam) unsetGift() (handler.ActionResponse, error) {
	normalList := make([]string, 0, len(param.arrUserIDs))
	var errList []int64
	for _, userID := range param.arrUserIDs {
		var (
			count int64
			err   error
		)
		switch param.Type {
		case useritems.BpItemTypeGift:
			count, err = useritems.CountGiftNum(userID, param.GiftID)
			if err != nil {
				logger.Error(err)
				errList = append(errList, userID)
				continue
			}
			if count == 0 {
				continue
			}
			err = useritems.UnsetGift(userID, param.GiftID)
			if err != nil {
				logger.Error(err)
				errList = append(errList, userID)
				continue
			}
		case useritems.BpItemTypeItem:
			count, err = useritems.CountItemNum(userID, param.ItemID)
			if err != nil {
				logger.Error(err)
				errList = append(errList, userID)
				continue
			}
			if count == 0 {
				continue
			}
			err = useritems.UnsetItem(userID, param.ItemID)
			if err != nil {
				logger.Error(err)
				errList = append(errList, userID)
				continue
			}
		default:
			panic("背包道具类型错误")
		}
		normalList = append(normalList, fmt.Sprintf("%d: %d 个", userID, count))
	}

	logbox := goclient.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("清空%s %s (%d)", param.typeName(), param.backpackItemName(), param.elementID)
	if len(normalList) != 0 {
		intro += fmt.Sprintf("，用户 %s 清空成功", strings.Join(normalList, "、"))
	}
	if len(errList) != 0 {
		intro += fmt.Sprintf("，用户 %s 清空异常", goutil.JoinInt64Array(errList, "、"))
	}
	logbox.Add(userapi.CatalogClearBackpackItem, intro)
	err := logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return intro, nil
}

func (param *setGiftParam) typeName() string {
	switch param.Type {
	case useritems.BpItemTypeGift:
		return "礼物"
	case useritems.BpItemTypeItem:
		return "道具"
	default:
		panic("背包道具类型错误")
	}
}

func (param *setGiftParam) backpackItemName() string {
	switch param.Type {
	case useritems.BpItemTypeGift:
		return param.gift.Name
	case useritems.BpItemTypeItem:
		return param.item.Name
	default:
		panic("背包道具类型错误")
	}
}
