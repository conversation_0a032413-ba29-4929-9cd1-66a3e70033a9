package user

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

// 补发礼物飘屏状态
const (
	reissueGiftBroadcastNormal = iota // 正常发送飘屏和特效
	reissueGiftNotifyDisabled         // 不发送飘屏，可以只发特效
	reissueGiftBroadcastRoom          // 指定房间发飘屏
)

type reissueGiftParam struct {
	UserID        int64 `form:"user_id" json:"user_id"`
	RoomID        int64 `form:"room_id" json:"room_id"`
	GiftID        int64 `form:"gift_id" json:"gift_id"`
	GiftNum       int   `form:"gift_num" json:"gift_num"`
	LuckyGiftID   int64 `form:"lucky_gift_id" json:"lucky_gift_id"`
	LuckyGiftNum  int   `form:"lucky_gift_num" json:"lucky_gift_num"`
	BroadcastType int   `form:"broadcast_type" json:"broadcast_type"`
	Confirm       int   `form:"confirm" json:"confirm"`

	gift      *gift.Gift
	luckyGift *gift.Gift
	user      *liveuser.Simple
	room      *room.Room
	bubble    *bubble.Simple
}

// ActionReissueGift 补发礼物特效和飘屏
/**
 * @api {post} /api/v2/admin/user/reissuegift 补发礼物特效和飘屏
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 补发礼物数量
 * @apiParam {Number} [lucky_gift_id] 随机礼物 ID
 * @apiParam {Number} [lucky_gift_num] 随机礼物数量
 * @apiParam {number=0,1,2} [broadcast_type=0] 飘屏类型 0 按照送礼金额正常发送飘屏，1 不发飘屏，2 发给指定直播间
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "确定要补发礼物特效吗？飘屏类型：按照送礼金额正常发送飘屏"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
/**
 * @api {post} /rpc/live/reissuegift 补发礼物特效和飘屏
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID（一起送礼物该字段为助攻王 ID）
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 补发礼物数量
 * @apiParam {Number} [lucky_gift_id] 随机礼物 ID
 * @apiParam {Number} [lucky_gift_num] 随机礼物数量
 * @apiParam {number=0,1,2} [broadcast_type=0] 飘屏类型 0 按照送礼金额正常发送飘屏，1 不发飘屏，2 发给指定直播间
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionReissueGift(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReissueGiftParam(c)
	if err != nil {
		return nil, err
	}
	switch param.gift.Comboable {
	case gift.ComboableTypeMulti:
		param.reissueMultiGift() // 一起送礼物的特效和飘屏不需要送礼人信息，所以单独处理
	default:
		param.sendEffect()
		param.notifyBroadcastAll()
	}

	param.addReissueGiftLog(c)
	return "success", nil
}

func (param *reissueGiftParam) reissueGiftNotifyToMessage() string {
	switch param.BroadcastType {
	case reissueGiftBroadcastNormal:
		return "按照送礼金额正常发送飘屏"
	case reissueGiftNotifyDisabled:
		return "不发飘屏"
	case reissueGiftBroadcastRoom:
		return "发给指定直播间"
	default:
		panic("不支持的飘屏类型")
	}
}

func newReissueGiftParam(c *handler.Context) (*reissueGiftParam, error) {
	var param reissueGiftParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.RoomID <= 0 || param.GiftID <= 0 || param.GiftNum <= 0 || param.LuckyGiftID < 0 {
		return nil, actionerrors.ErrParams
	}
	if param.LuckyGiftID > 0 && param.LuckyGiftNum == 0 {
		return nil, actionerrors.ErrParams
	}
	param.gift, err = gift.FindByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.gift == nil {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("礼物 %d 不存在", param.GiftID))
	}
	if err = param.checkLuckyGift(); err != nil {
		return nil, err
	}
	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.room.CreatorID == param.UserID {
		return nil, actionerrors.ErrParamsMsg("送礼人不能是房主")
	}
	param.bubble, err = userappearance.FindMessageBubble(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if param.Confirm == 0 {
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确定要补发礼物特效吗？飘屏类型：%s", param.reissueGiftNotifyToMessage()), 1)
	}

	return &param, nil
}

func (param *reissueGiftParam) checkLuckyGift() error {
	if param.LuckyGiftID <= 0 {
		return nil
	}
	var err error
	param.luckyGift, err = gift.FindByGiftID(param.LuckyGiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.luckyGift == nil || param.luckyGift.Type != gift.TypeDrawSend {
		return actionerrors.ErrParamsMsg("随机礼物不正确")
	}

	drawPool, err := gift.FindPoolGift(param.LuckyGiftID, param.LuckyGiftNum)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if drawPool == nil {
		return actionerrors.NewErrForbidden("奖池不存在")
	}
	if _, ok := drawPool.Rates[param.GiftID]; !ok {
		return actionerrors.ErrParamsMsg("抽出的礼物不在奖池中")
	}
	return nil
}

// sendEffect 补发礼物特效
func (param reissueGiftParam) sendEffect() {
	lg := livegifts.NewLiveGifts(param.room.OID, param.RoomID, param.user, param.bubble).
		SetGift(param.gift, int64(param.GiftNum)).
		SetRoomOpenStatus(param.room.IsOpen())
	if param.luckyGift != nil {
		lg.SetLuckyGift(param.luckyGift, param.LuckyGiftNum, false)
	}
	notify := lg.RoomMessage()
	// 补发特效价格设置为 0，避免客户端实时计算主播的榜单上多了一笔的收入
	notify.Gift.Price = 0
	if param.gift.Comboable == gift.ComboableTypeSingle {
		// 补发的特效不需要传递 combo_id
		combo := &livegifts.Combo{
			Num:        param.GiftNum,
			RemainTime: 0, // 连击剩余时间
		}
		combo.BuildEffect(param.gift, 0)
		if combo.EffectURL != "" {
			notify.Combo = combo
		}
	}
	err := userapi.Broadcast(param.RoomID, notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// notifyBroadcastAll 补发飘屏
func (param reissueGiftParam) notifyBroadcastAll() {
	if param.BroadcastType == reissueGiftNotifyDisabled {
		return
	}

	// TODO: 随机礼物飘屏逻辑后续和其他礼物统一（通过 attr 控制）
	if param.luckyGift == nil {
		// 普通礼物根据价格判断
		price := param.gift.Price * int64(param.GiftNum)
		if price < gift.ComboNotifyMinPrice {
			return
		}
	} else if param.gift.NotifyMessageTemplate() == "" {
		// 随机礼物根据是否有飘屏判断
		return
	}

	nb := gift.NotifyBuilder{
		RoomID:          param.RoomID,
		CreatorUsername: param.room.CreatorUsername,
		User:            param.user,
		Gift:            param.gift,
		GiftNum:         param.GiftNum,
		LuckyGift:       param.luckyGift,
		LuckyGiftNum:    param.LuckyGiftNum,
	}
	np := nb.Build()
	// 礼物价格设置为 0，避免客户端实时累加计算这次补发礼物的价值
	np.Gift.Price = 0

	switch param.BroadcastType {
	case reissueGiftBroadcastRoom:
		err := userapi.Broadcast(param.RoomID, np)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	default:
		err := userapi.BroadcastAll(np)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

func (param reissueGiftParam) reissueMultiGift() {
	if len(param.gift.Combos) == 0 {
		return
	}

	combo := &livegifts.Combo{
		TotalNum:   param.GiftNum,
		Top1UserID: param.UserID,
		RemainTime: 0,
	}
	combo.BuildEffect(param.gift, 0)
	elems := make([]*userapi.BroadcastElem, 0, 2)
	if combo.EffectURL != "" {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: param.RoomID,
			// 一起送补发特效不显示用户赠送数量及连击进度，所以只单独下发特效，不再下发完整的送礼消息
			Payload: liveim.NewRoomEffect(param.RoomID, liveim.Effect{
				EffectURL:    combo.EffectURL,
				WebEffectURL: combo.WebEffectURL,
			}),
		})
	}

	if combo.Notify && param.BroadcastType != reissueGiftNotifyDisabled {
		nb := gift.NotifyBuilder{
			RoomID:                 param.RoomID,
			CreatorUsername:        param.room.CreatorUsername,
			User:                   param.user,
			Bubble:                 param.bubble,
			Gift:                   param.gift,
			GiftNum:                param.GiftNum,
			ComboNum:               param.GiftNum,
			MultiComboTop1Username: param.user.Username,
		}
		notifyGiftPayload := nb.Build()
		// 一起送礼物无法确定具体送礼用户信息，需要避免使用礼物信息飘屏格式下发
		payload := notifymessages.NewGeneral(param.RoomID, notifyGiftPayload.Message, notifyGiftPayload.NotifyBubble)
		payload.Effect = &liveim.Effect{
			EffectURL:    combo.EffectURL,
			WebEffectURL: combo.WebEffectURL,
		}
		payload.EffectShow = notifymessages.EffectShowRoomJump
		switch param.BroadcastType {
		case reissueGiftBroadcastRoom:
			elems = append(elems, &userapi.BroadcastElem{
				Type:    liveim.IMMessageTypeNormal,
				RoomID:  param.RoomID,
				Payload: payload,
			})
		default:
			elems = append(elems, &userapi.BroadcastElem{
				Type:    liveim.IMMessageTypeAll,
				RoomID:  param.RoomID,
				Payload: payload,
			})
		}
	}

	if len(elems) == 0 {
		return
	}

	err := userapi.BroadcastMany(elems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param reissueGiftParam) addReissueGiftLog(c *handler.Context) {
	luckyGiftName := ""
	if param.luckyGift != nil {
		luckyGiftName = fmt.Sprintf("奖池礼物名称: %s, ", param.luckyGift.Name)
	}
	msg := fmt.Sprintf("%s (%d) × %d, %s补发通知类型: %d, 房间 ID: %d, 用户 ID: %d",
		param.gift.Name, param.gift.GiftID, param.GiftNum, luckyGiftName, param.BroadcastType,
		param.RoomID, param.UserID)
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogReissueGift, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}
