package user

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testUserIDs      = []int64{101, 102}
	testCSVUserIDs   = []int64{103, 104}
	testAppearanceID = int64(1001)
	testDuration     = int64(4 * 60 * 60)
)

func cleanTestFile(path string) {
	_, err := os.Stat(path)
	if err == nil {
		err2 := os.Remove(path)
		if err2 != nil {
			logger.Error(err2)
		}
	} else {
		logger.Error(err)
	}
}

func removeAndCreate(content []byte, path string) (*os.File, error) {
	_, err := os.Stat(path)
	if err == nil {
		err := os.Remove(path)
		if err != nil {
			return nil, err
		}
	}

	err = os.WriteFile(path, content, 0644)
	if err != nil {
		return nil, err
	}

	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}

	return file, nil
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(manageParam{}, "operation", "user_ids", "csv_url", "appearance_id", "appearance_type", "duration", "expire_time", "confirm")
	kc.Check(syncParam{}, "appearance_id")
}

func TestActionManageUserAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试使用 UserIDs 字符串下发
	param := manageParam{
		Operation:      appearanceOperationAdd,
		UserIDs:        goutil.JoinInt64Array(testUserIDs, ","),
		AppearanceID:   testAppearanceID,
		AppearanceType: appearance.TypeCardFrame,
		Duration:       testDuration,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err := ActionManageUserAppearance(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	require.NoError(err)

	// 检查是否下发
	now := goutil.TimeNow()
	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": testAppearanceID,
		"type":          appearance.TypeCardFrame,
		"user_id":       bson.M{"$in": testUserIDs},
		"status":        userappearance.StatusOwned,
	}, now.Unix())
	uaItems, err := userappearance.Find(filter, nil)
	require.NoError(err)
	require.Len(uaItems, len(testUserIDs))
	mUserAppearance := goutil.ToMap(uaItems, "UserID").(map[int64]*userappearance.UserAppearance)
	expectedTime := now.Unix() + testDuration
	for i := range testUserIDs {
		uaItem, ok := mUserAppearance[testUserIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.GreaterOrEqual(*uaItem.ExpireTime, expectedTime)
	}

	// 测试移除
	param.Confirm = 0
	param.Duration = 0
	param.Operation = appearanceOperationRemove
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	require.NoError(err)

	//检查是否移除
	uaItems, err = userappearance.Find(filter, nil)
	require.NoError(err)
	require.Empty(uaItems)

	// 测试通过 CSV 文件管理
	fileName := "user"
	path, err := filepath.Abs(fmt.Sprintf("../../../testdata/%s.csv", fileName))
	require.NoError(err)
	require.Contains(path, fmt.Sprintf("live-service/testdata/%s.csv", fileName))
	userCSV := []byte("user_id\n103\n104")
	defer cleanTestFile(path)
	file, err := removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	// 测试添加
	param.Confirm = 0
	param.UserIDs = ""
	param.CSVURL = upload.SourceURL(fmt.Sprintf("https://fm.example.com/testdata/%s.csv", fileName))
	param.Operation = appearanceOperationAdd
	param.ExpireTime = goutil.TimeNow().Add(4 * time.Hour).Unix()
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	require.NoError(err)

	now = goutil.TimeNow()
	filter["user_id"] = bson.M{"$in": testCSVUserIDs}
	filter = appearance.SetValidTimeFilter(filter, now.Unix())
	uaItems, err = userappearance.Find(filter, nil)
	require.NoError(err)
	require.Len(uaItems, len(testCSVUserIDs))
	mUserAppearance = goutil.ToMap(uaItems, "UserID").(map[int64]*userappearance.UserAppearance)
	expectedTime = now.Unix() + testDuration
	for i := range testCSVUserIDs {
		uaItem, ok := mUserAppearance[testCSVUserIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.GreaterOrEqual(*uaItem.ExpireTime, expectedTime)
	}

	// 测试移除
	param.Confirm = 0
	param.Duration = 0
	param.Operation = appearanceOperationRemove
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	require.NoError(err)

	// 检查是否移除
	uaItems, err = userappearance.Find(filter, nil)
	require.NoError(err)
	require.Empty(uaItems)

	param.Confirm = 1
	param.AppearanceID = testAppearanceID
	param.Operation = appearanceOperationRemove
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	_, err = ActionManageUserAppearance(c)
	assert.EqualError(err, "有用户没有持有对应的外观，请检查后再试")
}

func TestIsValidTimeInput(t *testing.T) {
	assert := assert.New(t)

	var param manageParam
	assert.False(param.isValidTimeInput())

	param.Duration = 1
	param.ExpireTime = 1
	assert.False(param.isValidTimeInput())

	param.Duration = -1
	param.ExpireTime = -1
	assert.False(param.isValidTimeInput())

	param.Duration = -2
	param.ExpireTime = -2
	assert.False(param.isValidTimeInput())

	param.Duration = 1
	param.ExpireTime = 0
	assert.True(param.isValidTimeInput())

	param.Duration = 0
	param.ExpireTime = 1
	assert.True(param.isValidTimeInput())

	param.Duration = -1
	param.ExpireTime = 0
	assert.True(param.isValidTimeInput())

	param.Duration = 0
	param.ExpireTime = -1
	assert.True(param.isValidTimeInput())
}

func TestManageParamLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param manageParam
	// 参数无效
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, nil)
	err := param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数为空
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数不合法
	param.Operation = 10
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 外观参数不合法
	param.Operation = appearanceOperationAdd
	param.Duration = -1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// csv 和输入的用户 ID 同时输入
	param = manageParam{
		Operation:      appearanceOperationAdd,
		CSVURL:         "test",
		UserIDs:        "1,2,3",
		Duration:       -1,
		AppearanceID:   appearance.AppearanceIDReserved + 1,
		AppearanceType: appearance.TypeCardFrame,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "CSV 和输入用户 ID 二选一，请检查后再试")

	// 参数缺失
	param.UserIDs = ""
	param.CSVURL = ""
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "CSV 和输入用户 ID 二选一，请检查后再试")

	// 用户 ID 填写不正确
	param = manageParam{
		Operation:      appearanceOperationAdd,
		UserIDs:        "str,str,str",
		Duration:       -1,
		AppearanceID:   testAppearanceID,
		AppearanceType: appearance.TypeCardFrame,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 持续时间填写不正确
	param.UserIDs = "1,2,3"
	param.Duration = -2
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "持续时间和过期时间二选一，请输入正确的时间并检查后再试")

	// 过期时间填写不正确
	param.ExpireTime = -2
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "持续时间和过期时间二选一，请输入正确的时间并检查后再试")

	// 外观不存在
	sevenDays := goutil.TimeNow().Add(4 * time.Hour).Unix()
	param.UserIDs = goutil.JoinInt64Array(testUserIDs, ",")
	param.AppearanceID = 99999
	param.Duration = 0
	param.ExpireTime = sevenDays
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, fmt.Sprintf("外观 ID：%d 不存在或当前不可用，请检查后再试", param.AppearanceID))

	// 测试使用 CSVURL + Duration 的情况
	// CSV 不可用的情况
	param = manageParam{
		Operation:      appearanceOperationAdd,
		CSVURL:         "https://fm.example.com/testdata/notexist.csv",
		Duration:       testDuration,
		AppearanceID:   testAppearanceID,
		AppearanceType: appearance.TypeCardFrame,
	}
	param.Duration = testDuration
	param.ExpireTime = 0
	param.AppearanceID = testAppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "CSV 不可用，请检查后再试")

	// 列表为空的情况
	fileName := "user"
	path, err := filepath.Abs(fmt.Sprintf("../../../testdata/%s.csv", fileName))
	require.NoError(err)
	require.Contains(path, fmt.Sprintf("live-service/testdata/%s.csv", fileName))
	userCSV := []byte("user_id")
	file, err := removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	param.CSVURL = upload.SourceURL(fmt.Sprintf("https://fm.example.com/testdata/%s.csv", fileName))
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "需要更新的用户数量为 0，请检查后再试")

	// 正常情况
	path, err = filepath.Abs(fmt.Sprintf("../../../testdata/%s.csv", fileName))
	require.NoError(err)
	require.Contains(path, fmt.Sprintf("live-service/testdata/%s.csv", fileName))
	userCSV = []byte("user_id\n103\n104")
	defer cleanTestFile(path)
	file, err = removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	param.CSVURL = upload.SourceURL(fmt.Sprintf("https://fm.example.com/testdata/%s.csv", fileName))
	param.Duration = testDuration
	param.ExpireTime = 0
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	require.NoError(err)

	// 测试使用 CSVURL + ExpireTime 的情况
	param.Confirm = 0
	param.Duration = 0
	param.ExpireTime = sevenDays
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	require.NoError(err)

	// 测试使用 UserID + Duration 的情况
	param.CSVURL = ""
	param.Confirm = 0
	param.UserIDs = goutil.JoinInt64Array(testUserIDs, ",")
	param.AppearanceID = testAppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	require.NoError(err)

	// 测试使用 UserID + ExpireTime 的情况
	param.Confirm = 0
	param.ExpireTime = sevenDays
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	assert.EqualError(err, "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？")

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	err = param.load(c)
	require.NoError(err)
}

func TestOperateWithCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	fileName := "user"
	path, err := filepath.Abs(fmt.Sprintf("../../../testdata/%s.csv", fileName))
	require.NoError(err)
	require.Contains(path, fmt.Sprintf("live-service/testdata/%s.csv", fileName))
	userCSV := []byte("user_id\n103\n104")
	file, err := removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	var param manageParam
	param.CSVURL = "https://testdata/notexist.csv"
	_, err = param.operateWithCSV(true)
	assert.EqualError(err, "CSV 不可用，请检查后再试")

	param.CSVURL = "https://fm.example.com/testdata/notexist.csv"
	_, err = param.operateWithCSV(true)
	assert.EqualError(err, "CSV 不可用，请检查后再试")

	param.CSVURL = "https://fm.example.com/testdata/user.csv"
	_, err = param.operateWithCSV(true)
	require.NoError(err)

	count, err := param.operateWithCSV(true)
	require.NoError(err)
	assert.Equal(int64(2), count)

	userCSV = []byte("user_id,appearance_id\n103,1001\n104,10001")
	file, err = removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	_, err = param.operateWithCSV(true)
	assert.EqualError(err, "CSV 列数不正确，请检查后再试")

	userCSV = []byte("user_id\nstr\nstr")
	file, err = removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	_, err = param.operateWithCSV(true)
	assert.EqualError(err, "CSV 的用户 ID 字段不可用，请检查后再试")

	path, err = filepath.Abs(fmt.Sprintf("../../../testdata/%s.csv", fileName))
	require.NoError(err)
	require.Contains(path, fmt.Sprintf("live-service/testdata/%s.csv", fileName))
	userCSV = []byte("user_id\n103\n104")
	defer cleanTestFile(path)
	file, err = removeAndCreate(userCSV, path)
	require.NoError(err)
	defer file.Close()

	aItem, err := appearance.FindOne(testAppearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)

	now := goutil.TimeNow()
	param = manageParam{
		CSVURL:         "https://fm.example.com/testdata/user.csv",
		Duration:       now.Add(7*24*time.Hour).Unix() - now.Unix(),
		AppearanceID:   testAppearanceID,
		AppearanceType: appearance.TypeCardFrame,
		appearance:     aItem,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	param.adminbox = goclient.NewAdminLogBox(c)
	param.operationFunc = param.addAppearance

	count, err = param.operateWithCSV(false)
	require.NoError(err)
	assert.Equal(int64(2), count)

	_, err = param.operateWithCSV(false)
	require.NoError(err)

	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": testAppearanceID,
		"type":          appearance.TypeCardFrame,
		"user_id":       bson.M{"$in": testCSVUserIDs},
		"status":        userappearance.StatusOwned,
	}, now.Unix())
	uaItems, err := userappearance.Find(filter, nil)
	require.NoError(err)
	require.Len(uaItems, len(testCSVUserIDs))
	mUserAppearance := goutil.ToMap(uaItems, "UserID").(map[int64]*userappearance.UserAppearance)
	expectedTime := now.Unix() + testDuration
	for i := range testCSVUserIDs {
		uaItem, ok := mUserAppearance[testCSVUserIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.GreaterOrEqual(*uaItem.ExpireTime, expectedTime)
	}

	param.operationFunc = param.removeAppearance
	_, err = param.operateWithCSV(false)
	require.NoError(err)

	uaItems, err = userappearance.Find(filter, nil)
	require.NoError(err)
	require.Empty(uaItems)
}

func TestOperateWithUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	aItem, err := appearance.FindOne(testAppearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)

	now := goutil.TimeNow()
	param := manageParam{
		userIDs:        testUserIDs,
		Duration:       now.Add(7*24*time.Hour).Unix() - now.Unix(),
		AppearanceID:   testAppearanceID,
		AppearanceType: appearance.TypeCardFrame,
		appearance:     aItem,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/manage", true, param)
	param.adminbox = goclient.NewAdminLogBox(c)
	param.operationFunc = param.addAppearance

	err = param.checkUserExists()
	require.NoError(err)

	err = param.operateWithUserIDs()
	require.NoError(err)

	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": testAppearanceID,
		"type":          appearance.TypeCardFrame,
		"user_id":       bson.M{"$in": testUserIDs},
		"status":        userappearance.StatusOwned,
	}, now.Unix())
	uaItems, err := userappearance.Find(filter, nil)
	require.NoError(err)
	require.Len(uaItems, len(testUserIDs))
	mUserAppearance := goutil.ToMap(uaItems, "UserID").(map[int64]*userappearance.UserAppearance)
	expectedTime := now.Unix() + testDuration
	for i := range testUserIDs {
		uaItem, ok := mUserAppearance[testUserIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.GreaterOrEqual(*uaItem.ExpireTime, expectedTime)
	}

	param.operationFunc = param.removeAppearance
	err = param.operateWithUserIDs()
	require.NoError(err)

	uaItems, err = userappearance.Find(filter, nil)
	require.NoError(err)
	require.Empty(uaItems)
}

func TestCheckUserExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param manageParam
	param.userIDs = []int64{-1, -2}
	err := param.checkUserExists()
	assert.EqualError(err, "有用户 ID 不存在，请上传正确的用户 ID")

	param.userIDs = testUserIDs
	err = param.checkUserExists()
	require.NoError(err)
}

const (
	testSyncVehicleID       = 3001
	testSyncAvatarFrameID   = 3002
	testSyncCardFrameID     = 3003
	testSyncMessageBubbleID = 3004
	testSyncBadgeID         = 3005
)

func addActionSyncUserAppearanceTestData() error {
	now := goutil.TimeNow()
	vehicle := &appearance.Appearance{
		ID:             testSyncVehicleID,
		Name:           "测试同步座驾",
		Type:           1,
		From:           1,
		Intro:          "测试同步",
		Icon:           "oss://live/vehicles/icons/test.png",
		EffectDuration: 4000,
		Effect:         "oss://live/vehicles/effects/test.mp4;oss://live/vehicles/effects/test.png",
		WebEffect:      "oss://live/vehicles/effects/test-web.mp4;oss://live/vehicles/effects/test-web.webm;oss://live/vehicles/effects/test-web.png",
		MessageBar: &appearance.MessageBar{
			Message: "<font color=\"#FFF297\">${username}</font><font color=\"#C4E8FF\">乘坐</font><font color=\"#FFF297\">${vehicle_name}</font><font color=\"#C4E8FF\">来了</font>",
			Image:   "oss://live/vehicles/messagebars/test_0_260_0_260.png",
		},
		StartTime: now.Unix(),
	}
	userVehicle := userappearance.NewUserAppearance(3001, vehicle)
	userVehicle.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	vehicle.Name = "测试同步座驾更新后"
	vehicle.Icon = "oss://live/vehicles/icons/test-updated.png"
	vehicle.MessageBar.Image = "oss://live/vehicles/messagebars/test_0_260_0_260-updated.png"

	avatarFrame := &appearance.Appearance{
		ID:        testSyncAvatarFrameID,
		Name:      "测试同步头像框",
		Type:      2,
		From:      1,
		Intro:     "测试同步",
		Icon:      "oss://live/avatarframes/icons/test.png",
		Image:     "oss://live/avatarframes/images/test.png",
		StartTime: now.Unix(),
	}
	userAvatarFrame := userappearance.NewUserAppearance(3002, avatarFrame)
	userAvatarFrame.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	avatarFrame.Name = "测试同步头像框更新后"
	avatarFrame.Icon = "oss://live/avatarframes/icons/test-updated.png"
	avatarFrame.Image = "oss://live/avatarframes/images/test-updated.png"

	cardFrame := &appearance.Appearance{
		ID:       testSyncCardFrameID,
		Name:     "测试同步名片框",
		Type:     3,
		From:     1,
		Intro:    "测试同步",
		Icon:     "oss://live/cardframes/icons/test.png",
		Image:    "oss://live/cardframes/images/test.png",
		ImageNew: "oss://live/cardframes/images/test_new.png",
		TextColorItem: &appearance.TextColorItem{
			Username: "#FFFFF0",
		},
		StartTime: now.Unix(),
	}
	userCardFrame := userappearance.NewUserAppearance(3003, cardFrame)
	userCardFrame.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	cardFrame.Name = "测试同步名片框更新后"
	cardFrame.Icon = "oss://live/cardframes/icons/test-updated.png"
	cardFrame.Image = "oss://live/cardframes/images/test-updated.png"
	cardFrame.ImageNew = "oss://live/cardframes/images/test-updated-new.png"

	messageBubble := &appearance.Appearance{
		ID:        testSyncMessageBubbleID,
		Name:      "测试同步消息气泡",
		Type:      4,
		From:      1,
		Intro:     "测试同步",
		Icon:      "oss://live/messagebubbles/icons/test.png",
		Image:     "oss://live/messagebubbles/images/test_36_36_36_36.png",
		Frame:     "oss://live/messagebubbles/frames/test_corner0.png",
		TextColor: "#FFFFFF",
		StartTime: now.Unix(),
	}
	userMessageBubble := userappearance.NewUserAppearance(3003, messageBubble)
	userMessageBubble.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	messageBubble.Name = "测试同步消息气泡更新后"
	messageBubble.Icon = "oss://live/messagebubbles/icons/test-updated.png"
	messageBubble.Image = "oss://live/messagebubbles/images/test_36_36_36_36-updated.png"
	messageBubble.Frame = "oss://live/messagebubbles/frames/test_corner0-updated.png"

	badge := &appearance.Appearance{
		ID:        testSyncBadgeID,
		Name:      "测试同步称号",
		Type:      5,
		From:      1,
		Intro:     "测试同步",
		Icon:      "oss://live/badges/icons/test.png",
		Image:     "oss://live/badges/images/test.png",
		StartTime: now.Unix(),
	}
	userBadge := userappearance.NewUserAppearance(3005, badge)
	userBadge.SetStatus(userappearance.StatusWorn, now.Add(time.Hour).Unix(), now.Unix())
	badge.Name = "测试同步称号更新后"
	badge.Icon = "oss://live/badges/icons/test-updated.png"
	badge.Image = "oss://live/badges/images/test-updated.png"

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().InsertMany(ctx, []interface{}{vehicle, avatarFrame, cardFrame, messageBubble, badge})
	if err != nil {
		return err
	}
	_, err = userappearance.Collection().InsertMany(ctx,
		[]interface{}{userVehicle, userAvatarFrame, userCardFrame, userMessageBubble, userBadge})
	if err != nil {
		return err
	}

	return nil
}

func deleteActionSyncUserAppearanceTestData() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().DeleteMany(ctx,
		bson.M{"id": bson.M{"$in": bson.A{testSyncVehicleID, testSyncAvatarFrameID, testSyncCardFrameID,
			testSyncMessageBubbleID, testSyncBadgeID}}})
	if err != nil {
		return err
	}

	_, err = userappearance.Collection().DeleteMany(ctx,
		bson.M{"appearance_id": bson.M{"$in": bson.A{testSyncVehicleID, testSyncAvatarFrameID, testSyncCardFrameID,
			testSyncMessageBubbleID, testSyncBadgeID}}})
	if err != nil {
		return err
	}

	return nil
}

func TestActionSyncUserAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := addActionSyncUserAppearanceTestData()
	require.NoError(err)
	defer func() {
		err := deleteActionSyncUserAppearanceTestData()
		assert.NoError(err)
	}()

	var param syncParam
	param.AppearanceID = testSyncVehicleID
	c := handler.NewTestContext(http.MethodPost, "/api/v2/dev/appearance/sync", true, param)
	_, err = ActionSyncUserAppearance(c)
	require.NoError(err)
}

func TestActionSyncUserAppearanceCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := addActionSyncUserAppearanceTestData()
	require.NoError(err)
	defer func() {
		err := deleteActionSyncUserAppearanceTestData()
		assert.NoError(err)
	}()

	var param syncParam
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/sync", true, nil)
	err = param.check(c)
	assert.Equal(actionerrors.ErrParams, err)

	param.AppearanceID = 0
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/sync", true, param)
	err = param.check(c)
	assert.Equal(actionerrors.ErrParams, err)

	param.AppearanceID = 9999999
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/sync", true, param)
	err = param.check(c)
	assert.EqualError(err, "外观不存在")

	param.AppearanceID = testSyncVehicleID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/user/appearance/sync", true, param)
	err = param.check(c)
	assert.NoError(err)
}

func TestActionSyncUserAppearanceSyncUserAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := addActionSyncUserAppearanceTestData()
	require.NoError(err)
	defer func() {
		err := deleteActionSyncUserAppearanceTestData()
		assert.NoError(err)
	}()

	var param syncParam
	param.AppearanceID = testSyncVehicleID
	beforeUpdateuaItem, err := userappearance.FindOne(bson.M{
		"appearance_id": param.AppearanceID,
		"user_id":       param.AppearanceID}, nil) // 此处的测试数据用户 ID 和外观 ID 是一致的
	require.NoError(err)
	require.NotNil(beforeUpdateuaItem)

	param.appearance, err = appearance.FindOne(param.AppearanceID, appearance.TypeVehicle)
	require.NoError(err)
	require.NotNil(param.appearance)
	err = param.syncUserAppearance()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := userappearance.Collection().CountDocuments(ctx,
		appearance.SetValidTimeFilter(bson.M{"appearance_id": param.AppearanceID}, goutil.TimeNow().Unix()))
	require.NoError(err)
	require.NotZero(count)
	assert.Equal(count, param.modifiedCount)

	uaItem, err := userappearance.FindOne(bson.M{
		"appearance_id": param.AppearanceID,
		"user_id":       param.AppearanceID}, nil) // 此处的测试数据用户 ID 和外观 ID 是一致的
	require.NoError(err)
	require.NotNil(uaItem)
	assert.Contains(uaItem.Name, "更新后")
	assert.Equal(param.appearance.Name, uaItem.Name)
	assert.NotEqual(beforeUpdateuaItem.Name, uaItem.Name)
	assert.Contains(uaItem.Icon, "updated")
	assert.Equal(param.appearance.Icon, uaItem.Icon)
	assert.NotEqual(beforeUpdateuaItem.Icon, uaItem.Icon)
	assert.Contains(uaItem.MessageBar.Image, "updated")
	assert.Equal(param.appearance.MessageBar.Image, uaItem.MessageBar.Image)
	assert.NotEqual(beforeUpdateuaItem.MessageBar.Image, uaItem.MessageBar.Image)
}
