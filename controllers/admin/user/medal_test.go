package user

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionMedalPointAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()

	var testMedalRoomID int64 = 347142108
	c := handler.NewTestContext(http.MethodPost, "/medal/point/add", true, handler.M{
		"user_id": 12,
		"room_id": testMedalRoomID,
		"point":   1,
	})
	r, err := ActionMedalPointAdd(c)
	require.NoError(err)
	assert.Equal("亲密度增加成功", r)
}
