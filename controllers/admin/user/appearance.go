package user

import (
	"fmt"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
)

// 操作类型
const (
	appearanceOperationAdd    = iota + 1 // 添加/叠加外观
	appearanceOperationRemove            // 移除外观
	appearanceOperationLimit             // 最大值
)

type manageParam struct {
	Operation      int              `form:"operation" json:"operation"`
	UserIDs        string           `form:"user_ids" json:"user_ids"`
	CSVURL         upload.SourceURL `form:"csv_url" json:"csv_url"`
	AppearanceID   int64            `form:"appearance_id" json:"appearance_id"`
	AppearanceType int              `form:"appearance_type" json:"appearance_type"`
	Duration       int64            `form:"duration" json:"duration"`
	ExpireTime     int64            `form:"expire_time" json:"expire_time"`
	Confirm        int              `form:"confirm" json:"confirm"`

	userIDs       []int64
	appearance    *appearance.Appearance
	adminbox      *goclient.AdminLogBox
	operationFunc func() error
}

// ActionManageUserAppearance 管理用户外观
/**
 * @api {post} /api/v2/admin/user/appearance/manage 管理用户外观
 * @apiDescription 管理用户外观
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} operation 操作类型，1 为添加/叠加外观，2 为移除外观
 * @apiParam {String} [user_ids] 要操作的用户 IDs，半角逗号分割
 * @apiParam {String} [csv_url] 用户列表，格式：CSV 文件
 * @apiParam {Number} appearance_id 要操作的对应外观 ID
 * @apiParam {Number} appearance_type 要操作的对应外观类型
 * @apiParam {Number} [duration] 外观的持续时间，单位：秒，传入 -1 表示不指定时长，如果模版为有限时间则取最大时间，如果模版为无限时间则为永久。如：生效 1 小时则为 3600，采用最大值则为 -1
 * @apiParam {Number} [expire_time] 外观的过期时间，单位：秒，传入 -1 表示不指定时长，如果模版为有限时间则取最大时间，如果模版为无限时间则为永久。如：1 小时后过期则为 当前时间戳 + 3600，采用最大值则为 -1
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "操作成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       "msg": "确定要对所上传的 2 个用户进行添加（移除）外观的操作吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部
 *
 */
func ActionManageUserAppearance(c *handler.Context) (handler.ActionResponse, error) {
	var param manageParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	if param.UserIDs != "" {
		err = param.operateWithUserIDs()
		if err != nil {
			return nil, err
		}
	} else if param.CSVURL != "" {
		_, err = param.operateWithCSV(false)
		if err != nil {
			return nil, err
		}
	}

	param.sendAdminBox()
	return "操作成功", nil
}

func (param *manageParam) checkUserExists() error {
	// 去重
	param.userIDs = util.Uniq(param.userIDs)
	// 检查是否有用户不存在
	mUser, err := mowangskuser.FindSimpleMap(param.userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.userIDs) != len(mUser) {
		return actionerrors.ErrParamsMsg("有用户 ID 不存在，请上传正确的用户 ID")
	}
	return nil
}

func (param *manageParam) operateWithCSV(check bool) (int64, error) {
	res, err := service.Upload.ToResource(param.CSVURL)
	if err != nil {
		return 0, actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}

	file, err := res.Open()
	if err != nil {
		return 0, actionerrors.ErrParamsMsg("CSV 不可用，请检查后再试")
	}
	defer file.Close()

	userIDsCount := int64(0)
	err = csv.Read(file, func(rows [][]string, _ int64) error {
		// NOTICE: 下方的 operationFunc 会消费 param.userIDs，因此此处需要重新初始化避免重复处理上个批次的内容
		param.userIDs = make([]int64, 0, len(rows))
		for i := range rows {
			if len(rows[i]) != 1 {
				return actionerrors.ErrParamsMsg("CSV 列数不正确，请检查后再试")
			}

			userID, err := strconv.ParseInt(rows[i][0], 10, 64)
			if err != nil {
				return actionerrors.ErrParamsMsg("CSV 的用户 ID 字段不可用，请检查后再试")
			}

			param.userIDs = append(param.userIDs, userID)
		}
		// 同一次请求中的第一次调用需要检查用户是否存在，第二次则可以直接操作
		if check {
			err = param.checkUserExists()
			if err != nil {
				return err
			}
		} else {
			// 去重
			// NOTICE: 回调这个地方无法判断整个 csv 表的重叠情况，同时多批次更新之后，
			// 在后续进行添加判断时会判断是否持有老外观，因此只在此处去重，如果 csv 中
			// 重复出现，则重叠添加
			param.userIDs = util.Uniq(param.userIDs)
			err := param.operationFunc()
			if err != nil {
				return err
			}
		}

		userIDsCount += int64(len(param.userIDs))
		return nil
	})
	if err != nil {
		return 0, err
	}

	return userIDsCount, nil
}

func (param *manageParam) operateWithUserIDs() error {
	return param.operationFunc()
}

func (param *manageParam) isValidTimeInput() bool {
	// 持续时间/过期时间二选一，并且不为 0 和除 -1 以外的负数值
	expireTimeValid := param.ExpireTime > 0 || param.ExpireTime == -1
	durationValid := param.Duration > 0 || param.Duration == -1
	return expireTimeValid != durationValid
}

func (param *manageParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}

	switch param.Operation {
	case appearanceOperationAdd:
		param.operationFunc = param.addAppearance
		if !param.isValidTimeInput() {
			return actionerrors.ErrParamsMsg("持续时间和过期时间二选一，请输入正确的时间并检查后再试")
		}
	case appearanceOperationRemove:
		param.operationFunc = param.removeAppearance
	default:
		return actionerrors.ErrParams
	}

	// 检查参数
	if param.AppearanceID <= appearance.AppearanceIDReserved ||
		param.AppearanceType == 0 {
		return actionerrors.ErrParams
	}
	// 用户 ID 的参数二选一
	if (param.UserIDs != "" && param.CSVURL != "") ||
		(param.UserIDs == "" && param.CSVURL == "") {
		return actionerrors.ErrParamsMsg("CSV 和输入用户 ID 二选一，请检查后再试")
	}

	userIDsCount := int64(0)
	if param.UserIDs != "" {
		var err error
		param.userIDs, err = goutil.SplitToInt64Array(param.UserIDs, ",")
		if err != nil {
			return actionerrors.ErrParams
		}
		// 检查 UserIDs 是否正确
		err = param.checkUserExists()
		if err != nil {
			return err
		}

		userIDsCount = int64(len(param.userIDs))
	}
	if param.CSVURL != "" {
		// 检查 CSV 是否完整和正确
		userIDsCount, err = param.operateWithCSV(true)
		if err != nil {
			return err
		}
	}
	if userIDsCount == 0 {
		return actionerrors.ErrParamsMsg("需要更新的用户数量为 0，请检查后再试")
	}

	// 查询对应外观，此处查询不做时间限制，如果是添加操作，则在下方进行额外判断，如果是移除操作则忽略
	opts := options.Find().SetLimit(1)
	appearances, err := appearance.Find(bson.M{
		"id":   param.AppearanceID,
		"type": param.AppearanceType,
	}, opts)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(appearances) == 0 || (param.Operation == appearanceOperationAdd &&
		appearances[0].ExpireTime != nil && goutil.TimeNow().Unix() >= *appearances[0].ExpireTime) {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("外观 ID：%d 不存在或当前不可用，请检查后再试", param.AppearanceID))
	}
	param.appearance = appearances[0]
	if param.Confirm == 0 {
		message := fmt.Sprintf("确定要对所上传的 %d 个用户进行添加（移除）外观的操作吗？", userIDsCount)
		return actionerrors.ErrConfirmRequired(message, 1)
	}

	param.adminbox = goclient.NewAdminLogBox(c)
	return nil
}

func (param *manageParam) addAppearance() error {
	// 批量下发
	err := userappearance.BatchAddAppearance(param.userIDs, param.Duration, param.ExpireTime, param.appearance, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	userappearance.ClearCache(param.userIDs...)

	intro := fmt.Sprintf("为用户 ID：%s 添加了类型为 %d 的外观 ID：%d，", goutil.JoinInt64Array(param.userIDs, ","),
		param.AppearanceType, param.AppearanceID)
	if param.Duration > 0 {
		intro += fmt.Sprintf("持续时长：%s", (time.Duration(param.Duration) * time.Second).String())
	} else if param.Duration == -1 || param.ExpireTime == -1 {
		intro += "持续时长：永久"
	} else {
		intro += fmt.Sprintf("过期时间：%s", time.Unix(param.ExpireTime, 0).Format(util.TimeFormatYMDHMS))
	}

	param.adminbox.Add(userapi.CatalogManageAppearance, intro)
	return nil
}

func (param *manageParam) removeAppearance() error {
	uaItems, err := userappearance.Find(bson.M{
		"user_id":       bson.M{"$in": param.userIDs},
		"appearance_id": param.AppearanceID,
		"type":          param.AppearanceType,
		"status":        bson.M{"$gt": userappearance.StatusPending},
	}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	foundUserIDs := make([]int64, 0, len(uaItems))
	for _, v := range uaItems {
		foundUserIDs = append(foundUserIDs, v.UserID)
	}
	if len(util.Uniq(foundUserIDs)) < len(param.userIDs) {
		return actionerrors.ErrParamsMsg("有用户没有持有对应的外观，请检查后再试")
	}

	updates := make([]mongo.WriteModel, len(param.userIDs))
	for i := range param.userIDs {
		updates[i] = mongo.NewDeleteOneModel().SetFilter(bson.M{
			"user_id":       param.userIDs[i],
			"appearance_id": param.AppearanceID,
			"type":          param.AppearanceType,
		})
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().BulkWrite(ctx, updates)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	userappearance.ClearCache(param.userIDs...)

	intro := fmt.Sprintf("为用户 ID：%s 移除了类型为 %d 的外观 ID：%d", goutil.JoinInt64Array(param.userIDs, ","),
		param.AppearanceType, param.AppearanceID)
	param.adminbox.Add(userapi.CatalogManageAppearance, intro)
	return nil
}

func (param *manageParam) sendAdminBox() {
	err := param.adminbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type syncParam struct {
	AppearanceID int64 `form:"appearance_id" json:"appearance_id"`

	appearance    *appearance.Appearance
	modifiedCount int64
}

// ActionSyncUserAppearance 从外观模板同步用户外观
/**
 * @api {post} /api/v2/admin/user/appearance/sync 同步用户外观
 * @apiDescription 从外观模板同步用户外观
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} appearance_id 需要同步的外观 ID，支持同步贵族外观
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "同步成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部
 *
 */
func ActionSyncUserAppearance(c *handler.Context) (handler.ActionResponse, error) {
	var param syncParam
	err := param.check(c)
	if err != nil {
		return nil, err
	}

	err = param.syncUserAppearance()
	if err != nil {
		return nil, err
	}

	param.sendAdminLogBox(c)
	return "同步成功", nil
}

func (param *syncParam) check(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	if param.AppearanceID <= 0 {
		return actionerrors.ErrParams
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	param.appearance = new(appearance.Appearance)
	err = appearance.Collection().
		FindOne(ctx, bson.M{"id": param.AppearanceID}).
		Decode(param.appearance)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return actionerrors.ErrNotFound("外观不存在")
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *syncParam) clearCache() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := userappearance.Collection().Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{"appearance_id": param.AppearanceID, "status": userappearance.StatusWorn}},
		bson.M{"$group": bson.M{"_id": nil, "user_ids": bson.M{"$addToSet": "$user_id"}}},
	})
	if err != nil {
		logger.WithField("appearance_id", param.AppearanceID).Error(err)
		return
	}

	type wornUserIDs struct {
		UserIDs []int64 `bson:"user_ids"`
	}
	defer cur.Close(ctx)
	var res []wornUserIDs
	err = cur.All(ctx, &res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(res) > 0 && len(res[0].UserIDs) > 0 {
		userappearance.ClearCache(res[0].UserIDs...)
	}
}

func (param *syncParam) syncUserAppearance() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := userappearance.Collection().UpdateMany(ctx,
		bson.M{"appearance_id": param.AppearanceID},
		param.appearance.NewSyncUpdate(),
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	param.modifiedCount = res.ModifiedCount
	if param.modifiedCount > 0 {
		// 刷新缓存
		param.clearCache()
	}
	return nil
}

func (param *syncParam) sendAdminLogBox(c *handler.Context) {
	adminLogBox := goclient.NewAdminLogBox(c)
	intro := fmt.Sprintf("为 %d 个用户同步了类型为 %d 的外观 ID：%d",
		param.modifiedCount, param.appearance.Type, param.AppearanceID)
	adminLogBox.Add(userapi.CatalogManageAppearance, intro)
	err := adminLogBox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
