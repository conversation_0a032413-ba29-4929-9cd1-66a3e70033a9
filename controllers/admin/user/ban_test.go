package user

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestIsRichUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 20220526
	param := banUserParams{
		UserID: testUserID,
		user: &liveuser.User{
			Contribution: usercommon.LevelStart[49],
		},
	}
	isRich, err := param.isRichUser()
	require.NoError(err)
	assert.True(isRich, "用户等级大于 50 级")
	key := keys.KeyNobleUserVips1.Format(testUserID)
	require.NoError(service.Redis.Set(key, `{"1":{"type":1,"level":5,"user_id":20220526,"expire_time":9999999999}}`, 5*time.Second).Err())
	param.user.Contribution = 0
	isRich, err = param.isRichUser()
	require.NoError(err)
	assert.True(isRich, "用户为巨星贵族")

	require.NoError(service.Redis.Set(key, `{"1":{"type":1,"level":5,"user_id":20220526,"expire_time":1}}`, 5*time.Second).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userconsumption.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	isRich, err = param.isRichUser()
	require.NoError(err)
	assert.False(isRich, "普通用户")

	_, err = userconsumption.Collection().InsertOne(ctx,
		userconsumption.UserConsumption{
			UserID:      testUserID,
			Consumption: 100000,
			Date:        goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		},
	)
	require.NoError(err)
	isRich, err = param.isRichUser()
	require.NoError(err)
	assert.True(isRich, "直播消费不低于 100000 钻石")
}

func TestBanUserAndUnban(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := []banUserParams{
		{
			UserID: -1,
			Type:   0,
			Reason: "test",
		},
		{
			UserID:   12,
			Type:     1,
			Duration: -10,
			Reason:   "test",
		},
		{
			UserID:   12,
			Type:     -1,
			Duration: 60,
			Reason:   "test",
		},
	}
	for i := range params {
		c := handler.NewTestContext(http.MethodPost, "/", true, params[i])
		_, err := ActionBanAdd(c)
		assert.Equal(actionerrors.ErrParams, err)
	}

	testBanUserID := int64(2020110401)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := liveuser.Collection()
	_, err := col.UpdateOne(ctx, bson.M{"user_id": testBanUserID},
		bson.M{"$set": bson.M{"user_id": testBanUserID, "username": "测试封禁"}},
		options.Update().SetUpsert(true))
	require.NoError(err)
	key := keys.KeyNobleUserVips1.Format(testBanUserID)
	require.NoError(service.Redis.Set(key, `{"1":{"type":1,"level":7,"user_id":2020110401,"expire_time":9999999999}}`, 5*time.Second).Err())

	param := banUserParams{
		UserID: testBanUserID,
		Type:   userstatus.TypeBanForever,
		Reason: "test",
	}
	c := handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBanAdd(c)
	assert.EqualError(err, "此用户为金主用户，是否继续操作？<br>（金主用户：巨星及以上贵族、40 天内直播消费 1 万元以上或直播等级 50 级及以上）")

	require.NoError(service.Redis.Set(key, `{"1":{"type":1,"level":7,"user_id":2020110401,"expire_time":1}}`, 5*time.Second).Err())
	c = handler.NewTestContext(http.MethodPost, "/", true, param)
	_, err = ActionBanAdd(c)
	require.NoError(err)
	ban, err := userstatus.FindBanned(testBanUserID)
	require.NoError(err)
	assert.Equal(userstatus.TypeBanForever, ban.Type)

	c = handler.NewTestContext(http.MethodPost, "/", true, handler.M{"user_id": testBanUserID})
	_, err = ActionBanRemove(c)
	require.NoError(err)
	ban, err = userstatus.FindBanned(testBanUserID)
	require.NoError(err)
	assert.Nil(ban)
}
