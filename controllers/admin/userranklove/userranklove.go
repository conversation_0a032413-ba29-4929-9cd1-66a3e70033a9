package userranklove

import (
	"fmt"
	"net/http"
	"time"
	"unicode/utf8"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveranklove"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ConfigUpsNum 后台配置心动主播的数量
var ConfigUpsNum = 5

type streamer struct {
	UserID    int64  `json:"user_id"`
	Character string `json:"character"`
	Point     int    `json:"point"`
	SoundID   int64  `json:"sound_id"`

	roomID int64
}

type actionSetReq struct {
	Month string     `json:"month"`
	Ups   []streamer `json:"ups"`

	month     int
	monthTime time.Time
}

func (v *streamer) check() error {
	if v.UserID == 0 || v.Character == "" || v.Point == 0 || v.SoundID == 0 {
		return actionerrors.ErrParamsMsg("请完整填写（有字段为空）")
	}
	user, err := mowangskuser.FindByUserID(v.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return actionerrors.ErrParamsMsg("M号不存在")
	}
	if utf8.RuneCountInString(v.Character) > 5 {
		return actionerrors.ErrParamsMsg("音属填写错误（最大不超过 5 个字符）")
	}
	var soundURL string
	err = service.DB.Table(sound.MSound{}.TableName()).Where("id = ?", v.SoundID).Select("soundurl_64").Row().Scan(&soundURL)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return actionerrors.ErrParamsMsg("M音不存在")
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	r, err := room.FindOne(bson.M{"creator_id": v.UserID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrParamsMsg("主播的房间号未找到")
	}
	if r.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}
	v.roomID = r.RoomID

	return nil
}

func (p *actionSetReq) checkMonth() error {
	tm, month, err := util.ParseYearMonth(p.Month)
	if err != nil {
		return actionerrors.ErrParamsMsg("年月格式错误：应如 2006-01")
	}

	if util.Abs(int64(util.DiffMonth(goutil.TimeNow(), tm))) > 1 {
		return actionerrors.ErrParamsMsg("年月填写错误：不能与当前月份相差超过一个月")
	}

	p.monthTime = tm
	p.month = month
	return nil
}

func (p *actionSetReq) checkUps() error {
	if len(p.Ups) != ConfigUpsNum {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("心动主播数据仅可填写 %d 个", ConfigUpsNum))
	}

	for i := range p.Ups {
		err := p.Ups[i].check()
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *actionSetReq) save(tx *gorm.DB) error {
	var list []liveranklove.Model
	for _, v := range p.Ups {
		m := liveranklove.Model{
			UserID:    v.UserID,
			RoomID:    v.roomID,
			Character: v.Character,
			Point:     v.Point,
			SoundID:   v.SoundID,
			Month:     p.month,
		}
		list = append(list, m)
	}

	err := liveranklove.Replace(tx, p.month, list)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// ActionSet 后台设置心动主播月榜
/**
 * @api {post} /admin/user/rank/love/set 后台设置心动主播月榜
 * @apiVersion 0.1.0
 * @apiGroup /user/rank/love
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "month": "2019-12",
 *       "ups": [
 *         {
 *           "user_id": 123,
 *           "character": "正太音",
 *           "point": 12,
 *           "sound_id": 1
 *         }
 *       ]
 *     }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "保存成功"
 *     }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 本月之前已发布的不可多次保存
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误，用户输入错误
 */
func ActionSet(c *handler.Context) (handler.ActionResponse, error) {
	var param actionSetReq
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	err = param.checkMonth()
	if err != nil {
		return nil, err
	}
	err = param.checkUps()
	if err != nil {
		return nil, err
	}

	if param.monthTime.Before(util.BeginningOfMonth(goutil.TimeNow())) {
		list, err := liveranklove.Get(param.month)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		isChecked := false
		for _, v := range list {
			if v.Checked == 1 {
				isChecked = true
			}
		}
		if isChecked {
			return nil, handler.NewActionError(http.StatusForbidden, handler.CodeUnknownError, "本月之前已发布的不可多次保存")
		}
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		return param.save(tx)
	})
	if err != nil {
		return nil, err
	}
	// 管理员操作日志
	box := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("设置心动主播月榜，设置月份：%s", param.Month)
	box.AddAdminLog(intro, userapi.CatalogSetLoveSet)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return "保存成功", nil
}

type actionGetResp struct {
	Checked int                  `json:"checked"` // 0 未发布 1 已发布
	Month   string               `json:"month"`
	Ups     []liveranklove.Model `json:"ups"`
}

// ActionGet 后台获取某年月的心动主播月榜，包含预览信息
/**
 * @api {get} /admin/user/rank/love/get 后台获取某年月的心动主播月榜，包含预览信息
 * @apiVersion 0.1.0
 * @apiGroup /user/rank/love
 *
 * @apiParam {String} month 年月：格式 "2006-12"
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "checked": 0, // 0 未发布，1 已发布
 *       "month": "2006-12",
 *       "ups": [
 *         {
 *           "user_id": 123,
 *           "name": "name",
 *           "icon_url": "http://static-test.missevan.com/icon.png",
 *           "room_id": 123,
 *           "character": "正太音",
 *           "point": 12,
 *           "sound_id": 1,
 *           "soundurl": "http://static-test.missevan.com/sound.mp3"
 *         }
 *       ],
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionGet(c *handler.Context) (handler.ActionResponse, error) {
	monthStr, ok := c.GetParam("month")
	if !ok {
		return nil, actionerrors.ErrParams
	}

	_, month, err := util.ParseYearMonth(monthStr)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	list, err := liveranklove.Get(month)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	checked := 0
	for i, v := range list {
		if i == 0 {
			checked = v.Checked
		} else {
			if v.Checked != checked {
				return nil, actionerrors.NewUnknownError(http.StatusInternalServerError, "后台数据错误，请联系后端开发")
			}
		}

	}
	return actionGetResp{
		Checked: checked,
		Month:   monthStr,
		Ups:     list,
	}, nil
}

// ActionPublish 发布指定月已保存的结果
/**
 * @api {post} /admin/user/rank/love/publish 发布指定月已保存的结果
 * @apiVersion 0.1.0
 * @apiGroup /user/rank/love
 *
 * @apiParam {String} month 年月：格式 "2006-12"
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "发布成功"
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 操作无效
 */
func ActionPublish(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		Month string `form:"month" json:"month"`
	}
	err := c.Bind(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	_, month, err := util.ParseYearMonth(input.Month)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	ok, err := liveranklove.Publish(month)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if !ok {
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeUnknownError, "操作无效")
	}
	return "发布成功", nil
}
