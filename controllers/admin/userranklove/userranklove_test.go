package userranklove

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testSondID = int64(44809)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)
	m.Run()
}

func TestSetGetPublish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ConfigUpsNum = 1
	testRoomID, err := room.FindRoomID(10)
	require.NoError(err)

	now := goutil.TimeNow()
	param := actionSetReq{
		Month: now.Format("2006-01"),
		Ups: []streamer{
			{
				UserID:    10,
				Character: "正太音",
				Point:     12,
				SoundID:   testSondID,

				roomID: testRoomID,
			},
		},
	}

	c := handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodPost, "/", tutil.ToRequestBody(param))
	_, err = ActionSet(c)
	require.NoError(err)

	assertHelper := func(data actionGetResp) {
		assert.Equal(param.Ups[0].UserID, data.Ups[0].UserID)
		assert.Equal(1, strings.Count(data.Ups[0].IconURL, "http"))
		assert.Equal(param.Ups[0].roomID, data.Ups[0].RoomID)
		assert.Equal(param.Ups[0].Character, data.Ups[0].Character)
		assert.Equal(param.Ups[0].Point, data.Ups[0].Point)
		assert.Equal(param.Ups[0].SoundID, data.Ups[0].SoundID)
		assert.Equal(1, strings.Count(data.Ups[0].SoundURL, "http"))
	}

	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/?month="+param.Month, nil)
	resp, err := ActionGet(c)
	require.NoError(err)
	data := resp.(actionGetResp)
	assert.Len(data.Ups, 1)
	assertHelper(data)
	assert.Equal(0, data.Ups[0].Checked)

	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodPost, "/?month="+param.Month, nil)
	_, err = ActionPublish(c)
	require.NoError(err)

	c = handler.CreateTestContext(true)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/?month="+param.Month, nil)
	resp, err = ActionGet(c)
	require.NoError(err)
	data = resp.(actionGetResp)
	assert.Len(data.Ups, 1)
	assertHelper(data)
	assert.Equal(1, data.Ups[0].Checked)
}

func TestCheckMonth(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	month := []int{
		getMonthInt(now, -1),
		getMonthInt(now, 0),
		getMonthInt(now, 1),
	}
	monthTimestamp := []int64{
		getMonthTimestamp(now, -1),
		getMonthTimestamp(now, 0),
		getMonthTimestamp(now, 1),
	}
	params := []actionSetReq{
		{Month: getMonthStr(now, -1)},
		{Month: getMonthStr(now, 0)},
		{Month: getMonthStr(now, 1)},
	}

	for i := range params {
		assert.NoError(params[i].checkMonth())
		assert.Equal(month[i], params[i].month)
		assert.Equal(monthTimestamp[i], params[i].monthTime.Unix())
	}

	params = []actionSetReq{
		{Month: getMonthStr(now, -2)},
		{Month: getMonthStr(now, 2)},
	}
	for _, v := range params {
		err := v.checkMonth()
		assert.EqualError(err, "年月填写错误：不能与当前月份相差超过一个月")
	}
	params[0].Month = "1414"
	assert.EqualError(params[0].checkMonth(), "年月格式错误：应如 2006-01")
}

func getMonthStr(t time.Time, delta int) string {
	y := t.Year()
	m := t.Month()
	return time.Date(y, time.Month(int(m)+delta), 1, 0, 0, 0, 0, t.Location()).Format("2006-01")
}

func getMonthInt(t time.Time, delta int) int {
	y := t.Year()
	m := t.Month()

	tm := time.Date(y, time.Month(int(m)+delta), 1, 0, 0, 0, 0, t.Location())
	i, _ := strconv.Atoi(tm.Format("200601"))
	return i
}

func getMonthTimestamp(t time.Time, delta int) int64 {
	y := t.Year()
	m := t.Month()
	return time.Date(y, time.Month(int(m)+delta), 1, 0, 0, 0, 0, t.Location()).Unix()
}

func TestStrimerCheck(t *testing.T) {
	assert := assert.New(t)

	var s streamer
	assert.EqualError(s.check(), "请完整填写（有字段为空）")
	s.UserID = -1
	s.Character = "Test123456"
	s.Point = 1
	s.SoundID = 123
	assert.EqualError(s.check(), "M号不存在")
	s.UserID = 3456864
	assert.EqualError(s.check(), "音属填写错误（最大不超过 5 个字符）")
	s.Character = "Test"
	s.SoundID = -1
	assert.EqualError(s.check(), "M音不存在")
	s.SoundID = testSondID
	assert.EqualError(s.check(), "主播的房间号未找到")
	s.UserID = 12
	assert.NoError(s.check())
	assert.NotZero(s.roomID)
}

func TestActionSetReqCheckUps(t *testing.T) {
	assert := assert.New(t)

	p := actionSetReq{}
	assert.EqualError(p.checkUps(), fmt.Sprintf("心动主播数据仅可填写 %d 个", ConfigUpsNum))
	p.Ups = make([]streamer, ConfigUpsNum)
	assert.Error(p.checkUps())
	for i := range p.Ups {
		p.Ups[i] = streamer{UserID: 12, Point: 1, SoundID: testSondID, Character: "test"}
	}
	assert.Nil(p.checkUps())
}
