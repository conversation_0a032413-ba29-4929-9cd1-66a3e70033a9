package activity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	rooms "github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	catalogIDMusic   int64 = 104 //nolint:deadcode,varcheck
	catalogIDPia     int64 = 105 //nolint:deadcode,varcheck
	catalogIDSleep   int64 = 115 //nolint:deadcode,varcheck
	catalogIDEmotion int64 = 116 //nolint:deadcode,varcheck
)

var (
	activityKeyExpireDuration = 45 * 24 * time.Hour

	event157PrepareStartTime = goutil.NewTimeUnixMilli(time.Date(2021, 5, 1, 0, 0, 0, 0, time.Local)) // 2021-05-01
	event157PrepareEndTime   = goutil.NewTimeUnixMilli(time.Date(2021, 5, 18, 0, 0, 0, 0, time.Local))
)

// ActionActivityPrepare 直播活动前的准备
/**
 * @api {post} /rpc/activity/prepare 直播活动前的准备，该接口暂不可调用
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number=157} event_id 活动 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionActivityPrepare(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		EventID int `json:"event_id"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	switch param.EventID {
	case usersrank.EventIDListenTogether:
		if err := addListenTogetherData(); err != nil {
			return nil, err
		}
	case usersrank.EventIDSuperFanParty:
		err := addSuperFanPartyData(event157PrepareStartTime, event157PrepareEndTime)
		if err != nil {
			return nil, err
		}
	default:
		return nil, actionerrors.ErrParams
	}
	return "success", nil
}

// 添加一起听音乐征集直播奖励没有资格的数据
func addListenTogetherData() error {
	const (
		st = 1601481600000 // 2020-10-01 00:00:00.000
		et = 1604160000000 // 2020-11-01 00:00:00.000

		scaleLimit = 0.2 // 20%
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livelog.Collection()
	type m bson.M
	type a bson.A
	cur, err := col.Aggregate(ctx, []m{
		{"$match": m{"start_time": m{"$gte": st}, "end_time": m{"$lt": et}}},
		{"$group": m{
			"_id":      "$creator_id",
			"total":    m{"$sum": "$duration"},
			"duration": m{"$sum": m{"$cond": a{m{"$eq": a{"$catalog_id", catalogIDMusic}}, "$duration", 0}}},
		}},
		{"$project": m{"creator_id": "$_id", "duration": "$duration", "total": "$total"}},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	scales := make([]struct {
		CreatorID int64   `bson:"creator_id"`
		Duration  float64 `bson:"duration"`
		Total     float64 `bson:"total"`
	}, 0)
	err = cur.All(ctx, &scales)
	_ = cur.Close(ctx)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	key := activity.KeyUsersApplication(usersrank.EventIDListenTogether)
	pipeline := service.Redis.TxPipeline()
	ids := make([]interface{}, len(scales))
	for index, value := range scales {
		if value.Duration/value.Total < scaleLimit {
			ids[index] = value.CreatorID
		}
	}
	pipeline.SAdd(key, ids...)
	pipeline.Expire(key, activityKeyExpireDuration)
	_, err = pipeline.Exec()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

// addSuperFanPartyData 超粉嘉年华活动分组
func addSuperFanPartyData(st, et goutil.TimeUnixMilli) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	type m bson.M

	cur, err := livelog.Collection().Aggregate(ctx, []m{
		{"$match": m{"start_time": m{"$gte": st}, "end_time": m{"$lt": et}}},
		{"$group": m{
			"_id":  m{"room_id": "$room_id", "catalog_id": "$catalog_id"},
			"time": m{"$sum": "$duration"},
		}},
		{"$project": m{
			"room_id":            "$_id.room_id",
			"catalog.catalog_id": "$_id.catalog_id",
			"catalog.time":       "$time",
		}},
		{"$group": m{"_id": "$room_id", "catalogs": m{"$push": "$catalog"}}},
	})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	type catalogDuration struct {
		CatalogID int64 `bson:"catalog_id"`
		Duration  int64 `bson:"time"`
	}

	var res []struct {
		RoomID   int64             `bson:"_id"`
		Catalogs []catalogDuration `bson:"catalogs"`
	}

	err = cur.All(ctx, &res)
	if err != nil {
		return err
	}

	if len(res) == 0 {
		return nil
	}

	catalogIDRoomIDs := make(map[int64][]int64)
	for _, room := range res {
		var maxCatalog catalogDuration
		for _, catalog := range room.Catalogs {
			if maxCatalog.Duration < catalog.Duration {
				maxCatalog = catalog
			}
		}

		catalogIDRoomIDs[maxCatalog.CatalogID] = append(catalogIDRoomIDs[maxCatalog.CatalogID], room.RoomID)
	}

	updates := make([]mongo.WriteModel, 0, len(catalogIDRoomIDs))
	for key, roomIDs := range catalogIDRoomIDs {
		updates = append(updates, mongo.NewUpdateManyModel().
			SetFilter(bson.M{"room_id": bson.M{"$in": roomIDs}}).
			SetUpdate(bson.M{"$set": bson.M{"activity_catalog_id": key}}),
		)
	}

	ctx, cancel = service.MongoDB.Context()
	defer cancel()
	_, err = rooms.Collection().BulkWrite(ctx, updates)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}
