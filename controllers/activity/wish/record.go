package wish

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 活动 ID
const (
	eventIDWishes = 552
)

type wishRecordResp struct {
	Data       []*record         `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

type record struct {
	ID         string `json:"id"`
	GoodsName  string `json:"goods_name"`
	GoodsNum   int    `json:"goods_num"`
	CreateTime int64  `json:"create_time"`
	Status     int    `json:"status"`

	Lucky   int       `json:"lucky,omitempty"`
	Rewards []*reward `json:"rewards,omitempty"`
}

type reward struct {
	Name string `json:"name"`
	Num  int64  `json:"num"`

	giftID int64
}

// TODO: 后续兑换积分礼物名需要从数据库中获取
const rewardNameRedeemPoint = "守护石" // 活动兑换积分礼物名

// ActionWishRecord 星座许愿记录
/**
 * @api {get} /api/v2/activity/wish/record 星座许愿记录
 * @apiVersion 0.1.0
 * @apiGroup activity
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": "62fb5705952383926d8750c8",
 *           "goods_name": "梦墟感应", // 商品名
 *           "goods_num": 60, // 购买商品数目
 *           "create_time": 1660030566, // 购买时间，单位：秒
 *           "status": 1, // 开奖状态，0：未开奖，1：已开奖
 *           "lucky": 1, // 幸运等级，仅开出大奖时返回，未开奖或未中大奖时不返回
 *           "rewards": [ // 未开奖时不返回此字段
 *             {
 *               "name": "白给礼物", // 开奖商品名
 *               "num": 20 // 开奖商品数量
 *             },
 *             {
 *               "name": "星愿币",
 *               "num": 20
 *             }
 *           ]
 *         },
 *         {
 *           "id": "62fb5705952383926d8750c7",
 *           "goods_name": "秘境感应",
 *           "goods_num": 3000,
 *           "create_time": 1660030566,
 *           "status": 1,
 *           "rewards": [
 *             {
 *               "name": "星愿币",
 *               "num": 20
 *             }
 *           ]
 *         },
 *         {
 *           "id": "62fb5705952383926d8750c6",
 *           "goods_name": "梦墟感应",
 *           "goods_num": 10,
 *           "create_time": 1660030566,
 *           "status": 0
 *         }
 *       ],
 *       "pagination": {
 *         "count": 20,
 *         "maxpage": 10,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 */
func ActionWishRecord(c *handler.Context) (handler.ActionResponse, error) {
	page, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	goods, err := livegoods.ListLiveGoods(livegoods.GoodsTypeWish)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(goods) == 0 {
		logger.WithFields(logger.Fields{
			"goods_type": livegoods.GoodsTypeWish,
		}).Error("未查到星座许愿商品")
		return nil, actionerrors.ErrCannotFindResource
	}

	type goodWithMore struct {
		ID    int64
		goods livegoods.LiveGoods
		more  *livegoods.More

		giftMap map[int64]livegoods.GiftItem
	}

	gm := make([]goodWithMore, 0, len(goods))
	for _, gd := range goods {
		more, err := gd.UnmarshalMore()
		if err != nil {
			logger.WithFields(logger.Fields{
				"goods_id": gd.ID,
			}).Error(err)
			continue
		}

		m := util.ToMap(more.Gifts, func(g livegoods.GiftItem) int64 {
			return g.ID
		})
		gm = append(gm, goodWithMore{
			ID:      gd.ID,
			goods:   gd,
			more:    more,
			giftMap: m,
		})
	}

	mGoods := util.ToMap(gm, func(g goodWithMore) int64 {
		return g.ID
	})

	eventID := mGoods[goods[0].ID].more.EventID
	e := new(activity.ExtendedFields)
	_, err = mevent.FindSimpleWithExtendedFields(eventID, e)
	if err != nil {
		logger.WithFields(logger.Fields{"event_id": eventID}).Error(err)
		// PASS
	}

	records, pa, err := wishes.ListWishesByUserID(c.UserID(), e.TimeNow(), page, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := wishRecordResp{
		Data:       make([]*record, 0, pageSize),
		Pagination: pa,
	}

	var giftIDs []int64
	for _, rd := range records {
		r := &record{
			ID:         rd.OID.Hex(),
			GoodsNum:   rd.Num,
			CreateTime: rd.CreateTime,
			Status:     rd.Status,
		}

		gd, ok := mGoods[rd.GoodsID]
		if !ok {
			logger.WithFields(logger.Fields{
				"goods_id": rd.GoodsID,
				"user_id":  c.UserID(),
				"wish_id":  r.ID,
			}).Error("goods not found")
			continue
		}
		if gd.more == nil {
			logger.WithFields(logger.Fields{
				"goods_id": rd.GoodsID,
				"user_id":  c.UserID(),
				"wish_id":  r.ID,
			}).Error("more is nil")
			continue
		}
		r.GoodsName = gd.goods.Title

		for _, v := range rd.Rewards {
			d := new(reward)
			switch v.Type {
			case wishes.RewardTypeRedeemPoint:
				d.Num = v.Point
				d.Name = rewardNameRedeemPoint
			case wishes.RewardTypeBackpackGift:
				g, ok := gd.giftMap[v.GiftID]
				if !ok {
					logger.WithFields(logger.Fields{
						"user_id": c.UserID(),
						"wish_id": r.ID,
						"gift_id": v.GiftID,
					}).Error("gift not found")
					continue
				}
				// 抽中多个礼物的话，按照抽中最大奖的幸运等级来显示
				r.Lucky = max(r.Lucky, g.Lucky)

				d.Num = int64(v.Num)
				d.giftID = v.GiftID
				giftIDs = append(giftIDs, d.giftID)
			default:
				logger.WithFields(logger.Fields{
					"user_id":     c.UserID(),
					"wish_id":     r.ID,
					"reward_type": v.Type,
				}).Error("错误的奖励类型")
				continue
			}
			r.Rewards = append(r.Rewards, d)
		}
		resp.Data = append(resp.Data, r)
	}

	if len(giftIDs) == 0 {
		return resp, nil
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, data := range resp.Data {
		for _, r := range data.Rewards {
			if r.giftID == 0 {
				continue
			}
			giftInfo, ok := giftMap[r.giftID]
			if !ok {
				logger.WithFields(logger.Fields{
					"user_id": c.UserID(),
					"wish_id": data.ID,
					"gift_id": r.giftID,
				}).Error("gift not found")
				continue
			}
			r.Name = giftInfo.Name
		}
	}

	return resp, nil
}
