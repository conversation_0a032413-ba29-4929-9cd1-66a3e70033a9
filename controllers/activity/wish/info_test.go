package wish

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/redis/wish"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionWishInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 1, 23, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	var (
		now = goutil.TimeNow()

		testGoodID15 int64 = 15
		testGoodID16 int64 = 16
		testUserID   int64 = 3131311
	)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info", true, nil)
	_, err := ActionWishInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 获取还未进行中的活动信息
	key := keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeWish)
	require.NoError(service.LRURedis.Del(key).Err())
	err = service.LiveDB.Table(livegoods.TableName()).Where("id IN (?)", []int64{testGoodID15, testGoodID16}).Updates(
		map[string]any{
			"start_time":      now.AddDate(0, 0, -1).Unix(),
			"sale_start_time": now.AddDate(0, 0, 1).Unix(),
		},
	).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info?event_id=214", true, nil)
	resp, err := ActionWishInfo(c)
	require.NoError(err)
	r := resp.(wishInfoResp)
	assert.Equal(r.WishStatus, wishNotStarted)
	assert.Equal("玩法尚未开启", r.WishInfo)
	require.Len(r.GoodsPools, 2)
	assert.Equal(int64(15), r.GoodsPools[0].GoodsID)
	assert.Equal(int64(16), r.GoodsPools[1].GoodsID)

	// 获取进行中的活动信息
	require.NoError(service.LRURedis.Del(key).Err())
	err = service.LiveDB.Table(livegoods.TableName()).Where("id IN (?)", []int64{testGoodID15, testGoodID16}).Updates(
		map[string]any{
			"sale_start_time": now.AddDate(0, 0, -1).Unix(),
		},
	).Error
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info?event_id=214", true, nil)
	resp, err = ActionWishInfo(c)
	require.NoError(err)
	r = resp.(wishInfoResp)
	assert.Equal("23:00 - 23:15 感应进行中", r.WishInfo)
	assert.Equal(r.WishStatus, wishOngoing)
	require.Len(r.GoodsPools, 2)
	assert.Equal(int64(15), r.GoodsPools[0].GoodsID)
	assert.Equal(int64(16), r.GoodsPools[1].GoodsID)

	// 获取进行中的活动信息
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 1, 23, 35, 0, 0, time.Local)
	})
	c = handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info?event_id=214", true, nil)
	resp, err = ActionWishInfo(c)
	require.NoError(err)
	r = resp.(wishInfoResp)
	assert.Equal("23:30 - 23:45 感应进行中", r.WishInfo)

	// 测试用户许愿后许愿商品数量变化
	err = service.Redis.Del(wish.KeyWishNum(testGoodID15, goutil.TimeNow()), wish.KeyWishNum(testGoodID16, goutil.TimeNow())).Err()
	require.NoError(err)
	n, err := wish.IncrUserWishNum(testGoodID15, testUserID, 10, goutil.TimeNow())
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info?event_id=214", true, nil)
	c.User().ID = testUserID
	resp, err = ActionWishInfo(c)
	require.NoError(err)
	r = resp.(wishInfoResp)
	require.NotEmpty(r.GoodsPools)
	assert.EqualValues(30-n, r.GoodsPools[0].RemainingNum)
	assert.EqualValues(3000, r.GoodsPools[1].RemainingNum)

	// 测试 redis 计数错误的情况
	_, err = wish.IncrUserWishNum(testGoodID15, testUserID, 3000, goutil.TimeNow())
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/info?event_id=214", true, nil)
	c.User().ID = testUserID
	resp, err = ActionWishInfo(c)
	require.NoError(err)
	r = resp.(wishInfoResp)
	require.Len(r.GoodsPools, 2)
	assert.Zero(r.GoodsPools[0].RemainingNum)
}
