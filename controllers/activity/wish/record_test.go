package wish

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionWishRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 231232
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := wishes.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	ws := make([]interface{}, 0, 20)
	now := util.TimeNow()
	for i := 1; i < 20; i++ {
		w := wishes.Wish{
			CreateTime: now.Unix(),
			UserID:     testUserID,
			GoodsID:    15,
			GoodsPrice: 10,
		}
		ws = append(ws, w)
	}
	w := wishes.Wish{
		CreateTime: now.Add(time.Second).Unix(),
		UserID:     testUserID,
		GoodsID:    15,
		GoodsPrice: 300,
		Rewards: []wishes.Reward{
			{
				GiftID: 3001,
				Type:   wishes.RewardTypeRedeemPoint,
				Point:  100,
			},
		},
	}
	ws = append(ws, w)
	res, err := wishes.Collection().InsertMany(ctx, ws)
	require.NoError(err)
	require.Equal(20, len(res.InsertedIDs))

	c := handler.NewTestContext(http.MethodGet, "/api/v2/activity/wish/record", true, nil)
	c.User().ID = testUserID
	resp, err := ActionWishRecord(c)
	require.NoError(err)
	r := resp.(wishRecordResp)
	require.Len(r.Data, 20)
	assert.Equal(r.Data[0].GoodsName, "秘境感应")
	require.Len(r.Data[0].Rewards, 1)
	assert.Equal("画境石", r.Data[0].Rewards[0].Name)
}
