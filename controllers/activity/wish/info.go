package wish

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/redis/wish"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type wishInfoResp struct {
	WishStartTime int64       `json:"wish_start_time"`
	WishEndTime   int64       `json:"wish_end_time"`
	WishStatus    int         `json:"wish_status"`
	WishInfo      string      `json:"wish_info"`
	GoodsPools    []goodsInfo `json:"goods_pools"`
}

type goodsInfo struct {
	GoodsID      int64  `json:"goods_id"`
	GoodsName    string `json:"goods_name"`
	GoodsPrice   int    `json:"goods_price"`
	RemainingNum int64  `json:"remaining_num"`
}

// 活动状态
const (
	wishNotStarted = iota // 活动未开始
	wishOngoing           // 活动进行中
)

// ActionWishInfo 星座许愿信息
/**
 * @api {get} /api/v2/activity/wish/info 星座许愿信息
 * @apiVersion 0.1.0
 * @apiGroup activity
 *
 * @apiParam {Number} event_id 活动 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "wish_title": "处女座之约", // 星座名
 *       "wish_start_time": 1660030566, // 星座活动开始时间，单位：秒
 *       "wish_end_time": 1660530566, // 星座活动结束时间，单位：秒
 *       "wish_status": 1, // 星座活动状态，0：星座许愿活动未开启，1：星座许愿活动进行中
 *       "wish_info": "02:00 - 02:30 感应进行中", // 星座活动信息
 *       "goods_pools": [
 *         {
 *           "goods_id": 101, // 商品 ID
 *           "goods_name": "梦墟感应", // 商品名
 *           "goods_price": 30, // 单次许愿价格，单位：钻石
 *           "remaining_num": 20 // 剩余购买次数
 *         },
 *         {
 *           "goods_id": 102,
 *           "goods_name": "秘境感应",
 *           "goods_price": 10,
 *           "remaining_num": 50
 *         }
 *       ]
 *     }
 *   }
 *
 */
func ActionWishInfo(c *handler.Context) (handler.ActionResponse, error) {
	eventID, err := c.GetParamInt64("event_id")
	if err != nil || eventID <= 0 {
		return nil, actionerrors.ErrParams
	}

	goods, err := livegoods.ListLiveGoods(livegoods.GoodsTypeWish)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(goods) == 0 {
		logger.WithFields(logger.Fields{
			"goods_type": livegoods.GoodsTypeWish,
		}).Error("未查到星座许愿商品")
		return nil, actionerrors.ErrCannotFindResource
	}

	e := new(activity.ExtendedFields)
	_, err = mevent.FindSimpleWithExtendedFields(eventID, e)
	if err != nil {
		logger.WithFields(logger.Fields{"event_id": eventID}).Error(err)
		// PASS
	}
	now := e.TimeNow()

	avaliableGoods := make([]livegoods.LiveGoods, 0, len(goods))
	for _, gd := range goods {
		tr := goutil.NewTimeRangeUnix(gd.StartTime, gd.EndTime)
		if tr.Between(now) {
			avaliableGoods = append(avaliableGoods, gd)
		}
	}

	var resp wishInfoResp
	if len(avaliableGoods) != 0 &&
		// 判断当前许愿商品是否在开售时间内，因为许愿商品开售时间相同，所以这里只需要判断第一个商品开售时间即可
		goutil.NewTimeRangeUnix(avaliableGoods[0].SaleStartTime, avaliableGoods[0].SaleEndTime).Between(now) {
		// 许愿活动期间，单轮许愿时间为 15min
		wishBegin := now.Truncate(15 * time.Minute)
		wishEnd := wishBegin.Add(15 * time.Minute)
		wishEndStr := wishEnd.Format(util.TimeFormatHHMM)
		if wishEnd.Hour() == 0 && wishEnd.Minute() == 0 {
			wishEndStr = "24:00"
		}
		resp.WishStatus = wishOngoing
		resp.WishInfo = fmt.Sprintf("%s - %s 感应进行中", wishBegin.Format(util.TimeFormatHHMM), wishEndStr)
	} else {
		resp.WishStatus = wishNotStarted
		resp.WishInfo = "玩法尚未开启"
	}

	userID := c.UserID()
	resp.GoodsPools = make([]goodsInfo, 0, len(avaliableGoods))
	for _, gd := range avaliableGoods {
		// 获取许愿活动开始和结束时间
		resp.WishStartTime = min(gd.SaleStartTime, resp.WishStartTime)
		resp.WishEndTime = max(gd.SaleEndTime, resp.WishEndTime)

		info := goodsInfo{
			GoodsID:    gd.ID,
			GoodsName:  gd.Title,
			GoodsPrice: gd.Price,
		}
		// 仅在售卖时间内，才显示剩余许愿次数
		if resp.WishStatus == wishOngoing {
			var num int64
			more, err := gd.UnmarshalMore()
			if err != nil {
				logger.WithFields(logger.Fields{"goods_id": gd.ID}).Error(err)
				// PASS
			} else if more == nil || len(more.Limits) != 1 {
				logger.WithFields(logger.Fields{"goods_id": gd.ID}).Error("商品信息错误")
				// PASS
			} else {
				num = more.Limits[0].Num
			}

			n, err := wish.UserWishNum(gd.ID, userID, now)
			if err != nil {
				logger.WithFields(logger.Fields{
					"goods_id": gd.ID,
					"user_id":  userID,
				}).Error(err)
				// PASS
			} else {
				// 获取用户当前许愿次数
				info.RemainingNum = max(num-n, 0)
			}
		}
		resp.GoodsPools = append(resp.GoodsPools, info)
	}

	return resp, nil
}
