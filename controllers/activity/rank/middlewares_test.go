package rank

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestIsErrNoFeed(t *testing.T) {
	assert := assert.New(t)

	assert.True(isErrNoFeed(errNoFeed))
	assert.False(isErrNoFeed(errors.New("test")))
}

func TestRankMiddlewareMap(t *testing.T) {
	checker := tutil.NewKeyChecker(t, tutil.MapString)
	checker.Check(rankMiddlewareMap(),
		MiddlewareKeyCollectGiftNum,
		MiddlewareKeyCollectLuckyGiftNum,
		MiddlewareKeyAddChallengerBuff,
		MiddlewareKeyCollectGiftPoint,
		MiddlewareKeyUserCost,
		MiddlewareKeyPointLimit,
	)
}
