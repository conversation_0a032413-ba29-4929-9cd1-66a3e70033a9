package rank

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMiddlewareAddChallengerBuff(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var now int64
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(now, 0)
	})
	r := rank{
		Rank:    activity.Rank{},
		eventID: 1999,
		event: event{
			ExtendedFields: &activity.ExtendedFields{
				Ranks: []activity.Rank{
					{
						Key:           "1",
						RankStartTime: 1,
						RankEndTime:   2,
					},
					{
						Key:           "2",
						RankStartTime: 2,
						RankEndTime:   3,
					},
					{
						Key:           "3",
						RankStartTime: 3,
						RankEndTime:   4,
					},
				},
			},
		},
	}
	key2 := activity.KeyRank(r.eventID, "2")
	key3 := activity.KeyRank(r.eventID, "3")
	require.NoError(service.Redis.Del(key2, key3).Err())

	add := addRankParam{
		score:    10,
		toUserID: 1,
	}

	require.NoError(middlewareAddChallengerBuff(r, &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"keys":["1","2","3"]}`),
	}, &add))
	assert.EqualValues(10, add.score)

	now = 10
	require.NoError(service.Redis.ZIncrBy(key3, 1, "1").Err())
	require.NoError(middlewareAddChallengerBuff(r, &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"keys":["1","2","3"]}`),
	}, &add))
	assert.EqualValues(12, add.score)

	add.score = 10
	require.NoError(service.Redis.ZIncrBy(key2, 2, "1").Err())
	require.NoError(middlewareAddChallengerBuff(r, &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"keys":["1","2","3"]}`),
	}, &add))
	assert.EqualValues(13, add.score)
}

func TestChallengerBuff(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var now int64
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(now, 0)
	})
	e := activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "1",
				RankStartTime: 1,
				RankEndTime:   2,
			},
			{
				Key:           "2",
				RankStartTime: 2,
				RankEndTime:   3,
			},
			{
				Key:           "3",
				RankStartTime: 3,
				RankEndTime:   4,
			},
		},
	}
	var eventID int64 = 1999
	key2 := activity.KeyRank(eventID, "2")
	key3 := activity.KeyRank(eventID, "3")
	require.NoError(service.Redis.Del(key2, key3).Err())

	status, err := challengerBuff(eventID, 1, e, arenaParam{
		Keys: []string{"1", "2", "3"},
	})
	require.NoError(err)
	assert.Equal(challengerBuffNone, status)

	now = 10
	require.NoError(service.Redis.ZIncrBy(key3, 1, "1").Err())
	status, err = challengerBuff(eventID, 1, e, arenaParam{
		Keys: []string{"1", "2", "3"},
	})
	require.NoError(err)
	assert.Equal(challengerBuffChallenger, status)

	require.NoError(service.Redis.ZIncrBy(key2, 1, "2").Err())
	status, err = challengerBuff(eventID, 1, e, arenaParam{
		Keys: []string{"1", "2", "3"},
	})
	require.NoError(err)
	assert.Equal(challengerBuffChallenger, status)

	require.NoError(service.Redis.ZIncrBy(key2, 2, "1").Err())
	status, err = challengerBuff(eventID, 1, e, arenaParam{
		Keys: []string{"1", "2", "3"},
	})
	require.NoError(err)
	assert.Equal(challengerBuffSuperChallenger, status)

	require.NoError(service.Redis.ZIncrBy(key2, 2, "1").Err())
	status, err = challengerBuff(eventID, 1, e, arenaParam{
		Keys:     []string{"1", "2", "3"},
		Duration: 1000,
	})
	require.NoError(err)
	assert.Equal(challengerBuffNone, status)

	e = activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "1",
				RankStartTime: 1,
				RankEndTime:   2,
			},
			{
				Key:           "2",
				RankStartTime: 2,
				RankEndTime:   3,
			},
			{
				Key:           "3",
				RankStartTime: 3,
				RankEndTime:   4,
			},
			{
				Key:           "4",
				RankStartTime: 20,
				RankEndTime:   30,
			},
		},
	}
	status, err = challengerBuff(eventID, 1, e, arenaParam{
		Keys:     []string{"1", "2", "3", "4"},
		Duration: 1000,
	})
	require.NoError(err)
	assert.Equal(challengerBuffNone, status)
}

func TestLastTwoArenaRankKeys(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ranks := []activity.Rank{
		{
			Key:           "1",
			RankStartTime: 1,
			RankEndTime:   2,
		},
		{
			Key:           "2",
			RankStartTime: 2,
			RankEndTime:   3,
		},
		{
			Key:           "3",
			RankStartTime: 3,
			RankEndTime:   4,
		},
	}

	rs, status := lastTwoArenaRankKeys(ranks, 0)
	assert.Empty(rs)
	assert.Equal(arenaStatusAllRanksNotStart, status)

	rs, status = lastTwoArenaRankKeys(ranks, 1)
	assert.Empty(rs)
	assert.Equal(arenaStatusRanksOnGoing, status)

	rs, status = lastTwoArenaRankKeys(ranks, 2)
	require.Len(rs, 1)
	assert.Equal("1", rs[0].Key)
	assert.Equal(arenaStatusRanksOnGoing, status)

	rs, status = lastTwoArenaRankKeys(ranks, 4)
	require.Len(rs, 2)
	assert.Equal("3", rs[0].Key)
	assert.Equal("2", rs[1].Key)
	assert.Equal(arenaStatusAllRanksEnd, status)

	rs, status = lastTwoArenaRankKeys(ranks, 5)
	require.Len(rs, 2)
	assert.Equal("3", rs[0].Key)
	assert.Equal("2", rs[1].Key)
	assert.Equal(arenaStatusAllRanksEnd, status)

	ranks = []activity.Rank{
		{
			Key:           "3",
			RankStartTime: 3,
			RankEndTime:   4,
		},
	}
	rs, status = lastTwoArenaRankKeys(ranks, 5)
	require.Len(rs, 1)
	assert.Equal("3", rs[0].Key)
	assert.Equal(arenaStatusAllRanksEnd, status)
}
