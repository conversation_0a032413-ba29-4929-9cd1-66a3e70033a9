package rank

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGuildRankCheckApplication(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g := guildRank{guildID: 0, rank: rank{
		eventID: 1,
		Rank: activity.Rank{
			Application: 0,
			Collect: &activity.Collect{
				Type: CollectTypeGuild,
			},
		},
		goodsParam: &SyncGoodsParam{
			SyncCommonParam: nil,
		},
	}}
	assert.False(g.checkApplication(0))

	p := SyncCommonParam{
		ToUserID: 123,
	}
	g.goodsParam = p.SetGuildID(1).SetRevenueGoods("", 100)
	assert.True(g.checkApplication(0))

	g.goodsParam = nil
	g.giftParam = p.SetGift(nil, 1)
	assert.True(g.checkApplication(0))

	g.Application = activity.ApplicationAllowList
	key := activity.KeyUsersApplication(1)
	require.NoError(service.Redis.SAdd(key, 1).Err())
	defer service.Redis.Del(key)
	assert.True(g.checkApplication(0))

	g.Application = activity.ApplicationBlockList
	assert.False(g.checkApplication(0))

	g.Application = activity.ApplicationGuildAllowList
	key1 := activity.KeyGuildUsersApplication(g.eventID)
	require.NoError(service.Redis.HSet(key1, 123, 1).Err())
	defer service.Redis.Del(key1)
	assert.True(g.checkApplication(0))

	g.giftParam.GuildID = 2
	assert.False(g.checkApplication(0))
}

func TestGuildRankFeedRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 99998
		guildID     int64 = 1
	)
	roomKey := activity.KeyGuildCreatorRank(guildID, strconv.FormatInt(eventID, 10))
	activityKey := activity.KeyGuildRank(strconv.FormatInt(eventID, 10))
	require.NoError(service.Redis.Del(roomKey, activityKey).Err())
	defer service.Redis.Del(roomKey, activityKey)

	e := guildRank{
		guildID: guildID,
		rank: rank{
			eventID: eventID,
			Rank: activity.Rank{
				Application: activity.ApplicationNone,
			},
			expireTime: goutil.TimeNow().Add(time.Hour).Add(ActivityKeyExpire15Days),
		}}

	e.giftParam = &SyncGiftParam{
		SyncCommonParam: &SyncCommonParam{
			RoomID:     roomID,
			FromUserID: fromUserID1,
			ToUserID:   toUserID,
			GuildID:    guildID,
		},
		GiftNum:   5,
		GiftPrice: 100,
	}
	param, err := e.beforeFeed()
	require.NoError(err)
	before, after, err := e.feedRank(param)
	require.NoError(err)
	assert.Zero(before)
	assert.EqualValues(500, after)

	score, err := service.Redis.ZScore(roomKey, strconv.FormatInt(toUserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(500, score)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(guildID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(500, score)

	// ttl
	ttl, err := service.Redis.TTL(activityKey).Result()
	require.NoError(err)
	assert.Less(time.Hour+15*24*time.Hour-5*time.Second, ttl)
}
