package rank

import (
	modelactivity "github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
)

// middlewareUserCost 统计用户实际消费
func middlewareUserCost(r rank, middleParam *modelactivity.RankMiddlewareParam, addParam *addRankParam) error {
	if r.goodsParam != nil {
		return nil
	}

	if r.giftParam.GiftType == gift.TypeRebate || r.giftParam.GiftType == gift.TypeFree {
		return errNoFeed
	}

	if r.giftParam.IsLuckyGift() {
		// 随机礼物的实际消费
		addParam.score = float64(int64(r.giftParam.LuckyGiftNum) * r.giftParam.LuckyGiftPrice)
		return nil
	}
	return nil
}
