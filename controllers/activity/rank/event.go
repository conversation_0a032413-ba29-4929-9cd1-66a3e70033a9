package rank

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goserviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// eventer 活动相关接口
// TODO: 后续支持查询
type eventer interface {
	eventID() int64
	collectType() int

	setGiftParam(param *SyncGiftParam)
	setGoodsParam(param *SyncGoodsParam)
	checkRankTime() bool
	checkLockActivityRankComponent() bool
	ranks() []ranker
}

// ranker 榜单相关接口
type ranker interface {
	checkRankTime() bool
	checkApplication(userID int64) bool
	checkCatalog() bool
	checkGift(giftID int64) bool
	checkGoodsType(goodsType string) bool

	// 函数返回的值会传入到 feedRank 作为参数
	// 可用于处理活动的特殊情况，如获取用户实际消费、统计用户送礼个数等
	beforeFeed() (param *addRankParam, err error)
	feedRank(param *addRankParam) (before, after int64, err error)
	afterFeed(param *SyncCommonParam, before, after int64)
}

type event struct {
	mevent.Simple

	ExtendedFields *activity.ExtendedFields
}

func (e event) eventID() int64 {
	return e.ID
}

func (e event) collectType() int {
	if !(e.ExtendedFields != nil && e.ExtendedFields.Collect != nil) {
		return 0
	}
	return e.ExtendedFields.Collect.Type
}

func (e event) checkRankTime() bool {
	now := e.ExtendedFields.TimeNow().Unix()
	return e.StartTime <= now && now < e.EndTime &&
		e.ExtendedFields.RankStartTime <= now && now < e.ExtendedFields.RankEndTime
}

// checkLockActivityRankComponent redis 查询失败不加积分，key 不存在不加积分
func (e event) checkLockActivityRankComponent() bool {
	key := keys.LockActivityRankComponent1.Format(e.eventID())
	err := service.Redis.Get(key).Err()
	if err != nil && !goserviceredis.IsRedisNil(err) {
		logger.Error(err)
	}
	return err != nil
}

func newEvent(eventID int64) (*event, error) {
	e, err := activity.GetEvent(eventID)
	if err != nil {
		return nil, err
	}
	return newEventByMEvent(e)
}

func newEventByMEvent(simple *mevent.Simple) (*event, error) {
	if simple == nil {
		return nil, errors.New("活动不存在")
	}
	if simple.ExtendedFields == "" {
		return nil, errors.New("ExtendedFields 不存在")
	}

	e := event{
		*simple,
		activity.NewExtendedFields(),
	}
	err := json.Unmarshal([]byte(simple.ExtendedFields), &e.ExtendedFields)
	if err != nil {
		return nil, err
	}
	return &e, nil
}

type syncEvent struct {
	event

	giftParam  *SyncGiftParam
	goodsParam *SyncGoodsParam
}

func (e *syncEvent) setGiftParam(param *SyncGiftParam) {
	e.giftParam = param
}

func (e *syncEvent) setGoodsParam(param *SyncGoodsParam) {
	e.goodsParam = param
}

type rank struct {
	activity.Rank
	giftParam  *SyncGiftParam
	goodsParam *SyncGoodsParam

	eventID    int64
	timeNow    func() time.Time
	expireTime time.Time
	event      event
}

// ongoingEvent get ongoing events list
func ongoingEvent() ([]eventer, error) {
	fn := func(list []*mevent.Simple) ([]event, error) {
		events := make([]event, 0, len(list))
		for _, simple := range list {
			if simple.ExtendedFields == "" {
				continue
			}

			var e event
			err := json.Unmarshal([]byte(simple.ExtendedFields), &e.ExtendedFields)
			if err != nil {
				return nil, err
			}
			e.Simple = *simple
			events = append(events, e)
		}
		return events, nil
	}
	key := keys.KeyActivityOngoingLiveRankEvent0.Format()
	str, err := service.Redis.Get(key).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		// PASS
	} else if str != "" {
		var simpleList []*mevent.Simple
		if err := json.Unmarshal([]byte(str), &simpleList); err != nil {
			logger.Error(err)
			// PASS
		} else {
			events, err := fn(simpleList)
			if err == nil {
				return eventToActivity(events), nil
			}
			logger.Error(err)
			// PASS
		}
	}

	simpleList, err := mevent.ListLiveRankOngoingSimple()
	if err != nil {
		return nil, err
	}

	bytes, err := json.Marshal(simpleList)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.Redis.Set(key, bytes, 5*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	events, err := fn(simpleList)
	if err != nil {
		return nil, err
	}
	return eventToActivity(events), nil
}

// eventToActivity []*event to []eventer
// 不确定 eventer 接口是否有用，先保留，确定无用后再删除
func eventToActivity(events []event) []eventer {
	var res []eventer
	for _, e := range events {
		if !(e.ExtendedFields != nil && e.ExtendedFields.Collect != nil) {
			continue
		}
		res = append(res, &syncEvent{event: e})
	}
	return res
}

func (r rank) checkGift(giftID int64) bool {
	if len(r.Collect.Gifts) == 0 {
		return true
	}

	for _, item := range r.Collect.Gifts {
		if item.GiftID == giftID {
			return true
		}
	}
	return false
}

// checkGoods check goods
// TODO: 支持超粉贵族单独判断
func (r rank) checkGoodsType(goodsType string) bool {
	// 收集榜不支持商品
	if r.Collect.Type == CollectTypeNum {
		return false
	}
	return r.NobleExtra == 1
}

func (r rank) getCommonParam() *SyncCommonParam {
	if r.giftParam != nil {
		return r.giftParam.SyncCommonParam
	}
	if r.goodsParam != nil {
		return r.goodsParam.SyncCommonParam
	}
	panic("无法获取 SyncCommonParam")
}

func (r rank) guildID() int64 {
	p := r.getCommonParam()
	if p == nil {
		return 0
	}
	return p.GuildID
}

func (r rank) lastKey() string {
	if r.LastKey == nil || *r.LastKey == "" {
		return ""
	}

	switch r.Collect.Type {
	case CollectTypeGuild:
		return usersrank.ActivityGuildKey(strconv.FormatInt(r.eventID, 10) + "/" + *r.LastKey)
	}
	return usersrank.ActivityKey(strconv.FormatInt(r.eventID, 10) + "/" + *r.LastKey)
}

// buildRank 根据配置构建 ranker
// 如果 rank 有配置则用 rank 配置
// 如果 rank 没有配置则用 ExtendedFields 配置
func buildRanker(e syncEvent, r *activity.Rank) ranker {
	rank := rank{
		eventID:    e.ID,
		timeNow:    e.ExtendedFields.TimeNow,
		expireTime: time.Unix(e.EndTime, 0).Add(ActivityKeyExpire15Days),
		giftParam:  e.giftParam,
		goodsParam: e.goodsParam,
		event:      e.event,
	}
	if r == nil {
		rank.Rank = activity.Rank{
			RankStartTime: e.ExtendedFields.RankStartTime,
			RankEndTime:   e.ExtendedFields.RankEndTime,
		}
	} else {
		rank.Rank = *r
	}

	if rank.Rank.Collect == nil {
		rank.Rank.Collect = e.ExtendedFields.Collect
	}
	if rank.Rank.Application == activity.ApplicationNone {
		rank.Rank.Application = e.ExtendedFields.Application
	}
	if rank.Rank.NobleExtra == 0 && e.ExtendedFields.NobleExtra != nil {
		rank.Rank.NobleExtra = *e.ExtendedFields.NobleExtra
	}
	if len(rank.Rank.CatalogIDs) == 0 && e.ExtendedFields.CatalogID != nil {
		rank.Rank.CatalogIDs = []int64{*e.ExtendedFields.CatalogID}
	}
	if len(rank.Rank.ActivityCatalogIDs) == 0 {
		rank.Rank.ActivityCatalogIDs = e.ExtendedFields.ActivityCatalogIDs
	}

	switch rank.Rank.Collect.Type {
	case CollectTypeNum:
		return &collectRank{rank: rank}
	case CollectTypePoint:
		return &pointRank{rank: rank}
	case CollectTypeGuild:
		return &guildRank{rank: rank}
	case CollectTypeUser:
		return &userRank{pointRank: pointRank{rank}}
	case CollectUserWall:
		return wallUserRank{rank: rank}
	case CollectCreatorWall:
		return wallCreatorRank{rank: rank}
	}
	return nil
}

func (e syncEvent) ranks() []ranker {
	if e.ExtendedFields.Ranks == nil {
		if r := buildRanker(e, nil); r != nil {
			return []ranker{r}
		}
		return []ranker{}
	}

	ranks := make([]ranker, 0, len(e.ExtendedFields.Ranks))
	for i := range e.ExtendedFields.Ranks {
		r := buildRanker(e, &e.ExtendedFields.Ranks[i])
		if r != nil {
			ranks = append(ranks, r)
		}
	}
	return ranks
}

func (r rank) checkRankTime() bool {
	now := r.timeNow().Unix()
	return r.RankStartTime <= now && r.RankEndTime > now
}

func (r rank) checkApplication(userID int64) bool {
	return checkApplication(r, userID, r.guildID())
}

// checkApplication 判断是否有资格
// 1. 判断是否有前置榜单要求，如果有前置榜单要求那么判断是否满足前置榜单要求
// 2. 判断是否有资格参加活动
func checkApplication(r rank, userID, guildID int64) bool {
	// 公会分组赛目前没有晋级赛需求
	if r.Application == activity.ApplicationGuildAllowList {
		return checkInGuildAllowList(r.eventID, r.Key, guildID, userID)
	}

	if lastKey := r.lastKey(); lastKey != "" {
		if !r.CheckPromoted(lastKey, strconv.FormatInt(userID, 10)) {
			return false
		}
	}

	switch r.Collect.Type {
	case CollectTypeGuild:
		return checkInApplication(r.Application, r.eventID, guildID, r.Key)
	}
	return checkInApplication(r.Application, r.eventID, userID, r.Key)
}

// checkInApplication 判断是否有资格参加活动
func checkInApplication(application activity.ApplicationType, eventID, memberID int64, keys ...string) bool {
	if application == activity.ApplicationNone {
		return true
	}
	key := activity.KeyUsersApplication(eventID, keys...)
	exists, err := service.Redis.SIsMember(key, memberID).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	if application == activity.ApplicationAllowList {
		return exists
	}
	return !exists
}

// checkCatalog 判断分区
// TODO: 添加对 CatalogID 的判断
func (r rank) checkCatalog() bool {
	return r.CheckCatalog(r.getCommonParam().ActivityCatalogID)
}

type addRankParam struct {
	score      float64
	fromUserID int64
	toUserID   int64
	roomID     int64
	guildID    int64
}

func (r rank) buildAddRankParam() *addRankParam {
	switch {
	case r.goodsParam != nil:
		param := &addRankParam{
			score:      float64(r.goodsParam.GoodsPrice),
			fromUserID: r.goodsParam.FromUserID,
			toUserID:   r.goodsParam.ToUserID,
			roomID:     r.goodsParam.RoomID,
			guildID:    r.goodsParam.GuildID,
		}
		return param
	case r.giftParam != nil:
		param := &addRankParam{
			score:      float64(r.giftParam.GiftPrice * int64(r.giftParam.GiftNum)),
			fromUserID: r.giftParam.FromUserID,
			toUserID:   r.giftParam.ToUserID,
			roomID:     r.giftParam.RoomID,
			guildID:    r.giftParam.GuildID,
		}
		// 活动免费礼物增加积分
		if r.giftParam.GiftAttr.IsSet(gift.AttrPointAddActivity) {
			// 兼容 point 与 price 同时存在的情况
			param.score += float64(r.giftParam.GiftPoint * int64(r.giftParam.GiftNum))
		}
		return param
	default:
		panic("商品和礼物不满足要求")
	}
}

func (r rank) formatEventKey() string {
	if r.Key != "" {
		return fmt.Sprintf("%d/%s", r.eventID, r.Key)
	}
	return strconv.FormatInt(r.eventID, 10)
}
