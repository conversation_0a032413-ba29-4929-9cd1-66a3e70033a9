package engine

import (
	"errors"
	"fmt"
	"strings"
	"sync"

	lua "github.com/yuin/gopher-lua"
	"github.com/yuin/gopher-lua/parse"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/rankuserinfo"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/annual"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/luckyuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// 小窗类型
const (
	WidgetTypeNormal       = iota + 1 // 正常小窗
	WidgetTypeEnding                  // 活动结束小窗
	WidgetTypeInBan                   // 黑名单小窗
	WidgetTypeNotQualified            // 被淘汰小窗
	WidgetTypeWarmUp                  // 活动开始前预热小窗
)

var (
	methods = map[string]lua.LGFunction{
		"get_user_rank":                   getUserRank,
		"get_rank_up":                     getRankUp,
		"get_user_info":                   getUserInfo,
		"get_guild_info":                  getGuildInfo,
		"get_today_finished_quest_detail": getTodayFinishedQuestDetail,
		"get_quest_score":                 getQuestScore,
		"get_lucky":                       getLucky,
		"get_taskrank_progress":           getTaskRankProgress, // TODO: 迁移到 live-activity
	}
	methodsLock = sync.Mutex{}
)

// AppendLuaMethods 添加 lua function
func AppendLuaMethods(funcName string, f lua.LGFunction) {
	methodsLock.Lock()
	defer methodsLock.Unlock()

	methods[funcName] = f
}

func getUserRank(l *lua.LState) int {
	key := l.ToString(2)
	userID := l.ToInt64(3)
	info := rankuserinfo.Info{
		ID: userID,
	}
	if err := info.GetUserInfo(key); err != nil {
		return LuaReturn(l, nil, err)
	}
	return LuaReturn(l, map[string]interface{}{
		"id":    info.ID,
		"rank":  info.Rank,
		"score": info.Score,
	}, nil)
}

func getRankUp(l *lua.LState) int {
	key := l.ToString(2)
	rank := l.ToInt64(3)

	_, score, err := rankuserinfo.RankMemberIDWithScore(key, rank)
	if err != nil && !serviceredis.IsRedisNil(err) {
		return LuaReturn(l, nil, err)
	}
	return LuaReturn(l, score, nil)
}

func getUserInfo(l *lua.LState) int {
	userID := l.ToInt64(2)

	u, err := mowangskuser.FindByUserID(userID)
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	if u == nil {
		return LuaReturn(l, nil, errors.New("can not found user"))
	}
	return LuaReturn(l, map[string]interface{}{
		"id":       u.ID,
		"username": u.Username,
		"iconurl":  u.IconURL,
	}, nil)
}

func getGuildInfo(l *lua.LState) int {
	guildID := l.ToInt64(2)

	g, err := guild.SimpleInfo(guildID)
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	if g == nil {
		return LuaReturn(l, nil, errors.New("can not found guild"))
	}
	return LuaReturn(l, map[string]interface{}{
		"id":   g.ID,
		"name": g.Name,
	}, nil)
}

func getTodayFinishedQuestDetail(l *lua.LState) int {
	userID := l.ToInt64(2)
	detail, box, err := box.TodayQuestDetail(userID)
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	if len(detail) != 3 {
		return LuaReturn(l, nil, errors.New("TodayFinishedQuestDetail 函数长度返回错误"))
	}

	boxNodes := []string{"", "鹿鸣珍宝", "鹤吟珍宝", "狮舞珍宝", "龙腾珍宝"}
	node := boxNodes[box.QuestType-1]
	if box.QuestType == boxprizes.BoxLevelS && box.LevelSBoxOpenTimes > 0 {
		node = boxNodes[boxprizes.BoxLevelS]
	}
	return LuaReturn(l, map[string]interface{}{
		"super_fan":  detail[0].Num * detail[0].Score,
		"noble":      detail[1].Num * detail[1].Score,
		"gift":       detail[2].Num * detail[2].Score,
		"gift_score": detail[2].Progress,
	}, map[string]interface{}{
		"nodes":     boxNodes[1:],
		"node":      node,
		"next_node": boxNodes[box.QuestType],
		"s_count":   box.LevelSBoxOpenTimes,
		"score":     box.Score,
		"miss":      box.RankUp,
	}, nil)
}

func getQuestScore(l *lua.LState) int {
	userID := l.ToInt64(2)
	score, err := annual.QuestScore(userID)
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	return LuaReturn(l, score, nil)
}

func getLucky(l *lua.LState) int {
	luckUser, err := luckyuser.FindLuckyUser(bson.M{"end_time": 0})
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	if luckUser == nil {
		return LuaReturn(l, nil, nil)
	}

	user, err := mowangskuser.FindByUserID(luckUser.UserID)
	if err != nil {
		return LuaReturn(l, nil, err)
	}
	if user == nil {
		return LuaReturn(l, nil, nil)
	}

	return LuaReturn(l, map[string]interface{}{
		"lucky": map[string]interface{}{
			"score":      luckUser.Score,
			"start_time": luckUser.StartTime,
		},
		"user": map[string]interface{}{
			"user_id":  user.ID,
			"username": user.Username,
			"iconurl":  user.IconURL,
		},
	}, nil)
}

func getTaskRankProgress(l *lua.LState) int {
	var progressResp map[string]interface{}
	err := service.MRPC.Call(userapi.URITaskRankProgress, "",
		map[string]int64{
			"event_id": l.ToInt64(2),
			"room_id":  l.ToInt64(3),
		}, &progressResp)
	if err != nil {
		return LuaReturn(l, nil, nil, err)
	}
	return LuaReturn(l, progressResp["collect"], progressResp["rank"], err)
}

// Runnable lua runnable interface
type Runnable interface {
	CacheKey() string
	SourceCode() (content string, err error)
}

// Exists 检查是否存在
func Exists(w Runnable) (bool, error) {
	f, err := getSource(w)
	return f != nil, err
}

func getSource(w Runnable) (*lua.FunctionProto, error) {
	i, ok := service.Cache5Min.Get(w.CacheKey())
	if ok {
		if i == nil {
			return nil, nil
		}
		proto, _ := i.(*lua.FunctionProto)
		return proto, nil
	}

	content, err := w.SourceCode()
	if err != nil {
		return nil, err
	}

	if content == "" {
		service.Cache5Min.Set(w.CacheKey(), nil, 0)
		return nil, nil
	}
	proto, err := initProto(content, w.CacheKey())
	if err != nil {
		return nil, err
	}
	service.Cache5Min.Set(w.CacheKey(), proto, 0)
	return proto, nil
}

// initProto 初始化 lua proto
func initProto(str, key string) (*lua.FunctionProto, error) {
	reader := strings.NewReader(str)
	chunk, err := parse.Parse(reader, key)
	if err != nil {
		return nil, err
	}
	proto, err := lua.CompileWithParNames(chunk, key, "ctx")
	if err != nil {
		return nil, err
	}
	return proto, nil
}

// RunWidget returns Lua engine
func RunWidget(c *handler.Context, w Runnable, params map[string]interface{}) (map[string]interface{}, error) {
	proto, err := getSource(w)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if proto == nil {
		return nil, nil
	}
	v, err := utils.ActionLuaRunAction(c, "run_widget", func(l *lua.LState, ctx map[string]interface{}) {
		tb := l.SetFuncs(l.NewTable(), methods)
		tb.RawSetString("params", luautil.ToLuaValue(l, params))
		ctx["widget"] = tb
		ctx["widget_func"] = l.NewFunctionFromProto(proto)
	})
	if err != nil {
		return nil, err
	}
	resp, ok := v.(map[string]interface{})
	if !ok {
		return nil, actionerrors.NewErrServerInternal(
			fmt.Errorf("lua resp not map[string]interface{} %+v", v),
			logger.Fields{"key": w.CacheKey()})
	}
	return resp, nil
}
