package engine

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine/engineevent"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()
	param := engineevent.NewWidgetParam(1, 2, 3, "a")
	ok, err := Exists(param)
	require.NoError(err)
	assert.False(ok)

	service.Cache5Min.Set(param.CacheKey(), nil, 0)
	ok, err = Exists(param)
	require.NoError(err)
	assert.False(ok)

	service.Cache5Min.Set(param.CacheKey(), &lua.FunctionProto{}, 0)
	ok, err = Exists(param)
	require.NoError(err)
	assert.True(ok)
}

func TestInitProto(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	proto, err := initProto(`
return ctx ~= nil, nil
`, "test_key")
	require.NoError(err)

	c := handler.NewTestContext("get", "", true, nil)
	v, err := utils.ActionLuaRunAction(c, "run_widget", func(l *lua.LState, ctx map[string]interface{}) {
		f := l.NewFunctionFromProto(proto)
		ctx["widget_func"] = f
	})
	require.NoError(err)
	assert.EqualValues(true, v)
}
