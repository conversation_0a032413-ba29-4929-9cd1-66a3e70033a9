package engineevent

import (
	"fmt"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/activities"
	"github.com/MiaoSiLa/live-service/service/keys"
)

// WidgetParam widget param
type WidgetParam struct {
	now        int64
	eventID    int64
	key        string
	widgetType int
}

// NewWidgetParam new widget param
func NewWidgetParam(now, eventID int64, widgetType int, keys ...string) *WidgetParam {
	param := &WidgetParam{now: now, eventID: eventID, widgetType: widgetType}
	if len(keys) > 0 {
		param.key = keys[0]
	}
	return param
}

// CacheKey widget cache key
func (w *WidgetParam) CacheKey() string {
	key := keys.KeyActivityWidgetLuaProto2.Format(w.eventID, w.widgetType)
	if w.key != "" {
		key += "/" + w.key
	}
	return key
}

// SourceCode get widget code
func (w *WidgetParam) SourceCode() (string, error) {
	filter := bson.M{
		"event_id": w.eventID,
		"type":     activities.TypeFaaSEventWidget,
		"order":    bson.M{"$gt": 0},
		"key":      strconv.Itoa(w.widgetType),
	}
	if w.key != "" {
		filter["key"] = fmt.Sprintf("%d/%s", w.widgetType, w.key)
	}

	activity, err := activities.FindOne(activities.SetValidTimeFilter(filter, w.now))
	if err != nil || activity == nil {
		return "", err
	}
	return activity.Content, nil
}
