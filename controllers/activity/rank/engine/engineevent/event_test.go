package engineevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/activities"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestNewWidgetParam(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(&WidgetParam{
		now:        1,
		eventID:    2,
		key:        "a",
		widgetType: 3,
	}, NewWidgetParam(1, 2, 3, "a"))
}

func TestCacheKey(t *testing.T) {
	assert := assert.New(t)

	key := NewWidgetParam(1, 2, 3, "a").Cache<PERSON>ey()
	assert.Equal("activity/widget/2/3/a", key)
}

func TestSourceCode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Unix()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	activities.Collection().FindOneAndUpdate(ctx,
		bson.M{
			"type":     activities.TypeFaaSEventWidget,
			"event_id": 1111,
			"order":    1,
			"key":      "1/end",
		},
		bson.M{"$set": bson.M{
			"start_time": now,
			"content":    `aaa`}},
		options.FindOneAndUpdate().SetUpsert(true),
	)

	p := WidgetParam{
		now:        now,
		eventID:    1111,
		key:        "end",
		widgetType: 1,
	}
	s, err := p.SourceCode()
	require.NoError(err)
	assert.EqualValues("aaa", s)
}
