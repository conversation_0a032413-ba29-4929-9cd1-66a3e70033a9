package engineliveshow

import (
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/activities"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// WidgetParam widget param
type WidgetParam struct {
	liveType   int
	widgetType int
}

// NewWidgetParam new widget param
func NewWidgetParam(liveType int, widgetType int) *WidgetParam {
	param := &WidgetParam{liveType: liveType, widgetType: widgetType}
	return param
}

// CacheKey widget cache key
func (w *WidgetParam) CacheKey() string {
	key := keys.KeyActivityWidgetLuaProto2.Format(w.liveType, w.widgetType)
	return key
}

// SourceCode get widget code
func (w *WidgetParam) SourceCode() (string, error) {
	filter := bson.M{
		"live_type": w.liveType,
		"type":      activities.TypeFaaSLiveShowWidget,
		"order":     bson.M{"$gt": 0},
		"key":       strconv.Itoa(w.widgetType),
	}

	activity, err := activities.FindOne(activities.SetValidTimeFilter(filter, goutil.TimeNow().Unix()))
	if err != nil || activity == nil {
		return "", err
	}
	return activity.Content, nil
}
