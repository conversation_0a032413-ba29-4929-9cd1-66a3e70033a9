package rank

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Event193CollectGifts 193 收集礼物
func Event193CollectGifts() []int64 {
	return []int64{90089, 90090, 90091, 90092, 90093, 90094, 90095, 90096, 90097}
}

func event193(userID, giftID int64, expireTime time.Time) {
	// 共收集 9 种礼物（礼物 ID：90089 至 90097）
	gifts := Event193CollectGifts()
	if !goutil.Includes(len(gifts), func(i int) bool {
		return gifts[i] == giftID
	}) {
		return
	}

	key := keys.KeyActivityUserCollect2.Format(usersrank.EventIDCollectFlowers, userID)
	pipe := service.Redis.TxPipeline()
	pipe.SAdd(key, giftID)
	serviceredis.ExpireAt(pipe, key, expireTime)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
