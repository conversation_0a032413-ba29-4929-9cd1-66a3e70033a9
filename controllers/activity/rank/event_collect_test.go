package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCollectRankGiftKey(t *testing.T) {
	assert := assert.New(t)

	ce := collectRank{rank: rank{giftParam: &SyncGiftParam{GiftID: 1}}}
	assert.Equal("gift_1", ce.giftKey())
}

func TestCollectRankGetCollectMaxNum(t *testing.T) {
	assert := assert.New(t)

	ce := collectRank{
		rank: rank{
			Rank: activity.Rank{
				Collect: &activity.Collect{
					Gifts: []*activity.CollectGift{
						{GiftID: 1, MaxNum: goutil.NewInt64(10)},
						{GiftID: 2, MaxNum: nil},
					},
				},
			},
			giftParam: &SyncGiftParam{
				GiftID: 1,
			},
		},
	}
	assert.EqualValues(10, ce.getCollectMaxNum())

	ce.giftParam.GiftID = 2
	assert.Zero(ce.getCollectMaxNum())

	ce.giftParam.GiftID = 3
	assert.Zero(ce.getCollectMaxNum())
}

func TestCollectRankFeedRank(t *testing.T) {
	ce := collectRank{
		rank: rank{
			eventID: 9999988,
			Rank: activity.Rank{
				Collect: &activity.Collect{
					Gifts: []*activity.CollectGift{
						{GiftID: 1, MaxNum: goutil.NewInt64(10)},
						{GiftID: 2, MaxNum: nil},
					},
				},
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteMany(ctx, bson.M{"event_id": ce.eventID})
	require.NoError(t, err)

	newSyncGiftParam := func(giftID, giftNum, giftPrice int64) SyncGiftParam {
		return SyncGiftParam{
			SyncCommonParam: &SyncCommonParam{
				RoomID:     123,
				FromUserID: 1,
				ToUserID:   2,
			},
			GiftID:    giftID,
			GiftNum:   int(giftNum),
			GiftPrice: giftPrice,
		}
	}
	tests := []struct {
		name       string
		gift       SyncGiftParam
		wantBefore int64
		wantAfter  int64
		wantNum    int64
	}{
		{
			"第一次添加礼物",
			newSyncGiftParam(1, 5, 3),
			0, 15, 5,
		},
		{
			"第二次添加礼物",
			newSyncGiftParam(1, 3, 3),
			15, 24, 8,
		},
		{
			"第三次添加礼物",
			newSyncGiftParam(1, 5, 3),
			24, 30, 10,
		},
		{
			"第四次添加礼物",
			newSyncGiftParam(1, 5, 3),
			30, 30, 10,
		},
		{
			"第五次添加礼物",
			newSyncGiftParam(2, 5, 3),
			30, 45, 5,
		},
		{
			"第六次添加礼物",
			newSyncGiftParam(2, 10, 3),
			45, 75, 15,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert := assert.New(t)
			require := require.New(t)

			ce.giftParam = &tt.gift
			gotBefore, gotAfter, err := ce.feedRank(nil)
			require.NoError(err)
			assert.EqualValues(tt.wantBefore, gotBefore)
			assert.EqualValues(tt.wantAfter, gotAfter)
			res, err := liveactivity.FindOne(
				bson.M{
					"event_id":   ce.eventID,
					"room_id":    ce.giftParam.RoomID,
					"creator_id": ce.giftParam.ToUserID,
				},
			)
			require.NoError(err)
			require.NotNil(res)
			assert.EqualValues(tt.wantNum, ce.achievementNum(res))
		})
	}
}
