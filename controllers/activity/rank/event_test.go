package rank

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestOnGoingEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyOnlineGifts0.Format()
	require.NoError(service.Redis.Del(key).Err())

	list, err := ongoingEvent()
	require.NoError(err)
	require.Greater(len(list), 2)
	require.Equal(CollectTypePoint, list[len(list)-1].collectType())
	p, ok := list[len(list)-1].(*syncEvent)
	require.True(ok)
	assert.Equal(
		&activity.ExtendedFields{
			RankStartTime: 1,
			RankEndTime:   3376656000,
			Application:   activity.ApplicationAllowList,
			NobleExtra:    util.NewInt(1),
			Collect: &activity.Collect{
				Type: CollectTypePoint,
				Gifts: []*activity.CollectGift{
					{
						GiftID: 7777,
						MaxNum: util.NewInt64(0),
					},
					{
						GiftID: 8888,
						MaxNum: util.NewInt64(0),
					},
					{
						GiftID: 9999,
						MaxNum: util.NewInt64(0),
					},
				},
			},
		}, p.ExtendedFields,
	)
}

func TestEventToActivity(t *testing.T) {
	assert := assert.New(t)

	events := []event{
		{
			ExtendedFields: &activity.ExtendedFields{
				Collect: &activity.Collect{Type: CollectTypePoint},
			},
		},
		{
			ExtendedFields: &activity.ExtendedFields{
				Collect: &activity.Collect{Type: CollectTypeNum},
			},
		},
	}

	activitys := eventToActivity(events)
	assert.IsType(&syncEvent{}, activitys[0])
	assert.IsType(&syncEvent{}, activitys[1])
}

func TestCheckRankTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	r := rank{
		Rank: activity.Rank{RankStartTime: now, RankEndTime: now + 100},
		timeNow: func() time.Time {
			return goutil.TimeNow()
		},
	}
	assert.True(r.checkRankTime())

	r = rank{
		Rank: activity.Rank{RankStartTime: now + 100, RankEndTime: now + 1000},
		timeNow: func() time.Time {
			return goutil.TimeNow()
		},
	}
	assert.False(r.checkRankTime())
}

func TestCheckGift(t *testing.T) {
	assert := assert.New(t)

	r := rank{
		Rank: activity.Rank{
			Collect: &activity.Collect{Gifts: []*activity.CollectGift{
				{GiftID: 1},
				{GiftID: 2},
			}},
		},
	}
	assert.True(r.checkGift(1))
	assert.True(r.checkGift(2))
	assert.False(r.checkGift(3))

	r.Collect = &activity.Collect{}
	assert.True(r.checkGift(1))
}

func TestCheckApplication(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		userID  int64 = 1234
		eventID int64 = 99999
	)
	key := activity.KeyUsersApplication(eventID)
	require.NoError(service.Redis.SAdd(key, userID).Err())
	defer service.Redis.Del(key)

	e := rank{
		eventID: eventID,
		Rank: activity.Rank{
			Application: activity.ApplicationNone,
			Collect: &activity.Collect{
				Type: CollectTypePoint,
			},
		},
		giftParam: &SyncGiftParam{
			SyncCommonParam: &SyncCommonParam{
				GuildID: 1,
			},
		},
	}
	assert.True(e.checkApplication(userID))

	e.Application = activity.ApplicationAllowList
	assert.True(e.checkApplication(userID))

	e.Application = activity.ApplicationBlockList
	assert.False(e.checkApplication(userID))
}

func TestRankCheckCatalog(t *testing.T) {
	assert := assert.New(t)

	r := rank{
		giftParam: &SyncGiftParam{
			SyncCommonParam: &SyncCommonParam{
				ActivityCatalogID: 2,
			},
		},
		Rank: activity.Rank{
			ActivityCatalogIDs: nil,
		},
	}
	assert.True(r.checkCatalog())

	r.ActivityCatalogIDs = []int64{1, 3}
	assert.False(r.checkCatalog())

	r.ActivityCatalogIDs = []int64{1, 2}
	assert.True(r.checkCatalog())

	r.ActivityCatalogIDs = []int64{}
	assert.True(r.checkCatalog())
}
