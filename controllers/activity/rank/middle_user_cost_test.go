package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMiddlewareUserCost(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := rank{
		goodsParam: &SyncGoodsParam{GoodsPrice: 200},
	}
	p := addRankParam{score: 100}
	require.NoError(middlewareUserCost(r, nil, &p))
	assert.EqualValues(100, p.score)

	r = rank{
		giftParam: &SyncGiftParam{GiftPrice: 200},
	}
	require.NoError(middlewareUserCost(r, nil, &p))
	assert.EqualValues(100, p.score)

	r = rank{
		giftParam: &SyncGiftParam{
			GiftPrice:      200,
			LuckyGiftID:    1,
			LuckyGiftPrice: 10,
			LuckyGiftNum:   30,
		},
	}
	assert.NoError(middlewareUserCost(r, nil, &p))
	assert.EqualValues(300, p.score)
}
