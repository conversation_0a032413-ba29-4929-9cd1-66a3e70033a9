package rank

import (
	"encoding/json"
	"strconv"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
)

type pointLimitParams struct {
	Limit int64 `json:"limit"`
}

// middlewarePointLimit 限制免费礼物积分获取上限
func middlewarePointLimit(r rank, middleParam *activity.RankMiddlewareParam, addParam *addRankParam) error {
	if r.giftParam == nil {
		return nil
	}

	if r.giftParam.GiftPrice != 0 {
		return nil
	}

	var limit pointLimitParams
	err := json.Unmarshal(middleParam.Params, &limit)
	if err != nil {
		return err
	}

	key := activity.KeyFreePoint(r.eventID, r.Key)
	pipe := service.Redis.TxPipeline()
	// key 对应的数值会持续增加
	cmd := pipe.HIncrBy(key,
		strconv.FormatInt(addParam.toUserID, 10),
		int64(addParam.score),
	)
	serviceredis.ExpireAt(pipe, key, r.expireTime)
	_, err = pipe.Exec()
	if err != nil {
		return err
	}

	res := cmd.Val()
	overflow := res - limit.Limit
	if overflow <= 0 {
		return nil
	}

	// 如果有部分超过限制则添加未超过限制的积分
	if overflow < int64(addParam.score) {
		addParam.score -= float64(overflow)
		return nil
	}

	return errNoFeed
}
