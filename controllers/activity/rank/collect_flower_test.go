package rank

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestEvent193(t *testing.T) {
	userID := int64(12)
	key := keys.KeyActivityUserCollect2.Format(usersrank.EventIDCollectFlowers, userID)
	require.NoError(t, service.Redis.Del(key).Err())

	expireTime := goutil.TimeNow().Add(time.Minute)
	tests := []struct {
		name   string
		giftID int64
		count  int64
	}{
		{"", 90088, 0},
		{"", 90089, 1},
		{"", 90090, 2},
		{"", 90091, 3},
		{"", 90092, 4},
		{"", 90092, 4},
		{"", 90093, 5},
		{"", 90094, 6},
		{"", 90095, 7},
		{"", 90096, 8},
		{"", 90097, 9},
		{"", 90097, 9},
		{"", 90098, 9},
		{"", 90099, 9},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event193(userID, tt.giftID, expireTime)
			count, err := service.Redis.SCard(key).Result()
			require.NoError(t, err)
			assert.Equal(t, tt.count, count)
		})
	}
}
