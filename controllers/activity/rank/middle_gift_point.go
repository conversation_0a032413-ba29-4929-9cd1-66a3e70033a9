package rank

import (
	"encoding/json"

	"github.com/MiaoSiLa/live-service/models/activity"
)

type giftPointParams struct {
	GiftIDs []int64 `json:"gift_ids"`
}

func middleCollectGiftPoint(r rank, middleParam *activity.RankMiddlewareParam, addParam *addRankParam) error {
	if r.giftParam == nil {
		return nil
	}

	var param giftPointParams
	if err := json.Unmarshal(middleParam.Params, &param); err != nil {
		return err
	}

	for _, gID := range param.GiftIDs {
		if gID == r.giftParam.GiftID {
			addParam.score += float64(r.giftParam.GiftPoint * int64(r.giftParam.GiftNum))
			return nil
		}
	}
	return nil
}
