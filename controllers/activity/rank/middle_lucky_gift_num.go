package rank

import (
	"errors"

	"github.com/MiaoSiLa/live-service/models/activity"
)

// middlewareCollectLuckyGiftNum 设置添加榜单积分为随机礼物数量
// lucky gift num 用于统计主播收到的用户送出的随机礼物数量
func middlewareCollectLuckyGiftNum(r rank, middleParam *activity.RankMiddlewareParam, addParam *addRankParam) error {
	if r.giftParam == nil {
		return errors.New("giftParam is nil")
	}

	if !r.giftParam.IsLuckyGift() {
		return errors.New("gift must be luck gift")
	}

	addParam.score = float64(r.giftParam.LuckyGiftNum)
	return nil
}
