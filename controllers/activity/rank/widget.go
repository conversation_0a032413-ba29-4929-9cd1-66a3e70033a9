package rank

import (
	"fmt"

	"golang.org/x/sync/singleflight"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine/engineevent"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/widget"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// attr type
const (
	attrNotUseSingleFlight = iota + 1
)

var (
	singleFlight singleflight.Group
)

type widgetParam struct {
	EventID int64 `json:"event_id"`
	RoomID  int64 `json:"room_id"`

	c     *handler.Context
	event *event
	r     *room.Room
}

// ActionWidget 活动小窗
/**
 * @api {get} /api/v2/activity/widget 活动小窗
 * @apiVersion 0.1.0
 * @apiGroup activity
 *
 * @apiParam {number=168,169,170} event_id 活动 ID 目前支持 168, 169, 170, 175 活动
 * @apiParam {Number} [room_id] 房间号，主播从活动页访问可以不传房间号，返回主播房间的数据
 * @apiParam {String} [key] 不传查询默认赛程小窗，传入查询指定赛程小窗
 * @apiParam {Number} [attr] 位 1：不使用缓存
 *
 * @apiSuccessExample 活动: 168
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1, // 活动状态 0：未开始，1：进行中，2：活动结束，3：活动已达成目标，如奖池瓜分完毕、奖励领取完毕，4：无活动资格
 *       "ts": 1, // 当前时间戳, 秒级
 *       "refresh_duration": 200000, // 刷新时间，没有该字段时不刷新。毫秒级
 *       "nodes": [25000, 105000, 305000], // 进度条
 *       "score": 111, // 当前积分
 *       "pool": 9999 // 奖池剩余钻石
 *     }
 *   }
 *
 * @apiSuccessExample 活动: 169
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1, // 活动状态 0：未开始，1：进行中，2：活动结束，3：活动已达成目标，如奖池瓜分完毕、奖励领取完毕，4：无活动资格
 *       "ts": 1, // 当前时间戳, 秒级
 *       "refresh_duration": 200000, // 刷新时间，没有该字段时不刷新。毫秒级
 *       "key": "1", // 当前进行的赛程，结束后不返回
 *       "id": 1, // 用户 ID
 *       "name": "aaa", // 用户昵称
 *       "iconurl": "url", // 用户头像
 *       "score": 111, // 当前积分
 *       "rank": 111, // 当前排名
 *       "rank_up": 111, // 距离晋级或距离上一名的分数，根据 promoted 判断
 *       "promoted": false, // 是否晋级
 *       "end_time": 11111111111, // 本场结束时间，秒级时间戳
 *       "user_list": [{"name": "aaa", "iconurl": "url"}] // 上榜名单，仅当 status 为 3 时返回
 *     }
 *   }
 *
 * @apiSuccessExample 活动: 170
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 1, // 活动状态 0：未开始，1：进行中，2：活动结束，3：活动已达成目标，如奖池瓜分完毕、奖励领取完毕，4：无活动资格
 *       "ts": 1, // 当前时间戳, 秒级
 *       "refresh_duration": 200000, // 刷新时间，没有该字段时不刷新。毫秒级
 *       "key": "1", // 当前进行的赛程，结束后不返回
 *       "id": 1, // 公会 ID
 *       "name": "aaa", // 公会名称
 *       "score": 111, // 当前积分
 *       "rank": 111, // 当前排名
 *       "user_list": [{"name": "aaa", "iconurl": "url"}, {"name": "aaa", "iconurl": "url"}] // 上榜名单，仅当 status 为 3 时返回
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionWidget(c *handler.Context) (handler.ActionResponse, error) {
	attr, err := c.GetDefaultParamInt("attr", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	eventID, _ := c.GetParamInt64("event_id")
	roomID, _ := c.GetParamInt64("room_id")
	if goutil.BitMask(attr).IsSet(attrNotUseSingleFlight) {
		return Widget(c, eventID, roomID)
	}

	// 前端通过 URL 来控制是否使用缓存
	key := c.C.Request.URL.RawQuery

	// 同一个直播间内的用户看到的小窗都是相同的
	// 缓存时间为一个请求的时间
	resp, err, _ := singleFlight.Do(key, func() (interface{}, error) {
		defer singleFlight.Forget(key)

		return Widget(c, eventID, roomID)
	})
	return resp, err
}

// Widget widget resp
func Widget(c *handler.Context, eventID, roomID int64) (handler.ActionResponse, error) {
	param := widgetParam{
		c:       c,
		EventID: eventID,
		RoomID:  roomID,
	}

	err := param.checkEvent()
	if err != nil {
		return nil, err
	}

	resp := param.checkActivityTime()
	if resp != nil {
		return resp, nil
	}

	err = param.checkRoom()
	if err != nil {
		return nil, err
	}

	return param.buildResp()
}

func (param *widgetParam) checkEvent() (err error) {
	if param.EventID <= 0 {
		return actionerrors.ErrParams
	}

	param.event, err = newEvent(param.EventID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.event == nil {
		return actionerrors.ErrParamsMsg("活动不存在")
	}
	return nil
}

// checkActivityTime 返回 nil 代表需要返回 luaResp, 不为 nil 则直接向前端返回
func (param *widgetParam) checkActivityTime() *widget.Widget {
	now := param.event.ExtendedFields.TimeNow().Unix()
	w := widget.NewWidget(now)

	if param.event.ExtendedFields.RankStartTime > now {
		// 如果活动还未开始，要是确定没有预热代码，就告诉前端活动开始的时候刷新
		if param.event.ExtendedFields.WarmUpStartTime == 0 {
			w.BuildNotStartWidget(param.event.ExtendedFields.RankStartTime)
			return w
		}
		// 有预热代码，且没到预热开始时间，就告诉前端预热开始时间刷新
		if now < param.event.ExtendedFields.WarmUpStartTime {
			w.BuildNotStartWidget(param.event.ExtendedFields.WarmUpStartTime)
			return w
		}
	}

	if param.event.ExtendedFields.RankEndTime <= now {
		w.BuildEndingWidget()
		return w
	}

	return nil
}

func (param *widgetParam) checkRoom() error {
	var err error
	if param.RoomID == 0 {
		if param.c.UserID() == 0 {
			return actionerrors.ErrUnloggedUser
		}
		param.RoomID, err = room.FindRoomID(param.c.UserID())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if param.RoomID == 0 {
			return actionerrors.ErrCannotFindRoom
		}
	}
	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return actionerrors.ErrCannotFindRoom
	}

	return nil
}

func (param *widgetParam) buildResp() (interface{}, error) {
	return param.luaResp()
}

// widgetLuaResp lua resp
// TODO: 之后考虑用更好的方法
type widgetLuaResp struct {
	*widget.Widget
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

func (param *widgetParam) buildLuaResp(r *activity.Rank, widgetType int) (*widgetLuaResp, error) {
	var (
		rankKey string
		endTime = param.event.ExtendedFields.RankEndTime
		now     = param.event.ExtendedFields.TimeNow().Unix()
	)
	if r != nil {
		rankKey = r.Key
		endTime = r.GetWidgetEndTime()
	}

	key := activity.KeyRank(param.EventID, rankKey)
	luaResp, err := engine.RunWidget(param.c,
		engineevent.NewWidgetParam(param.event.ExtendedFields.TimeNow().Unix(), param.EventID, widgetType, rankKey),
		map[string]interface{}{
			"creator_id": param.r.CreatorID,
			"key":        key,
			"room":       param.r,
			"now":        now,
		})
	if err != nil {
		return nil, err
	}

	var resp = &widgetLuaResp{
		Widget:   widget.NewWidget(now),
		Metadata: luaResp,
	}

	switch widgetType {
	case engine.WidgetTypeInBan:
		// 目前 InBan 用于直接晋级情况
		resp.Widget.BuildCompletedWidget(endTime)
	case engine.WidgetTypeNormal:
		resp.Widget.BuildOnGoingWidget(endTime)
	case engine.WidgetTypeEnding:
		resp.Widget.BuildEndingWidget()
	case engine.WidgetTypeNotQualified:
		if param.EventID == usersrank.Event215 {
			// NOTICE: 215 活动未晋级小窗 2022-10-06 00:00:00 刷新
			resp.Widget.BuildNotQualifiedWidget(1664985600) // 2022-10-06 00:00:00
		} else {
			resp.Widget.BuildNotQualifiedWidget(endTime)
		}
	case engine.WidgetTypeWarmUp:
		resp.Widget.BuildWarmUpWidget(param.event.ExtendedFields.RankStartTime)
	}

	return resp, nil
}

func (param *widgetParam) luaResp() (*widgetLuaResp, error) {
	if len(param.event.ExtendedFields.Ranks) == 0 {
		// TODO: 小窗支持没有 rank 的活动
		return nil, actionerrors.ErrParams
	}

	now := param.event.ExtendedFields.TimeNow().Unix()
	// 判断是否处于活动未开始时间，如果处于，则返回预热小窗
	if param.event.ExtendedFields.RankStartTime > now {
		// 检查有无 lua 代码，已经保证 now > WarmUpStartTime
		p := engineevent.NewWidgetParam(now, param.EventID, engine.WidgetTypeWarmUp)
		ok, err := engine.Exists(p)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return nil, actionerrors.NewErrServerInternal(fmt.Errorf("can not find warm up widget"), nil)
		}
		return param.buildLuaResp(nil, engine.WidgetTypeWarmUp)
	}

	ranks := param.event.ExtendedFields.RanksWithOngoingWidget()
	if len(ranks) == 0 {
		// 所有榜单结束
		return param.buildLuaResp(nil, engine.WidgetTypeEnding)
	}
	for _, r := range ranks {
		if !r.CheckCatalog(param.r.ActivityCatalogID) {
			continue
		}

		if checkApplication(rank{
			Rank:    r,
			eventID: param.EventID,
		}, param.r.CreatorID, param.r.GuildID) {
			return param.buildLuaResp(&r, engine.WidgetTypeNormal)
		}
		// 检查有无淘汰榜单，有返回，没有继续找有资格的榜单
		p := engineevent.NewWidgetParam(now, param.EventID, engine.WidgetTypeInBan, r.Key)
		ok, err := engine.Exists(p)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			continue
		}
		return param.buildLuaResp(&r, engine.WidgetTypeInBan)
	}

	// 被淘汰
	return param.buildLuaResp(nil, engine.WidgetTypeNotQualified)
}
