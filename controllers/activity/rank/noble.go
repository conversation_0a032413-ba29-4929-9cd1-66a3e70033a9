package rank

import (
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SyncNobleParam sync noble param
type SyncNobleParam struct {
	*SyncCommonParam

	IsNew              bool
	Type               int
	NoblePrice         int64 // 单位：钻
	NobleLevel         int   // 开通的贵族等级
	PreviousNobleLevel int   // 开通前的贵族等级，不包含贵族保护期和体验贵族
}

// SetNoble set noble
func (param *SyncNobleParam) SetNoble(isNew bool, nobleType int, price int64, nobleLevel, previousNobleLevel int) *SyncNobleParam {
	param.IsNew = isNew
	param.Type = nobleType
	param.NoblePrice = price
	param.NobleLevel = nobleLevel
	param.PreviousNobleLevel = previousNobleLevel
	return param
}

// SendLiveActivity send live activity
func (param *SyncNobleParam) SendLiveActivity(ctx mrpc.UserContext) {
	send := param.newRankRevenueParam()
	send.Noble = &userapi.RankRevenueNoble{
		IsNew:              param.IsNew,
		Type:               param.Type,
		NoblePrice:         param.NoblePrice,
		NobleLevel:         param.NobleLevel,
		PreviousNobleLevel: param.PreviousNobleLevel,
	}
	err := send.Send(ctx)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
