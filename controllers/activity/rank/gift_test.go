package rank

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewSyncGiftParam(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(
		&SyncGiftParam{SyncCommonParam: &SyncCommonParam{RoomID: 1, FromUserID: 2, ToUserID: 3}},
		NewSyncParam(1, 2, 3).SetGift(nil, 0))
}

func TestSyncGiftParamSetGift(t *testing.T) {
	assert := assert.New(t)

	p := SyncGiftParam{}
	assert.Equal(&SyncGiftParam{
		GiftID:    1,
		GiftType:  2,
		GiftNum:   3,
		GiftPrice: 4,
		GiftPoint: 5,
	}, p.SetGift(&gift.Gift{GiftID: 1, Type: 2, Price: 4, Point: 5}, 3))
}

func TestSyncGiftParamSetLuckyGift(t *testing.T) {
	assert := assert.New(t)

	p := SyncGiftParam{}
	assert.Equal(&SyncGiftParam{
		LuckyGiftID:    1,
		LuckyGiftPrice: 4,
		LuckyGiftNum:   3,
	}, p.SetLuckyGift(&gift.Gift{GiftID: 1, Type: 2, Price: 4, Point: 5}, 3))
}

func TestSyncGiftPointGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 999999
	)
	roomKey := roomsrank.ActivityKey(roomID, strconv.FormatInt(eventID, 10))
	activityKey := usersrank.ActivityKey(strconv.FormatInt(eventID, 10))
	applicationKey := activity.KeyUsersApplication(eventID)
	require.NoError(service.Redis.Del(roomKey, activityKey, applicationKey, keys.KeyActivityOngoingLiveRankEvent0.Format()).Err())
	defer service.Redis.Del(roomKey, activityKey, applicationKey, keys.KeyActivityOngoingLiveRankEvent0.Format())

	param := NewSyncParam(roomID, fromUserID1, toUserID).SetGift(&gift.Gift{
		GiftID: 1,
		Price:  100,
		Point:  100,
	}, 10)

	// gift
	// 没在礼物列表
	param.AddRankPoint()
	_, err := service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.Equal(redis.Nil, err)
	_, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.Equal(redis.Nil, err)

	// 没在白名单
	// application
	param.GiftID = 7777
	param.AddRankPoint()
	_, err = service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.Equal(redis.Nil, err)
	_, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.Equal(redis.Nil, err)

	// 添加白名单
	require.NoError(service.Redis.SAdd(applicationKey, toUserID).Err())

	// 送礼成功
	param.AddRankPoint()
	score, err := service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer goutil.SetTimeNow(nil)

	// not in rank time
	param.AddRankPoint()
	score, err = service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)
}

func TestSyncGiftPointGiftManyRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		eventID     int64 = 999997
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(3376684800, 0)
	})
	defer goutil.SetTimeNow(nil)
	roomKey := roomsrank.ActivityKey(roomID, strconv.FormatInt(eventID, 10))
	activityKey := usersrank.ActivityKey(strconv.FormatInt(eventID, 10))
	applicationKey := activity.KeyUsersApplication(eventID, "1")
	require.NoError(service.Redis.Del(
		roomKey,
		applicationKey,
		keys.KeyActivityOngoingLiveRankEvent0.Format(),
		activityKey+"/1",
		activityKey+"/2",
		activityKey+"/3",
		activityKey+"/4",
		activityKey+"/5",
	).Err())
	defer service.Redis.Del(
		roomKey,
		applicationKey,
		keys.KeyActivityOngoingLiveRankEvent0.Format(),
		activityKey+"/1",
		activityKey+"/2",
		activityKey+"/3",
		activityKey+"/4",
		activityKey+"/5",
	)

	m := make([]int64, 0, 10)
	// 获取 10 个 用户
	for i := 10; i > 0; i-- {
		m = append(m, int64(i))
	}

	sendGift := func() {
		param := NewSyncParam(roomID, fromUserID1, 0).SetGift(&gift.Gift{
			GiftID: 1,
			Price:  1,
			Point:  1,
		}, 0)
		for _, i := range m {
			param.ToUserID = i
			param.GiftNum = int(i * 10)
			param.AddRankPoint()
		}
	}

	// 将 1 个用户添加到黑名单
	require.NoError(service.Redis.SAdd(applicationKey, 1).Err())

	sendGift()
	list, err := service.Redis.ZRevRangeWithScores(activityKey+"/1", 0, -1).Result()
	require.NoError(err)
	require.Len(list, 9)
	for i, v := range list {
		assert.EqualValues((10-i)*10, v.Score)
	}

	// 分赛道 10 - 7 2 赛道，6 - 3 3 赛道，2 淘汰
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(3376684900, 0)
	})
	sendGift()
	list, err = service.Redis.ZRevRangeWithScores(activityKey+"/2", 0, -1).Result()
	require.NoError(err)
	require.Len(list, 4)
	for i, v := range list {
		assert.EqualValues((10-i)*10, v.Score)
	}

	list, err = service.Redis.ZRevRangeWithScores(activityKey+"/3", 0, -1).Result()
	require.NoError(err)
	require.Len(list, 4)
	for i, v := range list {
		assert.EqualValues((6-i)*10, v.Score)
	}

	// 分赛道 10 - 9 4 赛道，6 - 5 5 赛道，8 7 4 3 淘汰
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(3376685000, 0)
	})
	sendGift()
	list, err = service.Redis.ZRevRangeWithScores(activityKey+"/4", 0, -1).Result()
	require.NoError(err)
	require.Len(list, 2)
	for i, v := range list {
		assert.EqualValues((10-i)*10, v.Score)
	}

	list, err = service.Redis.ZRevRangeWithScores(activityKey+"/5", 0, -1).Result()
	require.NoError(err)
	require.Len(list, 2)
	for i, v := range list {
		assert.EqualValues((6-i)*10, v.Score)
	}
}

func TestSyncGiftPointGiftGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 999998
		guildID     int64 = 1
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1633017600, 0)
	})
	defer goutil.SetTimeNow(nil)
	roomKey := activity.KeyGuildCreatorRank(guildID, strconv.FormatInt(eventID, 10))
	activityKey := activity.KeyGuildRank(strconv.FormatInt(eventID, 10))
	require.NoError(service.Redis.Del(roomKey, activityKey).Err())
	defer service.Redis.Del(roomKey, activityKey)

	NewSyncParam(roomID, fromUserID1, toUserID).
		SetGuildID(guildID).
		SetGift(&gift.Gift{
			GiftID: 1,
			Price:  100,
			Point:  100,
		}, 10).
		AddRankPoint()

	score, err := service.Redis.ZScore(roomKey, strconv.FormatInt(toUserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(guildID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	// ttl
	ttl, err := service.Redis.TTL(activityKey).Result()
	require.NoError(err)
	assert.Less(time.Hour+15*24*time.Hour-5*time.Second, ttl)
}
