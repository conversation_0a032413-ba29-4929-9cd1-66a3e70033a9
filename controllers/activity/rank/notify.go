package rank

import (
	"html"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/activity/rank/rankuserinfo"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// BuildEffectURL 生成特效 URL
func BuildEffectURL(base string, exts []string) string {
	effects := make([]string, len(exts))
	for i, ext := range exts {
		effects[i] = storage.ParseSchemeURL(base + ext)
	}

	return strings.Join(effects, ";")
}

// NotifyConfig 通知配置
type NotifyConfig struct {
	key       string
	userID    int64
	rank      int64
	bubbleID  int64
	effect    *liveim.Effect
	message   string
	formatMap map[string]string
}

// newBroadcastElems 通过单条飘屏生成兼容的房间内特效+全站飘屏
func newBroadcastElems(roomID int64, payload *notifymessages.General) []*userapi.BroadcastElem {
	elems := make([]*userapi.BroadcastElem, 0, 2)
	if payload.Effect != nil && payload.EffectShow == notifymessages.EffectShowRoomJump {
		elems = append(elems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  roomID,
			Payload: liveim.NewRoomEffect(roomID, *payload.Effect),
		})
	}

	elems = append(elems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  roomID,
		Payload: payload,
	})

	return elems
}

// notifyCreatorRankElems 生成主播榜全站飘屏参数
func notifyCreatorRankElems(notifies []*NotifyConfig) ([]*userapi.BroadcastElem, error) {
	list := make([]*userapi.BroadcastElem, 0, 2*len(notifies))
	for _, item := range notifies {
		var (
			memberID = item.userID
			err      error
		)
		if memberID == 0 && item.key != "" {
			memberID, err = rankuserinfo.MemberID(item.key, item.rank)
			if err != nil {
				return nil, err
			}
		}

		r, err := room.FindOne(
			bson.M{"creator_id": memberID},
			&room.FindOptions{DisableAll: true},
		)
		if err != nil {
			return nil, err
		}
		if r == nil {
			return nil, err
		}

		if item.formatMap == nil {
			item.formatMap = NewFormatParam()
		}
		item.formatMap["creator_username"] = html.EscapeString(r.CreatorUsername)
		b, err := bubble.FindSimple(item.bubbleID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if b != nil {
			b.AppendFormatParams(item.formatMap)
		}

		general := notifymessages.NewGeneral(r.RoomID, goutil.FormatMessage(item.message, item.formatMap), b)
		if item.effect != nil {
			general.Effect = item.effect
			general.EffectShow = notifymessages.EffectShowRoomJump
		}
		elems := newBroadcastElems(r.RoomID, general)
		list = append(list, elems...)
	}

	return list, nil
}

// NewFormatParam 新建格式化参数
func NewFormatParam() map[string]string {
	return map[string]string{
		"normal_color":    "#FFFFFF",
		"highlight_color": "#FFFFFF",
	}
}

// SendNotify 发送飘屏
func SendNotify(configs ...*NotifyConfig) {
	list, err := notifyCreatorRankElems(configs)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(list) == 0 {
		return
	}
	if err := userapi.BroadcastMany(list); err != nil {
		logger.Error(err)
		// PASS
	}
}
