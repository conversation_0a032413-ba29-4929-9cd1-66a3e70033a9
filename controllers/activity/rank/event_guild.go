package rank

import (
	"strconv"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

type guildRank struct {
	rank

	guildID int64
}

func (gr *guildRank) checkApplication(userID int64) bool {
	param := gr.getCommonParam()
	if param == nil || param.GuildID == 0 {
		return false
	}
	gr.guildID = param.GuildID
	return checkApplication(gr.rank, param.ToUserID, param.GuildID)
}

// checkInGuildAllowList
// 通过判断主播在 redis key 中且主播的公会 id 为当前的公会 id
// 来确定主播是否有资格，以避免主播转会后在新的公会里也有资格
func checkInGuildAllowList(eventID int64, key string, guildID, userID int64) bool {
	// 目前没有公会成员黑名单
	key = activity.KeyGuildUsersApplication(eventID, key)
	gID, err := service.Redis.HGet(key, strconv.FormatInt(userID, 10)).Int64()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		return false
	}
	return gID == guildID
}

func (gr *guildRank) beforeFeed() (*addRankParam, error) {
	return gr.buildAddRankParam(), nil
}

func (gr guildRank) feedRank(param *addRankParam) (int64, int64, error) {
	if param == nil || param.score == 0 {
		return 0, 0, nil
	}

	key := gr.formatEventKey()
	roomKey := activity.KeyGuildCreatorRank(param.guildID, key)
	activityKey := activity.KeyGuildRank(key)

	pipe := service.Redis.TxPipeline()
	roomsCmd := pipe.ZIncrBy(roomKey, param.score, strconv.FormatInt(param.toUserID, 10))
	roomTTLCmd := pipe.TTL(roomKey)
	beforeCmd := pipe.ZScore(activityKey, strconv.FormatInt(param.guildID, 10))
	afterCmd := pipe.ZIncrBy(activityKey, param.score, strconv.FormatInt(param.guildID, 10))
	activityTTLCmd := pipe.TTL(activityKey)
	_, _ = pipe.Exec()

	_, err1 := roomsCmd.Result()
	before, err2 := beforeCmd.Result()
	after, err3 := afterCmd.Result()
	for _, err := range []error{err1, err2, err3} {
		if err == nil || err == redis.Nil {
			continue
		}
		return 0, 0, err
	}

	pipe = service.Redis.Pipeline()
	var setTTL bool
	if roomTTLCmd.Val() == -1 {
		// 15 days extend
		liveserviceredis.ExpireAt(pipe, roomKey, gr.expireTime)
		setTTL = true
	}
	if activityTTLCmd.Val() == -1 {
		// 15 days extend
		liveserviceredis.ExpireAt(pipe, activityKey, gr.expireTime)
		setTTL = true
	}
	if setTTL {
		_, err := pipe.Exec()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return int64(before), int64(after), nil
}

func (gr guildRank) afterFeed(param *SyncCommonParam, before, after int64) {}
