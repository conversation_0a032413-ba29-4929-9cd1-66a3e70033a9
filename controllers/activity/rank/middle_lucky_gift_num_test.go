package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMiddlewareCollectLuckyGiftNum(t *testing.T) {
	assert := assert.New(t)

	r := rank{
		giftParam:  nil,
		goodsParam: nil,
	}

	assert.EqualError(middlewareCollectLuckyGiftNum(r, nil, nil), "giftParam is nil")
	r.giftParam = &SyncGiftParam{}
	assert.EqualError(middlewareCollectLuckyGiftNum(r, nil, nil), "gift must be luck gift")
	r.giftParam = &SyncGiftParam{
		LuckyGiftID:  1,
		LuckyGiftNum: 10,
	}
	p := addRankParam{
		score: 0,
	}
	assert.NoError(middlewareCollectLuckyGiftNum(r, nil, &p))
	assert.EqualValues(10, p.score)
}
