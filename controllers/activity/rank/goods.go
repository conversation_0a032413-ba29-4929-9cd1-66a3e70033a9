package rank

import (
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// AddRevenueTypeGift 收益类型为 gift
	AddRevenueTypeGift = "gift"
	// AddRevenueTypeQuestion 收益类型为 question
	AddRevenueTypeQuestion = "question"
	// AddRevenueTypeNoble 收益类型为 noble
	AddRevenueTypeNoble = "noble"
	// AddRevenueTypeSuperFan 收益类型为 superfan
	AddRevenueTypeSuperFan = "superfan"
	// AddRevenueTypePK 收益类型为 PK
	AddRevenueTypePK = "pk"
	// AddRevenueTypeReward 收益类型为奖励
	AddRevenueTypeReward = "reward"
	// AddRevenueTypeDanmaku 收益类型为付费弹幕
	AddRevenueTypeDanmaku = "danmaku"
	// AddRevenueTypeLuckyBox 收益类型为宝盒
	AddRevenueTypeLuckyBox = "lucky_box"
	// AddRevenueTypeGashapon 收益类型为超能魔方
	AddRevenueTypeGashapon = "gashapon"
)

// SyncGoodsParam 同步商品
type SyncGoodsParam struct {
	*SyncCommonParam

	GoodsID    int64
	GoodsType  string
	GoodsPrice int64
	GoodsGifts []*SyncGoodsGifts
}

// SyncGoodsGifts 同步商品礼物
type SyncGoodsGifts struct {
	GiftID   int64  `json:"gift_id"`
	GiftName string `json:"gift_name"`
	GiftType int    `json:"gift_type"` // 礼物类型
	GiftNum  int    `json:"gift_num"`

	// 单个礼物的价格和数量
	GiftPrice int64          `json:"gift_price"`
	GiftPoint int64          `json:"gift_point"`
	GiftAttr  goutil.BitMask `json:"gift_attr"`
}

// SetRevenueGoods set revenue goods
func (param *SyncGoodsParam) SetRevenueGoods(revenueType string, revenue int64) *SyncGoodsParam {
	param.GoodsType = revenueType
	param.GoodsPrice = revenue
	return param
}

// SetGoods set goods
func (param *SyncGoodsParam) SetGoods(g *livegoods.LiveGoods) *SyncGoodsParam {
	param.GoodsID = g.ID
	param.GoodsPrice = int64(g.GoodsTotalPrice())

	switch g.GoodsType() {
	case livegoods.GoodsTypeSuperFan:
		param.GoodsType = AddRevenueTypeSuperFan
	case livegoods.GoodsTypeGashapon:
		param.GoodsType = AddRevenueTypeGashapon
	}
	return param
}

// SetGoodsGifts set gifts
func (param *SyncGoodsParam) SetGoodsGifts(gifts []*SyncGoodsGifts) *SyncGoodsParam {
	param.GoodsGifts = gifts
	return param
}

// SendLiveActivity send live activity
func (param *SyncGoodsParam) SendLiveActivity(ctx mrpc.UserContext, syncType string) {
	send := param.newRankRevenueParam()
	send.Goods = &userapi.RankRevenueGoods{
		GoodsID:    param.GoodsID,
		GoodsType:  syncType,
		GoodsPrice: param.GoodsPrice,
	}
	if len(param.GoodsGifts) > 0 {
		send.Goods.GoodsGifts = goutil.SliceMap(param.GoodsGifts, func(gift *SyncGoodsGifts) *userapi.RankRevenueGoodsGifts {
			return &userapi.RankRevenueGoodsGifts{
				GiftID:    gift.GiftID,
				GiftName:  gift.GiftName,
				GiftType:  gift.GiftType,
				GiftNum:   gift.GiftNum,
				GiftPrice: gift.GiftPrice,
				GiftPoint: gift.GiftPoint,
				GiftAttr:  gift.GiftAttr,
			}
		})
	}
	err := send.Send(ctx)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// AddRankPoint add rank point
func (param *SyncGoodsParam) AddRankPoint() {
	if param.GoodsType == "" {
		panic("礼物类型不能为空")
	}
	ongoinglist, err := ongoingEvent()
	if err != nil {
		logger.Error(err)
		return
	}
	for _, s := range ongoinglist {
		if !s.checkRankTime() {
			continue
		}

		if s.checkLockActivityRankComponent() {
			continue
		}

		s.setGoodsParam(param)
		ranks := s.ranks()
		if len(ranks) == 0 {
			// logger.WithField("event_id", s.eventID()).Error("活动榜单 ranks length 为 0")
			continue
		}
		for _, rank := range ranks {
			if !rank.checkRankTime() {
				continue
			}

			if !rank.checkCatalog() {
				continue
			}

			if !rank.checkGoodsType(param.GoodsType) {
				continue
			}

			if !rank.checkApplication(param.ToUserID) {
				continue
			}

			rankParam, err := rank.beforeFeed()
			if err != nil {
				logger.Error(err)
				continue
			}
			if rankParam == nil {
				continue
			}

			before, after, err := rank.feedRank(rankParam)
			if err != nil {
				logger.Error(err)
				continue
			}

			rank.afterFeed(param.SyncCommonParam, before, after)
		}
	}
}
