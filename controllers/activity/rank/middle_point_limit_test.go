package rank

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMiddlewarePointLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.Redis.Del(activity.KeyFreePoint(999, "test")).Err())

	r := rank{
		giftParam: nil,
	}
	addParam := &addRankParam{score: 1}
	assert.NoError(middlewarePointLimit(r, nil, addParam))
	assert.EqualValues(1, addParam.score)

	r = rank{
		giftParam: &SyncGiftParam{GiftPrice: 1},
	}
	addParam = &addRankParam{score: 2}
	assert.NoError(middlewarePointLimit(r, nil, addParam))
	assert.EqualValues(2, addParam.score)

	r = rank{
		giftParam:  &SyncGiftParam{GiftPoint: 10},
		eventID:    999,
		Rank:       activity.Rank{Key: "test"},
		expireTime: util.TimeNow().Add(time.Minute),
	}
	middleware := &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"limit":15}`),
	}
	addParam = &addRankParam{score: 10}
	assert.NoError(middlewarePointLimit(r, middleware, addParam))
	assert.EqualValues(10, addParam.score)

	addParam = &addRankParam{score: 6}
	assert.NoError(middlewarePointLimit(r, middleware, addParam))
	assert.EqualValues(5, addParam.score)

	addParam = &addRankParam{score: 10}
	assert.True(isErrNoFeed(middlewarePointLimit(r, middleware, addParam)))
}
