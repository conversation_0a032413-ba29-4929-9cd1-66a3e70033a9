package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestSyncNobleParam_SetNoble(t *testing.T) {
	assert := assert.New(t)

	param := &SyncNobleParam{}
	param.SetNoble(true, vip.TypeLiveNoble, 100, 2, 1)
	assert.True(param.IsNew)
	assert.Equal(vip.TypeLiveNoble, param.Type)
	assert.Equal(int64(100), param.NoblePrice)
	assert.Equal(2, param.NobleLevel)
	assert.Equal(1, param.PreviousNobleLevel)
}

func TestSyncNobleParam_SendLiveActivity(t *testing.T) {
	assert := assert.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankRevenue, func(interface{}) (interface{}, error) {
		count++
		return nil, nil
	})
	defer cleanup()

	param := &SyncNobleParam{
		SyncCommonParam: &SyncCommonParam{},
	}
	param.SendLiveActivity(mrpc.NewUserContextFromEnv())
	assert.Equal(1, count)
}
