package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestBuildEffectURL(t *testing.T) {
	assert := assert.New(t)

	resp := BuildEffectURL("a", []string{".a", ".b", ".c"})
	assert.Equal("https://static-test.missevan.com/a.a;https://static-test.missevan.com/a.b;https://static-test.missevan.com/a.c", resp)
}

func TestNewBroadcastElems(t *testing.T) {
	assert := assert.New(t)

	resp := newBroadcastElems(1, &notifymessages.General{
		RoomID: 0,
	})
	assert.Len(resp, 1)

	resp = newBroadcastElems(1, &notifymessages.General{
		Effect:     &liveim.Effect{},
		EffectShow: notifymessages.EffectShowRoomJump,
		RoomID:     0,
	})
	assert.Len(resp, 2)
}

func TestNewFormatParam(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(map[string]string{
		"normal_color":    "#FFFFFF",
		"highlight_color": "#FFFFFF",
	}, NewFormatParam())
}

func TestSendNotify(t *testing.T) {
	assert := assert.New(t)

	var count int
	cancel := mrpc.SetMock("im://broadcast/many", func(input interface{}) (output interface{}, err error) {
		count++
		return "success", nil
	})
	defer cancel()

	SendNotify(&NotifyConfig{userID: 1})
	assert.Equal(1, count)
}
