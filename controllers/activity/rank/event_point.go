package rank

import (
	"strconv"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type pointRank struct {
	rank
}

func (pr pointRank) beforeFeed() (*addRankParam, error) {
	param := pr.buildAddRankParam()
	for _, middleParam := range pr.Middlewares {
		f, ok := rankMiddlewareMap()[middleParam.Func]
		if !ok {
			logger.WithFields(logger.Fields{
				"event_id": pr.eventID,
				"key":      pr.Key,
				"func":     middleParam.Func,
			}).Error("指定的 middleware 不存在")
			continue
		}
		if err := f(pr.rank, &middleParam, param); err != nil {
			if isErrNoFeed(err) {
				return nil, nil
			}
			return nil, err
		}
	}
	return param, nil
}

func (pr *pointRank) feedRank(param *addRankParam) (int64, int64, error) {
	if param.score == 0 || param.toUserID == 0 {
		return 0, 0, nil
	}

	key := pr.formatEventKey()
	roomKey := roomsrank.ActivityKey(param.roomID, key)
	activityKey := usersrank.ActivityKey(key)

	pipe := service.Redis.TxPipeline()
	var roomCmd *redis.FloatCmd
	// 特定场景下加榜单可能非用户发起，不存在 fromUserID
	if param.fromUserID != 0 {
		roomCmd = pipe.ZIncrBy(roomKey, param.score, strconv.FormatInt(param.fromUserID, 10))
	}
	roomTTLCmd := pipe.TTL(roomKey)
	beforeCmd := pipe.ZScore(activityKey, strconv.FormatInt(param.toUserID, 10))
	afterCmd := pipe.ZIncrBy(activityKey, param.score, strconv.FormatInt(param.toUserID, 10))
	activityTTLCmd := pipe.TTL(activityKey)
	_, _ = pipe.Exec()

	var err1 error
	if roomCmd != nil {
		_, err1 = roomCmd.Result()
	}
	before, err2 := beforeCmd.Result()
	after, err3 := afterCmd.Result()
	for _, err := range []error{err1, err2, err3} {
		if err == nil || err == redis.Nil {
			continue
		}
		return 0, 0, err
	}

	pipe = service.Redis.Pipeline()
	var setTTL bool
	if roomTTLCmd.Val() == -1 {
		// 15 days extend
		serviceredis.ExpireAt(pipe, roomKey, pr.expireTime)
		setTTL = true
	}
	if activityTTLCmd.Val() == -1 {
		// 15 days extend
		serviceredis.ExpireAt(pipe, activityKey, pr.expireTime)
		setTTL = true
	}
	if setTTL {
		_, err := pipe.Exec()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return int64(before), int64(after), nil
}

func (pr pointRank) afterFeed(param *SyncCommonParam, before, after int64) {
}
