package rank

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

// SyncCommonParam SyncRank
// TODO: 活动代码迁移到 live-activity 后，这里迁移到 service 层
type SyncCommonParam struct {
	RoomID            int64
	FromUserID        int64
	ToUserID          int64
	TransactionID     int64
	ConfirmTime       int64 // 订单确认时间, 单位: 秒
	OpenLogID         string
	GuildID           int64
	ActivityCatalogID int64
}

// NewSyncParam new SyncParam
func NewSyncParam(roomID, fromUserID, toUserID int64) *SyncCommonParam {
	return &SyncCommonParam{RoomID: roomID, FromUserID: fromUserID, ToUserID: toUserID}
}

// SetTransaction set transaction
func (p *SyncCommonParam) SetTransaction(balance *userapi.BalanceResp) *SyncCommonParam {
	p.TransactionID = balance.TransactionID
	// 目前订单没有返回 confirm_time，所以这里暂时用当前时间
	p.ConfirmTime = util.TimeNow().Unix()
	return p
}

// SetOpenLogID set open log id
func (p *SyncCommonParam) SetOpenLogID(openLogID *primitive.ObjectID) *SyncCommonParam {
	if openLogID != nil {
		p.OpenLogID = openLogID.Hex()
	}
	return p
}

// SetGuildID set guildID
func (p *SyncCommonParam) SetGuildID(guildID int64) *SyncCommonParam {
	p.GuildID = guildID
	return p
}

// SetActivityCatalogID set activity catalog id
func (p *SyncCommonParam) SetActivityCatalogID(activityCatalogID int64) *SyncCommonParam {
	p.ActivityCatalogID = activityCatalogID
	return p
}

func (p *SyncCommonParam) newRankRevenueParam() *userapi.RankRevenueParams {
	return &userapi.RankRevenueParams{
		UserID:            p.FromUserID,
		CreatorID:         p.ToUserID,
		RoomID:            p.RoomID,
		TransactionID:     p.TransactionID,
		ConfirmTime:       p.ConfirmTime,
		GuildID:           p.GuildID,
		ActivityCatalogID: p.ActivityCatalogID,
	}
}

// SetGift return SyncGiftParam
func (p *SyncCommonParam) SetGift(g *gift.Gift, giftNum int) *SyncGiftParam {
	param := SyncGiftParam{SyncCommonParam: p}
	if g == nil {
		return &param
	}
	return param.SetGift(g, giftNum)
}

// SetRevenueGoods return SyncGoodsParam
func (p *SyncCommonParam) SetRevenueGoods(revenueType string, revenue int64) *SyncGoodsParam {
	param := SyncGoodsParam{SyncCommonParam: p}
	return param.SetRevenueGoods(revenueType, revenue)
}

// SetGoods return SyncGoodsParam
func (p *SyncCommonParam) SetGoods(g *livegoods.LiveGoods) *SyncGoodsParam {
	param := SyncGoodsParam{SyncCommonParam: p}
	if g == nil {
		return &param
	}
	return param.SetGoods(g)
}

// SetNoble return SyncNobleParam
func (p *SyncCommonParam) SetNoble(isNew bool, nobleType int, price int64, nobleLevel, previousNobleLevel int) *SyncNobleParam {
	param := SyncNobleParam{SyncCommonParam: p}
	return param.SetNoble(isNew, nobleType, price, nobleLevel, previousNobleLevel)
}

// SyncGiftParam 同步礼物
type SyncGiftParam struct {
	*SyncCommonParam

	GiftID   int64
	GiftName string
	GiftType int // 礼物类型
	GiftNum  int

	// 单个礼物的价格和数量
	GiftPrice int64
	GiftPoint int64
	GiftAttr  util.BitMask

	// 随机礼物专用的
	LuckyGiftID    int64
	LuckyGiftName  string
	LuckyGiftPrice int64
	LuckyGiftNum   int
}

// SetGift set gift
func (param *SyncGiftParam) SetGift(g *gift.Gift, giftNum int) *SyncGiftParam {
	param.GiftID = g.GiftID
	param.GiftName = g.Name
	param.GiftPrice = g.Price
	param.GiftPoint = g.Point
	param.GiftType = g.Type
	param.GiftNum = giftNum
	param.GiftAttr = g.Attr
	return param
}

// SetLuckyGift set lucky gift
func (param *SyncGiftParam) SetLuckyGift(g *gift.Gift, giftNum int) *SyncGiftParam {
	param.LuckyGiftID = g.GiftID
	param.LuckyGiftName = g.Name
	param.LuckyGiftPrice = g.Price
	param.LuckyGiftNum = giftNum
	return param
}

// IsLuckyGift return is lucky gift
func (param SyncGiftParam) IsLuckyGift() bool {
	return param.LuckyGiftID != 0
}

// SendLiveActivity send live activity
func (param *SyncGiftParam) SendLiveActivity(ctx mrpc.UserContext) {
	send := param.newRankRevenueParam()
	send.Gift = &userapi.RankRevenueGift{
		GiftID:         param.GiftID,
		GiftName:       param.GiftName,
		GiftType:       param.GiftType,
		GiftNum:        param.GiftNum,
		GiftPrice:      param.GiftPrice,
		GiftPoint:      param.GiftPoint,
		GiftAttr:       param.GiftAttr,
		LuckyGiftID:    param.LuckyGiftID,
		LuckyGiftName:  param.LuckyGiftName,
		LuckyGiftPrice: param.LuckyGiftPrice,
		LuckyGiftNum:   param.LuckyGiftNum,
	}

	err := send.Send(ctx)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// AddRankPoint add rank point
func (param *SyncGiftParam) AddRankPoint() {
	ongoinglist, err := ongoingEvent()
	if err != nil {
		logger.Error(err)
		return
	}
	for _, s := range ongoinglist {
		if !s.checkRankTime() {
			continue
		}

		if s.checkLockActivityRankComponent() {
			continue
		}

		s.setGiftParam(param)
		ranks := s.ranks()
		if len(ranks) == 0 {
			// logger.WithField("event_id", s.eventID()).Error("活动榜单 ranks length 为 0")
			continue
		}
		for _, rank := range ranks {
			if !rank.checkRankTime() {
				continue
			}

			if !rank.checkCatalog() {
				continue
			}

			if !rank.checkGift(param.GiftID) {
				continue
			}

			if !rank.checkApplication(param.ToUserID) {
				continue
			}

			rankParam, err := rank.beforeFeed()
			if err != nil {
				logger.Error(err)
				continue
			}
			if rankParam == nil {
				continue
			}

			before, after, err := rank.feedRank(rankParam)
			if err != nil {
				logger.Error(err)
				continue
			}

			rank.afterFeed(param.SyncCommonParam, before, after)
		}
	}
}
