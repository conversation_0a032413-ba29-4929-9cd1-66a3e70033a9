package rankuserinfo

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestInfoGetRankUp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_key"
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 100, Member: 1},
		&redis.Z{Score: 200, Member: 2},
		&redis.Z{Score: 300, Member: 3},
	).Err())
	defer service.Redis.Del(key)

	info := Info{
		ID:    1,
		Rank:  3,
		Score: 100,
	}
	err := info.GetRankUp(key)
	require.NoError(err)
	assert.EqualValues(101, info.RankUp)

	err = info.GetRankUp(key, 1)
	require.NoError(err)
	assert.EqualValues(201, info.RankUp)
}

func TestGetMemBerIDByKey(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_get_key"
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 4, Member: 1},
		&redis.Z{Score: 3, Member: 2},
		&redis.Z{Score: 2, Member: 3},
		&redis.Z{Score: 1, Member: 4}).Err())
	defer service.Redis.Del(key)

	for i := int64(1); i < 5; i++ {
		n, err := MemberID(key, i)
		require.NoError(err)
		require.EqualValues(i, n)
	}

	i, err := MemberID(key, 9999)
	require.NoError(err)
	assert.EqualValues(0, i)
}

func TestRankMemberIDWithScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_get_key"
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 4, Member: 1},
		&redis.Z{Score: 3, Member: 2},
		&redis.Z{Score: 2, Member: 3},
		&redis.Z{Score: 1, Member: 4}).Err())
	defer service.Redis.Del(key)

	for i := int64(1); i < 5; i++ {
		n, score, err := RankMemberIDWithScore(key, i)
		require.NoError(err)
		require.EqualValues(i, n)
		require.Equal(score, 5-i)
	}

	i, score, err := RankMemberIDWithScore(key, 9999)
	require.NoError(err)
	assert.EqualValues(0, i)
	assert.EqualValues(0, score)
}
