package rankuserinfo

import (
	"strconv"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
)

// Info 榜单收益信息
type Info struct {
	ID      int64        `json:"id"`
	Name    string       `json:"name"`
	IconURL string       `json:"iconurl,omitempty"`
	Rank    int64        `json:"rank"`
	Score   int64        `json:"score"`
	Room    *room.Simple `json:"room,omitempty"`

	RankUp int64 `json:"rank_up,omitempty"`
}

// GetUserInfo get user info by redis key
func (info *Info) GetUserInfo(key string) error {
	pipe := service.Redis.TxPipeline()
	uidStr := strconv.FormatInt(info.ID, 10)
	rankCmd := pipe.ZRevRank(key, uidStr)
	scoreCmd := pipe.ZScore(key, uidStr)
	_, err := pipe.Exec()
	if err != nil {
		if err != redis.Nil {
			return err
		}
		return nil
	}
	info.Rank = rankCmd.Val() + 1
	info.Score = int64(scoreCmd.Val())
	return nil
}

// GetRankUp 获取上一名分数，传入 targetRank 可获取距指定名称的分数，排名从 1 开始
func (info *Info) GetRankUp(key string, targetRank ...int64) error {
	if info.Rank <= 1 {
		return nil
	}
	rank := info.Rank - 1
	if len(targetRank) == 1 {
		rank = targetRank[0]
	}

	_, score, err := RankMemberIDWithScore(key, rank)
	if err != nil {
		return err
	}
	info.RankUp = score - info.Score + 1
	if info.RankUp < 0 {
		info.RankUp = 0
	}
	return nil
}

// MemberID 获取指定名次的 id
func MemberID(key string, rank int64) (int64, error) {
	result, err := service.Redis.ZRevRange(key, rank-1, rank-1).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, err
	}
	if len(result) == 0 {
		return 0, nil
	}
	i, err := strconv.ParseInt(result[0], 10, 64)
	if err != nil {
		return 0, err
	}
	return i, nil
}

// RankMemberIDWithScore 获取指定名次的 id 与得分
func RankMemberIDWithScore(key string, rank int64) (int64, int64, error) {
	result, err := service.Redis.ZRevRangeWithScores(key, rank-1, rank-1).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, 0, nil
		}
		return 0, 0, err
	}
	if len(result) == 0 {
		return 0, 0, nil
	}
	i, err := strconv.ParseInt(result[0].Member.(string), 10, 64)
	if err != nil {
		return 0, 0, err
	}
	return i, int64(result[0].Score), nil
}
