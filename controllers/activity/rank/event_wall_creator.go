package rank

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
)

// 判断 wallCreatorRank 是否实现 ranker 接口
var _ ranker = wallCreatorRank{}

// TODO: 迁移至 live-activity 后与榜单分开
type wallCreatorRank struct {
	rank
}

func (w wallCreatorRank) beforeFeed() (*addRankParam, error) {
	if w.giftParam != nil {
		return w.buildAddRankParam(), nil
	}
	return nil, nil
}

func (w wallCreatorRank) feedRank(param *addRankParam) (int64, int64, error) {
	key := activity.KeyGiftWall(param.toUserID, w.eventID, w.Key)
	pipe := service.Redis.TxPipeline()
	pipe.HIncrBy(key, strconv.FormatInt(w.giftParam.GiftID, 10), int64(w.giftParam.GiftNum))
	serviceredis.ExpireAt(pipe, key, w.expireTime)
	_, err := pipe.Exec()
	if err != nil {
		return 0, 0, err
	}

	// 这里只负责统计礼物数量，后面用不到返回值，所以返回默认值
	return 0, 0, nil
}

func (w wallCreatorRank) afterFeed(param *SyncCommonParam, before int64, after int64) {}
