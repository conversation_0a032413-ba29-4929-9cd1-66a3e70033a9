package rank

import (
	"errors"

	"github.com/MiaoSiLa/live-service/models/activity"
)

var (
	errNoFeed = errors.New("no feed")
)

func isErrNoFeed(err error) bool {
	return err == errNoFeed
}

type rankMiddleware func(rank, *activity.RankMiddlewareParam, *addRankParam) error

// Middleware func
const (
	MiddlewareKeyCollectGiftNum      = "gift_num"
	MiddlewareKeyCollectLuckyGiftNum = "lucky_gift_num"
	MiddlewareKeyAddChallengerBuff   = "challenger_buff"
	MiddlewareKeyCollectGiftPoint    = "gift_point"
	MiddlewareKeyUserCost            = "user_cost"
	MiddlewareKeyPointLimit          = "point_limit"
)

func rankMiddlewareMap() map[string]rankMiddleware {
	return map[string]rankMiddleware{
		MiddlewareKeyCollectGiftNum:      middlewareCollectGiftNum,
		MiddlewareKeyCollectLuckyGiftNum: middlewareCollectLuckyGiftNum,
		MiddlewareKeyAddChallengerBuff:   middlewareAddChallengerBuff,
		MiddlewareKeyCollectGiftPoint:    middleCollectGiftPoint,
		MiddlewareKeyUserCost:            middlewareUserCost,
		MiddlewareKeyPointLimit:          middlewarePointLimit,
	}
}
