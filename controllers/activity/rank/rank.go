package rank

import "time"

// ActivityKeyExpire15Days 活动相关数据过期时间
var ActivityKeyExpire15Days = time.Hour * 24 * 15

// 统计单位
const (
	// CollectTypeNum 数量
	CollectTypeNum = iota + 1
	// CollectTypePoint 积分
	CollectTypePoint
	// CollectTypeGuild 公会赛
	CollectTypeGuild
	// CollectTypeUser 用户赛
	CollectTypeUser
	// CollectTypeSuperFan 超粉榜，只提供查询，统计由定时任务完成，临时支持 175 活动
	CollectTypeSuperFan
	// CollectTypeOther 其他来源，只提供榜单查询
	CollectTypeOther
	// CollectUserWall 用户礼物墙
	CollectUserWall
	// CollectCreatorWall 主播礼物墙
	CollectCreatorWall
)
