package rank

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	lua "github.com/yuin/gopher-lua"
	"github.com/yuin/gopher-lua/parse"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/activities"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/widget"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionWidget(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1643558400, 0)
	})
	defer goutil.SetTimeNow(nil)

	c := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/activity/widget?event_id=%d&room_id=15", usersrank.EventIDNewYear2022),
		true, nil)

	list := make([]map[string]interface{}, 0, 10)
	ch := make(chan map[string]interface{}, 10)
	for i := 0; i < 10; i++ {
		go func(ch chan map[string]interface{}, i int) {
			resp, err := ActionWidget(c)
			require.NoError(err)
			w, ok := resp.(*widgetLuaResp)
			require.True(ok)
			ch <- w.Metadata
		}(ch, i)
	}

	for i := 0; i < 10; i++ {
		select {
		case m := <-ch:
			list = append(list, m)
		case <-time.After(3 * time.Second):
			t.Fatal("timeout")
		}
	}
	close(ch)

	for _, item := range list {
		assert.Equal(list[0], item)
	}
}

func TestWidgetParamCheckEvent(t *testing.T) {
	require := require.New(t)

	param := widgetParam{EventID: 0}
	err := param.checkEvent()
	require.Equal(err, actionerrors.ErrParams)

	param.EventID = 999
	err = param.checkEvent()
	require.EqualError(err, "服务器内部错误: 活动不存在")

	param.EventID = 166
	err = param.checkEvent()
	require.NoError(err)
	require.NotNil(param.event)
}

func TestWidgetParamCheckActivityTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	param := widgetParam{event: &event{ExtendedFields: &activity.ExtendedFields{RankStartTime: 0, RankEndTime: now - 1000}}}
	resp := param.checkActivityTime()
	assert.Equal(&widget.Widget{Status: widget.StatusEnding, TS: now}, resp)

	param.event.ExtendedFields.RankStartTime = now - 1000
	param.event.ExtendedFields.RankEndTime = now + 1000
	resp = param.checkActivityTime()
	assert.Nil(resp)
}

func TestWidgetParamCheckRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := widgetParam{
		c: handler.NewTestContext("GET", "", false, nil),
	}
	require.Equal(actionerrors.ErrUnloggedUser, param.checkRoom())
	assert.Nil(param.r)
	assert.Zero(param.RoomID)

	param = widgetParam{
		RoomID: 15,
	}
	require.NoError(param.checkRoom())
	assert.NotNil(param.r)

	param = widgetParam{
		c: handler.NewTestContext("GET", "", true, nil),
	}
	require.NoError(param.checkRoom())
	assert.NotEmpty(param.RoomID)
	require.NotNil(param.r)
	assert.Equal(param.c.UserID(), param.r.CreatorID)
}

func TestWidgetParamBuildLuaResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	w := widgetParam{
		EventID: 0,
		RoomID:  0,
		event: &event{
			Simple:         mevent.Simple{},
			ExtendedFields: &activity.ExtendedFields{},
		},
		r: &room.Room{
			Helper: room.Helper{CreatorID: 1},
		},
	}

	resp, err := w.buildLuaResp(&activity.Rank{
		Key:        "1",
		ShowMyRank: nil,
	}, engine.WidgetTypeNotQualified)
	require.NoError(err)
	expected := widget.NewWidget(goutil.TimeNow().Unix())
	expected.BuildNotQualifiedWidget(0)
	assert.Equal(expected.TS, resp.Widget.TS)
	assert.Equal(expected.Status, resp.Widget.Status)

	resp, err = w.buildLuaResp(nil, engine.WidgetTypeEnding)
	require.NoError(err)
	expected = widget.NewWidget(goutil.TimeNow().Unix())
	expected.BuildEndingWidget()
	assert.Equal(expected, resp.Widget)
}

func TestWidgetParamResp(t *testing.T) {
	// 目前小窗不支持该测试
	t.Skip()

	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	activities.Collection().FindOneAndUpdate(ctx,
		bson.M{"type": activities.TypeFaaSEventWidget},
		bson.M{"$set": bson.M{
			"start_time":  goutil.TimeNow().Unix(),
			"end_time":    goutil.TimeNow().Unix() + 10,
			"order":       1,
			"widget_type": 1,
			"content": `
local function checkPromoted(user_rank, user_score, RankMaxCount, PromotedScore)
    return user_rank <= RankMaxCount or user_score >= PromotedScore
end

local user, err = ctx.widget:get_user_info(ctx.widget.params.key, ctx.widget.params.creator_id)
if err then
    return nil, err
end

if user.score < ctx.widget.params.promoted_score then
    user.rank_up = ctx.widget.params.promoted_score - user.score
elseif user.rank ~= 1 then
    user.rank_up, err = ctx.widget:get_rank_up(ctx.params.key, user.rank - 1) - user.score
    if err then
        return nil, err
    end
else
    user.rank_up, err = user.score - ctx.widget:get_rank_up(ctx.widget.params.key, 2)
    if err then
        return nil, err
    end
end

if not checkPromoted(user.rank, user.score, ctx.widget.params.rank_max_count, ctx.widget.params.promoted_score)
then
    user.rank = 0
end
return user, nil
`}}, options.FindOneAndUpdate().SetUpsert(true),
	)
	service.Cache5Min.Flush()

	param := widgetParam{
		r: &room.Room{
			Helper: room.Helper{CreatorID: 1},
		},
		event: &event{
			Simple: mevent.Simple{
				ID: 172,
			},
			ExtendedFields: &activity.ExtendedFields{
				RankMaxCount:  5,
				PromotedScore: 200,
			},
		},
		c: handler.NewTestContext("get", "", true, nil),
	}

	key := activity.KeyRank(param.EventID)
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{
			Score:  50,
			Member: 1,
		},
		&redis.Z{
			Score:  30,
			Member: 2,
		},
		&redis.Z{
			Score:  20,
			Member: 3,
		},
		&redis.Z{
			Score:  10,
			Member: 4,
		},
		&redis.Z{
			Score:  5,
			Member: 5,
		},
		&redis.Z{
			Score:  1,
			Member: 6,
		},
	).Err())

	resp, err := param.luaResp()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(1, resp.Metadata["rank"])
	assert.EqualValues(150, resp.Metadata["rank_up"])
	assert.EqualValues(50, resp.Metadata["score"])
	expected := widget.NewWidget(goutil.TimeNow().Unix())
	expected.BuildOnGoingWidget(0)
	assert.Equal(expected, resp.Widget)
}

func TestWidgetParamLuaRespCheckCatalog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	param := widgetParam{
		r: &room.Room{
			Helper: room.Helper{CreatorID: 1, ActivityCatalogID: 2},
		},
		EventID: 9999,
		event: &event{
			ExtendedFields: &activity.ExtendedFields{
				Ranks: []activity.Rank{
					{
						Key: "1",
						Collect: &activity.Collect{
							Type: 2,
						},
						RankStartTime:      1,
						RankEndTime:        2,
						ActivityCatalogIDs: []int64{1},
					},
					{
						Key: "2",
						Collect: &activity.Collect{
							Type: 2,
						},
						RankStartTime:      1,
						RankEndTime:        2,
						ActivityCatalogIDs: []int64{2},
					},
					{
						Key: "3",
						Collect: &activity.Collect{
							Type: 2,
						},
						RankStartTime:      1,
						RankEndTime:        2,
						ActivityCatalogIDs: []int64{3},
					},
				},
			},
		},
		c: handler.NewTestContext("get", "", true, nil),
	}

	key := keys.KeyActivityWidgetLuaProto2.Format(9999, 1) + "/2"
	reader := strings.NewReader(`return { ['res'] = 'test' }`)
	chunk, err := parse.Parse(reader, key)
	require.NoError(err)
	proto, err := lua.Compile(chunk, key)
	require.NoError(err)

	service.Cache5Min.Set(key, proto, 0)

	resp, err := param.luaResp()
	require.NoError(err)
	require.NotNil(resp.Metadata)
	assert.Equal("test", resp.Metadata["res"])
}
