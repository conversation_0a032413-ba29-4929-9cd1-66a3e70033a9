package rank

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUserRankFeedRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 99999
	)

	activityKey := usersrank.ActivityKey(strconv.FormatInt(eventID, 10))
	require.NoError(service.Redis.Del(activityKey).Err())
	defer service.Redis.Del(activityKey)
	e := userRank{
		pointRank: pointRank{
			rank{
				eventID: eventID,
				Rank: activity.Rank{
					Application: activity.ApplicationNone,
				},
				expireTime: goutil.TimeNow().Add(time.Hour).Add(ActivityKeyExpire15Days),
			},
		},
	}

	// 免费礼物
	e.giftParam = &SyncGiftParam{
		SyncCommonParam: &SyncCommonParam{
			FromUserID: fromUserID1,
			ToUserID:   toUserID,
		},
		GiftNum:   5,
		GiftPrice: 0,
	}
	param, err := e.beforeFeed()
	require.NoError(err)
	before, after, err := e.feedRank(param)

	require.NoError(err)
	assert.Zero(before)
	assert.EqualValues(0, after)

	e.giftParam = &SyncGiftParam{
		SyncCommonParam: &SyncCommonParam{
			FromUserID: fromUserID1,
			ToUserID:   toUserID,
		},
		GiftNum:   5,
		GiftPrice: 100,
	}
	param, err = e.beforeFeed()
	require.NoError(err)
	before, after, err = e.feedRank(param)
	require.NoError(err)
	assert.Zero(before)
	assert.EqualValues(500, after)

	score, err := service.Redis.ZScore(activityKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(500, score)

	e.giftParam = &SyncGiftParam{
		SyncCommonParam: &SyncCommonParam{
			FromUserID: fromUserID1,
			ToUserID:   toUserID,
		},
		GiftNum:   5,
		GiftPrice: 100,
	}
	param, err = e.beforeFeed()
	require.NoError(err)
	before, after, err = e.feedRank(param)
	require.NoError(err)
	assert.EqualValues(500, before)
	assert.EqualValues(1000, after)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	// ttl
	ttl, err := service.Redis.TTL(activityKey).Result()
	require.NoError(err)
	assert.Less(time.Hour+15*24*time.Hour-5*time.Second, ttl)
}
