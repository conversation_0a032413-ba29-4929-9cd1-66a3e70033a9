package rank

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
)

func TestMiddleCollectGiftPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := rank{
		giftParam: &SyncGiftParam{
			GiftNum:   10,
			GiftPrice: 0,
			GiftPoint: 10,
		},
	}
	var p addRankParam
	err := middleCollectGiftPoint(r, &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"gift_ids":[1,2,3,4]}`),
	}, &p)
	require.NoError(err)
	assert.Zero(p.score)

	r.giftParam.GiftID = 1
	err = middleCollectGiftPoint(r, &activity.RankMiddlewareParam{
		Params: json.RawMessage(`{"gift_ids":[1,2,3,4]}`),
	}, &p)
	require.NoError(err)
	assert.EqualValues(100, p.score)
}
