package rank

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

const (
	winCount = 20   // 胜场数
	winScore = 2000 // 有效胜场积分
)

func init() {
	livepk.ActivityFunc = syncPK
}

func syncPK(p livepk.LivePK) {
	_, e, err := activity.FindLiveExtendedFields(usersrank.EventIDPK, true)
	if err != nil {
		logger.Error(err)
		return
	}

	now := e.TimeNow().Unix()
	if e.RankStartTime > now || e.RankEndTime <= now {
		return
	}

	key := activity.KeyUserComplete(usersrank.EventIDPK, "finished")
	finished, err := service.Redis.SAdd(key, p.OID.Hex()).Result()
	if err != nil {
		logger.Error(err)
		return
	}

	if finished == 0 {
		return
	}

	winner := p.Winner()
	for _, fighter := range p.Fighters {
		// 败方或平局双方获得自己 PK 值的 5%
		addScore := float64(fighter.Score) * 0.05
		if winner != nil && winner.RoomID == fighter.RoomID {
			// 胜方获得双方总 PK 值的 15%
			addScore = float64(p.Fighters[0].Score+p.Fighters[1].Score) * 0.15
		}
		if addScore <= 0 {
			continue
		}

		NewSyncParam(fighter.RoomID, 0, fighter.CreatorID).
			SetRevenueGoods(AddRevenueTypePK, int64(addScore)).
			AddRankPoint()
	}

	// 统计有效胜场
	if winner == nil || winner.Score <= winScore {
		return
	}

	key = keys.KeyActivityUserStatus2.Format(usersrank.EventIDPK, "pk_win_count")
	pipe := service.Redis.TxPipeline()
	beforeCmd := pipe.HGet(key, strconv.FormatInt(winner.CreatorID, 10))
	afterCmd := pipe.HIncrBy(key, strconv.FormatInt(winner.CreatorID, 10), 1)
	_, _ = pipe.Exec()

	before, err := beforeCmd.Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}

	after, err := afterCmd.Result()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	if before < winCount && after >= winCount {
		err = SendAppearances(SendAppearanceParam{
			AppearanceID: 50041, // 常胜先锋称号
			UserID:       winner.CreatorID,
			ExpireTime:   1647792000, // 2022.03.21
		})
		if err != nil {
			logger.Error(err)
			// PASS
		}

		key = activity.KeyUserComplete(usersrank.EventIDPK)
		err = service.Redis.SAdd(key, strconv.FormatInt(winner.CreatorID, 10)).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}
