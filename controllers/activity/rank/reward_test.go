package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCheckPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := activity.KeyPrizePool(999)
	defer service.Redis.Del(key)

	ok, err := checkPool("999", 10e3, 10, goutil.TimeNow().Unix()+10)
	require.NoError(err)
	assert.True(ok)

	ok, err = checkPool("999", 10e3, 10e4, goutil.TimeNow().Unix()+10)
	require.NoError(err)
	assert.False(ok)

	ttl, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.Greater(ttl, ActivityKeyExpire15Days)
}

func TestSendAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(SendAppearances(SendAppearanceParam{
		AppearanceID: 42,
		UserID:       12,
		Duration:     3600,
	}))

	uaItem, err := userappearance.FindValidAppearance(42,
		12, appearance.TypeMessageBubble)
	require.NoError(err)
	assert.NotNil(uaItem)
}
