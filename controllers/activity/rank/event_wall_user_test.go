package rank

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestWallUserRank_feedRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := activity.KeyGiftWall(1, 1)
	require.NoError(service.Redis.Del(key).Err())

	param := wallUserRank{
		rank: rank{
			eventID: 1,
			giftParam: &SyncGiftParam{
				GiftID:  1,
				GiftNum: 2,
			},
			expireTime: goutil.TimeNow().Add(time.Minute),
		},
	}
	_, _, err := param.feedRank(&addRankParam{fromUserID: 1})
	require.NoError(err)

	num, err := service.Redis.HGet(key, "1").Int()
	require.NoError(err)
	assert.Equal(2, num)
}
