package rank

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMiddlewareCollectGiftNum(t *testing.T) {
	assert := assert.New(t)

	r := rank{
		giftParam:  nil,
		goodsParam: nil,
	}

	assert.EqualError(middlewareCollectGiftNum(r, nil, nil), "giftParam is nil")
	r.giftParam = &SyncGiftParam{
		GiftID:  1,
		GiftNum: 10,
	}
	p := addRankParam{
		score: 0,
	}
	assert.NoError(middlewareCollectGiftNum(r, nil, &p))
	assert.EqualValues(10, p.score)
}
