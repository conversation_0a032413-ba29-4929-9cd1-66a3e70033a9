package rank

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewSyncGoodsParam(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(
		&SyncGoodsParam{
			SyncCommonParam: &SyncCommonParam{
				RoomID: 1, FromUserID: 2, ToUserID: 3,
			},
		},
		NewSyncParam(1, 2, 3).SetGoods(nil))
}

func TestSyncGoodsParam_SetGoods(t *testing.T) {
	assert := assert.New(t)

	p := SyncGoodsParam{}
	assert.Equal(
		&SyncGoodsParam{
			GoodsID:    1,
			GoodsType:  AddRevenueTypeSuperFan,
			GoodsPrice: 4,
		},
		p.SetGoods(&livegoods.LiveGoods{ID: 1, Type: 1, Price: 4}),
	)

	assert.Equal(
		&SyncGoodsParam{
			GoodsID:    1,
			GoodsType:  AddRevenueTypeGashapon,
			GoodsPrice: 4,
		},
		p.SetGoods(&livegoods.LiveGoods{ID: 1, Type: livegoods.GoodsTypeGashapon, Price: 4}),
	)
}

func TestSyncGoodsParam_SetGoodsGifts(t *testing.T) {
	assert := assert.New(t)

	p := SyncGoodsParam{}
	p.SetGoodsGifts([]*SyncGoodsGifts{
		{
			GiftID: 1,
		},
		{
			GiftID: 2,
		},
	})
	assert.Len(p.GoodsGifts, 2)
}

func TestSyncGoodsParamAddPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 999999
	)
	roomKey := roomsrank.ActivityKey(roomID, strconv.FormatInt(eventID, 10))
	activityKey := usersrank.ActivityKey(strconv.FormatInt(eventID, 10))
	applicationKey := activity.KeyUsersApplication(eventID)
	require.NoError(service.Redis.Del(roomKey, activityKey, applicationKey, keys.KeyActivityOngoingLiveRankEvent0.Format()).Err())
	defer service.Redis.Del(roomKey, activityKey, applicationKey, keys.KeyActivityOngoingLiveRankEvent0.Format())

	param := NewSyncParam(roomID, fromUserID1, toUserID).
		SetRevenueGoods("aaa", 1000)

	// goods
	// 没在礼物列表
	param.AddRankPoint()
	_, err := service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.Equal(redis.Nil, err)
	_, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.Equal(redis.Nil, err)

	// 没在白名单
	// application
	param.GoodsID = livegoods.GoodsTypeSuperFan
	param.AddRankPoint()
	_, err = service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.Equal(redis.Nil, err)
	_, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.Equal(redis.Nil, err)

	// 添加白名单
	require.NoError(service.Redis.SAdd(applicationKey, toUserID).Err())

	// 送礼成功
	param.AddRankPoint()
	score, err := service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(toUserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer goutil.SetTimeNow(nil)

	// not in rank time
	param.AddRankPoint()
	score, err = service.Redis.ZScore(roomKey, strconv.FormatInt(fromUserID1, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)
}

func TestSyncGoodsPointGiftGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomID      int64 = 1000
		fromUserID1 int64 = 1234
		toUserID    int64 = 4321
		eventID     int64 = 999998
		guildID     int64 = 1
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1633017600, 0)
	})
	defer goutil.SetTimeNow(nil)
	roomKey := activity.KeyGuildCreatorRank(guildID, strconv.FormatInt(eventID, 10))
	activityKey := activity.KeyGuildRank(strconv.FormatInt(eventID, 10))
	require.NoError(service.Redis.Del(roomKey, activityKey).Err())
	defer service.Redis.Del(roomKey, activityKey)

	NewSyncParam(roomID, fromUserID1, toUserID).
		SetGuildID(guildID).
		SetRevenueGoods("test", 1000).
		AddRankPoint()

	score, err := service.Redis.ZScore(roomKey, strconv.FormatInt(toUserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	score, err = service.Redis.ZScore(activityKey, strconv.FormatInt(guildID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1000, score)

	// ttl
	ttl, err := service.Redis.TTL(activityKey).Result()
	require.NoError(err)
	assert.Less(time.Hour+15*24*time.Hour-5*time.Second, ttl)
}
