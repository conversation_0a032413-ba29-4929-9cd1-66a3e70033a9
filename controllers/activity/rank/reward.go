package rank

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Node 奖励节点
type Node struct {
	Score   int64
	GiftID  int64
	GiftNum int
}

// NextNode 返回下一节点，没有返回 nil
func NextNode(nodes []Node, score int64) *Node {
	for _, node := range nodes {
		if node.Score > score {
			return &node
		}
	}
	return nil
}

func checkPool(key string, maxPoolValue, value, endTime int64) (bool, error) {
	poolKey := keys.KeyActivityPool1.Format(key)
	poolCmd := service.Redis.Get(poolKey)

	pool, err := poolCmd.Int64()
	if err != nil && err != redis.Nil {
		return false, err
	}
	if pool+value > maxPoolValue {
		return false, nil
	}

	pipe := service.Redis.TxPipeline()
	incrCmd := pipe.IncrBy(poolKey, value)
	pipe.Expire(poolKey, time.Unix(endTime, 0).Add(ActivityKeyExpire15Days).Sub(goutil.TimeNow()))
	_, err = pipe.Exec()
	if err != nil {
		return false, err
	}
	pool = incrCmd.Val()
	switch {
	case pool > maxPoolValue:
		err = service.Redis.IncrBy(poolKey, -value).Err()
		return false, err
	default:
		return true, nil
	}
}

// SendAppearanceParam send Appearance Param
type SendAppearanceParam struct {
	AppearanceID int64 `json:"appearance_id,omitempty"`
	UserID       int64 `json:"user_id,omitempty"`
	Duration     int64 `json:"duration,omitempty"`
	ExpireTime   int64 `json:"expire_time,omitempty"`
}

// SendAppearances send appearance
func SendAppearances(params ...SendAppearanceParam) error {
	appearanceIDs := make([]int64, len(params))
	for i := range params {
		appearanceIDs[i] = params[i].AppearanceID
	}

	appearances, err := appearance.Find(bson.M{
		"id": bson.M{"$in": appearanceIDs},
	}, nil)
	if err != nil {
		return err
	}

	mAppearances := goutil.ToMap(appearances, "ID").(map[int64]*appearance.Appearance)
	for _, item := range params {
		aItem := mAppearances[item.AppearanceID]
		if aItem == nil {
			logger.Error(fmt.Errorf("failed to find appearance id: %d", item.AppearanceID))
			continue
		}
		err := userappearance.AddAppearance(item.UserID, item.Duration, item.ExpireTime, aItem)
		if err != nil {
			logger.WithField("user_id", item.UserID).Error(err)
			// PASS
		}
	}
	return nil
}
