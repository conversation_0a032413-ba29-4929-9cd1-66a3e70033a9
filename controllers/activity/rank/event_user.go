package rank

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

type userRank struct {
	pointRank
}

func (ur userRank) feedRank(param *addRankParam) (int64, int64, error) {
	if param.score == 0 {
		return 0, 0, nil
	}

	key := ur.formatEventKey()
	activityKey := usersrank.ActivityKey(key)

	pipe := service.Redis.TxPipeline()
	beforeCmd := pipe.ZScore(activityKey, strconv.FormatInt(param.fromUserID, 10))
	afterCmd := pipe.ZIncrBy(activityKey, param.score, strconv.FormatInt(param.fromUserID, 10))
	activityTTLCmd := pipe.TTL(activityKey)
	_, _ = pipe.Exec()

	before, err1 := beforeCmd.Result()
	after, err2 := afterCmd.Result()
	for _, err := range []error{err1, err2} {
		if err == nil || serviceredis.IsRedisNil(err) {
			continue
		}
		return 0, 0, err
	}

	if activityTTLCmd.Val() == -1 {
		// 15 days extend
		liveserviceredis.ExpireAt(service.Redis, activityKey, ur.expireTime)
	}
	return int64(before), int64(after), nil
}

func (ur userRank) afterFeed(param *SyncCommonParam, before, after int64) {
}
