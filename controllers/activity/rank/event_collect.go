package rank

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 已迁移到 live-activity 项目, 需要删除

type collectRank struct {
	rank
}

func (cr collectRank) giftKey() string {
	return fmt.Sprintf("gift_%d", cr.giftParam.GiftID)
}

func (cr collectRank) achievementNum(res *liveactivity.LiveActivities) int64 {
	if res == nil {
		return 0
	}
	return res.Achievement[cr.giftKey()]
}

// getCollectMaxNum 获取当前添加礼物的收集限制
func (cr collectRank) getCollectMaxNum() int64 {
	for _, g := range cr.Collect.Gifts {
		if g.GiftID != cr.giftParam.GiftID {
			continue
		}
		if g.MaxNum != nil {
			return *g.MaxNum
		}
		return 0
	}
	return 0
}

func (cr collectRank) beforeFeed() (*addRankParam, error) {
	return cr.buildAddRankParam(), nil
}

func (cr *collectRank) feedRankFunc(ctx context.Context) (int64, int64, error) {
	// TODO: filter 考虑加个 num < max 的逻辑
	filter := bson.M{
		"event_id":   cr.eventID,
		"room_id":    cr.giftParam.RoomID,
		"creator_id": cr.giftParam.ToUserID,
	}
	if cr.Key != "" {
		filter["key"] = cr.Key
	}

	var res liveactivity.LiveActivities
	err := liveactivity.Collection().FindOne(ctx, filter).Decode(&res)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return 0, 0, err
	}

	var (
		addNum = int64(cr.giftParam.GiftNum)
		max    = cr.getCollectMaxNum()
		num    = cr.achievementNum(&res)
	)

	if max != 0 {
		if num >= max {
			return res.Score, res.Score, nil
		}

		if num+addNum >= max {
			addNum = max - num
		}
	}

	var (
		newRes   liveactivity.LiveActivities
		addScore = cr.giftParam.GiftPrice * addNum
	)
	err = liveactivity.Collection().FindOneAndUpdate(ctx, filter, bson.M{
		"$inc": bson.M{
			"score":                       addScore,
			"achievement." + cr.giftKey(): addNum,
		},
		"$set": bson.M{
			// REVIEW: update time 应该不需要时间偏移
			"updated_time": goutil.TimeNow(),
		},
		"$setOnInsert": bson.M{
			// REVIEW: create time 应该不需要时间偏移
			"created_time": goutil.TimeNow(),
		},
	}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&newRes)
	if err != nil {
		return 0, 0, err
	}
	// before 使用 after 的值 - add 的值来避免并发情况下带来的问题
	return newRes.Score - addScore, newRes.Score, nil
}

// feedRank feed rank
func (cr collectRank) feedRank(param *addRankParam) (before int64, after int64, err error) {
	if cr.getCollectMaxNum() == 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		return cr.feedRankFunc(ctx)
	}

	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		before, after, err = cr.feedRankFunc(ctx)
		return err
	})
	return
}

func (cr collectRank) afterFeed(param *SyncCommonParam, before, after int64) {}
