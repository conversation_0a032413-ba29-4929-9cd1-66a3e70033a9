package rank

import (
	"encoding/json"
	"errors"
	"math"

	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/controllers/activity/rank/engine"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank/rankuserinfo"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/service"
)

type arenaStatus int

const (
	arenaStatusAllRanksNotStart arenaStatus = iota + 1
	arenaStatusRanksOnGoing
	arenaStatusAllRanksEnd
)

const (
	challengerBuffNone = iota
	challengerBuffChallenger
	challengerBuffSuperChallenger
)

type arenaParam struct {
	Keys     []string `json:"keys"`
	Duration int64    `json:"duration"`
}

func init() {
	engine.AppendLuaMethods("get_challenger_user", getChallengerUser)
}

func getChallengerUser(l *lua.LState) int {
	eventID := l.ToInt64(2)
	key := l.ToString(3)

	_, e, err := activity.FindLiveExtendedFields(eventID, true)
	if err != nil {
		return engine.LuaReturn(l, nil, nil, err)
	}

	r := e.Rank(key)
	if r == nil {
		return engine.LuaReturn(l, nil, nil, errors.New("can not find rank"))
	}
	for _, middleware := range r.Middlewares {
		if middleware.Func != MiddlewareKeyAddChallengerBuff {
			continue
		}

		var param arenaParam
		err = json.Unmarshal(middleware.Params, &param)
		if err != nil {
			return engine.LuaReturn(l, nil, nil, err)
		}

		userID, buff, err := challengerUser(eventID, *e, param)
		if err != nil {
			return engine.LuaReturn(l, nil, nil, err)
		}
		return engine.LuaReturn(l, userID, buff, nil)
	}
	return engine.LuaReturn(l, nil, nil, errors.New("can not find middleware param"))
}

// middlewareAddChallengerBuff 擂主榜
func middlewareAddChallengerBuff(r rank, middleParam *activity.RankMiddlewareParam, addParam *addRankParam) error {
	var param arenaParam
	if err := json.Unmarshal(middleParam.Params, &param); err != nil {
		return err
	}

	buff, err := challengerBuff(r.eventID, addParam.toUserID, *r.event.ExtendedFields, param)
	if err != nil {
		return err
	}

	switch buff {
	case challengerBuffChallenger:
		addParam.score *= 1.2
	case challengerBuffSuperChallenger:
		addParam.score *= 1.3
	}

	addParam.score = math.Round(addParam.score)
	return nil
}

func challengerBuff(eventID int64, toUserID int64, e activity.ExtendedFields, middleParam arenaParam) (int, error) {
	now := e.TimeNow().Unix()
	ranks := e.RanksByKeys(middleParam.Keys...)
	if len(ranks) == 0 {
		// REVIEW: ranks 为空是否应该告警
		return challengerBuffNone, nil
	}

	rs, _ := lastTwoArenaRankKeys(ranks, now)
	if len(rs) == 0 {
		return challengerBuffNone, nil
	}

	// 没有 buff 结束时间时，擂主榜所有赛程结束后，最后一个擂主 buff 会持续到该 rank 结束
	if middleParam.Duration != 0 &&
		rs[0].RankEndTime+middleParam.Duration/1000 <= now {
		return challengerBuffNone, nil
	}

	challengerUserID, status, err := challengerUser(eventID, e, middleParam)
	if err != nil {
		return 0, err
	}
	if challengerUserID != toUserID {
		return challengerBuffNone, nil
	}
	return status, nil
}

// challengerUser 获取擂主榜用户和擂主状态
func challengerUser(eventID int64, e activity.ExtendedFields, middleParam arenaParam) (int64, int, error) {
	now := e.TimeNow().Unix()
	ranks := e.RanksByKeys(middleParam.Keys...)
	if len(ranks) == 0 {
		// REVIEW: ranks 为空是否应该告警
		return 0, challengerBuffNone, nil
	}

	rs, _ := lastTwoArenaRankKeys(ranks, now)
	if len(rs) == 0 {
		return 0, challengerBuffNone, nil
	}

	userID1, err := arenaCache(eventID, rs[0], now)
	if err != nil {
		return 0, challengerBuffNone, nil
	}
	if len(rs) < 2 {
		return userID1, challengerBuffChallenger, nil
	}
	userID2, err := arenaCache(eventID, rs[1], now)
	if err != nil {
		return 0, challengerBuffNone, nil
	}
	if userID1 == userID2 {
		return userID1, challengerBuffSuperChallenger, nil
	}
	return userID1, challengerBuffChallenger, nil
}

// lastTwoArenaRankKeys 获取刚结束的 rank，与擂主榜状态
// 返回值为 ranks 开始时间的倒序
func lastTwoArenaRankKeys(ranks []activity.Rank, now int64) ([]activity.Rank, arenaStatus) {
	if len(ranks) == 0 {
		return nil, arenaStatusAllRanksNotStart
	}
	if now < ranks[0].RankStartTime {
		return []activity.Rank{}, arenaStatusAllRanksNotStart
	}
	for i, r := range ranks {
		if now >= r.RankEndTime {
			continue
		}
		// 第一个榜单刚开始
		if i == 0 {
			return []activity.Rank{}, arenaStatusRanksOnGoing
		}
		// 第二个榜单刚开始
		if i == 1 {
			return []activity.Rank{ranks[i-1]}, arenaStatusRanksOnGoing
		}
		return []activity.Rank{ranks[i-1], ranks[i-2]}, arenaStatusRanksOnGoing
	}
	if len(ranks) == 1 {
		return []activity.Rank{ranks[len(ranks)-1]}, arenaStatusAllRanksEnd
	}
	return []activity.Rank{ranks[len(ranks)-1], ranks[len(ranks)-2]}, arenaStatusAllRanksEnd
}

func arenaCache(eventID int64, r activity.Rank, now int64) (int64, error) {
	key := activity.KeyRank(eventID, r.Key)
	res, ok := service.Cache5Min.Get(key)
	if ok {
		id, ok := res.(int64)
		if ok {
			return id, nil
		}
	}

	id, err := rankuserinfo.MemberID(key, 1)
	if err != nil {
		return 0, err
	}

	// 擂主刚结束的前几秒不产生缓存，避免因为时间不同导致一致性的问题
	if r.RankEndTime+10 > now {
		return id, nil
	}
	service.Cache5Min.Set(r.Key, id, 0)
	return id, nil
}
