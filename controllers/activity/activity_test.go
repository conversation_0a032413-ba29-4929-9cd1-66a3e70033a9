package activity

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var imRedis *redis.Client

func TestMain(m *testing.M) {
	initTest()
	m.Run()
}

func initTest() {
	config.InitTest()
	handler.SetMode(handler.TestMode)

	var err error
	redisConf := serviceredis.Config{
		Addr: "redis.srv.maoer.co:6379",
		DB:   106,
	}
	imRedis, err = serviceredis.NewRedisClient(&redisConf)
	if err != nil {
		logger.Fatal(err)
	}
	service.InitTest()
	service.SetDBUseSQLite()
	// 不 cancel
	mrpc.SetMock("app://live/buy-gift",
		func(input interface{}) (output interface{}, err error) {
			return userapi.BalanceResp{
				TransactionID:    123,
				Balance:          123,
				LiveNobleBalance: 123,
				Price:            123,
			}, nil
		})
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	h := Handler()
	assert.Equal("activity", h.Name)
	kc.Check(h,
		"widget",
		"wish/buy", "wish/record", "wish/info",
	)
}
