package activityreward

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaoerWalletUserID 猫耳娘的零钱袋用户 ID
const MaoerWalletUserID int64 = 2939325

// GiftParam 礼物 ID 和数量
type GiftParam struct {
	GiftID  int64 `json:"gift_id"`
	GiftNum int   `json:"gift_num"`
}

// RewardParam 直播间赠送礼物
type RewardParam struct {
	UserID    int64       `json:"user_id"`
	Gifts     []GiftParam `json:"gifts"`
	RoomID    int64       `json:"room_id"`
	EventID   int64       `json:"event_id"`
	StartTime int64       `json:"start_time"`

	Room          *room.Room
	c             *handler.Context
	uc            mrpc.UserContext
	bubble        *bubble.Simple
	GiftsMap      map[int64]*gift.Gift
	user          *liveuser.Simple
	uv            *vip.UserVip
	needNotifyAll bool
}

// NewReward 初始化直播间赠送礼物函数
// room 可选
func NewReward(eventID, roomID, userID int64, gifts []GiftParam, room *room.Room) *RewardParam {
	return &RewardParam{
		UserID:  userID,
		Gifts:   gifts,
		RoomID:  roomID,
		EventID: eventID,
		Room:    room,
	}
}

func newRewardParam(c *handler.Context) (*RewardParam, error) {
	var param RewardParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 || param.RoomID <= 0 || len(param.Gifts) == 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	param.uc = c.UserContext()
	return &param, err
}

// BuildRewardParam 构建发放参数
func (param *RewardParam) BuildRewardParam() error {
	var err error
	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrCannotFindUser
	}

	// 有可能 Room 会传进来
	if param.Room == nil {
		param.Room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if param.Room == nil {
			return actionerrors.ErrCannotFindRoom
		}
	}

	var gifts []int64
	for _, g := range param.Gifts {
		gifts = append(gifts, g.GiftID)
	}
	param.GiftsMap, err = gift.FindGiftMapByGiftIDs(gifts)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// TODO: 需支持同一礼物多次发送
	if len(param.GiftsMap) != len(gifts) {
		return actionerrors.ErrNotFound("无法找到指定礼物")
	}

	_, uv, err := param.findUserStatus()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 总是获取贵族状态来获取贵族气泡和计算用户的直播间等级经验值
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}

	param.bubble, err = userappearance.FindMessageBubble(param.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}

func (param *RewardParam) findUserStatus() (userstatus.GeneralStatus, *vip.UserVip, error) {
	if param.c == nil {
		// NOTICE: (*struct)(nil) 不等于 nil
		return userstatus.UserGeneral(param.UserID, nil)
	}
	return userstatus.UserGeneral(param.UserID, param.c)
}

// SendActivityReward 发送活动奖励
func (param *RewardParam) SendActivityReward() error {
	for _, giftValue := range param.Gifts {
		f := logger.Fields{
			"room_id":  param.Room.RoomID,
			"gift_id":  giftValue.GiftID,
			"event_id": param.EventID,
		}

		if err := param.SendReward(giftValue, param.Room); err != nil {
			logger.WithFields(f).Error(err)
			continue
		}
		logger.WithFields(f).Info("房间活动奖励发放成功")
	}
	return nil
}

// SendReward 给某个房间发奖
func (param *RewardParam) SendReward(sendGift GiftParam, room *room.Room) error {
	var (
		r   *userapi.BalanceResp
		err error
	)
	switch param.GiftsMap[sendGift.GiftID].Type {
	case gift.TypeRebate:
		r, err = userapi.SendRebateGift(param.UserID, room.CreatorID,
			sendGift.GiftID, sendGift.GiftNum, room.Status.OpenLogID, userapi.NewUserContext(nil))
	case gift.TypeFree:
		// 免费礼物不调用 rpc
	default:
		r, err = userapi.SendRewardGift(param.UserID, room.CreatorID,
			sendGift.GiftID, sendGift.GiftNum, room.Status.OpenLogID)
	}
	if err != nil {
		if errCli, ok := err.(*mrpc.ClientError); ok {
			logger.Debug(errCli)
		} else {
			logger.Error(err)
		}
		return err
	}

	g := param.GiftsMap[sendGift.GiftID]
	lg := livegifts.NewLiveGifts(room.OID, room.RoomID, param.user, nil).
		SetGift(g, int64(sendGift.GiftNum)).
		SetRoomOpenStatus(room.IsOpen())
	if r != nil {
		lg.SetTransactionIDs(r.TransactionID)
	}
	lg.OID, err = livegifts.UpdateSave(lg, nil, primitive.NilObjectID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	goutil.Go(func() {
		g := param.GiftsMap[sendGift.GiftID]
		// 房间榜单
		param.addRevenueRank(room, g, sendGift.GiftNum)
		// 增加 PK 值
		param.addPK(room, g, sendGift.GiftNum)
		// 粉丝亲密度
		param.addMedalPoint(room, g, sendGift.GiftNum)
		// 直播间经验
		param.addUserContribution(room, g, sendGift.GiftNum)
		// 送礼消息
		// TODO: 送礼消息、送礼飘屏和 PK 后续需要整合到一起，改成只使用一次 userapi.BroadcastMany
		param.notifyBroadcast(lg)
		// 送礼飘屏
		param.notifyBroadcastAll(room, g, sendGift.GiftNum)
		// 活动榜单
		param.addActivityRank(g, sendGift.GiftNum)
		// 主播个人场
		param.addLiveShow(room, g, sendGift.GiftNum)
	})
	return nil
}

func (param *RewardParam) addRevenueRank(r *room.Room, g *gift.Gift, num int) {
	price := g.Price * int64(num)
	var point int64
	if g.AllowPointAddRank() {
		point = g.Point * int64(num)
	}
	score := price + point
	if score == 0 {
		return
	}
	err := roomsrank.AddRevenue(r.RoomID, param.UserID, score, goutil.IntToBool(r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(r.CreatorID, r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	if price == 0 {
		err = liverevenues.AddPoint(param.UserID, r.OID, r.RoomID, point)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return
	}
	err = liverevenues.AddGiftRevenue(param.UserID, r.OID, r.RoomID, price, point)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = r.ReceiveGift(num, g.Price)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *RewardParam) addPK(r *room.Room, gift *gift.Gift, num int) {
	score, freeScore := gift.PKScores(num)
	res, err := livepk.AddPKScore(r, param.UserID, score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(res) == 0 {
		return
	}
	err = userapi.BroadcastMany(res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// NOTICE: 奖励送出的礼物不需要发送送礼人（猫耳娘的零钱袋）粉丝勋章获取、升级消息
func (param *RewardParam) addMedalPoint(room *room.Room, gift *gift.Gift, num int) {
	if room.Medal == nil {
		return
	}
	medalPoint := gift.MedalPoint(num)
	if medalPoint == 0 {
		return
	}
	medalParam := livemedal.AddPointParam{
		RoomOID:   room.OID,
		RoomID:    room.RoomID,
		CreatorID: room.CreatorID,
		UserID:    param.UserID,
		UV:        param.uv,
		MedalName: room.Medal.Name,
		Type:      livemedal.TypeGiftAddMedalPoint,
		PointAdd:  medalPoint,
		Scene:     livemedalpointlog.SceneTypePayGift,
	}
	_, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *RewardParam) addUserContribution(room *room.Room, g *gift.Gift, num int) {
	// 白给礼物不加经验值
	if g.Type == gift.TypeRebate {
		return
	}

	// 1 钻石 = 10 经验
	pointAdd := g.Price * int64(num) * 10
	if pointAdd == 0 {
		return
	}
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.UserID, room.RoomID, room.CreatorUsername, userstatus.FromNormal, param.uv)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *RewardParam) notifyBroadcast(lg *livegifts.LiveGift) {
	// 发送房间送礼消息
	notify := lg.RoomMessage()
	if lg.Gift.ComboFlags(int(lg.GiftNum)).IsSet(gift.ComboFlagEffect) {
		notify.Combo = &livegifts.Combo{
			ID:           lg.OID.Hex(),
			EffectURL:    lg.Gift.ComboEffect,
			WebEffectURL: lg.Gift.WebComboEffect,
			Num:          int(lg.GiftNum),
			RemainTime:   0,
		}
	}
	err := userapi.Broadcast(lg.RoomID, notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *RewardParam) notifyBroadcastAll(room *room.Room, g *gift.Gift, num int) {
	// TODO: 飘屏判断需要优化，待连击飘屏统一判断
	// needNotifyAll 是 true 的情况下一定会飘屏
	if !param.needNotifyAll && g.Price*int64(num) < gift.ComboNotifyMinPrice {
		return
	}

	nb := gift.NotifyBuilder{
		RoomID:          room.RoomID,
		CreatorUsername: room.CreatorUsername,
		User:            param.user,
		Gift:            g,
		GiftNum:         num,
	}
	np := nb.Build()
	err := userapi.BroadcastAll(np)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *RewardParam) addActivityRank(g *gift.Gift, num int) {
	rank.
		NewSyncParam(param.RoomID, param.UserID, param.Room.CreatorID).
		SetGift(g, num).
		SendLiveActivity(param.uc)
}

func (param *RewardParam) addLiveShow(room *room.Room, g *gift.Gift, num int) {
	// 目前个人场不统计猫耳娘送礼
}
