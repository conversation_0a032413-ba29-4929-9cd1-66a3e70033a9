package activityreward

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewActivityCronRewardParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before := goutil.TimeNow().Add(-time.Second).Unix()
	after := goutil.TimeNow().Add(time.Minute).Unix()
	c := handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": 12,
		"gifts": []GiftParam{{GiftID: existGiftID, GiftNum: 1}}, "start_time": after})
	_, err := newActivityCronRewardParam(c)
	assert.EqualError(err, "活动未开始")

	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": 12,
		"gifts": []GiftParam{{GiftID: existGiftID, GiftNum: 1}}, "start_time": before})
	p, err := newActivityCronRewardParam(c)
	require.NoError(err)
	assert.NotEmpty(p.GiftsMap[existGiftID].Name)
	assert.Nil(p.uv)
	assert.Equal(existGiftID, p.GiftsMap[existGiftID].GiftID)
	assert.Equal(existGiftID, p.Gifts[0].GiftID)
	assert.Equal(1, p.Gifts[0].GiftNum)
	assert.Equal(int64(12), p.UserID)

	var noble7UserID int64 = 3457114 // 神话测试账号
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": noble7UserID,
		"gifts": []GiftParam{{GiftID: existGiftID, GiftNum: 1}}, "start_time": before})
	p, err = newActivityCronRewardParam(c)
	require.NoError(err)
	assert.Equal(noble7UserID, p.UserID)
}

func TestBackpackAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := &room.Room{
		Helper: room.Helper{
			RoomID:    172842330,
			CreatorID: 3457111,
			Status:    room.Status{Open: 1},
		},
	}
	param := RewardParam{
		UserID: 12,
	}

	now := goutil.TimeNow()
	keyList := []string{
		roomsrank.Key(r.RoomID, roomsrank.RankTypeCurrent, now),
		roomsrank.Key(r.RoomID, roomsrank.RankTypeHourly, now),
		roomsrank.Key(r.RoomID, roomsrank.RankTypeWeek, now),
	}
	require.NoError(service.Redis.Del(keyList...).Err())
	g := &gift.Gift{
		GiftID:         125,
		Name:           "test",
		Icon:           "test",
		EffectDuration: 0,
		NotifyDuration: 0,
		Price:          10,
	}
	require.NoError(service.Redis.Del(keyList...).Err())
	assert.NotPanics(func() { param.addRevenueRank(r, g, 1) })
	// 通过判断键是否存在来看榜单是否添加成功
	val, err := service.Redis.Del(keyList...).Result()
	require.NoError(err)
	assert.Equal(int64(3), val)

	g.Attr.Set(gift.AttrPointAddRank)
	g.GiftID = 301
	g.Point = 1
	require.NoError(service.Redis.Del(keyList...).Err())
	assert.NotPanics(func() { param.addRevenueRank(r, g, 1) })
	val, err = service.Redis.Del(keyList...).Result()
	require.NoError(err)
	assert.Equal(int64(3), val)
}
