package activityreward

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const existGiftID int64 = 8

var imRedis *redis.Client

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)

	var err error
	redisConf := serviceredis.Config{
		Addr: "redis.srv.maoer.co:6379",
		DB:   106,
	}
	imRedis, err = serviceredis.NewRedisClient(&redisConf)
	if err != nil {
		logger.Fatal(err)
	}

	m.Run()
}

func TestNewRewardParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{"user_id": 10, "room_id": 22489473, "gifts": []GiftParam{{GiftID: existGiftID, GiftNum: 1}}}
	c := handler.NewTestContext(http.MethodPost, "", false, param)
	p, err := newRewardParam(c)
	require.NoError(err)
	err = p.BuildRewardParam()
	require.NoError(err)
	assert.NotEmpty(p.GiftsMap[existGiftID].Name)
	assert.Nil(p.uv)
	assert.Equal(existGiftID, p.GiftsMap[existGiftID].GiftID)
	assert.Equal(existGiftID, p.Gifts[0].GiftID)
	assert.Equal(1, p.Gifts[0].GiftNum)
	assert.Equal(int64(10), p.UserID)

	var noble7UserID int64 = 3457114 // 神话测试账号
	param["user_id"] = noble7UserID
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	p, err = newRewardParam(c)
	require.NoError(err)
	err = p.BuildRewardParam()
	require.NoError(err)
	assert.Equal(30, p.uv.Info.MedalNum)
	assert.Equal(noble7UserID, p.UserID)
}

func TestRewardParamAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count := 0
	cancel := mrpc.SetMock("im://broadcast/many", func(interface{}) (interface{}, error) {
		count++
		return "success", nil
	})
	defer cancel()
	r := new(room.Room)
	param := RewardParam{}
	param.addPK(r, &gift.Gift{}, 1)
	assert.Zero(count)
	param.addPK(r, &gift.Gift{Price: 1}, 1)
	assert.Zero(count)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"fighters": bson.M{"$exists": false}})
	require.NoError(err)
	var lp livepk.LivePK
	err = col.FindOne(ctx,
		bson.M{"status": livepk.PKRecordStatusFighting}).Decode(&lp)
	require.NoError(err)
	r.RoomID = lp.Fighters[0].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(r.RoomID),
		"test", time.Second).Err())
	param.addPK(r, &gift.Gift{Price: 1}, 1)
	assert.Equal(1, count)
}

func TestNotifyBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	watcher := tutil.NewPubSub(
		imRedis.Subscribe(keys.KeyIMPubSub2.Format(2, 0), keys.KeyIMPubSub2.Format(2, 1)),
	)
	defer watcher.Close()
	r := &room.Room{
		Helper: room.Helper{
			RoomID:          172842330,
			CreatorID:       3457111,
			CreatorUsername: "小蜜蜂",
		},
	}
	param := RewardParam{
		UserID: 12,
		user: &liveuser.Simple{
			UID:      12,
			Username: "bless",
		},
	}
	g := &gift.Gift{
		GiftID:         125,
		Name:           "test",
		Icon:           "test",
		EffectDuration: 0,
		NotifyDuration: 0,
		Price:          1000,
		Comboable:      1,
		ComboEffect:    "https://test.svga",
	}
	lg := livegifts.NewLiveGifts(r.OID, r.RoomID, param.user, param.bubble).
		SetGift(g, 1)
	require.NotPanics(func() { param.notifyBroadcast(lg) })
	// 接收消息
	v := watcher.Receive()
	logger.Debug(v)
	var msg map[string]interface{}
	_ = json.Unmarshal([]byte(v), &msg)
	rec, ok := msg["payload"].(map[string]interface{})
	require.True(ok)
	assert.True(rec["type"] == "gift" && rec["event"] == "send" && rec["room_id"] == float64(172842330))
	assert.NotNil(rec["combo"])
}

func TestSendGiftNotifyBroadcastAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	watcher := tutil.NewPubSub(
		imRedis.Subscribe(keys.KeyIMPubSub2.Format(2, 0), keys.KeyIMPubSub2.Format(2, 1)),
	)
	defer watcher.Close()
	r := &room.Room{
		Helper: room.Helper{
			RoomID:          172842330,
			CreatorID:       3457111,
			CreatorUsername: ">",
		},
	}
	param := RewardParam{
		UserID: 12,
		user: &liveuser.Simple{
			UID:      12,
			Username: "<",
		},
	}
	b, err := bubble.AllBubbles()
	require.NoError(err)
	require.NotEmpty(b)
	g := &gift.Gift{
		GiftID:         125,
		Name:           "test",
		Icon:           "test",
		EffectDuration: 0,
		NotifyDuration: 0,
		Price:          21000,
		Comboable:      1,
		ComboEffect:    "https://test.svga",
		NotifyBubbleID: b[0].BubbleID,
	}
	require.NotPanics(func() { param.notifyBroadcastAll(r, g, 1) })
	// 接收消息
	assert.NoError(watcher.ReceiveUntil(func(payload string) (bool, error) {
		var msg map[string]interface{}
		_ = json.Unmarshal([]byte(payload), &msg)
		rec, ok := msg["payload"].(map[string]interface{})
		if ok && rec["type"] == "notify" && rec["notify_type"] == "gift" &&
			rec["event"] == "send" && rec["room_id"] == float64(172842330) {
			assert.Equal(`<font color="#FFFFFF"><b>&lt;</b></font> `+
				`<font color="#FFFFFF">给</font> `+
				`<font color="#FFFFFF"><b>&gt;</b></font> `+
				`<font color="#FFFFFF">送出</font> `+
				`<font color="#FFFFFF"><b>1 个test</b></font>`+
				`<font color="#FFFFFF">，快来围观吧~</font>`, rec["message"])
			assert.NotNil(rec["notify_bubble"])
			return true, nil
		}
		return false, nil
	}))

	g.NotifyMessage = "test"
	require.NotPanics(func() { param.notifyBroadcastAll(r, g, 1) })
	// 接收消息
	assert.NoError(watcher.ReceiveUntil(func(payload string) (bool, error) {
		var msg map[string]interface{}
		_ = json.Unmarshal([]byte(payload), &msg)
		rec, ok := msg["payload"].(map[string]interface{})
		if ok && rec["message"] == "test" {
			return true, nil
		}
		return false, nil
	}))
}

func TestRewardParam_addUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rewardParam := RewardParam{
		RoomID: 22489473,
		UserID: MaoerWalletUserID,
		Room: &room.Room{
			Helper: room.Helper{
				RoomID:          22489473,
				CreatorID:       3457114,
				CreatorUsername: "神话",
			},
		},
	}

	u, err := liveuser.Find(MaoerWalletUserID)
	require.NoError(err)
	require.NotNil(u)
	point := u.Contribution + 10
	// 普通礼物加经验
	rewardParam.addUserContribution(rewardParam.Room, &gift.Gift{GiftID: 1, Price: 1}, 1)
	u, err = liveuser.Find(MaoerWalletUserID)
	require.NoError(err)
	assert.Equal(point, u.Contribution)

	// 白给礼物不加经验
	rewardParam.addUserContribution(rewardParam.Room, &gift.Gift{GiftID: 1, Price: 1, Type: gift.TypeRebate}, 1)
	u, err = liveuser.Find(MaoerWalletUserID)
	require.NoError(err)
	assert.Equal(point, u.Contribution)

	// 免费礼物不加经验
	rewardParam.addUserContribution(rewardParam.Room, &gift.Gift{GiftID: 1, Point: 1, Type: gift.TypeFree}, 1)
	u, err = liveuser.Find(MaoerWalletUserID)
	require.NoError(err)
	assert.Equal(point, u.Contribution)
}

func TestRewardParam_addActivityRank(t *testing.T) {
	assert := assert.New(t)

	rewardParam := RewardParam{
		uc:     mrpc.UserContext{},
		RoomID: 22489473,
		UserID: 3457114,
		Room: &room.Room{
			Helper: room.Helper{
				RoomID:          22489473,
				CreatorID:       3457114,
				CreatorUsername: "神话",
			},
		},
	}

	assert.NotPanics(func() {
		rewardParam.addActivityRank(&gift.Gift{GiftID: 1}, 1)
	})
}
