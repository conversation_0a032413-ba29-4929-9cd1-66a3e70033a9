package activity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func test157Data(t *testing.T) func() {
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	_, err := livelog.Collection().InsertMany(ctx, []interface{}{
		livelog.Record{RoomID: 170624474, CreatorID: 3457175, CatalogID: 115, StartTime: 1620573139601, EndTime: 1620616339601, Duration: 43200000},
		livelog.Record{RoomID: 343348035, CreatorID: 9074699, CatalogID: 105, StartTime: 1620383839609, EndTime: 1620383882172, Duration: 42563},
		livelog.Record{RoomID: 343348035, CreatorID: 9074699, CatalogID: 116, StartTime: 1620384127726, EndTime: 1620384326663, Duration: 198937},
		livelog.Record{RoomID: 343348035, CreatorID: 9074699, CatalogID: 105, StartTime: 1620384332747, EndTime: 1620387267499, Duration: 2934752},
		livelog.Record{RoomID: 173076071, CreatorID: 3457162, CatalogID: 122, StartTime: 1620387666798, EndTime: 1620387826258, Duration: 159460},
	})
	require.NoError(err)

	_, err = room.Collection().UpdateMany(ctx,
		bson.M{"creator_id": bson.M{"$in": []int64{3457175, 9074699, 3457162}}},
		bson.M{"$unset": bson.M{"activity_catalog_id": 0}},
	)

	require.NoError(err)

	return func() {
		st := event157PrepareStartTime
		et := event157PrepareEndTime
		_, err = livelog.Collection().DeleteMany(ctx, bson.M{"start_time": bson.M{"$gte": st}, "end_time": bson.M{"$lt": et}})
		require.NoError(err)
		cancel()
	}
}

func TestAction157ActivityPrepare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clear := test157Data(t)
	defer clear()

	c := handler.NewTestContext("POST", "", true, map[string]int64{
		"event_id": usersrank.EventIDSuperFanParty,
	})
	_, err := ActionActivityPrepare(c)
	require.NoError(err)

	all, err := room.FindAll(bson.M{"creator_id": bson.M{"$in": []int64{3457175, 9074699, 3457162}}},
		options.Find().SetSort(bson.M{"activity_catalog_id": 1}),
	)
	require.NoError(err)
	require.Len(all, 3)
	assert.Equal(int64(9074699), all[0].CreatorID)
	assert.Equal(int64(105), all[0].ActivityCatalogID)
	assert.Equal(int64(3457175), all[1].CreatorID)
	assert.Equal(int64(115), all[1].ActivityCatalogID)
	assert.Equal(int64(3457162), all[2].CreatorID)
	assert.Equal(int64(122), all[2].ActivityCatalogID)
}
