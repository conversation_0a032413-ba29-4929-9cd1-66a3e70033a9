package activity

import (
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	event138SignUpRewardScore = 40000 // 音乐征集签约奖励
)

// ActivityKeyExpire15Days 活动相关数据过期时间
var ActivityKeyExpire15Days = time.Hour * 24 * 15

// ActionRankIncrease 活动额外添加分数
/**
 * @api {post} /api/v2/admin/activity/rank/increase 活动额外添加分数
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} event_id 活动 ID
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionRankIncrease(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		EventID int64 `form:"event_id" json:"event_id"`
		UserID  int64 `form:"user_id" json:"user_id"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	startTime, endTime, err := activity.GetEventTime(param.EventID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if startTime == 0 || endTime == 0 {
		return nil, actionerrors.ErrNotFound("没有找到活动")
	}
	nowUnix := goutil.TimeNow().Unix()
	if nowUnix < startTime {
		return nil, actionerrors.NewErrForbidden("活动未开始")
	}
	if nowUnix > endTime {
		return nil, actionerrors.NewErrForbidden("活动已结束")
	}

	var score int64
	switch param.EventID {
	case usersrank.EventIDListenTogether:
		if err := addScore(param.EventID, param.UserID, event138SignUpRewardScore, endTime); err != nil {
			return nil, err
		}
	default:
		return nil, actionerrors.ErrParams
	}

	adminLogs := userapi.NewAdminLogBox(c)
	intro := fmt.Sprintf("活动额外添加分数, 用户 ID: %d, 活动 ID: %d, 添加分数 %d", param.UserID, param.EventID, score)
	adminLogs.AddAdminLog(intro, userapi.CatalogAdditionalScore)

	if err := adminLogs.Send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return "success", nil
}

func addScore(eventID, userID, score, eventEndTime int64) error {
	rankKey := usersrank.ActivityEventID(eventID)
	key := keys.KeyAdditionalScoreList1.Format(eventID)
	isExists, err := service.Redis.SIsMember(key, userID).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isExists {
		return actionerrors.NewErrForbidden(fmt.Sprintf("用户 ID: %d 重复添加", userID))
	}
	pipe := service.Redis.TxPipeline()
	pipe.ZIncrBy(rankKey, float64(score), strconv.FormatInt(userID, 10))
	// 计数统计
	pipe.SAdd(key, userID)
	rankCmd := pipe.TTL(rankKey)
	keyCmd := pipe.TTL(key)
	_, err = pipe.Exec()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	rankTTL := rankCmd.Val()
	keyTTL := keyCmd.Val()
	var ttlPipe redis.Pipeliner
	if rankTTL == -1 || keyTTL == -1 {
		ttlPipe = service.Redis.Pipeline()
		expireTime := time.Unix(eventEndTime, 0).Add(ActivityKeyExpire15Days).Unix()
		if rankTTL == -1 {
			liveserviceredis.ExpireAt(ttlPipe, rankKey, time.Unix(expireTime, 0))
		}
		if keyTTL == -1 {
			liveserviceredis.ExpireAt(ttlPipe, key, time.Unix(expireTime, 0))
		}
		_, err = ttlPipe.Exec()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}
