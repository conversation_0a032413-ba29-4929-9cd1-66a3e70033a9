package activity

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRankIncrease(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/api/v2/admin/activity/rank/increase", true,
		map[string]int64{
			"event_id": usersrank.EventIDListenTogether,
			"user_id":  12,
		})
	rankKey := usersrank.ActivityEventID(usersrank.EventIDListenTogether)
	key := keys.KeyAdditionalScoreList1.Format(usersrank.EventIDListenTogether)
	require.NoError(service.Redis.Del(rankKey, key).Err())

	startTime := goutil.TimeNow().Unix() + 10000
	endTime := startTime + 10
	updateTime := func() {
		err := service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventIDListenTogether).
			Updates(map[string]interface{}{"start_time": startTime, "end_time": endTime}).Error
		require.NoError(err)
	}
	updateTime()
	service.Cache5Min.Flush()
	_, err := ActionRankIncrease(c)
	assert.EqualError(err, "活动未开始")

	startTime = 1
	endTime = 100
	updateTime()
	c = handler.NewTestContext("POST", "/api/v2/admin/activity/rank/increase", true,
		map[string]int64{
			"event_id": usersrank.EventIDListenTogether,
			"user_id":  12,
		})
	service.Cache5Min.Flush()
	_, err = ActionRankIncrease(c)
	assert.EqualError(err, "活动已结束")

	endTime += goutil.TimeNow().Unix() + 100
	updateTime()
	c = handler.NewTestContext("POST", "/api/v2/admin/activity/rank/increase", true,
		map[string]int64{
			"event_id": usersrank.EventIDListenTogether,
			"user_id":  12,
		})
	service.Cache5Min.Flush()
	_, err = ActionRankIncrease(c)
	require.NoError(err)
	score, err := service.Redis.ZScore(rankKey, strconv.FormatInt(12, 10)).Result()
	require.NoError(err)
	assert.Equal(float64(40000), score)

	isExist, err := service.Redis.SIsMember(key, 12).Result()
	require.NoError(err)
	assert.True(isExist)

	c = handler.NewTestContext("POST", "/api/v2/admin/activity/rank/increase", true,
		map[string]int64{
			"event_id": usersrank.EventIDListenTogether,
			"user_id":  12,
		})
	_, err = ActionRankIncrease(c)
	assert.EqualError(err, "用户 ID: 12 重复添加")
}
