package activity

import (
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/activity/wish"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler return handler
func Handler() handler.Handler {
	return handler.Handler{
		Name:        "activity",
		SubHandlers: []handler.Handler{},
		Actions: map[string]*handler.Action{
			"widget": handler.NewAction(handler.GET, rank.ActionWidget, false),

			"wish/buy":    handler.NewAction(handler.POST, wish.ActionWishBuy, true),
			"wish/record": handler.NewAction(handler.GET, wish.ActionWishRecord, true),
			"wish/info":   handler.NewAction(handler.GET, wish.ActionWishInfo, false),
		},
	}
}
