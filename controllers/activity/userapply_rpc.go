package activity

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type activityApplyParam struct {
	UserID  int64 `json:"user_id"`
	EventID int64 `json:"event_id"`
}

const (
	percent20 = 0.2 // 20%
)

// ActionUserApply 添加活动报名用户
/**
 * @api {post} /rpc/activity/userapply 添加活动报名用户
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} event_id 活动 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": true
 *     }
 *
 * @apiError (500) {number} code 501010000
 * @apiError (500) {string} info 参数错误
 * */
func ActionUserApply(c *handler.Context) (handler.ActionResponse, error) {
	var param activityApplyParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID == 0 {
		return nil, actionerrors.ErrParams
	}

	switch param.EventID {
	case usersrank.EventIDHeartAdministration:
		// 限制报名资格的统计时段
		// TODO: 报名资格的统计时段放到数据库配置中
		st := goutil.TimeUnixMilli(1604160000000) // 2020-11-01 00:00:00.000
		et := goutil.TimeUnixMilli(1606752000000) // 2020-12-01 00:00:00.000
		if err := param.UserApply(st, et, catalogIDEmotion); err != nil {
			return false, err
		}
	default:
		if err := param.BaseUserApply(); err != nil {
			return false, err
		}
	}

	return true, nil
}

// UserApply 用户报名
func (p *activityApplyParam) UserApply(st, et goutil.TimeUnixMilli, catalogID int64) error {
	var extendedFields activity.ExtendedFields
	_, err := activity.GetEventWithExtendFields(p.EventID, &extendedFields)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if extendedFields.ApplyStartTime == 0 || extendedFields.ApplyEndTime == 0 {
		return actionerrors.ErrParamsMsg("活动不存在")
	}
	now := extendedFields.TimeNow().Unix()
	if now < extendedFields.ApplyStartTime {
		return actionerrors.ErrParamsMsg("报名未开始")
	}
	if now > extendedFields.ApplyEndTime {
		return actionerrors.ErrParamsMsg("报名已结束")
	}
	r, err := room.FindOne(bson.M{"creator_id": p.UserID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}

	scale, err := livelog.CountDuration(r.RoomID, catalogID, st, et)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if scale != nil && scale.Total != 0 && scale.Duration/scale.Total < percent20 {
		return actionerrors.ErrGlobalPopupPromptMsg(fmt.Sprintf("您在%s的直播时长过低，无法参加本次活动哦~", findLiveCatalogName(catalogID)))
	}

	key := activity.KeyUsersApplication(p.EventID)
	err = service.Redis.SAdd(key, p.UserID).Err()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func findLiveCatalogName(catalogID int64) string {
	liveCatalog, err := catalog.AllLiveCatalogsWithSubMap(true)
	if err != nil {
		logger.Error(err)
		return "活动分区"
	}
	return liveCatalog[catalogID].CatalogName + "分区"
}

func (p *activityApplyParam) BaseUserApply() error {
	var extendedFields activity.ExtendedFields
	_, err := activity.GetEventWithExtendFields(p.EventID, &extendedFields)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if extendedFields.ApplyStartTime == 0 || extendedFields.ApplyEndTime == 0 {
		return actionerrors.ErrParamsMsg("活动不存在")
	}
	now := extendedFields.TimeNow().Unix()
	if now < extendedFields.ApplyStartTime {
		return actionerrors.ErrParamsMsg("报名未开始")
	}
	if now >= extendedFields.ApplyEndTime {
		return actionerrors.ErrParamsMsg("报名已结束")
	}

	switch extendedFields.SubscribeType {
	case activity.SubscribeTypeUser:
		// NOTICE: 用户报名不用检查主播信息
	case activity.SubscribeTypeCreator:
		r, err := room.FindOne(bson.M{"creator_id": p.UserID}, &room.FindOptions{DisableAll: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			u, err := mowangskuser.FindByUserID(p.UserID)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if u == nil {
				return actionerrors.ErrCannotFindUser
			}
			// 当是主播报名时，没有查出房间信息且没有实名认证，返回未实名认证
			if (u.Confirm & mowangskuser.ConfirmCertificated) == 0 {
				return actionerrors.ErrUnrealNameAuthenticationUser
			}
			return actionerrors.ErrCannotFindRoom
		}
	default:
		return actionerrors.ErrParamsMsg("报名类型错误")
	}

	key := activity.KeyUsersApplication(p.EventID)
	err = service.Redis.SAdd(key, p.UserID).Err()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}
