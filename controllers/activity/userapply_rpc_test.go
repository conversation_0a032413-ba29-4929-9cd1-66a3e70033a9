package activity

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestHeartAdministration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	var applyTime struct {
		ApplyStartTime int64 `json:"apply_start_time"`
		ApplyEndTime   int64 `json:"apply_end_time"`
		SubscribeType  int   `json:"subscribe_type,omitempty"`
	}
	applyTime.ApplyStartTime = util.BeginningOfDay(now).Unix()
	applyTime.ApplyEndTime = util.BeginningOfDay(now.AddDate(0, 0, 1)).Unix()
	v, err := json.Marshal(applyTime)
	require.NoError(err)
	e := mevent.Simple{
		ID:             usersrank.EventIDHeartAdministration,
		ExtendedFields: string(v),
	}
	e.ExtendedFields = string(v)
	service.Cache5Min.Flush()
	keyEventTime := keys.KeyEventInfo1.Format(usersrank.EventIDHeartAdministration)
	service.Cache5Min.Set(keyEventTime, &e, 0)

	key := activity.KeyUsersApplication(usersrank.EventIDHeartAdministration)
	require.NoError(service.Redis.Del(key).Err())
	// 指定时间段内未直播过
	params := map[string]interface{}{
		"user_id":  223344,
		"event_id": usersrank.EventIDHeartAdministration,
	}
	c := handler.NewTestContext(http.MethodPost, "/", false, params)
	resp, err := ActionUserApply(c)
	require.NoError(err)
	assert.True(resp.(bool))
	exists, err := service.Redis.SIsMember(key, 223344).Result()
	require.NoError(err)
	assert.True(exists)
	// 参数错误
	params["event_id"] = 222212
	_, err = ActionUserApply(c)
	assert.Equal(actionerrors.ErrParams, err)
	params["user_id"] = 0
	_, err = ActionUserApply(c)
	assert.Equal(actionerrors.ErrParams, err)
	// 指定时间内直播过，放松分区直播的时长占比未达到要求
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = livelog.Collection().FindOneAndReplace(ctx, bson.M{"room_id": 223355, "catalog_id": catalogIDSleep},
		bson.M{
			"room_id":    223355,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2020, 11, 01, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2020, 11, 26, 0, 0, 0, 0, time.Local)),
			"duration":   180,
			"creator_id": 223355,
			"catalog_id": catalogIDSleep,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)
	exists, err = service.Redis.SIsMember(key, 223355).Result()
	require.NoError(err)
	assert.False(exists)
	_, err = livelog.Collection().UpdateOne(ctx, bson.M{"room_id": 223355, "catalog_id": catalogIDSleep},
		bson.M{"$set": bson.M{"duration": 10}})
	require.NoError(err)
	// 指定时间内直播过，放松分区直播的时长占比达到要求
	err = livelog.Collection().FindOneAndReplace(ctx, bson.M{"room_id": 223355, "catalog_id": catalogIDEmotion},
		bson.M{
			"room_id":    223355,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2020, 11, 01, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2020, 11, 26, 0, 0, 0, 0, time.Local)),
			"duration":   10,
			"creator_id": 223355,
			"catalog_id": catalogIDEmotion,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)
	params = map[string]interface{}{
		"user_id":  223355,
		"event_id": usersrank.EventIDHeartAdministration,
	}
	c = handler.NewTestContext(http.MethodPost, "/", false, params)
	resp, err = ActionUserApply(c)
	require.NoError(err)
	assert.True(resp.(bool))
	exists, err = service.Redis.SIsMember(key, 223355).Result()
	require.NoError(err)
	assert.True(exists)

	// 测试其它报名预约活动
	eventIDAnother := int64(160)
	require.NoError(service.DB.Table(mevent.TableName()).Where("id = ?", eventIDAnother).
		Update("extended_fields", string(v)).Error)
	key = activity.KeyUsersApplication(eventIDAnother)
	require.NoError(service.Redis.Del(key).Err())
	params = map[string]interface{}{
		"user_id":  223355,
		"event_id": eventIDAnother,
	}
	c = handler.NewTestContext(http.MethodPost, "/", false, params)
	resp, err = ActionUserApply(c)
	require.NoError(err)
	assert.True(resp.(bool))
	exists, err = service.Redis.SIsMember(key, 223355).Result()
	require.NoError(err)
	assert.True(exists)

	// 测试用户实名认证
	service.Cache5Min.Flush()
	applyTime.SubscribeType = 1
	v, err = json.Marshal(applyTime)
	require.NoError(err)
	require.NoError(service.DB.Table(mevent.TableName()).Where("id = ?", eventIDAnother).
		Update("extended_fields", string(v)).Error)
	params = map[string]interface{}{
		"user_id":  3456835,
		"event_id": eventIDAnother,
	}
	c = handler.NewTestContext(http.MethodPost, "/", false, params)
	resp, err = ActionUserApply(c)
	assert.False(resp.(bool))
	assert.Equal(actionerrors.ErrUnrealNameAuthenticationUser, err)

	// 测试无法找到该聊天室
	params = map[string]interface{}{
		"user_id":  248506,
		"event_id": eventIDAnother,
	}
	c = handler.NewTestContext(http.MethodPost, "/", false, params)
	resp, err = ActionUserApply(c)
	assert.False(resp.(bool))
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
}

// TestApplyCheckEvent 检查活动配置的错误
func TestApplyCheckEvent(t *testing.T) {
	assert := assert.New(t)

	defer service.Cache5Min.Flush()
	e := &mevent.Simple{ID: 123}
	key := keys.KeyEventInfo1.Format(e.ID)
	service.Cache5Min.Set(key, e, 0)
	p := activityApplyParam{
		EventID: e.ID,
	}
	err := p.UserApply(0, 0, 0)
	assert.Equal(actionerrors.ErrParamsMsg("活动不存在"), err)
	setApplyTime := func(st, et int64) {
		applyTime := map[string]int64{
			"apply_start_time": st,
			"apply_end_time":   et,
		}
		e.ExtendedFields = tutil.SprintJSON(applyTime)
	}
	now := goutil.TimeNow().Unix()
	setApplyTime(now+100, now+100)
	err = p.UserApply(0, 0, 0)
	assert.Equal(actionerrors.ErrParamsMsg("报名未开始"), err)
	setApplyTime(now-100, now-100)
	err = p.UserApply(0, 0, 0)
	assert.Equal(actionerrors.ErrParamsMsg("报名已结束"), err)
	setApplyTime(now-100, now+100)
	p.UserID = 987654321
	err = p.UserApply(0, 0, 0)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)
}
