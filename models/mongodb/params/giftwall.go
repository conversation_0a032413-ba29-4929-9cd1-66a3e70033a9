package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
)

// GiftWall 礼物墙
type GiftWall struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	OpenTime        int64                   `bson:"open_time,omitempty" json:"open_time,omitempty"` // 甄选礼物墙开放时间，单位：秒
	LabelIcon       string                  `bson:"label_icon" json:"label_icon"`                   // 即将冠名角标
	SponsorLevels   []*GiftWallSponsorLevel `bson:"sponsor_levels" json:"sponsor_levels"`           // 冠名标识配置
	NormalGiftWall  *GiftWallSponsor        `bson:"normal_gift_wall" json:"normal_gift_wall"`       // 普通礼物墙配置
	PremiumGiftWall *GiftWallSponsor        `bson:"premium_gift_wall" json:"premium_gift_wall"`     // 甄选礼物墙配置

	LabelIconURL string `bson:"-" json:"-"`
}

// GiftWallSponsor 礼物墙配置
type GiftWallSponsor struct {
	PromotionRevenueThreshold int64  `bson:"promotion_revenue_threshold" json:"promotion_revenue_threshold"` // 收益展示变色阈值，单位：钻石
	SponsorThreshold          int64  `bson:"sponsor_threshold" json:"sponsor_threshold"`                     // 获得礼物墙冠名标识阈值，单位：钻石
	DetailImage               string `bson:"detail_image" json:"detail_image"`                               // 礼物墙冠名详情

	DetailImageURL string `bson:"-" json:"-"`
}

// GiftWallSponsorLevel 冠名标识配置
type GiftWallSponsorLevel struct {
	TargetRevenue   int64  `bson:"target_revenue" json:"target_revenue"`       // 获得冠名标识目标金额，单位：钻石
	GiftMessageIcon string `bson:"gift_message_icon" json:"gift_message_icon"` // 送礼消息冠名图标
	GiftsIcon       string `bson:"gifts_icon" json:"gifts_icon"`               // 礼物栏冠名消息图标
	Text            string `bson:"text" json:"text"`                           // 发消息文案
	TextColor       string `bson:"text_color" json:"text_color"`               // 文案颜色

	GiftMessageIconURL string `bson:"-" json:"-"`
	GiftsIconURL       string `bson:"-" json:"-"`
}

// AfterLoad after load
func (gw *GiftWall) AfterLoad() {
	gw.LabelIconURL = storage.ParseSchemeURL(gw.LabelIcon)
	if gw.NormalGiftWall != nil {
		gw.NormalGiftWall.DetailImageURL = storage.ParseSchemeURL(gw.NormalGiftWall.DetailImage)
	}
	if gw.PremiumGiftWall != nil {
		gw.PremiumGiftWall.DetailImageURL = storage.ParseSchemeURL(gw.PremiumGiftWall.DetailImage)
	}
	for i := range gw.SponsorLevels {
		if gw.SponsorLevels[i] != nil {
			gw.SponsorLevels[i].GiftMessageIconURL = storage.ParseSchemeURL(gw.SponsorLevels[i].GiftMessageIcon)
			gw.SponsorLevels[i].GiftsIconURL = storage.ParseSchemeURL(gw.SponsorLevels[i].GiftsIcon)
		}
	}
}

// DefaultGiftWall 默认礼物墙配置
func DefaultGiftWall() *GiftWall {
	return &GiftWall{
		Key:       KeyGiftWall,
		LabelIcon: "oss://live/giftwall/label-icon.png",
		NormalGiftWall: &GiftWallSponsor{
			PromotionRevenueThreshold: 50000,
			SponsorThreshold:          60000,
			DetailImage:               "oss://live/giftwall/guide-image.png",
		},
		PremiumGiftWall: &GiftWallSponsor{
			PromotionRevenueThreshold: 50000,
			SponsorThreshold:          200000,
			DetailImage:               "oss://live/giftwall/guide-image.png",
		},
		SponsorLevels: []*GiftWallSponsorLevel{{
			TargetRevenue:   60000,
			GiftMessageIcon: "oss://live/giftwall/sponsor-normal.png",
			GiftsIcon:       "oss://live/giftwall/sponsor-self-normal.png",
			Text:            "由 TA 冠名的",
			TextColor:       "#FFFFFF",
		}, {
			TargetRevenue:   200000,
			GiftMessageIcon: "oss://live/giftwall/sponsor-high.png",
			GiftsIcon:       "oss://live/giftwall/sponsor-self-high.png",
			Text:            "由 TA 冠名的",
			TextColor:       "#FFF1AB",
		}},
	}
}

// FindGiftWall 查找礼物墙配置
func FindGiftWall() (*GiftWall, error) {
	wall := DefaultGiftWall()
	err := findParams(wall.Key, wall)
	wall.AfterLoad()
	return wall, err
}

// MatchSponsorLevel 匹配达标的冠名等级
func (gw *GiftWall) MatchSponsorLevel(revenue int64) *GiftWallSponsorLevel {
	var spLevel *GiftWallSponsorLevel
	for _, level := range gw.SponsorLevels {
		// 配置需要保证冠名等级根据收益门槛递增排列
		if revenue < level.TargetRevenue {
			break
		}
		spLevel = level
	}
	return spLevel
}
