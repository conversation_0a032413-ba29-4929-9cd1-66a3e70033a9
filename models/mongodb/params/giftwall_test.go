package params

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGiftWall_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(GiftWall{}, "_id", "key", "open_time", "label_icon", "sponsor_levels", "normal_gift_wall", "premium_gift_wall")
	kc.Check(GiftWallSponsorLevel{}, "target_revenue", "gift_message_icon", "gifts_icon", "text", "text_color")
	kc.Check(GiftWallSponsor{}, "promotion_revenue_threshold", "sponsor_threshold", "detail_image")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(GiftWall{}, "key", "open_time", "label_icon", "sponsor_levels", "normal_gift_wall", "premium_gift_wall")
	kc.Check(GiftWallSponsorLevel{}, "target_revenue", "gift_message_icon", "gifts_icon", "text", "text_color")
	kc.Check(GiftWallSponsor{}, "promotion_revenue_threshold", "sponsor_threshold", "detail_image")
}

func TestDefaultGiftWall(t *testing.T) {
	assert := assert.New(t)

	wall := DefaultGiftWall()
	assert.Equal(KeyGiftWall, wall.Key)
	assert.Equal("oss://live/giftwall/label-icon.png", wall.LabelIcon)
	assert.Equal(int64(50000), wall.PremiumGiftWall.PromotionRevenueThreshold)
	assert.Equal(int64(50000), wall.NormalGiftWall.PromotionRevenueThreshold)
	assert.Equal(int64(200000), wall.PremiumGiftWall.SponsorThreshold)
	assert.Equal(int64(60000), wall.NormalGiftWall.SponsorThreshold)
	assert.Equal("oss://live/giftwall/guide-image.png", wall.PremiumGiftWall.DetailImage)
	assert.Equal("oss://live/giftwall/guide-image.png", wall.NormalGiftWall.DetailImage)
	assert.Len(wall.SponsorLevels, 2)
}

func TestFindGiftWall(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	conf := GiftWall{
		Key:       KeyGiftWall,
		LabelIcon: "oss://test.png",
		NormalGiftWall: &GiftWallSponsor{
			PromotionRevenueThreshold: 100,
			SponsorThreshold:          100,
			DetailImage:               "oss://test.png",
		},
		SponsorLevels: []*GiftWallSponsorLevel{{
			TargetRevenue:   100,
			GiftsIcon:       "oss://test.png",
			GiftMessageIcon: "oss://test.png",
		}},
	}
	b, err := json.Marshal(conf)
	require.NoError(err)
	cacheKey := keys.KeyParams1.Format(conf.Key)
	require.NoError(service.LRURedis.Set(cacheKey, b, time.Minute).Err())

	wall, err := FindGiftWall()
	require.NoError(err)
	assert.Equal(conf.Key, wall.Key)
	assert.Equal(conf.LabelIcon, wall.LabelIcon)
	assert.Equal("https://static-test.missevan.com/test.png", wall.LabelIconURL)
	assert.Nil(wall.PremiumGiftWall)
	require.NotNil(wall.NormalGiftWall)
	assert.Equal(conf.NormalGiftWall.PromotionRevenueThreshold, wall.NormalGiftWall.PromotionRevenueThreshold)
	assert.Equal(conf.NormalGiftWall.SponsorThreshold, wall.NormalGiftWall.SponsorThreshold)
	assert.Equal(conf.NormalGiftWall.DetailImage, wall.NormalGiftWall.DetailImage)
	assert.Equal("https://static-test.missevan.com/test.png", wall.NormalGiftWall.DetailImageURL)
}

func TestGiftWall_MatchSponsorLevel(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 收益不达标
	gw := GiftWall{
		SponsorLevels: []*GiftWallSponsorLevel{{
			TargetRevenue: 100,
			Text:          "test1",
		}, {
			TargetRevenue: 200,
			Text:          "test2",
		}},
	}
	assert.Nil(gw.MatchSponsorLevel(50))

	// 收益达到一阶
	level := gw.MatchSponsorLevel(100)
	require.NotNil(level)
	assert.Equal(gw.SponsorLevels[0].Text, level.Text)

	// 收益达到二阶
	level = gw.MatchSponsorLevel(200)
	require.NotNil(level)
	assert.Equal(gw.SponsorLevels[1].Text, level.Text)
}
