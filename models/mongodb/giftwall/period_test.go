package giftwall

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindByPeriodOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testShowGiftIDs := []int64{1000}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()},
			"end_time":   bson.M{"$gt": now.Unix()},
		})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, &Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)

	gifts, err := FindByPeriodOID(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.Equal(gifts.ShowGiftIDs, testShowGiftIDs)
}

func createTestPeriodData(when time.Time, giftWallType int) (func(giftWallType int), error) {
	p := &Period{
		CreateTime:   when.Unix(),
		ModifiedTime: when.Unix(),
		StartTime:    when.Add(-time.Minute).Unix(),
		EndTime:      when.Add(time.Minute).Unix(),
		ShowGiftIDs:  testShowGiftIDs,
		Type:         giftWallType,
		Rewards: []*RewardInfo{
			{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
			{Threshold: 2, Type: RewardTypeAppearance, ElementID: 1000},
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().InsertOne(ctx, p)
	if err != nil {
		return nil, err
	}

	return cleanTestPeriodData, nil
}

func cleanTestPeriodData(giftWallType int) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{"show_gift_ids": testShowGiftIDs, "type": giftWallType})
	if err != nil {
		logger.Error(err)
	}
}

func TestPeriod_TotalNum(t *testing.T) {
	assert := assert.New(t)

	p := Period{
		ShowGiftIDs: []int64{1, 2, 3},
	}
	assert.Equal(int64(3), p.TotalNum())
}

func TestFindCurrentPeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cleanTestPeriodData(PeriodTypeNormal)
	p, err := findCurrentPeriod(now, PeriodTypeNormal)
	require.NoError(err)
	assert.Nil(p)

	cleanup, err := createTestPeriodData(now, PeriodTypeNormal)
	require.NoError(err)
	defer cleanup(PeriodTypeNormal)
	p, err = findCurrentPeriod(now, PeriodTypeNormal)
	require.NoError(err)
	assert.NotNil(p)
}

func TestCurrentPeriodInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	service.Cache5Min.Flush()
	cleanTestPeriodData(PeriodTypeNormal)
	p, err := CurrentPeriodInfo(now)
	require.NoError(err)
	assert.Nil(p)

	cleanup, err := createTestPeriodData(now, PeriodTypeNormal)
	require.NoError(err)
	defer cleanup(PeriodTypeNormal)
	service.Cache5Min.Flush()
	p, err = CurrentPeriodInfo(now)
	require.NoError(err)
	assert.NotNil(p)

	cacheP, err := CurrentPeriodInfo(now)
	require.NoError(err)
	assert.NotNil(cacheP)
	assert.Equal(p, cacheP)
}

func TestCurrentPeriodInfoByType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	service.Cache5Min.Flush()
	p, err := CurrentPeriodInfoByType(now, PeriodTypePremium)
	require.NoError(err)
	assert.Nil(p)

	cleanup, err := createTestPeriodData(now, PeriodTypePremium)
	require.NoError(err)
	defer cleanup(PeriodTypePremium)
	service.Cache5Min.Flush()
	p, err = CurrentPeriodInfoByType(now, PeriodTypePremium)
	require.NoError(err)
	assert.NotNil(p)

	cacheP, err := CurrentPeriodInfoByType(now, PeriodTypePremium)
	require.NoError(err)
	assert.NotNil(cacheP)
	assert.Equal(p, cacheP)
}

func TestDelPeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime := now.Unix()
	endTime := now.Add(PeriodDuration).Unix()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := PeriodCollection().InsertOne(ctx, bson.M{
		"start_time": startTime,
		"end_time":   endTime,
	})
	require.NoError(err)
	ok, err := DelPeriod(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.False(ok)

	_, err = PeriodCollection().UpdateOne(ctx,
		bson.M{"_id": res.InsertedID},
		bson.M{"$set": bson.M{"start_time": startTime + 1}},
	)
	require.NoError(err)
	ok, err = DelPeriod(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.True(ok)
}

func TestFindPeriodsByType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer cancel()
	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(PeriodDuration)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": bson.M{"$lt": endTime.Unix()},
		"end_time":   bson.M{"$gt": startTime.Unix()},
	})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, bson.M{
		"start_time":    startTime.Unix(),
		"end_time":      endTime.Unix(),
		"type":          PeriodTypePremium,
		"show_gift_ids": testShowGiftIDs,
	})
	require.NoError(err)

	ps, err := FindPeriodsByType(startTime, endTime, PeriodTypeNormal)
	require.NoError(err)
	assert.Empty(ps)

	ps, err = FindPeriodsByType(startTime, endTime, PeriodTypePremium)
	require.NoError(err)
	require.NotEmpty(ps)
	assert.Equal(res.InsertedID, ps[0].OID)

	ps, err = FindPeriodsByType(now.Add(-time.Hour), now, PeriodTypePremium)
	require.NoError(err)
	assert.Empty(ps)
}

func TestCreatePeriodWithType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer cancel()
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	err = CreatePeriodWithType(now, now.Add(PeriodDuration), PeriodTypePremium, testShowGiftIDs, []*RewardInfo{{Threshold: 1, Type: RewardTypeAppearance, ElementID: 2}}, []int64{233})
	require.NoError(err)

	var p *Period
	err = PeriodCollection().FindOne(ctx, bson.M{"show_gift_ids": testShowGiftIDs}).Decode(&p)
	require.NoError(err)
	require.NotNil(p)
	assert.Equal(now.Unix(), p.StartTime)
	assert.Equal(PeriodTypePremium, p.Type)
	require.NotEmpty(p.Rewards)
	assert.Equal(RewardInfo{Threshold: 1, Type: RewardTypeAppearance, ElementID: 2}, *p.Rewards[0])
	assert.Equal([]int64{233}, p.CreatorIDs)
}

func TestFindUnfinishedPeriodByShowGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{
		"show_gift_ids": []int64{1010101},
	})
	require.NoError(err)
	now := goutil.TimeNow()
	_, err = PeriodCollection().InsertMany(ctx, bson.A{
		bson.M{"show_gift_ids": []int64{1010101}, "end_time": now.Unix()},
		bson.M{"show_gift_ids": []int64{1010101}, "end_time": now.Add(time.Second).Unix()},
	})
	require.NoError(err)

	p, err := FindUnfinishedPeriodByShowGiftID(1010101)
	require.NoError(err)
	require.NotNil(p)
	assert.Greater(p.EndTime, now.Unix())

	p, err = FindUnfinishedPeriodByShowGiftID(909090)
	require.NoError(err)
	assert.Nil(p)
}

func TestUpdatePeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testShowGiftIDs := []int64{1000}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()},
			"end_time":   bson.M{"$gt": now.Unix()},
		})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, &Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
		Rewards: []*RewardInfo{
			{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
			{Threshold: 2, Type: RewardTypeAppearance, ElementID: 2},
		},
	})
	require.NoError(err)

	objectOID := res.InsertedID.(primitive.ObjectID)
	// 测试只更新上墙礼物
	err = UpdatePeriod(objectOID, []int64{2000, 3000}, []*RewardInfo{}, []int64{})
	require.NoError(err)
	// 断言更新了上墙礼物，没有更新奖励设置
	p, err := FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2000, 3000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
		{Threshold: 2, Type: RewardTypeAppearance, ElementID: 2},
	}, p.Rewards)

	// 测试只更新奖励设置
	err = UpdatePeriod(objectOID, []int64{}, []*RewardInfo{
		{Threshold: 3, Type: RewardTypeCustomGift, ElementID: 10},
		{Threshold: 4, Type: RewardTypeAppearance, ElementID: 20},
	}, []int64{})
	require.NoError(err)
	// 断言更新了奖励设置，没有更新上墙礼物
	p, err = FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2000, 3000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 3, Type: RewardTypeCustomGift, ElementID: 10},
		{Threshold: 4, Type: RewardTypeAppearance, ElementID: 20},
	}, p.Rewards)

	// 测试只更新礼物墙白名单
	err = UpdatePeriod(objectOID, []int64{}, []*RewardInfo{}, []int64{2333})
	require.NoError(err)
	// 断言更新了奖励设置，没有更新上墙礼物
	p, err = FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2333}, p.CreatorIDs)

	// 测试更新上墙礼物、奖励设置
	err = UpdatePeriod(objectOID, []int64{4000, 5000}, []*RewardInfo{
		{Threshold: 5, Type: RewardTypeCustomGift, ElementID: 100},
		{Threshold: 6, Type: RewardTypeAppearance, ElementID: 200},
	}, []int64{234})
	require.NoError(err)
	// 断言更新了上墙礼物、奖励设置
	p, err = FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{4000, 5000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 5, Type: RewardTypeCustomGift, ElementID: 100},
		{Threshold: 6, Type: RewardTypeAppearance, ElementID: 200},
	}, p.Rewards)
	assert.Equal([]int64{234}, p.CreatorIDs)
}

func TestPeriod_PrevPeriods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// mock 时间
	mockNow := time.Date(2025, 6, 6, 12, 0, 0, 0, time.UTC)
	cancel := goutil.SetTimeNow(func() time.Time { return mockNow })
	defer cancel()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := PeriodCollection()

	// 准备测试数据
	testPeriodOID1, _ := primitive.ObjectIDFromHex("684a50c4b13945dfe11b6179")
	testPeriodOID2, _ := primitive.ObjectIDFromHex("684a50c4b13945dfe11b6180")
	testPeriodOID3, _ := primitive.ObjectIDFromHex("684a50c4b13945dfe11b6181")

	// 清理本次插入的测试数据
	_, err := collection.DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{testPeriodOID1, testPeriodOID2, testPeriodOID3}}})
	require.NoError(err)

	now := goutil.TimeNow()
	todayUnix := goutil.BeginningOfWeek(goutil.BeginningOfDay(now)).Unix()
	yesterdayUnix := goutil.BeginningOfWeek(goutil.BeginningOfDay(now.AddDate(0, 0, -1))).Unix()

	// 插入测试数据
	testData := []any{
		Period{
			OID:        testPeriodOID1,
			CreateTime: now.Unix(),
			StartTime:  yesterdayUnix,
			EndTime:    todayUnix,
			Type:       PeriodTypeNormal,
		},
		Period{
			OID:        testPeriodOID2,
			CreateTime: now.Unix(),
			StartTime:  yesterdayUnix,
			EndTime:    todayUnix,
			Type:       PeriodTypePremium,
		},
		Period{
			OID:        testPeriodOID3,
			CreateTime: now.Unix(),
			StartTime:  yesterdayUnix,
			EndTime:    todayUnix,
		},
	}

	// 插入测试数据并验证
	result, err := collection.InsertMany(ctx, testData)
	require.NoError(err)
	assert.Len(result.InsertedIDs, 3)

	// 记录插入的 OID
	insertedOIDs := make([]primitive.ObjectID, 0, len(testData))
	for _, p := range testData {
		insertedOIDs = append(insertedOIDs, p.(Period).OID)
	}

	// 验证数据是否正确插入
	count, err := collection.CountDocuments(ctx, bson.M{"_id": bson.M{"$in": insertedOIDs}})
	require.NoError(err)
	assert.Equal(int64(3), count)

	// 测试用例：获取当天结束的周期
	currentPeriod := &Period{
		StartTime: todayUnix,
	}
	periods, err := currentPeriod.PrevPeriods()
	require.NoError(err)
	assert.Len(periods, 3)
	for _, p := range periods {
		assert.Equal(todayUnix, p.EndTime)
	}
}
