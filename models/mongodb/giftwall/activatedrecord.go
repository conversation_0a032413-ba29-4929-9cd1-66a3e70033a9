package giftwall

import (
	"fmt"
	"slices"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PromotionRevenueThreshold 收益展示变色阈值
const PromotionRevenueThreshold = 50000 // 单位: 钻石

// ActivatedRecord 直播间礼物墙点亮记录
type ActivatedRecord struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	RoomID       int64              `bson:"room_id"`
	CreatorID    int64              `bson:"creator_id"`
	PeriodOID    primitive.ObjectID `bson:"_period_id"`
	ShowGiftID   int64              `bson:"show_gift_id"`  // 上墙礼物 ID
	ActivatedNum int64              `bson:"activated_num"` // 对应上墙礼物的激活数量
	Revenue      int64              `bson:"revenue"`
}

// RecordCollection 返回 ActivatedRecord 的 Collection
func RecordCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_activated_records")
}

// AddRecord add activated record
func AddRecord(periodOID primitive.ObjectID, roomID, creatorID, showGiftID, addNum, addRevenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	nowUnix := goutil.TimeNow().Unix()
	incData := bson.M{
		"activated_num": addNum,
	}
	if addRevenue > 0 {
		// 免费礼物不计算价值
		incData["revenue"] = addRevenue
	}
	_, err := RecordCollection().UpdateOne(ctx,
		bson.M{
			"room_id":      roomID,
			"_period_id":   periodOID,
			"show_gift_id": showGiftID,
		},
		bson.M{
			"$inc": incData,
			"$set": bson.M{
				"modified_time": nowUnix,
			},
			"$setOnInsert": bson.M{
				"creator_id":  creatorID,
				"create_time": nowUnix,
			},
		}, options.Update().SetUpsert(true))
	return err
}

// ActiveGift 点亮礼物
func ActiveGift(r *room.Room, fromUserID, receiveGiftID, receiveRevenue int64, receiveNum int) (*userapi.BroadcastElem, error) {
	now := goutil.TimeNow()

	// 尝试点亮甄选礼物墙和普通礼物墙
	for _, periodType := range []int{PeriodTypePremium, PeriodTypeNormal} {
		result, activated, err := activeGiftByType(r, fromUserID, receiveGiftID, receiveRevenue, receiveNum, now, periodType)
		if err != nil {
			return nil, err
		}
		if activated {
			return result, nil
		}
	}

	// 如果没有点亮任何礼物墙，返回 nil
	return nil, nil
}

// activeGiftByType 根据礼物墙类型点亮礼物
func activeGiftByType(r *room.Room, fromUserID, receiveGiftID, receiveRevenue int64, receiveNum int, now time.Time, periodType int) (*userapi.BroadcastElem, bool, error) {
	// 获取指定类型的周期信息
	p, err := CurrentPeriodInfoByType(now, periodType)
	if err != nil {
		return nil, false, err
	}
	if p == nil {
		// 周期不存在
		return nil, false, nil
	}
	// 如果配置了白名单，需要当前直播间的主播在名单里面才能点亮并发放奖励
	if len(p.CreatorIDs) > 0 && !slices.Contains(p.CreatorIDs, r.CreatorID) {
		return nil, false, nil
	}
	// 判断礼物信息
	gs, err := FindGiftsByShowGiftIDs(p.ShowGiftIDs)
	if err != nil {
		return nil, false, err
	}
	var showGiftID int64
	for i := range gs {
		if goutil.HasElem(gs[i].EffectiveGiftIDs, receiveGiftID) {
			showGiftID = gs[i].ShowGiftID
			break
		}
	}
	if showGiftID == 0 {
		// 非有效礼物
		return nil, false, nil
	}
	// 添加点亮记录
	err = AddRecord(p.OID, r.RoomID, r.CreatorID, showGiftID, int64(receiveNum), receiveRevenue)
	if err != nil {
		return nil, false, err
	}
	// 添加用户点亮榜单
	err = AddUserRank(p.OID, r.RoomID, fromUserID, showGiftID, int64(receiveNum))
	if err != nil {
		return nil, false, err
	}
	activatedGiftKey := keys.KeyRoomGiftWallActivatedGiftIDs2.Format(r.RoomID, p.OID.Hex())
	pipe := service.Redis.TxPipeline()
	addCmd := pipe.SAdd(activatedGiftKey, showGiftID)
	countCmd := pipe.SCard(activatedGiftKey)
	activatedGiftKeyTTLCmd := pipe.TTL(activatedGiftKey)
	_, err = pipe.Exec()
	if err != nil {
		return nil, true, err
	}
	res := addCmd.Val()
	if res == 0 {
		// 礼物非首次点亮，不再发奖励和发消息
		return nil, true, nil
	}
	if ttl := activatedGiftKeyTTLCmd.Val(); ttl == -1 {
		// 7 days extend
		serviceredis.ExpireAt(service.Redis, activatedGiftKey, time.Unix(p.EndTime, 0).Add(7*24*time.Hour))
	}

	// 点亮总数量变化消息
	count := countCmd.Val()
	var message *userapi.BroadcastElem
	if periodType == PeriodTypeNormal {
		// 仅普通礼物墙点亮需要发消息
		message = newUpdateNotifyMessage(r, count, p.TotalNum())
	}
	// 发放礼物墙奖励
	for i := range p.Rewards {
		if count == p.Rewards[i].Threshold {
			switch p.Rewards[i].Type {
			case RewardTypeCustomGift:
				err = livecustom.AddRoomCustomGift(r.RoomID, p.Rewards[i].ElementID, now, time.Unix(p.EndTime, 0), livecustom.SourceDefault)
			case RewardTypeAppearance:
				aItem, err2 := appearance.FindOne(p.Rewards[i].ElementID, appearance.TypeNoSpecified)
				if err2 != nil {
					return message, true, err2
				}
				if aItem == nil {
					return message, true, fmt.Errorf("外观 (ID: %d) 奖励不存在", p.Rewards[i].ElementID)
				}
				err = userappearance.AddAppearance(r.CreatorID, 0, p.EndTime, aItem)
			case RewardTypeSticker:
				err = p.Rewards[i].sendRoomSticker(r.RoomID, now, p.EndTime)
			}
			if err != nil {
				return message, true, err // 发奖失败，不影响直播间消息
			}
			break
		}
	}
	return message, true, nil
}

// sendRoomSticker 发放直播间专属表情
func (reward *RewardInfo) sendRoomSticker(roomID int64, now time.Time, endTime int64) error {
	// 获取表情信息
	sticker, err := livesticker.FindSticker(reward.ElementID)
	if err != nil {
		return err
	}
	if sticker == nil {
		return fmt.Errorf("can not find sticker %d", reward.ElementID)
	}

	// 查找直播间专属表情包
	owner, err := livesticker.FindPackageOwner(livesticker.TypeRoom, roomID, 0, now)
	if err != nil {
		return err
	}

	// 如果没有专属表情包，创建一个
	var pkg *livesticker.Package
	if owner == nil {
		pkg, err = livesticker.AssignExclusivePackage(livesticker.TypeRoom, roomID, 0)
		if err != nil {
			return err
		}
	} else {
		pkg, err = livesticker.FindPackage(owner.PackageID)
		if err != nil {
			return err
		}
		if pkg == nil {
			return fmt.Errorf("can not find room sticker package")
		}
	}

	// 检查表情是否已在包中
	m, err := pkg.StickerMap(sticker.ID, now)
	if err != nil {
		return err
	}

	if m != nil {
		// 若表情过期时间小于礼物墙结束时间，则给该表情续期
		if m.ExpireTime != 0 && m.ExpireTime < endTime {
			err = livesticker.DB().Model(&livesticker.PackageStickerMap{}).Where("id = ?", m.ID).Updates(
				map[string]any{
					"expire_time": endTime,
				},
			).Error
		}
	} else {
		// 添加表情到包中，设置过期时间为礼物墙结束时间
		err = livesticker.DB().Create(&livesticker.PackageStickerMap{
			PackageID:  pkg.ID,
			StickerID:  sticker.ID,
			StartTime:  now.Unix(),
			ExpireTime: endTime,
		}).Error
	}

	return err
}

// updateMessage gift wall activated message
type updateMessage struct {
	Type     string           `json:"type"`
	Event    string           `json:"event"`
	RoomID   int64            `json:"room_id"`
	Time     int64            `json:"time"` // 秒级时间戳
	GiftWall *ActivatedDetail `json:"gift_wall"`
	Creator  *liveuser.Simple `json:"creator"`
}

// ActivatedDetail activated detail
type ActivatedDetail struct {
	ActivatedNum int64 `json:"activated_num"`
	TotalNum     int64 `json:"total_num"`
}

func newUpdateNotifyMessage(r *room.Room, activatedCount, totalNum int64) *userapi.BroadcastElem {
	u, err := mowangskuser.FindByUserID(r.CreatorID)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if u == nil {
		return nil
	}
	return &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: r.RoomID,
		Payload: updateMessage{
			Type:   liveim.TypeGiftWall,
			Event:  liveim.EventGiftWallUpdate,
			RoomID: r.RoomID,
			Time:   goutil.TimeNow().Unix(),
			GiftWall: &ActivatedDetail{
				ActivatedNum: activatedCount,
				TotalNum:     totalNum,
			},
			Creator: &liveuser.Simple{
				UID:      r.CreatorID,
				Username: r.CreatorUsername,
				IconURL:  u.IconURL,
			},
		},
	}
}

// ActivatedCountByRoomID 查询直播间上墙礼物的点亮次数
func ActivatedCountByRoomID(periodOID primitive.ObjectID, roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := RecordCollection().CountDocuments(ctx, bson.M{
		"_period_id": periodOID,
		"room_id":    roomID,
	})
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindActivatedDetail find giftwall activated detail
func FindActivatedDetail(roomID int64) (*ActivatedDetail, error) {
	period, err := CurrentPeriodInfo(goutil.TimeNow())
	if err != nil {
		return nil, err
	}
	if period == nil {
		return nil, nil
	}
	count, err := ActivatedCountByRoomID(period.OID, roomID)
	if err != nil {
		return nil, err
	}
	return &ActivatedDetail{ActivatedNum: count, TotalNum: period.TotalNum()}, nil
}

// FindOneRecord 获取点亮记录
func FindOneRecord(filter bson.M) (*ActivatedRecord, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	ar := new(ActivatedRecord)
	err := RecordCollection().FindOne(ctx, filter).Decode(ar)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return ar, nil
}

// ListRecord 获取点亮记录
func ListRecord(filter bson.M, findOptions ...*options.FindOptions) ([]*ActivatedRecord, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := RecordCollection().Find(ctx, filter, findOptions...)
	if err != nil {
		return nil, err
	}
	var res []*ActivatedRecord
	err = cur.All(ctx, &res)
	return res, err
}
