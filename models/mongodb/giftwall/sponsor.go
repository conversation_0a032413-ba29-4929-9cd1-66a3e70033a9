package giftwall

import (
	"context"
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Sponsor 礼物墙冠名用户信息
type Sponsor struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"_id,omitempty"`
	PeriodOID    primitive.ObjectID `bson:"_period_id" json:"_period_id"` // 获得冠名权的周期 ID（非生效周期）
	CreateTime   int64              `bson:"create_time" json:"create_time"`
	ModifiedTime int64              `bson:"modified_time" json:"modified_time"`
	RoomID       int64              `bson:"room_id" json:"room_id"`
	UserID       int64              `bson:"user_id" json:"user_id"`
	GiftID       int64              `bson:"gift_id" json:"gift_id"`             // 冠名的礼物
	StartTime    int64              `bson:"start_time" json:"start_time"`       // 冠名开始时间，秒级时间戳
	EndTime      int64              `bson:"end_time" json:"end_time"`           // 冠名结束时间，秒级时间戳
	ActivatedNum int64              `bson:"activated_num" json:"activated_num"` // 冠名用户点亮数量
	Revenue      int64              `bson:"revenue" json:"revenue"`             // 收益，单位：钻石
}

// SponsorCollection 返回 Sponsor 的 Collection
func SponsorCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_sponsor")
}

// KeyRoomSponsors 直播间许愿池冠名的 key
func KeyRoomSponsors(roomID int64) string {
	return keys.KeyRoomGiftWallSponsors1.Format(roomID)
}

// FindOngoingSponsorRevenue 查找用户直播间冠名消费
func FindOngoingSponsorRevenue(roomID, userID, giftID int64) (*int64, error) {
	sponsor, err := FindOngoingSponsor(roomID, giftID)
	if err != nil {
		return nil, err
	}
	if sponsor == nil || sponsor.UserID != userID {
		return nil, nil
	}
	return &sponsor.Revenue, nil
}

// FindOngoingSponsor 查找直播间指定礼物的冠名信息
func FindOngoingSponsor(roomID, giftID int64) (*Sponsor, error) {
	now := goutil.TimeNow()
	sponsorMap, err := FindOngoingRoomSponsorsWithCache(roomID, now)
	if err != nil {
		return nil, err
	}
	if sponsorMap == nil {
		return nil, nil
	}
	sponsor, ok := sponsorMap[giftID]
	if !ok {
		return nil, nil
	}
	if now.Unix() < sponsor.StartTime || now.Unix() >= sponsor.EndTime {
		return nil, nil
	}
	return sponsor, nil
}

// FindOngoingRoomSponsorsWithCache 查找直播间的冠名信息
// 返回结构：map[冠名礼物 ID]冠名信息
func FindOngoingRoomSponsorsWithCache(roomID int64, now time.Time) (map[int64]*Sponsor, error) {
	key := KeyRoomSponsors(roomID)
	readBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	toMap := func(sponsors []*Sponsor) map[int64]*Sponsor {
		return util.ToMap(sponsors, func(s *Sponsor) int64 {
			return s.GiftID
		})
	}
	var sponsors []*Sponsor
	if len(readBytes) != 0 {
		// 缓存命中读取
		err = json.Unmarshal(readBytes, &sponsors)
		if err != nil {
			return nil, err
		}
		return toMap(sponsors), nil
	}
	sponsors, err = findOngoingRoomSponsors(roomID, now)
	if err != nil {
		return nil, err
	}
	writeBytes, err := json.Marshal(sponsors)
	if err != nil {
		logger.WithField("room_id", roomID).Error(err)
		return toMap(sponsors), nil
	}
	err = service.LRURedis.Set(key, writeBytes, time.Minute).Err()
	if err != nil {
		logger.WithField("room_id", roomID).Error(err)
		// PASS
	}
	return toMap(sponsors), nil
}

func findOngoingRoomSponsors(roomID int64, now time.Time) ([]*Sponsor, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"room_id": roomID,
		"start_time": bson.M{
			"$lte": now.Unix(),
		},
		"end_time": bson.M{
			"$gt": now.Unix(),
		},
	}
	cur, err := SponsorCollection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var sponsors []*Sponsor
	if err = cur.All(ctx, &sponsors); err != nil {
		return nil, err
	}
	return sponsors, nil
}

// BatchAddSponsors 批量添加冠名用户
func BatchAddSponsors(sponsors []*Sponsor) error {
	if len(sponsors) == 0 {
		return nil
	}

	// 使用事务进行批量插入
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		// 分批插入，每批 500 条
		batchSize := 500
		docsLen := len(sponsors)
		for offset := 0; offset < docsLen; offset += batchSize {
			batchDocs := sponsors[offset:min(docsLen, offset+batchSize)]
			docs := goutil.SliceMap(batchDocs, func(sponsor *Sponsor) any {
				return sponsor
			})
			_, err := SponsorCollection().InsertMany(ctx, docs)
			if err != nil {
				return err
			}
		}
		return nil
	})

	return err
}

// ExistsSponsorsByPeriodIDs 检查指定的周期 ID 是否存在冠名记录，只要有任意一个存在就返回 true
func ExistsSponsorsByPeriodIDs(periodOIDs []primitive.ObjectID) (bool, error) {
	if len(periodOIDs) == 0 {
		return false, nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := SponsorCollection().CountDocuments(ctx, bson.M{
		"_period_id": bson.M{"$in": periodOIDs},
	})
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// ListSponsor 获取冠名记录
func ListSponsor(filter bson.M, findOptions ...*options.FindOptions) ([]*Sponsor, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := SponsorCollection().Find(ctx, filter, findOptions...)
	if err != nil {
		return nil, err
	}
	var res []*Sponsor
	err = cur.All(ctx, &res)
	return res, err
}
