package giftwall

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindOngoingSponsorRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(1919)
	testUserID := int64(810)
	testGiftID := int64(40010)

	// 命中空字符
	key := KeyRoomSponsors(testRoomID)
	require.NoError(service.LRURedis.Set(key, "null", time.Minute).Err())
	now := goutil.TimeNow()
	rev, err := FindOngoingSponsorRevenue(testRoomID, testUserID, testGiftID)
	require.NoError(err)
	assert.Nil(rev)

	// 命中缓存但无数据
	sponsors := []*Sponsor{{
		RoomID:    testRoomID,
		UserID:    testUserID,
		GiftID:    testGiftID,
		StartTime: now.Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
		Revenue:   100,
	}}
	bs, err := json.Marshal(sponsors)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(key, bs, time.Minute).Err())
	rev, err = FindOngoingSponsorRevenue(testRoomID, 0, testGiftID)
	require.NoError(err)
	assert.Nil(rev)

	// 命中缓存且有数据
	rev, err = FindOngoingSponsorRevenue(testRoomID, testUserID, testGiftID)
	require.NoError(err)
	assert.Equal(sponsors[0].Revenue, *rev)
}

func TestFindOngoingSponsor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 命中空字符
	testRoomID := int64(1919)
	testGiftID := int64(810)
	key := KeyRoomSponsors(testRoomID)
	require.NoError(service.LRURedis.Set(key, "null", time.Minute).Err())
	sponsor, err := FindOngoingSponsor(testRoomID, testGiftID)
	require.NoError(err)
	assert.Nil(sponsor)

	// 命中缓存但没数据
	now := goutil.TimeNow()
	sponsors := []*Sponsor{{
		RoomID:    testRoomID,
		GiftID:    testGiftID,
		StartTime: now.Add(time.Minute).Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
	}}
	bs, err := json.Marshal(sponsors)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(key, bs, time.Minute).Err())
	sponsor, err = FindOngoingSponsor(testRoomID, testGiftID)
	require.NoError(err)
	assert.Nil(sponsor)
	sponsor, err = FindOngoingSponsor(testRoomID, 811)
	require.NoError(err)
	assert.Nil(sponsor)

	// 命中缓存且有数据
	sponsors[0].StartTime = now.Unix()
	bs, err = json.Marshal(sponsors)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(key, bs, time.Minute).Err())
	sponsor, err = FindOngoingSponsor(testRoomID, testGiftID)
	require.NoError(err)
	require.NotNil(sponsor)
	assert.Equal(testGiftID, sponsor.GiftID)
	assert.Equal(testRoomID, sponsor.RoomID)
}

func TestFindOngoingRoomSponsorsWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 命中空字符
	testRoomID := int64(1919)
	testGiftID := int64(810)
	key := KeyRoomSponsors(testRoomID)
	require.NoError(service.LRURedis.Set(key, "null", time.Minute).Err())
	now := goutil.TimeNow()
	sponsors, err := FindOngoingRoomSponsorsWithCache(testRoomID, now)
	require.NoError(err)
	assert.Len(sponsors, 0)

	// 没有数据
	require.NoError(service.LRURedis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = SponsorCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	sponsors, err = FindOngoingRoomSponsorsWithCache(testRoomID, now)
	require.NoError(err)
	assert.Len(sponsors, 0)

	// 有数据
	require.NoError(service.LRURedis.Del(key).Err())
	sponsor := Sponsor{
		RoomID:    testRoomID,
		GiftID:    testGiftID,
		StartTime: now.Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
	}
	_, err = SponsorCollection().InsertOne(ctx, sponsor)
	require.NoError(err)
	sponsors, err = FindOngoingRoomSponsorsWithCache(testRoomID, now)
	require.NoError(err)
	require.Len(sponsors, 1)
	require.NotNil(sponsors[testGiftID])
	assert.Equal(testRoomID, sponsors[testGiftID].RoomID)

	// 命中个缓存
	sponsors, err = FindOngoingRoomSponsorsWithCache(testRoomID, now)
	require.NoError(err)
	require.Len(sponsors, 1)
	require.NotNil(sponsors[testGiftID])
	assert.Equal(testRoomID, sponsors[testGiftID].RoomID)
}

func TestBatchAddSponsors(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 准备测试数据
	periodOID := primitive.NewObjectID()
	now := goutil.TimeNow().Unix()

	// 清理函数
	cleanup := func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, _ = SponsorCollection().DeleteMany(ctx, bson.M{})
	}

	// 测试成功插入
	cleanup()
	sponsors := []*Sponsor{
		{
			PeriodOID: periodOID,
			RoomID:    10001,
			UserID:    20001,
			GiftID:    30001,
			StartTime: now,
			EndTime:   now + 86400,
		},
		{
			PeriodOID: periodOID,
			RoomID:    10002,
			UserID:    20002,
			GiftID:    30002,
			StartTime: now,
			EndTime:   now + 86400,
		},
	}

	err := BatchAddSponsors(sponsors)
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := SponsorCollection().CountDocuments(ctx, bson.M{})
	require.NoError(err)
	assert.EqualValues(2, count)

	// 测试空切片
	cleanup()
	err = BatchAddSponsors([]*Sponsor{})
	require.NoError(err)

	// 测试 nil 切片
	cleanup()
	err = BatchAddSponsors(nil)
	require.NoError(err)

	ctx, cancel = service.MongoDB.Context()
	defer cancel()
	count, err = SponsorCollection().CountDocuments(ctx, bson.M{})
	require.NoError(err)
	assert.Zero(count) // 保持原有数据不变
}

func TestExistsSponsorsByPeriodIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	periodOID1 := primitive.NewObjectID()
	periodOID2 := primitive.NewObjectID()
	roomID := int64(10001)
	now := goutil.TimeNow().Unix()

	// 清理函数
	cleanup := func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, _ = SponsorCollection().DeleteMany(ctx, bson.M{
			"_period_id": bson.M{"$in": []primitive.ObjectID{periodOID1, periodOID2}},
		})
	}
	cleanup()
	defer cleanup()

	// 测试空数组
	exists, err := ExistsSponsorsByPeriodIDs([]primitive.ObjectID{})
	require.NoError(err)
	assert.False(exists)

	// 测试不存在的周期 ID
	exists, err = ExistsSponsorsByPeriodIDs([]primitive.ObjectID{periodOID1})
	require.NoError(err)
	assert.False(exists)

	// 插入测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = SponsorCollection().InsertMany(ctx, []any{
		Sponsor{
			PeriodOID:    periodOID1,
			RoomID:       roomID,
			UserID:       1,
			GiftID:       1,
			StartTime:    now,
			EndTime:      now + 3600,
			ModifiedTime: now,
			CreateTime:   now,
		},
	})
	require.NoError(err)

	// 测试存在的周期 ID
	exists, err = ExistsSponsorsByPeriodIDs([]primitive.ObjectID{periodOID1})
	require.NoError(err)
	assert.True(exists)

	// 测试部分存在的周期 ID
	exists, err = ExistsSponsorsByPeriodIDs([]primitive.ObjectID{periodOID1, periodOID2})
	require.NoError(err)
	assert.True(exists)

	// 测试不存在的周期 ID
	emptyOID := primitive.NewObjectID()
	exists, err = ExistsSponsorsByPeriodIDs([]primitive.ObjectID{emptyOID})
	require.NoError(err)
	assert.False(exists)
}

func TestListSponsor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := SponsorCollection().DeleteMany(ctx, bson.M{"room_id": 223344})
	require.NoError(err)
	list, err := ListSponsor(bson.M{"room_id": -999})
	require.NoError(err)
	assert.Empty(list)

	// 测试获取冠名记录
	s := Sponsor{
		RoomID: 223344,
		GiftID: 100,
		UserID: 12,
	}
	_, err = SponsorCollection().InsertOne(ctx, Sponsor{
		RoomID: 223344,
		GiftID: 100,
		UserID: 12,
	})
	require.NoError(err)
	list, err = ListSponsor(bson.M{"user_id": 12})
	require.NoError(err)
	require.Len(list, 1)
	assert.Equal(s.RoomID, list[0].RoomID)
	assert.Equal(s.GiftID, list[0].GiftID)
	assert.Equal(s.UserID, list[0].UserID)
}
