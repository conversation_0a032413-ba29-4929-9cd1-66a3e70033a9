package giftwall

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRecordTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(ActivatedRecord{}, "_id", "create_time", "modified_time",
		"room_id", "creator_id", "_period_id", "show_gift_id", "activated_num", "revenue")

	kc = tutil.<PERSON><PERSON>eyChecker(t, tutil.JSON)
	kc.Check(updateMessage{}, "type", "event", "room_id", "time", "gift_wall", "creator")
	kc.Check(ActivatedDetail{}, "activated_num", "total_num")
}

func TestAddRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	periodOID := primitive.NewObjectID()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := RecordCollection().DeleteMany(ctx, bson.M{"_period_id": periodOID})
		assert.NoError(err)
	}()
	err := AddRecord(periodOID, 1, 1, 1, 1, 1)
	require.NoError(err)

	err = AddRecord(periodOID, 1, 1, 1, 2, 1)
	require.NoError(err)

	var ar ActivatedRecord
	require.NoError(RecordCollection().FindOne(ctx, bson.M{"_period_id": periodOID}).Decode(&ar))
	assert.Equal(int64(3), ar.ActivatedNum)
	assert.Equal(int64(2), ar.Revenue)
}

func TestActiveGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("WithNormalType", func(t *testing.T) {
		cleanup, err := createTestGiftsData()
		require.NoError(err)
		defer cleanup()
		cleanupPeriod, err := createTestPeriodData(goutil.TimeNow(), PeriodTypeNormal)
		require.NoError(err)
		defer cleanupPeriod(PeriodTypeNormal)
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err = RecordCollection().DeleteMany(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}})
		require.NoError(err)
		r, err := room.FindOne(bson.M{"status.open": 1})
		require.NoError(err)
		require.NotNil(r)

		r.CreatorID = 1
		message, err := ActiveGift(r, 1, 1, 100, 1)
		require.NoError(err)
		assert.NotNil(message)

		var ar ActivatedRecord
		require.NoError(RecordCollection().FindOne(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}}).Decode(&ar))
		assert.Equal(int64(1), ar.ActivatedNum)
		assert.Equal(int64(100), ar.Revenue)
		assert.Equal(testShowGiftIDs[1], ar.ShowGiftID)
		assert.Equal(r.RoomID, ar.RoomID)

		message, err = ActiveGift(r, 1, 1, 100, 5)
		require.NoError(err)
		assert.Nil(message)

		var newRecord ActivatedRecord
		require.NoError(RecordCollection().FindOne(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}}).Decode(&newRecord))
		assert.Equal(int64(6), newRecord.ActivatedNum)
		assert.Equal(int64(200), newRecord.Revenue)
	})

	t.Run("WithPremiumType", func(t *testing.T) {
		cleanup, err := createTestGiftsData()
		require.NoError(err)
		defer cleanup()

		r, err := room.FindOne(bson.M{"status.open": 1})
		require.NoError(err)
		require.NotNil(r)
		r.CreatorID = 1

		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		t.Run("TestPriorityToPremiumWhenBothValid", func(t *testing.T) {
			now := goutil.TimeNow()

			_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
			require.NoError(err)
			service.Cache5Min.Flush()
			clearCurrentPeriodCache(now, PeriodTypeNormal)
			clearCurrentPeriodCache(now, PeriodTypePremium)

			normalCleanup, err := createTestPeriodData(now, PeriodTypeNormal)
			require.NoError(err)
			defer func() {
				normalCleanup(PeriodTypeNormal)
				clearCurrentPeriodCache(now, PeriodTypeNormal)
			}()

			premiumCleanup, err := createTestPeriodData(now, PeriodTypePremium)
			require.NoError(err)
			defer func() {
				premiumCleanup(PeriodTypePremium)
				clearCurrentPeriodCache(now, PeriodTypePremium)
			}()

			defer func() {
				_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
				if err != nil {
					t.Logf("cleanup records error: %v", err)
				}
			}()

			message, err := ActiveGift(r, 1, 1, 100, 1)
			require.NoError(err)
			assert.Nil(message) // Premium 类型不发送消息

			normalPeriod, err := CurrentPeriodInfoByType(now, PeriodTypeNormal)
			require.NoError(err)
			require.NotNil(normalPeriod)
			normalRecord, err := FindOneRecord(bson.M{"room_id": r.RoomID, "_period_id": normalPeriod.OID})
			require.NoError(err)
			assert.Nil(normalRecord)

			premiumPeriod, err := CurrentPeriodInfoByType(now, PeriodTypePremium)
			require.NoError(err)
			require.NotNil(premiumPeriod)
			premiumRecord, err := FindOneRecord(bson.M{"room_id": r.RoomID, "_period_id": premiumPeriod.OID})
			require.NoError(err)
			require.NotNil(premiumRecord)
			assert.Equal(int64(1), premiumRecord.ActivatedNum)
		})

		t.Run("TestPremiumWhenOnlyPremiumIsValid", func(t *testing.T) {
			now := goutil.TimeNow()

			_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
			require.NoError(err)
			service.Cache5Min.Flush()
			clearCurrentPeriodCache(now, PeriodTypeNormal)
			clearCurrentPeriodCache(now, PeriodTypePremium)

			premiumCleanup, err := createTestPeriodData(now, PeriodTypePremium)
			require.NoError(err)
			defer func() {
				premiumCleanup(PeriodTypePremium)
				clearCurrentPeriodCache(now, PeriodTypePremium)
			}()

			defer func() {
				_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
				if err != nil {
					t.Logf("cleanup records error: %v", err)
				}
			}()

			message, err := ActiveGift(r, 1, 1, 100, 1)
			require.NoError(err)
			assert.Nil(message) // Premium 类型不发送消息

			normalPeriod, err := CurrentPeriodInfoByType(now, PeriodTypeNormal)
			require.NoError(err)
			assert.Nil(normalPeriod)

			premiumPeriod, err := CurrentPeriodInfoByType(now, PeriodTypePremium)
			require.NoError(err)
			require.NotNil(premiumPeriod)
			premiumRecord, err := FindOneRecord(bson.M{"room_id": r.RoomID, "_period_id": premiumPeriod.OID})
			require.NoError(err)
			require.NotNil(premiumRecord)
			assert.Equal(int64(1), premiumRecord.ActivatedNum)
		})
	})
}

func TestActivatedCountByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(223344)
	testUserID := int64(100)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID, "creator_id": testUserID})
	require.NoError(err)
	periodOID := primitive.NewObjectID()
	_, err = RecordCollection().InsertOne(ctx, ActivatedRecord{
		PeriodOID: periodOID,
		RoomID:    testRoomID,
	})
	require.NoError(err)

	count, err := ActivatedCountByRoomID(periodOID, testRoomID)
	require.NoError(err)
	assert.EqualValues(count, 1)
}

func TestFindActivatedDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftIDs := []int64{1000, 2000}
	testRoomID := int64(223344)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()}, "end_time": bson.M{"$gt": now.Unix()}})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)
	_, err = RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = RecordCollection().InsertOne(ctx, ActivatedRecord{
		PeriodOID: res.InsertedID.(primitive.ObjectID),
		RoomID:    testRoomID,
	})
	require.NoError(err)

	ad, err := FindActivatedDetail(testRoomID)
	require.NoError(err)
	require.NotNil(ad)
	assert.EqualValues(len(testShowGiftIDs), ad.TotalNum)
	assert.EqualValues(1, ad.ActivatedNum)
}

func TestFindOneRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindOneRecord(bson.M{"room_id": -1})
	require.NoError(err)
	assert.Nil(res)

	res, err = FindOneRecord(bson.M{})
	require.NoError(err)
	assert.NotNil(res)
}

func TestListRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ListRecord(bson.M{"room_id": -999})
	require.NoError(err)
	assert.Empty(list)

	list, err = ListRecord(bson.M{})
	require.NoError(err)
	assert.NotEmpty(list)
}

func TestRewardInfo_sendRoomSticker(t *testing.T) {
	testNow := time.Unix(1700000000, 0) // 固定测试时间
	testEndTime := testNow.Add(7 * 24 * time.Hour).Unix()
	testRoomID := int64(999999)

	goutil.SetTimeNow(func() time.Time {
		return testNow
	})
	defer goutil.SetTimeNow(nil)

	// 清理测试数据
	defer func() {
		// 清理表情包和关联数据
		livesticker.DB().Delete(&livesticker.PackageStickerMap{}, "package_id IN (SELECT id FROM live_sticker_package WHERE type = ? AND name LIKE ?)", livesticker.TypeRoom, "房间 999999 专属表情包%")
		livesticker.DB().Delete(&livesticker.PackageOwner{}, "room_id = ?", testRoomID)
		livesticker.DB().Delete(&livesticker.Package{}, "type = ? AND name LIKE ?", livesticker.TypeRoom, "房间 999999 专属表情包%")
		livesticker.DB().Delete(&livesticker.LiveSticker{}, "id IN (?, ?)", 888881, 888882)
	}()

	// 准备测试表情数据
	testSticker1 := &livesticker.LiveSticker{
		ID:           888881,
		CreateTime:   testNow.Unix(),
		ModifiedTime: testNow.Unix(),
		Name:         "测试表情 1",
		Intro:        "测试表情描述 1",
		Icon:         "oss://test/sticker1.png",
		Image:        "oss://test/sticker1.webp",
	}
	testSticker2 := &livesticker.LiveSticker{
		ID:           888882,
		CreateTime:   testNow.Unix(),
		ModifiedTime: testNow.Unix(),
		Name:         "测试表情 2",
		Intro:        "测试表情描述 2",
		Icon:         "oss://test/sticker2.png",
		Image:        "oss://test/sticker2.webp",
	}

	require.NoError(t, livesticker.DB().Create(testSticker1).Error)
	require.NoError(t, livesticker.DB().Create(testSticker2).Error)

	t.Run("StickerNotFound", func(t *testing.T) {
		reward := &RewardInfo{
			ElementID: 999999, // 不存在的表情 ID
		}

		err := reward.sendRoomSticker(testRoomID, testNow, testEndTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "can not find sticker")
	})

	t.Run("CreateExclusivePackageForFirstSticker", func(t *testing.T) {
		reward := &RewardInfo{
			ElementID: testSticker1.ID,
		}

		err := reward.sendRoomSticker(testRoomID, testNow, testEndTime)
		require.NoError(t, err)

		// 验证创建了表情包拥有者记录
		var owner livesticker.PackageOwner
		err = livesticker.DB().Where("room_id = ? AND user_id = 0", testRoomID).Take(&owner).Error
		require.NoError(t, err)
		assert.NotZero(t, owner.PackageID)

		// 验证创建了表情包
		var pkg livesticker.Package
		err = livesticker.DB().Where("id = ?", owner.PackageID).Take(&pkg).Error
		require.NoError(t, err)
		assert.Equal(t, livesticker.TypeRoom, pkg.Type)
		assert.Equal(t, "直播间专属表情", pkg.Name)

		// 验证表情被添加到表情包中
		var stickerMap livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", pkg.ID, testSticker1.ID).Take(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, testNow.Unix(), stickerMap.StartTime)
		assert.Equal(t, testEndTime, stickerMap.ExpireTime)
	})

	t.Run("ExtendStickerExpireTime", func(t *testing.T) {
		// 先找到之前创建的表情包
		var owner livesticker.PackageOwner
		require.NoError(t, livesticker.DB().Where("room_id = ? AND user_id = 0", testRoomID).Take(&owner).Error)

		// 将表情 1 的过期时间设置为更早的时间
		earlyExpireTime := testEndTime - 3600 // 比 testEndTime 早 1 小时
		require.NoError(t, livesticker.DB().Model(&livesticker.PackageStickerMap{}).
			Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker1.ID).
			Update("expire_time", earlyExpireTime).Error)

		reward := &RewardInfo{
			ElementID: testSticker1.ID,
		}

		// 用更晚的结束时间调用
		newEndTime := testEndTime + 3600 // 比原来晚 1 小时
		err := reward.sendRoomSticker(testRoomID, testNow, newEndTime)
		require.NoError(t, err)

		// 验证过期时间被更新
		var stickerMap livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker1.ID).Take(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, newEndTime, stickerMap.ExpireTime)
	})

	t.Run("NoUpdateWhenExpireTimeLater", func(t *testing.T) {
		// 先找到之前创建的表情包
		var owner livesticker.PackageOwner
		require.NoError(t, livesticker.DB().Where("room_id = ? AND user_id = 0", testRoomID).Take(&owner).Error)

		// 获取当前的过期时间
		var currentMap livesticker.PackageStickerMap
		require.NoError(t, livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker1.ID).Take(&currentMap).Error)
		currentExpireTime := currentMap.ExpireTime

		reward := &RewardInfo{
			ElementID: testSticker1.ID,
		}

		// 用更早的结束时间调用
		earlierEndTime := currentExpireTime - 3600 // 比当前早 1 小时
		err := reward.sendRoomSticker(testRoomID, testNow, earlierEndTime)
		require.NoError(t, err)

		// 验证过期时间没有被更新
		var stickerMap livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker1.ID).Take(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, currentExpireTime, stickerMap.ExpireTime)
	})

	t.Run("AddNewStickerToExistingPackage", func(t *testing.T) {
		// 先找到之前创建的表情包
		var owner livesticker.PackageOwner
		require.NoError(t, livesticker.DB().Where("room_id = ? AND user_id = 0", testRoomID).Take(&owner).Error)

		reward := &RewardInfo{
			ElementID: testSticker2.ID,
		}

		err := reward.sendRoomSticker(testRoomID, testNow, testEndTime)
		require.NoError(t, err)

		// 验证新表情被添加到现有表情包中
		var stickerMap livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker2.ID).Take(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, testNow.Unix(), stickerMap.StartTime)
		assert.Equal(t, testEndTime, stickerMap.ExpireTime)

		// 验证表情包中现在有两个表情
		var count int64
		err = livesticker.DB().Model(&livesticker.PackageStickerMap{}).Where("package_id = ?", owner.PackageID).Count(&count).Error
		require.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})

	t.Run("NoUpdateWhenExpireTimeIsZero", func(t *testing.T) {
		// 先找到之前创建的表情包
		var owner livesticker.PackageOwner
		require.NoError(t, livesticker.DB().Where("room_id = ? AND user_id = 0", testRoomID).Take(&owner).Error)

		// 将表情 2 的过期时间设置为 0（永不过期）
		require.NoError(t, livesticker.DB().Model(&livesticker.PackageStickerMap{}).
			Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker2.ID).
			Update("expire_time", 0).Error)

		reward := &RewardInfo{
			ElementID: testSticker2.ID,
		}

		err := reward.sendRoomSticker(testRoomID, testNow, testEndTime)
		require.NoError(t, err)

		// 验证过期时间没有被更新，仍然是 0
		var stickerMap livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, testSticker2.ID).Take(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, int64(0), stickerMap.ExpireTime)
	})
}
