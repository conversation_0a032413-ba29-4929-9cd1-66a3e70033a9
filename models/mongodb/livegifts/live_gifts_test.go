package livegifts

import (
	"context"
	"encoding/json"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestLiveGiftTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(sortSentTimeDesc, "sent_time")
	kc.Check(LiveGift{}, "_id", "_room_id", "room_id", "user_id", "message_prefix",
		"lucky_gift_id", "lucky_gift_num", "lucky_gift_guaranteed",
		"gift_id", "gift_type", "gift_price", "gift_num", "gift_attr", "price", "point",
		"transaction_ids", "bubble",
		"sent_time", "updated_time", "combo_end_time", "open_status", "goods_id")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LiveGift{}, "type", "room_id", "user_id", "message_prefix",
		"lucky_gift_id", "lucky_gift_num",
		"gift_id", "gift_price", "gift_num", "price", "bubble", "sent_time")
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	col := Collection()
	assert.Equal("test_live_gifts", col.Name())
}

func TestNewLiveGifts(t *testing.T) {
	assert := assert.New(t)

	id := primitive.NewObjectID()
	lg := NewLiveGifts(id, 12, &liveuser.Simple{UID: 123}, nil)
	assert.NotNil(lg.User)
	lg.User = nil
	assert.Equal(&LiveGift{RoomOID: id, RoomID: 12, UserID: 123}, lg)
}

func TestLiveGiftSetGift(t *testing.T) {
	assert := assert.New(t)

	lg := LiveGift{}
	lg.SetGift(&gift.Gift{GiftID: 1, Price: 2, Attr: 3, Point: 5}, 4)
	assert.EqualValues(1, lg.GiftID)
	assert.EqualValues(2, lg.GiftPrice)
	assert.EqualValues(3, lg.GiftAttr)
	assert.EqualValues(4, lg.GiftNum)
	assert.EqualValues(8, lg.Price)
	assert.EqualValues(20, lg.Point)
}

func TestUpdateSave(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		col := Collection()
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		u := &liveuser.Simple{UID: 12}
		lg := NewLiveGifts(primitive.NewObjectIDFromTimestamp(goutil.TimeNow()),
			0, u, nil).
			SetGift(&gift.Gift{GiftID: 1, Price: 6}, 1).
			SetTransactionIDs(1)

		now := goutil.TimeNow()
		type m bson.M

		comboEndTime := now.Add(ComboDurationDefault)
		comboOID, err := UpdateSave(lg, &comboEndTime, primitive.NewObjectIDFromTimestamp(now))
		require.NoError(err)
		assert.False(comboOID.IsZero())

		lg.TransactionIDs = []int64{2}
		_, err = UpdateSave(lg, &comboEndTime, comboOID)
		require.NoError(err)

		lg.GiftID = 301
		lg.TransactionIDs = nil
		filter := m{
			"_room_id":       lg.RoomOID,
			"user_id":        lg.UserID,
			"gift_id":        lg.GiftID,
			"updated_time":   m{"$gte": now.Add(-ComboDurationDefault)},
			"combo_end_time": m{"$lt": now},
		}
		_, err = col.DeleteMany(ctx, filter)
		assert.NoError(err)
		noComboOID, err := UpdateSave(lg, nil, primitive.NilObjectID)
		require.NoError(err)
		assert.False(noComboOID.IsZero()) // 插入新的，不和连击的冲突

		var gift LiveGift
		err = col.FindOne(ctx, bson.M{"_id": comboOID}).Decode(&gift)
		require.NoError(err)
		assert.Equal(int64(2), gift.GiftNum)
		assert.Equal(int64(12), gift.Price)
		assert.Equal([]int64{1, 2}, gift.TransactionIDs)

		gift = LiveGift{}
		err = col.FindOne(ctx, bson.M{"_id": noComboOID}).Decode(&gift)
		require.NoError(err)
		assert.Equal(int64(1), gift.GiftNum)
		assert.Equal(int64(6), gift.Price)
		assert.Empty(gift.TransactionIDs)
	})

	t.Run("luckyGift", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		lg := &LiveGift{
			RoomOID:        primitive.NewObjectIDFromTimestamp(goutil.TimeNow()),
			UserID:         12,
			LuckyGiftID:    new(int64),
			GiftID:         90001,
			GiftPrice:      6,
			GiftNum:        1,
			TransactionIDs: []int64{1},
		}
		*lg.LuckyGiftID = 80001
		oid, err := UpdateSave(lg, nil, primitive.NilObjectID)
		require.NoError(err)
		assert.NotEqual(primitive.NilObjectID, oid)
	})
}

func TestSearcherListLiveGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := LiveGift{
		RoomID:      123456,
		UserID:      12,
		GiftID:      10,
		SentTime:    goutil.TimeNow(),
		UpdatedTime: goutil.TimeNow(),
	}
	col := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := col.InsertOne(ctx, lg)
	require.NoError(err)
	s := Searcher{
		RoomID:     lg.RoomID,
		StartTime:  lg.SentTime.Add(-time.Second),
		EndTime:    lg.SentTime.Add(time.Second),
		P:          1,
		PageSize:   1,
		GiftType:   GiftPurchased,
		Projection: bson.M{"room_id": 1, "sent_time": 1},
	}
	var lgs []*LiveGift
	_, err = s.ListLiveGifts(&lgs)
	require.NoError(err)
	assert.Empty(lgs)
	assert.NotNil(lgs)

	s.GiftType = GiftFree
	pa, err := s.ListLiveGifts(&lgs)
	require.NoError(err)
	require.NotZero(pa.Count)
	assert.Equal(lg.RoomID, lgs[0].RoomID)
	assert.Equal(lg.SentTime.Unix(), lgs[0].SentTime.Unix())
	assert.Zero(lgs[0].UserID)
	assert.Zero(lgs[0].GiftID)

	s.StartTime = lg.SentTime.Add(time.Second / 2)
	_, err = s.ListLiveGifts(&lgs)
	require.NoError(err)
	assert.Empty(lgs)
	assert.NotNil(lgs)

	_, _ = col.DeleteMany(ctx,
		bson.M{
			"room_id":   lg.RoomID,
			"sent_time": bson.M{"$lt": lg.SentTime.Add(-7 * 24 * time.Hour)}}) // 删除一周前的测试数据
}

func TestIsGiftType(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsGiftType(GiftAll))
	assert.True(IsGiftType(GiftFree))
	assert.True(IsGiftType(GiftPurchased))
	assert.False(IsGiftType(4))
}

func TestSaveMany(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	col := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	roomID := int64(123895)
	roomOID := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	defer func() {
		_, err := col.DeleteMany(ctx, bson.M{"_room_id": roomOID})
		assert.NoError(err)
	}()
	u := &liveuser.Simple{UID: 12}
	g := &gift.Gift{GiftID: 1, Price: 2, Attr: 3, Point: 5}
	lgs := []*LiveGift{
		NewLiveGifts(roomOID, roomID, u, nil).SetGift(g, 1),
		NewLiveGifts(roomOID, roomID, u, nil).SetGift(g, 2),
	}
	require.NoError(SaveMany(lgs))

	cur, err := col.Find(ctx, bson.M{"_room_id": roomOID},
		options.Find().SetSort(bson.M{"_id": 1}))
	require.NoError(err)
	var after []*LiveGift
	require.NoError(cur.All(ctx, &after))
	require.Equal(len(lgs), len(after))
	assert.Equal(lgs[0].OID, after[0].OID)
	assert.Equal(lgs[1].OID, after[1].OID)
}

func TestLiveGiftRoomMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := NewLiveGifts(primitive.NilObjectID, 1234, &liveuser.Simple{UID: 12},
		&bubble.Simple{}).SetGift(&gift.Gift{GiftID: 123, LuckyEffect: "effect"}, 2).
		SetLuckyGift(&gift.Gift{GiftID: 456}, 10, false)
	lg.MessagePrefix = "test_"
	rm := lg.RoomMessage()
	assert.Equal(liveim.TypeGift, rm.Type)
	assert.Equal(liveim.EventSend, rm.Event)
	assert.Equal(lg.RoomID, rm.RoomID)
	assert.Equal(lg.User, rm.User)
	assert.NotZero(rm.Time)
	assert.Equal(lg.MessagePrefix, rm.MessagePrefix)
	assert.Equal(gift.NewNotifyGift(lg.Gift, 2), rm.Gift)
	require.Equal(gift.NewNotifyGiftLucky(lg.LuckyGift, lg.Gift, 10), rm.Lucky)
	assert.Equal(lg.Gift.LuckyEffect, rm.Lucky.EffectURL)

	lg = NewLiveGifts(primitive.NilObjectID, 1234, &liveuser.Simple{UID: 12},
		&bubble.Simple{}).SetGift(&gift.Gift{GiftID: 999, Comboable: 1}, 2).SetCombo(&Combo{Num: 1})
	lg.MessagePrefix = "test_"
	rm = lg.RoomMessage()
	assert.Equal(liveim.TypeGift, rm.Type)
	assert.Equal(liveim.EventSend, rm.Event)
	assert.Equal(lg.RoomID, rm.RoomID)
	assert.Equal(lg.User, rm.User)
	assert.NotZero(rm.Time)
	assert.Equal(lg.MessagePrefix, rm.MessagePrefix)
	assert.Equal(gift.NewNotifyGift(lg.Gift, 2), rm.Gift)
	assert.Equal(1, rm.Combo.Num)
}

func TestLiveGift_sponsorMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 配置命中空缓存
	lg := NewLiveGifts(primitive.NilObjectID, 114, &liveuser.Simple{UID: 514},
		&bubble.Simple{}).SetGift(&gift.Gift{GiftID: 1919}, 2)
	paramKey := keys.KeyParams1.Format(params.KeyGiftWall)
	require.NoError(service.LRURedis.Set(paramKey, "null", time.Minute).Err())
	assert.Nil(lg.sponsorMessage())

	// 有配置但无冠名
	param := params.GiftWall{
		SponsorLevels: []*params.GiftWallSponsorLevel{
			{TargetRevenue: 1000, Text: "test1"},
			{TargetRevenue: 2000, Text: "test2"},
		},
	}
	b, err := json.Marshal(param)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(paramKey, b, time.Minute).Err())
	sponsorKey := giftwall.KeyRoomSponsors(lg.RoomID)
	require.NoError(service.LRURedis.Set(sponsorKey, "null", time.Minute).Err())
	assert.Nil(lg.sponsorMessage())

	// 有冠名但未达标
	now := goutil.TimeNow()
	sponsors := []*giftwall.Sponsor{{
		RoomID:    lg.RoomID,
		UserID:    lg.UserID,
		GiftID:    lg.GiftID,
		StartTime: now.Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
		Revenue:   100,
	}}
	bs, err := json.Marshal(sponsors)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(sponsorKey, bs, time.Minute).Err())
	assert.Nil(lg.sponsorMessage())

	// 冠名达标
	sponsors[0].Revenue = 1500
	bs, err = json.Marshal(sponsors)
	require.NoError(err)
	require.NoError(service.LRURedis.Set(sponsorKey, bs, time.Minute).Err())
	require.NotNil(lg.sponsorMessage())
	assert.Equal(param.SponsorLevels[0].Text, lg.sponsorMessage().Text)
}

func TestLiveGift_CrossRoomMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := NewLiveGifts(primitive.NilObjectID, 1234, &liveuser.Simple{UID: 12},
		&bubble.Simple{}).SetGift(&gift.Gift{GiftID: 123, LuckyEffect: "effect"}, 2).
		SetLuckyGift(&gift.Gift{GiftID: 456}, 10, false)
	rm := lg.CrossRoomMessage(1, &room.Room{})
	assert.Equal(liveim.TypeGift, rm.Type)
	assert.Equal(liveim.EventCrossSend, rm.Event)
	assert.NotNil(rm.RoomCreator)
	assert.Equal(lg.User, rm.User)
	assert.NotZero(rm.Time)
	assert.Empty(rm.MessagePrefix)
	assert.Equal(gift.NewNotifyGift(lg.Gift, 2), rm.Gift)
	require.Equal(gift.NewNotifyGiftLucky(lg.LuckyGift, lg.Gift, 10), rm.Lucky)
	assert.Equal(lg.Gift.LuckyEffect, rm.Lucky.EffectURL)
	assert.Nil(rm.CurrentRevenue)
}

func TestLiveGift_SetLuckyGift(t *testing.T) {
	assert := assert.New(t)

	var lg LiveGift
	lg.SetLuckyGift(&gift.Gift{GiftID: 123}, 1, true)
	assert.Equal(goutil.NewInt64(123), lg.LuckyGiftID)
	assert.NotNil(lg.LuckyGift)
	assert.Equal(goutil.NewInt(1), lg.LuckyGiftNum)
	assert.Equal(goutil.NewBool(true), lg.LuckyGiftGuaranteed)

	lg = LiveGift{}
	lg.SetLuckyGift(&gift.Gift{GiftID: 123}, 1, false)
	assert.Nil(lg.LuckyGiftGuaranteed)
}

func TestLiveGift_SetRoomOpenStatus(t *testing.T) {
	assert := assert.New(t)

	var lg LiveGift
	lg.SetRoomOpenStatus(false)
	assert.Equal(OpenStatusClosed, lg.OpenStatus)
	lg.SetRoomOpenStatus(true)
	assert.Equal(OpenStatusOpen, lg.OpenStatus)
}

func TestLiveGift_BuildBroadcastMessage(t *testing.T) {
	assert := assert.New(t)

	lg := LiveGift{
		RoomID: 1,
		UserID: 2,
	}
	lg.SetGift(&gift.Gift{GiftID: 123}, 1)

	rm := lg.BuildBroadcastMessage(false)
	assert.Equal(liveim.IMMessageTypeNormal, rm.Type)
	assert.EqualValues(1, rm.RoomID)
	assert.Empty(rm.UserID)
	assert.NotNil(rm.Payload)

	rm = lg.BuildBroadcastMessage(false, nil)
	assert.Nil(rm.Payload)
}

func TestLiveGift_BuildCrossRoomBroadcastMessage(t *testing.T) {
	assert := assert.New(t)

	lg := LiveGift{
		RoomID: 1,
		UserID: 2,
	}
	lg.SetGift(&gift.Gift{GiftID: 123}, 1)

	rm := lg.BuildCrossRoomBroadcastMessage(1, &room.Room{})
	assert.Equal(liveim.IMMessageTypeNormal, rm.Type)
	assert.Equal(lg.UserID, rm.UserID)
	assert.NotNil(rm.Payload)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg, err := FindOne(bson.M{"gift_id": bson.M{"$gt": 0}})
	require.NoError(err)
	assert.NotNil(lg)

	lg, err = FindOne(bson.M{"gift_id": -11111})
	require.NoError(err)
	assert.Nil(lg)
}

func TestListUser7DaysRoomFreeGiftNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1233421321)
	)
	require.NoError(service.LRURedis.Del(keys.KeyUser7DaysRoomFreeGiftNum1.Format(123)).Err())
	_, err := Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = Collection().InsertMany(context.Background(), []any{
		&LiveGift{UserID: testUserID, RoomID: 1, GiftID: 1, GiftNum: 44, SentTime: goutil.TimeNow().AddDate(0, 0, -8)},
		&LiveGift{UserID: testUserID, RoomID: 1, GiftID: 1, GiftNum: 1, SentTime: goutil.TimeNow().AddDate(0, 0, -1)},
		&LiveGift{UserID: testUserID, RoomID: 2, GiftID: 2, GiftNum: 2, SentTime: goutil.TimeNow().AddDate(0, 0, -1)},
		&LiveGift{UserID: testUserID, RoomID: 2, GiftID: 2, GiftNum: 1, SentTime: goutil.TimeNow().AddDate(0, 0, -1)},
	})
	require.NoError(err)

	num, err := ListUser7DaysRoomFreeGiftNum(123)
	require.NoError(err)
	assert.Empty(num)

	require.NoError(service.LRURedis.Del(keys.KeyUser7DaysRoomFreeGiftNum1.Format(testUserID)).Err())
	num, err = ListUser7DaysRoomFreeGiftNum(testUserID)
	require.NoError(err)
	require.Len(num, 2)
	sort.Slice(num, func(i, j int) bool {
		return num[i].RoomID < num[j].RoomID
	})
	assert.EqualValues(1, num[0].RoomID)
	assert.EqualValues(2, num[1].RoomID)
}
