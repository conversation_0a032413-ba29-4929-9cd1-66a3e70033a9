package livegifts

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 查询礼物类型
const (
	GiftAll = iota
	GiftPurchased
	GiftFree
)

// 开播状态
// 同 models/mysql/livetxnorder/live_txn_order.go
const (
	OpenStatusNoRoom = iota - 1 // 不在直播间
	OpenStatusClosed            // 直播间关播
	OpenStatusOpen              // 直播间开播
)

// ComboDurationDefault 连击默认时长
const ComboDurationDefault = 5 * time.Second

var sortSentTimeDesc = struct {
	SentTime int `bson:"sent_time"`
}{-1}

// LiveGift live_gift 存储字段示例
type LiveGift struct {
	OID                 primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	RoomOID             primitive.ObjectID `bson:"_room_id" json:"-"`
	RoomID              int64              `bson:"room_id" json:"room_id"`
	UserID              int64              `bson:"user_id" json:"user_id"`
	MessagePrefix       string             `bson:"message_prefix,omitempty" json:"message_prefix,omitempty"`
	LuckyGiftID         *int64             `bson:"lucky_gift_id,omitempty" json:"lucky_gift_id,omitempty"`
	LuckyGiftNum        *int               `bson:"lucky_gift_num,omitempty" json:"lucky_gift_num,omitempty"`
	LuckyGiftGuaranteed *bool              `bson:"lucky_gift_guaranteed,omitempty" json:"-"`
	GiftID              int64              `bson:"gift_id" json:"gift_id"`
	GiftType            int                `bson:"gift_type,omitempty" json:"-"`
	GiftPrice           int64              `bson:"gift_price" json:"gift_price"`
	GiftNum             int64              `bson:"gift_num" json:"gift_num"`
	GiftAttr            goutil.BitMask     `bson:"gift_attr" json:"-"`
	Price               int64              `bson:"price,omitempty" json:"price"` // 总价 = gift_price * gift_num
	Point               int64              `bson:"point,omitempty" json:"-"`
	TransactionIDs      []int64            `bson:"transaction_ids,omitempty" json:"-"`
	Bubble              *bubble.Simple     `bson:"bubble,omitempty" json:"bubble,omitempty"`
	SentTime            time.Time          `bson:"sent_time" json:"-"`
	UpdatedTime         time.Time          `bson:"updated_time" json:"-"`
	ComboEndTime        time.Time          `bson:"combo_end_time" json:"-"`
	OpenStatus          int                `bson:"open_status" json:"-"`

	// 商品 ID
	GoodsID int64 `bson:"goods_id" json:"-"`

	// 历史消息中的字段
	HistoryType string               `bson:"-" json:"type,omitempty"`
	HistoryTime goutil.TimeUnixMilli `bson:"-" json:"sent_time"`

	// 临时变量
	Gift       *gift.Gift       `bson:"-" json:"-"`
	LuckyGift  *gift.Gift       `bson:"-" json:"-"`
	User       *liveuser.Simple `bson:"-" json:"-"`
	Combo      *Combo           `bson:"-" json:"-"`
	MultiCombo *Combo           `bson:"-" json:"-"`
}

// Searcher searcher
type Searcher struct {
	StartTime  time.Time
	EndTime    time.Time
	GiftType   int
	RoomID     int64
	P          int64
	PageSize   int64
	Projection interface{}
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_gifts")
}

// NewLiveGifts new live gifts
func NewLiveGifts(roomOID primitive.ObjectID, roomID int64, u *liveuser.Simple, bubble *bubble.Simple) *LiveGift {
	lg := &LiveGift{
		RoomOID: roomOID,
		RoomID:  roomID,
		UserID:  u.UserID(),
		Bubble:  bubble,
		User:    u,
	}
	return lg
}

// SetGift set gift
func (lg *LiveGift) SetGift(g *gift.Gift, giftNum int64) *LiveGift {
	lg.GiftID = g.GiftID
	lg.GiftPrice = g.Price
	lg.GiftNum = giftNum
	lg.GiftAttr = g.Attr
	lg.GiftType = g.Type
	lg.Price = g.Price * giftNum
	lg.Point = g.Point * giftNum
	lg.Gift = g
	return lg
}

// SetLuckyGift set lucky gift
func (lg *LiveGift) SetLuckyGift(g *gift.Gift, giftNum int, guaranteed bool) *LiveGift {
	lg.LuckyGiftID = goutil.NewInt64(g.GiftID)
	lg.LuckyGiftNum = goutil.NewInt(giftNum)
	if guaranteed {
		lg.LuckyGiftGuaranteed = goutil.NewBool(guaranteed)
	} else {
		lg.LuckyGiftGuaranteed = nil
	}
	lg.LuckyGift = g
	return lg
}

// SetTransactionIDs set transactionIDs
func (lg *LiveGift) SetTransactionIDs(ids ...int64) *LiveGift {
	lg.TransactionIDs = ids
	return lg
}

// SetGoodsID set go
func (lg *LiveGift) SetGoodsID(id int64) *LiveGift {
	lg.GoodsID = id
	return lg
}

// SetCombo set combo
func (lg *LiveGift) SetCombo(combo *Combo) *LiveGift {
	lg.Combo = combo
	return lg
}

// SetMultiCombo set multi combo
func (lg *LiveGift) SetMultiCombo(combo *Combo) *LiveGift {
	lg.MultiCombo = combo
	return lg
}

// SetRoomOpenStatus 设置房间是否开播的状态
func (lg *LiveGift) SetRoomOpenStatus(open bool) *LiveGift {
	if open {
		lg.OpenStatus = OpenStatusOpen
	} else {
		lg.OpenStatus = OpenStatusClosed
	}
	return lg
}

func (lg LiveGift) saveNew(comboEndTime *time.Time) (primitive.ObjectID, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	lg.OID = primitive.NilObjectID
	lg.SentTime = goutil.TimeNow()
	lg.UpdatedTime = lg.SentTime
	if comboEndTime != nil {
		lg.ComboEndTime = *comboEndTime
	} else {
		lg.ComboEndTime = lg.SentTime
	}
	r, err := collection.InsertOne(ctx, lg)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return r.InsertedID.(primitive.ObjectID), nil
}

// UpdateSave 更新或者插入新的 gift
// 假如新插入数据，会返回 primitive.ObjectID
func UpdateSave(lg *LiveGift, comboEndTime *time.Time, preOID primitive.ObjectID) (primitive.ObjectID, error) {
	if lg.LuckyGiftID != nil {
		// 随机礼物不会聚合
		oid, err := lg.saveNew(nil)
		if err != nil {
			return primitive.NilObjectID, err
		}
		return oid, nil
	}
	type m bson.M

	now := goutil.TimeNow()
	var filter m
	if comboEndTime != nil && !preOID.IsZero() {
		filter = m{"_id": preOID}
	} else if comboEndTime == nil {
		filter = m{
			"_room_id":     lg.RoomOID,
			"user_id":      lg.UserID,
			"gift_id":      lg.GiftID,
			"updated_time": m{"$gte": now.Add(-time.Minute)}, // 不连击的礼物以一分钟进行聚合
		}
	}
	inc := m{"gift_num": lg.GiftNum}
	if lg.Price > 0 {
		inc["price"] = lg.Price
	}
	if lg.Point > 0 {
		inc["point"] = lg.Point
	}
	update := m{
		"$inc": inc,
	}
	if len(lg.TransactionIDs) != 0 {
		update["$push"] = m{"transaction_ids": m{"$each": lg.TransactionIDs}}
	}
	if comboEndTime == nil {
		// 不连击
		update["$set"] = m{"updated_time": now, "combo_end_time": now}
	} else {
		// 连击
		update["$set"] = m{"updated_time": now, "combo_end_time": *comboEndTime}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	if filter != nil {
		// 尝试更新已有的
		res, err := col.UpdateOne(ctx, filter, update)
		if err != nil {
			return primitive.NilObjectID, err
		}
		if res.ModifiedCount != 0 {
			// 更新成功
			return primitive.NilObjectID, nil
		}
	}
	// 插入新数据
	oid, err := lg.saveNew(comboEndTime)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return oid, nil
}

// ListLiveGifts 分页查询 live_gifts, 送礼时间倒序
// 保证入参是数组的指针
func (s Searcher) ListLiveGifts(v interface{}) (goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"room_id": s.RoomID}
	if !s.StartTime.IsZero() || !s.EndTime.IsZero() {
		var timeFilter struct {
			Gte time.Time `bson:"$gte,omitempty"`
			Lt  time.Time `bson:"$lt,omitempty"`
		}
		if !s.StartTime.IsZero() {
			timeFilter.Gte = s.StartTime
		}
		if !s.EndTime.IsZero() {
			timeFilter.Lt = s.EndTime
		}
		filter["sent_time"] = timeFilter
	}

	switch s.GiftType {
	case GiftPurchased:
		filter["gift_price"] = bson.M{"$gt": 0}
	case GiftFree:
		filter["gift_price"] = 0
	}

	opts := options.Find().SetSort(sortSentTimeDesc)
	col := Collection()
	if s.Projection != nil {
		opts.SetProjection(s.Projection)
	}
	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		return goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, s.P, s.PageSize)
	if !pa.Valid() {
		// WARKAROUND: 使用 json.Unmarshal 置空 v
		err := json.Unmarshal([]byte(`[]`), v)
		return pa, err
	}
	cur, err := col.Find(ctx, filter, pa.SetFindOptions(opts))
	if err != nil {
		return pa, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, v)
	return pa, err
}

// IsGiftType 检查是否是礼物类型
func IsGiftType(t int) bool {
	switch t {
	case GiftAll, GiftPurchased, GiftFree:
		return true
	}
	return false
}

// SaveMany 批量保存多条记录，不支持连击
func SaveMany(lgs []*LiveGift) error {
	now := goutil.TimeNow()
	documents := make([]interface{}, len(lgs))
	for i := range lgs {
		lgs[i].SentTime = now
		lgs[i].UpdatedTime = now
		lgs[i].ComboEndTime = now
		documents[i] = lgs[i]
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().InsertMany(ctx, documents)
	if err != nil {
		return err
	}
	for i := range cur.InsertedIDs {
		lgs[i].OID = cur.InsertedIDs[i].(primitive.ObjectID)
	}
	return nil
}

// RoomMessage 返回房间内的广播消息
func (lg *LiveGift) RoomMessage() *RoomMessage {
	rm := &RoomMessage{
		Type:           liveim.TypeGift,
		Event:          liveim.EventSend,
		RoomID:         lg.RoomID,
		User:           lg.User,
		Bubble:         lg.Bubble,
		Time:           goutil.NewTimeUnixMilli(goutil.TimeNow()),
		MessagePrefix:  lg.MessagePrefix,
		Gift:           gift.NewNotifyGift(lg.Gift, int(lg.GiftNum)),
		CurrentRevenue: goutil.NewInt64(roomsrank.CurrentRevenue(lg.RoomID, lg.UserID)),
		Combo:          lg.Combo,
		MultiCombo:     lg.MultiCombo,
	}
	if rm.Gift != nil {
		rm.Gift.Sponsor = lg.sponsorMessage()
	}
	if lg.LuckyGift != nil {
		rm.Lucky = gift.NewNotifyGiftLucky(lg.LuckyGift, lg.Gift, *lg.LuckyGiftNum)
	}
	return rm
}

// 返回礼物冠名信息
func (lg *LiveGift) sponsorMessage() *gift.Sponsor {
	giftWallParam, err := params.FindGiftWall()
	if err != nil {
		logger.Errorf("find gift wall param failed, err: %v", err)
		// PASS
	}
	if giftWallParam == nil || len(giftWallParam.SponsorLevels) == 0 {
		return nil
	}
	sponsorRevenue, err := giftwall.FindOngoingSponsorRevenue(lg.RoomID, lg.UserID, lg.GiftID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if sponsorRevenue == nil {
		return nil
	}
	sponsorConfig := giftWallParam.MatchSponsorLevel(*sponsorRevenue)
	if sponsorConfig == nil {
		return nil
	}
	return &gift.Sponsor{
		IconURL:   sponsorConfig.GiftMessageIconURL,
		Text:      sponsorConfig.Text,
		TextColor: sponsorConfig.TextColor,
	}
}

// CrossRoomMessage 返回跨房间送礼的广播消息
func (lg *LiveGift) CrossRoomMessage(fromRoomID int64, toRoom *room.Room) *RoomMessage {
	rm := &RoomMessage{
		Type:   liveim.TypeGift,
		Event:  liveim.EventCrossSend,
		RoomID: fromRoomID, // 收 ws 消息的直播间
		RoomCreator: &roomCreator{ // 需要展示收礼直播间的信息
			RoomID:          toRoom.RoomID,
			CreatorID:       toRoom.CreatorID,
			CreatorUsername: toRoom.CreatorUsername,
			CreatorIconURL:  toRoom.CreatorIconURL,
		},
		User:       lg.User,
		Bubble:     lg.Bubble,
		Time:       goutil.NewTimeUnixMilli(goutil.TimeNow()),
		Gift:       gift.NewNotifyGift(lg.Gift, int(lg.GiftNum)),
		Combo:      lg.Combo,
		MultiCombo: lg.MultiCombo,
	}
	if lg.LuckyGift != nil {
		rm.Lucky = gift.NewNotifyGiftLucky(lg.LuckyGift, lg.Gift, *lg.LuckyGiftNum)
	}
	return rm
}

// BuildBroadcastMessage build broadcast message
// NOTICE: 需要注意触发全站飘屏的送礼消息不能屏蔽
func (lg *LiveGift) BuildBroadcastMessage(enableFilter bool, payload ...*RoomMessage) *userapi.BroadcastElem {
	notify := &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: lg.RoomID,
	}
	if len(payload) > 0 {
		// diy 礼物是特殊的 room message，需要单独传入
		notify.Payload = payload[0]
	} else {
		notify.Payload = lg.RoomMessage()
	}

	if !enableFilter {
		return notify
	}

	// 触发以下条件的消息仅送礼用户本人可见
	// 单次赠送礼物总价值在 [0,1000) （单位：钻石）的，50% 的概率屏蔽
	// 单次赠送礼物总价值在 [1000,5000) （单位：钻石）的，30% 的概率屏蔽
	// 单次赠送礼物总价值在 [5000,+∞) （单位：钻石）的，不进行屏蔽
	// NOTICE: 随机礼物按照开出的价值计算
	var selfView bool
	switch {
	case lg.Price < 1000:
		selfView = util.Coin(0.5).Flip()
	case lg.Price < 5000:
		selfView = util.Coin(0.3).Flip()
	default:
		selfView = false
	}

	if selfView {
		notify.UserID = lg.UserID
	}

	return notify
}

// BuildCrossRoomBroadcastMessage build cross room broadcast message
func (lg *LiveGift) BuildCrossRoomBroadcastMessage(fromRoomID int64, toRoom *room.Room, payload ...*RoomMessage) *userapi.BroadcastElem {
	notify := &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: fromRoomID,
		UserID: lg.UserID,
	}
	if len(payload) > 0 {
		// diy 礼物是特殊的 room message，需要单独传入
		notify.Payload = payload[0]
	} else {
		notify.Payload = lg.CrossRoomMessage(fromRoomID, toRoom)
	}
	return notify
}

// FindOne find one live gift send record
func FindOne(filter interface{}) (*LiveGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	lg := new(LiveGift)
	err := Collection().FindOne(ctx, filter).Decode(lg)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return lg, nil
}

// RoomWithGiftNum 房间送礼数量
type RoomWithGiftNum struct {
	RoomID  int64 `bson:"_id" json:"room_id"`
	GiftNum int64 `bson:"gift_num" json:"gift_num"`
}

// ListUser7DaysRoomFreeGiftNum 获取用户最近 7 天内各个直播间免费送礼数量
func ListUser7DaysRoomFreeGiftNum(userID int64) ([]RoomWithGiftNum, error) {
	key := keys.KeyUser7DaysRoomFreeGiftNum1.Format(userID)
	res, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	if err == nil {
		var roomsWithGiftNum []RoomWithGiftNum
		err = json.Unmarshal(res, &roomsWithGiftNum)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return roomsWithGiftNum, nil
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	today := goutil.BeginningOfDay(goutil.TimeNow())
	cur, err := Collection().Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"user_id":    userID,
				"gift_price": 0,
				"sent_time": bson.M{
					"$gte": today.AddDate(0, 0, -7),
					"$lt":  today,
				},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id":      "$room_id",
				"gift_num": bson.M{"$sum": "$gift_num"},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var roomsWithGiftNum []RoomWithGiftNum
	err = cur.All(ctx, &roomsWithGiftNum)
	if err != nil {
		return nil, err
	}
	res, err = json.Marshal(roomsWithGiftNum)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, res, 30*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return roomsWithGiftNum, nil
}
