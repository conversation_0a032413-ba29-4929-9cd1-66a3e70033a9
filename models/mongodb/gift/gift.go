// Package gift 用于直播间礼物相关处理，包括礼物、奖池、奖池 buff 日志等操作
// TODO: 因该包直接处理了非 mongodb 的数据, 包路径有点不合理
package gift

import (
	"html"
	"math"
	"sort"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Gift type 礼物类型
// TODO: 需要将 TypeHide 类型的礼物变更为原始类型，并将全部类型为 TypeHide 礼物的 order 键值设定为 0
const (
	TypeHide        = iota // 隐藏的礼物
	TypeNormal             // 一般的礼物
	TypeCustom             // 用户定制礼物，通过 user_id 判断归属人, noble_level 不为 0 时为贵族定制礼物
	TypeFree               // 免费礼物，无法通过钻石购买
	TypeRebate             // 白给礼物，主播能拿到收益
	TypeMedal              // 粉丝礼物，粉丝勋章达到一定程度才能送出
	TypeNoble              // 贵族礼物，占位和分类用，暂未存入数据库
	TypeRoomCustom         // 房间定制礼物
	TypeDrawSend           // 抽奖礼物（随机礼物，用户送）
	TypeDrawReceive        // 抽奖礼物（奖池礼物，用户抽）
	TypeSuperFan           // 超粉礼物
	TypeUpgrade            // 升级礼物

	TypeSpecial = 0 // 特殊礼物（用于红包、宝盒等）
)

// 特殊礼物对应玩法
const (
	SpecialGiftNum = 3 // 玩法礼物数量

	SpecialTypeRedPacket = "red_packet" // 红包
	SpecialTypeLuckyBox  = "lucky_box"  // 宝盒
	SpecialTypeGashapon  = "gashapon"   // 超能魔方
)

// 专属类型
const (
	// GiftExclusiveUser 用户专属礼物
	GiftExclusiveUser = 1
	// GiftExclusiveRoom 直播间专属礼物
	GiftExclusiveRoom = 2
)

// 排序礼物栏
const (
	TabGiftNormal    = iota // 礼物栏，包含普通礼物，直播间定制礼物，用户定制礼物，抽奖礼物
	TabGiftNoble            // 贵族礼物栏，包含贵族礼物和贵族定制礼物
	TabGiftExclusive        // 专属礼物栏，包含直播间专属礼物、用户专属礼物和设置了 Exclusive 字段的礼物
	TabGiftMedal            // 粉丝礼物栏，粉丝礼物
	TabGiftLimit
)

// gift attr
const (
	AttrPointAddActivity = iota + 1
	AttrDisableMedalPoint
	AttrDisableAddScore
	AttrAlwaysNotify                  // 总是全站飘屏
	AttrPointAddPK                    // 免费礼物增加 PK 值
	AttrPointAddRank                  // 使用亲密度加消费榜单
	AttrDisableDrawRoomCustomAfterSSR // 抽出大奖后失去抽奖资格
	AttrIsHotCard                     // 是否是热度卡
	AttrDrawSendSuperFan              // 超粉专属随机礼物
	AttrPointAddMultiConnect          // 免费礼物增加主播连线积分
	AttrUpgradeBase                   // 可升级礼物

	AttrCount
)

// OrderHide 下架的礼物
const OrderHide = 0

// 连击状态
const (
	ComboFlagEffect = iota + 1
	ComboFlagNotify
)

const (
	// ComboEffectMinPrice 触发连击特效的最小钻石，单位：钻
	ComboEffectMinPrice = 1000

	// ComboNotifyMinPrice 连击飘屏最小钻石，单位：钻
	ComboNotifyMinPrice = 21000

	// DefaultComboNotifyMessage 默认连击礼物飘屏
	DefaultComboNotifyMessage = `<font color="${highlight_color}"><b>${username}</b></font> ` +
		`<font color="${normal_color}">给</font> ` +
		`<font color="${highlight_color}"><b>${creator_username}</b></font> ` +
		`<font color="${normal_color}">送出</font> ` +
		`<font color="${highlight_color}"><b>${gift_num} 个${gift_name}</b></font>` +
		`<font color="${normal_color}">，快来围观吧~</font>`

	// DefaultRoomMultiComboNotifyMessage 默认直播间一起送连击礼物飘屏
	DefaultRoomMultiComboNotifyMessage = `<font color="${normal_color}">大家在</font> ` +
		`<font color="${highlight_color}"><b>${creator_username}</b></font> ` +
		`<font color="${normal_color}">的直播间送出</font> ` +
		`<font color="${highlight_color}"><b>${gift_name} × ${gift_num}</b></font>` +
		`<font color="${normal_color}">，</font>` +
		`<font color="${highlight_color}"><b>${top1_username}</b></font> ` +
		`<font color="${normal_color}">成为最强助攻王！心意满分！快来围观~</font>`
)

const colorWhite = "#FFFFFF"

// 礼物贵族类型
const (
	VipTypeLiveNoble = iota + 1
	VipTypeLiveHighness
)

// 连击类型
const (
	ComboableTypeSingle = iota + 1 // 单人连击
	ComboableTypeMulti             // 直播间一起送连击
)

// SetOrder 字段用于礼物的定时下架与定时排序
type SetOrder struct {
	EffectiveTime time.Time `bson:"effective_time" json:"effective_time"`
	Order         int       `bson:"order" json:"order"`
}

// Combo 连击档位信息
type Combo struct {
	TargetPrice    int64  `bson:"target_price" json:"-"`
	RepeatAddPrice int64  `bson:"repeat_add_price,omitempty" json:"-"` // 达到最高档后的固定阈值: LastTargetPrice + N * RepeatAddPrice
	ComboEffect    string `bson:"combo_effect" json:"-"`
	WebComboEffect string `bson:"web_combo_effect" json:"-"`
}

// Gift mongodb 查找的单条 gifts 信息的结构
type Gift struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`

	Type      int `bson:"type" json:"type"`
	Exclusive int `bson:"exclusive" json:"-"` // 针对非用户定制（TypeCustom）和房间定制（TypeRoomCustom）的礼物，用于服务端判断是否属于专属礼物的类型（目前仅对随机礼物有效）

	GiftID    int64  `bson:"gift_id" json:"gift_id"`
	Name      string `bson:"name" json:"name"`
	NameClean string `bson:"name_clean" json:"-"`

	Icon       string `bson:"icon" json:"icon_url"`
	IconActive string `bson:"icon_active" json:"icon_active_url,omitempty"`

	SetOrder *SetOrder `bson:"setorder,omitempty" json:"setorder,omitempty"`

	Intro        string `bson:"intro" json:"intro,omitempty"`
	IntroIcon    string `bson:"intro_icon" json:"intro_icon_url,omitempty"`
	IntroOpenURL string `bson:"intro_open_url" json:"intro_open_url,omitempty"`

	Effect         string `bson:"effect" json:"effect_url,omitempty"`
	WebEffect      string `bson:"web_effect" json:"web_effect_url,omitempty"`
	EffectDuration int64  `bson:"effect_duration" json:"effect_duration,omitempty"`
	NotifyDuration int64  `bson:"notify_duration" json:"notify_duration,omitempty"`

	Comboable      int    `bson:"comboable" json:"comboable,omitempty"`
	ComboEffect    string `bson:"combo_effect" json:"combo_effect_url,omitempty"`
	WebComboEffect string `bson:"web_combo_effect" json:"web_combo_effect_url,omitempty"`

	Combos []Combo `bson:"combos,omitempty" json:"-"` // 从小到大的档位顺序

	LabelIcon string `bson:"label_icon" json:"label_icon_url,omitempty"`

	Price          int64   `bson:"price" json:"price"`                       // 礼物价格
	Point          int64   `bson:"point" json:"point,omitempty"`             // 亲密度点数，目前仅免费礼物使用
	MedalPointBuff float64 `bson:"medal_point_buff" json:"medal_point_buff"` // 亲密度倍率 buff，仅对价格翻倍计算，跟其他 buff 叠加时乘法累加

	BaseGiftID int64          `bson:"base_gift_id,omitempty" json:"-"` // 对应的原型礼物 ID
	Attr       goutil.BitMask `bson:"attr,omitempty" json:"-"`

	Order     int       `bson:"order" json:"order"`
	AddedTime time.Time `bson:"added_time,omitempty" json:"-"`

	// 上下架时间
	ToggleTime int64 `bson:"toggle_time,omitempty" json:"toggle_time"` // 上下架时间，单位：秒

	UserID int64 `bson:"user_id" json:"-"`

	RoomID int64 `bson:"room_id" json:"-"`

	VipType    int `bson:"vip_type" json:"vip_type"`
	NobleLevel int `bson:"noble_level" json:"noble_level"`

	MedalLevel int `bson:"medal_level" json:"medal_level"`

	UserLevel int `bson:"user_level" json:"user_level"`

	NotifyMessage  string `bson:"notify_message,omitempty" json:"-"`
	NotifyBubbleID int64  `bson:"notify_bubble_id,omitempty" json:"-"`

	AllowedNums []int `bson:"allowed_nums,omitempty" json:"allowed_nums,omitempty"`

	LuckyEffect    string `bson:"lucky_effect" json:"-"`
	WebLuckyEffect string `bson:"web_lucky_effect" json:"-"`
	IsLucky        bool   `bson:"-" json:"is_lucky,omitempty"`     // 是否是随机礼物，用于客户端判断
	IsSuperFan     bool   `bson:"-" json:"is_super_fan,omitempty"` // 是否是超粉礼物，只有超粉才能送

	DiyEntry *DiyEntry `bson:"-" json:"diy_entry,omitempty"`

	Sponsor *Sponsor `bson:"-" json:"sponsor,omitempty"`
}

// Sponsor 冠名标识配置
type Sponsor struct {
	IconURL   string `json:"icon_url"`
	Text      string `json:"text"`
	TextColor string `json:"text_color"`
}

// DiyEntry DIY 入口
type DiyEntry struct {
	ImageURL string `json:"image_url"`
}

// Collection 返回 Gift 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("gifts")
}

// NotifyGift 赠送礼物消息中礼物具体信息
type NotifyGift struct {
	GiftID         int64          `json:"gift_id"`
	Name           string         `json:"name"`
	IconURL        string         `json:"icon_url"`
	Price          int64          `json:"price"`
	Num            int            `json:"num"`
	Sponsor        *Sponsor       `json:"sponsor,omitempty"`
	EffectURL      string         `json:"effect_url,omitempty"`
	NewEffectURL   string         `json:"new_effect_url,omitempty"`
	WebEffectURL   string         `json:"web_effect_url,omitempty"`
	EffectDuration int64          `json:"effect_duration,omitempty"`
	EffectOptions  *EffectOptions `json:"effect_options,omitempty"`
	NotifyDuration int64          `json:"notify_duration,omitempty"`
}

// NotifyPayload 赠送礼物飘屏信息
type NotifyPayload struct {
	Type       string `json:"type"`
	NotifyType string `json:"notify_type"`
	Event      string `json:"event"`
	RoomID     int64  `json:"room_id"`
	// TODO: 迁移解决包循环引用问题
	User         *liveuser.Simple `json:"user"`
	Bubble       *bubble.Simple   `json:"bubble,omitempty"` // 用户消息气泡
	Gift         *NotifyGift      `json:"gift"`
	NotifyBubble *bubble.Simple   `json:"notify_bubble,omitempty"`
	Message      string           `json:"message"`
}

// NotifyBuilder 全站广播 builder
// NOTICE: 避免引入 room 包
type NotifyBuilder struct {
	RoomID          int64
	CreatorUsername string
	User            *liveuser.Simple
	Bubble          *bubble.Simple // 用户消息气泡

	NotifyGift *NotifyGift
	Gift       *Gift
	// TODO: 待整合
	GiftNum                int    // 本次送礼的数量
	ComboNum               int    // 本次连击礼物的总数
	MultiComboTop1Username string // 一起送助攻王用户名
	notifyBubble           *bubble.Simple

	LuckyGift    *Gift
	LuckyGiftNum int
}

func (nb *NotifyBuilder) findBubble() {
	bubbleID := nb.Gift.NotifyBubbleID
	if bubbleID <= 0 {
		// 总是查询默认气泡
		bubbleID = bubble.BubbleIDDefault
	}
	var err error
	nb.notifyBubble, err = bubble.FindSimple(bubbleID)
	if err != nil {
		logger.WithField("bubble_id", bubbleID).Error(err)
		// PASS
	}
}

func (nb *NotifyBuilder) formatMessage() string {
	format := map[string]string{
		"username":         html.EscapeString(nb.User.Username),
		"creator_username": html.EscapeString(nb.CreatorUsername),
		"gift_name":        html.EscapeString(nb.Gift.Name),
		"highlight_color":  colorWhite,
		"normal_color":     colorWhite,
		"top1_username":    html.EscapeString(nb.MultiComboTop1Username),
	}
	if nb.ComboNum > 0 {
		// 飘屏信息中送礼数量优先使用连击总数
		format["gift_num"] = strconv.Itoa(nb.ComboNum)
	} else {
		format["gift_num"] = strconv.Itoa(nb.GiftNum)
	}
	if nb.LuckyGift != nil {
		format["lucky_gift_name"] = html.EscapeString(nb.LuckyGift.Name)
		format["lucky_gift_num"] = strconv.Itoa(nb.LuckyGiftNum)
	}
	if nb.notifyBubble != nil {
		nb.notifyBubble.AppendFormatParams(format)
	}
	return goutil.FormatMessage(nb.Gift.NotifyMessageTemplate(), format)
}

// Build NotifyPayload
func (nb *NotifyBuilder) Build() *NotifyPayload {
	if nb.notifyBubble == nil {
		nb.findBubble()
	}
	res := &NotifyPayload{
		Type:         liveim.TypeNotify,
		NotifyType:   liveim.TypeGift,
		Event:        liveim.EventSend,
		RoomID:       nb.RoomID,
		User:         nb.User,
		Bubble:       nb.Bubble,
		NotifyBubble: nb.notifyBubble,
		Message:      nb.formatMessage(),
	}
	if nb.NotifyGift != nil {
		res.Gift = nb.NotifyGift
	} else {
		res.Gift = NewNotifyGift(nb.Gift, nb.GiftNum)
	}
	return res
}

// NewNotifyGift new NotifyGift from gift
func NewNotifyGift(g *Gift, num int) *NotifyGift {
	return &NotifyGift{
		GiftID:         g.GiftID,
		Name:           g.Name,
		IconURL:        g.Icon,
		EffectURL:      g.Effect,
		WebEffectURL:   g.WebEffect,
		EffectDuration: g.EffectDuration,
		NotifyDuration: g.NotifyDuration,
		Price:          g.Price,
		Num:            num,
	}
}

// NewNotifyGiftLucky 广播消息中的 lucky 礼物
func NewNotifyGiftLucky(giftSend *Gift, giftReceive *Gift, num int) *NotifyGift {
	return &NotifyGift{
		GiftID:         giftSend.GiftID,
		Name:           giftSend.Name,
		IconURL:        giftSend.Icon,
		NotifyDuration: giftSend.NotifyDuration,
		Price:          giftSend.Price,
		EffectURL:      giftReceive.LuckyEffect,
		WebEffectURL:   giftReceive.WebLuckyEffect,
		Num:            num,
	}
}

// ConstructIconURL 使用 Icon 的相对路径得到完整的 IconURL 路径
func (g *Gift) ConstructIconURL() {
	g.IntroIcon = storage.ParseSchemeURL(g.IntroIcon)
	g.Icon = storage.ParseSchemeURL(g.Icon)
	g.LabelIcon = storage.ParseSchemeURL(g.LabelIcon)
	g.Effect = storage.ParseSchemeURLs(g.Effect)
	g.WebEffect = storage.ParseSchemeURLs(g.WebEffect)
	g.ComboEffect = storage.ParseSchemeURLs(g.ComboEffect)
	g.WebComboEffect = storage.ParseSchemeURL(g.WebComboEffect)
	g.IconActive = storage.ParseSchemeURL(g.IconActive)
	g.LuckyEffect = storage.ParseSchemeURLs(g.LuckyEffect)
	g.WebLuckyEffect = storage.ParseSchemeURLs(g.WebLuckyEffect)
	if len(g.Combos) > 0 {
		for i := range g.Combos {
			g.Combos[i].ComboEffect = storage.ParseSchemeURLs(g.Combos[i].ComboEffect)
			g.Combos[i].WebComboEffect = storage.ParseSchemeURLs(g.Combos[i].WebComboEffect)
		}
		sort.Slice(g.Combos, func(i, j int) bool {
			// 从小到大对档位排序
			return g.Combos[i].TargetPrice < g.Combos[j].TargetPrice
		})
	}
}

// MedalPoint 礼物亲密度
func (g *Gift) MedalPoint(giftNum int) int64 {
	// 默认提供价格一倍的亲密度
	medalPoint := float64(g.Price)
	if g.Type != TypeFree && g.Type != TypeDrawSend {
		// 非免费、随机礼物需要乘以倍率，最低为一倍
		medalPoint *= max(g.MedalPointBuff, 1)
	}
	if g.AllowAddMedalPoint() {
		medalPoint += float64(g.Point)
	}
	return int64(math.Floor(medalPoint * float64(giftNum)))
}

// ComboLevel 连击档位
type ComboLevel struct {
	TargetPrice              int64
	TargetNum                int64
	AchievedPrice            int64
	AchievedNum              int64
	ComboEffect              string
	WebComboEffect           string
	IsReachMaxFixedThreshold bool // 已到达最大档位，固定阈值触发
}

// ComboLevel 查找连击档位
func (g *Gift) ComboLevel(price int64) *ComboLevel {
	var comboLevel *ComboLevel
	defer func() {
		ceilFunc := func(price int64) int64 {
			return int64(math.Ceil(float64(price) / float64(g.Price)))
		}
		comboLevel.TargetNum = ceilFunc(comboLevel.TargetPrice)
		comboLevel.AchievedNum = ceilFunc(comboLevel.AchievedPrice)
	}()

	comboLevelCount := len(g.Combos)
	for i := comboLevelCount - 1; i >= 0; i-- { // 从大到小确认档位
		combo := g.Combos[i]
		if price >= combo.TargetPrice {
			if i == comboLevelCount-1 {
				// 最高档位
				comboLevel = &ComboLevel{
					TargetPrice:              ((price-combo.TargetPrice)/combo.RepeatAddPrice+1)*combo.RepeatAddPrice + combo.TargetPrice,
					AchievedPrice:            (price-combo.TargetPrice)/combo.RepeatAddPrice*combo.RepeatAddPrice + combo.TargetPrice,
					ComboEffect:              combo.ComboEffect,
					WebComboEffect:           combo.WebComboEffect,
					IsReachMaxFixedThreshold: true,
				}
				return comboLevel
			}
			comboLevel = &ComboLevel{
				AchievedPrice:  combo.TargetPrice,
				ComboEffect:    combo.ComboEffect,
				WebComboEffect: combo.WebComboEffect,
			}
			comboLevel.TargetPrice = g.Combos[i+1].TargetPrice
			return comboLevel
		}
	}
	minComboLevel := g.Combos[0]
	comboLevel = &ComboLevel{
		TargetPrice:    minComboLevel.TargetPrice,
		AchievedPrice:  0,
		ComboEffect:    minComboLevel.ComboEffect,
		WebComboEffect: minComboLevel.WebComboEffect,
	}
	return comboLevel
}

// FindShowingGiftByGiftID finds the showing gifts by gift_id
func FindShowingGiftByGiftID(giftID int64) (*Gift, error) {
	res, err := FindAllShowingGifts()
	if err != nil {
		return nil, err
	}
	for i := range res {
		if res[i].GiftID == giftID {
			return &res[i], nil
		}
	}
	return nil, nil
}

// FindByGiftID find one gift by gift_id
func FindByGiftID(giftID int64) (*Gift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var g *Gift
	err := Collection().FindOne(ctx, bson.M{"gift_id": giftID}).Decode(&g)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	g.ConstructIconURL()
	return g, nil
}

// FindGiftMapByGiftIDs 通过 giftIDs 查询礼物, giftIDs 函数内部已去重
func FindGiftMapByGiftIDs(giftIDs []int64) (map[int64]*Gift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx,
		bson.M{"gift_id": bson.M{"$in": util.Uniq(giftIDs)}})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var l []*Gift
	err = cur.All(ctx, &l)
	if err != nil {
		return nil, err
	}
	for i := range l {
		l[i].ConstructIconURL()
	}
	m := goutil.ToMap(l, "GiftID")
	return m.(map[int64]*Gift), nil
}

// FindAllShowingGifts 查询所有礼物，type 正序, order 正序排序
func FindAllShowingGifts() ([]Gift, error) {
	key := keys.KeyOnlineGifts0.Format()
	cache := service.Cache5s

	v, ok := cache.Get(key)
	if ok {
		return copyGiftSlice(v.([]Gift)), nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 查询时包括 order > 0 或者 setorder != nil 的数据
	filter := bson.M{
		"type":       bson.M{"$gt": TypeHide},
		"added_time": bson.M{"$lt": goutil.TimeNow()},
		"$or": bson.A{
			bson.M{"setorder": bson.M{"$ne": nil}},
			bson.M{"order": bson.M{"$gt": OrderHide}},
		},
	}
	var rec []Gift
	cur, err := Collection().Find(ctx, filter, options.Find().
		SetSort(bson.D{{Key: "type", Value: 1}, {Key: "order", Value: 1}}))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &rec)
	if err != nil {
		return nil, err
	}

	recOrderValid := make([]Gift, 0, len(rec))
	for i := range rec {
		rec[i].ConstructIconURL()
		// NOTICE: 不会直接更新 order，需要等待缓存结束
		rec[i].checkOrder()
		// 只返回 order > 0 的数据
		if rec[i].Order > OrderHide {
			recOrderValid = append(recOrderValid, rec[i])
		}
	}
	// 统一用礼物的缓存
	findDiyEntry(recOrderValid)

	cache.Set(key, recOrderValid, 0)
	return copyGiftSlice(recOrderValid), nil
}

// FindShowingMultiComboGifts 查找当前生效的一起送连击礼物
func FindShowingMultiComboGifts() ([]Gift, error) {
	g, err := FindAllShowingGifts()
	if err != nil {
		return nil, err
	}
	res := make([]Gift, 0, len(g)/4) // 实际一起送礼物数量较少，除 4 后的长度应该也够
	for i := range g {
		if g[i].Comboable == ComboableTypeMulti {
			res = append(res, g[i])
		}
	}
	return res, nil
}

func findDiyEntry(gifts []Gift) {
	giftIDs := make([]int64, len(gifts))
	for i := range gifts {
		giftIDs[i] = gifts[i].GiftID
	}
	entry := diygifts.FindDiyEntry(giftIDs)
	if len(entry) == 0 {
		return
	}
	for i := range gifts {
		if imageURL := entry[gifts[i].GiftID]; imageURL != "" {
			gifts[i].DiyEntry = &DiyEntry{ImageURL: imageURL}
		}
	}
}

// toTypeMap 将礼物数组通过 type 归类
// order 的排序是通过数据库查询的时候做的
func toTypeMap(gifts []Gift) map[int][]Gift {
	res := make(map[int][]Gift, 8)
	for i := range gifts {
		switch gifts[i].Type {
		case TypeNormal:
			if gifts[i].NobleLevel > 0 {
				res[TypeNoble] = append(res[TypeNoble], gifts[i])
			} else {
				res[TypeNormal] = append(res[TypeNormal], gifts[i])
			}
		default:
			res[gifts[i].Type] = append(res[gifts[i].Type], gifts[i])
		}
	}
	return res
}

func copyGiftSlice(org []Gift) []Gift {
	dst := make([]Gift, len(org))
	copy(dst, org)
	return dst
}

// GroupGiftsOptionsUser 礼物分组用户参数
type GroupGiftsOptionsUser struct {
	UserID    int64
	UserLevel int
}

// GroupGiftsOptions 礼物分组参数
// NOTICE: 房间 RoomID 为 nil 时, 默认对应的全部直播间定制礼物都需要展示
// 匿名用户 User.UserID 依然需要传 0, 0 和 user=nil 含义不同，user==nil 是需要获取展示所有用户定制礼物（排序使用）
type GroupGiftsOptions struct {
	User   *GroupGiftsOptionsUser
	RoomID *int64
	// 过滤随机礼物
	HideLuckyGifts bool
}

func filterGiftsByUserAndRoom(gifts []Gift, options GroupGiftsOptions) []Gift {
	filteredGifts := make([]Gift, 0, len(gifts))
	// 仅支持配置单个专属用户或专属房间
	// NOTICE: 非普通礼物的场景，仅隐藏展示，不限制赠送
	for _, gift := range gifts {
		if gift.UserID != 0 {
			if options.User != nil && options.User.UserID != gift.UserID {
				continue
			}
		}
		if gift.RoomID != 0 {
			if options.RoomID != nil && *options.RoomID != gift.RoomID {
				continue
			}
		}
		filteredGifts = append(filteredGifts, gift)
	}
	return filteredGifts
}

// GroupGifts 礼物分组
// returns: [TabGiftLimit][]Gift 对应普通礼物栏，贵族礼物栏，专属礼物栏，粉丝礼物栏中需要展示的礼物; []Gift 对应不会在礼物栏中展示的礼物
func GroupGifts(gifts []Gift, options GroupGiftsOptions) ([TabGiftLimit][]Gift, []Gift) {
	var giftsShow [TabGiftLimit][]Gift

	allGifts := make([]Gift, 0, len(gifts))
	for i := range gifts {
		g := gifts[i]
		switch g.Type {
		case TypeDrawSend:
			if options.HideLuckyGifts {
				continue
			}
			g.IsLucky = true
			if g.IsDrawSendSuperFan() {
				g.IsSuperFan = true
			}
		case TypeSuperFan:
			g.IsSuperFan = true
		}
		allGifts = append(allGifts, g)
	}
	// TODO: 考虑先统一判断权限，需要注意是否会影响最终下发的礼物顺序
	giftsMap := toTypeMap(allGifts)

	normalGifts := giftsMap[TypeNormal]
	giftsShow[TabGiftNormal] = filterGiftsByUserAndRoom(normalGifts, options)

	luckyGifts := giftsMap[TypeDrawSend]
	for i := range luckyGifts {
		// TODO: 如果随机礼物数量比较多的话可以优化成批量验证权限
		if options.RoomID != nil {
			if !luckyGifts[i].OwnedByRoom(*options.RoomID) {
				continue
			}
		}
		if options.User != nil &&
			!luckyGifts[i].OwnedByUser(options.User.UserID, options.User.UserLevel) {
			continue
		}
		if luckyGifts[i].IsExclusiveTabGift() {
			giftsShow[TabGiftExclusive] = append(giftsShow[TabGiftExclusive], luckyGifts[i])
			continue
		}
		// 超粉专属随机礼物不放在礼物栏中展示，需要在粉丝礼物栏中单独处理
		if luckyGifts[i].IsDrawSendSuperFan() {
			giftsMap[TypeSuperFan] = append(giftsMap[TypeSuperFan], luckyGifts[i])
			continue
		}
		giftsShow[TabGiftNormal] = append(giftsShow[TabGiftNormal], luckyGifts[i]) // 随机礼物也需要显示
	}

	giftsShow[TabGiftNoble] = filterGiftsByUserAndRoom(giftsMap[TypeNoble], options)
	gm := filterGiftsByUserAndRoom(giftsMap[TypeMedal], options)
	gsf := filterGiftsByUserAndRoom(giftsMap[TypeSuperFan], options)
	giftsShow[TabGiftMedal] = make([]Gift, 0, len(gm)+len(gsf))
	giftsShow[TabGiftMedal] = append(giftsShow[TabGiftMedal], gm...)
	giftsShow[TabGiftMedal] = append(giftsShow[TabGiftMedal], gsf...)

	var showingLen int
	for i := range giftsShow {
		showingLen += len(giftsShow[i])
	}

	// 建立新的数组以分配内存
	// TODO: 移除兼容下发的 giftsExtra 字段
	giftsExtra := make([]Gift, len(giftsMap[TypeFree]), len(gifts)-showingLen)
	copy(giftsExtra, giftsMap[TypeFree])
	giftsExtra = append(giftsExtra, giftsMap[TypeRebate]...)
	// 用户定制礼物
	customShow, customExtra := userOwnedGifts(giftsMap[TypeCustom], options)
	for i := range customShow {
		if customShow[i].IsExclusiveTabGift() {
			// 普通定制礼物
			giftsShow[TabGiftExclusive] = append(giftsShow[TabGiftExclusive], customShow[i])
			continue
		}
		if customShow[i].NobleLevel > 0 {
			// 贵族定制礼物
			giftsShow[TabGiftNoble] = append(giftsShow[TabGiftNoble], customShow[i])
		}
	}
	if len(customExtra) > 0 {
		giftsExtra = append(giftsExtra, customExtra...)
	}
	// 直播间定制礼物
	custom := roomOwnedGifts(giftsMap[TypeRoomCustom], options)
	if len(custom) > 0 {
		giftsShow[TabGiftExclusive] = append(giftsShow[TabGiftExclusive], custom...)
	}
	return giftsShow, giftsExtra
}

func userOwnedGifts(gifts []Gift, options GroupGiftsOptions) (show, extra []Gift) {
	if len(gifts) == 0 || options.User == nil { // options.User == nil（管理员在后台对礼物按栏目排序）
		return gifts, nil
	}
	if options.User.UserID == 0 {
		// 游客用户
		return nil, gifts
	}
	availableGiftIDs, err := livecustom.FindAllUserCustomGiftIDs(options.User.UserID)
	if err != nil {
		logger.WithField("user_id", options.User.UserID).Error(err)
		// PASS
	}
	show = make([]Gift, 0, len(availableGiftIDs))
	extra = make([]Gift, 0, len(gifts))
	for i := range gifts {
		if gifts[i].UserID == options.User.UserID ||
			(gifts[i].UserLevel > 0 && options.User.UserLevel >= gifts[i].UserLevel) ||
			goutil.HasElem(availableGiftIDs, gifts[i].GiftID) {
			show = append(show, gifts[i])
			continue
		}
		extra = append(extra, gifts[i])
	}
	return show, extra
}

func roomOwnedGifts(gifts []Gift, options GroupGiftsOptions) []Gift {
	if len(gifts) == 0 || options.RoomID == nil {
		return gifts
	}
	customGiftIDs, err := livecustom.FindAllRoomCustomGiftIDs(*options.RoomID)
	if err != nil {
		logger.WithField("room_id", options.RoomID).Error(err)
		// PASS
	}
	show := make([]Gift, 0, len(customGiftIDs))
	for i := range gifts {
		if (gifts[i].RoomID != 0 && gifts[i].RoomID == *options.RoomID) ||
			goutil.HasElem(customGiftIDs, gifts[i].GiftID) {
			show = append(show, gifts[i])
			continue
		}
	}
	return show
}

func (g *Gift) checkOrder() {
	// 检查 SetOrder 是否为 nil，以及生效时间是否小于等于当前时间
	if g.SetOrder == nil {
		return
	}
	defer func() { g.SetOrder = nil }()
	if g.SetOrder.EffectiveTime.After(goutil.TimeNow()) {
		return
	}

	so := g.SetOrder
	giftID := g.GiftID
	goutil.Go(func() {
		lockKey := keys.LockGiftSetOrder1.Format(giftID)
		ok, err := service.Redis.SetNX(lockKey, giftID, 5*time.Second).Result()
		if err != nil {
			logger.Errorf("failed to set nx key while update setorder for gifts: %v", err)
			return
		}
		if !ok {
			return
		}

		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		updateAttrs := bson.M{
			// 设定 order 为 setorder.order
			"order": so.Order,
		}
		// 只有在礼物上下架状态切换时，才会更新 toggle_time 的值
		if g.Order != so.Order && (g.Order == OrderHide || so.Order == OrderHide) {
			updateAttrs["toggle_time"] = so.EffectiveTime.Unix()
		}
		_, err = Collection().UpdateOne(ctx,
			bson.M{"gift_id": giftID},
			bson.M{
				"$set": updateAttrs,
				// 重设 setorder
				"$unset": bson.M{"setorder": ""},
			})
		if err != nil {
			logger.Errorf("failed to update setorder for gift id %d: %v", giftID, err)
		}
	})
}

// NotifyMessageTemplate 飘屏模板
func (g Gift) NotifyMessageTemplate() string {
	if g.Type == TypeDrawReceive || g.NotifyMessage != "" {
		// 随机礼物总是返回 NotifyMessage
		return g.NotifyMessage
	}
	if g.Comboable == ComboableTypeMulti {
		return DefaultRoomMultiComboNotifyMessage
	}
	return DefaultComboNotifyMessage
}

// AllowPointAddActivity 是否允许 point 添加榜单
func (g Gift) AllowPointAddActivity() bool {
	return g.Attr.IsSet(AttrPointAddActivity)
}

// AllowAddMedalPoint 是否允许添加勋章积分
func (g Gift) AllowAddMedalPoint() bool {
	return !g.Attr.IsSet(AttrDisableMedalPoint)
}

// AllowAddScore 是否允许添加热度
func (g Gift) AllowAddScore() bool {
	return !g.Attr.IsSet(AttrDisableAddScore)
}

// AlwaysNotify 是否总是飘屏
// TODO: 目前就背包礼物支持
func (g Gift) AlwaysNotify() bool {
	return g.Attr.IsSet(AttrAlwaysNotify)
}

// AllowPointAddPK 免费礼物是否增加 PK 值
func (g Gift) AllowPointAddPK() bool {
	return g.Attr.IsSet(AttrPointAddPK)
}

// AllowPointAddRank 免费礼物使用亲密度值加榜单
func (g Gift) AllowPointAddRank() bool {
	return g.Attr.IsSet(AttrPointAddRank)
}

// IsDisableDrawRoomCustomAfterSSR 是否抽出大奖后失去抽奖资格
func (g Gift) IsDisableDrawRoomCustomAfterSSR() bool {
	return g.Attr.IsSet(AttrDisableDrawRoomCustomAfterSSR)
}

// IsHotCard 是否是热度卡
func (g Gift) IsHotCard() bool {
	return g.Type == TypeFree && g.Attr.IsSet(AttrIsHotCard)
}

// ComboFlags 连击状态
// NOTICE: g.Comboable 需要另行判断
// TODO: 仅支持一次送够的情况，不支持正常连击
func (g Gift) ComboFlags(num int) (bm goutil.BitMask) {
	price := g.Price * int64(num)
	if g.ComboEffect != "" && g.Effect == "" && price >= ComboEffectMinPrice {
		bm.Set(ComboFlagEffect)
	}
	if price >= ComboNotifyMinPrice {
		bm.Set(ComboFlagNotify)
	}
	return
}

// PKScores 送礼 PK 积分
// score: PK 积分中有价值的部分
// freeScore: PK 积分中无价值的部分
func (g Gift) PKScores(num int) (score int64, freeScore int64) {
	score = g.Price * int64(num)
	if g.AllowPointAddPK() {
		freeScore = g.Point * int64(num)
	}
	return
}

// AbleToSend 是否是能送出的礼物（指正常花费送出）
func AbleToSend(giftType int) bool {
	switch giftType {
	case TypeNormal, TypeCustom, TypeMedal, TypeNoble,
		TypeRoomCustom, TypeSuperFan, TypeUpgrade:
		return true
	}
	return false
}

// ComboRemainDuration 连击倒计时时长
func ComboRemainDuration(totalPrice int64) time.Duration {
	if totalPrice < 1000 {
		return 5 * time.Second
	}
	return 15 * time.Second
}

// ShortcutGift 快捷送礼礼物信息
type ShortcutGift struct {
	GiftID    int64  `json:"gift_id"`
	Name      string `json:"name"`
	IconURL   string `json:"icon_url"`
	Price     int64  `json:"price"` // 单位：钻石
	Comboable int    `json:"comboable,omitempty"`
}

// NewShortcutGift new ShortcutGift from gift
func (g Gift) NewShortcutGift() *ShortcutGift {
	return &ShortcutGift{
		GiftID:    g.GiftID,
		Name:      g.Name,
		IconURL:   g.Icon,
		Price:     g.Price,
		Comboable: g.Comboable,
	}
}

// IsDrawSendSuperFan 是否是随机送超粉礼物
func (g *Gift) IsDrawSendSuperFan() bool {
	return g.Attr.IsSet(AttrDrawSendSuperFan)
}

// AllowPointAddMultiConnect 免费礼物是否增加主播连线积分
func (g Gift) AllowPointAddMultiConnect() bool {
	return g.Attr.IsSet(AttrPointAddMultiConnect)
}

// IsUpgradeBaseGift 是否是升级基础礼物
func (g Gift) IsUpgradeBaseGift() bool {
	return g.Attr.IsSet(AttrUpgradeBase)
}

// MultiConnectScores 主播连线积分
func (g Gift) MultiConnectScores(num int64) (score, freeScore int64) {
	score = g.Price * num
	if g.AllowPointAddMultiConnect() {
		freeScore = g.Point * num
	}
	return
}
