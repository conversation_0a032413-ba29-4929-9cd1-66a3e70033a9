package cleanup

import (
	"context"
	"strconv"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/redis/imuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Command server
type Command struct {
}

// Name command's name
func (c *Command) Name() string {
	return "cleanup"
}

// Run command
func (c *Command) Run(conf *config.Config) error {
	err := service.Init(&config.Conf.Service)
	if err != nil {
		return err
	}
	eg, _ := errgroup.WithContext(context.Background())
	eg.Go(func() error {
		return cleanupNeteaseRecords()
	})
	eg.Go(func() error {
		// TODO: 此操作待删除，被 imserver 的 rpc/cron/cleanup 替代
		_, err := cleanupIMRooms()
		return err
	})

	return eg.Wait()
}

func cleanupNeteaseRecords() error {
	ret, err := service.Vod.ListVideo(0, 500, 0, 0, "asc")
	if err != nil {
		return err
	}

	limiter := time.NewTicker(time.Second * 5)
	defer limiter.Stop()
	t := util.TimeToUnixMilli(goutil.TimeNow().AddDate(0, -6, 0))

	var vids []int64
	pageNum := ret.PageNum
loop:
	for p := 1; p <= pageNum; p++ {
		<-limiter.C
		ret, err = service.Vod.ListVideo(p, 500, 0, 0, "asc")
		if err != nil {
			logger.WithField("page", p).Errorf("list video error: %v", err)
			if strings.Contains(err.Error(), "请求offset") && strings.Contains(err.Error(), "超出限制") {
				break
			}
			continue
		}

		// delete old video (6 months ago)
		for _, v := range ret.List {
			if v.CreateTime < t {
				vids = append(vids, v.Vid)
			} else {
				// we sort records by asc so that we can break here
				break loop
			}
		}
	}

	count := 0
	for _, vid := range vids {
		<-limiter.C
		err = service.Vod.DeleteVideo(vid)
		if err != nil {
			logger.WithField("vid", vid).Errorf("delete video error: %v", err)
		} else {
			logger.WithField("vid", vid).Info("video deleted")
			count++
		}
	}

	logger.Infof("%d videos deleted", count)

	return nil
}

func cleanupIMRooms() (int64, error) {
	imKeys, err := service.IMRedis.Keys("live-service:im:room:*:members").Result()
	if err != nil {
		logger.Error(err)
		return 0, err
	}
	score := goutil.TimeNow().Add(-time.Hour).Unix()
	scoreStr := strconv.FormatInt(score, 10)
	count := int64(0)
	for i := 0; i < len(imKeys); i++ {
		roomID := keys.ParseIMRoomID(imKeys[i])
		n, err := service.IMRedis.ZRemRangeByScore(imKeys[i], "-inf", scoreStr).Result()
		if err != nil {
			logger.Error(err)
			return count, err
		}
		if n != 0 {
			count += n
			logger.Infof("room %d clear count: %d", roomID, n)
		}
		err = imuser.RefreshUsers(roomID, 0)
		if err != nil {
			logger.Errorf("refresh room %d users error: %v", roomID, err)
			// PASS
		}
	}
	return count, nil
}
