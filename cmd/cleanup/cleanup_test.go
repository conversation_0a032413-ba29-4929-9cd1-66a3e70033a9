package cleanup

import (
	"strings"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestRun(t *testing.T) {
	assert := assert.New(t)
	var c Command
	assert.EqualError(c.Run(config.Conf), "点播服务未开通")
}

func TestCommandName(t *testing.T) {
	assert := assert.New(t)
	var c Command
	assert.Equal("cleanup", c.Name())
}

func TestCleanupNeteaseRecords(t *testing.T) {
	assert := assert.New(t)
	err := cleanupNeteaseRecords()
	assert.Error(err)
	contains := strings.Contains(err.Error(), "AppKey")
	if !contains {
		contains = strings.Contains(err.Error(), "未开通")
	}
	assert.True(contains, "%v does not contain specified error message", err)

	/* oldVodClient := service.Vod
	defer func() {
		service.Vod = oldVodClient
	}()
	service.Vod = vod.NewVodClient(&vod.Config{
		AppKey:    "appkey",
		AppSecret: "testkey",
	})
	err = cleanupNeteaseRecords()
	assert.NoError(err) */
}

func TestCleanupIMRooms(t *testing.T) {
	assert := assert.New(t)
	_, _ = cleanupIMRooms()
	service.IMRedis.ZAdd(keys.KeyIMRoomMembers1.Format(1), &redis.Z{
		Score:  float64(goutil.TimeNow().AddDate(0, 0, -2).Unix()),
		Member: "test_member",
	})
	count, err := cleanupIMRooms()
	assert.NoError(err)
	assert.Equal(int64(1), count)
}
