package imserver

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestCommandTest(t *testing.T) {
	var c Command
	assert.Equal(t, "imserver", c.Name())
}

func TestServerAddr(t *testing.T) {
	assert := assert.New(t)
	config.Conf.IM.RPCAddress = "rpc"
	config.Conf.IM.RPCPort = 1234
	config.Conf.IM.Address = "im"
	config.Conf.IM.Port = 1234
	srv := newRPCServer()
	assert.Equal("rpc:1234", srv.Addr)
	srv = newWSServer()
	assert.Equal("im:1234", srv.Addr)
}
