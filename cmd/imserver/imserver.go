package imserver

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/cmd/base"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/im"
	"github.com/MiaoSiLa/live-service/controllers/imrpc"
	"github.com/MiaoSiLa/live-service/internal/controllers/health"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// Command server
type Command struct {
}

// Name command's name
func (c *Command) Name() string {
	return "imserver"
}

// Run command
func (c *Command) Run(conf *config.Config) (err error) {
	initService()
	defer im.ClearIM()
	handler.SetMode(config.Conf.HTTP.Mode)

	g, ctx := errgroup.WithContext(context.Background())

	rpcSrv := newRPCServer()
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", rpcSrv.Addr)
		return rpcSrv.ListenAndServe()
	})

	wsSrv := newWSServer()
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", wsSrv.Addr)
		return wsSrv.ListenAndServe()
	})

	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)

	select {
	case <-ctx.Done():
	case <-osCh:
	}
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err = wsSrv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown websocket server failed: %v", err)
	}
	if err = rpcSrv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown rpc server failed: %v", err)
	}

	return g.Wait()
}

type mounter interface {
	Mount(r *gin.Engine)
}

func newRPCServer() *http.Server {
	r := base.NewEngine()
	pprof.Register(r)

	handlers := []mounter{
		im.Handler(),
		imrpc.Handler(),
		health.Handler(),
	}
	for _, h := range handlers {
		h.Mount(r)
	}
	r.GET("metrics", im.MetricsHandler())

	srv := &http.Server{
		Addr: fmt.Sprintf("%s:%d", config.Conf.IM.RPCAddress,
			config.Conf.IM.RPCPort),
		Handler: r}
	return srv
}

func newWSServer() *http.Server {
	r := base.NewEngine()
	h := im.Handler()
	h.Mount(r)

	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", config.Conf.IM.Address, config.Conf.IM.Port),
		Handler: r}
	return srv
}
