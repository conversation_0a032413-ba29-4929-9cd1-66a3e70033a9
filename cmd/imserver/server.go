package imserver

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
)

func initService() {
	conf := config.Conf.Service
	var err error

	service.MongoDB, err = mongodb.NewMongoDB(&conf.MongoDB)
	if err != nil {
		logger.Fatal(err)
		return
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		logger.Fatal(err)
		return
	}
	service.IMRedis, err = serviceredis.NewRedisClient(&conf.IMRedis)
	if err != nil {
		logger.Fatal(err)
		return
	}
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		logger.Fatal(err)
		return
	}

	service.Storage = storage.NewClient(conf.Storage)

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		logger.Fatal(err)
		return
	}
	service.SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		logger.Fatal(err)
		return
	}

	service.AfterInit()
}
