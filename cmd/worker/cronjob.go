package worker

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/disintegration/imaging"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func startCronJob(ctx context.Context) {
	t := time.NewTicker(30 * time.Second)
	defer t.Stop()
	for {
		select {
		case <-t.C:
			processRecommendedTopCoverGray()
		case <-ctx.Done():
			return
		}
	}
}

func processRecommendedTopCoverGray() {
	enable, err := service.Redis.HGet(keys.KeyRecommendedTopCoverGray0.Format(), "enable").Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return
		}
		logger.Error(err)
		return
	}
	if enable == "" {
		return
	}

	coverSets, err := service.Redis.HGetAll(keys.KeyRecommendedTopCoverGray0.Format()).Result()
	if err != nil {
		logger.Error(err)
		return
	}

	setGrayCovers := make([]interface{}, 0)
	for cover, grayVal := range coverSets {
		if grayVal == "-" {
			grayCover, err := handleCoverGray(cover)
			if err != nil {
				logger.Errorf("gray cover %q error: %v", cover, err)
				continue
			}
			logger.Infof("handle cover %q set gray %q", cover, grayCover)
			setGrayCovers = append(setGrayCovers, cover, grayCover)
		}
	}

	if len(setGrayCovers) > 0 {
		_, err = service.Redis.HMSet(keys.KeyRecommendedTopCoverGray0.Format(), setGrayCovers...).Result()
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

func handleCoverGray(cover string) (string, error) {
	u, err := url.Parse(cover)
	if err != nil {
		return "", err
	}
	bucket, err := service.Storage.Get(u.Scheme)
	if err != nil {
		return "", err
	}
	objectKey := u.Host + u.Path
	lastDotPos := strings.LastIndex(objectKey, ".")
	if lastDotPos == -1 {
		return "", fmt.Errorf("could not found ext in cover filename")
	}
	format, err := imaging.FormatFromFilename(objectKey)
	if err != nil {
		return "", fmt.Errorf("imaging.FormatFromFilename error: %v", err)
	}
	reader, err := bucket.GetObject(objectKey)
	if err != nil {
		return "", fmt.Errorf("bucket.GetObject error: %v", err)
	}
	imageData, err := io.ReadAll(reader)
	reader.Close()
	if err != nil {
		return "", fmt.Errorf("io.ReadAll error: %v", err)
	}
	img, err := imaging.Decode(bytes.NewReader(imageData), imaging.AutoOrientation(true))
	if err != nil {
		return "", fmt.Errorf("imaging.Decode error: %v", err)
	}
	grayImg := imaging.Grayscale(img)
	w := new(bytes.Buffer)
	err = imaging.Encode(w, grayImg, format)
	if err != nil {
		return "", fmt.Errorf("imaging.Encode error: %v", err)
	}
	grayImageObjectKey := objectKey[:lastDotPos] + "_gray" + objectKey[lastDotPos:]
	err = bucket.PutObject(grayImageObjectKey, w)
	if err != nil {
		return "", fmt.Errorf("bucket.PutObject error: %v", err)
	}
	return u.Scheme + "://" + grayImageObjectKey, nil
}
