package worker

import (
	"context"
	"math/rand"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/x/bsonx"

	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func tryRemoveStaledPlaybackTasks(playbacksKey string, force bool) {
	if force || rand.Intn(100) < 2 { // 2%
		// Remove staled playback tasks
		t := goutil.TimeNow().AddDate(0, 0, -1).Unix()
		maxScore := strconv.FormatInt(t, 10)
		err := service.Redis.ZRemRangeByScore(playbacksKey, "-inf", maxScore).Err()
		if err != nil {
			logger.Errorf("redis remove staled playback tasks error: %v", err)
			// PASS
		}
	}
}

func findPlaybacks(collection *mongo.Collection) (empty bool, err error) {
	empty = true
	var pbs []models.Playback
	// lock for 10s
	lockKey := keys.KeyLockWorkerFetchPlayback0.Format()
	ok, err := service.Redis.SetNX(lockKey, "1", time.Second*10).Result()
	if err != nil {
		logger.Errorf("redis lock error: %v", err)
		return empty, err
	}
	if !ok {
		// wait for next fetch
		return true, nil
	}
	defer func() {
		_, err = service.Redis.Del(lockKey).Result()
		if err != nil {
			logger.Warnf("redis unlock error: %v", err)
		}
	}()
	now := goutil.TimeNow()
	filter := bson.M{
		"archive": models.ArchiveReadyDownload,
		// 收到直播录制回调后，记录时会将 archive_time 设置为当前时间加 5min，
		// 来保证如果有连续的直播场次或新的录制需要合并时，当前回放的处理任务不会被立即启动
		"archive_time": bson.M{"$lt": now, "$gte": now.AddDate(0, 0, -14)},
		"$or": bson.A{
			bson.M{"url": bson.M{"$ne": nil}},
			bson.M{"urls": bson.M{"$ne": nil}},
		},
	}
	playbacksKey := keys.KeyWorkerPlayback0.Format()
	playbackIDs, err := service.Redis.ZRange(playbacksKey, 0, -1).Result()
	if err != nil {
		logger.Errorf("redis get playbacks error: %v", err)
		return false, err
	}
	if len(playbackIDs) > 0 {
		logger.WithField("playback_ids", playbackIDs).Debugf("redis received processing playbacks")
		tryRemoveStaledPlaybackTasks(playbacksKey, false)
	}
	errorPlaybacksKey := buildErrorPlaybacksKey()
	errorPlaybackIDs, err := service.Redis.ZRangeByScore(errorPlaybacksKey, &redis.ZRangeBy{Min: "5", Max: "+inf"}).Result()
	if err != nil {
		logger.Errorf("redis get error playbacks error: %v", err)
		return false, err
	}
	if len(errorPlaybackIDs) > 0 {
		logger.WithField("playback_ids", playbackIDs).Debugf("redis received error playbacks")
	}
	playbackIDs = append(playbackIDs, errorPlaybackIDs...)
	if len(playbackIDs) > 0 {
		ids := make([]bsonx.Val, 0, len(playbackIDs))
		for _, pbID := range playbackIDs {
			objID, err := primitive.ObjectIDFromHex(pbID)
			if err != nil {
				logger.Errorf("redis playback id invalid: %v", err)
				continue
			}
			ids = append(ids, bsonx.ObjectID(objID))
		}
		filter["_id"] = bson.M{"$nin": ids}
	}

	// TODO: find multiple docs
	opts := options.Find().
		SetSort(bson.D{{Key: "priority", Value: -1}, {Key: "archive_time", Value: 1}}).
		SetLimit(4)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := collection.Find(ctx, filter, opts)
	if err != nil {
		logger.Errorf("find error: %v", err)
		return empty, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var pb models.Playback
		err = cur.Decode(&pb)
		if err != nil {
			logger.Errorf("doc decode error: %v", err)
			continue
		}
		pbs = append(pbs, pb)
	}
	if err = cur.Err(); err != nil {
		logger.Errorf("find cursor error: %v", err)
		return empty, err
	}

	var wg *sync.WaitGroup

	logger.Infof("Got %d playbacks", len(pbs))
	if len(pbs) > 0 {
		t := goutil.TimeNow().Unix()

		// ensure in the processing list
		members := make([]*redis.Z, 0, len(pbs))
		for _, pb := range pbs {
			members = append(members, &redis.Z{Score: float64(t), Member: pb.OID.Hex()})
		}
		_, err = service.Redis.ZAdd(playbacksKey, members...).Result()
		if err != nil {
			memberKeys := make([]interface{}, 0, len(pbs))
			for _, member := range members {
				memberKeys = append(memberKeys, member.Member)
			}
			logger.WithField("playback_ids", memberKeys).Errorf("redis add playbacks error: %v", err)
			// PASS
		}

		for _, pb := range pbs {
			// TODO: 把归档前的文件都放到 URLs 中
			if !((pb.URL != nil && *pb.URL != "") || len(pb.URLs) > 0) {
				logger.WithFields(logger.Fields{
					"playback_id": pb.OID,
					"room_id":     pb.RoomID,
					"user_id":     pb.UserID,
				}).Warn("empty playback url")
				continue
			}
			if wg == nil {
				wg = &sync.WaitGroup{}
				empty = false
			}
			n := NewQueueItem(pb, wg)
			atomic.AddInt64(&metrics.Queued, 1)
			Queue <- n
		}
	}

	if wg != nil {
		go func() {
			wg.Wait()

			// remove from the processing list
			memberKeys := make([]interface{}, 0, len(pbs))
			for _, pb := range pbs {
				memberKeys = append(memberKeys, pb.OID.Hex())
			}
			_, err = service.Redis.ZRem(playbacksKey, memberKeys...).Result()
			if err != nil {
				logger.WithField("playback_ids", memberKeys).Errorf("redis remove playbacks error: %v", err)
				// PASS
			}
		}()
	}

	return empty, nil
}

func subscriber(ctx context.Context) {
	// TODO: get duration from time
	duration, _ := time.ParseDuration("30s")
	collection := service.MongoDB.Collection("playbacks")
	emptyCounter := 1
	for {
		select {
		case <-time.After(time.Duration(emptyCounter) * duration):
		case <-ctx.Done():
			return
		}

		empty, _ := findPlaybacks(collection)
		if empty {
			if emptyCounter < 32 {
				// max 32 (16 minutes)
				emptyCounter *= 2
			}
		} else {
			emptyCounter = 1
		}
	}
}
