package worker

import (
	"sync"

	"github.com/MiaoSiLa/live-service/models"
)

// QueueItem is the item pass to worker
type QueueItem struct {
	Playback models.Playback
	wg       *sync.WaitGroup
}

// NewQueueItem creates new queue item
func NewQueueItem(pb models.Playback, wg *sync.WaitGroup) QueueItem {
	wg.Add(1)
	return QueueItem{
		Playback: pb,
		wg:       wg,
	}
}

// AddWaitCount increments the QueueItem's wg counter.
func (n QueueItem) AddWaitCount() {
	if n.wg != nil {
		n.wg.Add(1)
	}
}

// Done decrements the QueueItem's wg counter by one.
func (n QueueItem) Done() {
	n.wg.Add(-1)
}

// Wait blocks until the QueueItem's wg counter is zero.
func (n QueueItem) Wait() {
	n.wg.Wait()
}
