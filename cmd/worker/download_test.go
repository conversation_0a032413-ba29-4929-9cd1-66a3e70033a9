package worker

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models"
)

func TestDownloadPlayback(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	objID, _ := primitive.ObjectIDFromHex("5c8b61eb7afcbf74235a35e0")
	channelID := "live-test"
	// from oss
	url := "archive://record/live-dev/10659544/2020-12-04-00-47-01_2020-12-04-00-47-19.flv"
	df, err := downloadPlayback(&models.Playback{
		OID:       objID,
		ChannelID: &channelID,
		URL:       &url,
	})
	df.cleanup = nil   // avoid delete from oss
	defer df.Cleanup() //nolint:errcheck
	require.NoError(err)
	assert.FileExists(df.FilePath())

	df, err = downloadPlayback(&models.Playback{
		OID:       objID,
		ChannelID: &channelID,
		URLs: []models.PlaybackURL{
			{URL: "archive://record/live-dev/10659544/2020-12-04-00-47-01_2020-12-04-00-47-19.flv"},
		},
	})
	df.cleanup = nil
	defer df.Cleanup() //nolint:errcheck
	require.NoError(err)
	assert.FileExists(df.FilePath())
	assert.NotZero(df.Size)
}

func TestDownloadPlaybackBVCURLs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	objID, _ := primitive.ObjectIDFromHex("5c8b61eb7afcbf74235a35e0")
	channelID := "bvc-test"

	// 测试鉴权地址
	vid := "0"
	pb := &models.Playback{
		OID:       objID,
		ChannelID: &channelID,
		Vid:       &vid,
	}
	pb.URLs = []models.PlaybackURL{
		{URL: "http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/liverecord/2aa38a0d8602268010726784975/f0.flv"},
	}
	_, err := downloadPlayback(pb)
	require.NotNil(err)
	assert.Contains(err.Error(), "http status 403")

	vid = "1"
	pb.Vid = &vid
	// format unequal (filter_complex)
	pb.URLs = []models.PlaybackURL{
		{URL: "http://static-test.maoercdn.com/record/liverecord_f9e652d9387702298654323708_f0.flv"}, // with video
		{URL: "archive://record/live-dev/10652247/2019-08-08-12-48-17_2019-08-08-12-49-29.flv"},
	}
	df, err := downloadPlayback(pb)
	df.cleanup = nil   // avoid delete from oss
	defer df.Cleanup() //nolint:errcheck
	require.NoError(err)
	assert.FileExists(df.FilePath())

	vid = "2"
	pb.Vid = &vid
	// format unequal (filter_complex)
	pb.URLs = []models.PlaybackURL{
		{URL: "archive://record/live-dev/10659544/2020-12-04-00-47-01_2020-12-04-00-47-19.flv"}, // mix from oss (44100Hz)
		{URL: "archive://record/live-dev/10652247/2019-08-08-12-48-17_2019-08-08-12-49-29.flv"},
	}
	df2, err := downloadPlayback(pb)
	df2.cleanup = nil   // avoid delete from oss
	defer df2.Cleanup() //nolint:errcheck
	require.NoError(err)
	assert.FileExists(df2.FilePath())

	vid = "3"
	pb.Vid = &vid
	// format equal (concat)
	pb.URLs = []models.PlaybackURL{
		{URL: "archive://record/live-dev/10652247/2019-07-05-10-51-21_2019-07-05-10-53-37.flv"}, // channels: 1
		{URL: "archive://record/live-dev/10652247/2019-07-05-16-11-33_2019-07-05-16-12-39.flv"},
	}
	df3, err := downloadPlayback(pb)
	df3.cleanup = nil   // avoid delete from oss
	defer df3.Cleanup() //nolint:errcheck
	require.NoError(err)
	assert.FileExists(df3.FilePath())
}
