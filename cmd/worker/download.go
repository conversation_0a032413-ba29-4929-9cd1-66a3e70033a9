package worker

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	ffprobe "gopkg.in/vansante/go-ffprobe.v2"

	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util/ratelimit"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	playbackDownloadFileMaxSizeLimit int64 = 5 * 1024 * 1024 * 1024 // 回放下载文件最大限制 5GB, 单位 byte
)

var errDownloadFileSizeExceed = errors.New("download file size exceed the max size limit")

var globalDownloadLimiter ratelimit.RateLimiter
var globalUploadLimiter ratelimit.RateLimiter

// DownloadFile .
type DownloadFile struct {
	Size int64

	filePath string
	format   *Format
	cleanup  func() error
}

// Format .
type Format struct {
	d *ffprobe.ProbeData
}

// Equal check the stream format equal
func (f Format) Equal(f2 *Format) bool {
	if f.d == nil || f2.d == nil {
		// 可能是格式无法正确读取，这里标记成不相等
		return false
	}
	if len(f.d.Streams) != len(f2.d.Streams) {
		return false
	}
	// REVIEW: same layout
	for i := range f.d.Streams {
		s := f.d.Streams[i]
		s2 := f2.d.Streams[i]
		if s == nil || s2 == nil || s.CodecType != s2.CodecType {
			return false
		}
	}
	a := f.d.FirstAudioStream()
	a2 := f2.d.FirstAudioStream()
	if a == nil || a2 == nil {
		return false
	}
	return a.CodecName == a2.CodecName &&
		a.Profile == a2.Profile &&
		a.Channels == a2.Channels &&
		a.ChannelLayout == a2.ChannelLayout &&
		a.SampleRate == a2.SampleRate
}

// Cleanup download file source
func (df *DownloadFile) Cleanup() (err error) {
	if df.filePath != "" {
		// clean created files
		_ = os.Remove(df.filePath)
		// PASS
		df.filePath = ""
	}
	if df.cleanup != nil {
		err = df.cleanup()
		df.cleanup = nil
	}
	df.format = nil
	return
}

// FilePath get downloaded file path
func (df *DownloadFile) FilePath() string {
	return df.filePath
}

// Format get file format
func (df *DownloadFile) Format() (*Format, error) {
	if df.filePath != "" {
		if df.format == nil {
			ctx, cancelFn := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancelFn()
			d, err := ffprobe.ProbeURL(ctx, df.FilePath())
			if err != nil {
				return nil, err
			}
			df.format = &Format{d: d}
		}
	}
	return df.format, nil
}

func createDownloadFile(channelID, urlStr string) (string, error) {
	u, err := url.Parse(urlStr)
	if err != nil {
		return "", err
	}
	folder := goutil.TimeNow().Format("200601/02")
	downloadPath := path.Join("runtime/files", folder)
	err = os.MkdirAll(downloadPath, 0755)
	if err != nil {
		return "", err
	}
	var fileName string
	if strings.HasPrefix(channelID, "bvc-") {
		// BVC (txy)'s record file name always be 'f0.flv'
		// e.g. http://1252693259.vod2.myqcloud.com/55033e4evodcq1252693259/417842775285890810664136989/f0.flv
		// We will do url string hash here
		fileName = goutil.MD5(urlStr) + filepath.Ext(u.Path)
	} else {
		fileName = filepath.Base(u.Path)
	}
	filePath := path.Join(downloadPath, fileName)
	return filePath, nil
}

func downloadPlaybackURL(logEntry *logger.Entry, channelID, vid, downloadURL string) (string, func() error, error) {
	logEntry.Debugf("start downloading playback %s", downloadURL)

	u, err := url.Parse(downloadURL)
	if err != nil {
		logEntry.WithField("url", downloadURL).Errorf("url.Parse error: %v", err)
		return "", nil, err
	}

	filePath, err := createDownloadFile(channelID, downloadURL)
	if err != nil {
		logger.Errorf("create download file error: %v", err)
		return "", nil, err
	}

	fromOSS := false
	if u.Scheme == "http" || u.Scheme == "https" {
		out, err := os.Create(filePath)
		if err != nil {
			return filePath, nil, err
		}
		defer out.Close()

		// Get the data
		downloadURL, err = service.TencentCOS.GetPresignedURL(downloadURL, 20*time.Minute)
		if err != nil {
			logEntry.Errorf("get cos presigned url error: %v", err)
			return filePath, nil, err
		}

		if globalDownloadLimiter != nil {
			// assume get response head require 500 bytes
			d := globalDownloadLimiter.Take(500)
			if d > 0 {
				time.Sleep(d)
			}
		}

		resp, err := http.Get(downloadURL)
		if err != nil {
			logEntry.Errorf("http.Get error: %v", err)
			return filePath, nil, err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			content, _ := io.ReadAll(resp.Body)
			sampleContent := string(content)
			if len(sampleContent) > 200 {
				sampleContent = sampleContent[:200] + "..."
			}
			logEntry.Errorf("http.Get status error: %d, content: %s", resp.StatusCode, sampleContent)
			return filePath, nil, fmt.Errorf("http status %d", resp.StatusCode)
		}

		// Write the body to file
		_, err = io.Copy(out, ratelimit.NewReader(resp.Body, globalDownloadLimiter))
		if err != nil {
			logEntry.Errorf("io.Copy error: %v", err)
			return filePath, nil, err
		}
	} else {
		fromOSS = true
		bucket, err := service.Storage.Get(u.Scheme)

		if err != nil {
			logEntry.Errorf("OSS.Get %s error: %v", u.Scheme, err)
			return filePath, nil, err
		}
		err = bucket.GetObjectToFile(u.Host+u.Path, filePath)
		if err != nil {
			logEntry.Errorf("bucket.GetObjectToFile error: %v", err)
			return filePath, nil, err
		}
	}

	cleanup := func() error {
		var err error
		if fromOSS {
			// Aliyun OSS
			u, err2 := url.Parse(downloadURL)
			if err2 != nil {
				err = err2
			} else if u.Scheme != "http" && u.Scheme != "https" {
				// Aliyun Record
				bucket, err2 := service.Storage.Get(u.Scheme)
				if err2 != nil {
					err = err2
				} else {
					err = bucket.DeleteObject(u.Host + u.Path)
				}
			} else {
				err = fmt.Errorf("unexpected scheme: %s", u.Scheme)
			}
		} else if strings.HasPrefix(channelID, "live-") || strings.HasPrefix(channelID, "bvc-") {
			// Ksyun (ks3) or BVC (txy)
			// PASS
		} else if vid != "" {
			// Netease
			vid, err2 := strconv.ParseInt(vid, 10, 64)
			if err2 != nil {
				err = err2
			} else {
				err = service.Vod.DeleteVideo(vid)
			}
			if err != nil {
				logEntry.WithField("vid", vid).Warnf("delete video error: %v", err)
				// PASS
			} else {
				logEntry.WithField("vid", vid).Info("video deleted")
			}
		}
		return err
	}

	logEntry.Infof("playback is downloaded at %s", strconv.Quote(filePath))

	return filePath, cleanup, nil
}

func downloadPlayback(pb *models.Playback) (DownloadFile, error) {
	logEntry := logger.WithFields(logger.Fields{
		"playback_id": pb.OID,
	})

	vid := ""
	if pb.Vid != nil {
		vid = *pb.Vid
	}

	if pb.URLs != nil {
		// download & merge playback
		var files []*DownloadFile
		cleanup := func() error {
			for _, f := range files {
				_ = f.Cleanup()
			}
			return nil
		}

		formatEqual := true
		var size int64
		for i, u := range pb.URLs {
			filePath, cleanup2, err := downloadPlaybackURL(logEntry, *pb.ChannelID, vid, u.URL)
			df := &DownloadFile{filePath: filePath, cleanup: cleanup2}
			files = append(files, df)
			if err != nil {
				return DownloadFile{cleanup: cleanup}, err
			}
			f, err := df.Format()
			if err != nil {
				return DownloadFile{cleanup: cleanup}, err
			}
			fi, err := os.Stat(df.FilePath())
			if err != nil {
				logger.Errorf("download acquire size error: %v", err)
				return DownloadFile{cleanup: cleanup}, err
			}
			size += fi.Size()
			if size >= playbackDownloadFileMaxSizeLimit {
				return DownloadFile{cleanup: cleanup}, errDownloadFileSizeExceed
			}
			if i > 0 && formatEqual {
				f0, err := files[i-1].Format()
				if err != nil {
					// check last time, it must no error
					logger.Fatal(err)
				}
				formatEqual = f0.Equal(f)
			}
		}

		folder := goutil.TimeNow().Format("200601/02")
		downloadPath := path.Join("runtime/files", folder)
		err := os.MkdirAll(downloadPath, 0755)
		if err != nil {
			logger.Errorf("ensure download path error: %v", err)
			return DownloadFile{cleanup: cleanup}, err
		}
		fileName := fmt.Sprintf("%s-%s.m4a", *pb.ChannelID, vid)
		filePath := path.Join(downloadPath, fileName)

		err = mergePlaybacksAudio(files, formatEqual, filePath)

		return DownloadFile{filePath: filePath, Size: size, cleanup: cleanup}, err
	}

	filePath, cleanup, err := downloadPlaybackURL(logEntry, *pb.ChannelID, vid, *pb.URL)
	return DownloadFile{filePath: filePath, cleanup: cleanup}, err
}
