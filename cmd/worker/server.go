package worker

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"github.com/MiaoSiLa/live-service/cmd/base"
	"github.com/MiaoSiLa/live-service/cmd/worker/rpc"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/internal/controllers/health"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Metrics worker metrics
type Metrics struct {
	Queued    int64
	Processed int64
}

var metrics *Metrics

// meticsHandler /metrics [get]
// 响应格式：
/*
# HELP live_worker_playbacks worker playbacks count
# TYPE live_worker_playbacks gauge
live_worker_playbacks{status="queued"} 5
live_worker_playbacks{status="processed"} 10
*/
func meticsHandler() gin.HandlerFunc {
	workerGauges := prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "live",
			Subsystem: "worker",
			Name:      "playbacks",
			Help:      "worker playbacks count",
		},
		[]string{"status"},
	)
	service.PromRegistry.MustRegister(workerGauges)

	next := promhttp.HandlerFor(service.PromRegistry, promhttp.HandlerOpts{})

	return func(c *gin.Context) {
		workerGauges.WithLabelValues("queued").Set(float64(metrics.Queued))
		workerGauges.WithLabelValues("processed").Set(float64(metrics.Processed))
		next.ServeHTTP(c.Writer, c.Request)
	}
}

func newServer() *http.Server {
	r := base.NewEngine()
	handlers := []*handler.HandlerV2{
		health.Handler(),
		rpc.Handler(),
	}
	for _, h := range handlers {
		h.Mount(r)
	}
	r.GET("metrics", meticsHandler())

	srv := &http.Server{
		Addr: fmt.Sprintf("%s:%d", config.Conf.Worker.Address,
			config.Conf.Worker.Port),
		Handler: r}
	return srv
}
