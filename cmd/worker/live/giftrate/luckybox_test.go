package giftrate

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/livegiftratesync"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSyncLuckyBoxGiftRate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goods := []livegoods.LiveGoods{
		{
			ID:   111111,
			Type: livegoods.GoodsTypeLuckyBox,
			More: `{"lucky_box":{"gift_pool_id":111111}}`,
		},
		{
			ID:   111112,
			Type: livegoods.GoodsTypeLuckyBox,
			More: `{"lucky_box":{"gift_pool_id":111112}}`,
		},
	}
	err := service.LiveDB.Delete(livegoods.LiveGoods{}, "id IN (?)", []int64{goods[0].ID, goods[1].ID}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(service.LiveDB, livegoods.TableName(), goods)
	require.NoError(err)
	_, err = gift.CollectionDrawPool().DeleteMany(context.Background(), bson.M{"pool_id": bson.M{"$in": bson.A{111111, 111112}}})
	require.NoError(err)
	_, err = gift.CollectionDrawPool().InsertMany(context.Background(), bson.A{
		gift.PoolLuckyBox{
			PoolID: 111111,
			Type:   gift.PoolTypeLuckyBox,
			SSRID:  1,
			Rates: map[int64]int{
				1: 100,
				2: 900,
			},
		},
		gift.PoolLuckyBox{
			PoolID: 111112,
			Type:   gift.PoolTypeLuckyBox,
			SSRID:  4,
			Rates: map[int64]int{
				3: 100,
				4: 900,
			},
		},
	})
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.Record{}, "goods_id IN (?)", []int64{goods[0].ID, goods[1].ID}).Error
	require.NoError(err)
	err = livegiftratesync.DB().Delete(livegiftratesync.GiftRateSync{}, "element_type = ?", livegiftratesync.ElementTypeLuckyBox).Error
	require.NoError(err)

	syncLuckyBoxGiftRate()
	var syncs []*livegiftratesync.GiftRateSync
	err = livegiftratesync.DB().Where("element_type = ?", livegiftratesync.ElementTypeLuckyBox).Find(&syncs).Error
	require.NoError(err)
	assert.Len(syncs, 4)
	for _, sync := range syncs {
		assert.NotZero(sync.GiftID)
		switch sync.GiftID {
		case 1, 3:
			assert.Equal("0.1", sync.ExpectedRate)
		case 2, 4:
			assert.Equal("0.9", sync.ExpectedRate)
		}
		require.NotNil(sync.ActualRate)
		assert.Equal("0", *sync.ActualRate)
	}

	nowUnix := goutil.TimeNow().Unix()
	records := []*liveluckybox.Record{
		{
			CreateTime: nowUnix,
			GoodsID:    111111,
			UserID:     111111,
			GiftID:     1,
		},
		{
			CreateTime: nowUnix,
			GoodsID:    111111,
			UserID:     111111,
			GiftID:     1,
		},
		{
			CreateTime: nowUnix,
			GoodsID:    111112,
			UserID:     111112,
			GiftID:     3,
		},
		{
			CreateTime: nowUnix,
			GoodsID:    111111,
			UserID:     111112,
			GiftID:     3,
		},
	}
	err = servicedb.BatchInsert(liveluckybox.DB(), liveluckybox.Record{}.TableName(), records)
	require.NoError(err)
	syncLuckyBoxGiftRate()
	var newSyncs []*livegiftratesync.GiftRateSync
	err = livegiftratesync.DB().Where("element_type = ?", livegiftratesync.ElementTypeLuckyBox).Find(&newSyncs).Error
	require.NoError(err)
	require.Len(newSyncs, 4)
	for _, sync := range newSyncs {
		assert.NotZero(sync.GiftID, fmt.Sprintf("%+v", sync))
		require.NotNil(sync.ActualRate)
		switch sync.GiftID {
		case 1:
			assert.Equal("0.1", sync.ExpectedRate)
			assert.Equal("0.66667", *sync.ActualRate)
		case 3:
			assert.Equal("0.1", sync.ExpectedRate)
			switch sync.ElementID {
			case 111111:
				assert.Equal("0.33333", *sync.ActualRate)
			case 111112:
				assert.Equal("1", *sync.ActualRate)
			}
		case 2, 4:
			assert.Equal("0.9", sync.ExpectedRate)
			assert.Equal("0", *sync.ActualRate)
		}
	}
}
