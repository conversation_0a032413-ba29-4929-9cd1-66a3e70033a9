package giftrate

import (
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/livedb/livegiftratesync"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSyncLuckyGiftGiftRate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livegiftratesync.DB().Delete(livegiftratesync.GiftRateSync{},
		"element_type IN (?)", []int{livegiftratesync.ElementTypeLuckyGift, livegiftratesync.ElementTypeLuckyGiftReward}).Error
	require.NoError(err)

	gifts := []gift.Gift{
		{Type: gift.TypeDrawSend, GiftID: 80001},
		{Type: gift.TypeDrawSend, GiftID: 80002},
		{Type: gift.TypeDrawSend, GiftID: 80003, Exclusive: gift.GiftExclusiveRoom},
	}

	nums := []int{1, 10, 100}
	rates := map[int64]int{10001: 85, 10002: 10, 10003: 5}
	updates := make([]mongo.WriteModel, 0, len(gifts)*len(nums))
	for i, g := range gifts {
		for _, num := range nums {
			add := int64(num)
			if num == 1 {
				add = 0
			}
			ssrID := g.GiftID + add + 10001
			dp := &gift.PoolGift{
				PoolID:  int64(i+1) + add,
				GiftID:  g.GiftID,
				GiftNum: num,
				Rates:   make(map[int64]int, len(rates)),
				SSRID:   ssrID,
			}
			for id, rate := range rates {
				dp.Rates[g.GiftID+add+id] = rate
			}
			m := mongo.NewUpdateOneModel().
				SetFilter(bson.M{"gift_id": dp.GiftID, "gift_num": dp.GiftNum}).
				SetUpdate(bson.M{"$set": dp}).
				SetUpsert(true)
			updates = append(updates, m)
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = gift.CollectionDrawPool().BulkWrite(ctx, updates)
	require.NoError(err)

	key := keys.KeyOnlineGifts0.Format()
	service.Cache5s.Set(key, gifts, 0)
	defer service.Cache5s.Flush()

	syncLuckyGiftGiftRate()

	var syncs []*livegiftratesync.GiftRateSync
	err = livegiftratesync.DB().Where("element_type = ?", livegiftratesync.ElementTypeLuckyGiftReward).Find(&syncs).Error
	require.NoError(err)
	assert.Len(syncs, len(gifts)*len(nums))

	syncs = nil
	err = livegiftratesync.DB().Where("element_type = ?", livegiftratesync.ElementTypeLuckyGift).Find(&syncs).Error
	require.NoError(err)
	totalCount := len(gifts) * len(nums) * len(rates)
	assert.Len(syncs, totalCount)

	type receiveGiftRate struct {
		GiftID       int64
		ExpectedRate string
	}
	giftRates := make([]receiveGiftRate, 0, len(rates))
	for _, sync := range syncs {
		if sync.ElementID == 80001 && sync.MoreInfo.LuckyGift.LuckyGiftNum == 1 {
			giftRates = append(giftRates, receiveGiftRate{
				GiftID:       sync.GiftID,
				ExpectedRate: sync.ExpectedRate,
			})
		}
	}
	sort.Slice(giftRates, func(i, j int) bool {
		return giftRates[i].GiftID < giftRates[j].GiftID
	})
	syncExpectedRates := make([]string, 0, len(giftRates))
	for _, gr := range giftRates {
		syncExpectedRates = append(syncExpectedRates, gr.ExpectedRate)
	}
	assert.Equal([]string{"0.85", "0.1", "0.05"}, syncExpectedRates)

	// 测试随机礼物下架
	gifts = []gift.Gift{
		{Type: gift.TypeDrawSend, Name: "测试随机礼物1", GiftID: 80001},
		{Type: gift.TypeDrawSend, Name: "测试随机礼物2", GiftID: 80002},
	}
	service.Cache5s.Set(key, gifts, 0)

	testUserID := int64(17187109)
	require.NoError(transactionlog.ADB().Delete("", "from_id = ?", testUserID).Error)
	tl := transactionlog.TransactionLog{
		Title:       "draw receive 3（测试随机礼物1）",
		GiftID:      gifts[0].GiftID,
		Num:         int64(nums[0]),
		FromID:      testUserID,
		Attr:        4,
		Income:      1.3,
		AllCoin:     10,
		Status:      transactionlog.StatusSuccess,
		Type:        transactionlog.TypeLive,
		ConfirmTime: goutil.TimeNow().Unix(),
	}
	tl2 := tl
	tl2.Title = "draw receive 2（测试随机礼物1）"
	require.NoError(transactionlog.ADB().Create(&tl).Error)
	require.NoError(transactionlog.ADB().Create(&tl2).Error)

	syncLuckyGiftGiftRate()

	syncs = nil
	err = livegiftratesync.DB().Where("element_id = ? AND element_type = ?", gifts[0].GiftID, livegiftratesync.ElementTypeLuckyGiftReward).
		Where("IFNULL(JSON_EXTRACT(more, '$.lucky_gift.lucky_gift_num'), 0) = ?", nums[0]).
		Find(&syncs).Error
	require.NoError(err)
	require.NotEmpty(syncs)
	assert.Equal("1.3", *syncs[0].ActualRate)
	assert.NotZero(syncs[0].TotalNum)
	assert.NotZero(syncs[0].TotalUserNum)

	syncs = nil
	err = livegiftratesync.DB().Where("element_type = ?", livegiftratesync.ElementTypeLuckyGift).Find(&syncs).Error
	require.NoError(err)
	assert.Len(syncs, totalCount)
	hideSyncs := make([]*livegiftratesync.GiftRateSync, 0, len(nums)*len(rates))
	for _, sync := range syncs {
		if sync.ElementID == 80003 {
			assert.Equal(livegiftratesync.StatusHide, sync.Status)
			hideSyncs = append(hideSyncs, sync)
		}
		if sync.ElementID == tl.GiftID && sync.GiftID == 90003 {
			assert.Equal("0.5", *sync.ActualRate)
			assert.EqualValues(1, sync.TotalNum)
			assert.EqualValues(1, sync.TotalUserNum)
		}
	}
	assert.Len(hideSyncs, len(nums)*len(rates))
}

func TestAggregateLuckyGiftRewardData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(17187109)
	testGiftID := int64(91918)
	require.NoError(transactionlog.ADB().Delete("", "gift_id = ?", testGiftID).Error)

	aggRes, err := aggregateLuckyGiftRewardData()
	require.NoError(err)
	assert.Nil(aggRes[testGiftID])

	tl := transactionlog.TransactionLog{
		GiftID:      testGiftID,
		Num:         1,
		FromID:      testUserID,
		Attr:        4,
		Income:      1.3,
		AllCoin:     10,
		Status:      transactionlog.StatusSuccess,
		Type:        transactionlog.TypeLive,
		ConfirmTime: goutil.TimeNow().Unix(),
	}
	require.NoError(transactionlog.ADB().Create(&tl).Error)
	aggRes, err = aggregateLuckyGiftRewardData()
	require.NoError(err)
	sync, ok := aggRes[testGiftID][int(tl.Num)]
	require.True(ok)
	assert.EqualValues(13, sync.TotalIncome)
	assert.EqualValues(10, sync.TotalSpend)
	assert.EqualValues(1, sync.TotalNum)
	assert.EqualValues(1, sync.TotalUserNum)
}

func TestAggregateLuckyGiftDrawData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTitle := "测试礼物（测试随机礼物）"
	testUserID := int64(17187109)
	testGiftID := int64(91918)
	require.NoError(transactionlog.ADB().Delete("", "gift_id = ?", testGiftID).Error)

	aggRes, err := aggregateLuckyGiftDrawData()
	require.NoError(err)
	assert.Nil(aggRes[testTitle])

	tl := transactionlog.TransactionLog{
		GiftID:      testGiftID,
		Title:       testTitle,
		FromID:      testUserID,
		Attr:        4,
		Status:      transactionlog.StatusSuccess,
		Type:        transactionlog.TypeLive,
		ConfirmTime: goutil.TimeNow().Unix(),
	}
	require.NoError(transactionlog.ADB().Create(&tl).Error)
	aggRes, err = aggregateLuckyGiftDrawData()
	require.NoError(err)
	sync, ok := aggRes[testTitle]
	require.True(ok)
	assert.EqualValues(1, sync.TotalNum)
	assert.EqualValues(1, sync.TotalUserNum)
}
