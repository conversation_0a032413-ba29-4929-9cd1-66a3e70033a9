package giftrate

import (
	"sort"

	"github.com/MiaoSiLa/live-service/models/livedb/livegiftratesync"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func syncLuckyBoxGiftRate() {
	goods, err := livegoods.AllLuckyBox()
	if err != nil {
		logger.Errorf("查询宝盒失败，错误信息：%v", err)
		return
	}
	if len(goods) == 0 {
		logger.Error("未查询到任何宝盒信息")
		return
	}
	var (
		goodsIDs       = make([]int64, 0, len(goods))
		goodsMap       = make(map[int64]livegoods.Goods, len(goods))
		goodsPoolIDMap = make(map[int64]int64, len(goods))
		giftPoolIDs    = make([]int64, 0, len(goods))
	)
	for _, g := range goods {
		goodsID := g.GoodsID()
		more, err := g.UnmarshalMore()
		if err != nil {
			logger.WithFields(logger.Fields{
				"goods_id": goodsID,
			}).Errorf("unmarshal more error: %v", err)
			continue
		}
		if more == nil || more.LuckyBox == nil {
			logger.WithFields(logger.Fields{
				"goods_id": goodsID,
			}).Error("live_goods more 或 more.lucky_box 为空")
			continue
		}
		goodsIDs = append(goodsIDs, goodsID)
		goodsMap[goodsID] = g
		goodsPoolIDMap[goodsID] = more.LuckyBox.GiftPoolID
		giftPoolIDs = append(giftPoolIDs, more.LuckyBox.GiftPoolID)
	}
	pools, err := gift.ListPoolLuckyBox(giftPoolIDs)
	if err != nil {
		logger.Errorf("查询奖池数据错误：%v", err)
		return
	}
	if len(pools) == 0 {
		logger.Error("未查询到奖池数据")
		return
	}
	giftIDs := make([]int64, 0, len(pools)*15) // 奖池礼物数量普遍 <= 15 个，所以这里 *15 即可
	for _, pool := range pools {
		for giftID := range pool.Rates {
			giftIDs = append(giftIDs, giftID)
		}
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		logger.Errorf("查询礼物信息错误：%v", err)
		return
	}
	if len(giftMap) != len(giftIDs) {
		logger.WithFields(logger.Fields{
			"gift_ids": giftIDs,
			"pool_ids": giftPoolIDs,
		}).Error("未查询到所有礼物信息")
		// PASS
	}

	goodsGiftNumMap, err := liveluckybox.EachGiftNum(goodsIDs)
	if err != nil {
		logger.Errorf("获取每个礼物抽中次数错误：%v", err)
		return
	}
	// 宝盒礼物总数
	goodsGiftTotalNumMap := make(map[int64]int64, len(goodsGiftNumMap)) // map[goods_id]int64
	for goodsID, giftNumMap := range goodsGiftNumMap {
		for _, giftNum := range giftNumMap {
			goodsGiftTotalNumMap[goodsID] += giftNum.Num
		}
	}

	var (
		nowUnix    = goutil.TimeNow().Unix()
		poolMap    = goutil.ToMap(pools, "PoolID").(map[int64]*gift.PoolLuckyBox)
		afterSyncs = make([]*livegiftratesync.GiftRateSync, 0, len(goods)*15) // 奖池礼物数量普遍 <= 15 个，所以这里 *15 即可
	)
	for _, g := range goods {
		goodsID := g.GoodsID()
		giftPoolID, ok := goodsPoolIDMap[goodsID]
		if !ok {
			logger.WithFields(logger.Fields{
				"goods_id": goodsID,
			}).Error("商品奖池关联失败")
			continue
		}
		pool, ok := poolMap[giftPoolID]
		if !ok {
			logger.WithFields(logger.Fields{
				"goods_id": goodsID,
			}).Error("查询奖池详情失败")
			continue
		}
		var (
			totalRate   int
			poolGiftIDs []int64
		)
		for giftID, rate := range pool.Rates {
			totalRate += rate
			poolGiftIDs = append(poolGiftIDs, giftID)
		}
		// 按礼物 ID 升序排序，保证插入顺序一致
		sort.Slice(poolGiftIDs, func(i, j int) bool {
			return poolGiftIDs[i] < poolGiftIDs[j]
		})
		for _, giftID := range poolGiftIDs {
			gift, ok := giftMap[giftID]
			if !ok {
				logger.WithFields(logger.Fields{
					"goods_id": goodsID,
					"gift_id":  giftID,
				}).Error("gift not found")
				continue
			}
			giftNum := goodsGiftNumMap[goodsID][giftID]
			syncMore := livegiftratesync.More{
				LuckyBox: &livegiftratesync.MoreLuckyBox{
					GoodsTitle: g.Title,
					GiftName:   gift.Name,
					IsSSR:      pool.IsSSRID(giftID),
				},
			}
			actualRate := livegiftratesync.Rate(giftNum.Num, goodsGiftTotalNumMap[goodsID])
			afterSyncs = append(afterSyncs, &livegiftratesync.GiftRateSync{
				CreateTime:   nowUnix,
				ModifiedTime: nowUnix,
				ElementID:    goodsID,
				ElementType:  livegiftratesync.ElementTypeLuckyBox,
				Status:       livegiftratesync.StatusShow, // TODO: 后续需要区分商品是否下架，目前都设置为正常状态
				GiftPoolID:   giftPoolID,
				GiftID:       giftID,
				ExpectedRate: livegiftratesync.Rate(int64(pool.Rates[giftID]), int64(totalRate)),
				ActualRate:   &actualRate,
				TotalNum:     giftNum.Num,
				TotalUserNum: giftNum.UserNum,
				More:         syncMore.Marshal(),
				MoreInfo:     &syncMore,
			})
		}
	}

	// 旧的同步信息
	beforeSyncs, err := livegiftratesync.ListLuckyBox()
	if err != nil {
		logger.Errorf("查询宝盒礼物概率同步数据出错：%v", err)
		return
	}
	// 新增或插入同步的礼物概率相关数据
	err = livegiftratesync.Upsert(beforeSyncs, afterSyncs)
	if err != nil {
		logger.Errorf("upsert gift rate sync error: %v", err)
		// PASS
	}
}
