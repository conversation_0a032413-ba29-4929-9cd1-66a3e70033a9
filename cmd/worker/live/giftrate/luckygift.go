package giftrate

import (
	"sort"

	"github.com/MiaoSiLa/live-service/models/livedb/livegiftratesync"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type luckyGiftWeight struct {
	LuckyGiftID   int64
	LuckyGiftName string
	LuckyGiftNum  int
	GiftID        int64
	GiftName      string
	TotalNum      int64
	TotalUserNum  int64
	Weight        int
	PriceWeight   int64
}

func syncLuckyGiftGiftRate() {
	allGifts, err := gift.FindAllShowingGifts()
	if err != nil {
		logger.Errorf("查询礼物列表失败，错误信息：%v", err)
		return
	}
	luckyGiftIDs := make([]int64, 0, len(allGifts)/4)
	luckyGifts := make([]gift.Gift, 0, len(allGifts)/4)
	for _, lg := range allGifts {
		if lg.Type == gift.TypeDrawSend {
			luckyGiftIDs = append(luckyGiftIDs, lg.GiftID)
			luckyGifts = append(luckyGifts, lg)
		}
	}
	if len(luckyGiftIDs) == 0 {
		logger.Error("未查询到任何随机礼物信息")
		return
	}
	pools, err := gift.ListPoolGift(luckyGiftIDs)
	if err != nil {
		logger.Errorf("查询奖池数据错误：%v", err)
		return
	}
	if len(pools) == 0 {
		logger.Error("未查询到奖池数据")
		return
	}
	receiveGiftIDs := make([]int64, 0, len(pools)*10) // 奖池礼物数量普遍 <= 10 个，所以这里 *10 即可
	for _, pool := range pools {
		for giftID := range pool.Rates {
			receiveGiftIDs = append(receiveGiftIDs, giftID)
		}
	}
	receiveGiftMap, err := gift.FindGiftMapByGiftIDs(receiveGiftIDs)
	if err != nil {
		logger.Errorf("查询礼物信息错误：%v", err)
		return
	}

	luckyGiftRewardAggregateMap, err := aggregateLuckyGiftRewardData()
	if err != nil {
		logger.Errorf("查询随机礼物消费信息错误：%v", err)
		return
	}

	luckyGiftDrawAggregateMap, err := aggregateLuckyGiftDrawData()
	if err != nil {
		logger.Errorf("查询随机礼物消费信息错误：%v", err)
		return
	}

	var (
		nowUnix    = goutil.TimeNow().Unix()
		afterSyncs = make([]*livegiftratesync.GiftRateSync, 0, len(receiveGiftIDs))
	)
	for _, lg := range luckyGifts {
		giftPools := make([]*gift.PoolGift, 0, 10) // 随机礼物档位普遍小于 10 个，这里用 10 即可
		for _, pool := range pools {
			if pool.GiftID == lg.GiftID {
				giftPools = append(giftPools, pool)
			}
		}
		if len(giftPools) == 0 {
			logger.WithFields(logger.Fields{
				"lucky_gift_id": lg.GiftID,
			}).Error("随机礼物奖池关联失败")
			continue
		}
		// 按随机礼物档位排序
		sort.Slice(giftPools, func(i, j int) bool {
			return giftPools[i].GiftNum < giftPools[j].GiftNum
		})
		for _, pool := range giftPools {
			var drawTotalNum int64
			var totalPriceWeight int64
			var totalWeight int64
			luckyGiftPrice := lg.Price * int64(pool.GiftNum)
			weights := make([]luckyGiftWeight, 0, len(pool.Rates))
			// 此处不考虑保底带来的概率影响
			for giftID, weight := range pool.Rates {
				w := luckyGiftWeight{
					LuckyGiftID:   lg.GiftID,
					LuckyGiftName: lg.Name,
					LuckyGiftNum:  pool.GiftNum,
					GiftID:        giftID,
					Weight:        weight,
				}
				totalWeight += int64(weight)
				g, ok := receiveGiftMap[giftID]
				if ok {
					w.GiftName = g.Name
					w.PriceWeight = int64(weight) * g.Price
					totalPriceWeight += w.PriceWeight
					title := transactionlog.LuckyGiftTitle(w.GiftName, lg.Name)
					if data, ok := luckyGiftDrawAggregateMap[title]; ok {
						w.TotalNum = data.TotalNum
						w.TotalUserNum = data.TotalUserNum
						drawTotalNum += data.TotalNum
					}
				} else {
					logger.WithFields(logger.Fields{
						"lucky_gift_id": lg.GiftID,
						"gift_id":       giftID,
					}).Error("随机礼物奖池礼物不存在")
					// PASS
				}
				weights = append(weights, w)
			}
			sort.Slice(weights, func(i, j int) bool {
				if weights[i].Weight == weights[j].Weight {
					return weights[i].GiftID < weights[j].GiftID
				}
				return weights[i].Weight < weights[j].Weight
			})
			var guessedSSRID int64
			if pool.SSRID == 0 {
				guessedSSRID = pool.GuessSSRID() // 部分随机礼物没有直接配置大奖，需要根据概率来推断一个
			}
			for _, w := range weights {
				syncMore := livegiftratesync.More{
					LuckyGift: &livegiftratesync.MoreLuckyGift{
						LuckyGiftName: w.LuckyGiftName,
						LuckyGiftNum:  w.LuckyGiftNum,
						GiftName:      w.GiftName,
					},
				}
				if pool.SSRID == 0 {
					syncMore.LuckyGift.IsSSR = w.GiftID == guessedSSRID
				} else {
					syncMore.LuckyGift.IsSSR = pool.IsSSR(w.GiftID)
				}
				actualRate := livegiftratesync.Rate(w.TotalNum, drawTotalNum)
				afterSyncs = append(afterSyncs, &livegiftratesync.GiftRateSync{
					CreateTime:   nowUnix,
					ModifiedTime: nowUnix,
					ElementType:  livegiftratesync.ElementTypeLuckyGift,
					ElementID:    w.LuckyGiftID,
					Status:       livegiftratesync.StatusShow,
					GiftPoolID:   pool.PoolID,
					GiftID:       w.GiftID,
					ExpectedRate: livegiftratesync.Rate(int64(w.Weight), totalWeight),
					ActualRate:   &actualRate,
					TotalNum:     w.TotalNum,
					TotalUserNum: w.TotalUserNum,
					More:         syncMore.Marshal(),
					MoreInfo:     &syncMore,
				})
			}
			// 随机礼物回报率
			syncMore := livegiftratesync.More{
				LuckyGift: &livegiftratesync.MoreLuckyGift{
					LuckyGiftName: lg.Name,
					LuckyGiftNum:  pool.GiftNum,
				},
			}
			var (
				actualRate   = livegiftratesync.Rate(0, 0) // 设置默认值
				totalNum     int64
				totalUserNum int64
			)
			if aMap, ok := luckyGiftRewardAggregateMap[lg.GiftID]; ok {
				if data, ok := aMap[pool.GiftNum]; ok {
					actualRate = livegiftratesync.Rate(data.TotalIncome, data.TotalSpend)
					totalNum = data.TotalNum
					totalUserNum = data.TotalUserNum
				}
			}
			afterSyncs = append(afterSyncs, &livegiftratesync.GiftRateSync{
				CreateTime:   nowUnix,
				ModifiedTime: nowUnix,
				ElementType:  livegiftratesync.ElementTypeLuckyGiftReward,
				ElementID:    lg.GiftID,
				Status:       livegiftratesync.StatusShow,
				GiftPoolID:   pool.PoolID,
				// totalReceiveGiftPriceWithWeight / totalReceiveGiftWeight / giftPrice
				ExpectedRate: livegiftratesync.Rate(totalPriceWeight, totalWeight*luckyGiftPrice),
				ActualRate:   &actualRate,
				TotalNum:     totalNum,
				TotalUserNum: totalUserNum,
				More:         syncMore.Marshal(),
				MoreInfo:     &syncMore,
			})
		}
	}

	// 旧的同步信息
	beforeSyncs, err := livegiftratesync.ListLuckyGift()
	if err != nil {
		logger.Errorf("查询随机礼物概率同步数据出错：%v", err)
		return
	}
	// 新增或插入同步的礼物概率相关数据
	err = livegiftratesync.Upsert(beforeSyncs, afterSyncs)
	if err != nil {
		logger.Errorf("upsert gift rate sync error: %v", err)
		// PASS
	}
}

type aggregateData struct {
	Title        string `gorm:"column:title"` // 消费数据中缺少奖池礼物的相关内容，只能通过 title 来区分，仅查询单个随机礼物掉率的情况需要
	GiftID       int64  `gorm:"column:gift_id"`
	LuckGiftNum  int    `gorm:"column:lucky_gift_num"`
	TotalIncome  int64  `gorm:"column:total_income"` // 单位：钻石
	TotalSpend   int64  `gorm:"column:total_spend"`  // 单位：钻石
	TotalNum     int64  `gorm:"column:total_num"`
	TotalUserNum int64  `gorm:"column:total_user_num"`
}

func aggregateLuckyGiftRewardData() (map[int64]map[int]*aggregateData, error) {
	var aggregateResult []aggregateData
	err := transactionlog.ADB().
		Select("gift_id, num AS lucky_gift_num, "+
			"CAST(SUM(ROUND(income * 10)) AS SIGNED) AS total_income, SUM(all_coin) AS total_spend, "+
			"COUNT(*) AS total_num, COUNT(DISTINCT from_id) AS total_user_num").
		Where(transactionlog.LuckyGiftCondSQL()).
		Where("confirm_time >= ?", goutil.BeginningOfDay(goutil.TimeNow()).Unix()). // 获取当前自然日的数据
		Group("gift_id, num").Scan(&aggregateResult).Error
	if err != nil {
		logger.Errorf("查询随机礼物消费数据出错：%v", err)
		return nil, err
	}
	aggregateDataMap := make(map[int64]map[int]*aggregateData, len(aggregateResult))
	if len(aggregateResult) == 0 {
		return aggregateDataMap, nil
	}
	for i := range aggregateResult {
		res := aggregateResult[i]
		if res.GiftID == 0 || res.LuckGiftNum == 0 {
			continue
		}
		if _, ok := aggregateDataMap[res.GiftID]; !ok {
			aggregateDataMap[res.GiftID] = make(map[int]*aggregateData)
		}
		aggregateDataMap[res.GiftID][res.LuckGiftNum] = &res
	}
	return aggregateDataMap, nil
}

func aggregateLuckyGiftDrawData() (map[string]*aggregateData, error) {
	var aggregateResult []aggregateData
	err := transactionlog.ADB().
		Select("title, "+ // title 格式：奖池礼物名称（随机礼物名称），奖池礼物目前没有字段可以直接区分，先用标题区分
			"COUNT(*) AS total_num, COUNT(DISTINCT from_id) AS total_user_num").
		Where(transactionlog.LuckyGiftCondSQL()).
		Where("confirm_time >= ?", goutil.TimeNow().AddDate(0, 0, -30).Unix()). // 获取近 30 日的数据
		Group("title").Scan(&aggregateResult).Error
	if err != nil {
		logger.Errorf("查询随机礼物消费数据出错：%v", err)
		return nil, err
	}
	aggregateDataMap := make(map[string]*aggregateData, len(aggregateResult))
	if len(aggregateResult) == 0 {
		return aggregateDataMap, nil
	}
	for i := range aggregateResult {
		res := aggregateResult[i]
		if res.Title == "" {
			continue
		}
		aggregateDataMap[res.Title] = &res
	}
	return aggregateDataMap, nil
}
