package giftrate

import (
	"context"
	"errors"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// Cron 礼物同步定时任务
func Cron(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			if err := ctx.Err(); err != nil && !errors.Is(err, context.Canceled) {
				logger.Errorf("context error: %v", err)
			}
			return
		case <-ticker.C:
			syncLuckyGiftGiftRate()
			syncLuckyBoxGiftRate()
		}
	}
}
