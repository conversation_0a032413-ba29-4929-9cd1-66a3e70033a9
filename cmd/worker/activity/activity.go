package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/livedb/liveactivityjob"
	"github.com/MiaoSiLa/live-service/models/mongodb/activityparam"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type job struct {
	EventID       int64           `json:"event_id"`
	URL           string          `json:"url"`
	Param         json.RawMessage `json:"param"`
	RunTime       int64           `json:"run_time"`        // 数据库中保存的执行时间, 单位秒
	ActualRunTime int64           `json:"actual_run_time"` // 实际执行时间, 单位秒
	Description   string          `json:"description"`
}

// Cron .
func Cron(ctx context.Context) {
	const duration = 5 * time.Second

	lastRunTime := goutil.TimeNow()
	ticker := time.NewTicker(duration)
	for {
		select {
		case <-ticker.C:
			var (
				startTime = lastRunTime
				endTime   = goutil.TimeNow().Add(duration)
			)
			jobs, err := loadActivityJobsWithCache(startTime, endTime)
			if err != nil {
				logger.Error(err)
				continue
			}

			lastRunTime = endTime
			for _, j := range jobs {
				// TODO: 要支持 dispatch 到 worker 里
				goutil.GoArg1(func(j job) {
					fields := logger.Fields{
						"event_id":        j.EventID,
						"url":             j.URL,
						"param":           string(j.Param),
						"run_time":        time.Unix(j.RunTime, 0).Format(goutil.TimeFormatHMS),
						"actual_run_time": time.Unix(j.ActualRunTime, 0).Format(goutil.TimeFormatHMS),
					}

					now := goutil.TimeNow()
					if j.ActualRunTime > now.Unix() {
						time.Sleep(time.Unix(j.ActualRunTime, 0).Sub(now))
					}

					// TODO: 记录数据库日志
					logger.WithFields(fields).Infof("run job: %s", j.Description)
					err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), j.URL, j.Param, nil)
					if err != nil {
						logger.WithFields(fields).Error(err)
						// PASS
					}
				}, j)
			}
		case <-ctx.Done():
			return
		}
	}
}

func loadActivityJobsWithCache(startTime, endTime time.Time) ([]job, error) {
	key := keys.LocalKeyActivityCronJobs0.Format()
	var jobs []job
	res, ok := service.Cache5Min.Get(key)
	if ok {
		if jobs, ok = res.([]job); ok {
			return filterJobsTime(jobs, startTime, endTime), nil
		}
	}

	var err error
	for retry := 0; retry < 3; retry++ {
		jobs, err = loadActivityJobs(startTime)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			break
		}
	}
	// 退出重试后 err 仍不为 nil，说明查询失败
	if err != nil {
		return nil, err
	}

	// 更新待执行的定时任务
	resetActivityJobsList(jobs)

	service.Cache5Min.SetDefault(key, jobs)
	return filterJobsTime(jobs, startTime, endTime), nil
}

func loadActivityJobs(now time.Time) ([]job, error) {
	// 查询所有未结束的活动定时任务
	// 活动偏移只考虑向后偏移的情况，所以只需要查结束时间大于当前时间的情况
	params, err := activityparam.OngoingJobsParams(now)
	if err != nil {
		return nil, err
	}

	js, err := liveactivityjob.FindByTime(now)
	if err != nil {
		return nil, err
	}
	if len(params) == 0 && len(js) == 0 {
		return nil, nil
	}

	eventIDs := make([]int64, 0, len(params)+len(js))
	for _, param := range params {
		eventIDs = append(eventIDs, param.EventID)
	}
	for _, j := range js {
		eventIDs = append(eventIDs, j.EventID)
	}

	eventIDs = sets.Uniq(eventIDs)
	var events []mevent.Simple
	err = service.DB.Table(mevent.TableName()).Select("id, extended_fields").Find(&events, "id IN (?)", eventIDs).Error
	if err != nil {
		return nil, err
	}

	em := make(map[int64]*mevent.ExtendedFields, len(events))
	for _, event := range events {
		var e mevent.ExtendedFields
		if event.ExtendedFields != "" {
			err = json.Unmarshal([]byte(event.ExtendedFields), &e)
			if err != nil {
				logger.WithFields(logger.Fields{"event_id": event.ID}).Error(err)
				// PASS
			}
		}
		em[event.ID] = &e
	}

	jobs := make([]job, 0, len(params)+len(js))
	for _, param := range params {
		e, ok := em[param.EventID]
		// 如果 event 不存在，则不添加到 jobs 中
		if !ok {
			logger.WithFields(logger.Fields{"event_id": param.EventID}).Error("event not found")
			continue
		}
		for _, j := range param.Jobs {
			rawParam := json.RawMessage(j.Param)
			if !json.Valid(rawParam) {
				logger.WithFields(logger.Fields{
					"event_id":    param.EventID,
					"url":         j.URL,
					"param":       j.Param,
					"run_time":    time.Unix(j.RunTime, 0).Format(goutil.TimeFormatHMS),
					"description": j.Description,
				}).Error("invalid param")
				continue
			}

			actualRunTime := j.RunTime - e.TimeOffset
			jobs = append(jobs, job{
				EventID:       param.EventID,
				URL:           j.URL,
				Param:         rawParam,
				RunTime:       j.RunTime,
				ActualRunTime: actualRunTime,
				Description:   j.Description,
			})
		}
	}

	for _, j := range js {
		e, ok := em[j.EventID]
		// 如果 event 不存在，则不添加到 jobs 中
		if !ok {
			logger.WithFields(logger.Fields{
				"event_id": j.EventID,
				"job_id":   j.ID,
			}).Error("event not found")
			continue
		}

		actualRunTime := j.RunTime - e.TimeOffset
		rawParam := json.RawMessage(fmt.Sprintf(`{"job_id":%d}`, j.ID))
		jobs = append(jobs, job{
			EventID:       j.EventID,
			URL:           userapi.URIRunJob,
			Param:         rawParam,
			RunTime:       j.RunTime,
			ActualRunTime: actualRunTime,
			Description:   j.Intro,
		})
	}

	return jobs, nil
}

func resetActivityJobsList(jobs []job) {
	key := keys.KeyActivityCronJobList0.Format()
	if len(jobs) == 0 {
		err := service.Redis.Del(key).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return
	}

	zs := make([]*redis.Z, 0, len(jobs))
	for _, j := range jobs {
		bs, err := json.Marshal(j)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		zs = append(zs, &redis.Z{
			Score:  float64(j.ActualRunTime),
			Member: string(bs),
		})
	}

	pipe := service.Redis.TxPipeline()
	pipe.Del(key)
	pipe.ZAdd(key, zs...)
	pipe.Expire(key, 10*time.Minute)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func filterJobsTime(jobs []job, startTime, endTime time.Time) []job {
	// 预估 5s 间隔内要执行的任务数量不会超过 5 个
	filteredJobs := make([]job, 0, 5)
	for _, j := range jobs {
		if j.ActualRunTime >= startTime.Unix() && j.ActualRunTime < endTime.Unix() {
			filteredJobs = append(filteredJobs, j)
		}
	}
	return filteredJobs
}
