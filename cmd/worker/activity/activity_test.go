package activity

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/liveactivityjob"
	"github.com/MiaoSiLa/live-service/models/mongodb/activityparam"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestCron(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := activityparam.Collection().DeleteMany(ctx, bson.M{
		"event_id": 160,
	})
	require.NoError(err)

	now := goutil.TimeNow()
	jobs := make([]activityparam.Job, 0, 30)
	for i := 0; i < 15; i++ {
		jobs = append(jobs, activityparam.Job{
			URL:     "test://test",
			Param:   "{}",
			RunTime: now.Add(time.Duration(5+i) * time.Second).Unix(),
		})
	}

	_, err = activityparam.Collection().InsertOne(ctx, &activityparam.ActivityParam{
		EventID: 160,
		Jobs:    jobs,
	})
	require.NoError(err)

	ctx, cancel = context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var count int
	cancel = mrpc.SetMock("test://test", func(input any) (output any, err error) {
		count++
		return
	})
	defer cancel()

	Cron(ctx)

	<-ctx.Done()
	assert.EqualValues(15, count)
}

func Test_loadActivityJobsWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := activityparam.Collection().DeleteMany(ctx, bson.M{
		"event_id": bson.M{"$in": []int64{110, 124, 134, 138, 160}},
	})
	require.NoError(err)

	_, err = activityparam.Collection().InsertMany(ctx, []any{
		&activityparam.ActivityParam{
			EventID: 110,
			Jobs: []activityparam.Job{
				{
					RunTime: 1,
					Param:   "{}",
				},
			},
		},
		&activityparam.ActivityParam{
			EventID: 124,
			Jobs: []activityparam.Job{
				{URL: "test0", RunTime: 1, Param: "{}"},
				{URL: "test1", RunTime: 2, Param: `{"a":"b"}`},
				{URL: "test2", RunTime: 2, Param: "{}"},
			},
		},
		&activityparam.ActivityParam{
			EventID: 134,
			Jobs: []activityparam.Job{
				{URL: "test3", RunTime: 2, Param: "{}"},
				{URL: "test4", RunTime: 3, Param: "{}"},
			},
		},
		&activityparam.ActivityParam{
			EventID: 138,
			Jobs: []activityparam.Job{
				{
					RunTime: 4,
					Param:   "{}",
				},
			},
		},
	})
	require.NoError(err)

	// 清空缓存和数据库中的任务记录
	service.Cache5Min.Flush()
	require.NoError(service.LiveDB.Delete(&liveactivityjob.ActivityJob{}).Error)

	// 创建测试用的 job 记录
	nowTime := goutil.TimeNow().Unix()

	// 使用 job.Job 结构体创建测试数据
	dbJobs := []*liveactivityjob.ActivityJob{
		{
			ID:           1,
			EventID:      124,
			Intro:        "现有任务 1",
			RunTime:      2,
			More:         `{"url":"test1","param":{"a":"b"}}`,
			CreateTime:   nowTime,
			ModifiedTime: nowTime,
			DeleteTime:   0,
		},
		{
			ID:           2,
			EventID:      125,
			Intro:        "现有任务 2",
			RunTime:      5,
			More:         `{"url":"test5","param":{}}`,
			CreateTime:   nowTime,
			ModifiedTime: nowTime,
			DeleteTime:   0,
		},
	}

	err = servicedb.BatchInsert(service.LiveDB, dbJobs[0].TableName(), dbJobs)
	require.NoError(err)

	// 执行测试
	js, err := loadActivityJobsWithCache(time.Unix(2, 0), time.Unix(3, 0))
	require.NoError(err)
	require.Len(js, 4)
	assert.EqualValues([]job{
		{EventID: 124, URL: "test1", RunTime: 2, ActualRunTime: 2, Param: []byte(`{"a":"b"}`)},
		{EventID: 124, URL: "test2", RunTime: 2, ActualRunTime: 2, Param: []byte("{}")},
		{EventID: 134, URL: "test3", RunTime: 2, ActualRunTime: 2, Param: []byte("{}")},
		{EventID: 124, URL: userapi.URIRunJob, RunTime: 2, ActualRunTime: 2, Description: "现有任务 1", Param: []byte(`{"job_id":1}`)},
	}, js)

	key := keys.KeyActivityCronJobList0.Format()
	res, err := service.Redis.ZRangeWithScores(key, 0, -1).Result()
	require.NoError(err)
	require.Len(res, 7)
	assert.Equal(redis.Z{Score: 1, Member: "{\"event_id\":124,\"url\":\"test0\",\"param\":{},\"run_time\":1,\"actual_run_time\":1,\"description\":\"\"}"}, res[0])
	assert.Equal(redis.Z{Score: 2, Member: "{\"event_id\":124,\"url\":\"activity://job/run\",\"param\":{\"job_id\":1},\"run_time\":2,\"actual_run_time\":2,\"description\":\"现有任务 1\"}"}, res[1])
	assert.Equal(redis.Z{Score: 2, Member: "{\"event_id\":124,\"url\":\"test1\",\"param\":{\"a\":\"b\"},\"run_time\":2,\"actual_run_time\":2,\"description\":\"\"}"}, res[2])
	assert.Equal(redis.Z{Score: 2, Member: "{\"event_id\":124,\"url\":\"test2\",\"param\":{},\"run_time\":2,\"actual_run_time\":2,\"description\":\"\"}"}, res[3])
	assert.Equal(redis.Z{Score: 2, Member: "{\"event_id\":134,\"url\":\"test3\",\"param\":{},\"run_time\":2,\"actual_run_time\":2,\"description\":\"\"}"}, res[4])
	assert.Equal(redis.Z{Score: 3, Member: "{\"event_id\":134,\"url\":\"test4\",\"param\":{},\"run_time\":3,\"actual_run_time\":3,\"description\":\"\"}"}, res[5])
	assert.Equal(redis.Z{Score: 4, Member: "{\"event_id\":138,\"url\":\"\",\"param\":{},\"run_time\":4,\"actual_run_time\":4,\"description\":\"\"}"}, res[6])

	// 加载 cache
	js, err = loadActivityJobsWithCache(time.Unix(3, 0), time.Unix(4, 0))
	require.NoError(err)
	require.Len(js, 1)
	assert.EqualValues([]job{
		{EventID: 134, URL: "test4", RunTime: 3, ActualRunTime: 3, Param: []byte("{}")},
	}, js)
}
