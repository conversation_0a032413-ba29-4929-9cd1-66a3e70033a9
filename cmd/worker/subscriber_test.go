package worker

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTryRemoveStaledPlaybackTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	playbacksKey := keys.KeyWorkerPlayback0.Format()
	err := service.Redis.Del(playbacksKey).Err()
	require.NoError(err)

	now := goutil.TimeNow()

	pbOID1 := "624a94b9ac67d2ebbb62606d"
	pbOID2 := "624a838bb2785a4b6b6dbea7"
	members := []*redis.Z{
		{Score: float64(now.Unix()), Member: pbOID1},
		{Score: float64(now.AddDate(0, 0, -2).Unix()), Member: pbOID2},
	}

	_, err = service.Redis.ZAdd(playbacksKey, members...).Result()
	require.NoError(err)

	require.NotPanics(func() {
		tryRemoveStaledPlaybackTasks(playbacksKey, true)
	})

	ids, err := service.Redis.ZRange(playbacksKey, 0, -1).Result()
	require.NoError(err)
	assert.Contains(ids, pbOID1)
	assert.NotContains(ids, pbOID2)
}
