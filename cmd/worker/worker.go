package worker

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strings"
	"sync/atomic"
	"syscall"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/x/bsonx"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/cmd/worker/activity"
	"github.com/MiaoSiLa/live-service/cmd/worker/live/giftrate"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserivceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/util/ratelimit"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	// Queue worker's item
	Queue chan QueueItem

	enableUpos = true
)

// Command worker
type Command struct {
	conf *config.Config
}

// Name command's name
func (c *Command) Name() string {
	return "worker"
}

// Run command
func (c *Command) Run(conf *config.Config) error {
	err := initService(&config.Conf.Service)
	if err != nil {
		return err
	}
	c.conf = conf
	err = Init(&c.conf.Worker)
	if err != nil {
		logger.Fatalf("init worker error: %v", err)
	}
	return err
}

// Init init workers
func Init(conf *config.SectionWorker) error {
	logger.Debugf("worker number is %d, queue number is %d", conf.WorkerNum, conf.QueueNum)

	Queue = make(chan QueueItem, conf.QueueNum)
	metrics = new(Metrics)

	handler.SetMode(config.Conf.HTTP.Mode)

	// limit download & upload speed at 150 Mbps
	downloadBucket := liveserivceredis.NewBucketWithRate("live-record:download", 150, 150)
	globalDownloadLimiter = ratelimit.NewIOLimiter(downloadBucket)
	uploadBucket := liveserivceredis.NewBucketWithRate("live-record:upload", 150, 150)
	globalUploadLimiter = ratelimit.NewIOLimiter(uploadBucket)

	service.Upos.SetLimitReaderFactory(ratelimit.NewLimitReaderFactory(globalUploadLimiter))

	ctx, cancel := context.WithCancel(context.Background())
	g, ctx1 := errgroup.WithContext(ctx)

	srv := newServer()
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})

	for i := 0; i < conf.WorkerNum; i++ {
		g.Go(func() error {
			startWorker(ctx1)
			return nil
		})
	}

	g.Go(func() error {
		startCronJob(ctx1)
		return nil
	})

	g.Go(func() error {
		subscriber(ctx1)
		return nil
	})

	g.Go(func() error {
		activity.Cron(ctx1)
		return nil
	})

	g.Go(func() error {
		giftrate.Cron(ctx1)
		return nil
	})

	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)

	select {
	case <-ctx1.Done():
	case <-osCh:
	}
	cancel()

	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown worker server failed: %v", err)
	}

	return g.Wait()
}

func buildErrorPlaybacksKey() string {
	date := goutil.TimeNow().Format("20060102") // every day rotation
	return keys.KeyWorkerErrors1.Format(date)
}

func handleErrorItem(item QueueItem, needRetry bool) {
	errorPlaybacksKey := buildErrorPlaybacksKey()
	memberKey := item.Playback.OID.Hex()
	logEntry := logger.WithField("playback_id", memberKey)

	if needRetry {
		n, err := service.Redis.ZIncrBy(errorPlaybacksKey, 1, memberKey).Result()
		if err != nil {
			logEntry.Errorf("redis mark error playback error: %v", err)
			return
		}
		_, err = service.Redis.Expire(errorPlaybacksKey, time.Hour*26).Result()
		if err != nil {
			logEntry.Errorf("redis set expire error: %v", err)
			// PASS
		}
		if n < 5 {
			return
		}
	}
	// need set to failed state (after retrying for 5 times)
	err := item.Playback.Update(bson.M{"archive": bsonx.Double(-1)})
	if err != nil {
		logEntry.Errorf("playback update error: %v", err)
		return
	}
	logEntry.Warn("playback handle failed")

	// remove from error queue (after setting state successfully)
	_, err = service.Redis.ZRem(errorPlaybacksKey, memberKey).Result()
	if err != nil {
		logEntry.Errorf("redis zRem error: %v", err)
		// PASS
	}
}

func startWorker(ctx context.Context) {
	for {
		select {
		case item := <-Queue:
			err := handleItem(item)
			if err != nil {
				handleErrorItem(item, err != errDownloadFileSizeExceed)
			}
			atomic.AddInt64(&metrics.Queued, -1)
			atomic.AddInt64(&metrics.Processed, 1)
		case <-ctx.Done():
			return
		}
	}
}

func mergePlaybacksAudio(files []*DownloadFile, formatEqual bool, filePath string) error {
	ctx, cancelFn := context.WithTimeout(context.Background(), 20*time.Minute)
	defer cancelFn()
	args := []string{"-hide_banner", "-loglevel", "warning", "-y"}
	mergeType := ""
	if formatEqual {
		mergeType = "concat"
		var concatLines string
		for _, df := range files {
			// relative to 'runtime/files/202012/03/'
			concatLines += "file '../../../../" + df.FilePath() + "'\n"
		}
		concatFile := filePath + ".txt"
		f, err := os.Create(concatFile)
		if err != nil {
			return err
		}
		_, err = f.Write([]byte(concatLines))
		f.Close()
		defer os.Remove(concatFile)
		if err != nil {
			return err
		}
		args = append(args, "-fflags", "+igndts", "-f", "concat", "-safe", "0",
			"-i", concatFile, "-vn", "-c:a", "copy", "-movflags", "+faststart")
	} else {
		mergeType = "filter_complex"
		var filterComplex string
		for i, df := range files {
			args = append(args, "-i", df.FilePath())
			filterComplex += fmt.Sprintf("[%d:a] ", i)
		}
		filterComplex += fmt.Sprintf(" concat=n=%d:v=0:a=1 [a]", len(files))
		args = append(args, "-filter_complex", filterComplex, "-map", "[a]")
		// Filtering and streamcopy cannot be used together
		args = append(args, "-c:a", "aac", "-b:a", "192k", "-movflags", "+faststart")
	}
	args = append(args, filePath)
	logger.Debugf("ffmpeg args: %v", args)
	var buf bytes.Buffer
	cmd := exec.CommandContext(ctx, "ffmpeg", args...)
	cmd.Stderr = &buf
	cmd.Stdout = &buf
	err := cmd.Run()
	if err != nil {
		logger.Errorf("merge playback (%s) error, ffmpeg output: %s", mergeType, buf.Bytes())
		return err
	}
	logger.Debugf("merge playback (%s) ffmpeg output: %s", mergeType, buf.Bytes())
	return nil
}

type videoResp struct {
	SoundID int64 `json:"sound_id"`
}

func handleItem(item QueueItem) error {
	defer item.Done()
	pb := item.Playback

	logger.WithFields(logger.Fields{
		"playback_id": pb.OID,
		"room_id":     pb.RoomID,
		"user_id":     pb.UserID,
	}).Infof("Worker got item: %s", pb.Title)
	logEntry := logger.WithFields(logger.Fields{
		"playback_id": pb.OID,
	})

	createTime := goutil.TimeUnixMilli(pb.CreatedTime).ToTime()
	if len(pb.URLs) == 1 && strings.HasPrefix(pb.URLs[0].URL, "upos://") {
		// 录制地址为 bvc 合并处理过的 upos 文件时，不需要再下载到本地处理，直接更新回放状态，并进行后续投稿流程
		return updatePlayback(pb, pb.URLs[0].URL, pb.URLs[0].URL, 0, createTime)
	}

	df, err := downloadPlayback(&pb)
	defer df.Cleanup() //nolint:errcheck
	if err != nil {
		logEntry.Errorf("playback download error: %v", err)
		return err
	}

	var publicURL string
	var recordURL string
	if enableUpos {
		f, err := os.Open(df.FilePath())
		if err != nil {
			logEntry.Errorf("open file error: %v", err)
			return err
		}
		defer f.Close()

		fileName := filepath.Base(df.FilePath())
		remotePath := "liverecord/" + createTime.Format("200601/02") + "/" + pb.OID.Hex() + filepath.Ext(fileName)
		logEntry.Infof("uploading %s -> %s", df.FilePath(), remotePath)

		// assume preparing upload require 500+1000 bytes
		d := globalUploadLimiter.Take(500)
		d += globalDownloadLimiter.Take(1000)
		if d > 0 {
			time.Sleep(d)
		}

		res, err := service.Upos.Upload(fileName, remotePath, f, 0)
		if err != nil {
			logEntry.Errorf("upload to upos error: %v", err)
			return err
		}

		publicURL = res.UposURI
		recordURL = publicURL
	} else {
		remotePath := "records/" + createTime.Format("200601/02") + "/" + filepath.Base(df.FilePath())
		logEntry.Infof("uploading %s -> %s", df.FilePath(), remotePath)

		ossArchive, err := service.Storage.Get("archive")
		if err != nil {
			logger.Fatalf("oss get entry error: %v", err)
			return err
		}

		err = ossArchive.PutObjectFromFile(remotePath, df.FilePath())
		if err != nil {
			logEntry.Errorf("upload to oss error: %v", err)
			return err
		}

		// public url from oss config
		publicURL = ossArchive.GetPublicURL(remotePath)
		u, _ := url.Parse(publicURL)
		recordURL = u.Path[1:] // remove leading /
	}

	// cleanup explicitly
	err = df.Cleanup()
	if err != nil {
		logEntry.Errorf("cleanup error: %v", err)
		// PASS
	}

	return updatePlayback(pb, recordURL, publicURL, df.Size, createTime)
}

func updatePlayback(pb models.Playback, recordURL, publicURL string, size int64, createTime time.Time) error {
	update := bson.M{"archive": models.ArchiveDownloaded, "url": publicURL}
	if size > 0 {
		update["size"] = size
	}
	err := pb.Update(update)
	if err != nil {
		logger.WithField("playback_id", pb.OID).Errorf("playback update error: %v", err)
		return err
	}

	var videoRes videoResp
	err = service.MRPC.Call("app://live/video", "", map[string]interface{}{
		"playback_id": pb.OID.Hex(),
		"user_id":     pb.UserID,
		"username":    pb.Username,
		"title":       "【回放】" + pb.Title,
		"description": pb.Description,
		"cover":       pb.GetCover(),
		"record":      recordURL,
		"create_time": createTime.Unix(),
	}, &videoRes)
	if err != nil {
		logger.WithField("playback_id", pb.OID).Errorf("remote live video archive request error: %v", err)
		return err
	}
	// 音频 ID，若为 0 表示该任务进入了视频转音频队列（record 非 UPOS 地址时）
	if videoRes.SoundID != 0 {
		logger.WithFields(logger.Fields{"playback_id": pb.OID, "sound_id": videoRes.SoundID}).Info("archive done")
	}
	return nil
}
