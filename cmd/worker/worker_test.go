package worker

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/upos"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestMain(m *testing.M) {
	initTest()

	m.Run()
}

type noLimiter struct {
}

func (*noLimiter) Take(n int64) time.Duration {
	return 0
}

func (*noLimiter) Return(n int64, d time.Duration) {
}

func initTest() {
	service.InitTest(true)

	globalDownloadLimiter = &noLimiter{}
	globalUploadLimiter = &noLimiter{}
	service.Upos = upos.NewClient(config.Conf.Service.Upos)
}

func TestHandleItem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// disable upload upos
	enableUpos = false

	wg := &sync.WaitGroup{}
	channelID := "live-test"
	ksyunVid := "ks3_test"
	ksyunURL := "https://ks3-cn-beijing.ksyun.com/live-audio/record/live-fm/live_18113499_10/flv/not_exists.flv"
	testRoomID := int64(18113499)
	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	pb := models.Playback{
		RoomID:      testRoomID,
		RoomOID:     r.OID,
		UserID:      12,
		Title:       "test",
		ChannelID:   &channelID,
		Vid:         &ksyunVid,
		URL:         &ksyunURL,
		CreatedTime: 1568278030000,
	}
	pb.OID, _ = primitive.ObjectIDFromHex("5d7a0f2fc2cf754ab07fe217")
	item := NewQueueItem(pb, wg)
	err = handleItem(item)
	assert.EqualError(err, "http status 404")

	cancel := mrpc.SetMock("app://live/video", func(input interface{}) (output interface{}, err error) {
		return videoResp{SoundID: 1111}, nil
	})
	defer cancel()

	ksyunURL2 := "https://static-test.maoercdn.com/test/flv/live_10659544_10_1568278030_1568278041.flv"
	pb.URL = &ksyunURL2
	item = NewQueueItem(pb, wg)
	err = handleItem(item)
	assert.NoError(err)

	// enable upload upos
	enableUpos = true
	item = NewQueueItem(pb, wg)
	err = handleItem(item)
	assert.NoError(err)

	// 不用本地处理回放
	pb.URLs = []models.PlaybackURL{{URL: "upos://test.m4a"}}
	item = NewQueueItem(pb, wg)
	err = handleItem(item)
	assert.NoError(err)
}
