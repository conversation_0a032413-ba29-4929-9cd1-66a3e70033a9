package rpc

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/cmd/worker/rpc/dev"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
)

// Handler 返回 handler
func Handler() *handler.HandlerV2 {
	return &handler.HandlerV2{
		Name:        "rpc/live-service-worker",
		Middlewares: gin.HandlersChain{rpc.Middleware(config.Conf.HTTP.RPCKey)},
		SubHandlers: []handler.HandlerV2{
			dev.Handler(),
		},
	}
}
