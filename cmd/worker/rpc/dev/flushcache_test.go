package dev

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionDevFlushCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5s.Set("test", "test", 0)
	service.Cache10s.Set("test", "test", 0)
	service.Cache5Min.Set("test", "test", 0)

	resp, msg, err := actionDevFlushCache(handler.NewTestContext(http.MethodPost, "/rpc/live-service/dev/flush-cache", false, nil))
	require.NoError(err)
	assert.Nil(resp)
	assert.Equal("删除成功！", msg)

	_, ok := service.Cache5s.Get("test")
	assert.False(ok)
	_, ok = service.Cache10s.Get("test")
	assert.False(ok)
	_, ok = service.Cache5Min.Get("test")
	assert.False(ok)
}
