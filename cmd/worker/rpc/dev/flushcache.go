package dev

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// actionDevFlushCache 清除 worker 缓存
/**
 * @api {post} /rpc/live-service-worker/dev/flush-cache 清除 worker 缓存
 * @apiVersion 0.1.0
 * @apiGroup workerrpc
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功！",
 *     "data": null
 *   }
 */
func actionDevFlushCache(*handler.Context) (handler.ActionResponse, string, error) {
	service.Cache5s.Flush()
	service.Cache10s.Flush()
	service.Cache5Min.Flush()
	return nil, "删除成功！", nil
}
