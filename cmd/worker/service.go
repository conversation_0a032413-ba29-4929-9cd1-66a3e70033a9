package worker

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/netease/vod"
	"github.com/MiaoSiLa/live-service/service/tencentcos"
	"github.com/MiaoSiLa/live-service/service/upos"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
)

// Init services
func initService(conf *config.SectionService) (err error) {
	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	service.LiveDB, err = servicedb.InitDatabase(&conf.LiveDB)
	if err != nil {
		return
	}
	service.NewADB, err = servicedb.InitDatabase(&conf.NewADB)
	if err != nil {
		return
	}

	service.MongoDB, err = mongodb.NewMongoDB(&conf.MongoDB)
	if err != nil {
		return
	}

	service.TencentCOS, err = tencentcos.NewCOSClient(&conf.TencentCOS)
	if err != nil {
		return
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}

	service.Storage = storage.NewClient(conf.Storage)
	service.Upos = upos.NewClient(conf.Upos)
	service.Vod = vod.NewVodClient(&conf.Vod)

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return
	}

	service.AfterInit()
	return nil
}
