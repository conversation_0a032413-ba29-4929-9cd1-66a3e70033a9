package fixdata

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const batchCount = 100

func getDataByLastID(startID int64) ([]livetxnorder.LiveTxnOrder, error) {
	var orders []livetxnorder.LiveTxnOrder
	err := service.LiveDB.Limit(batchCount).Order("id ASC").Find(&orders, "goods_type = ? AND id > ? AND more = ''", livegoods.GoodsTypeWish, startID).Error
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return orders, nil
}

func fixWishesData(list []livetxnorder.LiveTxnOrder) error {
	tIDs := make([]int64, 0, len(list))
	for _, wish := range list {
		tIDs = append(tIDs, wish.TID)
	}

	var logs []transactionlog.TransactionLog
	err := transactionlog.DB().Find(&logs, "id IN (?)", tIDs).Error
	if err != nil {
		return err
	}

	m := goutil.ToMap(logs, "ID").(map[int64]transactionlog.TransactionLog)
	for _, wish := range list {
		log, ok := m[wish.TID]
		if !ok {
			logger.Warnf("找不到交易记录，wish_id: %d, tid: %d", wish.ID, wish.TID)
			continue
		}

		if log.AllCoin == 0 || log.Num == 0 {
			logger.Warnf("交易记录数据存在异常, wish_id: %d, tid: %d", wish.ID, wish.TID)
			continue
		}

		err = service.LiveDB.Table(livetxnorder.TableName()).Where("id = ? AND more = ''", wish.ID).Updates(map[string]interface{}{
			"modified_time": goutil.TimeNow().Unix(),
			"price":         log.AllCoin,
			"more":          fmt.Sprintf(`{"open_status":-1,"num":%d}`, log.Num),
		}).Error
		if err != nil {
			logger.Warnf("更新许愿池数据失败，wish_id: %d, tid: %d", wish.ID, wish.TID)
			return err
		}
	}

	return nil
}

// fixWishes 修复许愿池错误数据
func fixWishes() error { //nolint:deadcode,unused
	fmt.Println("开始修复许愿池数据")
	var (
		curID int64
		now   = goutil.TimeNow()
	)
	for count := 0; ; count++ {
		// 每执行 10 次打印当前处理进度
		if count%10 == 0 {
			fmt.Printf("已处理至 %d, 已处理 %d 条数据, 共用时 %s\r", curID, count*batchCount, time.Since(now))
		}

		data, err := getDataByLastID(curID)
		if err != nil {
			fmt.Printf("已处理至 %d，%v\n", curID, err)
			return err
		}

		if len(data) == 0 {
			break
		}

		err = fixWishesData(data)
		if err != nil {
			fmt.Printf("已处理至 %d，%v\n", curID, err)
			return err
		}

		// 如果数据小于 batchCount，说明已经处理完毕
		if len(data) < batchCount {
			break
		}

		last := data[len(data)-1]
		curID = last.ID
	}

	fmt.Println("修复许愿池数据完成")
	return nil
}
