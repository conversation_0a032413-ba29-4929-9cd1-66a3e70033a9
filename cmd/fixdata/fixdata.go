package fixdata

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/netease/vod"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// Command server
type Command struct {
	conf *config.Config
}

// Name command's name
func (c *Command) Name() string {
	return "fixdata"
}

// Run command
func (c *Command) Run(conf *config.Config) error {
	c.conf = conf

	return nil
}

func (c *Command) syncPlaybacks() error { //nolint:deadcode,unused
	ret, err := service.Vod.ListVideo(0, 500, 0, 0, "")
	if err != nil {
		return err
	}
	pageNum := ret.PageNum
	var videos []vod.Video
	for p := 1; p <= pageNum; p++ {
		ret, err = service.Vod.ListVideo(p, 500, 0, 0, "")
		if err != nil {
			return err
		}
		videos = append(videos, ret.List...)
	}
	collection := service.MongoDB.Collection("playbacks")
	pbs, err := findNonArchivePlaybacks(collection)
	if err != nil {
		return err
	}
	deleted := 0
	updated := 0
	for _, pb := range pbs {
		if pb.ChannelID == nil {
			continue
		}
		v := findVideo(videos, *pb.ChannelID)
		if v != nil {
			vid := strconv.FormatInt(v.Vid, 10)
			err = pb.Update(bson.M{"url": v.DownloadOrigURL, "vid": vid, "type": "flv", "filename": v.VideoName})
			if err != nil {
				return err
			}
			updated++
		}
		/*if pb.Vid == nil {
			continue
		}
		exists, err := existsArchivePlayback(collection, *pb.Vid, pb.CreatedTime)
		if err != nil {
			return err
		}
		if exists {
			_, err = collection.DeleteOne(nil, bson.M{"_id": pb.ID_})
			if err != nil {
				return err
			}
			deleted++
			logger.WithFields(logger.Fields{
				"playback_id": pb.ID_,
			}).Infof("deleted duplicated playback %s", pb.Title)
		}*/

	}
	logger.Infof("deleted %d, updated %d playback(s)", deleted, updated)
	return nil
}

func existsArchivePlayback(collection *mongo.Collection, vid string, createdTime int64) (bool, error) { //nolint:deadcode,unused
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"archive": bson.M{"$gt": 0}, "vid": vid, "created_time": createdTime}
	var pb models.Playback
	err := collection.FindOne(ctx, filter).Decode(&pb)
	if mongodb.IsNoDocumentsError(err) {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	return pb.CreatedTime == createdTime, nil
}

func findNonArchivePlaybacks(collection *mongo.Collection) ([]models.Playback, error) { //nolint:deadcode,unused
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pbs []models.Playback
	//filter := bson.M{"archive": 0}
	filter := bson.M{"vid": nil}
	opts := options.Find().
		SetSort(bson.M{"created_time": -1})
	cur, err := collection.Find(ctx, filter, opts)
	if err != nil {
		logger.Errorf("fine error: %v", err)
		return nil, err
	}
	defer cur.Close(ctx)
	for cur.Next(ctx) {
		var pb models.Playback
		err = cur.Decode(&pb)
		if err != nil {
			logger.Debugf("doc decode error: %v", err)
			continue
		}
		pbs = append(pbs, pb)
	}
	if err = cur.Err(); err != nil {
		logger.Errorf("find cursor error: %v", err)
		return nil, err
	}
	return pbs, nil
}

func findVideo(videos []vod.Video, filename string) *vod.Video { //nolint:deadcode,unused
	for _, v := range videos {
		if v.TypeID != 85405 {
			continue
		}
		//if strings.HasPrefix(v.VideoName, filename+"_") {
		if strings.HasPrefix(v.VideoName, "0-"+filename+"-") {
			if strings.HasSuffix(v.VideoName, ".flv") {
				return &v
			}
		}
	}
	return nil
}

// fixRank 修复月榜数据
func fixRank(fixTime time.Time) error {
	startTime := time.Date(fixTime.Year(), fixTime.Month(), 1, 0, 0, 0, 0, fixTime.Location())
	endTime := time.Date(fixTime.Year(), fixTime.Month()+1, 1, 0, 0, 0, 0, fixTime.Location())
	logger.Infof("start fix live rank, fix time: %v", fixTime)

	logger.Infof("start calculate revenue")
	type revenue struct {
		RoomID  int64 `bson:"_id"`
		Revenue int64 `bson:"revenue"`
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// gift 收益
	collection := service.MongoDB.Collection("live_gifts")
	match := bson.M{"$match": bson.M{"sent_time": bson.M{"$gte": startTime, "$lt": endTime}}}
	group := bson.M{"$group": bson.M{
		"_id":     "$room_id",
		"revenue": bson.M{"$sum": "$price"},
	}}
	giftCur, err := collection.Aggregate(ctx, bson.A{match, group})
	if err != nil {
		logger.Errorf("find gift revenue failed: %v", err)
		return err
	}
	defer giftCur.Close(ctx)
	var giftRevenues []revenue
	err = giftCur.All(ctx, &giftRevenues)
	if err != nil {
		logger.Error(err)
		return err
	}
	// question 收益
	collection = service.MongoDB.Collection("live_questions")
	match = bson.M{"$match": bson.M{"answered_time": bson.M{"$gte": startTime, "$lt": endTime}}}
	group = bson.M{"$group": bson.M{
		"_id":     "$room_id",
		"revenue": bson.M{"$sum": "$price"},
	}}
	questionCur, err := collection.Aggregate(ctx, bson.A{match, group})
	if err != nil {
		logger.Errorf("find gift revenue failed: %v", err)
		return err
	}
	defer questionCur.Close(ctx)
	var questionRevenues []revenue
	err = questionCur.All(ctx, &questionRevenues)
	if err != nil {
		logger.Error(err)
		return err
	}
	// 获取总收益
	totalRevenue := make(map[int64]int64, len(giftRevenues)) // map[RoomID]revenue
	for i := 0; i < len(giftRevenues); i++ {
		totalRevenue[giftRevenues[i].RoomID] = giftRevenues[i].Revenue
	}
	for i := 0; i < len(questionRevenues); i++ {
		totalRevenue[questionRevenues[i].RoomID] =
			totalRevenue[questionRevenues[i].RoomID] + questionRevenues[i].Revenue
	}
	logger.Info("start update rank list")
	key := usersrank.Key(usersrank.TypeMonth, fixTime)
	members := make([]*redis.Z, 0, len(totalRevenue))
	roomIDs := make([]int64, 0, len(totalRevenue))
	for roomID := range totalRevenue {
		roomIDs = append(roomIDs, roomID)
	}
	rooms, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(bson.M{"room_id": 1, "creator_id": 1}))
	if err != nil {
		logger.Errorf("find rooms error: %v", err)
		return err
	}
	if len(rooms) == 0 {
		logger.Info("len(rooms) == 0")
		return nil
	}
	for i := 0; i < len(rooms); i++ {
		revenue := totalRevenue[rooms[i].RoomID]
		if revenue != 0 && rooms[i].CreatorID != 0 {
			members = append(members, &redis.Z{Score: float64(revenue), Member: rooms[i].CreatorID})
		}
	}
	pipe := service.Redis.Pipeline()
	pipe.Del(key)
	pipe.ZAdd(key, members...)
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		return err
	}
	logger.Info("finish fix live rank")
	return nil
}

// fixRoomTotalDuration 修复总直播时长
func fixRoomTotalDuration() error { //nolint:deadcode,unused
	limit := int64(10)
	skip := int64(0)
	ticker := time.NewTicker(time.Second)
	closeCh := make(chan struct{})
	defer func() {
		closeCh <- struct{}{}
	}()
	go func() {
		for {
			select {
			case <-closeCh:
				logger.Infof("fixRoomTotalDuration finished, skip: %d", skip)
			case <-ticker.C:
				logger.Infof("fixRoomTotalDuration running, skip: %d", skip)
			}
		}
	}()
	logger.Info("start fixRoomTotalDuration")

	col := room.Collection()
	for {
		var l []room.Simple
		ctx := context.Background()
		cur, err := col.Find(ctx, bson.M{},
			options.Find().SetProjection(bson.M{"room_id": 1}).SetSkip(skip).SetLimit(limit))
		if err != nil {
			logger.WithField("skip", skip).Error(err)
			return err
		}
		err = cur.All(ctx, &l)
		if err != nil {
			logger.WithField("skip", skip).Error(err)
			return err
		}
		err = cur.Close(ctx)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		err = updateTotalDuration(l)
		if err != nil {
			logger.WithField("skip", skip).Error(err)
			return err
		}
		if size := len(l); size != 0 {
			skip += int64(len(l))
		} else {
			break
		}
	}
	return nil
}

type liveLog struct {
	RoomID        int64 `bson:"_id"`
	TotalDuration int64 `bson:"total_duration"`
}

func updateTotalDuration(l []room.Simple) error {
	ctx := context.Background()
	roomIDs := make([]int64, len(l))
	for i := range l {
		roomIDs[i] = l[i].RoomID
	}
	col := livelog.Collection()
	cur, err := col.Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{"room_id": bson.M{"$in": roomIDs}}},
		bson.M{"$group": bson.M{
			"_id":            "$room_id",
			"total_duration": bson.M{"$sum": "$duration"}},
		},
	})
	if err != nil {
		return err
	}
	var liveLogs []liveLog
	err = cur.All(ctx, &liveLogs)
	if err != nil {
		return err
	}
	m := make(map[int64]int64, len(liveLogs))
	for i := range liveLogs {
		m[liveLogs[i].RoomID] = liveLogs[i].TotalDuration
	}
	roomCol := room.Collection()
	for i := range roomIDs {
		_, err = roomCol.UpdateOne(ctx, bson.M{"room_id": roomIDs[i]},
			bson.M{"$set": bson.M{"statistics.total_duration": m[roomIDs[i]]}})
		if err != nil {
			logger.WithField("room_id", liveLogs[i].RoomID).Error(err)
			// PASS
		}
	}
	return nil
}
