package fixdata

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestFixRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 1559318400 2019-06-01
	fixTime := time.Unix(1559318400, 0)
	endTime := fixTime.Add(30 * 24 * time.Hour)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("live_gifts")
	_, err := collection.DeleteMany(ctx,
		bson.M{"sent_time": bson.M{"$gte": fixTime, "$lt": endTime}})
	assert.NoError(err)
	oid, _ := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	lg := &livegifts.LiveGift{
		RoomOID:   oid,
		RoomID:    22489473,
		GiftNum:   1,
		GiftPrice: 6,
		UserID:    -13,
		SentTime:  fixTime.Add(time.Hour),
	}
	_, err = livegifts.UpdateSave(lg, nil, primitive.NilObjectID)
	assert.NoError(err)
	collection = service.MongoDB.Collection("live_questions")
	_, err = collection.DeleteMany(ctx,
		bson.M{"answered_time": bson.M{"$gte": fixTime, "$lt": endTime}})
	assert.NoError(err)
	lqh := livequestion.Helper{
		RoomOID:      lg.RoomOID,
		RoomID:       lg.RoomID,
		UserID:       lg.UserID,
		Username:     "test",
		Question:     "test",
		Price:        20,
		Status:       livequestion.StatusFinished,
		AnsweredTime: &lg.SentTime,
	}
	_, err = collection.InsertOne(ctx, lqh)
	assert.NoError(err)

	key := usersrank.Key(usersrank.TypeMonth, fixTime)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(fixRank(fixTime))
	afterFix, err := service.Redis.ZRange(key, 0, -1).Result()
	require.NoError(err)
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())
	assert.Equal([]string{"10"}, afterFix)
}

func TestFixRoomTotalDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := room.Collection()
	_, err := col.UpdateOne(ctx, bson.M{},
		bson.M{"$set": bson.M{"statistics.total_duration": primitive.Undefined{}}})
	require.NoError(err)

	assert.NoError(fixRoomTotalDuration())
	err = col.FindOne(ctx,
		bson.M{"room_id": bson.M{"$exists": true},
			"statistics.total_duration": bson.M{"$exists": false}}).Err()
	assert.Equal(mongo.ErrNoDocuments, err)
}
