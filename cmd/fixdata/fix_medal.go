package fixdata

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 参数秒级时间戳
func findFreeLiveGifts(startTime, endTime int64) ([]livegifts.LiveGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var liveGifts []livegifts.LiveGift
	cur, err := livegifts.Collection().Find(ctx, bson.M{
		"sent_time": bson.M{"$gte": time.Unix(startTime, 0), "$lt": time.Unix(endTime, 0)},
		"gift_type": gift.TypeFree,
		"gift_id":   bson.M{"$in": []int64{301, 30005, 30039, 30006}}, // 猫粮、猫罐头、风铃物语、守护之力
	})
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &liveGifts)
	if err != nil {
		return nil, err
	}
	return liveGifts, nil
}

type rebateLiveGift struct {
	UserID     int64 `bson:"user_id"`
	RoomID     int64 `bson:"room_id"`
	TotalPrice int64 `bson:"total_price"`
}

// 参数秒级时间戳
func findRebateLiveGifts(startTime, endTime int64) ([]rebateLiveGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := livegifts.Collection().Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{
			"sent_time": bson.M{"$gte": time.Unix(startTime, 0), "$lt": time.Unix(endTime, 0)},
			"gift_type": gift.TypeRebate,
		}},
		bson.M{"$group": bson.M{
			"_id":         bson.M{"user_id": "$user_id", "room_id": "$room_id"},
			"total_price": bson.M{"$sum": "$price"},
		}},
		bson.M{"$project": bson.M{
			"user_id":     "$_id.user_id",
			"room_id":     "$_id.room_id",
			"total_price": "$total_price",
		}},
		bson.M{"$sort": bson.M{"user_id": 1}},
	})
	if err != nil {
		return nil, err
	}
	var res []rebateLiveGift
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// 参数秒级时间戳
func findQuestionAndGiftTime(startTime, endTime int64) ([]transactionlog.TransactionLog, error) {
	var logs []transactionlog.TransactionLog
	err := transactionlog.ADB().
		Where("confirm_time >= ? AND confirm_time < ?", startTime, endTime).
		Where("status = ? AND type IN (?) AND attr IN (?)",
			transactionlog.StatusSuccess,
			[]int64{transactionlog.TypeLive, transactionlog.TypeGuildLive},
			[]int64{transactionlog.AttrCommon, transactionlog.AttrLiveRebateGift, transactionlog.AttrLiveLuckyGift}).
		Order("confirm_time ASC, id ASC").
		Find(&logs).Error
	if err != nil {
		return nil, err
	}
	return logs, nil
}

// 参数秒级时间戳
func findSuperFansByTime(startTime, endTime int64) ([]livetxnorder.LiveTxnOrder, error) {
	var superFans []livetxnorder.LiveTxnOrder
	err := service.LiveDB.Table(livetxnorder.TableName()).
		Where("goods_type = ? AND status = ?", livegoods.GoodsTypeSuperFan, livetxnorder.StatusSuccess).
		Where("create_time >= ? AND create_time < ?", startTime, endTime).
		Find(&superFans).Order("create_time ASC").Error
	if err != nil {
		return nil, err
	}
	return superFans, nil
}

const (
	fixMedalStartTime = 1710179545 // 2024-03-12 01:52:25
	medalFailTime     = 1710230706 // 2024-03-12 16:05:06
	fixMedalEndTime   = 1710240071 // 2024-03-12 18:41:11
)

// fixFreeGiftMedals
// 备份遗漏: 10394 个
func fixFreeGiftMedals() error {
	fmt.Println("开始修复免费礼物粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)

	// orders, err := findFreeLiveGifts(fixMedalStartTime, medalFailTime)
	orders, err := findFreeLiveGifts(medalFailTime, fixMedalEndTime)
	if err != nil {
		fmt.Printf("查询数据错误 %v\n", err)
		return err
	}
	fmt.Printf("共计 %d 条数据\n", len(orders))
	for i := range orders {
		r, err := findRoomByRoomID(orders[i].RoomID)
		if err != nil {
			fmt.Printf("order_id: %s 处理失败 %v\n", orders[i].OID.Hex(), err)
			continue
		}
		if r == nil || r.Medal == nil {
			continue
		}
		addParam := &livemedal.AddFreePointParam{
			RoomID:    r.RoomID,
			CreatorID: r.CreatorID,
			UserID:    orders[i].UserID,
			PointAdd:  orders[i].Point,
			Scene:     livemedalpointlog.SceneTypeFreeGift,
		}
		_, err = addParam.AddFreePoint()
		if err != nil {
			fmt.Printf("order_id: %s 处理失败 %v\n", orders[i].OID.Hex(), err)
			continue
		}
		fmt.Printf("%d, order_id: %s 处理成功\r", num, orders[i].OID.Hex())
		num++
	}

	fmt.Printf("共成功修复 %d 条数据共用时 %s\n", num, time.Since(now))
	fmt.Println("修复粉丝勋章数据完成")
	return nil
}

// fixQuestionAndGiftLiveMedals
// 备份遗漏: 54700 个
// SELECT count(*) FROM transaction_log WHERE (confirm_time >= 1710179545 AND confirm_time < 1710230706) AND
// (status = 1 AND type IN (1,9) AND attr IN (0,3,4))
// NOTICE: 随机礼物和白给礼物不能使用 transaction_log.all_coin，这里只能处理提问和普通礼物
func fixQuestionAndGiftLiveMedals() error {
	fmt.Println("开始修复粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)

	// orders, err := findQuestionAndGiftTime(fixMedalStartTime, medalFailTime)
	orders, err := findQuestionAndGiftTime(medalFailTime, fixMedalEndTime)
	if err != nil {
		fmt.Printf("查询数据错误 %v\n", err)
		return err
	}
	fmt.Printf("共计 %d 条数据\n", len(orders))
	for i := range orders {
		r, err := findRoomByCreatorID(orders[i].ToID)
		if err != nil {
			fmt.Printf("order_id: %d 处理失败 %v\n", orders[i].ID, err)
			continue
		}
		if r == nil || r.Medal == nil {
			continue
		}
		medalParam := livemedal.AddPointParam{
			RoomOID:   r.OID,
			RoomID:    r.RoomID,
			CreatorID: r.CreatorID,
			UserID:    orders[i].FromID,
			MedalName: r.Medal.Name,
			From:      livemedal.FromNormalConsume,
			PointAdd:  orders[i].AllCoin,
		}
		if orders[i].GiftID == 0 {
			medalParam.Type = livemedal.TypeQuestionAddMedalPoint
			medalParam.Source = livemedal.ChangeSourceQuestion
		} else {
			medalParam.Type = livemedal.TypeGiftAddMedalPoint
			medalParam.Source = livemedal.ChangeSourceGift
		}
		medalParam.UV, err = vip.UserActivatedVip(orders[i].FromID, false, nil)
		if err != nil {
			// PASS
			fmt.Printf("order_id: %d 贵族状态查询失败\n", orders[i].ID)
		}
		_, err = medalParam.AddPoint()
		if err != nil {
			fmt.Printf("order_id: %d 处理失败 %v\n", orders[i].ID, err)
			continue
		}
		fmt.Printf("%d, order_id: %d 处理成功\r", num, orders[i].ID)
		num++
	}

	fmt.Printf("共成功修复 %d 条数据共用时 %s\n", num, time.Since(now))
	fmt.Println("修复粉丝勋章数据完成")
	return nil
}

// fixSuperFanLiveMedals
// 备份遗漏: 51 个
func fixSuperFanLiveMedals() error {
	fmt.Println("开始修复超粉粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)

	// superFans, err := findSuperFansByTime(fixMedalStartTime, medalFailTime)
	superFans, err := findSuperFansByTime(medalFailTime, fixMedalEndTime)
	if err != nil {
		fmt.Println("查询超粉数据错误")
	}
	fmt.Printf("共计 %d 条数据\n", len(superFans))
	for i := range superFans {
		r, err := findRoomByCreatorID(superFans[i].SellerID)
		if err != nil {
			fmt.Printf("购买超粉 order_id: %d 处理失败\n", superFans[i].ID)
			continue
		}
		if r == nil || r.Medal == nil {
			continue
		}
		medalParam := livemedal.AddPointParam{
			RoomOID:    r.OID,
			RoomID:     r.RoomID,
			CreatorID:  r.CreatorID,
			UserID:     superFans[i].BuyerID,
			MedalName:  r.Medal.Name,
			Type:       livemedal.TypeSuperFanMedalPoint,
			Source:     livemedal.ChangeSourceBuySuperFan,
			From:       livemedal.FromBuySuperFan,
			PointAdd:   int64(superFans[i].Price),
			ExpireTime: superFans[i].ExpireTime,
		}
		medalParam.UV, err = vip.UserActivatedVip(superFans[i].BuyerID, false, nil)
		if err != nil {
			// PASS
			fmt.Printf("购买超粉 order_id: %d 贵族状态查询失败\n", superFans[i].ID)
		}
		_, err = medalParam.AddPoint()
		if err != nil {
			fmt.Printf("购买超粉 order_id: %d 处理失败\n", superFans[i].ID)
			continue
		}
		fmt.Printf("%d, 购买超粉 order_id: %d 处理成功\r", num, superFans[i].ID)
		num++
	}

	fmt.Printf("共成功修复 %d 条数据共用时 %s\n", num, time.Since(now))
	fmt.Println("修复超粉粉丝勋章数据完成")
	return nil
}

var roomMap = make(map[int64]*room.Room)

func findRoomByRoomID(roomID int64) (*room.Room, error) {
	r, ok := roomMap[roomID]
	if ok {
		return r, nil
	}
	r, err := room.FindOne(bson.M{"room_id": roomID})
	if err != nil {
		return nil, err
	}
	roomMap[roomID] = r
	return r, nil
}

var creatorRoomMap = make(map[int64]*room.Room)

func findRoomByCreatorID(creatorID int64) (*room.Room, error) {
	r, ok := creatorRoomMap[creatorID]
	if ok {
		return r, nil
	}
	r, err := room.FindOne(bson.M{"creator_id": creatorID})
	if err != nil {
		return nil, err
	}
	creatorRoomMap[creatorID] = r
	return r, nil
}

// findLiveListenUserIDs 参数秒级时间戳
func findLiveListenUserIDs() ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := livelistenlogs.Collection().Aggregate(ctx, []bson.M{
		{"$match": bson.M{"start_time": bson.M{"$gte": medalFailTime * 1000, "$lt": fixMedalEndTime * 1000}}},
		{"$group": bson.M{"_id": "$user_id"}},
		{"$sort": bson.M{"_id": 1}},
	})
	if err != nil {
		return nil, err
	}
	var res []struct {
		UserID int64 `bson:"_id"`
	}
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(res))
	for i := range res {
		userIDs = append(userIDs, res[i].UserID)
	}
	return userIDs, nil
}

func fixFreeGiftUserItems() error {
	fmt.Println("开始修复礼物数据")
	var (
		num int
		now = goutil.TimeNow()
	)
	userIDs, err := findLiveListenUserIDs()
	if err != nil {
		fmt.Printf("查询数据错误 %v\n", err)
		return err
	}
	fmt.Printf("需要修复 %d 个用户\n", len(userIDs))
	gMap, err := gift.FindGiftMapByGiftIDs([]int64{useritems.GiftIDCatFood, useritems.GiftIDCatCanFood})
	if err != nil {
		fmt.Printf("查询礼物错误 %v\n", err)
		return err
	}
	if len(gMap) != 2 {
		// 礼物不存在
		fmt.Printf("gift %d or %d not exists\n", useritems.GiftIDCatFood, useritems.GiftIDCatCanFood)
		return err
	}
	for i := range userIDs {
		userID := userIDs[i]
		// 先查询用户勋章，若该用户没有勋章，则不添加猫粮
		lm, err := livemedal.FindOne(bson.M{
			"user_id": userID,
			"status":  bson.M{"$gt": livemedal.StatusPending}},
			options.FindOne().SetSort(bson.M{"point": -1}))
		if err != nil {
			fmt.Printf("查询勋章错误 %v\n", err)
			continue
		}
		if lm == nil {
			continue
		}
		catFoodCount := livemedal.CatFoodAddCount(lm.Level)
		superMedalCount, err := livemedal.CountSuperMedal(userID)
		if err != nil {
			fmt.Printf("userID %d 查询超粉勋章错误 %v\n", userID, err)
			continue
		}
		catCanFoodCount := 10 * superMedalCount
		if catFoodCount == 0 && catCanFoodCount == 0 {
			continue
		}
		num301, num30005 := findUserItems(userID)
		addCatFoodNum := catFoodCount - num301
		if addCatFoodNum <= 0 {
			addCatFoodNum = 0
		}
		addCatCanFoodNum := catCanFoodCount - num30005
		if addCatCanFoodNum < 0 {
			addCatCanFoodNum = 0
		}
		if addCatFoodNum == 0 && addCatCanFoodNum == 0 {
			continue
		}

		et := time.Date(2024, 3, 14, 0, 0, 0, 0, time.Local).Unix()
		adder := useritems.NewAdder(userID, 2)
		if superMedalCount > 0 {
			et = util.BeginningOfWeek(now.Add(7 * 24 * time.Hour)).Unix()
		}
		if addCatCanFoodNum > 0 {
			adder.Append(gMap[useritems.GiftIDCatCanFood], addCatCanFoodNum, now.Unix(), et)
		}
		if addCatFoodNum > 0 {
			adder.Append(gMap[useritems.GiftIDCatFood], addCatFoodNum, now.Unix(), et)
		}
		err = adder.Add()
		if err != nil {
			fmt.Printf("userID %d 发放礼物错误 %v\n", userID, err)
			continue
		}
		fmt.Printf("用户 %d 发放猫粮 %d, 猫罐头 %d, 超粉数量 %d\n", userID, addCatFoodNum, addCatCanFoodNum, superMedalCount)
		num++
	}
	fmt.Printf("共成功修复 %d 个用户共用时 %s\n", num, time.Since(now))
	fmt.Println("修复免费礼物数据完成")
	return nil
}

func findUserItems(userID int64) (catFood, catCanFood int64) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	st := time.Date(2024, 3, 12, 0, 0, 0, 0, time.Local)
	et := time.Date(2024, 3, 13, 0, 0, 0, 0, time.Local)

	var items []*useritems.UserItem
	cur, err := useritems.Collection().Find(ctx, bson.M{
		"user_id":     userID,
		"create_time": bson.M{"$gte": st.Unix(), "$lt": et.Unix()},
		"gift_id":     bson.M{"$in": []int64{useritems.GiftIDCatFood, useritems.GiftIDCatCanFood}},
	})
	if err != nil {
		fmt.Printf("获取用户 %d 已领猫粮猫罐头错误，error: %v\n", userID, err)
		return 0, 0
	}
	err = cur.All(ctx, &items)
	if err != nil {
		fmt.Printf("获取用户 %d 已领猫粮猫罐头错误，error: %v\n", userID, err)
		return 0, 0
	}
	for i := range items {
		switch items[i].GiftID {
		case useritems.GiftIDCatFood:
			if items[i].CreateTime < medalFailTime || items[i].CreateTime >= fixMedalEndTime {
				catFood += 100000000 // 非勋章数据丢失的时间内领取过的，没有领取错误，返回一个较大值，外面相减判断为负值会不再补发
			} else {
				catFood += items[i].GainNum
			}
		case useritems.GiftIDCatCanFood:
			catCanFood += items[i].GainNum
		}
	}
	return
}

// fixRebateGiftLiveMedals 修复白给礼物粉丝勋章数据亲密度
func fixRebateGiftLiveMedals() error {
	fmt.Println("开始修复粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)

	orders, err := findRebateLiveGifts(fixMedalStartTime, medalFailTime)
	// orders, err := findRebateLiveGifts(medalFailTime, fixMedalEndTime)
	if err != nil {
		fmt.Printf("查询数据错误 %v\n", err)
		return err
	}
	fmt.Printf("共计 %d 条数据\n", len(orders))
	for i := range orders {
		r, err := findRoomByRoomID(orders[i].RoomID)
		if err != nil {
			fmt.Printf("用户: %d, 直播间 %d, 查询直播间失败 %v\n", orders[i].UserID, orders[i].RoomID, err)
			continue
		}
		if r == nil || r.Medal == nil {
			continue
		}
		err = addPoint(orders[i].UserID, orders[i].RoomID, orders[i].TotalPrice)
		if err != nil {
			fmt.Printf("用户: %d 在直播间 %d 处理失败 %v\n", orders[i].UserID, orders[i].RoomID, err)
			continue
		}
		num++
	}

	fmt.Printf("共成功修复 %d 条数据共用时 %s\n", num, time.Since(now))
	fmt.Println("修复粉丝勋章数据完成")
	return nil
}

func addPoint(userID, roomID, point int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	record := new(livemedal.LiveMedal)
	filter := bson.M{"user_id": userID, "room_id": roomID}
	err := livemedal.Collection().FindOne(ctx, filter).Decode(record)
	if err != nil {
		if !mongodb.IsNoDocumentsError(err) {
			return err
		}
		record = nil
	}
	if record == nil {
		fmt.Printf("用户 %d 在直播间 %d 未找到勋章数据\n", userID, roomID)
		return nil
	}
	// 其他的二阶段数据修复，需要直接修改 livemedal.FindMedalPointMultiple() 里的计算过程
	pointMulti, thresholdMulti := int64(1), int64(1)
	if livemedal.IsSuperFanActive(record.SuperFan) {
		// 超粉亲密度和上限翻倍
		pointMulti++
		thresholdMulti++
	}
	level := livemedal.ParseLevel(record.TPoint)
	threshold := livemedal.LevelThreshold()[level-1]
	threshold *= thresholdMulti
	add := point * pointMulti
	if add >= threshold {
		fmt.Printf("用户 %d 在直播间 %d 勋章亲密度添加后已达到上限\n", userID, roomID)
	}
	pointAdd := min(threshold, point*pointMulti)
	updates := bson.M{
		"$inc": bson.M{
			"point":   pointAdd,
			"t_point": pointAdd,
		},
	}
	var medalAfterUpdate livemedal.LiveMedal
	err = livemedal.Collection().FindOneAndUpdate(ctx, filter, updates,
		options.FindOneAndUpdate().SetReturnDocument(options.After)).
		Decode(&medalAfterUpdate)
	if err != nil {
		return err
	}
	if medalAfterUpdate.Point >= livemedal.MinContribution && medalAfterUpdate.Status == livemedal.StatusPending {
		_, err := livemedal.Collection().UpdateOne(ctx, filter, bson.M{"$set": bson.M{"status": livemedal.StatusOwned}})
		if err != nil {
			return err
		}
	}
	fmt.Printf("用户 %d 在直播间 %d 勋章亲密度 %d => %d\n", userID, roomID, record.Point, medalAfterUpdate.Point)
	return nil
}

type luckyGiftLogs struct {
	ID          int64   `gorm:"column:id;primary_key"`
	FromID      int64   `gorm:"column:from_id"`
	ToID        int64   `gorm:"column:to_id"`
	TotalCoin   int64   `gorm:"column:total_coin"`
	TotalIncome float64 `gorm:"column:total_income"`
}

// 参数秒级时间戳
func findLuckyGiftByTime(startTime, endTime int64) ([]luckyGiftLogs, error) {
	var logs []luckyGiftLogs
	err := transactionlog.ADB().Select("to_id, from_id, SUM(all_coin) AS total_coin, SUM(FLOOR(ROUND(IFNULL((income), 0) * 1000) / 100)) AS total_income").
		Where("confirm_time >= ? AND confirm_time < ?", startTime, endTime).
		Where("status = ? AND type IN (?) AND attr = ?",
			transactionlog.StatusSuccess,
			[]int64{transactionlog.TypeLive, transactionlog.TypeGuildLive},
			transactionlog.AttrLiveLuckyGift).
		Group("to_id, from_id").
		Order("from_id ASC").
		Find(&logs).Error
	if err != nil {
		return nil, err
	}
	return logs, nil
}

// fixLuckyGiftLiveMedals 修复礼物粉丝勋章数据亲密度
func fixLuckyGiftLiveMedals() error {
	fmt.Println("开始修复粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)

	orders, err := findLuckyGiftByTime(fixMedalStartTime, medalFailTime)
	// orders, err := findLuckyGiftByTime(medalFailTime, fixMedalEndTime)
	if err != nil {
		fmt.Printf("查询数据错误 %v\n", err)
		return err
	}
	fmt.Printf("共计 %d 条数据\n", len(orders))
	for i := range orders {
		r, err := findRoomByCreatorID(orders[i].ToID)
		if err != nil {
			fmt.Printf("用户: %d, 主播 %d, 处理失败 %v\n", orders[i].FromID, orders[i].ToID, err)
			continue
		}
		if r == nil || r.Medal == nil {
			continue
		}
		add := int64(orders[i].TotalIncome) - orders[i].TotalCoin
		if add <= 0 {
			fmt.Printf("用户: %d 在直播间 %d, 多加了亲密度 %d\n", orders[i].FromID, r.RoomID, -add)
			continue
		}
		err = addPoint(orders[i].FromID, r.RoomID, add)
		if err != nil {
			fmt.Printf("用户: %d 在直播间 %d, 处理失败 %v\n", orders[i].FromID, r.RoomID, err)
			continue
		}
		num++
	}

	fmt.Printf("共成功修复 %d 条数据共用时 %s\n", num, time.Since(now))
	fmt.Println("修复粉丝勋章数据完成")
	return nil
}
