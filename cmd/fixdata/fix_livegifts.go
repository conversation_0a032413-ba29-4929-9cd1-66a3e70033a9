package fixdata

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func getTransactionsByTime(startTime, endTime int64) ([]transactionlog.TransactionLog, error) {
	var logs []transactionlog.TransactionLog
	err := transactionlog.ADB().
		Where("confirm_time >= ? AND confirm_time < ?", startTime, endTime).
		Where("status = ? AND type IN (?) AND gift_id IN (?) AND attr = ?",
			transactionlog.StatusSuccess,
			[]int64{transactionlog.TypeLive, transactionlog.TypeGuildLive},
			[]int64{livemedal.ObtainMedalGiftID, livemedal.ObtainMedalHalfPriceGiftID},
			transactionlog.AttrCommon).
		Where("num = 1").
		Order("confirm_time ASC, id ASC").
		Find(&logs).Error
	if err != nil {
		return nil, err
	}
	return logs, nil
}

func fixLiveGiftsData(list []transactionlog.TransactionLog) (int, error) {
	creatorIDs := make([]int64, 0, len(list))
	for i := range list {
		creatorIDs = append(creatorIDs, list[i].ToID)
	}
	rooms, err := room.FindAll(bson.M{"creator_id": bson.M{"$in": goutil.Uniq(creatorIDs)}})
	if err != nil {
		return 0, err
	}
	roomMap := goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Room)

	inserts := make([]interface{}, 0, len(list))
	for i := range list {
		r := roomMap[list[i].ToID]
		if r == nil {
			continue
		}
		lg, err := livegifts.FindOne(
			bson.M{"room_id": r.RoomID,
				"user_id":         list[i].FromID,
				"gift_id":         list[i].GiftID,
				"transaction_ids": list[i].ID,
			})
		if err != nil {
			return 0, err
		}
		if lg != nil {
			// 数据已存在，跳过
			continue
		}
		sentTime := time.Unix(list[i].ConfirmTime, 0)
		inserts = append(inserts, livegifts.LiveGift{
			RoomOID:        r.OID,
			RoomID:         r.RoomID,
			UserID:         list[i].FromID,
			GiftID:         list[i].GiftID,
			GiftType:       gift.TypeNormal,
			GiftPrice:      list[i].AllCoin,
			GiftNum:        list[i].Num,
			GiftAttr:       0,
			Price:          list[i].AllCoin,
			TransactionIDs: []int64{list[i].ID},
			SentTime:       sentTime,
			UpdatedTime:    sentTime,
			ComboEndTime:   sentTime,
			OpenStatus:     livegifts.OpenStatusOpen, // 假设都在开播时赠送
		})
	}
	if len(inserts) == 0 {
		return 0, nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	insertRes, err := livegifts.Collection().InsertMany(ctx, inserts)
	if err != nil {
		return 0, nil
	}
	return len(insertRes.InsertedIDs), err
}

// fixLiveGift 修复一键获取粉丝勋章数据
func fixLiveGift() error {
	fmt.Println("开始修复一键获取粉丝勋章数据")
	var (
		num int
		now = goutil.TimeNow()
	)
	// 处理 2023-03-09 00:00:00 到 2023-10-26 00:00:00 的数据
	for startTime := int64(1678291200); startTime < 1698249600; startTime += util.SecondOneDay {
		fmt.Printf("已处理至 %s, 插入 %d 条数据, 共用时 %s\r", time.Unix(startTime, 0).Format(util.TimeFormatYMDHMS), num, time.Since(now))
		data, err := getTransactionsByTime(startTime, startTime+util.SecondOneDay)
		if err != nil {
			fmt.Printf("已处理至 %d，%v\n", startTime, err)
			return err
		}
		if len(data) == 0 {
			continue
		}

		insertNum, err := fixLiveGiftsData(data)
		if err != nil {
			fmt.Printf("已处理至 %d，%v\n", startTime, err)
			return err
		}
		num += insertNum
	}
	fmt.Printf("共插入 %d 条数据，共用时 %s\n", num, time.Since(now))
	fmt.Println("修复一键获取粉丝勋章数据完成")
	return nil
}
