package fixdata

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
)

func TestGetDataByLastID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	date, err := getDataByLastID(0)
	require.NoError(err)
	assert.Greater(len(date), 0)

	date, err = getDataByLastID(10000)
	require.NoError(err)
	assert.Equal(0, len(date))
}

func TestActionFixWishes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := fixWishes()
	require.NoError(err)

	var after []livetxnorder.LiveTxnOrder
	err = service.LiveDB.Find(&after, "id IN (?)", []int64{14, 15}).Error
	require.NoError(err)
	require.Len(after, 2)
	assert.EqualValues(10, after[0].Price)
	assert.EqualValues(100, after[1].Price)
	assert.EqualValues(&livetxnorder.MoreInfo{
		Num:        1,
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
	}, after[0].More)
	assert.EqualValues(&livetxnorder.MoreInfo{
		Num:        10,
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
	}, after[1].More)
}

func TestFixWishesData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before := []livetxnorder.LiveTxnOrder{
		{ID: 12, TID: 1},
		{ID: 13, TID: 2},
	}
	err := fixWishesData(before)
	require.NoError(err)

	var after []livetxnorder.LiveTxnOrder
	err = service.LiveDB.Find(&after, "id IN (?)", []int64{12, 13}).Error
	require.NoError(err)
	require.Equal(2, len(after))
	assert.EqualValues(10, after[0].Price)
	assert.EqualValues(100, after[1].Price)
	assert.EqualValues(&livetxnorder.MoreInfo{
		Num:        1,
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
	}, after[0].More)
	assert.EqualValues(&livetxnorder.MoreInfo{
		Num:        10,
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
	}, after[1].More)
}
