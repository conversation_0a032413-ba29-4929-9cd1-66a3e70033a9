package fixdata

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testFromUserID       int64 = 12345
	testToUserID         int64 = 10
	testTransactionID    int64 = 4
	testNewTransactionID int64 = 5
)

func TestGetTransactionsByTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data, err := getTransactionsByTime(0, goutil.TimeNow().Unix())
	require.NoError(err)
	assert.Greater(len(data), 0)

	data, err = getTransactionsByTime(-1, 0)
	require.NoError(err)
	assert.Equal(0, len(data))
}

func TestFixLiveGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID, err := room.FindRoomID(testToUserID)
	require.NoError(err)
	require.NotEmpty(roomID)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livegifts.Collection().DeleteMany(ctx, bson.M{"user_id": testFromUserID, "room_id": roomID})
	require.NoError(err)
	lg := livegifts.LiveGift{
		RoomID:         roomID,
		UserID:         testFromUserID,
		GiftID:         livemedal.ObtainMedalGiftID,
		TransactionIDs: []int64{12345, testTransactionID, 99999},
		SentTime:       goutil.TimeNow(),
	}
	_, err = livegifts.Collection().InsertOne(ctx, lg)
	require.NoError(err)

	require.NoError(fixLiveGift())
	col, err := livegifts.Collection().Find(ctx, bson.M{"user_id": testFromUserID, "room_id": roomID},
		options.Find().SetSort(bson.M{"sent_time": 1}))
	require.NoError(err)
	var res []livegifts.LiveGift
	err = col.All(ctx, &res)
	require.NoError(err)
	require.Equal(2, len(res))
	assert.Equal([]int64{testNewTransactionID}, res[0].TransactionIDs)
	assert.Equal(lg.TransactionIDs, res[1].TransactionIDs)
}
