package consumer

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestInitService(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	skipInit = false
	service.DB.Close()
	service.LiveDB.Close()
	service.PayDB.Close()
	service.NewADB.Close()
	defer func() {
		initTest()
	}()

	service.DB = nil
	require.NoError(initService())
	assert.NotNil(service.DB)
}
