package consumer

import (
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
)

var skipInit bool

func initService() (err error) {
	if skipInit {
		return nil
	}
	conf := config.Conf.Service
	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	service.LiveDB, err = servicedb.InitDatabase(&conf.LiveDB)
	if err != nil {
		return
	}
	service.PayDB, err = servicedb.InitDatabase(&conf.PayDB)
	if err != nil {
		return
	}

	service.MongoDB, err = mongodb.NewMongoDB(&conf.MongoDB)
	if err != nil {
		return
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}
	// service.IMRedis, err = serviceredis.NewRedisClient(&conf.IMRedis)
	// if err != nil {
	// 	return
	// }
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return
	}

	// 默认只初始化 DatabusSub
	service.DatabusSub = databus.New(&conf.Databus.Sub)
	service.DatabusDelaySub = databus.New(&conf.DatabusDelay.Sub)
	service.DatabusDelayPub = databus.New(&conf.DatabusDelay.Pub)

	service.Storage = storage.NewClient(conf.Storage)
	// service.Vod = vod.NewVodClient(&conf.Vod)

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return
	}
	service.PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return
	}
	service.SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return
	}

	service.BiliLive = bililive.NewClient(conf.BiliLive)

	service.AfterInit()
	return nil
}
