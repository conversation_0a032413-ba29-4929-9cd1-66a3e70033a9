package consumer

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/pprof"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/cmd/base"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/controllers/webrpc"
	"github.com/MiaoSiLa/live-service/internal/controllers/health"
	medaldatabus "github.com/MiaoSiLa/live-service/internal/databus/medal"
	luckyboxdatabus "github.com/MiaoSiLa/live-service/models/livedb/liveluckybox/databus"
	multiconnectdatabus "github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect/databus"
	luckybagdatabus "github.com/MiaoSiLa/live-service/models/livedb/luckybag/databus"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	rpdatabus "github.com/MiaoSiLa/live-service/models/mongodb/redpacket/databus"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
)

// Operator 处理消息的 operator
type Operator func(*databus.Message)

// Command cmd
type Command struct {
	Operators []Operator
}

// NewCommand new Command
func NewCommand() *Command {
	s, f := debugMessage()
	return &Command{
		Operators: []Operator{
			s,
			room.GashaponOperator(),
			utils.LiveSpendOperator(),
			livepk.PKMatchingStartOperator(),
			livepk.DelayPKMatchingTimeoutOperator(),
			livepk.DelayPKFightingFinishOperator(),
			livepk.DelayPKPunishFinishOperator(),
			webrpc.DelayActivityDatabusOperator(),
			rpdatabus.DelayRedPacketSetGrabbingOperator(),
			rpdatabus.DelayRedPacketExpiredOperator(),
			medaldatabus.ChangeOperator(),
			luckybagdatabus.DelayDrawLuckyBagOperator(),
			luckyboxdatabus.LuckyBoxOperator(),
			multiconnectdatabus.DelayMultiConnectMatchTimeoutOperator(),
			f,
		},
	}
}

// Name name
func (*Command) Name() string {
	return "consumer"
}

var osCh = make(chan os.Signal, 1)

// Run run
func (c *Command) Run(conf *config.Config) error {
	err := initService()
	if err != nil {
		return err
	}
	defer service.DatabusSub.Close()
	defer service.DatabusDelaySub.Close()

	srv := newServer(conf)

	eg, ctx := errgroup.WithContext(context.Background())
	// stop 不阻塞代码，后面通过 eg.Wait() 控制
	stop := make(chan int, 1)
	eg.Go(func() (err error) {
		defer func() {
			r := recover()
			if r != nil {
				// TODO: 堆栈信息？
				logger.Error(r)
			}
			err = fmt.Errorf("panic: %v", r)
		}()
		msgCh := service.DatabusSub.Messages()
		delayMsgCh := service.DatabusDelaySub.Messages()
		// TODO: 需要支持出错了之后重试
		handleMessage := func(msg *databus.Message) {
			for _, o := range c.Operators {
				o(msg)
			}
			msg.Commit()
		}
		for {
			select {
			case <-stop:
				return nil
			case msg := <-msgCh:
				handleMessage(msg)
			case msg := <-delayMsgCh:
				handleMessage(msg)
			}
		}
	})

	eg.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})

	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
	case <-osCh:
	}
	close(stop)
	shutdownServer(srv)
	err = eg.Wait()
	return err
}

func debugMessage() (Operator, Operator) {
	return func(m *databus.Message) {
			logger.WithFields(logger.Fields{
				"key":   m.Key,
				"value": string(m.Value),
			}).Debug("quest start")
		}, func(m *databus.Message) {
			logger.WithFields(logger.Fields{
				"key":   m.Key,
				"value": string(m.Value),
			}).Debug("quest finish")
		}
}

func newServer(conf *config.Config) *http.Server {
	r := base.NewEngine()
	pprof.Register(r)
	h := health.Handler()
	h.Mount(r)

	srv := &http.Server{
		Addr: fmt.Sprintf("%s:%d", conf.Web.Address,
			conf.Web.Port),
		Handler: r}
	return srv
}

func shutdownServer(srv *http.Server) {
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown http server failed: %v", err)
	}
}
