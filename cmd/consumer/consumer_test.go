package consumer

import (
	"encoding/json"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
)

func initTest() {
	service.InitTest(true)
	service.InitTestDatabusSub()
	skipInit = true
}

func TestMain(m *testing.M) {
	initTest()

	m.Run()
}

func TestCommand(t *testing.T) {
	assert := assert.New(t)

	c := NewCommand()
	assert.Len(c.Operators, 15)
	assert.Equal("consumer", c.Name())
}

func TestCommandRun(t *testing.T) {
	assert := assert.New(t)

	defer service.DatabusSub.ClearDebugPubMsgs()
	c := NewCommand()
	count := 0
	before, after := debugMessage()
	c.Operators = []Operator{
		before,
		func(m *databus.Message) {
			count++
		},
		after,
	}
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		// TODO: msg.Commit() 目前肯定会 panic
		_ = c.Run(config.Conf)
	}()
	service.DatabusSub.TestPush(&databus.Message{
		Key:   "test_key",
		Value: json.RawMessage("test_value"),
	})
	<-time.After(time.Second)
	assert.Equal(1, count)
	osCh <- os.Interrupt
	wg.Wait()
}
