package web

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/cmd/base"
	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/apiv2"
	"github.com/MiaoSiLa/live-service/controllers/bvclive"
	"github.com/MiaoSiLa/live-service/controllers/openapi"
	"github.com/MiaoSiLa/live-service/controllers/webrpc"
	"github.com/MiaoSiLa/live-service/internal/controllers/health"
	"github.com/MiaoSiLa/live-service/luavm"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// Command server
type Command struct {
}

// Name command's name
func (c *Command) Name() string {
	return "web"
}

// Run command
func (c *Command) Run(conf *config.Config) error {
	err := service.Init(&config.Conf.Service)
	if err != nil {
		return err
	}
	handler.SetMode(config.Conf.HTTP.Mode)

	// TODO: check lua config
	// defer shutdown lua vm
	defer luavm.Pool.Shutdown()

	g, ctx := errgroup.WithContext(context.Background())
	srv := newServer()
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})
	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
	case <-osCh:
	}
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown server failed: %v", err)
		return err
	}
	return g.Wait()
}

type mounter interface {
	Mount(r *gin.Engine)
}

func mount(r *gin.Engine) {
	pprof.Register(r)

	handlers := []mounter{
		apiv2.Handler(),
		apiv2.HandlerV2(),
		webrpc.Handler(),
		webrpc.HandlerV2(),
		bvclive.Handler(),
		openapi.HandlerV2(),
		health.Handler(),
	}
	for _, h := range handlers {
		h.Mount(r)
	}
}

func newServer() *http.Server {
	router := base.NewEngine()
	mount(router)
	srv := &http.Server{
		Addr: fmt.Sprintf("%s:%d", config.Conf.Web.Address,
			config.Conf.Web.Port),
		Handler: router}
	return srv
}
