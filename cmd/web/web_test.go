package web

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestCommandTest(t *testing.T) {
	var c Command
	assert.Equal(t, "web", c.Name())
}

func TestMount(t *testing.T) {
	assert := assert.New(t)
	handler.SetMode(handler.TestMode)
	resp := httptest.NewRecorder()
	c, r := gin.CreateTestContext(resp)
	mount(r)
	c.Request, _ = http.NewRequest(http.MethodGet, "/health", nil)
	r.ServeHTTP(resp, c.Request)
	require.Equal(t, 200, resp.Code)
	raw := resp.Body.Bytes()
	var body struct {
		Code int    `json:"code"`
		Info string `json:"info"`
	}
	require.NoError(t, json.Unmarshal(raw, &body))
	assert.Equal(0, body.Code)
}
