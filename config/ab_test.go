package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v2"
)

func TestGetAB(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	Conf.AB = make(map[string]interface{})
	err := yaml.Unmarshal([]byte("bool: true\nfloat64: 0.5\nint: 1\nint64: 2\nstring: string"), Conf.AB)
	require.NoError(err)
	defer func() {
		Conf.AB = make(map[string]interface{})
	}()
	t.Run("int64", func(t *testing.T) {
		var numInt64 int64
		GetAB("int64", &numInt64)
		assert.Equal(int64(2), numInt64)

		Conf.AB["int64"] = int64(3)
		GetAB("int64", &numInt64)
		assert.Equal(int64(3), numInt64)

		assert.PanicsWithValue("type conversion error", func() {
			Conf.AB["int64"] = int32(3)
			var numPanic int64
			GetAB("int64", &numPanic)
		})
	})
	t.Run("int", func(t *testing.T) {
		var numInt int
		GetAB("int", &numInt)
		assert.Equal(1, numInt)
	})
	t.Run("double", func(t *testing.T) {
		var double float64
		GetAB("float64", &double)
		assert.Equal(0.5, double)
	})
	t.Run("bool", func(t *testing.T) {
		var ok bool
		GetAB("bool", &ok)
		assert.True(ok)
	})
	t.Run("string", func(t *testing.T) {
		var s string
		GetAB("string", &s)
		assert.Equal("string", s)
	})
	t.Run("chan int", func(t *testing.T) {
		var ch chan int
		assert.NotPanics(func() { GetAB("not_exists", ch) })
	})
}
