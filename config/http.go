package config

// SectionHTTP is sub section of config
type SectionHTTP struct {
	Mode                string            `yaml:"mode"`
	RPC<PERSON>ey              string            `yaml:"rpc_key"`
	BiliAppKey          map[string]string `yaml:"bili_app_key"`
	DisableAPISign      bool              `yaml:"disable_api_sign"`
	APISignKey          string            `yaml:"api_sign_key"`
	OpenAPIAppKeySecret map[string]string `yaml:"openapi_app_key_secret"`
	SessionCookieName   string            `yaml:"session_cookie_name"`
	SessionPrefix       string            `yaml:"session_prefix"`
	AccIDPrefix         string            `yaml:"accid_prefix"`

	CSRFAllowTopDomains []string `yaml:"csrf_allow_top_domains"`
}

// SectionWeb config for mode web
type SectionWeb struct {
	Enabled      bool   `yaml:"enabled"`
	Address      string `yaml:"address"`
	Port         int    `yaml:"port"`
	CheckChannel bool   `yaml:"check_channel"`
	HornOpen     bool   `yaml:"horn_open"`
}

// SectionIM config for mode im
type SectionIM struct {
	Address                string `yaml:"address"`
	Port                   int    `yaml:"port"`
	EnableRPC              bool   `yaml:"enable_rpc"`
	RPCAddress             string `yaml:"rpc_address"`
	RPCPort                int    `yaml:"rpc_port"`
	AllowTestUser          bool   `yaml:"allow_test_user"`
	PubsubSize             int    `yaml:"pubsub_size"`
	JoinSignHmacKey        string `yaml:"join_sign_hmac_key"`
	JoinUUIDValidStartTime int64  `yaml:"join_uuid_valid_start_time"`
	DisableIMHighRiskCheck bool   `yaml:"disable_im_high_risk_check"`
}
