package config

import (
	"github.com/MiaoSiLa/live-service/service/biliapi"
	"github.com/MiaoSiLa/live-service/service/cdn/aliyun"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/live-service/service/cdn/ksyun"
	"github.com/MiaoSiLa/live-service/service/cdn/netease"
	"github.com/MiaoSiLa/live-service/service/netease/vod"
	"github.com/MiaoSiLa/live-service/service/tencentcos"
	"github.com/MiaoSiLa/live-service/service/upos"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
)

// SectionService is service config
type SectionService struct {
	DB     servicedb.Config `yaml:"db"`
	LiveDB servicedb.Config `yaml:"live_db"`
	PayDB  servicedb.Config `yaml:"paydb"`
	NewADB servicedb.Config `yaml:"newadb"`
	LogDB  servicedb.Config `yaml:"log_db"`

	MongoDB mongodb.Config `yaml:"mongodb"`

	Redis    serviceredis.Config `yaml:"redis"`
	IMRedis  serviceredis.Config `yaml:"im_redis"`
	LRURedis serviceredis.Config `yaml:"lru_redis"`

	Databus      DatabusPairs `yaml:"databus"`
	DatabusDelay DatabusPairs `yaml:"databus_delay"`
	DatabusLog   DatabusPairs `yaml:"databus_log"`

	TencentCOS tencentcos.SectionTencentCOS `yaml:"tencent_cos"`

	Storage storage.Config `yaml:"storage"`
	Upos    upos.Config    `yaml:"upos"`
	Vod     vod.Config     `yaml:"vod"`

	// rpc client
	MRPC        mrpc.Config        `yaml:"mrpc"`
	PushService pushservice.Config `yaml:"pushservice"`

	// live cdn
	Agora       SectionAgora    `yaml:"agora"`
	NeteaseLive netease.Config  `yaml:"netease_live"`
	AliyunLive  aliyun.Config   `yaml:"aliyun_live"`
	KsyunLive   ksyun.Config    `yaml:"ksyun_live"`
	BvcLive     bvc.Config      `yaml:"bvc_live"`
	BiliLive    bililive.Config `yaml:"bili_live"`
	BiliAPI     biliapi.Config  `yaml:"bili_api"`

	// others
	Captcha *captcha.Config `yaml:"captcha"`
	Upload  upload.Config   `yaml:"upload"`
}

// DatabusPairs databus 对
type DatabusPairs struct {
	Pub databus.Config `yaml:"pub"`
	Sub databus.Config `yaml:"sub"`
}

func (dp *DatabusPairs) afterLoad() {
	dp.Pub.Action = "pub"
	dp.Sub.Action = "sub"
}

// AfterLoad 后处理
func (s *SectionService) AfterLoad() error {
	s.Databus.afterLoad()
	s.DatabusDelay.afterLoad()
	s.DatabusLog.afterLoad()

	return nil
}
