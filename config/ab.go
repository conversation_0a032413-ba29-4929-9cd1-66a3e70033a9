package config

// GetAB 获取临时变量配置
func GetAB(key string, v interface{}) {
	c, ok := Conf.AB[key]
	if !ok {
		return
	}
	switch v := v.(type) {
	case *bool:
		*v = c.(bool)
	case *float64:
		*v = c.(float64)
	case *int64:
		switch ct := c.(type) {
		case int:
			*v = int64(ct)
		case int64:
			*v = ct
		default:
			panic("type conversion error")
		}
	case *int:
		*v = c.(int)
	case *string:
		*v = c.(string)
	}
}
