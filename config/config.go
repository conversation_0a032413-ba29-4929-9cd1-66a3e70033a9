package config

import (
	"errors"
	"net/url"
	"os"
	"runtime"

	yaml "gopkg.in/yaml.v2"

	"github.com/MiaoSiLa/missevan-go/config/configsync"
	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger/log"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// Conf config for live-service
var Conf *Config

// KeyPrefix 和 audio-chatroom 共用的键需要前缀
// 和 mongodb 的 prefix 一致
var KeyPrefix string

func init() {
	Conf = NewConfig()
}

// BuildDefaultConf is default config setting.
func BuildDefaultConf() Config {
	return *NewConfig()
}

// NewConfig new Config
func NewConfig() *Config {
	conf := &Config{AB: make(map[string]interface{})}

	// Worker
	conf.Worker.WorkerNum = runtime.NumCPU()
	conf.Worker.QueueNum = 20
	conf.Worker.Port = 3014

	// IM
	conf.IM.Port = 3015
	conf.IM.RPCPort = 3011
	conf.IM.EnableRPC = true
	conf.IM.PubsubSize = 10

	// Web
	conf.Web.Port = 3013

	// net
	conf.HTTP.Mode = "release"
	conf.HTTP.RPCKey = "testkey"
	conf.HTTP.APISignKey = "testkey"
	conf.HTTP.OpenAPIAppKeySecret = map[string]string{
		"testkey": "testsecret",
	}
	conf.HTTP.SessionCookieName = "TSESS"
	conf.HTTP.SessionPrefix = "test_"

	// param
	conf.Params.General.RoomTip = `<font color="#FFC525">系统提示：</font><font color="#FFFFFF">猫耳FM直播内容及互动评论须严格遵守直播规范，` +
		`严禁传播违法违规、低俗血暴、吸烟酗酒、造谣诈骗等不良有害信息。如有违规，平台将进行封禁直至永久封停账号哦！注意理性打赏，` +
		`严禁未成年人直播或打赏。请勿轻信平台上各类广告信息，谨防上当受骗。</font>`

	// log
	conf.Log = *log.DefaultConfig

	// service
	defaultDBConfig := func() servicedb.Config {
		return servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: servicedb.DefaultMaxIdleConns(),
			MaxLifeTime:  "10s",
		}
	}
	conf.Service.DB = defaultDBConfig()
	conf.Service.LiveDB = defaultDBConfig()
	conf.Service.PayDB = defaultDBConfig()
	conf.Service.NewADB = defaultDBConfig()
	conf.Service.LogDB = defaultDBConfig()

	conf.Service.Redis = serviceredis.Config{
		Addr:     "127.0.0.1:6379",
		DB:       6,
		PoolSize: serviceredis.DefaultPoolSize(),
	}
	conf.Service.IMRedis = serviceredis.Config{
		Addr:     "127.0.0.1:6379",
		DB:       6,
		PoolSize: serviceredis.DefaultPoolSize(),
	}
	conf.Service.LRURedis = serviceredis.Config{
		Addr:     "127.0.0.1:6379",
		DB:       6,
		PoolSize: serviceredis.DefaultPoolSize(),
	}
	return conf
}

// Config is config structure.
type Config struct {
	AB map[string]interface{} `yaml:"ab"`

	Worker  SectionWorker  `yaml:"worker"`
	IM      SectionIM      `yaml:"im"`
	Web     SectionWeb     `yaml:"web"`
	Log     log.Config     `yaml:"log"`
	HTTP    SectionHTTP    `yaml:"http"`
	Params  SectionParams  `yaml:"params"`
	Service SectionService `yaml:"service"`

	Lua SectionLua `yaml:"lua"`
}

// SectionWorker is sub section of config.
type SectionWorker struct {
	WorkerNum int `yaml:"worker_num"`
	QueueNum  int `yaml:"queue_num"`

	Address string `yaml:"address"`
	Port    int    `yaml:"port"`
}

// SectionLua .
type SectionLua struct {
	InitFile string `yaml:"init_file"`
	// TODO: MaxDoTimes int

	InitScript string

	// readFunc 读取脚本的函数
	// yaml 文件，使用 os.ReadFile
	// 配置中心，使用 configsync.GetFile
	readFunc func(fileName string) ([]byte, error)
}

// AfterLoad after load
func (sl *SectionLua) AfterLoad() error {
	if sl.InitFile != "" {
		if sl.readFunc == nil {
			panic("undefined readFunc")
		}
		luaInitScript, err := sl.readFunc(sl.InitFile)
		if err != nil {
			return err
		}
		sl.InitScript = string(luaInitScript)
	}
	return nil
}

// LoadFromYML load config from yml file
func LoadFromYML(confPath string) error {
	configFile, err := os.ReadFile(confPath)
	if err != nil {
		return err
	}

	err = yaml.Unmarshal(configFile, Conf)
	if err != nil {
		return err
	}
	// TODO: 如果还有额外的地方需要读取文件，则需要把读取的函数作为 AfterLoad 的参数
	Conf.Lua.readFunc = os.ReadFile
	return afterLoad()
}

// LoadInCluster load config from cluster
func LoadInCluster(appName, version string) error {
	err := configsync.InitSync("", appName, version, nil)
	if err != nil {
		return err
	}
	err = configsync.Unmarshal(Conf)
	if err != nil {
		return err
	}
	Conf.Lua.readFunc = configsync.GetFile
	return afterLoad()
}

// errors
var (
	ErrNoDefaultCDN = errors.New("Default CDN scheme is not configured")
	ErrNoPubsubSize = errors.New("im pubsub_size is not configured")
)

func afterLoad() error {
	if Conf.AB == nil {
		Conf.AB = make(map[string]interface{})
	}

	if Conf.IM.PubsubSize <= 0 {
		return ErrNoPubsubSize
	}

	KeyPrefix = Conf.Service.MongoDB.Prefix
	params.URL = Conf.Params.URL.SectionURL

	u, err := url.Parse(params.URL.CDN)
	if err != nil {
		return err
	}
	DefaultCDNScheme = u.Scheme

	if DefaultCDNScheme == "" {
		return ErrNoDefaultCDN
	}
	if _, ok := Conf.Service.Storage[DefaultCDNScheme]; !ok {
		return ErrNoDefaultCDN
	}

	err = Conf.Lua.AfterLoad()
	if err != nil {
		return err
	}

	err = Conf.Service.AfterLoad()
	if err != nil {
		return err
	}

	Conf.Params.Recommended.AfterLoad()

	return nil
}
