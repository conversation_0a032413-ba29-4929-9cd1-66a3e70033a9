//go:build !release
// +build !release

package config

import (
	"github.com/MiaoSiLa/live-service/service/biliapi"
	"github.com/MiaoSiLa/live-service/service/cdn/aliyun"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/live-service/service/cdn/ksyun"
	"github.com/MiaoSiLa/live-service/service/cdn/netease"
	"github.com/MiaoSiLa/live-service/service/liveupload"
	"github.com/MiaoSiLa/live-service/service/netease/vod"
	"github.com/MiaoSiLa/live-service/service/tencentcos"
	"github.com/MiaoSiLa/live-service/service/upos"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
)

// InitTest 构建测试用 config
func InitTest() {
	Conf.Log = *logger.InitTestLog()

	Conf.IM.PubsubSize = 1
	Conf.Web.CheckChannel = true
	Conf.Service.BvcLive = bvc.TestConfig()
	Conf.Service.BiliLive = bililive.TestConfig()

	Conf.Service.BiliAPI = biliapi.Config{
		URL:       "http://uat-api.bilibili.com",
		AppKey:    "testkey",
		AppSecret: "app_secret",
		Enable:    true,
	}

	Conf.Params.General = SectionGeneral{
		RoomTip: `<font color="#FFC525">系统提示：</font><font color="#FFFFFF">猫耳FM直播内容及互动评论须严格遵守直播规范</font>`,
	}
	// live url
	Conf.Params.LiveURL = SectionLiveURL{
		GuildApplyContract: "https://fm.example.com/guild/apply/%d",
		WebSocket:          []string{"wss://fm.example.com:3016/ws?room_id=${room_id}"},
		ActivityWebSocket:  []string{"wss://fm.example.com:3016/ws/activity?room_id=${room_id}"},
	}

	// guild agreement
	Conf.Params.GuildAgreement.Title = "猫耳FM公会主播入驻服务协议"
	Conf.Params.GuildAgreement.ContentHTML = "<p><b>猫耳FM公会主播入驻服务协议。</b></p>"
	// live agreement
	Conf.Params.LiveAgreement.Title = "猫耳FM主播入驻服务协议"
	Conf.Params.LiveAgreement.ContentHTML = "<p><b>猫耳FM主播入驻服务协议。</b></p>"
	// medal
	Conf.Params.MedalParams.FanRule = "https://link.uat.missevan.com/fm/fans-system-guide"
	Conf.Params.MedalParams.SuperFanRule = "https://link.uat.missevan.com/fm/super-fans-guide"
	Conf.Params.MedalParams.CostPoint = 500
	Conf.Params.MedalParams.ContentHTML = "<p><b>温馨提示。</b></p>'"
	// 加密
	Conf.Params.Security.SensitiveInformationKey = "test_sensitive_information_key"
	Conf.Params.Security.SensitiveFixedIVKey = "testiv"

	Conf.Params.NobleParams = SectionNobleParams{
		NobleDetailsURL:              "https://fm.example.com/noble/privilege",
		InvisibleIcon:                "oss://avatars/invisible.png",
		DefaultCardFrame:             "oss://gifts/cardframes/000.png",
		RecommendWebTopIcon:          "oss://gifts/recommend/top/007.png",
		RecommendTopIcon:             "oss://gifts/recommend/top/007-new.png",
		RecommendWebHighnessTopIcon:  "oss://live/recommend/highness/top/001-web.png",
		RecommendHighnessTopIcon:     "oss://live/recommend/highness/top/001.png",
		RecommendListIcon:            "oss://gifts/recommend/list/007.png",
		RecommendHighnessListIcon:    "oss://live/recommend/highness/list/001.png",
		RecommendWebFrame:            "oss://live/avatarframes/noble/007-recommend.png",
		RecommendWebHighnessFrame:    "oss://live/avatarframes/noble/highness/001-recommend.png",
		RecommendAvatarFrame:         "oss://gifts/recommendframes/007_3_177_3_21.png",
		RecommendHighnessAvatarFrame: "oss://live/recommendframes/highness/001_3_177_3_21.png",
	}
	Conf.Params.Recommended = SectionRecommended{
		HypnosisDisableTimeRange: [][2]string{{"", "24:00:00"}},
	}
	// 互动
	Conf.Params.Interaction = SectionInteraction{
		VoteHelpURL:      "https://fm.example.com/vote/help",
		VoteAgreementURL: "https://fm.example.com/vote/agreement",
	}
	Conf.Params.UserInfo = SectionUserInfo{
		DefaultCardFrame: DefaultCardFrame{
			ImageURL: "oss://live/cardframes/000_690_60_216_60.png",
			TextColorItem: CardFrameTextColorItem{
				Username:        "#FFFFFF",
				Introduction:    "#FFFFFF",
				ReportAndManage: "#FFFFFF",
			},
		},
		DefaultMedalFrameURL: "oss://live/medalframes/normal/level${level}_0_9_0_54${ext}",
	}
	Conf.Params.LiveFeed = SectionLiveFeed{
		PositionForceInsertionSwitch: true,
	}

	Conf.Lua.InitScript = `
local os = require('os')

local logger = require('logger')
local crypto = require('crypto')
local json = require('json')
local redis = require('redis')
local regexp = require('regexp')
local mongo = require('mongo')

function run_action(ctx)
{faas}
  return nil
end

function run_widget(ctx)
  if not ctx.widget_func then
    return nil
  end
  return ctx.widget_func(ctx)
end
`

	Conf.HTTP.SessionCookieName = "TEST_SESS"
	Conf.HTTP.SessionPrefix = "test:sess"
	Conf.HTTP.AccIDPrefix = "test"

	Conf.Service.DB = servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		Name:         "app_missevan",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
		User:         "root",
		Pass:         "rootmysql",
	}
	Conf.Service.LiveDB = servicedb.Config{
		// Host: "test01.bj1.maoer.co",
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		Name:         "missevan_live",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
		User:         "root",
		Pass:         "rootmysql",
	}
	Conf.Service.PayDB = servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		Name:         "missevan_pay",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
		User:         "root",
		Pass:         "rootmysql",
	}
	Conf.Service.LogDB = servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		Name:         "app_missevan_log",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
		User:         "root",
		Pass:         "rootmysql",
	}
	Conf.Service.NewADB = Conf.Service.PayDB
	Conf.Service.MongoDB = mongodb.TestConfig("chatroom")
	Conf.Service.Redis.Addr = "redis.srv.maoer.co:6379"
	Conf.Service.Redis.DB = 506
	Conf.Service.IMRedis.Addr = "redis.srv.maoer.co:6379"
	Conf.Service.IMRedis.DB = 606
	Conf.Service.LRURedis.Addr = "redis.srv.maoer.co:6379"
	Conf.Service.LRURedis.DB = 706

	Conf.Service.TencentCOS = tencentcos.SectionTencentCOS{
		AccessKeyID:     "test_key",
		AccessKeySecret: "test_secert",
		BucketURL:       "http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/",
	}

	Conf.Service.Storage = map[string]entryconfig.Config{
		"oss": {
			Type:            "oss",
			AccessKeyID:     "LTAIsNW7Hxzgnxu2",
			AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
			Bucket:          "missevan-test",
			Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
			PublicURL:       "https://static-test.missevan.com/",
		},
		"archive": {
			Type:            "oss",
			AccessKeyID:     "LTAIsNW7Hxzgnxu2",
			AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
			Bucket:          "missevan-test",
			Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
			PublicURL:       "https://static-test.maoercdn.com/",
		},
		"sound": {
			Type:            "oss",
			AccessKeyID:     "LTAIsNW7Hxzgnxu2",
			AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
			Bucket:          "missevan-test",
			Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
			// 从配置的 PublicURL 和 storage 的 entry 都是唯一对应的
			// 以保证 storage.Format 不会出现同一种路径返回两种结果的情况
			PublicURL: "https://static%2Dtest.maoercdn.com/",
		},
	}
	Conf.Service.Upos = upos.Config{
		PreuploadEndpoint: "http://uat-member.bilibili.com",

		Bucket:  "mefmboss",
		Profile: "mefm/upclone",
		Token:   "ba1bea1e70329dc72c53bc43424920f9",
	}
	Conf.Service.Vod = vod.Config{
		AppKey:    "73c5195d486849bdaa7137054bbea07a",
		AppSecret: "4176d44f4b6b462993d2da6d2b5a0a7a",
	}

	Conf.HTTP.APISignKey = "testkey"
	Conf.Service.MRPC = mrpc.TestConfig()
	Conf.Service.PushService = pushservice.Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	}
	// CDN
	Conf.Service.NeteaseLive = netease.TestConfig()
	Conf.Service.AliyunLive = aliyun.TestConfig()
	Conf.Service.KsyunLive = ksyun.TestConfig()
	Conf.Service.Captcha = captcha.BuildDefaultConfig()
	// upload
	Conf.Service.Upload = liveupload.TestUploadConfig()

	// 初始化 Params
	Conf.Params.URL.Main = "https://www.missevan.com/"
	Conf.Params.URL.Live = "https://fm.uat.missevan.com/"
	Conf.Params.URL.CDN = "oss://"
	Conf.Params.URL.AvatarURL = "oss://avatars/"
	Conf.Params.URL.ProfileURL = "oss://profile/"
	Conf.Params.URL.DefaultIconURL = "oss://avatars/icon01.png"
	Conf.Params.LiveURL.WebRoom = "https://fm.uat.missevan.com/live/%d"
	Conf.Params.LiveURL.AppRoom = "missevan://live/%d"
	Conf.Params.LiveURL.UserRankRule = "https://link.uat.missevan.com/help/fm-ranks"
	Conf.Params.PK.EntryIcon = "oss://live/pk/entrance.png"
	Conf.Params.PK.PeakLimitStartTime = "20:00:00"
	Conf.Params.PK.PeakLimitEndTime = "22:00:00"
	Conf.Params.GuildRate.GuildRateDetailURL = "https://fm.uat.missevan.com/user/center/applymentratedetail"
	Conf.Params.GuildOperate.GuildPermissionSwitch = false
	Conf.Params.GuildOperate.CreatorPermissionSwitch = false
	Conf.Params.RedPacket.ExpireDuration = "24h"
	Conf.Params.RedPacket.Rule = "https://link.uat.missevan.com/fm/gift-redpacket-guide"
	Conf.Params.RedPacket.HighRiskScore = 85
	Conf.Params.ChannelCallback.AppKey = "test_appkey"
	Conf.Params.ChannelCallback.AppSecret = "test_appsecret"
	Conf.Params.Sticker.RoomIconFrame = "oss://live/stickers/package/room.png"
	Conf.Params.Sticker.UserIconFrame = "oss://live/stickers/package/user.png"
	Conf.Params.Luckybag.DramalistURL = "https://fm.uat.missevan.com/user/luckybag/dramalist"

	err := afterLoad()
	if err != nil {
		logger.Fatal(err)
	}
}
