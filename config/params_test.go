package config

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestSectionRecommendedAfterLoad(t *testing.T) {
	assert := assert.New(t)
	var sr SectionRecommended
	assert.NotPanics(func() { sr.AfterLoad() })
	assert.NotNil(sr.HypnosisDisableClocks)
	sr.HypnosisDisableTimeRange = [][2]string{{"", "24:00:00"}}
	assert.NotPanics(func() { sr.AfterLoad() })
	assert.Len(sr.HypnosisDisableClocks, 1)
	assert.Equal(util.Clock{Hour: 24}, sr.HypnosisDisableClocks[0][1])
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(SectionGuildOperate{}, "guild_permission_switch", "creator_permission_switch")
	kc.Check(SectionRedPacket{}, "expire_duration", "rule", "high_risk_score")
}
