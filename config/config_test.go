package config

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestBuildDefaultConf(t *testing.T) {
	assert := assert.New(t)
	conf := BuildDefaultConf()
	assert.Equal(3015, conf.IM.Port)
}

func TestLoadFromYML(t *testing.T) {
	oldConf := *Conf
	redisPrefix := KeyPrefix
	defer func() {
		Conf = &oldConf
		KeyPrefix = redisPrefix
	}()

	assert := assert.New(t)
	KeyPrefix = "123"
	err := LoadFromYML(`../config.example.yml`)
	assert.NoError(err)
	assert.Equal("xxxxxx", Conf.Service.Vod.AppSecret)
	assert.Empty(KeyPrefix)
	assert.Equal(10*time.Second, Conf.Service.MongoDB.Timeout)
	assert.Error(LoadFromYML("../config.123"))
	assert.Error(LoadFromYML("./config.go"))
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Conf, "ab", "worker", "im", "web", "log", "http", "params", "service", "lua")
	kc.Check(Conf.Worker, "worker_num", "queue_num", "address", "port")
	kc.Check(Conf.IM, "address", "port", "enable_rpc", "rpc_address",
		"rpc_port", "allow_test_user", "pubsub_size", "join_sign_hmac_key", "join_uuid_valid_start_time",
		"disable_im_high_risk_check")
	kc.Check(Conf.Web, "enabled", "address", "port", "check_channel", "horn_open")
	kc.Check(Conf.HTTP, "mode", "rpc_key", "bili_app_key", "disable_api_sign", "api_sign_key", "openapi_app_key_secret", "session_cookie_name",
		"session_prefix", "accid_prefix", "csrf_allow_top_domains")
	kc.Check(Conf.Service, "db", "live_db", "newadb", "paydb", "log_db",
		"mongodb",
		"redis", "im_redis", "lru_redis",
		"databus", "databus_delay", "databus_log",
		"tencent_cos",
		"storage", "upos", "vod", "mrpc", "pushservice",
		"agora", "netease_live", "aliyun_live", "ksyun_live", "bvc_live", "bili_live", "bili_api", "captcha", "upload")
	kc.Check(Conf.Service.Databus, "pub", "sub")
	kc.Check(Conf.Params, "url", "general", "noble", "recommended", "security", "interaction", "medal",
		"guild_agreement", "live_url", "pk", "gift_wall", "live_agreement",
		"disable_open", "userinfo", "guild_notice_emails", "guild_rate", "guild_operate", "red_packet", "channel_callback",
		"sticker", "luckybag", "notification", "live_feed")
	kc.Check(Conf.Params.URL, "live")
	kc.Check(Conf.Params.General, "room_tip", "feedback_key")
	kc.Check(Conf.Params.NobleParams, "noble_details_url", "noble_guide_url", "custom_noble_gift_url", "buy_noble_url", "invisible_icon", "default_card_frame",
		"recommend_web_top_icon", "recommend_top_icon",
		"recommend_web_highness_top_icon", "recommend_highness_top_icon",
		"recommend_list_icon", "recommend_highness_list_icon",
		"recommend_web_frame", "recommend_web_highness_frame",
		"recommend_avatar_frame", "recommend_highness_avatar_frame",
		"recommend_notify_email")
	kc.Check(Conf.Params.MedalParams, "fan_rule", "super_fan_rule", "cost_point", "content_html")
	kc.Check(Conf.Params.LiveURL, "web_room", "app_room", "user_rank_rule", "guild_apply_contract", "websocket",
		"activity_websocket")
	kc.Check(Conf.Params.GuildNoticeEmails, "guild_delete_notice_emails",
		"guild_update_notice_emails", "guild_transfer_notice_emails")
	kc.Check(Conf.Params.UserInfo, "default_card_frame", "default_medal_frame_url")
	kc.Check(SectionRecommended{}, "hypnosis_disable_time_range")
	kc.Check(SectionTextContent{}, "title", "content_html")
	kc.Check(SectionPK{}, "entry_icon", "win_effect", "win_web_effect", "lose_effect", "lose_web_effect",
		"draw_effect", "draw_web_effect", "peak_limit_start_time", "peak_limit_end_time")
	kc.Check(SectionGiftWall{}, "guide_url")
	kc.Check(SectionSticker{}, "room_icon_frame", "user_icon_frame")
	kc.Check(SectionNotification{}, "set_gift_online_mention_users")
	kc.Check(SectionLiveFeed{}, "position_force_insertion_switch", "tab_ab_salt", "tab_ab_bucket_count", "tab_ab_bucket_ids", "tab_ab_allow_list_user_ids",
		"hot_icon", "hot_dark_icon", "hot_web_icon", "recommend_icon", "recommend_dark_icon", "recommend_web_icon")
}

func TestAfterLoad(t *testing.T) {
	assert := assert.New(t)

	oldConf := *Conf
	defer func() {
		Conf = &oldConf
	}()

	Conf.IM.PubsubSize = 0
	assert.Equal(ErrNoPubsubSize, afterLoad())
	Conf.IM.PubsubSize = 1
	assert.Equal(ErrNoDefaultCDN, afterLoad())
	Conf.Params.URL.CDN = "oss://"
	assert.Equal(ErrNoDefaultCDN, afterLoad())
	Conf.AB = nil
	Conf.Service.MongoDB.Prefix = "test_"
	Conf.Service.Storage = storage.Config{"oss": {}}
	Conf.Service.Upload.URL = "https://1234/"
	assert.NoError(afterLoad())
	assert.NotNil(Conf.AB)
	assert.Empty(Conf.AB)
}

func TestLuaAfterLoad(t *testing.T) {
	assert := assert.New(t)

	sl := SectionLua{
		readFunc: func(status string) ([]byte, error) {
			if status == "error" {
				return nil, errors.New(status)
			}
			return []byte(status), nil
		},
	}
	sl.InitFile = "success"
	assert.NotPanics(func() { assert.NoError(sl.AfterLoad()) })
	assert.Equal("success", sl.InitScript)
	sl.InitFile = "error"
	assert.EqualError(sl.AfterLoad(), "error")
	sl.readFunc = nil
	assert.PanicsWithValue("undefined readFunc", func() { _ = sl.AfterLoad() })
}
