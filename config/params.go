package config

import (
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/config/params"
)

// DefaultCDNScheme 默认 CDN 的 scheme
var DefaultCDNScheme string

// SectionParams 配置项参数
type SectionParams struct {
	URL               SectionURL               `yaml:"url"`
	General           SectionGeneral           `yaml:"general"`
	NobleParams       SectionNobleParams       `yaml:"noble"`
	Recommended       SectionRecommended       `yaml:"recommended"`
	Security          SectionSecurity          `yaml:"security"`
	Interaction       SectionInteraction       `yaml:"interaction"`
	MedalParams       SectionMedalParams       `yaml:"medal"`
	GuildAgreement    SectionTextContent       `yaml:"guild_agreement"`
	LiveURL           SectionLiveURL           `yaml:"live_url"`
	PK                SectionPK                `yaml:"pk"`
	GiftWall          SectionGiftWall          `yaml:"gift_wall"`
	LiveAgreement     SectionTextContent       `yaml:"live_agreement"`
	DisableOpen       SectionTextContent       `yaml:"disable_open"`
	UserInfo          SectionUserInfo          `yaml:"userinfo"`
	GuildNoticeEmails SectionGuildNoticeEmails `yaml:"guild_notice_emails"`
	GuildRate         SectionGuildRate         `yaml:"guild_rate"`
	GuildOperate      SectionGuildOperate      `yaml:"guild_operate"`
	RedPacket         SectionRedPacket         `yaml:"red_packet"`
	ChannelCallback   SectionChannelCallback   `yaml:"channel_callback"`
	Sticker           SectionSticker           `yaml:"sticker"`
	Luckybag          SectionLuckybag          `yaml:"luckybag"`
	Notification      SectionNotification      `yaml:"notification"`
	LiveFeed          SectionLiveFeed          `yaml:"live_feed"`
}

// SectionURL URL 配置
type SectionURL struct {
	params.SectionURL `yaml:",inline"`
	Live              string `yaml:"live"`
}

// SectionGeneral 通用配置
type SectionGeneral struct {
	RoomTip     string `yaml:"room_tip"`
	FeedbackKey string `yaml:"feedback_key"`
}

// SectionGuildRate 分成比例相关配置
type SectionGuildRate struct {
	GuildRateDetailURL string `yaml:"guild_rate_detail_url"`
}

// SectionLiveURL 直播 URL 配置
type SectionLiveURL struct {
	WebRoom string `yaml:"web_room"`
	AppRoom string `yaml:"app_room"`

	UserRankRule       string `yaml:"user_rank_rule"`       // 主播榜规则
	GuildApplyContract string `yaml:"guild_apply_contract"` // 公会签续约详情页 https://fm.missevan.com/xx/%d, 参数为合约 ID

	WebSocket         []string `yaml:"websocket"`
	ActivityWebSocket []string `yaml:"activity_websocket"`
}

// SectionPK 相关配置
type SectionPK struct {
	EntryIcon          string `yaml:"entry_icon"` // PK 入口标识
	WinEffect          string `yaml:"win_effect"`
	WinWebEffect       string `yaml:"win_web_effect"`
	LoseEffect         string `yaml:"lose_effect"`
	LoseWebEffect      string `yaml:"lose_web_effect"`
	DrawEffect         string `yaml:"draw_effect"`
	DrawWebEffect      string `yaml:"draw_web_effect"`
	PeakLimitStartTime string `yaml:"peak_limit_start_time"`
	PeakLimitEndTime   string `yaml:"peak_limit_end_time"`
}

// SectionGiftWall 礼物墙配置
type SectionGiftWall struct {
	GuideURL string `yaml:"guide_url"`
}

// SectionTextContent 标题与内容配置参数
type SectionTextContent struct {
	Title       string `yaml:"title"`
	ContentHTML string `yaml:"content_html"`
}

// SectionNobleParams 贵族配置参数
type SectionNobleParams struct {
	NobleDetailsURL    string `yaml:"noble_details_url"`
	NobleGuideURL      string `yaml:"noble_guide_url"`
	CustomNobleGiftURL string `yaml:"custom_noble_gift_url"`
	BuyNobleURL        string `yaml:"buy_noble_url"`
	InvisibleIcon      string `yaml:"invisible_icon"`
	DefaultCardFrame   string `yaml:"default_card_frame"`

	RecommendWebTopIcon          string `yaml:"recommend_web_top_icon"`
	RecommendTopIcon             string `yaml:"recommend_top_icon"`
	RecommendWebHighnessTopIcon  string `yaml:"recommend_web_highness_top_icon"`
	RecommendHighnessTopIcon     string `yaml:"recommend_highness_top_icon"`
	RecommendListIcon            string `yaml:"recommend_list_icon"`
	RecommendHighnessListIcon    string `yaml:"recommend_highness_list_icon"`
	RecommendWebFrame            string `yaml:"recommend_web_frame"`             // Web 神话推荐头像框
	RecommendWebHighnessFrame    string `yaml:"recommend_web_highness_frame"`    // Web 上神推荐头像框
	RecommendAvatarFrame         string `yaml:"recommend_avatar_frame"`          // App 神话推荐头像框，文件命名规则: name_top_right_bottom_left.png
	RecommendHighnessAvatarFrame string `yaml:"recommend_highness_avatar_frame"` // App 上神用户推荐头像框，文件命名规则: name_top_right_bottom_left.png
	RecommendNotifyEmail         string `yaml:"recommend_notify_email"`          // 神话推荐成功后发送邮件给此邮箱
}

// SectionMedalParams 勋章配置参数
type SectionMedalParams struct {
	FanRule      string `yaml:"fan_rule"`
	SuperFanRule string `yaml:"super_fan_rule"` // 超粉规则

	CostPoint   int    `yaml:"cost_point"`
	ContentHTML string `yaml:"content_html"`
}

// SectionRecommended 推荐相关参数
type SectionRecommended struct {
	HypnosisDisableTimeRange [][2]string     `yaml:"hypnosis_disable_time_range"` // 禁止助眠分区的时间段
	HypnosisDisableClocks    [][2]util.Clock `yaml:"-"`
}

// SectionSecurity 敏感信息加密相关参数
type SectionSecurity struct {
	SensitiveInformationKey string `yaml:"sensitive_information_key"`
	SensitiveFixedIVKey     string `yaml:"sensitive_fixed_iv_key"`
}

// SectionInteraction 互动相关协议文档
type SectionInteraction struct {
	VoteHelpURL      string `yaml:"vote_help_url" json:"vote_help_url"`
	VoteAgreementURL string `yaml:"vote_agreement_url" json:"vote_agreement_url"`
}

// CardFrameTextColorItem 名片框默认字体颜色
type CardFrameTextColorItem struct {
	// 默认用户昵称字体颜色
	Username string `yaml:"username"`
	// 默认个性签名字体颜色
	Introduction string `yaml:"introduction"`
	// 默认举报和管理按钮字体颜色
	ReportAndManage string `yaml:"report_and_manage"`
}

// DefaultCardFrame 默认名片框配置
type DefaultCardFrame struct {
	// 默认用户名片框框体
	ImageURL string `yaml:"image_url"`
	// 默认字体颜色
	TextColorItem CardFrameTextColorItem `yaml:"text_color_item"`
}

// SectionUserInfo 用户相关配置
type SectionUserInfo struct {
	// 默认新版用户名片框
	DefaultCardFrame DefaultCardFrame `yaml:"default_card_frame"`
	// 默认粉丝勋章
	DefaultMedalFrameURL string `yaml:"default_medal_frame_url"`
}

// SectionGuildNoticeEmails 公会资料变更、注销、转会邮件配置
type SectionGuildNoticeEmails struct {
	GuildDeleteNoticeEmails   string `yaml:"guild_delete_notice_emails"`
	GuildUpdateNoticeEmails   string `yaml:"guild_update_notice_emails"`
	GuildTransferNoticeEmails string `yaml:"guild_transfer_notice_emails"`
}

// SectionGuildOperate 公会相关操作权限验证开关
type SectionGuildOperate struct {
	GuildPermissionSwitch   bool `yaml:"guild_permission_switch"`   // 公会长、经纪人重要操作权限验证开关
	CreatorPermissionSwitch bool `yaml:"creator_permission_switch"` // 主播重要操作权限验证开关
}

// SectionRedPacket 红包相关配置
type SectionRedPacket struct {
	ExpireDuration string  `yaml:"expire_duration"` // 红包过期时长
	Rule           string  `yaml:"rule"`            // 红包玩法说明跳转链接
	HighRiskScore  float64 `yaml:"high_risk_score"` // 抢红包营销风险检测时，认定为高风险用户的分数阈值
}

// SectionChannelCallback 连麦回调相关配置
type SectionChannelCallback struct {
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
}

// SignParams sign param
func (scc SectionChannelCallback) SignParams() (string, string) {
	return scc.AppKey, scc.AppSecret
}

// SectionSticker 表情包相关配置
type SectionSticker struct {
	RoomIconFrame string `yaml:"room_icon_frame"`
	UserIconFrame string `yaml:"user_icon_frame"`
}

// SectionLuckybag 广播剧福袋相关配置
type SectionLuckybag struct {
	DramalistURL string `yaml:"dramalist_url"`
}

// SectionNotification 消息通知相关配置
type SectionNotification struct {
	SetGiftOnlineMentionUsers []string `yaml:"set_gift_online_mention_users"`
}

// SectionLiveFeed 直播 feed 流相关配置
type SectionLiveFeed struct {
	PositionForceInsertionSwitch bool `yaml:"position_force_insertion_switch"` // 位置强插开关（控制是否强插热 6）

	// Tab 实验相关配置
	TabABSalt             string  `yaml:"tab_ab_salt"`                // 实验盐值
	TabABBucketCount      int     `yaml:"tab_ab_bucket_count"`        // 实验分桶总数
	TabABBucketIDs        []int   `yaml:"tab_ab_bucket_ids"`          // 实验组桶 ID 列表，hash 结果 % bucket_count，结果如果在 bucket_ids 中则表示进入实验组
	TabABAllowListUserIDs []int64 `yaml:"tab_ab_allow_list_user_ids"` // 实验组白名单用户 ID 列表

	// 热门分区
	HotIcon     string `yaml:"hot_icon"`      // 热门分区图标
	HotDarkIcon string `yaml:"hot_dark_icon"` // 热门分区夜间模式图标
	HotWebIcon  string `yaml:"hot_web_icon"`  // 热门分区 Web 图标

	// 推荐分区
	RecommendIcon     string `yaml:"recommend_icon"`      // 推荐分区图标
	RecommendDarkIcon string `yaml:"recommend_dark_icon"` // 推荐分区夜间模式图标
	RecommendWebIcon  string `yaml:"recommend_web_icon"`  // 推荐分区 Web 图标
}

// AfterLoad 读取完成后的配置
func (sr *SectionRecommended) AfterLoad() {
	sr.HypnosisDisableClocks = make([][2]util.Clock, len(sr.HypnosisDisableTimeRange))
	for i, v := range sr.HypnosisDisableTimeRange {
		sr.HypnosisDisableClocks[i][0] = util.ParseClock(v[0])
		sr.HypnosisDisableClocks[i][1] = util.ParseClock(v[1])
	}
}
