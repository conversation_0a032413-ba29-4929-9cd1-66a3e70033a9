package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
)

func TestMain(m *testing.M) {
	config.InitTest()
	m.Run()
}

func TestParseRoomIDByRoomURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID, ok := ParseRoomIDByRoomURL("https://fm.uat.missevan.com/live/61618636")
	require.True(ok)
	assert.EqualValues(61618636, roomID)

	roomID, ok = ParseRoomIDByRoomURL("https://www.missevan.com/mtopic/item/53")
	require.False(ok)
	assert.EqualValues(0, roomID)
}
